import { createBrowserClient } from "@supabase/ssr";

/**
 * Enhanced Supabase client for browser usage with improved session management
 *
 * SECURITY NOTE: This client should only be used in client components.
 * Always use getUser() instead of getSession() for authentication checks.
 */
export const createClient = () => {
  // Create the client with enhanced session management
  const client = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        detectSessionInUrl: true,
        persistSession: true,
        // Disable debug logging to reduce console noise
        debug: false,
      },
      // Enhanced global configuration
      global: {
        headers: {
          'X-Client-Info': 'wealthie-modelling-app',
        },
      },
    }
  );

  // SECURITY: Removed warning suppression to ensure proper authentication validation
  // Always use getUser() instead of getSession() for authentication checks

  return client;
};

export const signOut = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
};
