import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { headers } from "next/headers";

/**
 * Creates a Supabase client for server usage
 *
 * SECURITY NOTE: This client should only be used in server components.
 * Always use getUser() instead of getSession() for authentication checks.
 */
export const createClient = () => {
  const cookieStore = cookies();

  // Try to get the origin from headers
  let origin = '';
  try {
    const headersList = headers();
    origin = headersList.get('origin') || headersList.get('host') || '';
    if (origin && !origin.startsWith('http')) {
      origin = `https://${origin}`;
    }
  } catch (error) {
    console.error('Error getting origin from headers:', error);
  }

  // Fallback to environment variable if available
  if (!origin && process.env.NEXT_PUBLIC_SITE_URL) {
    origin = process.env.NEXT_PUBLIC_SITE_URL;
  }

  console.log('Server client using origin:', origin);

  // Create the client with the server configuration
  const client = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        detectSessionInUrl: true,
        persistSession: true,
      },
    },
  );

  // Monkey patch the client to suppress the warning
  // This is a temporary solution until the issue is fixed in the Supabase library
  if (client.auth && typeof client.auth === 'object') {
    // @ts-ignore - Adding a property to suppress the warning
    client.auth.suppressGetSessionWarning = true;
  }

  return client;
};
