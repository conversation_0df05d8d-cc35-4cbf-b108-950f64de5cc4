import { createClient } from "@/utils/supabase/client";
import { createClient as createServerClient } from "@/utils/supabase/server";
import { User } from "@supabase/supabase-js";

/**
 * Safely gets the authenticated user
 *
 * SECURITY NOTE: Always use this function instead of supabase.auth.getSession()
 * as getSession() returns data directly from storage (cookies) which may not be authentic.
 * getUser() authenticates the data by contacting the Supabase Auth server.
 */
export const getAuthenticatedUser = async (serverSide = false) => {
  const supabase = serverSide ? await createServerClient() : createClient();

  // SECURITY: Removed warning suppression to ensure proper authentication validation

  const { data: { user }, error } = await supabase.auth.getUser();

  if (error) {
    console.error('Error getting authenticated user:', error);
    return { user: null, error };
  }

  // Additional session validation for security (client-side only)
  if (user && !serverSide) {
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error("Session validation failed:", sessionError);
        return { user: null, error: sessionError || new Error('No valid session') };
      }

      // Check if session is expired
      if (session.expires_at && session.expires_at < Date.now() / 1000) {
        console.error("Session expired");
        return { user: null, error: new Error('Session expired') };
      }
    } catch (sessionValidationError) {
      console.error("Session validation error:", sessionValidationError);
      return { user: null, error: sessionValidationError as Error };
    }
  }

  return { user, error: null };
};

/**
 * Checks if a user is authenticated
 *
 * SECURITY NOTE: Always use this function instead of checking session directly
 * as it uses getUser() which authenticates the data by contacting the Supabase Auth server.
 */
export const isAuthenticated = async (serverSide = false) => {
  const { user, error } = await getAuthenticatedUser(serverSide);
  return { isAuthenticated: !!user, user, error };
};

/**
 * Hook for client components to get the authenticated user
 *
 * @param callback Function to call with the user object when authentication is complete
 * @param onError Function to call if there's an error
 */
export const withAuthenticatedUser = async (
  callback: (user: User) => void,
  onError?: (error: Error) => void
) => {
  try {
    const { user, error } = await getAuthenticatedUser();

    if (error) {
      throw error;
    }

    if (!user) {
      throw new Error('User not authenticated');
    }

    callback(user);
  } catch (error) {
    console.error('Authentication error:', error);
    if (onError && error instanceof Error) {
      onError(error);
    }
  }
};
