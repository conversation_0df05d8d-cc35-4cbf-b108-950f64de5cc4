/**
 * Server-Side Authentication Guards
 *
 * Provides robust server-side authentication and authorization checks
 * that cannot be bypassed by client-side manipulation.
 */

import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { getMFAStatusServer } from '@/utils/server-mfa-security';
import { validateSessionServer } from '@/utils/server-session-manager';

export interface AuthUser {
  id: string;
  email?: string;
  user_metadata?: any;
  app_metadata?: any;
}

export interface AuthResult {
  user: AuthUser;
  isAuthenticated: true;
}

/**
 * Require authentication for server components and API routes
 * Redirects to sign-in if not authenticated
 */
export async function requireAuth(): Promise<AuthUser> {
  const supabase = createClient();

  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      redirect('/sign-in');
    }

    if (!user) {
      redirect('/sign-in');
    }

    return user;
  } catch (error) {
    redirect('/sign-in');
  }
}

/**
 * Require MFA verification for sensitive operations
 * Redirects to MFA challenge if MFA is required but not verified
 */
export async function requireMFA(): Promise<AuthUser> {
  const user = await requireAuth();

  try {
    const mfaStatus = await getMFAStatusServer();

    // If MFA is required but not verified, redirect to challenge
    if (mfaStatus.needsChallenge) {
      redirect('/mfa-challenge');
    }

    return user;
  } catch (error) {
    // If MFA check fails, still allow access but continue
    return user;
  }
}

/**
 * Check authentication without redirecting
 * Returns null if not authenticated, user object if authenticated
 */
export async function getAuthUser(): Promise<AuthUser | null> {
  const supabase = createClient();

  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    return user;
  } catch (error) {
    return null;
  }
}

/**
 * Validate session integrity
 * Checks both user authentication and session validity
 */
export async function validateSession(): Promise<{ user: AuthUser; isValid: boolean }> {
  const supabase = createClient();

  try {
    // First check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { user: null as any, isValid: false };
    }

    // Then validate session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return { user, isValid: false };
    }

    // Check if session is expired
    if (session.expires_at && session.expires_at < Date.now() / 1000) {
      return { user, isValid: false };
    }

    return { user, isValid: true };
  } catch (error) {
    return { user: null as any, isValid: false };
  }
}

/**
 * Require specific role or permission
 * Can be extended for role-based access control
 */
export async function requireRole(allowedRoles: string[]): Promise<AuthUser> {
  const user = await requireAuth();

  // Get user profile to check role
  const supabase = createClient();

  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('org_role')
      .eq('user_id', user.id)
      .single();

    if (error) {
      redirect('/sign-in');
    }

    if (!profile || !allowedRoles.includes(profile.org_role)) {
      redirect('/unauthorized');
    }

    return user;
  } catch (error) {
    redirect('/sign-in');
  }
}

/**
 * Create a higher-order function for protecting API routes
 */
export function withAuth<T extends any[]>(
  handler: (user: AuthUser, ...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    try {
      const user = await getAuthUser();

      if (!user) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      return await handler(user, ...args);
    } catch (error) {
      return new Response(
        JSON.stringify({ error: 'Internal server error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}

/**
 * Create a higher-order function for protecting API routes with MFA
 */
export function withMFAAuth<T extends any[]>(
  handler: (user: AuthUser, ...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    try {
      const user = await getAuthUser();

      if (!user) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      // Check MFA status
      const mfaStatus = await getMFAStatusServer();

      if (mfaStatus.needsChallenge) {
        return new Response(
          JSON.stringify({ error: 'MFA verification required' }),
          {
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      return await handler(user, ...args);
    } catch (error) {
      return new Response(
        JSON.stringify({ error: 'Internal server error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}
