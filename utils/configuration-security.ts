/**
 * Configuration Security Utilities
 * 
 * Provides secure configuration management to prevent hardcoded values
 * and ensure proper environment variable usage across the application.
 */

import { validateEnvironmentVariables, getSupabaseDomain } from './env-validation';

/**
 * Configuration security validation results
 */
export interface ConfigurationSecurityResult {
  isSecure: boolean;
  vulnerabilities: string[];
  recommendations: string[];
  configurationStatus: {
    hasHardcodedUrls: boolean;
    hasHardcodedKeys: boolean;
    hasHardcodedDomains: boolean;
    usesEnvironmentVariables: boolean;
    hasProperValidation: boolean;
  };
}

/**
 * Patterns that indicate hardcoded configuration values
 */
const HARDCODED_PATTERNS = {
  supabaseUrls: [
    /https:\/\/[a-z0-9]+\.supabase\.co/gi,
    /[a-z0-9]+\.supabase\.co/gi
  ],
  apiKeys: [
    /eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*/g, // JWT pattern
    /sk-[A-Za-z0-9]{48}/g, // OpenAI API key pattern
    /re_[A-Za-z0-9]{26}/g, // Resend API key pattern
  ],
  domains: [
    /localhost:\d+/g,
    /127\.0\.0\.1:\d+/g,
    /\d+\.\d+\.\d+\.\d+:\d+/g
  ],
  ports: [
    /:\d{4,5}/g
  ]
};

/**
 * Secure configuration templates
 */
export const SECURE_CONFIG_TEMPLATES = {
  nextConfig: `/** @type {import('next').NextConfig} */

/**
 * Get Supabase domain from environment variable
 * This prevents hardcoding the Supabase URL in the configuration
 */
function getSupabaseDomain() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) {
    console.warn('NEXT_PUBLIC_SUPABASE_URL not configured, using localhost for images');
    return 'localhost';
  }

  try {
    return new URL(url).hostname;
  } catch {
    console.error('Invalid NEXT_PUBLIC_SUPABASE_URL format');
    return 'localhost';
  }
}

const nextConfig = {
  output: 'standalone',
  experimental: {
    swcPlugins: [['next-superjson-plugin', {}]]
  },
  images: {
    domains: [
      getSupabaseDomain(),
      'localhost',
      '127.0.0.1'
    ]
  }
}

module.exports = nextConfig`,

  supabaseConfig: `# Supabase Local Development Configuration
# Uses environment variables to prevent hardcoded values

project_id = "env(SUPABASE_PROJECT_ID)"

[api]
enabled = true
port = "env(SUPABASE_API_PORT)"

[db]
port = "env(SUPABASE_DB_PORT)"

[studio]
enabled = true
port = "env(SUPABASE_STUDIO_PORT)"
api_url = "env(SUPABASE_API_URL)"
openai_api_key = "env(OPENAI_API_KEY)"

[auth]
site_url = "env(SITE_URL)"
additional_redirect_urls = ["env(ADDITIONAL_REDIRECT_URLS)"]`
};

/**
 * Validate configuration files for security issues
 */
export function validateConfigurationSecurity(
  configContent: string,
  configType: 'nextjs' | 'supabase' | 'general' = 'general'
): ConfigurationSecurityResult {
  const vulnerabilities: string[] = [];
  const recommendations: string[] = [];
  
  // Check for hardcoded Supabase URLs
  const hasHardcodedUrls = HARDCODED_PATTERNS.supabaseUrls.some(pattern => 
    pattern.test(configContent)
  );
  
  if (hasHardcodedUrls) {
    vulnerabilities.push('Hardcoded Supabase URLs found in configuration');
    recommendations.push('Replace hardcoded URLs with environment variables');
  }
  
  // Check for hardcoded API keys
  const hasHardcodedKeys = HARDCODED_PATTERNS.apiKeys.some(pattern => 
    pattern.test(configContent)
  );
  
  if (hasHardcodedKeys) {
    vulnerabilities.push('Hardcoded API keys found in configuration');
    recommendations.push('Move API keys to environment variables');
  }
  
  // Check for hardcoded domains/IPs
  const hasHardcodedDomains = HARDCODED_PATTERNS.domains.some(pattern => 
    pattern.test(configContent)
  );
  
  if (hasHardcodedDomains && !configContent.includes('localhost')) {
    vulnerabilities.push('Hardcoded domains or IP addresses found');
    recommendations.push('Use environment variables for domain configuration');
  }
  
  // Check for environment variable usage
  const usesEnvironmentVariables = /process\.env\.|env\(/g.test(configContent);
  
  if (!usesEnvironmentVariables) {
    vulnerabilities.push('Configuration does not use environment variables');
    recommendations.push('Implement environment variable usage for dynamic configuration');
  }
  
  // Check for proper validation
  const hasProperValidation = /validateEnvironmentVariables|getSupabaseDomain/g.test(configContent);
  
  if (!hasProperValidation && configType === 'nextjs') {
    vulnerabilities.push('Missing configuration validation');
    recommendations.push('Add configuration validation utilities');
  }
  
  return {
    isSecure: vulnerabilities.length === 0,
    vulnerabilities,
    recommendations,
    configurationStatus: {
      hasHardcodedUrls,
      hasHardcodedKeys,
      hasHardcodedDomains,
      usesEnvironmentVariables,
      hasProperValidation
    }
  };
}

/**
 * Get secure configuration for different environments
 */
export function getSecureConfiguration(environment: 'development' | 'production' | 'test') {
  try {
    const envConfig = validateEnvironmentVariables();
    
    return {
      supabase: {
        url: envConfig.NEXT_PUBLIC_SUPABASE_URL,
        anonKey: envConfig.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        domain: getSupabaseDomain()
      },
      api: {
        baseUrl: environment === 'development' 
          ? 'http://localhost:3000' 
          : process.env.NEXT_PUBLIC_SITE_URL || 'https://app.wealthie.com',
      },
      features: {
        openai: !!envConfig.OPENAI_API_KEY,
        resend: !!envConfig.RESEND_API_KEY,
        livekit: !!envConfig.NEXT_PUBLIC_LIVEKIT_URL,
        s3: !!(envConfig.SUPABASE_S3_ACCESS_KEY_ID && envConfig.SUPABASE_S3_SECRET_ACCESS_KEY)
      }
    };
  } catch (error) {
    throw new Error(`Configuration validation failed: ${error}`);
  }
}

/**
 * Sanitize configuration for logging (removes sensitive data)
 */
export function sanitizeConfigurationForLogging(config: any): any {
  const sanitized = JSON.parse(JSON.stringify(config));
  
  // Remove sensitive fields
  const sensitiveFields = [
    'anonKey', 'serviceRoleKey', 'apiKey', 'secret', 'password', 'token'
  ];
  
  function sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        obj[key] = sanitizeObject(obj[key]);
      }
    }
    
    return obj;
  }
  
  return sanitizeObject(sanitized);
}

/**
 * Monitor configuration changes for security issues
 */
export function monitorConfigurationSecurity(): void {
  const configFiles = [
    'next.config.js',
    'supabase/config.toml',
    '.env.example'
  ];
  
  console.log('🔍 Configuration Security Monitor Active');
  console.log(`📁 Monitoring files: ${configFiles.join(', ')}`);
  
  // In a real implementation, you would set up file watchers
  // For now, we'll just log the monitoring status
  console.log('✅ Configuration security monitoring enabled');
}

/**
 * Generate secure environment template
 */
export function generateSecureEnvironmentTemplate(): string {
  return `# Wealthie Application Environment Variables
# Copy this file to .env.local and fill in your values

# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# External API Keys (Optional)
OPENAI_API_KEY=your_openai_api_key_here
RESEND_API_KEY=your_resend_api_key_here
GROQ_API_KEY=your_groq_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# LiveKit Configuration (Optional)
NEXT_PUBLIC_LIVEKIT_URL=your_livekit_url_here
LIVEKIT_API_KEY=your_livekit_api_key_here
LIVEKIT_API_SECRET=your_livekit_api_secret_here

# S3 Configuration (Optional)
SUPABASE_S3_ACCESS_KEY_ID=your_s3_access_key_here
SUPABASE_S3_SECRET_ACCESS_KEY=your_s3_secret_key_here

# Application Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NODE_ENV=development

# Security Note: Never commit this file to version control
# Add .env.local to your .gitignore file`;
}

/**
 * Validate runtime configuration security
 */
export function validateRuntimeConfigurationSecurity(): {
  isSecure: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check if sensitive environment variables are properly set
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    issues.push(`Missing required environment variables: ${missingVars.join(', ')}`);
    recommendations.push('Set all required environment variables in .env.local');
  }
  
  // Check for development URLs in production
  if (process.env.NODE_ENV === 'production') {
    const devPatterns = ['localhost', '127.0.0.1', 'dev', 'test'];
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    
    if (devPatterns.some(pattern => supabaseUrl.includes(pattern))) {
      issues.push('Development URLs detected in production environment');
      recommendations.push('Use production URLs for production deployment');
    }
  }
  
  return {
    isSecure: issues.length === 0,
    issues,
    recommendations
  };
}
