/**
 * API Response Filtering Utilities
 * 
 * Provides middleware and utilities to filter API responses and prevent
 * excessive data exposure in API endpoints.
 */

import { NextResponse } from 'next/server';
import { 
  APIResponseDTO, 
  APIErrorResponseDTO,
  HouseholdSummaryDTO,
  HouseholdDetailDTO,
  AssetSummaryDTO,
  ScenarioSummaryDTO
} from '@/types/dto';
import { filterSensitiveFields, convertToDTO } from './data-exposure-prevention';

/**
 * Create a safe API response with filtered data
 */
export function createSafeAPIResponse<T>(
  data: T,
  success: boolean = true,
  message?: string,
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }
): NextResponse<APIResponseDTO<T>> {
  const response: APIResponseDTO<T> = {
    success,
    data,
    message,
    pagination
  };

  return NextResponse.json(response);
}

/**
 * Create a safe API error response
 */
export function createSafeAPIErrorResponse(
  error: string,
  status: number = 500,
  details?: any[],
  path?: string
): NextResponse<APIErrorResponseDTO> {
  const response: APIErrorResponseDTO = {
    success: false,
    error,
    details,
    timestamp: new Date().toISOString(),
    path: path || ''
  };

  return NextResponse.json(response, { status });
}

/**
 * Filter database records for API response
 */
export function filterForAPIResponse<T>(
  records: any[],
  tableName: string,
  level: 'summary' | 'detail' = 'summary'
): T[] {
  // First filter sensitive fields
  const filteredRecords = records.map(record => 
    filterSensitiveFields(record, tableName)
  );

  // Then convert to appropriate DTOs
  return convertToDTO<T>(filteredRecords, tableName, level);
}

/**
 * Middleware to automatically filter API responses
 */
export function withAPIResponseFiltering<T>(
  handler: (req: Request, context?: any) => Promise<T[]>,
  tableName: string,
  level: 'summary' | 'detail' = 'summary'
) {
  return async (req: Request, context?: any) => {
    try {
      const rawData = await handler(req, context);
      const filteredData = filterForAPIResponse(rawData, tableName, level);
      return createSafeAPIResponse(filteredData);
    } catch (error: any) {
      console.error(`API Error in ${tableName} endpoint:`, error);
      return createSafeAPIErrorResponse(
        error.message || 'Internal server error',
        500
      );
    }
  };
}

/**
 * Validate API request parameters to prevent injection
 */
export function validateAPIParameters(params: Record<string, any>): {
  isValid: boolean;
  errors: string[];
  sanitizedParams: Record<string, any>;
} {
  const errors: string[] = [];
  const sanitizedParams: Record<string, any> = {};

  for (const [key, value] of Object.entries(params)) {
    // Basic validation
    if (typeof value === 'string') {
      // Check for SQL injection patterns
      const sqlInjectionPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)/i,
        /(UNION|OR|AND)\s+\d+\s*=\s*\d+/i,
        /['"]\s*(OR|AND)\s*['"]/i,
        /['"]\s*;\s*--/i
      ];

      const hasSQLInjection = sqlInjectionPatterns.some(pattern => 
        pattern.test(value)
      );

      if (hasSQLInjection) {
        errors.push(`Parameter ${key} contains potentially malicious content`);
        continue;
      }

      // Sanitize the value
      sanitizedParams[key] = value.trim();
    } else {
      sanitizedParams[key] = value;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedParams
  };
}

/**
 * Rate limiting for API endpoints
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 60000 // 1 minute
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  for (const [key, data] of requestCounts.entries()) {
    if (data.resetTime < windowStart) {
      requestCounts.delete(key);
    }
  }

  const current = requestCounts.get(identifier) || { count: 0, resetTime: now + windowMs };

  if (current.resetTime < now) {
    // Reset the window
    current.count = 0;
    current.resetTime = now + windowMs;
  }

  current.count++;
  requestCounts.set(identifier, current);

  return {
    allowed: current.count <= maxRequests,
    remaining: Math.max(0, maxRequests - current.count),
    resetTime: current.resetTime
  };
}

/**
 * Middleware to add rate limiting to API endpoints
 */
export function withRateLimit(
  handler: (req: Request, context?: any) => Promise<NextResponse>,
  maxRequests: number = 100,
  windowMs: number = 60000
) {
  return async (req: Request, context?: any) => {
    // Use IP address as identifier (in production, you might want to use user ID)
    const identifier = req.headers.get('x-forwarded-for') || 
                      req.headers.get('x-real-ip') || 
                      'unknown';

    const rateLimit = checkRateLimit(identifier, maxRequests, windowMs);

    if (!rateLimit.allowed) {
      return createSafeAPIErrorResponse(
        'Rate limit exceeded',
        429
      );
    }

    const response = await handler(req, context);
    
    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());

    return response;
  };
}

/**
 * Comprehensive API security middleware
 */
export function withAPISecurity<T>(
  handler: (req: Request, context?: any) => Promise<T[]>,
  options: {
    tableName: string;
    level?: 'summary' | 'detail';
    maxRequests?: number;
    windowMs?: number;
    requireAuth?: boolean;
  }
) {
  return withRateLimit(
    withAPIResponseFiltering(handler, options.tableName, options.level),
    options.maxRequests,
    options.windowMs
  );
}

/**
 * Log API access for security monitoring
 */
export function logAPIAccess(
  endpoint: string,
  method: string,
  userId?: string,
  params?: Record<string, any>,
  success: boolean = true,
  error?: string
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    endpoint,
    method,
    userId: userId || 'anonymous',
    params: params ? Object.keys(params) : [],
    success,
    error: error || null
  };

  // In production, you would send this to a logging service
  console.log('API Access Log:', JSON.stringify(logEntry));
}

/**
 * Sanitize file paths to prevent directory traversal
 */
export function sanitizeFilePath(path: string): string {
  // Remove any path traversal attempts
  return path
    .replace(/\.\./g, '')
    .replace(/[\/\\]/g, '')
    .replace(/[^a-zA-Z0-9._-]/g, '');
}

/**
 * Validate file uploads
 */
export function validateFileUpload(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'application/pdf'],
  maxSize: number = 5 * 1024 * 1024 // 5MB
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
  }

  // Check file name for malicious patterns
  const maliciousPatterns = [
    /\.php$/i,
    /\.exe$/i,
    /\.bat$/i,
    /\.sh$/i,
    /\.cmd$/i
  ];

  if (maliciousPatterns.some(pattern => pattern.test(file.name))) {
    errors.push('File name contains potentially dangerous extension');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
