/**
 * Utility for generating ICS calendar files
 */

// Generate a unique ID for calendar events
const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// Format date for ICS file
const formatDateForICS = (date: Date): string => {
  return date
    .toISOString()
    .replace(/-/g, '')
    .replace(/:/g, '')
    .replace(/\.\d{3}/g, '');
};

// Escape text for ICS file
const escapeICSText = (text: string): string => {
  return text
    .replace(/\n/g, '\\n')
    .replace(/,/g, '\\,')
    .replace(/;/g, '\\;')
    .replace(/\\/g, '\\\\');
};

interface ICSEventOptions {
  title: string;
  description?: string;
  location?: string;
  startDate: Date;
  endDate: Date;
  isAllDay: boolean;
  organizerName: string;
  organizerEmail: string;
  attendees: Array<{
    name?: string;
    email: string;
    role: 'REQ-PARTICIPANT' | 'OPT-PARTICIPANT';
  }>;
}

/**
 * Generate an ICS file content for a calendar event
 */
export const generateICSFile = (options: ICSEventOptions): string => {
  const {
    title,
    description = '',
    location = '',
    startDate,
    endDate,
    isAllDay,
    organizerName,
    organizerEmail,
    attendees = [],
  } = options;

  const eventUid = generateUUID();
  const now = new Date();
  const timestamp = formatDateForICS(now);

  // Format dates based on whether it's an all-day event
  let start: string;
  let end: string;

  if (isAllDay) {
    // For all-day events, use DATE format without time
    start = formatDateForICS(startDate).substring(0, 8);
    // For all-day events in Outlook, the end date needs to be the day after
    const nextDay = new Date(endDate);
    nextDay.setDate(nextDay.getDate() + 1);
    end = formatDateForICS(nextDay).substring(0, 8);
  } else {
    // For timed events, use full datetime with Z for UTC
    start = formatDateForICS(startDate);
    end = formatDateForICS(endDate);
  }

  // Build the ICS content
  let icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Wealthie//Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:REQUEST',
    'X-MS-OLK-FORCEINSPECTOROPEN:TRUE', // Force Outlook to open the meeting request
    'BEGIN:VEVENT',
    `UID:${eventUid}@wealthie.co.nz`,
    `DTSTAMP:${timestamp}`,
    `DTSTART${isAllDay ? ';VALUE=DATE' : ''}:${start}`,
    `DTEND${isAllDay ? ';VALUE=DATE' : ''}:${end}`,
    `SUMMARY:${escapeICSText(title)}`,
  ].join('\r\n');

  // Add description if provided
  if (description) {
    icsContent += `\r\nDESCRIPTION:${escapeICSText(description)}`;
  }

  // Add location if provided
  if (location) {
    icsContent += `\r\nLOCATION:${escapeICSText(location)}`;
  }

  // Add organizer
  icsContent += `\r\nORGANIZER;CN=${escapeICSText(organizerName)}:mailto:${organizerEmail}`;

  // Add attendees
  attendees.forEach((attendee) => {
    const name = attendee.name ? `CN=${escapeICSText(attendee.name)};` : '';
    icsContent += `\r\nATTENDEE;${name}ROLE=${attendee.role};PARTSTAT=NEEDS-ACTION;RSVP=TRUE:mailto:${attendee.email}`;
  });

  // Add sequence and status
  icsContent += '\r\nSEQUENCE:0';
  icsContent += '\r\nSTATUS:CONFIRMED';
  icsContent += '\r\nTRANSP:OPAQUE';
  icsContent += '\r\nX-MICROSOFT-CDO-BUSYSTATUS:BUSY'; // Outlook specific property
  icsContent += '\r\nX-MICROSOFT-CDO-IMPORTANCE:1'; // Outlook specific property
  icsContent += '\r\nX-MICROSOFT-DISALLOW-COUNTER:FALSE'; // Allow proposing new time in Outlook
  icsContent += '\r\nX-MS-OLK-CONFTYPE:0'; // Regular meeting (not a conference)

  // Add end of event and calendar
  icsContent += '\r\nEND:VEVENT';
  icsContent += '\r\nEND:VCALENDAR';

  return icsContent;
};

/**
 * Generate a Google Calendar event URL
 */
export const generateGoogleCalendarUrl = (options: ICSEventOptions): string => {
  const {
    title,
    description = '',
    location = '',
    startDate,
    endDate,
    isAllDay,
  } = options;

  // Format dates for Google Calendar
  const formatDate = (date: Date): string => {
    return date.toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/g, '');
  };

  const dates = isAllDay
    ? `${formatDate(startDate).substring(0, 8)}/${formatDate(endDate).substring(0, 8)}`
    : `${formatDate(startDate)}/${formatDate(endDate)}`;

  // Build the URL
  const url = new URL('https://calendar.google.com/calendar/render');
  url.searchParams.append('action', 'TEMPLATE');
  url.searchParams.append('text', title);

  if (description) {
    url.searchParams.append('details', description);
  }

  if (location) {
    url.searchParams.append('location', location);
  }

  url.searchParams.append('dates', dates);

  return url.toString();
};

/**
 * Generate an Outlook Calendar event URL
 */
export const generateOutlookCalendarUrl = (options: ICSEventOptions): string => {
  const {
    title,
    description = '',
    location = '',
    startDate,
    endDate,
    isAllDay,
  } = options;

  // Format dates for Outlook Calendar
  const formatDate = (date: Date): string => {
    return date.toISOString();
  };

  // Build the URL
  const url = new URL('https://outlook.live.com/calendar/0/deeplink/compose');
  url.searchParams.append('subject', title);

  if (description) {
    url.searchParams.append('body', description);
  }

  if (location) {
    url.searchParams.append('location', location);
  }

  url.searchParams.append('startdt', formatDate(startDate));
  url.searchParams.append('enddt', formatDate(endDate));

  if (isAllDay) {
    url.searchParams.append('allday', 'true');
  }

  return url.toString();
};
