/**
 * Server-Side MFA Security Utilities
 *
 * This module provides server-side MFA security enforcement
 * that works with Supabase's native MFA system.
 *
 * Note: This file is separate from utils/mfa-security.ts to avoid
 * importing server-side utilities in client-side code.
 */

import { createClient } from '@/utils/supabase/server';

export interface MFAStatus {
  isRequired: boolean;
  isEnrolled: boolean;
  isVerified: boolean;
  currentLevel: 'aal1' | 'aal2' | null;
  nextLevel: 'aal1' | 'aal2' | null;
  factors: any[];
  needsChallenge: boolean;
}

/**
 * Get comprehensive MFA status for a user (server-side)
 */
export async function getMFAStatusServer(): Promise<MFAStatus> {
  const supabase = await createClient();

  try {
    // Get AAL (Authenticator Assurance Level) data
    const { data: aalData, error: aalError } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

    if (aalError) {
      throw aalError;
    }

    // Get MFA factors
    const { data: factorsData, error: factorsError } = await supabase.auth.mfa.listFactors();

    if (factorsError) {
      throw factorsError;
    }

    // Combine all factor types
    const allFactors = factorsData ? [...(factorsData.totp || []), ...(factorsData.phone || [])] : [];
    const verifiedFactors = allFactors.filter(factor => factor.status === 'verified');

    const isEnrolled = verifiedFactors.length > 0;
    const isVerified = aalData?.currentLevel === 'aal2';
    const needsChallenge = isEnrolled && aalData?.nextLevel === 'aal2' && aalData?.currentLevel === 'aal1';

    return {
      isRequired: isEnrolled, // If user has enrolled MFA, it's required
      isEnrolled,
      isVerified,
      currentLevel: aalData?.currentLevel || null,
      nextLevel: aalData?.nextLevel || null,
      factors: verifiedFactors,
      needsChallenge
    };
  } catch (error) {
    return {
      isRequired: false,
      isEnrolled: false,
      isVerified: false,
      currentLevel: null,
      nextLevel: null,
      factors: [],
      needsChallenge: false
    };
  }
}

/**
 * Require MFA verification for sensitive operations (server-side)
 * Throws an error if MFA is required but not verified
 */
export async function requireMFAServer(): Promise<void> {
  const mfaStatus = await getMFAStatusServer();

  if (mfaStatus.isRequired && !mfaStatus.isVerified) {
    throw new Error('MFA verification required for this operation');
  }
}

/**
 * Check if a route should require MFA verification
 * This defines which routes need enhanced security
 */
export function isSensitiveRoute(pathname: string): boolean {
  const sensitiveRoutes = [
    '/protected/households/household', // Individual household access
    '/protected/settings', // Account settings
    '/protected/profile', // Profile management
    // Add more sensitive routes as needed
  ];

  return sensitiveRoutes.some(route => pathname.startsWith(route));
}

/**
 * Enhanced MFA enforcement for middleware
 * Returns redirect URL if MFA challenge is needed, null otherwise
 */
export async function checkMFAEnforcement(pathname: string): Promise<string | null> {
  try {
    const mfaStatus = await getMFAStatusServer();

    // If user has MFA enrolled but not verified for this session
    if (mfaStatus.needsChallenge) {
      return '/mfa-challenge';
    }

    // For sensitive routes, require MFA if available
    if (isSensitiveRoute(pathname) && mfaStatus.isEnrolled && !mfaStatus.isVerified) {
      return '/mfa-challenge';
    }

    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Log MFA security events for monitoring (server-side)
 */
export function logMFAEventServer(event: string, details?: any): void {
  const timestamp = new Date().toISOString();
  console.log(`[MFA Security Server] ${timestamp}: ${event}`, details || '');

  // In production, you might want to send this to a security monitoring service
  // Example: sendToSecurityMonitoring({ event, details, timestamp });
}
