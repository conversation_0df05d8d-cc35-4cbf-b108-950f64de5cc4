/**
 * SQL Injection Prevention Utilities
 *
 * Comprehensive utilities to prevent SQL injection attacks in AI-generated
 * queries and user inputs. Provides validation, sanitization, and safe
 * query building functions.
 */

/**
 * SQL injection prevention configuration
 */
export interface SQLValidationConfig {
  allowedTables: string[];
  allowedOperations: string[];
  forbiddenKeywords: string[];
  maxQueryLength: number;
  allowJoins: boolean;
  allowSubqueries: boolean;
}

/**
 * Default SQL validation configuration
 */
export const DEFAULT_SQL_CONFIG: SQLValidationConfig = {
  allowedTables: [
    'households', 'assets', 'liabilities', 'income', 'expenses',
    'scenarios_data1', 'notes', 'custom_reports', 'goals',
    'notifications', 'profiles', 'relationships'
  ],
  allowedOperations: ['SELECT'],
  forbiddenKeywords: [
    'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE',
    'TRUNCATE', 'GRANT', 'REVOKE', 'EXEC', 'EXECUTE',
    'XP_', 'SP_', 'PG_', 'INFORMATION_SCHEMA', 'SYS',
    'UNION', 'DECLARE', 'CAST', 'CONVERT'
  ],
  maxQueryLength: 2000,
  allowJoins: true,
  allowSubqueries: false
};

/**
 * Validation result interface
 */
export interface SQLValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedQuery?: string;
  detectedThreats: string[];
}

/**
 * Validate AI-generated SQL query for injection attacks
 */
export function validateAIGeneratedSQL(
  query: string,
  config: SQLValidationConfig = DEFAULT_SQL_CONFIG
): SQLValidationResult {
  const result: SQLValidationResult = {
    isValid: true,
    errors: [],
    detectedThreats: []
  };

  if (!query || typeof query !== 'string') {
    result.isValid = false;
    result.errors.push('Query must be a non-empty string');
    return result;
  }

  const upperQuery = query.toUpperCase().trim();
  const cleanQuery = query.trim();

  // Check query length
  if (cleanQuery.length > config.maxQueryLength) {
    result.isValid = false;
    result.errors.push(`Query exceeds maximum length of ${config.maxQueryLength} characters`);
  }

  // Check for allowed operations
  const startsWithAllowedOp = config.allowedOperations.some(op => 
    upperQuery.startsWith(op.toUpperCase())
  );
  
  if (!startsWithAllowedOp) {
    result.isValid = false;
    result.errors.push(`Query must start with one of: ${config.allowedOperations.join(', ')}`);
  }

  // Check for forbidden keywords
  for (const keyword of config.forbiddenKeywords) {
    if (upperQuery.includes(keyword.toUpperCase())) {
      result.isValid = false;
      result.errors.push(`Forbidden keyword detected: ${keyword}`);
      result.detectedThreats.push(`SQL_INJECTION_KEYWORD: ${keyword}`);
    }
  }

  // Check for multiple statements (semicolon injection)
  const semicolonCount = (cleanQuery.match(/;/g) || []).length;
  if (semicolonCount > 1 || (semicolonCount === 1 && !cleanQuery.endsWith(';'))) {
    result.isValid = false;
    result.errors.push('Multiple SQL statements not allowed');
    result.detectedThreats.push('MULTIPLE_STATEMENTS');
  }

  // Validate table names
  const tableMatches = upperQuery.match(/FROM\s+(\w+)|JOIN\s+(\w+)/gi);
  if (tableMatches) {
    for (const match of tableMatches) {
      const tableName = match.replace(/FROM\s+|JOIN\s+/gi, '').trim().toLowerCase();
      if (!config.allowedTables.includes(tableName)) {
        result.isValid = false;
        result.errors.push(`Access to table '${tableName}' not allowed`);
        result.detectedThreats.push(`UNAUTHORIZED_TABLE: ${tableName}`);
      }
    }
  }

  // Check for subqueries if not allowed
  if (!config.allowSubqueries && /\(\s*SELECT/i.test(upperQuery)) {
    result.isValid = false;
    result.errors.push('Subqueries are not allowed');
    result.detectedThreats.push('SUBQUERY_DETECTED');
  }

  // Check for joins if not allowed
  if (!config.allowJoins && /JOIN/i.test(upperQuery)) {
    result.isValid = false;
    result.errors.push('JOIN operations are not allowed');
    result.detectedThreats.push('JOIN_DETECTED');
  }

  // Check for SQL injection patterns
  const injectionPatterns = [
    /--/,                    // SQL comments
    /\/\*.*?\*\//,          // Block comments
    /'\s*OR\s*'1'\s*=\s*'1'/i, // Classic injection
    /'\s*OR\s*1\s*=\s*1/i,     // Numeric injection
    /UNION\s+SELECT/i,         // Union injection
    /'\s*;\s*DROP/i,           // Drop injection
    /'\s*;\s*DELETE/i,         // Delete injection
    /'\s*;\s*UPDATE/i,         // Update injection
    /'\s*;\s*INSERT/i          // Insert injection
  ];

  for (const pattern of injectionPatterns) {
    if (pattern.test(cleanQuery)) {
      result.isValid = false;
      result.errors.push('Potential SQL injection pattern detected');
      result.detectedThreats.push(`INJECTION_PATTERN: ${pattern.source}`);
    }
  }

  return result;
}

/**
 * Sanitize user input for safe SQL usage
 */
export function sanitizeUserInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/['"`;\\]/g, '') // Remove quotes, semicolons, backslashes
    .replace(/--.*$/gm, '')   // Remove SQL comments
    .replace(/\/\*.*?\*\//gs, '') // Remove block comments
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .trim()
    .substring(0, 500); // Limit length
}

/**
 * Validate and sanitize AI prompt input
 */
export function validateAIPrompt(prompt: string): { isValid: boolean; sanitized: string; errors: string[] } {
  const errors: string[] = [];
  
  if (!prompt || typeof prompt !== 'string') {
    return { isValid: false, sanitized: '', errors: ['Prompt is required'] };
  }

  if (prompt.length > 1000) {
    errors.push('Prompt exceeds maximum length of 1000 characters');
  }

  // Check for injection attempts in prompts
  const dangerousPatterns = [
    /ignore\s+previous\s+instructions/i,
    /system\s*:/i,
    /assistant\s*:/i,
    /\bDROP\b/i,
    /\bDELETE\b/i,
    /\bUPDATE\b/i,
    /\bINSERT\b/i,
    /\bALTER\b/i,
    /\bCREATE\b/i,
    /\bEXEC\b/i,
    /\bEXECUTE\b/i
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(prompt)) {
      errors.push('Potentially dangerous content detected in prompt');
      break;
    }
  }

  const sanitized = sanitizeUserInput(prompt);

  return {
    isValid: errors.length === 0,
    sanitized,
    errors
  };
}

/**
 * Build safe SQL query with parameterized values
 */
export function buildSafeQuery(
  baseQuery: string,
  parameters: Record<string, any>,
  config: SQLValidationConfig = DEFAULT_SQL_CONFIG
): { query: string; isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate base query structure
  const validation = validateAIGeneratedSQL(baseQuery, config);
  if (!validation.isValid) {
    return {
      query: '',
      isValid: false,
      errors: validation.errors
    };
  }

  // Validate parameters
  for (const [key, value] of Object.entries(parameters)) {
    if (typeof value === 'string') {
      const sanitized = sanitizeUserInput(value);
      if (sanitized !== value) {
        errors.push(`Parameter '${key}' contains potentially dangerous content`);
      }
    }
  }

  return {
    query: baseQuery,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate column selection to prevent data exposure
 */
export function validateColumnSelection(columns: string[], tableName: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Define sensitive columns that should not be exposed
  const sensitiveColumns: Record<string, string[]> = {
    profiles: ['password_hash', 'mfa_secret', 'recovery_codes'],
    households: ['internal_notes', 'admin_flags'],
    users: ['password', 'salt', 'session_token']
  };

  const tableColumns = sensitiveColumns[tableName.toLowerCase()] || [];

  for (const column of columns) {
    if (column === '*') {
      errors.push('Wildcard selection (*) not allowed - specify explicit columns');
      continue;
    }

    if (tableColumns.includes(column.toLowerCase())) {
      errors.push(`Sensitive column '${column}' cannot be selected`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create audit log entry for SQL validation failures
 */
export function logSQLValidationFailure(
  query: string,
  errors: string[],
  threats: string[],
  userId?: string,
  ipAddress?: string
): void {
  // In a real application, this would log to a security monitoring system
  const logEntry = {
    timestamp: new Date().toISOString(),
    event: 'SQL_VALIDATION_FAILURE',
    query: query.substring(0, 200), // Truncate for logging
    errors,
    threats,
    userId,
    ipAddress,
    severity: threats.length > 0 ? 'HIGH' : 'MEDIUM'
  };

  // For now, we'll just log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.warn('SQL Validation Failure:', logEntry);
  }

  // In production, this should be sent to a security monitoring service
  // Example: await securityLogger.log(logEntry);
}
