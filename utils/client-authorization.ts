/**
 * Client-Side Authorization Utilities
 *
 * Provides client-side authorization checks for IDOR protection.
 * This file is separate from utils/authorization.ts to avoid importing
 * server-side utilities in client components.
 */

import { createClient } from '@/utils/supabase/client';

export interface AuthorizedUser {
  id: string;
  email?: string;
  profile?: {
    org_id?: string;
    org_role?: string;
    name?: string;
  };
}

export interface AuthorizedResource {
  user: AuthorizedUser;
  resource: any;
  hasAccess: boolean;
}

/**
 * Verify household access for client components
 * Returns authorization result without redirecting
 */
export async function verifyHouseholdAccessClient(householdId: string): Promise<AuthorizedResource | null> {
  const supabase = createClient();

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    // Get user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      return null;
    }

    // Get household data
    const { data: household, error: householdError } = await supabase
      .from('households')
      .select('id, user_id, org_id, "householdName"')
      .eq('id', householdId)
      .single();

    if (householdError || !household) {
      return null;
    }

    // Check access permissions
    const hasAccess = household.user_id === user.id ||
                     (household.org_id && household.org_id === profile?.org_id);

    return {
      user: { ...user, profile },
      resource: household,
      hasAccess
    };
  } catch (error) {
    return null;
  }
}

/**
 * Verify scenario access for client components
 */
export async function verifyScenarioAccessClient(scenarioId: string): Promise<AuthorizedResource | null> {
  const supabase = createClient();

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    // Get scenario with household information
    const { data: scenario, error: scenarioError } = await supabase
      .from('scenarios_data1')
      .select(`
        id,
        scenario_name,
        household_id,
        user_id,
        org_id,
        households!inner (
          id,
          user_id,
          org_id,
          householdName
        )
      `)
      .eq('id', scenarioId)
      .single();

    if (scenarioError || !scenario) {
      return null;
    }

    // Get user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      return null;
    }

    // Check access permissions - user owns scenario or household, or same organization
    const household = Array.isArray(scenario.households) ? scenario.households[0] : scenario.households;
    const hasAccess = scenario.user_id === user.id ||
                     (household && household.user_id === user.id) ||
                     (scenario.org_id && scenario.org_id === profile?.org_id) ||
                     (household && household.org_id && household.org_id === profile?.org_id);

    return {
      user: { ...user, profile },
      resource: scenario,
      hasAccess
    };
  } catch (error) {
    return null;
  }
}

/**
 * Verify note access for client components
 */
export async function verifyNoteAccessClient(noteId: string): Promise<AuthorizedResource | null> {
  const supabase = createClient();

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    // Get note data
    const { data: note, error: noteError } = await supabase
      .from('notes')
      .select('id, title, user_id, org_id, household_id')
      .eq('id', noteId)
      .single();

    if (noteError || !note) {
      return null;
    }

    // Get user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      return null;
    }

    // Check access permissions
    const hasAccess = note.user_id === user.id ||
                     (note.org_id && note.org_id === profile?.org_id);

    return {
      user: { ...user, profile },
      resource: note,
      hasAccess
    };
  } catch (error) {
    return null;
  }
}

/**
 * Input validation for IDs to prevent injection attacks
 */
export function validateId(id: string, type: 'household' | 'scenario' | 'note' | 'user' = 'household'): string {
  if (!id || typeof id !== 'string') {
    throw new Error(`Invalid ${type} ID`);
  }

  // Remove any whitespace
  id = id.trim();

  // Check for basic format (numeric or UUID)
  const isNumeric = /^\d+$/.test(id);
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);

  if (!isNumeric && !isUUID) {
    throw new Error(`Invalid ${type} ID format`);
  }

  return id;
}
