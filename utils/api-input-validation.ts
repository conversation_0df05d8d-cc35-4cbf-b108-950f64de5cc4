/**
 * API Input Validation Middleware
 *
 * Comprehensive input validation for API routes to prevent injection attacks,
 * data corruption, and security vulnerabilities.
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  validateHouseholdId, 
  validateUUID, 
  validateTextInput, 
  validateNumericInput,
  validateEmail,
  validatePhoneNumber,
  validateDate,
  validateJSONInput
} from './validation';

/**
 * Validation schema interface
 */
export interface ValidationSchema {
  [key: string]: {
    type: 'string' | 'number' | 'email' | 'phone' | 'date' | 'uuid' | 'householdId' | 'json' | 'boolean';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    allowEmpty?: boolean;
    sanitize?: boolean;
  };
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  validatedData: Record<string, any>;
  sanitizedData: Record<string, any>;
}

/**
 * Validate request body against schema
 */
export async function validateRequestBody(
  request: NextRequest,
  schema: ValidationSchema
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    validatedData: {},
    sanitizedData: {}
  };

  try {
    const body = await request.json();
    
    // Validate each field in the schema
    for (const [fieldName, fieldSchema] of Object.entries(schema)) {
      const value = body[fieldName];
      
      try {
        // Check if required field is missing
        if (fieldSchema.required && (value === undefined || value === null)) {
          result.errors.push(`${fieldName} is required`);
          result.isValid = false;
          continue;
        }
        
        // Skip validation for optional empty fields
        if (!fieldSchema.required && (value === undefined || value === null || value === '')) {
          result.validatedData[fieldName] = value;
          result.sanitizedData[fieldName] = value;
          continue;
        }
        
        // Validate based on type
        let validatedValue: any;
        
        switch (fieldSchema.type) {
          case 'string':
            validatedValue = validateTextInput(value, {
              minLength: fieldSchema.minLength,
              maxLength: fieldSchema.maxLength,
              allowEmpty: fieldSchema.allowEmpty,
              fieldName,
              pattern: fieldSchema.pattern,
              sanitize: fieldSchema.sanitize
            });
            break;
            
          case 'number':
            validatedValue = validateNumericInput(value, {
              min: fieldSchema.min,
              max: fieldSchema.max,
              fieldName
            });
            break;
            
          case 'email':
            validatedValue = validateEmail(value, fieldName);
            break;
            
          case 'phone':
            validatedValue = validatePhoneNumber(value, fieldName);
            break;
            
          case 'date':
            validatedValue = validateDate(value, fieldName);
            break;
            
          case 'uuid':
            validatedValue = validateUUID(value);
            break;
            
          case 'householdId':
            validatedValue = validateHouseholdId(value);
            break;
            
          case 'json':
            validatedValue = validateJSONInput(value, fieldName);
            break;
            
          case 'boolean':
            if (typeof value !== 'boolean') {
              throw new Error(`${fieldName} must be a boolean`);
            }
            validatedValue = value;
            break;
            
          default:
            throw new Error(`Unknown validation type: ${fieldSchema.type}`);
        }
        
        result.validatedData[fieldName] = validatedValue;
        result.sanitizedData[fieldName] = validatedValue;
        
      } catch (error) {
        result.errors.push(error instanceof Error ? error.message : `Invalid ${fieldName}`);
        result.isValid = false;
      }
    }
    
    // Check for unexpected fields
    for (const key of Object.keys(body)) {
      if (!schema[key]) {
        result.errors.push(`Unexpected field: ${key}`);
        result.isValid = false;
      }
    }
    
  } catch (error) {
    result.errors.push('Invalid JSON in request body');
    result.isValid = false;
  }

  return result;
}

/**
 * Validate URL parameters
 */
export function validateURLParams(
  params: Record<string, string | string[]>,
  schema: ValidationSchema
): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    validatedData: {},
    sanitizedData: {}
  };

  for (const [fieldName, fieldSchema] of Object.entries(schema)) {
    const value = params[fieldName];
    
    try {
      // Check if required field is missing
      if (fieldSchema.required && !value) {
        result.errors.push(`${fieldName} parameter is required`);
        result.isValid = false;
        continue;
      }
      
      // Skip validation for optional empty fields
      if (!fieldSchema.required && !value) {
        result.validatedData[fieldName] = value;
        result.sanitizedData[fieldName] = value;
        continue;
      }
      
      // Convert array to string if needed
      const stringValue = Array.isArray(value) ? value[0] : value;
      
      // Validate based on type
      let validatedValue: any;
      
      switch (fieldSchema.type) {
        case 'string':
          validatedValue = validateTextInput(stringValue, {
            minLength: fieldSchema.minLength,
            maxLength: fieldSchema.maxLength,
            allowEmpty: fieldSchema.allowEmpty,
            fieldName,
            pattern: fieldSchema.pattern,
            sanitize: fieldSchema.sanitize
          });
          break;
          
        case 'number':
          validatedValue = validateNumericInput(stringValue, {
            min: fieldSchema.min,
            max: fieldSchema.max,
            fieldName
          });
          break;
          
        case 'uuid':
          validatedValue = validateUUID(stringValue);
          break;
          
        case 'householdId':
          validatedValue = validateHouseholdId(stringValue);
          break;
          
        default:
          validatedValue = validateTextInput(stringValue, {
            fieldName,
            sanitize: true
          });
      }
      
      result.validatedData[fieldName] = validatedValue;
      result.sanitizedData[fieldName] = validatedValue;
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : `Invalid ${fieldName} parameter`);
      result.isValid = false;
    }
  }

  return result;
}

/**
 * Validate query parameters
 */
export function validateQueryParams(
  searchParams: URLSearchParams,
  schema: ValidationSchema
): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    validatedData: {},
    sanitizedData: {}
  };

  for (const [fieldName, fieldSchema] of Object.entries(schema)) {
    const value = searchParams.get(fieldName);
    
    try {
      // Check if required field is missing
      if (fieldSchema.required && !value) {
        result.errors.push(`${fieldName} query parameter is required`);
        result.isValid = false;
        continue;
      }
      
      // Skip validation for optional empty fields
      if (!fieldSchema.required && !value) {
        result.validatedData[fieldName] = value;
        result.sanitizedData[fieldName] = value;
        continue;
      }
      
      // Validate based on type
      let validatedValue: any;
      
      switch (fieldSchema.type) {
        case 'string':
          validatedValue = validateTextInput(value, {
            minLength: fieldSchema.minLength,
            maxLength: fieldSchema.maxLength,
            allowEmpty: fieldSchema.allowEmpty,
            fieldName,
            pattern: fieldSchema.pattern,
            sanitize: fieldSchema.sanitize
          });
          break;
          
        case 'number':
          validatedValue = validateNumericInput(value, {
            min: fieldSchema.min,
            max: fieldSchema.max,
            fieldName
          });
          break;
          
        case 'boolean':
          validatedValue = value === 'true' || value === '1';
          break;
          
        default:
          validatedValue = validateTextInput(value, {
            fieldName,
            sanitize: true
          });
      }
      
      result.validatedData[fieldName] = validatedValue;
      result.sanitizedData[fieldName] = validatedValue;
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : `Invalid ${fieldName} query parameter`);
      result.isValid = false;
    }
  }

  return result;
}

/**
 * Higher-order function for API route validation
 */
export function withInputValidation(
  handler: (request: NextRequest, context: any, validatedData: any) => Promise<Response>,
  options: {
    bodySchema?: ValidationSchema;
    paramsSchema?: ValidationSchema;
    querySchema?: ValidationSchema;
  }
) {
  return async (request: NextRequest, context: any): Promise<Response> => {
    const validatedData: any = {};
    const allErrors: string[] = [];

    // Validate request body if schema provided
    if (options.bodySchema && (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')) {
      const bodyValidation = await validateRequestBody(request, options.bodySchema);
      if (!bodyValidation.isValid) {
        allErrors.push(...bodyValidation.errors);
      } else {
        validatedData.body = bodyValidation.validatedData;
      }
    }

    // Validate URL parameters if schema provided
    if (options.paramsSchema && context.params) {
      const paramsValidation = validateURLParams(context.params, options.paramsSchema);
      if (!paramsValidation.isValid) {
        allErrors.push(...paramsValidation.errors);
      } else {
        validatedData.params = paramsValidation.validatedData;
      }
    }

    // Validate query parameters if schema provided
    if (options.querySchema) {
      const queryValidation = validateQueryParams(request.nextUrl.searchParams, options.querySchema);
      if (!queryValidation.isValid) {
        allErrors.push(...queryValidation.errors);
      } else {
        validatedData.query = queryValidation.validatedData;
      }
    }

    // Return validation errors if any
    if (allErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: allErrors,
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      );
    }

    // Call the original handler with validated data
    return await handler(request, context, validatedData);
  };
}
