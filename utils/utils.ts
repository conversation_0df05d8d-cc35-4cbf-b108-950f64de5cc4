import { redirect } from "next/navigation";

/**
 * Redirects to a specified path with an encoded message as a query parameter.
 * @param {('error' | 'success' | 'warning')} type - The type of message: 'error', 'success', or 'warning'.
 * @param {string} path - The path to redirect to.
 * @param {string} message - The message to be encoded and added as a query parameter.
 * @returns {never} This function doesn't return as it triggers a redirect.
 */
export function encodedRedirect(
  type: "error" | "success" | "warning",
  path: string,
  message: string,
) {
  return redirect(`${path}?status=${type}&message=${encodeURIComponent(message)}`);
}
