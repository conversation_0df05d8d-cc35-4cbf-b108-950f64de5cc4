/**
 * Enhanced Route Protection Utilities
 *
 * Provides comprehensive route protection that combines server-side and client-side
 * security measures to prevent authentication bypass vulnerabilities.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { requireAuth, getAuthUser } from '@/utils/auth-guard';
import { redirect } from 'next/navigation';

/**
 * Route protection configuration
 */
export interface RouteProtectionConfig {
  requireAuth: boolean;
  requireMFA?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
  bypassRoutes?: string[];
}

/**
 * Default protection configurations for different route types
 */
export const ROUTE_CONFIGS: Record<string, RouteProtectionConfig> = {
  public: {
    requireAuth: false,
    bypassRoutes: ['/sign-in', '/sign-up', '/reset-password', '/']
  },
  protected: {
    requireAuth: true,
    redirectTo: '/sign-in'
  },
  admin: {
    requireAuth: true,
    allowedRoles: ['admin', 'owner'],
    redirectTo: '/unauthorized'
  },
  mfa: {
    requireAuth: true,
    requireMFA: true,
    redirectTo: '/mfa-challenge'
  }
};

/**
 * Server-side route protection for middleware
 */
export async function protectRoute(
  request: NextRequest,
  config: RouteProtectionConfig
): Promise<NextResponse | null> {
  const pathname = request.nextUrl.pathname;

  // Check if route should be bypassed
  if (config.bypassRoutes?.some(route => pathname.startsWith(route))) {
    return null; // Allow through
  }

  // If authentication is not required, allow through
  if (!config.requireAuth) {
    return null;
  }

  try {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    // If no user or error, redirect to sign-in
    if (error || !user) {
      const redirectUrl = new URL(config.redirectTo || '/sign-in', request.url);
      redirectUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // Check role requirements
    if (config.allowedRoles && config.allowedRoles.length > 0) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', user.id)
        .single();

      if (profileError || !profile || !config.allowedRoles.includes(profile.org_role)) {
        return NextResponse.redirect(new URL(config.redirectTo || '/unauthorized', request.url));
      }
    }

    // Check MFA requirements
    if (config.requireMFA) {
      const { data: aalData } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      
      if (aalData && aalData.currentLevel !== 'aal2') {
        return NextResponse.redirect(new URL('/mfa-challenge', request.url));
      }
    }

    return null; // Allow through
  } catch (error) {
    // If any check fails, redirect to sign-in
    const redirectUrl = new URL(config.redirectTo || '/sign-in', request.url);
    redirectUrl.searchParams.set('redirectTo', pathname);
    return NextResponse.redirect(redirectUrl);
  }
}

/**
 * Server component route protection
 */
export async function protectServerRoute(config: RouteProtectionConfig): Promise<any> {
  if (!config.requireAuth) {
    return null; // No protection needed
  }

  try {
    const user = await requireAuth();

    // Check role requirements
    if (config.allowedRoles && config.allowedRoles.length > 0) {
      const supabase = createClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', user.id)
        .single();

      if (error || !profile || !config.allowedRoles.includes(profile.org_role)) {
        redirect(config.redirectTo || '/unauthorized');
      }
    }

    // Check MFA requirements
    if (config.requireMFA) {
      const { requireMFA } = await import('@/utils/auth-guard');
      await requireMFA();
    }

    return user;
  } catch (error) {
    redirect(config.redirectTo || '/sign-in');
  }
}

/**
 * API route protection
 */
export async function protectApiRoute(
  request: NextRequest,
  config: RouteProtectionConfig
): Promise<{ user: any; error?: string; status?: number }> {
  if (!config.requireAuth) {
    return { user: null };
  }

  try {
    const user = await getAuthUser();

    if (!user) {
      return {
        user: null,
        error: 'Authentication required',
        status: 401
      };
    }

    // Check role requirements
    if (config.allowedRoles && config.allowedRoles.length > 0) {
      const supabase = createClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', user.id)
        .single();

      if (error || !profile || !config.allowedRoles.includes(profile.org_role)) {
        return {
          user: null,
          error: 'Insufficient permissions',
          status: 403
        };
      }
    }

    // Check MFA requirements
    if (config.requireMFA) {
      const supabase = createClient();
      const { data: aalData } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      
      if (!aalData || aalData.currentLevel !== 'aal2') {
        return {
          user: null,
          error: 'MFA verification required',
          status: 403
        };
      }
    }

    return { user };
  } catch (error) {
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Higher-order function for protecting API routes
 */
export function withRouteProtection(
  handler: (request: NextRequest, context: any, user: any) => Promise<Response>,
  config: RouteProtectionConfig
) {
  return async (request: NextRequest, context: any): Promise<Response> => {
    const protection = await protectApiRoute(request, config);

    if (protection.error) {
      return new Response(
        JSON.stringify({ error: protection.error }),
        {
          status: protection.status || 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return await handler(request, context, protection.user);
  };
}

/**
 * Utility to get route protection config based on pathname
 */
export function getRouteConfig(pathname: string): RouteProtectionConfig {
  if (pathname.startsWith('/protected/admin/')) {
    return ROUTE_CONFIGS.admin;
  }
  
  if (pathname.startsWith('/protected/')) {
    return ROUTE_CONFIGS.protected;
  }
  
  if (pathname.startsWith('/api/admin/')) {
    return ROUTE_CONFIGS.admin;
  }
  
  if (pathname.startsWith('/api/protected/')) {
    return ROUTE_CONFIGS.protected;
  }

  return ROUTE_CONFIGS.public;
}
