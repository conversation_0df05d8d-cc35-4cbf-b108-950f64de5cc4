/**
 * Profile Utilities
 * 
 * Utilities for managing user profiles and ensuring they exist
 * when users sign up or log in.
 */

import { createClient } from '@/utils/supabase/client';

export interface UserProfile {
  id: number;
  user_id: string;
  name: string | null;
  email: string | null;
  org_id: string | null;
  org_name: string | null;
  org_role: string;
  mfa_enabled: boolean | null;
  logo_path: string | null;
  notification_preferences: any | null;
  preferences: any | null;
}

/**
 * Ensure the current user has a profile record
 * Creates one if it doesn't exist
 */
export async function ensureUserProfile(): Promise<UserProfile | null> {
  const supabase = createClient();

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('Error getting user:', userError);
      return null;
    }

    // First, try to get the existing profile
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // If profile exists, return it
    if (existingProfile && !fetchError) {
      return existingProfile;
    }

    // If profile doesn't exist, call the server function to create it
    const { error: ensureError } = await supabase.rpc('ensure_user_profile');
    
    if (ensureError) {
      console.error('Error ensuring profile:', ensureError);
      return null;
    }

    // Now fetch the newly created profile
    const { data: newProfile, error: newFetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (newFetchError) {
      console.error('Error fetching new profile:', newFetchError);
      return null;
    }

    return newProfile;
  } catch (error) {
    console.error('Error in ensureUserProfile:', error);
    return null;
  }
}

/**
 * Get the current user's profile
 * Ensures the profile exists before returning it
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  const supabase = createClient();

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return null;
    }

    // Try to get the profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // If profile doesn't exist, ensure it's created
    if (profileError && profileError.code === 'PGRST116') {
      return await ensureUserProfile();
    }

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return null;
    }

    return profile;
  } catch (error) {
    console.error('Error in getUserProfile:', error);
    return null;
  }
}

/**
 * Update the current user's profile
 */
export async function updateUserProfile(updates: Partial<UserProfile>): Promise<UserProfile | null> {
  const supabase = createClient();

  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('Error getting user:', userError);
      return null;
    }

    // Ensure profile exists first
    await ensureUserProfile();

    // Update the profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update(updates)
      .eq('user_id', user.id)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return null;
    }

    return updatedProfile;
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    return null;
  }
}

/**
 * Check if the current user has a complete profile
 */
export async function hasCompleteProfile(): Promise<boolean> {
  const profile = await getUserProfile();
  
  if (!profile) {
    return false;
  }

  // Check if essential fields are filled
  return !!(profile.name && profile.email);
}

/**
 * Initialize user profile on first login/signup
 * This should be called after successful authentication
 */
export async function initializeUserProfile(): Promise<UserProfile | null> {
  try {
    // Ensure the profile exists
    const profile = await ensureUserProfile();
    
    if (!profile) {
      console.error('Failed to initialize user profile');
      return null;
    }

    return profile;
  } catch (error) {
    console.error('Error initializing user profile:', error);
    return null;
  }
}
