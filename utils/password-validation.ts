/**
 * Password Validation Utilities
 * 
 * Comprehensive password validation that matches Supabase's enhanced
 * password policies and provides user-friendly feedback.
 */

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: PasswordStrength;
  score: number;
}

export interface PasswordStrength {
  level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  label: string;
  color: string;
  percentage: number;
}

/**
 * Comprehensive password validation
 * Matches Supabase's enhanced password requirements
 */
export function validatePassword(password: string): PasswordValidationResult {
  const errors: string[] = [];
  let score = 0;

  // Minimum length requirement (8 characters)
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  // Uppercase letter requirement
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase letter requirement
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number requirement
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character requirement
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Additional strength checks
  if (password.length >= 16) {
    score += 1; // Bonus for longer passwords
  }

  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password should not contain repeated characters');
    score = Math.max(0, score - 1);
  }

  // Check for common patterns
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push('Password contains common patterns and may be easily guessed');
      score = Math.max(0, score - 1);
      break;
    }
  }

  const strength = calculatePasswordStrength(score, password.length);
  
  return {
    isValid: errors.length === 0,
    errors,
    strength,
    score
  };
}

/**
 * Calculate password strength based on score and length
 */
function calculatePasswordStrength(score: number, length: number): PasswordStrength {
  // Adjust score based on length
  let adjustedScore = score;
  if (length >= 20) adjustedScore += 1;
  if (length >= 24) adjustedScore += 1;

  if (adjustedScore <= 1) {
    return {
      level: 'very-weak',
      label: 'Very Weak',
      color: '#ef4444', // red-500
      percentage: 20
    };
  } else if (adjustedScore <= 2) {
    return {
      level: 'weak',
      label: 'Weak',
      color: '#f97316', // orange-500
      percentage: 40
    };
  } else if (adjustedScore <= 3) {
    return {
      level: 'fair',
      label: 'Fair',
      color: '#eab308', // yellow-500
      percentage: 60
    };
  } else if (adjustedScore <= 4) {
    return {
      level: 'good',
      label: 'Good',
      color: '#22c55e', // green-500
      percentage: 80
    };
  } else {
    return {
      level: 'strong',
      label: 'Strong',
      color: '#16a34a', // green-600
      percentage: 100
    };
  }
}

/**
 * Check if password meets minimum security requirements
 * (for form validation)
 */
export function isPasswordSecure(password: string): boolean {
  const result = validatePassword(password);
  return result.isValid && result.strength.level !== 'very-weak';
}

/**
 * Generate password requirements text for UI
 */
export function getPasswordRequirements(): string[] {
  return [
    'At least 8 characters long',
    'Contains uppercase letters (A-Z)',
    'Contains lowercase letters (a-z)',
    'Contains numbers (0-9)',
    'Contains special characters (!@#$%^&*)',
    'Avoid common patterns or repeated characters'
  ];
}

/**
 * Check if two passwords match
 */
export function doPasswordsMatch(password: string, confirmPassword: string): boolean {
  return password === confirmPassword && password.length > 0;
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(
  password: string, 
  confirmPassword: string
): { isValid: boolean; error?: string } {
  if (!confirmPassword) {
    return { isValid: true }; // Don't show error until user starts typing
  }

  if (!doPasswordsMatch(password, confirmPassword)) {
    return { 
      isValid: false, 
      error: 'Passwords do not match' 
    };
  }

  return { isValid: true };
}

/**
 * Generate a secure password suggestion
 */
export function generateSecurePassword(length: number = 16): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}
