/**
 * API Authorization Middleware
 *
 * Provides server-side authorization checks for API routes to prevent IDOR attacks.
 * Used in API endpoints to verify user access before processing requests.
 */

import { createClient } from '@/utils/supabase/server';
import { NextRequest } from 'next/server';

export interface ApiAuthResult {
  success: boolean;
  user?: any;
  profile?: any;
  error?: string;
  statusCode?: number;
}

export interface ResourceAuthResult extends ApiAuthResult {
  resource?: any;
  hasAccess?: boolean;
}

/**
 * Authenticate user from API request
 */
export async function authenticateApiUser(request: NextRequest): Promise<ApiAuthResult> {
  try {
    const supabase = createClient();

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
        statusCode: 401
      };
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      return {
        success: false,
        error: 'Profile not found',
        statusCode: 403
      };
    }

    return {
      success: true,
      user,
      profile
    };
  } catch (error) {
    return {
      success: false,
      error: 'Authentication failed',
      statusCode: 500
    };
  }
}

/**
 * Verify household access for API routes
 */
export async function verifyHouseholdAccessApi(
  request: NextRequest,
  householdId: string
): Promise<ResourceAuthResult> {
  // First authenticate the user
  const authResult = await authenticateApiUser(request);
  if (!authResult.success) {
    return authResult;
  }

  try {
    const supabase = createClient();

    // Get household data
    const { data: household, error: householdError } = await supabase
      .from('households')
      .select('id, user_id, org_id, householdName')
      .eq('id', householdId)
      .single();

    if (householdError || !household) {
      return {
        success: false,
        error: 'Household not found',
        statusCode: 404
      };
    }

    // Check access permissions
    const hasAccess = household.user_id === authResult.user!.id ||
                     (household.org_id && household.org_id === authResult.profile?.org_id);

    if (!hasAccess) {
      return {
        success: false,
        error: 'Access denied to household',
        statusCode: 403
      };
    }

    return {
      success: true,
      user: authResult.user,
      profile: authResult.profile,
      resource: household,
      hasAccess: true
    };
  } catch (error) {
    return {
      success: false,
      error: 'Access verification failed',
      statusCode: 500
    };
  }
}

/**
 * Verify scenario access for API routes
 */
export async function verifyScenarioAccessApi(
  request: NextRequest,
  scenarioId: string
): Promise<ResourceAuthResult> {
  // First authenticate the user
  const authResult = await authenticateApiUser(request);
  if (!authResult.success) {
    return authResult;
  }

  try {
    const supabase = createClient();

    // Get scenario with household information
    const { data: scenario, error: scenarioError } = await supabase
      .from('scenarios_data1')
      .select(`
        id,
        scenario_name,
        household_id,
        user_id,
        org_id,
        households (
          id,
          user_id,
          org_id,
          householdName
        )
      `)
      .eq('id', scenarioId)
      .single();

    if (scenarioError || !scenario) {
      return {
        success: false,
        error: 'Scenario not found',
        statusCode: 404
      };
    }

    // Check access permissions
    const hasAccess = scenario.user_id === authResult.user!.id ||
                     (scenario.households?.[0] && scenario.households[0].user_id === authResult.user!.id) ||
                     (scenario.org_id && scenario.org_id === authResult.profile?.org_id) ||
                     (scenario.households?.[0] && scenario.households[0].org_id &&
                      scenario.households[0].org_id === authResult.profile?.org_id);

    if (!hasAccess) {
      return {
        success: false,
        error: 'Access denied to scenario',
        statusCode: 403
      };
    }

    return {
      success: true,
      user: authResult.user,
      profile: authResult.profile,
      resource: scenario,
      hasAccess: true
    };
  } catch (error) {
    return {
      success: false,
      error: 'Access verification failed',
      statusCode: 500
    };
  }
}

/**
 * Verify note access for API routes
 */
export async function verifyNoteAccessApi(
  request: NextRequest,
  noteId: string
): Promise<ResourceAuthResult> {
  // First authenticate the user
  const authResult = await authenticateApiUser(request);
  if (!authResult.success) {
    return authResult;
  }

  try {
    const supabase = createClient();

    // Get note data
    const { data: note, error: noteError } = await supabase
      .from('notes')
      .select('id, title, user_id, org_id, household_id')
      .eq('id', noteId)
      .single();

    if (noteError || !note) {
      return {
        success: false,
        error: 'Note not found',
        statusCode: 404
      };
    }

    // Check access permissions
    const hasAccess = note.user_id === authResult.user!.id ||
                     (note.org_id && note.org_id === authResult.profile?.org_id);

    if (!hasAccess) {
      return {
        success: false,
        error: 'Access denied to note',
        statusCode: 403
      };
    }

    return {
      success: true,
      user: authResult.user,
      profile: authResult.profile,
      resource: note,
      hasAccess: true
    };
  } catch (error) {
    return {
      success: false,
      error: 'Access verification failed',
      statusCode: 500
    };
  }
}

/**
 * Verify organization access for API routes
 */
export async function verifyOrganizationAccessApi(
  request: NextRequest,
  orgId: string
): Promise<ResourceAuthResult> {
  // First authenticate the user
  const authResult = await authenticateApiUser(request);
  if (!authResult.success) {
    return authResult;
  }

  try {
    const supabase = createClient();

    // Get organization data
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, created_by')
      .eq('id', orgId)
      .single();

    if (orgError || !organization) {
      return {
        success: false,
        error: 'Organization not found',
        statusCode: 404
      };
    }

    // Check access permissions - user must be member of the organization
    const hasAccess = authResult.profile?.org_id === orgId;

    if (!hasAccess) {
      return {
        success: false,
        error: 'Access denied to organization',
        statusCode: 403
      };
    }

    return {
      success: true,
      user: authResult.user,
      profile: authResult.profile,
      resource: organization,
      hasAccess: true
    };
  } catch (error) {
    return {
      success: false,
      error: 'Access verification failed',
      statusCode: 500
    };
  }
}

/**
 * Create standardized error response for API routes
 */
export function createErrorResponse(error: string, statusCode: number = 400) {
  return new Response(
    JSON.stringify({ error }),
    {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

/**
 * Create standardized success response for API routes
 */
export function createSuccessResponse(data: any, statusCode: number = 200) {
  return new Response(
    JSON.stringify(data),
    {
      status: statusCode,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}
