/**
 * Input Validation Utilities
 *
 * Provides comprehensive input validation to prevent SQL injection,
 * XSS attacks, and other security vulnerabilities.
 */

/**
 * Validate household ID format and sanitize input
 */
export function validateHouseholdId(id: string): string {
  if (!id || typeof id !== 'string') {
    throw new Error('Invalid household ID');
  }

  // Remove whitespace and convert to string
  const cleanId = String(id).trim();

  // Check for empty string after trimming
  if (!cleanId) {
    throw new Error('Household ID cannot be empty');
  }

  // Validate format - should be numeric for household IDs
  if (!/^\d+$/.test(cleanId)) {
    throw new Error('Invalid household ID format - must be numeric');
  }

  // Check reasonable length (prevent extremely long IDs)
  if (cleanId.length > 20) {
    throw new Error('Household ID too long');
  }

  return cleanId;
}

/**
 * Validate UUID format
 */
export function validateUUID(id: string, fieldName: string = 'ID'): string {
  if (!id || typeof id !== 'string') {
    throw new Error(`Invalid ${fieldName}`);
  }

  const cleanId = id.trim();

  if (!cleanId) {
    throw new Error(`${fieldName} cannot be empty`);
  }

  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  if (!uuidRegex.test(cleanId)) {
    throw new Error(`Invalid ${fieldName} format - must be a valid UUID`);
  }

  return cleanId;
}

/**
 * Validate scenario ID (can be numeric or UUID)
 */
export function validateScenarioId(id: string): string {
  if (!id || typeof id !== 'string') {
    throw new Error('Invalid scenario ID');
  }

  const cleanId = String(id).trim();

  if (!cleanId) {
    throw new Error('Scenario ID cannot be empty');
  }

  // Check if it's numeric
  const isNumeric = /^\d+$/.test(cleanId);

  // Check if it's UUID
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(cleanId);

  if (!isNumeric && !isUUID) {
    throw new Error('Invalid scenario ID format - must be numeric or UUID');
  }

  // Check reasonable length
  if (cleanId.length > 50) {
    throw new Error('Scenario ID too long');
  }

  return cleanId;
}

/**
 * Validate note ID
 */
export function validateNoteId(id: string): string {
  if (!id || typeof id !== 'string') {
    throw new Error('Invalid note ID');
  }

  const cleanId = String(id).trim();

  if (!cleanId) {
    throw new Error('Note ID cannot be empty');
  }

  // Notes typically use numeric IDs
  if (!/^\d+$/.test(cleanId)) {
    throw new Error('Invalid note ID format - must be numeric');
  }

  if (cleanId.length > 20) {
    throw new Error('Note ID too long');
  }

  return cleanId;
}

/**
 * Validate user ID (UUID format)
 */
export function validateUserId(id: string): string {
  return validateUUID(id, 'User ID');
}

/**
 * Validate organization ID (UUID format)
 */
export function validateOrgId(id: string): string {
  return validateUUID(id, 'Organization ID');
}

/**
 * Validate document path for storage access
 */
export function validateDocumentPath(path: string): string {
  if (!path || typeof path !== 'string') {
    throw new Error('Invalid document path');
  }

  const cleanPath = path.trim();

  if (!cleanPath) {
    throw new Error('Document path cannot be empty');
  }

  // Check for path traversal attempts
  if (cleanPath.includes('..') || cleanPath.includes('//')) {
    throw new Error('Invalid document path - path traversal not allowed');
  }

  // Check for absolute paths (should be relative)
  if (cleanPath.startsWith('/')) {
    throw new Error('Invalid document path - absolute paths not allowed');
  }

  // Check reasonable length
  if (cleanPath.length > 500) {
    throw new Error('Document path too long');
  }

  // Basic file extension validation (optional)
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.gif'];
  allowedExtensions.some(ext =>
    cleanPath.toLowerCase().endsWith(ext)
  );

  // Note: Unusual extensions are allowed but not validated

  return cleanPath;
}

/**
 * Sanitize string input to prevent XSS
 */
export function sanitizeString(input: string, maxLength: number = 1000): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input.trim();

  // Limit length
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }

  // Basic HTML entity encoding for common XSS vectors
  sanitized = sanitized
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');

  return sanitized;
}



/**
 * Validate pagination parameters
 */
export function validatePagination(page?: string, limit?: string): { page: number; limit: number } {
  let pageNum = 1;
  let limitNum = 10;

  if (page) {
    const parsedPage = parseInt(page, 10);
    if (isNaN(parsedPage) || parsedPage < 1) {
      throw new Error('Invalid page number');
    }
    if (parsedPage > 10000) {
      throw new Error('Page number too large');
    }
    pageNum = parsedPage;
  }

  if (limit) {
    const parsedLimit = parseInt(limit, 10);
    if (isNaN(parsedLimit) || parsedLimit < 1) {
      throw new Error('Invalid limit');
    }
    if (parsedLimit > 100) {
      throw new Error('Limit too large - maximum 100 items per page');
    }
    limitNum = parsedLimit;
  }

  return { page: pageNum, limit: limitNum };
}

/**
 * Validate sort parameters
 */
export function validateSort(sort?: string, allowedFields: string[] = []): { field: string; direction: 'asc' | 'desc' } {
  if (!sort) {
    return { field: 'created_at', direction: 'desc' };
  }

  const parts = sort.split(':');
  if (parts.length !== 2) {
    throw new Error('Invalid sort format - use field:direction');
  }

  const [field, direction] = parts;

  if (allowedFields.length > 0 && !allowedFields.includes(field)) {
    throw new Error(`Invalid sort field - allowed fields: ${allowedFields.join(', ')}`);
  }

  if (!['asc', 'desc'].includes(direction)) {
    throw new Error('Invalid sort direction - use asc or desc');
  }

  return { field, direction: direction as 'asc' | 'desc' };
}

/**
 * Validate search query
 */
export function validateSearchQuery(query?: string): string {
  if (!query || typeof query !== 'string') {
    return '';
  }

  let cleanQuery = query.trim();

  // Limit length
  if (cleanQuery.length > 100) {
    cleanQuery = cleanQuery.substring(0, 100);
  }

  // Remove potentially dangerous characters
  cleanQuery = cleanQuery.replace(/[<>'"&]/g, '');

  return cleanQuery;
}

/**
 * Enhanced SQL injection prevention utilities
 */

/**
 * Validate and sanitize SQL table names
 */
export function validateTableName(tableName: string): string {
  if (!tableName || typeof tableName !== 'string') {
    throw new Error('Invalid table name');
  }

  const cleanTableName = tableName.trim().toLowerCase();

  // Allowed table names for the application
  const allowedTables = [
    'households', 'assets', 'liabilities', 'income', 'expenses',
    'scenarios_data1', 'notes', 'custom_reports', 'goals',
    'notifications', 'profiles', 'relationships', 'toe_tokens',
    'tpa_tokens', 'risk_profiler_tokens'
  ];

  if (!allowedTables.includes(cleanTableName)) {
    throw new Error(`Access to table '${tableName}' not allowed`);
  }

  return cleanTableName;
}

/**
 * Validate SQL column names
 */
export function validateColumnName(columnName: string): string {
  if (!columnName || typeof columnName !== 'string') {
    throw new Error('Invalid column name');
  }

  const cleanColumnName = columnName.trim();

  // Check for SQL injection patterns
  if (/[;'"\\]/.test(cleanColumnName)) {
    throw new Error('Invalid characters in column name');
  }

  // Check for SQL keywords
  const sqlKeywords = ['drop', 'delete', 'update', 'insert', 'alter', 'create', 'exec', 'execute'];
  if (sqlKeywords.some(keyword => cleanColumnName.toLowerCase().includes(keyword))) {
    throw new Error('SQL keywords not allowed in column names');
  }

  return cleanColumnName;
}

/**
 * Validate numeric input with range checking
 */
export function validateNumericInput(
  value: any,
  options: {
    min?: number;
    max?: number;
    allowDecimals?: boolean;
    fieldName?: string;
  } = {}
): number {
  const { min, max, allowDecimals = true, fieldName = 'value' } = options;

  if (value === null || value === undefined || value === '') {
    throw new Error(`${fieldName} is required`);
  }

  const numValue = Number(value);

  if (isNaN(numValue)) {
    throw new Error(`${fieldName} must be a valid number`);
  }

  if (!allowDecimals && !Number.isInteger(numValue)) {
    throw new Error(`${fieldName} must be a whole number`);
  }

  if (min !== undefined && numValue < min) {
    throw new Error(`${fieldName} must be at least ${min}`);
  }

  if (max !== undefined && numValue > max) {
    throw new Error(`${fieldName} must not exceed ${max}`);
  }

  return numValue;
}

/**
 * Validate text input with length and content restrictions
 */
export function validateTextInput(
  value: any,
  options: {
    minLength?: number;
    maxLength?: number;
    allowEmpty?: boolean;
    fieldName?: string;
    pattern?: RegExp;
    sanitize?: boolean;
  } = {}
): string {
  const {
    minLength = 0,
    maxLength = 1000,
    allowEmpty = false,
    fieldName = 'field',
    pattern,
    sanitize = true
  } = options;

  if (value === null || value === undefined) {
    if (!allowEmpty) {
      throw new Error(`${fieldName} is required`);
    }
    return '';
  }

  let textValue = String(value).trim();

  if (!allowEmpty && textValue.length === 0) {
    throw new Error(`${fieldName} cannot be empty`);
  }

  if (textValue.length < minLength) {
    throw new Error(`${fieldName} must be at least ${minLength} characters`);
  }

  if (textValue.length > maxLength) {
    throw new Error(`${fieldName} must not exceed ${maxLength} characters`);
  }

  if (pattern && !pattern.test(textValue)) {
    throw new Error(`${fieldName} format is invalid`);
  }

  if (sanitize) {
    // Remove potentially dangerous characters
    textValue = textValue.replace(/[<>'"&]/g, '');

    // Remove SQL injection patterns
    textValue = textValue.replace(/[;\\]/g, '');

    // Remove script tags and other dangerous HTML
    textValue = textValue.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }

  return textValue;
}

/**
 * Validate email address
 */
export function validateEmail(email: string, fieldName: string = 'email'): string {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const cleanEmail = validateTextInput(email, {
    maxLength: 254,
    fieldName,
    pattern: emailPattern,
    allowEmpty: false
  });

  return cleanEmail.toLowerCase();
}

/**
 * Validate phone number
 */
export function validatePhoneNumber(phone: string, fieldName: string = 'phone'): string {
  // Allow various phone number formats
  const phonePattern = /^[\+]?[1-9][\d\s\-\(\)]{7,15}$/;

  const cleanPhone = validateTextInput(phone, {
    minLength: 7,
    maxLength: 20,
    fieldName,
    allowEmpty: false,
    sanitize: false // Don't sanitize phone numbers as they have special characters
  });

  if (!phonePattern.test(cleanPhone.replace(/[\s\-\(\)]/g, ''))) {
    throw new Error(`${fieldName} format is invalid`);
  }

  return cleanPhone;
}

/**
 * Validate date input
 */
export function validateDate(date: any, fieldName: string = 'date'): string {
  if (!date) {
    throw new Error(`${fieldName} is required`);
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    throw new Error(`${fieldName} must be a valid date`);
  }

  // Check for reasonable date range (not too far in past or future)
  const minDate = new Date('1900-01-01');
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 100);

  if (dateObj < minDate || dateObj > maxDate) {
    throw new Error(`${fieldName} must be between 1900 and ${maxDate.getFullYear()}`);
  }

  return dateObj.toISOString().split('T')[0]; // Return YYYY-MM-DD format
}

/**
 * Validate JSON input
 */
export function validateJSONInput(value: any, fieldName: string = 'data'): any {
  if (value === null || value === undefined) {
    return null;
  }

  try {
    // If it's already an object, validate it
    if (typeof value === 'object') {
      // Check for dangerous properties
      const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
      const checkObject = (obj: any): void => {
        if (obj && typeof obj === 'object') {
          for (const key in obj) {
            if (dangerousKeys.includes(key)) {
              throw new Error(`Dangerous property '${key}' not allowed`);
            }
            if (typeof obj[key] === 'object') {
              checkObject(obj[key]);
            }
          }
        }
      };

      checkObject(value);
      return value;
    }

    // If it's a string, try to parse it
    if (typeof value === 'string') {
      const parsed = JSON.parse(value);
      return validateJSONInput(parsed, fieldName); // Recursive validation
    }

    throw new Error(`${fieldName} must be valid JSON`);
  } catch (error) {
    throw new Error(`${fieldName} contains invalid JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
