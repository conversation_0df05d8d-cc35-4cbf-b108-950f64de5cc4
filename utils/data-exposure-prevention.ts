/**
 * Data Exposure Prevention Utilities
 * 
 * Provides utilities to prevent excessive data exposure by filtering
 * database queries and API responses to only include necessary fields.
 */

import { 
  HouseholdSummaryDTO, 
  HouseholdDetailDTO,
  AssetSummaryDTO,
  AssetDetailDTO,
  LiabilitySummaryDTO,
  LiabilityDetailDTO,
  IncomeSummaryDTO,
  IncomeDetailDTO,
  ExpenseSummaryDTO,
  ExpenseDetailDTO,
  ProfileSummaryDTO,
  ScenarioSummaryDTO,
  ScenarioDetailDTO,
  toHouseholdSummaryDTO,
  toHouseholdDetailDTO
} from '@/types/dto';

/**
 * Define sensitive fields that should never be exposed
 */
export const SENSITIVE_FIELDS = {
  profiles: [
    'password_hash', 'mfa_secret', 'recovery_codes', 'session_token',
    'api_keys', 'internal_notes', 'admin_flags', 'debug_data'
  ],
  households: [
    'internal_notes', 'admin_flags', 'tax_file_number1', 'tax_file_number2',
    'social_security_number1', 'social_security_number2', 'bank_account_numbers',
    'credit_card_numbers', 'sensitive_financial_data', 'admin_metadata'
  ],
  assets: [
    'account_numbers', 'login_credentials', 'api_keys', 'sensitive_details',
    'admin_notes', 'internal_calculations'
  ],
  liabilities: [
    'account_numbers', 'loan_numbers', 'bank_details', 'sensitive_terms',
    'admin_notes', 'internal_calculations'
  ],
  income: [
    'tax_details', 'bank_account_info', 'employer_tax_id', 'sensitive_data'
  ],
  expenses: [
    'account_details', 'payment_methods', 'sensitive_data'
  ],
  scenarios_data1: [
    'raw_calculations', 'internal_metadata', 'debug_data', 'admin_notes'
  ]
};

/**
 * Define safe column selections for each table
 */
export const SAFE_COLUMNS = {
  households: {
    summary: [
      'id', 'householdName', 'city', 'state', 'primary_advisor',
      'last_review', 'next_review', 'created_at', 'updated_at'
    ],
    detail: [
      'id', 'householdName', 'members', 'address', 'street', 'city', 'state',
      'zip_code', 'country', 'marital_status', 'preferred_contact',
      'best_time_to_call', 'alternative_contact', 'primary_advisor',
      'last_review', 'next_review', 'created_at', 'updated_at'
    ]
  },
  assets: {
    summary: ['id', 'name', 'value', 'asset_type', 'household_id', 'created_at'],
    detail: [
      'id', 'name', 'value', 'asset_type', 'description', 'purchase_date',
      'current_value', 'household_id', 'created_at', 'updated_at'
    ]
  },
  liabilities: {
    summary: ['id', 'name', 'value', 'liability_type', 'household_id', 'created_at'],
    detail: [
      'id', 'name', 'value', 'liability_type', 'description', 'interest_rate',
      'monthly_payment', 'maturity_date', 'household_id', 'created_at', 'updated_at'
    ]
  },
  income: {
    summary: ['id', 'amount', 'frequency', 'source', 'household_id', 'created_at'],
    detail: [
      'id', 'amount', 'frequency', 'source', 'description', 'start_date',
      'end_date', 'is_guaranteed', 'household_id', 'created_at', 'updated_at'
    ]
  },
  expenses: {
    summary: ['id', 'amount', 'frequency', 'category', 'household_id', 'created_at'],
    detail: [
      'id', 'amount', 'frequency', 'category', 'description', 'is_essential',
      'notes', 'household_id', 'created_at', 'updated_at'
    ]
  },
  scenarios_data1: {
    summary: [
      'id', 'scenario_name', 'household_id', 'household_name', 'created_at',
      'last_viewed_at', 'last_edited_at'
    ],
    detail: [
      'id', 'scenario_name', 'household_id', 'household_name', 'scenario_data',
      'created_at', 'last_viewed_at', 'last_edited_at', 'updated_at'
    ]
  },
  profiles: {
    summary: ['user_id', 'name', 'org_id', 'org_role', 'created_at'],
    detail: [
      'user_id', 'name', 'email', 'phone', 'org_id', 'org_role',
      'last_login', 'mfa_enabled', 'created_at', 'updated_at'
    ]
  },
  tasks: {
    summary: [
      'id', 'title', 'status', 'priority', 'due_date', 'household_id',
      'created_at'
    ],
    detail: [
      'id', 'title', 'description', 'status', 'priority', 'due_date',
      'assigned_to', 'household_id', 'created_at', 'updated_at'
    ]
  },
  custom_reports: {
    summary: ['id', 'name', 'report_type', 'created_by', 'created_at'],
    detail: [
      'id', 'name', 'description', 'report_type', 'parameters',
      'created_by', 'last_run', 'created_at', 'updated_at'
    ]
  },
  report_schedules: {
    summary: [
      'id', 'report_id', 'name', 'frequency', 'next_run', 'is_active', 'created_at'
    ],
    detail: [
      'id', 'report_id', 'name', 'description', 'frequency', 'parameters',
      'next_run', 'last_run', 'is_active', 'created_at', 'updated_at'
    ]
  },
  report_executions: {
    summary: [
      'id', 'schedule_id', 'status', 'started_at', 'completed_at'
    ],
    detail: [
      'id', 'schedule_id', 'status', 'started_at', 'completed_at',
      'duration', 'record_count', 'error_message'
    ]
  }
};

/**
 * Get safe column selection for a table and detail level
 */
export function getSafeColumns(tableName: string, level: 'summary' | 'detail' = 'summary'): string {
  const tableColumns = SAFE_COLUMNS[tableName as keyof typeof SAFE_COLUMNS];
  if (!tableColumns) {
    throw new Error(`No safe columns defined for table: ${tableName}`);
  }
  
  const columns = tableColumns[level];
  if (!columns) {
    throw new Error(`No ${level} columns defined for table: ${tableName}`);
  }
  
  return columns.join(', ');
}

/**
 * Filter database record to remove sensitive fields
 */
export function filterSensitiveFields<T extends Record<string, any>>(
  record: T,
  tableName: string
): Partial<T> {
  const sensitiveFields = SENSITIVE_FIELDS[tableName as keyof typeof SENSITIVE_FIELDS] || [];
  const filtered = { ...record };
  
  sensitiveFields.forEach(field => {
    delete filtered[field];
  });
  
  return filtered;
}

/**
 * Filter array of records to remove sensitive fields
 */
export function filterSensitiveFieldsArray<T extends Record<string, any>>(
  records: T[],
  tableName: string
): Partial<T>[] {
  return records.map(record => filterSensitiveFields(record, tableName));
}

/**
 * Convert database records to appropriate DTOs
 */
export function convertToDTO<T>(
  records: any[],
  tableName: string,
  level: 'summary' | 'detail' = 'summary'
): T[] {
  switch (tableName) {
    case 'households':
      return records.map(record => 
        level === 'summary' 
          ? toHouseholdSummaryDTO(record)
          : toHouseholdDetailDTO(record)
      ) as T[];
    
    case 'assets':
      return records.map(record => ({
        id: record.id,
        name: record.name,
        value: record.value,
        asset_type: record.asset_type,
        household_id: record.household_id,
        created_at: record.created_at,
        ...(level === 'detail' && {
          description: record.description,
          purchase_date: record.purchase_date,
          current_value: record.current_value,
          updated_at: record.updated_at
        })
      })) as T[];
    
    case 'scenarios_data1':
      return records.map(record => ({
        id: record.id,
        scenario_name: record.scenario_name,
        household_id: record.household_id,
        household_name: record.household_name,
        created_at: record.created_at,
        last_viewed_at: record.last_viewed_at,
        last_edited_at: record.last_edited_at,
        ...(level === 'detail' && {
          scenario_data: record.scenario_data,
          updated_at: record.updated_at
        })
      })) as T[];
    
    default:
      // For other tables, just filter sensitive fields
      return filterSensitiveFieldsArray(records, tableName) as T[];
  }
}

/**
 * Validate that a query doesn't expose sensitive data
 */
export function validateQuerySafety(query: string, tableName: string): {
  isValid: boolean;
  errors: string[];
  suggestions: string[];
} {
  const errors: string[] = [];
  const suggestions: string[] = [];
  
  // Check for SELECT *
  if (query.includes('SELECT *') || query.includes('select *')) {
    errors.push('Query uses SELECT * which may expose sensitive data');
    suggestions.push(`Use specific columns: SELECT ${getSafeColumns(tableName, 'summary')}`);
  }
  
  // Check for sensitive fields
  const sensitiveFields = SENSITIVE_FIELDS[tableName as keyof typeof SENSITIVE_FIELDS] || [];
  sensitiveFields.forEach(field => {
    if (query.toLowerCase().includes(field.toLowerCase())) {
      errors.push(`Query includes sensitive field: ${field}`);
      suggestions.push(`Remove sensitive field: ${field}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    suggestions
  };
}
