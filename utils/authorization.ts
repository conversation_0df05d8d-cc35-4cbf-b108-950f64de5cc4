/**
 * Authorization Utilities for IDOR Protection
 *
 * Provides comprehensive authorization checks to prevent Insecure Direct Object References (IDOR)
 * by verifying user ownership and organization access before allowing data operations.
 */

import { createClient } from '@/utils/supabase/server';
import { createClient as createClientClient } from '@/utils/supabase/client';
import { redirect } from 'next/navigation';

export interface AuthorizedUser {
  id: string;
  email?: string;
  profile?: {
    org_id?: string;
    org_role?: string;
    name?: string;
  };
}

export interface AuthorizedResource {
  user: AuthorizedUser;
  resource: any;
  hasAccess: boolean;
}

/**
 * Verify household access for server components
 * Ensures user owns the household or belongs to the same organization
 */
export async function verifyHouseholdAccess(householdId: string): Promise<AuthorizedResource> {
  const supabase = createClient();

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/sign-in');
  }

  // Get user's profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id, org_role, name')
    .eq('user_id', user.id)
    .single();

  if (profileError) {
    redirect('/sign-in');
  }

  // Get household data
  const { data: household, error: householdError } = await supabase
    .from('households')
    .select('id, user_id, org_id, householdName')
    .eq('id', householdId)
    .single();

  if (householdError || !household) {
    redirect('/protected/households');
  }

  // Check access permissions
  const hasAccess = household.user_id === user.id ||
                   (household.org_id && household.org_id === profile?.org_id);

  if (!hasAccess) {
    redirect('/protected/households');
  }

  return {
    user: { ...user, profile },
    resource: household,
    hasAccess: true
  };
}

/**
 * Verify household access for client components
 * Returns authorization result without redirecting
 */
export async function verifyHouseholdAccessClient(householdId: string): Promise<AuthorizedResource | null> {
  const supabase = createClientClient();

  try {
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    // Get user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      return null;
    }

    // Get household data
    const { data: household, error: householdError } = await supabase
      .from('households')
      .select('id, user_id, org_id, householdName')
      .eq('id', householdId)
      .single();

    if (householdError || !household) {
      return null;
    }

    // Check access permissions
    const hasAccess = household.user_id === user.id ||
                     (household.org_id && household.org_id === profile?.org_id);

    return {
      user: { ...user, profile },
      resource: household,
      hasAccess
    };
  } catch (error) {
    return null;
  }
}

/**
 * Verify scenario access
 * Ensures user can access scenarios through household ownership
 */
export async function verifyScenarioAccess(scenarioId: string): Promise<AuthorizedResource> {
  const supabase = createClient();

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/sign-in');
  }

  // Get scenario with household information
  const { data: scenario, error: scenarioError } = await supabase
    .from('scenarios_data1')
    .select(`
      id,
      scenario_name,
      household_id,
      user_id,
      org_id,
      households (
        id,
        user_id,
        org_id,
        householdName
      )
    `)
    .eq('id', scenarioId)
    .single();

  if (scenarioError || !scenario) {
    redirect('/protected/scenarios');
  }

  // Get user's profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id, org_role, name')
    .eq('user_id', user.id)
    .single();

  if (profileError) {
    redirect('/sign-in');
  }

  // Check access permissions - user owns scenario or household, or same organization
  const hasAccess = scenario.user_id === user.id ||
                   (scenario.households && scenario.households.user_id === user.id) ||
                   (scenario.org_id && scenario.org_id === profile?.org_id) ||
                   (scenario.households && scenario.households.org_id && scenario.households.org_id === profile?.org_id);

  if (!hasAccess) {
    redirect('/protected/scenarios');
  }

  return {
    user: { ...user, profile },
    resource: scenario,
    hasAccess: true
  };
}

/**
 * Verify note access
 * Ensures user owns the note or has organization access
 */
export async function verifyNoteAccess(noteId: string): Promise<AuthorizedResource> {
  const supabase = createClient();

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/sign-in');
  }

  // Get note data
  const { data: note, error: noteError } = await supabase
    .from('notes')
    .select('id, title, user_id, org_id, household_id')
    .eq('id', noteId)
    .single();

  if (noteError || !note) {
    redirect('/protected/notes');
  }

  // Get user's profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id, org_role, name')
    .eq('user_id', user.id)
    .single();

  if (profileError) {
    redirect('/sign-in');
  }

  // Check access permissions
  const hasAccess = note.user_id === user.id ||
                   (note.org_id && note.org_id === profile?.org_id);

  if (!hasAccess) {
    redirect('/protected/notes');
  }

  return {
    user: { ...user, profile },
    resource: note,
    hasAccess: true
  };
}

/**
 * Verify document access
 * Ensures user can access documents through household ownership
 */
export async function verifyDocumentAccess(documentPath: string, householdId?: string): Promise<AuthorizedResource> {
  const supabase = createClient();

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/sign-in');
  }

  // If household ID is provided, verify household access
  if (householdId) {
    const householdAccess = await verifyHouseholdAccess(householdId);
    return {
      user: householdAccess.user,
      resource: { path: documentPath, household: householdAccess.resource },
      hasAccess: true
    };
  }

  // For documents without household context, check if user owns the document
  // This would require additional document metadata tracking
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id, org_role, name')
    .eq('user_id', user.id)
    .single();

  if (profileError) {
    redirect('/sign-in');
  }

  return {
    user: { ...user, profile },
    resource: { path: documentPath },
    hasAccess: true // For now, allow access if authenticated
  };
}

/**
 * Input validation for IDs to prevent injection attacks
 */
export function validateId(id: string, type: 'household' | 'scenario' | 'note' | 'user' = 'household'): string {
  if (!id || typeof id !== 'string') {
    throw new Error(`Invalid ${type} ID`);
  }

  // Remove any whitespace
  id = id.trim();

  // Check for basic format (numeric or UUID)
  const isNumeric = /^\d+$/.test(id);
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);

  if (!isNumeric && !isUUID) {
    throw new Error(`Invalid ${type} ID format`);
  }

  return id;
}
