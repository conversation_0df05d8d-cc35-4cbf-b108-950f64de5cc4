/**
 * Generates a unique device identifier based on browser and device information
 * This is used to identify devices for the device management feature
 */
export function generateDeviceId(): string {
  if (typeof window === 'undefined') {
    return '';
  }

  // First, check if we already have a device ID in localStorage
  const storedDeviceId = localStorage.getItem('wealthie_device_id');
  if (storedDeviceId) {
    return storedDeviceId;
  }

  // Get browser information
  const { userAgent, platform } = window.navigator;

  // Create a fingerprint from available browser data
  const components = [
    userAgent,
    platform,
    window.screen.colorDepth,
    window.screen.width + 'x' + window.screen.height,
    new Date().getTimezoneOffset(),
    !!window.sessionStorage,
    !!window.localStorage,
    !!window.indexedDB,
    // Add more entropy
    Math.random().toString(36).substring(2),
    new Date().toISOString()
  ];

  // Create a hash from the components
  let hash = 0;
  const stringToHash = components.join('###');

  for (let i = 0; i < stringToHash.length; i++) {
    const char = stringToHash.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }

  // Convert to a hex string and add a timestamp for uniqueness
  const deviceId = Math.abs(hash).toString(16) + '-' + Date.now().toString(36);

  // Store the device ID in localStorage for persistence
  try {
    localStorage.setItem('wealthie_device_id', deviceId);
  } catch (e) {
    console.error('Failed to store device ID in localStorage:', e);
  }

  return deviceId;
}

/**
 * Gets a user-friendly name for the current device
 */
export function getDeviceName(): string {
  if (typeof window === 'undefined') {
    return 'Unknown Device';
  }

  const { userAgent } = window.navigator;
  let deviceName = 'Unknown Device';

  // Detect device type
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);

  // Detect OS
  const isWindows = /Windows/.test(userAgent);
  const isMac = /Macintosh|MacIntel|MacPPC|Mac68K/.test(userAgent);
  const isLinux = /Linux/.test(userAgent);
  const isIOS = /iPhone|iPad|iPod/.test(userAgent);
  const isAndroid = /Android/.test(userAgent);

  // Detect browser
  const isChrome = /Chrome/.test(userAgent) && !/Edge|Edg/.test(userAgent);
  const isFirefox = /Firefox/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  const isEdge = /Edge|Edg/.test(userAgent);

  // Build device name
  let deviceType = isTablet ? 'Tablet' : (isMobile ? 'Mobile' : 'Desktop');

  let os = 'Unknown OS';
  if (isWindows) os = 'Windows';
  else if (isMac) os = 'Mac';
  else if (isIOS) os = 'iOS';
  else if (isAndroid) os = 'Android';
  else if (isLinux) os = 'Linux';

  let browser = 'Unknown Browser';
  if (isChrome) browser = 'Chrome';
  else if (isFirefox) browser = 'Firefox';
  else if (isSafari) browser = 'Safari';
  else if (isEdge) browser = 'Edge';

  deviceName = `${deviceType} - ${os} (${browser})`;

  return deviceName;
}

/**
 * Gets the current user agent string
 */
export function getUserAgent(): string {
  if (typeof window === 'undefined') {
    return '';
  }

  return window.navigator.userAgent;
}
