import { createClient } from '@/utils/supabase/server';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { spawn } from 'child_process';
import { v4 as uuidv4 } from 'uuid';

/**
 * Downloads a file from Supabase storage
 */
export async function downloadFileFromStorage(supabase: any, bucket: string, filePath: string): Promise<string> {
  console.log(`Downloading file from ${bucket}/${filePath}`);
  
  const { data: file, error: downloadError } = await supabase.storage
    .from(bucket)
    .download(filePath);

  if (downloadError || !file) {
    throw new Error(`Failed to download file: ${downloadError?.message || 'No file data returned'}`);
  }

  const tempDir = os.tmpdir();
  const fileName = path.basename(filePath);
  const localFilePath = path.join(tempDir, fileName);

  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);
  // Convert Buffer to Uint8Array which is compatible with fs.promises.writeFile
  await fs.promises.writeFile(localFilePath, new Uint8Array(buffer)); 
  
  console.log(`File downloaded to ${localFilePath}`);
  return localFilePath;
}

/**
 * Extracts audio from a video file
 * @param videoPath Path to the video file
 * @returns Path to the extracted audio file
 */
export async function extractAudioFromVideo(videoPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const tempDir = os.tmpdir();
    const outputFileName = `audio-${uuidv4()}.mp3`;
    const outputPath = path.join(tempDir, outputFileName);
    
    console.log(`Extracting audio from ${videoPath} to ${outputPath}`);
    
    const ffmpeg = spawn('ffmpeg', [
      '-i', videoPath,
      '-vn',
      '-acodec', 'libmp3lame',
      '-ac', '1',
      '-ar', '16000',
      '-b:a', '64k',
      outputPath
    ]);
    
    let ffmpegLogs = '';
    
    ffmpeg.stdout.on('data', (data) => {
      ffmpegLogs += data.toString();
    });
    
    ffmpeg.stderr.on('data', (data) => {
      ffmpegLogs += data.toString();
    });
    
    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log('Audio extraction completed successfully');
        resolve(outputPath);
      } else {
        console.error(`FFmpeg process exited with code ${code}`);
        console.error('FFmpeg logs:', ffmpegLogs);
        reject(new Error(`FFmpeg process exited with code ${code}`));
      }
    });
    
    ffmpeg.on('error', (err) => {
      console.error('FFmpeg process error:', err);
      reject(err);
    });
  });
}

/**
 * Uploads a file to Supabase storage
 */
export async function uploadFileToStorage(
  supabase: any,
  bucket: string,
  filePath: string,
  fileBuffer: Buffer
): Promise<string> {
  console.log(`Uploading file to ${bucket}/${filePath}`);
  
  const { error: uploadError } = await supabase.storage
    .from(bucket)
    .upload(filePath, fileBuffer, {
      contentType: 'audio/mpeg',
      cacheControl: '3600',
      upsert: false
    });

  if (uploadError) {
    throw new Error(`Upload error: ${uploadError.message}`);
  }
  
  console.log(`File uploaded to ${bucket}/${filePath}`);
  return filePath;
}

/**
 * Checks if a file is a video
 */
export function isVideoFile(fileType: string): boolean {
  return fileType.startsWith('video/');
}

/**
 * Creates a URL-safe filename
 */
export function createSafeFilename(originalFilename: string): string {
  return originalFilename.replace(/[^a-zA-Z0-9.-]/g, '_');
}

/**
 * Main function for processing video-to-audio conversion
 */
export async function processVideoToAudio(supabaseClient: any, bucketName: string, videoFilePath: string): Promise<void> {
  try {
    // Step 1: Download the video file from Supabase storage
    const localVideoPath = await downloadFileFromStorage(supabaseClient, bucketName, videoFilePath);

    // Step 2: Extract audio from the downloaded video file
    const audioFilePath = await extractAudioFromVideo(localVideoPath);

    // Step 3: Read the extracted audio file into a buffer
    const audioBuffer = await fs.promises.readFile(audioFilePath);

    // Step 4: Generate a safe filename for the audio file upload
    const originalFilename = path.basename(videoFilePath);
    const safeAudioFilename = createSafeFilename(`audio_${originalFilename}.mp3`);

    // Step 5: Upload the extracted audio back to Supabase storage
    await uploadFileToStorage(supabaseClient, bucketName, safeAudioFilename, audioBuffer);

    console.log(`Audio processing complete. Audio uploaded as ${safeAudioFilename}`);
    
  } catch (error) {
    console.error('Error during video-to-audio processing:', error);
    throw error;
  }
}
