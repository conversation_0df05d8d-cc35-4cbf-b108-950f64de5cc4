/**
 * Enhanced MFA Security Utilities
 *
 * This module provides comprehensive MFA security enforcement
 * that works with Supabase's native MFA system while maintaining
 * the existing elegant UI/UX flow.
 */

import { createClient } from '@/utils/supabase/client';

export interface MFAStatus {
  isRequired: boolean;
  isEnrolled: boolean;
  isVerified: boolean;
  currentLevel: 'aal1' | 'aal2' | null;
  nextLevel: 'aal1' | 'aal2' | null;
  factors: any[];
  needsChallenge: boolean;
}

/**
 * Get comprehensive MFA status for a user (client-side)
 */
export async function getMFAStatus(): Promise<MFAStatus> {
  const supabase = createClient();

  try {
    // Get AAL (Authenticator Assurance Level) data
    const { data: aalData, error: aalError } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

    if (aalError) {
      throw aalError;
    }

    // Get MFA factors
    const { data: factorsData, error: factorsError } = await supabase.auth.mfa.listFactors();

    if (factorsError) {
      throw factorsError;
    }

    // Combine all factor types
    const allFactors = factorsData ? [...(factorsData.totp || []), ...(factorsData.phone || [])] : [];
    const verifiedFactors = allFactors.filter(factor => factor.status === 'verified');

    const isEnrolled = verifiedFactors.length > 0;
    const isVerified = aalData?.currentLevel === 'aal2';
    const needsChallenge = isEnrolled && aalData?.nextLevel === 'aal2' && aalData?.currentLevel === 'aal1';

    return {
      isRequired: isEnrolled, // If user has enrolled MFA, it's required
      isEnrolled,
      isVerified,
      currentLevel: aalData?.currentLevel || null,
      nextLevel: aalData?.nextLevel || null,
      factors: verifiedFactors,
      needsChallenge
    };
  } catch (error) {
    return {
      isRequired: false,
      isEnrolled: false,
      isVerified: false,
      currentLevel: null,
      nextLevel: null,
      factors: [],
      needsChallenge: false
    };
  }
}

// Server-side MFA functions moved to utils/server-mfa-security.ts

/**
 * Require MFA verification for sensitive operations (client-side)
 * Throws an error if MFA is required but not verified
 */
export async function requireMFA(): Promise<void> {
  const mfaStatus = await getMFAStatus();

  if (mfaStatus.isRequired && !mfaStatus.isVerified) {
    throw new Error('MFA verification required for this operation');
  }
}

// Server-side MFA functions moved to utils/server-mfa-security.ts

/**
 * Check if a route should require MFA verification
 * This defines which routes need enhanced security
 */
export function isSensitiveRoute(pathname: string): boolean {
  const sensitiveRoutes = [
    '/protected/households/household', // Individual household access
    '/protected/settings', // Account settings
    '/protected/profile', // Profile management
    // Add more sensitive routes as needed
  ];

  return sensitiveRoutes.some(route => pathname.startsWith(route));
}

// Server-side MFA enforcement moved to utils/server-mfa-security.ts

/**
 * Get user-friendly MFA status message
 */
export function getMFAStatusMessage(status: MFAStatus): string {
  if (!status.isEnrolled) {
    return 'Two-factor authentication is not set up';
  }

  if (status.isVerified) {
    return 'Two-factor authentication is active and verified';
  }

  if (status.needsChallenge) {
    return 'Two-factor authentication verification required';
  }

  return 'Two-factor authentication is set up but not verified';
}

/**
 * Log MFA security events for monitoring
 */
export function logMFAEvent(event: string, details?: any): void {
  const timestamp = new Date().toISOString();
  console.log(`[MFA Security] ${timestamp}: ${event}`, details || '');

  // In production, you might want to send this to a security monitoring service
  // Example: sendToSecurityMonitoring({ event, details, timestamp });
}
