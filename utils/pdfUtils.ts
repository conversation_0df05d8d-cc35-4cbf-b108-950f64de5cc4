// PDF Utilities for pagination and A4 formatting

// A4 dimensions in pixels (at 96 DPI)
// A4 is 210mm × 297mm
// 1 inch = 25.4mm
// 1 inch = 96px (standard screen DPI)
// Therefore: A4 width = (210 / 25.4) * 96 = 794px
// A4 height = (297 / 25.4) * 96 = 1123px
export const A4_WIDTH_PX = 794;
export const A4_HEIGHT_PX = 1123;
// Reduced margins for A4 at 96 DPI to allow more content
export const A4_MARGIN_TOP_PX = 60; // ~16mm top margin
export const A4_MARGIN_SIDE_PX = 60; // ~16mm side margins
export const A4_MARGIN_BOTTOM_PX = 80; // ~21mm bottom margin for better spacing

// Content area dimensions
export const CONTENT_WIDTH_PX = A4_WIDTH_PX - (A4_MARGIN_SIDE_PX * 2);
export const CONTENT_HEIGHT_PX = A4_HEIGHT_PX - A4_MARGIN_TOP_PX - A4_MARGIN_BOTTOM_PX;

// Minimum space threshold to consider for splitting sections
export const MIN_SPACE_THRESHOLD = 100; // Minimum space in pixels to attempt fitting content

// Helper function to measure content height more accurately
export function measureContentHeight(element: HTMLElement): number {
  // Create a clone to measure without affecting the original
  const clone = element.cloneNode(true) as HTMLElement;
  clone.style.position = 'absolute';
  clone.style.visibility = 'hidden';
  clone.style.width = `${CONTENT_WIDTH_PX}px`;
  document.body.appendChild(clone);

  const height = clone.offsetHeight;
  document.body.removeChild(clone);

  return height;
}

// Helper function to create a new A4 page
export function createA4Page(): HTMLDivElement {
  const page = document.createElement('div');
  page.className = 'a4-page bg-white shadow-md mb-8 mx-auto';
  page.style.width = `${A4_WIDTH_PX}px`;
  page.style.height = `${A4_HEIGHT_PX}px`;
  page.style.padding = `${A4_MARGIN_TOP_PX}px ${A4_MARGIN_SIDE_PX}px ${A4_MARGIN_BOTTOM_PX}px`;
  page.style.position = 'relative';
  page.style.breakAfter = 'page';
  page.style.overflow = 'hidden';
  page.style.boxSizing = 'border-box';

  // Add CSS for subsection indentation and reduced vertical spacing
  const style = document.createElement('style');
  style.textContent = `
    .a4-page .pdf-subsection {
      margin-bottom: 4px;
      padding-bottom: 1px;
      padding-left: 10px;
    }
    .a4-page .pdf-subsection h2 {
      font-size: 15px;
      font-weight: 600;
      margin-top: 6px;
      margin-bottom: 4px;
      margin-left: -10px;
      color: #333;
    }
    .a4-page .pdf-subsection h3 {
      font-size: 13px;
      font-weight: 500;
      margin-top: 3px;
      margin-bottom: 1px;
      color: #444;
    }
    .a4-page p, .a4-page span, .a4-page div:not(.pdf-subsection):not(.a4-page):not(.pdf-page-number) {
      font-size: 12px;
      line-height: 1.2;
      margin-top: 2px;
      margin-bottom: 2px;
    }
    .a4-page table {
      font-size: 11px;
      width: 100%;
      table-layout: fixed;
      margin-bottom: 4px;
      border-spacing: 0;
      border-collapse: collapse;
    }
    .a4-page table td, .a4-page table th {
      padding: 2px 4px;
      vertical-align: top;
    }
  `;
  page.appendChild(style);

  return page;
}

// Helper function to check if content fits on current page
export function contentFitsOnPage(contentHeight: number, currentPageContentHeight: number): boolean {
  return (currentPageContentHeight + contentHeight) <= CONTENT_HEIGHT_PX;
}

// Helper function to check if a section can be split
export function canSplitSection(section: HTMLElement): boolean {
  // Check if the section has children that can be split across pages
  return section.children.length > 1 || !!section.querySelector('div, p, h1, h2, h3, h4, h5, h6, table');
}

// Helper function to split a section into parts that can fit on pages
export function splitSectionContent(section: HTMLElement, remainingHeight: number): [HTMLElement, HTMLElement] {
  // Create containers for the parts
  const firstPart = document.createElement('div');
  firstPart.className = section.className;
  firstPart.style.cssText = section.style.cssText;

  const secondPart = document.createElement('div');
  secondPart.className = section.className;
  secondPart.style.cssText = section.style.cssText;

  // Get the section title if it exists (usually the first child or element with heading tag)
  const title = section.querySelector('h1, h2, h3, h4, h5, h6');
  let titleElement = null;

  if (title) {
    // Clone the title for the second part
    titleElement = title.cloneNode(true) as HTMLElement;
  }

  // Get all children of the section
  const children = Array.from(section.children) as HTMLElement[];

  // Track height as we add elements
  let currentHeight = 0;
  let splitFound = false;

  // Process each child element
  for (let i = 0; i < children.length; i++) {
    const child = children[i];

    // Skip empty elements
    if (child.children.length === 0 && !child.textContent?.trim()) {
      continue;
    }

    const childHeight = measureContentHeight(child);

    // If this child would exceed the remaining height and we already have content in firstPart
    if (currentHeight + childHeight > remainingHeight && currentHeight > 0) {
      // Add remaining children to the second part
      if (titleElement) {
        secondPart.appendChild(titleElement);
      }

      for (let j = i; j < children.length; j++) {
        // Skip empty elements
        if (children[j].children.length === 0 && !children[j].textContent?.trim()) {
          continue;
        }
        secondPart.appendChild(children[j].cloneNode(true));
      }
      splitFound = true;
      break;
    }

    // Otherwise add to the first part
    firstPart.appendChild(child.cloneNode(true));
    currentHeight += childHeight;
  }

  // If we couldn't find a good split point but the section is too large
  if (!splitFound && currentHeight > remainingHeight) {
    // Try to split complex elements (like tables, lists, or divs with many children)
    if (section.querySelector('table, ul, ol, div > div')) {
      // Clear the parts to start fresh
      firstPart.innerHTML = '';
      secondPart.innerHTML = '';

      // Add the title to both parts
      if (title) {
        firstPart.appendChild(title.cloneNode(true));
        if (titleElement) {
          secondPart.appendChild(titleElement);
        }
      }

      // Find complex elements that can be split
      const complexElements = section.querySelectorAll('table, ul, ol, div > div');

      if (complexElements.length > 0) {
        // Try to find a good split point within complex elements
        for (let i = 0; i < complexElements.length; i++) {
          const element = complexElements[i] as HTMLElement;

          // If this is a table, try to split rows
          if (element.tagName === 'TABLE') {
            const rows = element.querySelectorAll('tr');
            if (rows.length > 2) { // Need at least header + 2 rows to split
              const newTable1 = element.cloneNode(true) as HTMLElement;
              const newTable2 = element.cloneNode(true) as HTMLElement;

              // Clear the tables except for the structure
              const tbody1 = newTable1.querySelector('tbody');
              const tbody2 = newTable2.querySelector('tbody');

              if (tbody1) tbody1.innerHTML = '';
              if (tbody2) tbody2.innerHTML = '';

              // Keep the header in both tables
              const headerRow = element.querySelector('thead tr');
              if (headerRow) {
                const thead1 = newTable1.querySelector('thead');
                const thead2 = newTable2.querySelector('thead');
                if (thead1) thead1.innerHTML = headerRow.outerHTML;
                if (thead2) thead2.innerHTML = headerRow.outerHTML;
              }

              // Split the rows between the tables
              const splitPoint = Math.ceil(rows.length / 2);

              for (let j = 0; j < rows.length; j++) {
                if (j < splitPoint) {
                  if (tbody1) tbody1.appendChild(rows[j].cloneNode(true));
                } else {
                  if (tbody2) tbody2.appendChild(rows[j].cloneNode(true));
                }
              }

              // Add the tables to the parts
              firstPart.appendChild(newTable1);
              secondPart.appendChild(newTable2);
              splitFound = true;
              break;
            }
          }
          // If this is a list, split the items
          else if (element.tagName === 'UL' || element.tagName === 'OL') {
            const items = element.querySelectorAll('li');
            if (items.length > 1) {
              const newList1 = element.cloneNode(false) as HTMLElement;
              const newList2 = element.cloneNode(false) as HTMLElement;

              // Split the items between the lists
              const splitPoint = Math.ceil(items.length / 2);

              for (let j = 0; j < items.length; j++) {
                if (j < splitPoint) {
                  newList1.appendChild(items[j].cloneNode(true));
                } else {
                  newList2.appendChild(items[j].cloneNode(true));
                }
              }

              // Add the lists to the parts
              firstPart.appendChild(newList1);
              secondPart.appendChild(newList2);
              splitFound = true;
              break;
            }
          }
          // For other complex elements, try to split their children
          else if (element.children.length > 1) {
            const childElements = Array.from(element.children) as HTMLElement[];
            const newElement1 = element.cloneNode(false) as HTMLElement;
            const newElement2 = element.cloneNode(false) as HTMLElement;

            // Split the children between the elements
            const splitPoint = Math.ceil(childElements.length / 2);

            for (let j = 0; j < childElements.length; j++) {
              if (j < splitPoint) {
                newElement1.appendChild(childElements[j].cloneNode(true));
              } else {
                newElement2.appendChild(childElements[j].cloneNode(true));
              }
            }

            // Add the elements to the parts
            firstPart.appendChild(newElement1);
            secondPart.appendChild(newElement2);
            splitFound = true;
            break;
          }
        }
      }
    }

    // If we still couldn't split complex elements, try text elements
    if (!splitFound) {
      // Clear the first part and try a different approach
      firstPart.innerHTML = '';
      secondPart.innerHTML = '';

      // Add the title to the first part if it exists
      if (title && title.parentNode === section) {
        firstPart.appendChild(title.cloneNode(true));
        currentHeight = measureContentHeight(title as HTMLElement);
      } else {
        currentHeight = 0;
      }

      // Try to split by paragraphs or other text elements
      const textElements = section.querySelectorAll('p, div:not(:has(>*)), span:not(:has(>*))');

      if (textElements.length > 1) {
        splitFound = false;

        for (let i = 0; i < textElements.length; i++) {
          const element = textElements[i] as HTMLElement;

          // Skip empty elements
          if (!element.textContent?.trim()) {
            continue;
          }

          const elementHeight = measureContentHeight(element);

          if (currentHeight + elementHeight > remainingHeight && currentHeight > 0) {
            // We found our split point
            splitFound = true;

            // Add the title to the second part if it exists
            if (titleElement) {
              secondPart.appendChild(titleElement);
            }

            // Add this and remaining text elements to the second part
            for (let j = i; j < textElements.length; j++) {
              // Skip empty elements
              if (!textElements[j].textContent?.trim()) {
                continue;
              }
              secondPart.appendChild(textElements[j].cloneNode(true));
            }

            break;
          }

          firstPart.appendChild(element.cloneNode(true));
          currentHeight += elementHeight;
        }
      }
    }

    // If we still couldn't find a good split, try to split text content
    if (!splitFound && section.textContent) {
      // Create a simple container with the text content
      const textContainer = document.createElement('div');
      textContainer.textContent = section.textContent;

      // Split the text roughly in half
      const halfLength = Math.ceil(section.textContent.length / 2);
      const firstHalf = section.textContent.substring(0, halfLength);
      const secondHalf = section.textContent.substring(halfLength);

      // Create elements for each half
      const firstHalfElement = document.createElement('div');
      firstHalfElement.textContent = firstHalf;

      const secondHalfElement = document.createElement('div');
      secondHalfElement.textContent = secondHalf;

      // Add to the parts
      if (title) {
        firstPart.appendChild(title.cloneNode(true));
        if (titleElement) {
          secondPart.appendChild(titleElement);
        }
      }

      firstPart.appendChild(firstHalfElement);
      secondPart.appendChild(secondHalfElement);
      splitFound = true;
    }
  }

  // If we still couldn't find a good split, just use the original approach
  if (firstPart.children.length === 0) {
    return [section, document.createElement('div')];
  }

  // If the second part is empty, return only the first part
  if (secondPart.children.length === 0) {
    return [firstPart, document.createElement('div')];
  }

  return [firstPart, secondPart];
}

// Advanced helper function to paginate content with subsection-based approach for optimal space usage
export function paginateContent(container: HTMLElement, sections: HTMLElement[]): void {
  // Clear the container
  container.innerHTML = '';

  // Create the first page
  let currentPage = createA4Page();
  container.appendChild(currentPage);

  let currentPageContent = document.createElement('div');
  currentPage.appendChild(currentPageContent);

  let currentPageHeight = 0;
  const maxPageContentHeight = CONTENT_HEIGHT_PX;

  // Create a temporary container to measure content
  const tempContainer = document.createElement('div');
  tempContainer.style.position = 'absolute';
  tempContainer.style.visibility = 'hidden';
  tempContainer.style.width = `${CONTENT_WIDTH_PX}px`;
  document.body.appendChild(tempContainer);

  // Collect all subsections from the sections
  const allSubsections: HTMLElement[] = [];

  // First, extract all subsections
  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];

    // Skip empty sections
    if (!section || section.children.length === 0) continue;

    // Check if this is a subsection or a section with subsections
    if (section.classList.contains('pdf-subsection')) {
      // This is already a subsection, add it directly
      allSubsections.push(section);
    } else if (section.classList.contains('pdf-section')) {
      // This is a section that might contain subsections
      const subsections = section.querySelectorAll('.pdf-subsection');
      if (subsections.length > 0) {
        // Add all subsections from this section
        subsections.forEach(subsection => {
          allSubsections.push(subsection as HTMLElement);
        });
      } else {
        // No subsections found, treat the entire section as one unit
        allSubsections.push(section);
      }
    } else {
      // Regular element, treat as a single unit
      allSubsections.push(section);
    }
  }

  console.log(`Found ${allSubsections.length} subsections to paginate`);

  // Now process each subsection
  for (let i = 0; i < allSubsections.length; i++) {
    const subsection = allSubsections[i];

    // Skip empty subsections
    if (!subsection || subsection.children.length === 0) continue;

    // Clone the subsection to measure its height
    const clonedSubsection = subsection.cloneNode(true) as HTMLElement;
    tempContainer.appendChild(clonedSubsection);
    const subsectionHeight = clonedSubsection.offsetHeight;
    tempContainer.removeChild(clonedSubsection);

    // If the subsection is very small, add a minimum height
    const effectiveSubsectionHeight = Math.max(subsectionHeight, 20);

    // Check if the subsection fits on the current page
    if (currentPageHeight + effectiveSubsectionHeight <= maxPageContentHeight) {
      // Add the subsection to the current page
      const subsectionClone = subsection.cloneNode(true) as HTMLElement;
      subsectionClone.setAttribute('data-pdf-subsection', `subsection-${i}`);
      currentPageContent.appendChild(subsectionClone);
      currentPageHeight += effectiveSubsectionHeight;

      // Add a small margin between subsections
      if (i < allSubsections.length - 1) {
        const spacer = document.createElement('div');
        spacer.style.height = '6px';
        currentPageContent.appendChild(spacer);
        currentPageHeight += 6;
      }
    }
    // If the subsection doesn't fit on the current page
    else {
      // Check if we're at the start of a page and the subsection is too large
      if (currentPageHeight === 0 && effectiveSubsectionHeight > maxPageContentHeight) {
        // This subsection is larger than a full page, we need to split it
        let remainingSubsection = subsection.cloneNode(true) as HTMLElement;
        let pageCount = 0;

        while (remainingSubsection.children.length > 0 && pageCount < 10) { // Limit to 10 pages to prevent infinite loops
          const remainingHeight = maxPageContentHeight - currentPageHeight;
          const [pagePart, nextPart] = splitSectionContent(remainingSubsection, remainingHeight);

          if (pagePart.children.length > 0) {
            pagePart.setAttribute('data-pdf-subsection', `subsection-${i}-part${pageCount}`);
            currentPageContent.appendChild(pagePart);
          }

          // If there's more content, create a new page
          if (nextPart.children.length > 0) {
            currentPage = createA4Page();
            container.appendChild(currentPage);
            currentPageContent = document.createElement('div');
            currentPage.appendChild(currentPageContent);
            currentPageHeight = 0;
            remainingSubsection = nextPart;
          } else {
            break; // No more content to add
          }

          pageCount++;
        }
      } else {
        // Create a new page for this subsection
        currentPage = createA4Page();
        container.appendChild(currentPage);
        currentPageContent = document.createElement('div');
        currentPage.appendChild(currentPageContent);
        currentPageHeight = 0;

        // Add the subsection to the new page
        const subsectionClone = subsection.cloneNode(true) as HTMLElement;
        subsectionClone.setAttribute('data-pdf-subsection', `subsection-${i}`);
        currentPageContent.appendChild(subsectionClone);
        currentPageHeight += effectiveSubsectionHeight;

        // Add a small margin between subsections
        if (i < allSubsections.length - 1) {
          const spacer = document.createElement('div');
          spacer.style.height = '6px';
          currentPageContent.appendChild(spacer);
          currentPageHeight += 6;
        }
      }
    }
  }

  // Add page numbers to each page
  const pages = container.querySelectorAll('.a4-page');
  pages.forEach((page, index) => {
    const pageNumber = document.createElement('div');
    pageNumber.className = 'pdf-page-number';
    pageNumber.style.position = 'absolute';
    pageNumber.style.bottom = '30px';
    pageNumber.style.right = '60px';
    pageNumber.style.fontSize = '9px';
    pageNumber.style.color = '#888';
    pageNumber.style.fontFamily = 'Arial, sans-serif';
    pageNumber.textContent = `Page ${index + 1} of ${pages.length}`;
    page.appendChild(pageNumber);
  });

  // Clean up
  document.body.removeChild(tempContainer);
}
