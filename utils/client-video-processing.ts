/**
 * Client-side utility for processing video files
 */

/**
 * Extracts audio from a video file using the Web Audio API
 * @param videoFile The video file to extract audio from
 * @returns A Promise that resolves to an audio file (Blob)
 */
export async function extractAudioFromVideo(videoFile: File): Promise<Blob> {
  return new Promise((resolve, reject) => {
    // Create a video element to load the video
    const video = document.createElement('video');
    video.preload = 'metadata';

    // Create object URL for the video file
    const videoURL = URL.createObjectURL(videoFile);
    video.src = videoURL;

    // Set up the audio context
    const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
    const audioContext = new AudioContext();

    // Create a media element source
    let source: MediaElementAudioSourceNode;

    // Create a media recorder to capture the audio
    let mediaRecorder: MediaRecorder;
    const chunks: Blob[] = [];

    // Handle when the video can play
    video.oncanplay = async () => {
      try {
        // Connect the video's audio to the audio context
        source = audioContext.createMediaElementSource(video);
        const destination = audioContext.createMediaStreamDestination();
        source.connect(destination);

        // Create a media recorder to capture the audio
        mediaRecorder = new MediaRecorder(destination.stream);

        // Collect audio data
        mediaRecorder.ondataavailable = (e) => {
          if (e.data.size > 0) {
            chunks.push(e.data);
          }
        };

        // When recording stops, create the audio blob
        mediaRecorder.onstop = () => {
          const audioBlob = new Blob(chunks, { type: 'audio/mp3' });
          URL.revokeObjectURL(videoURL);
          resolve(audioBlob);
        };

        // Start recording and playing the video
        mediaRecorder.start();
        await video.play();

        // Stop recording when the video ends
        video.onended = () => {
          mediaRecorder.stop();
          video.pause();
        };

        // If the video is too long, stop after 30 minutes (to avoid memory issues)
        setTimeout(() => {
          if (mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
            video.pause();
          }
        }, 30 * 60 * 1000);
      } catch (error) {
        URL.revokeObjectURL(videoURL);
        reject(error);
      }
    };

    // Handle errors
    video.onerror = (error) => {
      URL.revokeObjectURL(videoURL);
      reject(error);
    };
  });
}

/**
 * Creates a smaller audio file from a video file
 * @param videoFile The video file to process
 * @returns A Promise that resolves to an object containing both the original video and the extracted audio
 */
export async function createAudioFileFromVideo(videoFile: File): Promise<{ videoFile: File, audioFile: File }> {
  try {
    // Extract audio from video
    const audioBlob = await extractAudioFromVideo(videoFile);

    // Create a new File object from the blob
    const audioFile = new File(
      [audioBlob],
      `audio_${videoFile.name.replace(/\.[^/.]+$/, '')}.mp3`,
      { type: 'audio/mp3' }
    );

    return {
      videoFile: videoFile,  // Return the original video file
      audioFile: audioFile   // Return the extracted audio file
    };
  } catch (error) {
    console.error('Error creating audio from video:', error);
    throw error;
  }
}
