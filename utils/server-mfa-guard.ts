/**
 * Server-Side MFA Guard Utilities
 *
 * These utilities provide server-side MFA enforcement for sensitive operations
 * and API routes, ensuring that critical actions require MFA verification.
 */

import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { NextRequest, NextResponse } from 'next/server';
import { getMFAStatusServer, type MFAStatus } from '@/utils/server-mfa-security';

export interface MFAGuardResult {
  success: boolean;
  user: any | null;
  mfaStatus: MFAStatus;
  error?: string;
}

/**
 * Server-side MFA guard for page components
 * Use this in server components that handle sensitive data
 */
export async function requireMFAForPage(): Promise<MFAGuardResult> {
  const supabase = await createClient();

  try {
    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      redirect('/sign-in');
    }

    // Get MFA status using server utilities
    const mfaStatus = await getMFAStatusServer();

    // If MFA is required but not verified, redirect to challenge
    if (mfaStatus.needsChallenge) {
      redirect('/mfa-challenge');
    }

    return {
      success: true,
      user,
      mfaStatus
    };
  } catch (error) {
    return {
      success: false,
      user: null,
      mfaStatus: {
        isRequired: false,
        isEnrolled: false,
        isVerified: false,
        currentLevel: null,
        nextLevel: null,
        factors: [],
        needsChallenge: false
      },
      error: 'Internal server error'
    };
  }
}

/**
 * API route MFA guard
 * Use this in API routes that handle sensitive operations
 */
export async function requireMFAForAPI(request: NextRequest): Promise<MFAGuardResult> {
  const supabase = await createClient();

  try {
    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        user: null,
        mfaStatus: {
          isRequired: false,
          isEnrolled: false,
          isVerified: false,
          currentLevel: null,
          nextLevel: null,
          factors: [],
          needsChallenge: false
        },
        error: 'Authentication required'
      };
    }

    // Get MFA status using server utilities
    const mfaStatus = await getMFAStatusServer();

    // For API routes, return the status without redirecting
    return {
      success: !mfaStatus.needsChallenge, // Success if no challenge is needed
      user,
      mfaStatus,
      error: mfaStatus.needsChallenge ? 'MFA verification required' : undefined
    };
  } catch (error) {
    return {
      success: false,
      user: null,
      mfaStatus: {
        isRequired: false,
        isEnrolled: false,
        isVerified: false,
        currentLevel: null,
        nextLevel: null,
        factors: [],
        needsChallenge: false
      },
      error: 'Internal server error'
    };
  }
}

/**
 * Create a standardized API error response for MFA failures
 */
export function createMFAErrorResponse(result: MFAGuardResult): NextResponse {
  if (!result.success && result.error === 'Authentication required') {
    return NextResponse.json(
      { error: 'Authentication required', code: 'AUTH_REQUIRED' },
      { status: 401 }
    );
  }

  if (!result.success && result.mfaStatus.needsChallenge) {
    return NextResponse.json(
      {
        error: 'MFA verification required',
        code: 'MFA_REQUIRED',
        mfaStatus: result.mfaStatus
      },
      { status: 403 }
    );
  }

  return NextResponse.json(
    { error: result.error || 'Security check failed', code: 'SECURITY_ERROR' },
    { status: 500 }
  );
}

/**
 * Wrapper for API routes that require MFA
 * Usage: export const POST = withMFAGuard(async (request, { user }) => { ... });
 */
export function withMFAGuard(
  handler: (request: NextRequest, context: { user: any; mfaStatus: any }) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const mfaResult = await requireMFAForAPI(request);

    if (!mfaResult.success) {
      return createMFAErrorResponse(mfaResult);
    }

    return handler(request, {
      user: mfaResult.user,
      mfaStatus: mfaResult.mfaStatus
    });
  };
}

/**
 * Check if a specific operation requires MFA based on its sensitivity level
 */
export function operationRequiresMFA(operation: string): boolean {
  const mfaRequiredOperations = [
    'delete_household',
    'update_financial_data',
    'export_data',
    'change_password',
    'update_profile',
    'manage_organization',
    'invite_users'
  ];

  return mfaRequiredOperations.includes(operation);
}
