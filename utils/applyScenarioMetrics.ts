import { ScenarioMetricsData, isToggleEnabled, getDefaultValue } from '@/hooks/useScenarioMetrics';

/**
 * Applies scenario metrics to form values
 * 
 * @param metrics The scenario metrics data
 * @param formValues The current form values
 * @returns Updated form values with metrics applied
 */
export function applyScenarioMetrics(metrics: ScenarioMetricsData, formValues: any): any {
  const updatedValues = { ...formValues };
  
  // Apply personal settings
  if (isToggleEnabled(metrics, 'personal', 'include_partner')) {
    updatedValues.includePartner = getDefaultValue(metrics, 'personal', 'include_partner', true);
  }
  
  // Apply income settings
  if (isToggleEnabled(metrics, 'income', 'include_superannuation')) {
    updatedValues.includeSuperannuation = getDefaultValue(metrics, 'income', 'include_superannuation', true);
  }
  
  // Apply inflation settings
  if (isToggleEnabled(metrics, 'income', 'enable_inflation')) {
    if (isToggleEnabled(metrics, 'income', 'income_inflation_rate')) {
      updatedValues.incomeInflationRate = getDefaultValue(metrics, 'income', 'income_inflation_rate', 2.0);
    }
    
    if (isToggleEnabled(metrics, 'income', 'partner_income_inflation_rate')) {
      updatedValues.partnerIncomeInflationRate = getDefaultValue(metrics, 'income', 'partner_income_inflation_rate', 2.0);
    }
    
    if (isToggleEnabled(metrics, 'income', 'additional_income_inflation_rate')) {
      updatedValues.additionalIncomeInflationRate = getDefaultValue(metrics, 'income', 'additional_income_inflation_rate', 2.0);
    }
  }
  
  // Apply expense settings
  if (isToggleEnabled(metrics, 'expense', 'second_expense')) {
    updatedValues.includeSecondExpense = getDefaultValue(metrics, 'expense', 'second_expense', true);
  }
  
  if (isToggleEnabled(metrics, 'expense', 'enable_inflation')) {
    if (isToggleEnabled(metrics, 'expense', 'expense1_inflation_rate')) {
      updatedValues.expense1InflationRate = getDefaultValue(metrics, 'expense', 'expense1_inflation_rate', 2.0);
    }
    
    if (isToggleEnabled(metrics, 'expense', 'expense2_inflation_rate')) {
      updatedValues.expense2InflationRate = getDefaultValue(metrics, 'expense', 'expense2_inflation_rate', 2.0);
    }
    
    if (isToggleEnabled(metrics, 'expense', 'additional_expense_inflation_rate')) {
      updatedValues.additionalExpenseInflationRate = getDefaultValue(metrics, 'expense', 'additional_expense_inflation_rate', 2.0);
    }
  }
  
  // Apply savings settings
  if (isToggleEnabled(metrics, 'savings', 'cash_reserve')) {
    updatedValues.cashReserve = getDefaultValue(metrics, 'savings', 'cash_reserve', 10000);
  }
  
  if (isToggleEnabled(metrics, 'savings', 'saving_percentage')) {
    updatedValues.savingPercentage = getDefaultValue(metrics, 'savings', 'saving_percentage', 10);
  }
  
  // Apply investment settings
  if (isToggleEnabled(metrics, 'investment', 'utilise_excess_cashflow')) {
    updatedValues.utiliseExcessCashflow = getDefaultValue(metrics, 'investment', 'utilise_excess_cashflow', true);
  }
  
  if (isToggleEnabled(metrics, 'investment', 'allocate_to_investment')) {
    updatedValues.allocateToInvestment = getDefaultValue(metrics, 'investment', 'allocate_to_investment', true);
  }
  
  // Apply KiwiSaver settings
  if (isToggleEnabled(metrics, 'kiwisaver', 'kiwisaver_contribution')) {
    updatedValues.kiwisaverContribution = getDefaultValue(metrics, 'kiwisaver', 'kiwisaver_contribution', 3);
  }
  
  if (isToggleEnabled(metrics, 'kiwisaver', 'employer_contribution')) {
    updatedValues.employerContribution = getDefaultValue(metrics, 'kiwisaver', 'employer_contribution', 3);
  }
  
  if (isToggleEnabled(metrics, 'kiwisaver', 'consolidate_kiwisaver')) {
    updatedValues.consolidateKiwisaver = getDefaultValue(metrics, 'kiwisaver', 'consolidate_kiwisaver', false);
  }
  
  // Apply property settings
  if (isToggleEnabled(metrics, 'property', 'rental_income')) {
    updatedValues.rentalIncome = getDefaultValue(metrics, 'property', 'rental_income', 0);
  }
  
  if (isToggleEnabled(metrics, 'property', 'board_income')) {
    updatedValues.boardIncome = getDefaultValue(metrics, 'property', 'board_income', 0);
  }
  
  if (isToggleEnabled(metrics, 'property', 'interest_only_period')) {
    updatedValues.interestOnlyPeriod = getDefaultValue(metrics, 'property', 'interest_only_period', false);
  }
  
  if (isToggleEnabled(metrics, 'property', 'property_inflation_rate')) {
    updatedValues.propertyInflationRate = getDefaultValue(metrics, 'property', 'property_inflation_rate', 2.0);
  }
  
  // Apply misc settings
  if (isToggleEnabled(metrics, 'misc', 'show_savings')) {
    updatedValues.showSavings = getDefaultValue(metrics, 'misc', 'show_savings', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_investment')) {
    updatedValues.showInvestment = getDefaultValue(metrics, 'misc', 'show_investment', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_kiwisaver')) {
    updatedValues.showKiwisaver = getDefaultValue(metrics, 'misc', 'show_kiwisaver', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_monte_carlo')) {
    updatedValues.showMonteCarlo = getDefaultValue(metrics, 'misc', 'show_monte_carlo', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_property_value')) {
    updatedValues.showPropertyValue = getDefaultValue(metrics, 'misc', 'show_property_value', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_debt_value')) {
    updatedValues.showDebtValue = getDefaultValue(metrics, 'misc', 'show_debt_value', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_annotations')) {
    updatedValues.showAnnotations = getDefaultValue(metrics, 'misc', 'show_annotations', false);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_realistic_netwealth')) {
    updatedValues.showRealisticNetwealth = getDefaultValue(metrics, 'misc', 'show_realistic_netwealth', true);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'show_cashflow')) {
    updatedValues.showCashflow = getDefaultValue(metrics, 'misc', 'show_cashflow', true);
  }
  
  // Apply simulation settings
  if (isToggleEnabled(metrics, 'misc', 'num_simulations')) {
    updatedValues.numSimulations = getDefaultValue(metrics, 'misc', 'num_simulations', 1000);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'confidence_interval')) {
    updatedValues.confidenceInterval = getDefaultValue(metrics, 'misc', 'confidence_interval', 95);
  }
  
  if (isToggleEnabled(metrics, 'misc', 'inflation_rate')) {
    updatedValues.inflationRate = getDefaultValue(metrics, 'misc', 'inflation_rate', 2.0);
  }
  
  // Apply fund settings
  updatedValues.funds = metrics.funds;
  
  return updatedValues;
}
