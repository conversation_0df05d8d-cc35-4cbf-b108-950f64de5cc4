/** @type {import('next').NextConfig} */

/**
 * Get Supabase domain from environment variable
 * This prevents hardcoding the Supabase URL in the configuration
 */
function getSupabaseDomain() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) {
    console.warn('NEXT_PUBLIC_SUPABASE_URL not configured, using localhost for images');
    return 'localhost';
  }

  try {
    return new URL(url).hostname;
  } catch {
    console.error('Invalid NEXT_PUBLIC_SUPABASE_URL format');
    return 'localhost';
  }
}

const nextConfig = {
  output: 'standalone', // Required for DigitalOcean App Platform
  // Fix for SWC dependencies warning
  experimental: {
    swcPlugins: [
      ['next-superjson-plugin', {}]
    ]
  },
  images: {
    domains: [
      getSupabaseDomain(),
      // Add other domains as needed
      'localhost',
      '127.0.0.1'
    ]
  }
}

module.exports = nextConfig
