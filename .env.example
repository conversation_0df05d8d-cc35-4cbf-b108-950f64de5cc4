# Wealthie Application Environment Variables
# Copy this file to .env.local and fill in your values
# 
# SECURITY NOTE: Never commit .env.local to version control
# Add .env.local to your .gitignore file

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Supabase Configuration (Required)
# Get these values from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# OPTIONAL EXTERNAL API KEYS
# =============================================================================

# OpenAI API Key (Optional - for AI features)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google Generative AI API Key (Optional - for Gemini AI features)
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Resend API Key (Optional - for email features)
# Get from: https://resend.com/api-keys
RESEND_API_KEY=your_resend_api_key_here

# Groq API Key (Optional - for alternative AI features)
# Get from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# =============================================================================
# LIVEKIT CONFIGURATION (Optional - for video/audio features)
# =============================================================================

# LiveKit Configuration (Optional)
# Get from your LiveKit project dashboard
NEXT_PUBLIC_LIVEKIT_URL=your_livekit_url_here
LIVEKIT_API_KEY=your_livekit_api_key_here
LIVEKIT_API_SECRET=your_livekit_api_secret_here

# =============================================================================
# S3 STORAGE CONFIGURATION (Optional)
# =============================================================================

# S3 Configuration (Optional - for file storage)
# Get from your AWS S3 or Supabase Storage configuration
SUPABASE_S3_ACCESS_KEY_ID=your_s3_access_key_here
SUPABASE_S3_SECRET_ACCESS_KEY=your_s3_secret_key_here
S3_HOST=your_s3_host_here
S3_REGION=your_s3_region_here
S3_ACCESS_KEY=your_s3_access_key_here
S3_SECRET_KEY=your_s3_secret_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment
NODE_ENV=development

# Site URL (adjust for your deployment)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Supabase Local Development Ports (for local development only)
SUPABASE_PROJECT_ID=Wealthie
SUPABASE_API_PORT=54321
SUPABASE_DB_PORT=54322
SUPABASE_STUDIO_PORT=54323
SUPABASE_API_URL=http://127.0.0.1

# Authentication Configuration
SITE_URL=http://127.0.0.1:3000
ADDITIONAL_REDIRECT_URLS=https://127.0.0.1:3000

# =============================================================================
# SMS/MESSAGING CONFIGURATION (Optional)
# =============================================================================

# Twilio Configuration (Optional - for SMS features)
SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit this file with real values to version control
# 2. Use strong, unique API keys for each service
# 3. Rotate API keys regularly for security
# 4. Use different keys for development, staging, and production
# 5. Monitor API key usage for suspicious activity
# 6. Store production keys securely (e.g., in deployment platform secrets)

# =============================================================================
# VALIDATION NOTES
# =============================================================================

# Required format examples:
# - Supabase URL: https://your-project.supabase.co
# - Supabase Keys: eyJ... (JWT format)
# - OpenAI Key: sk-... (starts with sk-)
# - Resend Key: re_... (starts with re_)

# The application will validate these formats on startup
# and provide helpful error messages if any are incorrect.

# =============================================================================
# DEPLOYMENT NOTES
# =============================================================================

# For production deployment:
# 1. Set NODE_ENV=production
# 2. Update NEXT_PUBLIC_SITE_URL to your production domain
# 3. Use production Supabase project URLs and keys
# 4. Ensure all URLs use HTTPS in production
# 5. Configure proper CORS settings in Supabase
# 6. Set up proper authentication redirect URLs

# For staging/testing:
# 1. Use separate Supabase project for testing
# 2. Set appropriate SITE_URL for your staging environment
# 3. Use test API keys where available

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you encounter configuration errors:
# 1. Check that all required variables are set
# 2. Verify URL formats are correct
# 3. Ensure API keys are valid and not expired
# 4. Check Supabase project settings and permissions
# 5. Review application logs for specific error messages

# For help with configuration:
# - Supabase: https://supabase.com/docs
# - OpenAI: https://platform.openai.com/docs
# - Resend: https://resend.com/docs
# - LiveKit: https://docs.livekit.io
