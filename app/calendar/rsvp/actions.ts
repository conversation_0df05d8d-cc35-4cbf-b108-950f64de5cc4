'use server';

import { createClient } from '@/utils/supabase/server';

export async function updateRSVPResponse(eventId: string, email: string, response: string) {
  try {
    const supabase = createClient();
    
    // Get the event
    const { data: event, error: eventError } = await supabase
      .from('calendar_events')
      .select('id, title, date, end_date, start_time, end_time, is_all_day, responses')
      .eq('id', eventId)
      .single();
    
    if (eventError || !event) {
      return { success: false, error: 'Event not found', eventDetails: null };
    }
    
    // Update the responses
    const currentResponses = event.responses || {};
    currentResponses[email] = response;
    
    const { error: updateError } = await supabase
      .from('calendar_events')
      .update({ responses: currentResponses })
      .eq('id', eventId);
    
    if (updateError) {
      console.error('Error updating event responses:', updateError);
      return { success: false, error: 'Failed to update response', eventDetails: null };
    }
    
    return { 
      success: true, 
      error: null, 
      eventDetails: event 
    };
  } catch (error) {
    console.error('Error handling RSVP:', error);
    return { success: false, error: 'An unexpected error occurred', eventDetails: null };
  }
}
