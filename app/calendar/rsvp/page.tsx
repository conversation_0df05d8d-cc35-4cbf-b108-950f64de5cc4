'use client';

import { useEffect, useState, Suspense } from 'react'; // Import Suspense
import { useSearchParams } from 'next/navigation';
import { updateRSVPResponse } from './actions';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle2, XCircle, AlertCircle, Calendar } from 'lucide-react';
import Link from 'next/link';

// Explicitly mark the page as dynamic
export const dynamic = 'force-dynamic';

// Inner component to handle logic depending on searchParams
function RSVPContent() {
  const searchParams = useSearchParams();
  const eventId = searchParams.get('eventId');
  const response = searchParams.get('response');
  const email = searchParams.get('email');
  const error = searchParams.get('error');

  // If there's an error parameter, set the error state immediately
  useEffect(() => {
    if (error) {
      setStatus('error');
      switch (error) {
        case 'invalid_email':
          setErrorMessage('Invalid email address format');
          break;
        case 'invalid_rsvp':
          setErrorMessage('Missing required information');
          break;
        case 'invalid_response':
          setErrorMessage('Invalid response type');
          break;
        case 'event_not_found':
          setErrorMessage('Event not found');
          break;
        case 'update_failed':
          setErrorMessage('Failed to update your response');
          break;
        case 'server_error':
          setErrorMessage('An unexpected server error occurred');
          break;
        default:
          setErrorMessage('An error occurred processing your response');
      }
    }
  }, [error]);

  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [eventDetails, setEventDetails] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // Skip processing if we're already in an error state from the URL parameters
    if (error) return;

    const processRSVP = async () => {
      if (!eventId || !response || !email) {
        setStatus('error');
        setErrorMessage('Missing required parameters');
        return;
      }

      // Validate response value
      if (!['yes', 'no', 'maybe'].includes(response)) {
        setStatus('error');
        setErrorMessage('Invalid response value');
        return;
      }

      try {
        // Use the server action to update the RSVP response
        const result = await updateRSVPResponse(eventId, email, response);

        if (!result.success) {
          setStatus('error');
          setErrorMessage(result.error || 'Failed to update your response');
          return;
        }

        setEventDetails(result.eventDetails);
        setStatus('success');
      } catch (error) {
        console.error('Error handling RSVP:', error);
        setStatus('error');
        setErrorMessage('An unexpected error occurred');
      }
    };

    processRSVP();
  }, [eventId, response, email, error]);

  // Format the response for display
  const getResponseText = () => {
    switch (response) {
      case 'yes':
        return 'attending';
      case 'no':
        return 'not attending';
      case 'maybe':
        return 'tentatively attending';
      default:
        return '';
    }
  };

  // Format the date for display
  const formatDate = (dateStr: string, isAllDay: boolean, startTime?: string, endTime?: string, endDate?: string) => {
    const date = new Date(dateStr);
    const formattedDate = date.toLocaleDateString('en-NZ', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    if (isAllDay) {
      if (endDate && endDate !== dateStr) {
        const endDateObj = new Date(endDate);
        const formattedEndDate = endDateObj.toLocaleDateString('en-NZ', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
        return `${formattedDate} - ${formattedEndDate} (All day)`;
      }
      return `${formattedDate} (All day)`;
    }

    // Ensure start and end times are provided before formatting
    const timeString = startTime && endTime ? `, ${startTime} - ${endTime}` : '';
    return `${formattedDate}${timeString}`;
  };

  // Get the appropriate icon based on the response
  const getResponseIcon = () => {
    switch (response) {
      case 'yes':
        return <CheckCircle2 className="h-12 w-12 text-green-500" />;
      case 'no':
        return <XCircle className="h-12 w-12 text-red-500" />;
      case 'maybe':
        return <AlertCircle className="h-12 w-12 text-amber-500" />;
      default:
        // Return a default icon if response is invalid or missing initially
        return <Calendar className="h-12 w-12 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full max-w-md shadow-lg">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          {/* Render icon based on status if loading, otherwise use response */}
          {status === 'loading' ? <Calendar className="h-12 w-12 text-gray-500 animate-pulse" /> : getResponseIcon()}
        </div>
        <CardTitle className="text-2xl">Calendar Response</CardTitle>
        {status === 'loading' && (
          <CardDescription>Processing your response...</CardDescription>
        )}
      </CardHeader>

      <CardContent>
        {status === 'loading' && (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        )}

        {status === 'success' && eventDetails && (
          <div className="space-y-4">
            <p className="text-center text-lg font-medium">
              You have confirmed that you are <span className="font-bold">{getResponseText()}</span> this event.
            </p>

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="font-bold text-lg mb-2">{eventDetails.title}</h3>
              <p className="text-gray-600">
                {formatDate(
                  eventDetails.date,
                  eventDetails.is_all_day,
                  eventDetails.start_time,
                  eventDetails.end_time,
                  eventDetails.end_date
                )}
              </p>
            </div>

            <p className="text-sm text-gray-500 text-center">
              Your response has been recorded. You can change your response by clicking on the RSVP links in the original email.
            </p>
          </div>
        )}

        {status === 'error' && (
          <div className="text-center py-4">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 font-medium">Error: {errorMessage}</p>
            <p className="text-sm text-gray-500 mt-2">
              Please try again or contact the event organizer for assistance.
            </p>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-center">
        <Button asChild>
          <a href="/" target="_blank" rel="noopener noreferrer">Go to Wealthie</a>
        </Button>
      </CardFooter>
    </Card>
  );
}


// Main page component wraps the content in Suspense
export default function RSVPResponsePage() {
  return (
    <div className="flex min-h-screen bg-gray-50 items-center justify-center p-4">
      <Suspense fallback={
        // Simple loading state for Suspense
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="text-center">
             <div className="flex justify-center mb-4">
                <Calendar className="h-12 w-12 text-gray-500 animate-pulse" />
             </div>
             <CardTitle className="text-2xl">Loading Response...</CardTitle>
          </CardHeader>
          <CardContent>
             <div className="flex justify-center py-8">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
             </div>
          </CardContent>
           <CardFooter className="flex justify-center">
           </CardFooter>
        </Card>
      }>
        <RSVPContent />
      </Suspense>
    </div>
  );
}
