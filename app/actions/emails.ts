'use server';

import { Resend } from 'resend';
import { createClient } from "@/utils/supabase/server";
import { OrganizationInviteEmail } from '@/components/emails/OrganizationInviteEmail';
import { VerificationEmail } from '@/components/emails/VerificationEmail';
import { PasswordResetEmail } from '@/components/emails/PasswordResetEmail';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * Sends an organization invitation email
 */
export async function sendOrganizationInvitationEmail(
  email: string,
  token: string,
  organizationName: string
) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    // Use the correct URL format that the sign-up form expects
    const inviteUrl = `${siteUrl}/sign-up?token=${token}`;

    console.log(`Sending invitation email to ${email} with token: ${token}`);

    const { data, error } = await resend.emails.send({
      from: 'Wealthie <<EMAIL>>',
      to: [email],
      subject: `You've been invited to join ${organizationName} on Wealthie`,
      react: OrganizationInviteEmail({
        organizationName,
        inviteUrl,
        siteUrl
      }),
      headers: {
        'X-Entity-Ref-ID': `invite-${token}`,
        'List-Unsubscribe': '<mailto:<EMAIL>>',
      },
    });

    if (error) {
      console.error('Error sending invitation email:', error);
      return { success: false, error };
    }

    console.log('Invitation email sent:', data?.id);
    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Error sending invitation email:', error);
    return { success: false, error };
  }
}

/**
 * Sends a verification email
 * This is a wrapper for Supabase's email verification
 * It's not directly used but shows how to implement custom verification emails
 */
export async function sendVerificationEmail(
  email: string,
  token: string,
  tokenHash: string,
  redirectTo: string
) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    const { data, error } = await resend.emails.send({
      from: 'Wealthie <<EMAIL>>',
      to: [email],
      subject: 'Verify your email address',
      react: VerificationEmail({
        token,
        tokenHash,
        redirectTo,
        siteUrl
      }),
      headers: {
        'X-Entity-Ref-ID': `verify-${tokenHash}`,
        'List-Unsubscribe': '<mailto:<EMAIL>>',
      },
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return { success: false, error };
    }

    console.log('Verification email sent:', data?.id);
    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Error sending verification email:', error);
    return { success: false, error };
  }
}

/**
 * Sends a password reset email
 * This is a wrapper for Supabase's password reset
 * It's not directly used but shows how to implement custom reset emails
 */
export async function sendPasswordResetEmail(
  email: string,
  token: string,
  tokenHash: string,
  redirectTo: string
) {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    const { data, error } = await resend.emails.send({
      from: 'Wealthie <<EMAIL>>',
      to: [email],
      subject: 'Reset your password',
      react: PasswordResetEmail({
        token,
        tokenHash,
        redirectTo,
        siteUrl
      }),
      headers: {
        'X-Entity-Ref-ID': `reset-${tokenHash}`,
        'List-Unsubscribe': '<mailto:<EMAIL>>',
      },
    });

    if (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, error };
    }

    console.log('Password reset email sent:', data?.id);
    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return { success: false, error };
  }
}
