'use server';

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Creates an organization with admin privileges
 * This function bypasses RLS policies using the service role
 */
export async function createOrganizationAdmin(name: string, userId: string) {
  try {
    console.log('Creating organization with admin privileges for user:', userId);

    // Create organization with admin privileges
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from('organizations')
      .insert({
        name: name
      })
      .select('id')
      .single();

    if (orgError) {
      console.error('Admin organization creation error:', orgError);
      return { success: false, error: orgError };
    }

    if (!orgData) {
      console.error('No organization data returned after creation');
      return { success: false, error: 'No organization data returned' };
    }

    console.log('Organization created with ID:', orgData.id);

    // Update profile with organization info and set role to 'owner'
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        org_id: orgData.id.toString(),
        org_name: name,
        org_role: 'owner' // Organization creator is always the owner
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error('Error updating profile with organization:', updateError);
      return { success: false, error: updateError };
    }

    console.log('Profile updated with organization info');
    return { success: true, organizationId: orgData.id };
  } catch (error) {
    console.error('Admin organization creation error:', error);
    return { success: false, error };
  }
}

/**
 * Associates a user with an existing organization using admin privileges
 * This function bypasses RLS policies using the service role
 */
export async function associateUserWithOrganizationAdmin(userId: string, orgId: string, role: string = 'readonly') {
  try {
    console.log('Associating user with organization using admin privileges');

    // Validate role
    if (!['owner', 'full', 'readonly'].includes(role)) {
      console.warn(`Invalid role: ${role}, defaulting to 'readonly'`);
      role = 'readonly';
    }

    // Get organization details
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from('organizations')
      .select('id, name')
      .eq('id', orgId)
      .single();

    if (orgError) {
      console.error('Error getting organization details:', orgError);
      return { success: false, error: orgError };
    }

    if (!orgData) {
      console.error('Organization not found');
      return { success: false, error: 'Organization not found' };
    }

    // Update profile with organization info and role
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        org_id: orgData.id.toString(),
        org_name: orgData.name,
        org_role: role
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error('Error updating profile with organization:', updateError);
      return { success: false, error: updateError };
    }

    console.log(`Profile updated with organization info from invitation with role: ${role}`);
    return { success: true };
  } catch (error) {
    console.error('Error in organization association:', error);
    return { success: false, error };
  }
}

/**
 * Updates an invitation status using admin privileges
 * This function bypasses RLS policies using the service role
 */
export async function updateInvitationStatusAdmin(token: string, status: string) {
  try {
    console.log('Updating invitation status with admin privileges');

    const { error } = await supabaseAdmin
      .from('organization_invitations')
      .update({ status })
      .eq('token', token);

    if (error) {
      console.error('Error updating invitation status:', error);
      return { success: false, error };
    }

    console.log('Invitation status updated to:', status);
    return { success: true };
  } catch (error) {
    console.error('Error updating invitation status:', error);
    return { success: false, error };
  }
}
