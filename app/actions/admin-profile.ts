'use server';

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Creates a user profile with admin privileges
 * This function bypasses RLS policies using the service role
 */
export async function createProfileAdmin(userId: string, userData: any) {
  try {
    console.log('Creating profile with admin privileges for user:', userId);
    
    // First, check if profile already exists
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('user_id', userId)
      .single();
    
    // If profile already exists, return success
    if (existingProfile) {
      console.log('Profile already exists for user:', userId);
      return { success: true, profileId: existingProfile.id };
    }
    
    // If error is not "not found", return error
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking for existing profile:', checkError);
      return { success: false, error: checkError };
    }
    
    // Get the maximum ID value to avoid conflicts
    const { data: maxIdData } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .order('id', { ascending: false })
      .limit(1);
    
    const nextId = maxIdData && maxIdData.length > 0 ? maxIdData[0].id + 1 : 1;
    
    // Create profile with explicit ID
    const { error: insertError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: nextId,
        user_id: userId,
        name: userData.full_name || `${userData.first_name || ''} ${userData.last_name || ''}`.trim(),
        email: userData.email
      });
    
    if (insertError) {
      console.error('Error creating profile with admin privileges:', insertError);
      return { success: false, error: insertError };
    }
    
    console.log('Profile created successfully with admin privileges, ID:', nextId);
    return { success: true, profileId: nextId };
  } catch (error) {
    console.error('Admin profile creation error:', error);
    return { success: false, error };
  }
}
