'use server';

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

import { encodedRedirect } from "@/utils/utils";

/**
 * Creates a new organization invitation
 */
export async function createOrganizationInvitation(formData: FormData) {
  const email = formData.get('email')?.toString();
  const role = formData.get('role')?.toString() || 'readonly';
  const returnPath = formData.get('returnPath')?.toString() || '/protected/admin/organisation';

  if (!email) {
    return encodedRedirect('error', returnPath, 'Email is required');
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return encodedRedirect('error', returnPath, 'Please enter a valid email address');
  }

  // Validate role
  if (!['owner', 'full', 'readonly'].includes(role)) {
    return encodedRedirect('error', returnPath, 'Invalid role');
  }

  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return encodedRedirect('error', returnPath, 'User not authenticated');
  }

  // Get user's organization
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id')
    .eq('user_id', user.id)
    .single();

  if (profileError || !profile || !profile.org_id) {
    return encodedRedirect('error', returnPath, 'No organization found for user');
  }

  try {
    // Create invitation
    const { data: token, error: inviteError } = await supabase.rpc(
      'create_organization_invitation',
      {
        org_id: profile.org_id,
        email: email,
        created_by: user.id,
        expiry_hours: 48,
        role: role
      }
    );

    if (inviteError) {
      throw inviteError;
    }

    // Send invitation email
    await sendInvitationEmail(email, token, profile.org_id);

    revalidatePath(returnPath);
    return encodedRedirect('success', returnPath, `Invitation sent to ${email}`);
  } catch (error: any) {
    console.error('Error creating invitation:', error);
    return encodedRedirect('error', returnPath, error.message || 'Failed to create invitation');
  }
}

/**
 * Sends an invitation email to a user
 */
async function sendInvitationEmail(email: string, token: string, orgId: string) {
  const supabase = await createClient();

  console.log(`Preparing to send invitation email with token: ${token}`);

  // Get organization details
  const { data: organization } = await supabase
    .from('organizations')
    .select('name')
    .eq('id', orgId)
    .single();

  const orgName = organization?.name || 'an organization';

  // Double-check that the token exists in the database
  const { data: invitation, error: invitationError } = await supabase
    .from('organization_invitations')
    .select('id, token')
    .eq('token', token)
    .single();

  if (invitationError || !invitation) {
    console.error('Token not found in database before sending email:', invitationError);
    console.log('Attempting to send email anyway with token:', token);
  } else {
    console.log(`Found invitation in database with ID: ${invitation.id}, token: ${invitation.token}`);
  }

  // Import the email sending function
  const { sendOrganizationInvitationEmail } = await import('./emails');

  // Send the invitation email
  const result = await sendOrganizationInvitationEmail(email, token, orgName);

  if (!result.success) {
    console.error('Failed to send invitation email:', result.error);
  } else {
    console.log(`Invitation email sent to ${email} for ${orgName}, ID: ${result.messageId}`);
  }

  return result;
}

/**
 * Gets all members of the current user's organization
 */
export async function getOrganizationMembers() {
  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return { error: 'User not authenticated', data: null };
  }

  // Get user's organization
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id')
    .eq('user_id', user.id)
    .single();

  if (profileError || !profile || !profile.org_id) {
    return { error: 'No organization found for user', data: null };
  }

  // Get all members of the organization
  const { data: members, error: membersError } = await supabase
    .from('profiles')
    .select('id, user_id, name, email, org_role')
    .eq('org_id', profile.org_id);

  if (membersError) {
    return { error: membersError.message, data: null };
  }

  return { data: members, error: null };
}

/**
 * Gets all pending invitations for the current user's organization
 */
export async function getOrganizationInvitations() {
  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return { error: 'User not authenticated', data: null };
  }

  // Get user's organization
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id')
    .eq('user_id', user.id)
    .single();

  if (profileError || !profile || !profile.org_id) {
    return { error: 'No organization found for user', data: null };
  }

  // Get all pending invitations
  const { data: invitations, error: invitationsError } = await supabase
    .from('organization_invitations')
    .select('id, email, created_at, expires_at, status, role')
    .eq('organization_id', profile.org_id)
    .eq('status', 'pending')
    .order('created_at', { ascending: false });

  if (invitationsError) {
    return { error: invitationsError.message, data: null };
  }

  return { data: invitations, error: null };
}

/**
 * Cancels an organization invitation
 */
export async function cancelOrganizationInvitation(formData: FormData) {
  const invitationId = formData.get('invitationId')?.toString();
  const returnPath = formData.get('returnPath')?.toString() || '/protected/admin/organisation';

  if (!invitationId) {
    return encodedRedirect('error', returnPath, 'Invitation ID is required');
  }

  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return encodedRedirect('error', returnPath, 'User not authenticated');
  }

  // Update invitation status
  const { error: updateError } = await supabase
    .from('organization_invitations')
    .update({ status: 'cancelled' })
    .eq('id', invitationId)
    .eq('created_by', user.id);

  if (updateError) {
    return encodedRedirect('error', returnPath, updateError.message);
  }

  revalidatePath(returnPath);
  return encodedRedirect('success', returnPath, 'Invitation cancelled successfully');
}

/**
 * Updates organization details
 */
export async function updateOrganizationDetails(formData: FormData) {
  const name = formData.get('name')?.toString();
  const email = formData.get('email')?.toString();
  const phone = formData.get('phone')?.toString();
  const website = formData.get('website')?.toString();
  const address = formData.get('address')?.toString();
  const returnPath = formData.get('returnPath')?.toString() || '/protected/admin/organisation';

  if (!name) {
    return encodedRedirect('error', returnPath, 'Organization name is required');
  }

  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return encodedRedirect('error', returnPath, 'User not authenticated');
  }

  // Get user's organization
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('org_id')
    .eq('user_id', user.id)
    .single();

  if (profileError || !profile || !profile.org_id) {
    return encodedRedirect('error', returnPath, 'No organization found for user');
  }

  // Update organization details
  const { error: updateError } = await supabase
    .from('organizations')
    .update({
      name,
      email,
      phone,
      website,
      address,
      updated_at: new Date().toISOString()
    })
    .eq('id', profile.org_id);

  if (updateError) {
    return encodedRedirect('error', returnPath, updateError.message);
  }

  // Also update org_name in profiles table for all members
  await supabase
    .from('profiles')
    .update({ org_name: name })
    .eq('org_id', profile.org_id);

  revalidatePath(returnPath);
  return encodedRedirect('success', returnPath, 'Organization details updated successfully');
}
