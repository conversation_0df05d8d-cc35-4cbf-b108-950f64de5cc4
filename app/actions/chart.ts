'use server';

import { generateObject } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

const chartConfigSchema = z.object({
  type: z.enum(['bar', 'line', 'pie', 'area', 'card']).describe('Type of chart that best represents the data'),
  xKey: z.string().describe('Key for x-axis or category'),
  yKeys: z.array(z.string()).describe('Key(s) for y-axis values (typically the quantitative columns)'),
  title: z.string().describe('A concise, descriptive title for the chart'),
  description: z.string().describe('A brief explanation of what the chart shows'),
  legend: z.boolean().describe('Whether to show legend').optional(),
});

export type ChartConfig = z.infer<typeof chartConfigSchema>;

export async function generateChartConfig(data: any[], query: string): Promise<ChartConfig> {
  try {
    // Limit the data sample to avoid token limits
    const dataSample = data.slice(0, 10);

    const result = await generateObject({
      model: openai('gpt-4o-mini'),
      system: `You are a data visualization expert. Your task is to analyze SQL query results and determine the best chart type to visualize the data.

      For the chart configuration:
      - Choose the most appropriate chart type (bar, line, pie, area, card) based on the data and query
      - Bar charts are good for comparing values across categories
      - Line charts are good for showing trends over time or sequences
      - Pie charts are good for showing proportions of a whole (limit to 7 categories max)
      - Area charts are good for showing cumulative totals over time
      - Card is good for displaying a single value or KPI

      - Select appropriate x-axis (usually categorical or time-based) and y-axis (usually numerical) fields
      - Provide a clear, concise title that explains what the chart shows
      - Include a brief description of the insights from the chart`,

      prompt: `Generate a chart configuration for the following SQL query results.

      User Query: "${query}"

      Data Sample (first ${dataSample.length} rows):
      ${JSON.stringify(dataSample, null, 2)}

      Full Dataset Size: ${data.length} rows

      Available Columns: ${Object.keys(data[0] || {}).join(', ')}

      Return a chart configuration that best visualizes this data to answer the user's query.`,

      schema: chartConfigSchema,
    });

    return result.object;
  } catch (error) {
    console.error('Error generating chart configuration:', error);
    throw new Error('Failed to generate chart configuration');
  }
}
