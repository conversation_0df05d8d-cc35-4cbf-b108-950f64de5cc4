'use server';


import { Resend } from 'resend';
import { AdminNotificationEmail } from '@/components/emails/AdminNotificationEmail';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * Sends a notification email to the admin about a new user sign-up
 */
export async function sendAdminNotification(userData: {
  email: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  phone: string;
}) {
  try {
    // Log the notification for debugging
    console.log('Admin notification for new user sign-up:');
    console.log('- Name:', `${userData.firstName} ${userData.lastName}`);
    console.log('- Email:', userData.email);
    console.log('- Phone:', userData.phone);
    console.log('- Organization:', userData.organizationName);

    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: 'Wealthie <<EMAIL>>',
      to: ['<EMAIL>'], // Admin email address
      subject: 'New User Sign-Up',
      react: AdminNotificationEmail({
        userData,
        siteUrl
      }),
      headers: {
        'X-Entity-Ref-ID': `signup-${userData.email}-${Date.now()}`,
        'List-Unsubscribe': '<mailto:<EMAIL>>',
      },
    });

    if (error) {
      console.error('Error sending admin notification email:', error);
      return { success: false, error };
    }

    console.log('Admin notification email sent:', data?.id);
    return { success: true, messageId: data?.id };
  } catch (error) {
    console.error('Error sending admin notification:', error);
    return { success: false, error };
  }
}
