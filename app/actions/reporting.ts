'use server';

import { createClient } from '@/utils/supabase/server';
import { generateObject } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { SQL_GENERATION_SYSTEM_PROMPT, SQL_EXPLANATION_SYSTEM_PROMPT } from '@/lib/db/schema-description';

// Define the schema for SQL query generation
const sqlQuerySchema = z.object({
  query: z.string().describe('The SQL query to execute'),
});

// Define the schema for SQL query explanation
const sqlExplanationSchema = z.object({
  explanation: z.string().describe('Explanation of the SQL query in plain English'),
});

// Define the schema for SQL query results
export type SQLQueryResult = {
  data: any[] | null;
  error: string | null;
  query: string;
  explanation: string | null;
};

/**
 * Generates a SQL query from a natural language question
 */
export async function generateSQLQuery(input: string): Promise<string> {
  try {
    const result = await generateObject({
      model: openai('gpt-4o-mini'),
      system: SQL_GENERATION_SYSTEM_PROMPT,
      prompt: `Generate the SQL query necessary to answer this question: ${input}`,
      schema: sqlQuerySchema,
    });

    return result.object.query;
  } catch (error) {
    console.error('Error generating SQL query:', error);
    throw new Error('Failed to generate SQL query');
  }
}

/**
 * Explains a SQL query in plain English
 */
export async function explainSQLQuery(query: string): Promise<string> {
  try {
    const result = await generateObject({
      model: openai('gpt-4o-mini'),
      system: SQL_EXPLANATION_SYSTEM_PROMPT,
      prompt: `Explain this SQL query in simple terms: ${query}`,
      schema: sqlExplanationSchema,
    });

    return result.object.explanation;
  } catch (error) {
    console.error('Error explaining SQL query:', error);
    return 'Could not generate explanation for this query.';
  }
}

/**
 * Executes a SQL query against the Supabase database
 */
export async function executeSQLQuery(query: string): Promise<{ data: any[] | null; error: string | null }> {
  const supabase = createClient();

  try {
    // Remove any semicolons from the end of the query
    const cleanedQuery = query.trim().replace(/;+$/, '');

    console.log('Executing SQL query:', cleanedQuery);

    // Execute the query
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: cleanedQuery });

    if (error) {
      console.error('Error executing SQL query:', error);
      return { data: null, error: error.message };
    }

    // If data is null but there's no error, return an empty array
    if (data === null) {
      return { data: [], error: null };
    }

    return { data, error: null };
  } catch (error: any) {
    console.error('Error executing SQL query:', error);
    return { data: null, error: error.message || 'An unknown error occurred' };
  }
}

/**
 * Processes a natural language question, generates a SQL query, executes it, and returns the results
 */
export async function processNaturalLanguageQuery(input: string): Promise<SQLQueryResult> {
  try {
    // Generate SQL query
    const query = await generateSQLQuery(input);

    // Execute the query
    const { data, error } = await executeSQLQuery(query);

    // Generate explanation if query was successful
    const explanation = error ? null : await explainSQLQuery(query);

    return {
      data,
      error,
      query,
      explanation,
    };
  } catch (error: any) {
    console.error('Error processing natural language query:', error);
    return {
      data: null,
      error: error.message || 'An unknown error occurred',
      query: '',
      explanation: null,
    };
  }
}
