import { FinancialData } from '@/app/utils/financialTypes';
const Groq = require('groq-sdk');

const groq = new Groq({
  apiKey: '********************************************************',
  dangerouslyAllowBrowser: true
});

export async function optimizeScenario(inputData: FinancialData): Promise<FinancialData> {
  const prompt = generatePrompt(inputData);

  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.7,
      max_tokens: 1024,
      top_p: 1,
      stream: false,
      stop: null
    });

    const response = chatCompletion.choices[0]?.message?.content || '';
    const optimizedData = parseAIResponse(response);

    return optimizedData;
  } catch (error) {
    console.error('Error in AI optimization:', error);
    throw error;
  }
}

function generatePrompt(inputData: FinancialData): string {
  return `
    Given the following financial scenario:
    ${JSON.stringify(inputData, null, 2)}

    Please analyze this scenario and suggest optimizations to increase the chance of success above 80%. 
    Consider adjusting parameters such as savings rate, investment strategy, retirement age, and expenses.
    Provide specific numerical recommendations for each parameter you suggest changing.
    Format your response as a JSON object with the same structure as the input data, including only the parameters that should be changed.
  `;
}

function parseAIResponse(response: string): FinancialData {
  try {
    const parsedResponse = JSON.parse(response);
    return { ...parsedResponse };
  } catch (error) {
    console.error('Error parsing AI response:', error);
    throw new Error('Invalid AI response format');
  }
}

export async function generateScenarioSummary(inputData: FinancialData, metrics: any[]): Promise<string> {
  console.log("Starting generateScenarioSummary");
  const prompt = generateSummaryPrompt(inputData, metrics);
  console.log("Generated prompt:", prompt);

  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are a financial analysis expert. Provide clear, concise summaries of financial scenarios."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "mixtral-8x7b-32768",
      temperature: 0.3,
      max_tokens: 4096,
      top_p: 1,
      stream: false,
    });

    console.log("AI Response:", chatCompletion.choices[0]?.message?.content);
    return chatCompletion.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('Error in AI summary generation:', error);
    throw error;
  }
}

function generateSummaryPrompt(inputData: FinancialData, metrics: any[]): string {
  if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
    throw new Error('No metrics data available for summary generation');
  }

  const lastMetric = metrics[metrics.length - 1];
  const retirementMetrics = metrics.find(m => m.Age === inputData.ending_age);
  
  return `
    Please analyze this financial scenario and provide a clear, concise summary (around 250 words).
    
    Key Input Data:
    - Starting age: ${inputData.starting_age}
    - Retirement/Ending age: ${inputData.ending_age}
    - Current annual income: ${inputData.annual_income}
    - Partner annual income: ${inputData.partner_annual_income || 'N/A'}
    - Initial savings: ${inputData.savings_amount}
    - Initial investments: ${inputData.initial_investment}
    - Property value: ${inputData.property_value || 'N/A'}
    - Property debt: ${inputData.debt || 'N/A'}
    - Additional incomes: ${JSON.stringify(inputData.additional_incomes || [])}
    - Additional expenses: ${JSON.stringify(inputData.additional_expenses || [])}
    
    Scenario Results:
    - Final net wealth: ${lastMetric?.['Net Wealth'] || 'N/A'}
    - Retirement net wealth: ${retirementMetrics?.['Net Wealth'] || 'N/A'}
    - Investment growth pattern: ${metrics?.map(m => m['Investments Fund'])?.join(', ') || 'N/A'}
    - KiwiSaver final balance: ${lastMetric?.['Total KiwiSaver'] || 'N/A'}
    - Property final value: ${lastMetric?.['Property Value'] || 'N/A'}
    - Final debt value: ${lastMetric?.['Debt Value'] || 'N/A'}

    Please provide a summary that:
    1. Outlines the key aspects of the scenario (ages, income sources, savings strategy)
    2. Highlights any significant additional incomes or expenses
    3. Analyzes the wealth progression
    4. Evaluates retirement readiness
    5. Identifies potential risks or areas of concern
    6. Mentions final wealth outcomes
    
    Format the response in clear paragraphs without bullet points or technical jargon.
  `;
}
