import { Groq } from "groq-sdk";
import { FinancialData, FinancialMetrics } from "@/app/utils/financialTypes";
import { OptimizationSettings } from "@/components/modals/OptimizationSettingsModal";

const groq = new Groq({
  apiKey: '********************************************************',
  dangerouslyAllowBrowser: true
});

export interface OptimizationSuggestion {
  parameter: string;
  currentValue: number;
  suggestedValue: number;
  impact: string;
  priority: number;
}

// Create a mapping between AI parameters and settings fields
const parameterToSettingsMap = {
  'Starting Age': ['personal', 'startingAge', 'starting_age'],
  'Ending Age': ['personal', 'endingAge', 'ending_age'],
  'Annual Income': ['income', 'annualIncome', 'annual_income'],
  'Partner Annual Income': ['income', 'partnerIncome', 'partner_annual_income'],
  'Superannuation Rate': ['income', 'superannuation', 'superannuation'],
  'Savings Amount': ['savings', 'savingsAmount', 'savings_amount'],
  'Cash Reserve': ['savings', 'cashReserve', 'cash_reserve'],
  'Initial Investment': ['investment', 'initialInvestment', 'initial_investment'],
  'Investment Return': ['investment', 'annualReturn', 'annual_investment_return'],
  'Initial KiwiSaver': ['kiwiSaver', 'initialKiwiSaver', 'initial_kiwisaver'],
  'KiwiSaver Contribution': ['kiwiSaver', 'contribution', 'kiwisaver_contribution'],
  'Employer Contribution': ['kiwiSaver', 'employerContribution', 'employer_contribution'],
  'KiwiSaver Return': ['kiwiSaver', 'annualReturn', 'annual_kiwisaver_return'],
  'Property Value': ['property', 'propertyValue', 'property_value'],
  'Property Debt': ['property', 'debt', 'debt'],
  'Property Growth Rate': ['property', 'propertyGrowth', 'property_growth'],
  'Debt Interest Rate': ['property', 'debtInterestRate', 'debt_ir'],
  'Initial Debt Years': ['property', 'initialDebtYears', 'initial_debt_years'],
  'Additional Debt Repayments': ['property', 'additionalDebtRepayments', 'additional_debt_repayments'],
} as const;

// Add this function to get the current value from inputData
function getCurrentValue(parameter: string, inputData: FinancialData): number {
  const mapping = parameterToSettingsMap[parameter as keyof typeof parameterToSettingsMap];
  if (!mapping) return 0;
  const [_, __, dataKey] = mapping;
  return inputData[dataKey as keyof FinancialData] as number;
}

export async function optimizeScenario(
  inputData: FinancialData, 
  metrics: FinancialMetrics[], 
  currentSuccessRate: number,
  settings: OptimizationSettings
): Promise<OptimizationSuggestion[]> {
  const prompt = generateOptimizationPrompt(inputData, metrics, currentSuccessRate, settings);

  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are a financial optimization expert. Analyze the scenario and suggest realistic adjustments to improve the chance of success to the target rate."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "llama3-8b-8192",
      temperature: 0.3,
      max_tokens: 4096,
      top_p: 1,
      stream: false,
    });

    const suggestions = parseOptimizationResponse(chatCompletion.choices[0]?.message?.content || '');
    return filterSuggestionsBySettings(suggestions, settings);
  } catch (error) {
    console.error('Error in optimization generation:', error);
    throw error;
  }
}

function generateOptimizationPrompt(
  inputData: FinancialData, 
  metrics: FinancialMetrics[],
  currentSuccessRate: number,
  settings: OptimizationSettings
): string {
  const allowedParameters = Object.entries(parameterToSettingsMap)
    .filter(([_, [section, field]]) => {
      const sectionData = settings[section as keyof typeof settings];
      if (typeof sectionData === 'number') return false;
      return sectionData.selected && sectionData.fields[field as keyof typeof sectionData.fields];
    })
    .map(([param]) => ({
      name: param,
      currentValue: getCurrentValue(param, inputData)
    }));

  return `
    Analyze this financial scenario and suggest optimizations to achieve a ${settings.targetSuccessRate}% chance of success.
    Current success rate: ${currentSuccessRate}%

    Parameters that can be modified:
    ${allowedParameters.map(p => `${p.name}: Current value = ${p.currentValue}`).join('\n')}

    Please provide optimization suggestions in the following JSON format:
    [
      {
        "parameter": "parameter_name",
        "currentValue": current_number,
        "suggestedValue": suggested_number,
        "impact": "Brief explanation of the impact",
        "priority": priority_number
      }
    ]

    Rules for suggestions:
    1. ONLY suggest changes for the parameters listed above
    2. Suggest realistic changes (no more than 20% change for most parameters)
    3. Prioritize high-impact, achievable changes
    4. Consider the relationship between different parameters
    5. Focus on reaching the target success rate of ${settings.targetSuccessRate}%
    `;
}

function filterSuggestionsBySettings(
  suggestions: OptimizationSuggestion[], 
  settings: OptimizationSettings
): OptimizationSuggestion[] {
  return suggestions.filter(suggestion => {
    const mappedParam = parameterToSettingsMap[suggestion.parameter as keyof typeof parameterToSettingsMap];
    if (!mappedParam) return false;

    const [section, field] = mappedParam;
    const sectionData = settings[section as keyof typeof settings];
    if (typeof sectionData !== 'object' || sectionData === null) return false;
    return sectionData.selected && sectionData.fields[field as keyof typeof sectionData.fields];
  });
}

function parseOptimizationResponse(response: string): OptimizationSuggestion[] {
  try {
    // Find the JSON array in the response using regex
    const jsonMatch = response.match(/\[[\s\S]*\]/);
    if (!jsonMatch) return [];
    
    const suggestions = JSON.parse(jsonMatch[0]);
    return suggestions.filter((s: OptimizationSuggestion) => 
      s.parameter && 
      typeof s.currentValue === 'number' && 
      typeof s.suggestedValue === 'number' &&
      typeof s.priority === 'number'
    );
  } catch (error) {
    console.error('Error parsing optimization response:', error);
    return [];
  }
}