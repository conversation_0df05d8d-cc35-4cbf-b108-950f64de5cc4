
import { random } from 'lodash';
import { FinancialData, FinancialMetrics } from './financialTypes';

import { mulberry32, normalRandom, getPercentile, calculateMonthlyPayment } from '@/app/utils/PreCalculations';


export function financialLoop(data: FinancialData, random: () => number) {


        // Number of Monte Carlo simulations
        const totalScenarios = data.num_simulations || 1000; // Use input or default to 1000
        const confidenceInterval = data.confidence_interval || 95; // Use input confidence interval or default to 95%

        // Arrays to store net wealth at ending age for all scenarios
        const netWealthAtEndAge: number[] = [];

        // Arrays to accumulate metrics over all scenarios
        let cumulativeMetrics: FinancialMetrics[] = [];

        // Initialize min and max net wealth arrays
        let minNetWealthAtAge: number[] = [];
        let maxNetWealthAtAge: number[] = [];

        const years = Array.from({ length: data.ending_age - data.starting_age + 1 }, (_, i) => data.starting_age + i);
        const netWealthAtAges: number[][] = Array.from({ length: years.length }, () => []);

        // Initialize arrays to store all scenario values for each metric at each age
        const metricArrays = Array.from({ length: years.length }, () => ({
          'Savings Fund': [] as number[],
          'Gross Income': [] as number[],
          'Net Income': [] as number[],
          'Total Expenditure': [] as number[],
          'Additional Expenditure': [] as number[],
          'Net Wealth': [] as number[],
          'Net Cashflow': [] as number[],
          'Total Withdrawals': [] as number[],
          'Investments Fund': [] as number[],
          'Investment Fund 1': [] as number[],
          'Investment Fund 2': [] as number[],
          'Investment Fund 3': [] as number[],
          'Investment Fund 4': [] as number[],
          'Investment Fund 5': [] as number[],
          'Total KiwiSaver': [] as number[],
          'Main KiwiSaver': [] as number[],
          'Partner KiwiSaver': [] as number[],
          'Fund 1 Income Portion': [] as number[],
          'Fund 2 Income Portion': [] as number[],
          'Fund 3 Income Portion': [] as number[],
          'Fund 4 Income Portion': [] as number[],
          'Fund 5 Income Portion': [] as number[],
          'KiwiSaver Income Portion': [] as number[],
          'Partner KiwiSaver Income Portion': [] as number[],
          'Annual Investment Return': [] as number[],
          'Annual Investment Return 1': [] as number[],
          'Annual Investment Return 2': [] as number[],
          'Annual Investment Return 3': [] as number[],
          'Annual Investment Return 4': [] as number[],
          'Annual Investment Return 5': [] as number[],
          'Annual KiwiSaver Return': [] as number[],
          'Main KiwiSaver Return': [] as number[],
          'Partner KiwiSaver Return': [] as number[],
          'Minimum Investment Return': [] as number[],
          'Maximum Investment Return': [] as number[],
          'Annual Investment Contribution': [] as number[],
          'Annual Investment Contribution 1': [] as number[],
          'Annual Investment Contribution 2': [] as number[],
          'Annual Investment Contribution 3': [] as number[],
          'Annual Investment Contribution 4': [] as number[],
          'Annual Investment Contribution 5': [] as number[],
          'Main Employee KiwiSaver': [] as number[],
          'Main Employer KiwiSaver': [] as number[],
          'Partner Employee KiwiSaver': [] as number[],
          'Partner Employer KiwiSaver': [] as number[],
          'Rental Income': [] as number[],
          'Rental Income 2': [] as number[],
          'Rental Income 3': [] as number[],
          'Rental Income 4': [] as number[],
          'Rental Income 5': [] as number[],
          'Board Income': [] as number[],
          'Board Income 2': [] as number[],
          'Board Income 3': [] as number[],
          'Board Income 4': [] as number[],
          'Board Income 5': [] as number[],
          'Property Value': [] as number[],
          'Property Value 2': [] as number[],
          'Property Value 3': [] as number[],
          'Property Value 4': [] as number[],
          'Property Value 5': [] as number[],
          'Debt Value': [] as number[],
          'Debt Value 2': [] as number[],
          'Debt Value 3': [] as number[],
          'Debt Value 4': [] as number[],
          'Debt Value 5': [] as number[],
          'Property Purchase': [] as number[],
          'Property Deposit': [] as number[],
          'Property Purchase 2': [] as number[],
          'Property Deposit 2': [] as number[],
          'Property Purchase 3': [] as number[],
          'Property Deposit 3': [] as number[],
          'Property Purchase 4': [] as number[],
          'Property Deposit 4': [] as number[],
          'Property Purchase 5': [] as number[],
          'Property Deposit 5': [] as number[],
          'Monthly Debt Repayment': [] as number[],
          'Monthly Debt Repayment 2': [] as number[],
          'Monthly Debt Repayment 3': [] as number[],
          'Monthly Debt Repayment 4': [] as number[],
          'Monthly Debt Repayment 5': [] as number[],
          'Annual Debt Repayments': [] as number[],
          'Annual Debt Repayments 2': [] as number[],
          'Annual Debt Repayments 3': [] as number[],
          'Annual Debt Repayments 4': [] as number[],
          'Annual Debt Repayments 5': [] as number[],
          'Annual Interest Payments': [] as number[],
          'Annual Interest Payments 2': [] as number[],
          'Annual Interest Payments 3': [] as number[],
          'Annual Interest Payments 4': [] as number[],
          'Annual Interest Payments 5': [] as number[],
          'Annual Principal Repayments': [] as number[],
          'Annual Principal Repayments 2': [] as number[],
          'Annual Principal Repayments 3': [] as number[],
          'Annual Principal Repayments 4': [] as number[],
          'Annual Principal Repayments 5': [] as number[],
          'Income Tax': [] as number[],
          'MTR Investment Tax': [] as number[],
          'PIE Investment tax': [] as number[],
          'Main KiwiSaver Tax': [] as number[],
          'Partner KiwiSaver Tax': [] as number[],
          'KiwiSaver Tax': [] as number[],
          'Fund 1 Tax': [] as number[],
          'Fund 2 Tax': [] as number[],
          'Fund 3 Tax': [] as number[],
          'Fund 4 Tax': [] as number[],
          'Fund 5 Tax': [] as number[],
          'KiwiSaver Contributions': [] as number[],
          'Partner KiwiSaver Contributions': [] as number[],
          'Superannuation': [] as number[],
          'Partner Income': [] as number[],
          'Main Income': [] as number[],
          'Main Income Tax': [] as number[],
          'Partner Income Tax': [] as number[],
          'Rental Income Tax': [] as number[],
          'Rental Income Tax 2': [] as number[],
          'Rental Income Tax 3': [] as number[],
          'Rental Income Tax 4': [] as number[],
          'Rental Income Tax 5': [] as number[],
          'Property Sale Proceeds': [] as number[],
          'Property Sale Proceeds 2': [] as number[],
          'Property Sale Proceeds 3': [] as number[],
          'Property Sale Proceeds 4': [] as number[],
          'Property Sale Proceeds 5': [] as number[],
          'Transaction Costs': [] as number[],
          'Transaction Costs 2': [] as number[],
          'Transaction Costs 3': [] as number[],
          'Transaction Costs 4': [] as number[],
          'Transaction Costs 5': [] as number[],
          'Debt Paid': [] as number[],
          'Debt Paid 2': [] as number[],
          'Debt Paid 3': [] as number[],
          'Debt Paid 4': [] as number[],
          'Debt Paid 5': [] as number[],
          'Lump Sum Payment Amount': [] as number[],
          'Lump Sum Payment Amount 2': [] as number[],
          'Lump Sum Payment Amount 3': [] as number[],
          'Lump Sum Payment Amount 4': [] as number[],
          'Lump Sum Payment Amount 5': [] as number[],
          'Basic Expenses 1': [] as number[],
          'Basic Expenses 2': [] as number[],
        }));
    for (let scenario = 0; scenario < totalScenarios; scenario++) {
        // Copy of the data for the current scenario
        let scenarioData = { ...data };

        // Process data
        const name = scenarioData.name || '';
        const partner_name = scenarioData.partner_name || '';
        let partner = false;
        // Check both the partner name and the includePartner flag
        if (partner_name !== '' && scenarioData.includePartner) {
        partner = true;
        }

        const starting_age = scenarioData.starting_age;
        const ending_age = scenarioData.ending_age || 100;
        if (starting_age == null || ending_age == null || starting_age >= ending_age) {
        throw new Error('Invalid starting_age or ending_age');
        }

        let partner_starting_age = scenarioData.partner_starting_age;
        if (partner) {
        if (partner_starting_age == null) {
            throw new Error('partner_starting_age is required for partner');
        }
        }

        let annual_income = scenarioData.annual_income || 0;
        if (typeof annual_income !== 'number' || annual_income < 0) {
        throw new Error('Invalid annual_income');
        }

        const income_period = scenarioData.income_period || [starting_age, 64];
        if (!Array.isArray(income_period) || income_period.length !== 2) {
        throw new Error('Invalid income_period');
        }

        let partner_annual_income = scenarioData.partner_annual_income || 0;
        let partner_income_period = scenarioData.partner_income_period || [partner_starting_age || 0, 60];
        if (partner) {
        if (partner_annual_income == null) {
            throw new Error('partner_annual_income is required for partner');
        }
        if (!Array.isArray(partner_income_period) || partner_income_period.length !== 2) {
            throw new Error('Invalid partner_income_period');
        }
        }

        const superannuation = scenarioData.superannuation != null ? Boolean(scenarioData.superannuation) : true;

        const savings_amount = scenarioData.savings_amount || 0;
        const cash_reserve = scenarioData.cash_reserve || 0;

        let annual_expenses1 = scenarioData.annual_expenses1 || 40000;
        const expense_period1 = scenarioData.expense_period1 && Array.isArray(scenarioData.expense_period1) && scenarioData.expense_period1.length === 2
        ? scenarioData.expense_period1
        : [starting_age, 64];

        if (!Array.isArray(expense_period1) || expense_period1.length !== 2 || expense_period1.some(value => value === null)) {
        throw new Error('Invalid expense_period1');
        }

        const second_expense = scenarioData.second_expense != null ? Boolean(scenarioData.second_expense) : false;
        let annual_expenses2 = 0;
        let expense_period2: [number, number] = [0, 0];
        if (second_expense) {
        annual_expenses2 = scenarioData.annual_expenses2 || 60000;
        expense_period2 = scenarioData.expense_period2 && Array.isArray(scenarioData.expense_period2) && scenarioData.expense_period2.length === 2
            ? scenarioData.expense_period2
            : [65, ending_age];
        if (!Array.isArray(expense_period2) || expense_period2.length !== 2 || expense_period2.some(value => value === null)) {
            throw new Error('Invalid expense_period2');
        }
        }

        const additional_incomes_data = scenarioData.additional_incomes || [];
        const additional_expenses_data = scenarioData.additional_expenses || [];

        // Initialize investment bucket parameters
        // Bucket 1
        const initial_investment1 = scenarioData.initial_investment1 || 0;
        let annual_investment_contribution1 = scenarioData.annual_investment_contribution1 !== undefined ? scenarioData.annual_investment_contribution1 : 0;
        const annual_investment_return1 = scenarioData.annual_investment_return1 || 5.0;
        const inv_std_dev1 = scenarioData.inv_std_dev1 || 0.0;

        // Bucket 2
        const initial_investment2 = scenarioData.initial_investment2 || 0;
        let annual_investment_contribution2 = scenarioData.annual_investment_contribution2 !== undefined ? scenarioData.annual_investment_contribution2 : 0;
        const annual_investment_return2 = scenarioData.annual_investment_return2 || 5.0;
        const inv_std_dev2 = scenarioData.inv_std_dev2 || 0.0;

        // Bucket 3
        const initial_investment3 = scenarioData.initial_investment3 || 0;
        let annual_investment_contribution3 = scenarioData.annual_investment_contribution3 !== undefined ? scenarioData.annual_investment_contribution3 : 0;
        const annual_investment_return3 = scenarioData.annual_investment_return3 || 5.0;
        const inv_std_dev3 = scenarioData.inv_std_dev3 || 0.0;

        // Bucket 4
        const initial_investment4 = scenarioData.initial_investment4 || 0;
        let annual_investment_contribution4 = scenarioData.annual_investment_contribution4 !== undefined ? scenarioData.annual_investment_contribution4 : 0;
        const annual_investment_return4 = scenarioData.annual_investment_return4 || 5.0;
        const inv_std_dev4 = scenarioData.inv_std_dev4 || 0.0;

        // Bucket 5
        const initial_investment5 = scenarioData.initial_investment5 || 0;
        let annual_investment_contribution5 = scenarioData.annual_investment_contribution5 !== undefined ? scenarioData.annual_investment_contribution5 : 0;
        const annual_investment_return5 = scenarioData.annual_investment_return5 || 5.0;
        const inv_std_dev5 = scenarioData.inv_std_dev5 || 0.0;

        // Investment allocation percentages (default to equal distribution if not specified)
        const investment_allocation1 = scenarioData.investment_allocation1 !== undefined ? scenarioData.investment_allocation1 : 20;
        const investment_allocation2 = scenarioData.investment_allocation2 !== undefined ? scenarioData.investment_allocation2 : 20;
        const investment_allocation3 = scenarioData.investment_allocation3 !== undefined ? scenarioData.investment_allocation3 : 20;
        const investment_allocation4 = scenarioData.investment_allocation4 !== undefined ? scenarioData.investment_allocation4 : 20;
        const investment_allocation5 = scenarioData.investment_allocation5 !== undefined ? scenarioData.investment_allocation5 : 20;

        // Legacy parameters (for backward compatibility)
        const initial_investment = scenarioData.initial_investment;
        let annual_investment_contribution = scenarioData.annual_investment_contribution !== undefined ? scenarioData.annual_investment_contribution : 5200;
        const annual_investment_return = scenarioData.annual_investment_return || 5.0;
        const inv_std_dev = scenarioData.inv_std_dev || 0.0;
        const investment_return_period = scenarioData.investment_return_period || [starting_age, ending_age];
        const inv_tax = scenarioData.inv_tax || 'PIE';

        const initial_kiwiSaver = scenarioData.initial_kiwiSaver || 0;
        let kiwisaver_contribution = scenarioData.kiwisaver_contribution !== undefined ? scenarioData.kiwisaver_contribution : 3;
        let employer_contribution = scenarioData.employer_contribution !== undefined ? scenarioData.employer_contribution : 3;
        const annual_kiwisaver_return = scenarioData.annual_kiwisaver_return || 3.0;
        const ks_std_dev = scenarioData.ks_std_dev || 0.0;

        let partner_initial_kiwisaver = 0;
        let partner_kiwisaver_contribution = 0;
        let partner_employer_contribution = 0;
        const partner_annual_kiwisaver_return = scenarioData.partner_annual_kiwisaver_return || 5.0;
        const partner_ks_std_dev = scenarioData.partner_ks_std_dev || 0.0;

        if (partner) {
        partner_initial_kiwisaver = scenarioData.partner_initial_kiwisaver || 0;
        partner_kiwisaver_contribution = scenarioData.partner_kiwisaver_contribution !== undefined ? scenarioData.partner_kiwisaver_contribution : 3;
        partner_employer_contribution = scenarioData.partner_employer_contribution !== undefined ? scenarioData.partner_employer_contribution : 3;
        }

        // Property values and debt are initialized later
        const property_growth = scenarioData.property_growth || 0;
        const property_growth2 = scenarioData.property_growth2 || 0;
        const property_growth3 = scenarioData.property_growth3 || 0;
        const property_growth4 = scenarioData.property_growth4 || 0;
        const property_growth5 = scenarioData.property_growth5 || 0;
        const debt_ir = scenarioData.debt_ir || 0.0;
        const debt_ir2 = scenarioData.debt_ir2 || 0.0;
        const debt_ir3 = scenarioData.debt_ir3 || 0.0;
        const debt_ir4 = scenarioData.debt_ir4 || 0.0;
        const debt_ir5 = scenarioData.debt_ir5 || 0.0;
        let initial_debt_years = scenarioData.initial_debt_years || 30;
        let initial_debt_years2 = scenarioData.initial_debt_years2 || 30;
        let initial_debt_years3 = scenarioData.initial_debt_years3 || 30;
        let initial_debt_years4 = scenarioData.initial_debt_years4 || 30;
        let initial_debt_years5 = scenarioData.initial_debt_years5 || 30;
        const additional_debt_repayments = scenarioData.additional_debt_repayments || 0;
        const additional_debt_repayments_start_age = scenarioData.additional_debt_repayments_start_age || starting_age;
        const additional_debt_repayments_end_age = scenarioData.additional_debt_repayments_end_age || ending_age;

        const additional_debt_repayments2 = scenarioData.additional_debt_repayments2 || 0;
        const additional_debt_repayments_start_age2 = scenarioData.additional_debt_repayments_start_age2 || starting_age;
        const additional_debt_repayments_end_age2 = scenarioData.additional_debt_repayments_end_age2 || ending_age;

        const additional_debt_repayments3 = scenarioData.additional_debt_repayments3 || 0;
        const additional_debt_repayments_start_age3 = scenarioData.additional_debt_repayments_start_age3 || starting_age;
        const additional_debt_repayments_end_age3 = scenarioData.additional_debt_repayments_end_age3 || ending_age;

        const additional_debt_repayments4 = scenarioData.additional_debt_repayments4 || 0;
        const additional_debt_repayments_start_age4 = scenarioData.additional_debt_repayments_start_age4 || starting_age;
        const additional_debt_repayments_end_age4 = scenarioData.additional_debt_repayments_end_age4 || ending_age;

        const additional_debt_repayments5 = scenarioData.additional_debt_repayments5 || 0;
        const additional_debt_repayments_start_age5 = scenarioData.additional_debt_repayments_start_age5 || starting_age;
        const additional_debt_repayments_end_age5 = scenarioData.additional_debt_repayments_end_age5 || ending_age;
        const include_property_debt = scenarioData.include_property_debt != null ? Boolean(scenarioData.include_property_debt) : false;
        const include_property_debt2 = scenarioData.include_property_debt2 != null ? Boolean(scenarioData.include_property_debt2) : false;
        const include_property_debt3 = scenarioData.include_property_debt3 != null ? Boolean(scenarioData.include_property_debt3) : false;
        const include_property_debt4 = scenarioData.include_property_debt4 != null ? Boolean(scenarioData.include_property_debt4) : false;
        const include_property_debt5 = scenarioData.include_property_debt5 != null ? Boolean(scenarioData.include_property_debt5) : false;

        const inflation_rate = scenarioData.inflation_rate || 0.0;
        const main_income_inflation_rate = scenarioData.main_income_inflation_rate !== undefined ? scenarioData.main_income_inflation_rate : inflation_rate;
        const partner_income_inflation_rate = scenarioData.partner_income_inflation_rate !== undefined ? scenarioData.partner_income_inflation_rate : inflation_rate;
        const expense1_inflation_rate = scenarioData.expense1_inflation_rate !== undefined ? scenarioData.expense1_inflation_rate : inflation_rate;
        const expense2_inflation_rate = scenarioData.expense2_inflation_rate !== undefined ? scenarioData.expense2_inflation_rate : inflation_rate;

        const sell_main_property = scenarioData.sell_main_property != null ? Boolean(scenarioData.sell_main_property) : false;
        const sell_main_property2 = scenarioData.sell_main_property2 != null ? Boolean(scenarioData.sell_main_property2) : false;
        const sell_main_property3 = scenarioData.sell_main_property3 != null ? Boolean(scenarioData.sell_main_property3) : false;
        const sell_main_property4 = scenarioData.sell_main_property4 != null ? Boolean(scenarioData.sell_main_property4) : false;
        const sell_main_property5 = scenarioData.sell_main_property5 != null ? Boolean(scenarioData.sell_main_property5) : false;
        let main_property_sale_age = 0;
        let main_property_sale_age2 = 0;
        let main_property_sale_age3 = 0;
        let main_property_sale_age4 = 0;
        let main_property_sale_age5 = 0;
        let main_prop_sale_value = 0;
        let main_prop_sale_value2 = 0;
        let main_prop_sale_value3 = 0;
        let main_prop_sale_value4 = 0;
        let main_prop_sale_value5 = 0;
        let pay_off_debt = false;
        let pay_off_debt2 = false;
        let pay_off_debt3 = false;
        let pay_off_debt4 = false;
        let pay_off_debt5 = false;
        if (sell_main_property) {
        main_property_sale_age = scenarioData.main_property_sale_age || 0;
        main_prop_sale_value = scenarioData.main_prop_sale_value || 0;
        pay_off_debt = scenarioData.pay_off_debt != null ? Boolean(scenarioData.pay_off_debt) : true;
        }
        if (sell_main_property2) {
        main_property_sale_age2 = scenarioData.main_property_sale_age2 || 0;
        main_prop_sale_value2 = scenarioData.main_prop_sale_value2 || 0;
        pay_off_debt2 = scenarioData.pay_off_debt2 != null ? Boolean(scenarioData.pay_off_debt2) : true;
        }
        if (sell_main_property3) {
        main_property_sale_age3 = scenarioData.main_property_sale_age3 || 0;
        main_prop_sale_value3 = scenarioData.main_prop_sale_value3 || 0;
        pay_off_debt3 = scenarioData.pay_off_debt3 != null ? Boolean(scenarioData.pay_off_debt3) : true;
        }
        if (sell_main_property4) {
        main_property_sale_age4 = scenarioData.main_property_sale_age4 || 0;
        main_prop_sale_value4 = scenarioData.main_prop_sale_value4 || 0;
        pay_off_debt4 = scenarioData.pay_off_debt4 != null ? Boolean(scenarioData.pay_off_debt4) : true;
        }
        if (sell_main_property5) {
        main_property_sale_age5 = scenarioData.main_property_sale_age5 || 0;
        main_prop_sale_value5 = scenarioData.main_prop_sale_value5 || 0;
        pay_off_debt5 = scenarioData.pay_off_debt5 != null ? Boolean(scenarioData.pay_off_debt5) : true;
        }

        // Process additional incomes
        const additional_incomes: Array<{ title: string; value: number; period: [number, number]; tax_type: string; inflation_rate?: number }> = [];
        for (const income of additional_incomes_data) {
        const title = income.title || 'Additional Income';
        const value = income.value || 0;
        const period = income.period || [starting_age + 10, starting_age + 11];
        const tax_type = income.tax_type || 'main';
        const incomeInflationRate = income.inflation_rate !== undefined ? income.inflation_rate : scenarioData.inflation_rate || 0.0;
        additional_incomes.push({ title, value, period, tax_type, inflation_rate: incomeInflationRate });
        }

        // Process additional expenses
        const additional_expenses: Array<{ title: string; value: number; period: [number, number]; frequency: number }> = [];
        for (const expense of additional_expenses_data) {
        const title = expense.title || '';
        const value = expense.value || 0;
        const period = expense.period || [starting_age, starting_age + 1];
        const frequency = expense.frequency || 1;
        additional_expenses.push({ title, value, period, frequency });
        }

        // Process one-off investments
        const one_off_investments: Array<any> = [];
        if (scenarioData.one_off_investments) {
            // Just use the original array directly without modifying it
            // This ensures all properties are preserved
            one_off_investments.push(...scenarioData.one_off_investments);
        }

        // Now proceed with calculations

        // Create lists to store the data over time for this scenario
        const years: number[] = [];
        for (let y = starting_age; y <= ending_age; y++) {
        years.push(y);
        }

        // Initialize all the arrays for storing financial metrics
        const savings: number[] = [];
        const net_incomes: number[] = [];
        const gross_income: number[] = [];
        const annual_tax: number[] = [];
        const net_wealth: number[] = [];
        const yearly_savings: number[] = [];
        const additional_expenditure: number[] = [];
        const total_expenditure: number[] = [];
        const total_drawing: number[] = [];

        // Investment buckets
        const investments: number[] = [];  // Total investments (sum of all buckets)
        const investments1: number[] = []; // Investment bucket 1
        const investments2: number[] = []; // Investment bucket 2
        const investments3: number[] = []; // Investment bucket 3
        const investments4: number[] = []; // Investment bucket 4
        const investments5: number[] = []; // Investment bucket 5

        const basic_expenses1_list: number[] = [];
        const basic_expenses2_list: number[] = [];
        const kiwisaver: number[] = [];
        const total_kiwisaver: number[] = [];
        const main_kiwisaver: number[] = [];
        const partner_kiwisaver: number[] = [];

        // Investment returns
        const investment_return_list: number[] = [];  // Total investment return
        const investment_return_list1: number[] = []; // Investment bucket 1 return
        const investment_return_list2: number[] = []; // Investment bucket 2 return
        const investment_return_list3: number[] = []; // Investment bucket 3 return
        const investment_return_list4: number[] = []; // Investment bucket 4 return
        const investment_return_list5: number[] = []; // Investment bucket 5 return

        const kiwisaver_return_list: number[] = [];
        const main_kiwisaver_return_list: number[] = [];
        const partner_kiwisaver_return_list: number[] = [];

        // Min/max investment values
        const min_investment: number[] = [];  // Min total investment
        const max_investment: number[] = [];  // Max total investment
        const min_investment1: number[] = []; // Min investment bucket 1
        const max_investment1: number[] = []; // Max investment bucket 1
        const min_investment2: number[] = []; // Min investment bucket 2
        const max_investment2: number[] = []; // Max investment bucket 2
        const min_investment3: number[] = []; // Min investment bucket 3
        const max_investment3: number[] = []; // Max investment bucket 3
        const min_investment4: number[] = []; // Min investment bucket 4
        const max_investment4: number[] = []; // Max investment bucket 4
        const min_investment5: number[] = []; // Min investment bucket 5
        const max_investment5: number[] = []; // Max investment bucket 5

        // Investment contributions
        const yearly_investment_contribution_list: number[] = [];  // Total contribution
        const yearly_investment_contribution_list1: number[] = []; // Bucket 1 contribution
        const yearly_investment_contribution_list2: number[] = []; // Bucket 2 contribution
        const yearly_investment_contribution_list3: number[] = []; // Bucket 3 contribution
        const yearly_investment_contribution_list4: number[] = []; // Bucket 4 contribution
        const yearly_investment_contribution_list5: number[] = []; // Bucket 5 contribution

        const pie_tax: number[] = [];
        const mtr_tax: number[] = [];
        const main_ks_tax: number[] = [];
        const partner_ks_tax: number[] = [];
        const ks_tax: number[] = [];
        const yearly_kiwisaver_contribution: number[] = [];
        const partner_yearly_kiwisaver_contribution: number[] = [];

        // Individual fund tax arrays
        const fund1_tax: number[] = [];
        const fund2_tax: number[] = [];
        const fund3_tax: number[] = [];
        const fund4_tax: number[] = [];
        const fund5_tax: number[] = [];



        // Income portion tracking arrays
        const fund1_income_portion_list: number[] = [];
        const fund2_income_portion_list: number[] = [];
        const fund3_income_portion_list: number[] = [];
        const fund4_income_portion_list: number[] = [];
        const fund5_income_portion_list: number[] = [];
        const kiwisaver_income_portion_list: number[] = [];
        const partner_kiwisaver_income_portion_list: number[] = [];
        const property_values: number[] = [];
        const property_values2: number[] = [];
        const property_values3: number[] = [];
        const property_values4: number[] = [];
        const property_values5: number[] = [];
        const debt_values: number[] = [];
        const debt_values2: number[] = [];
        const debt_values3: number[] = [];
        const debt_values4: number[] = [];
        const debt_values5: number[] = [];
        const debt_repayment_values: number[] = [];
        const debt_repayment_values2: number[] = [];
        const debt_repayment_values3: number[] = [];
        const debt_repayment_values4: number[] = [];
        const debt_repayment_values5: number[] = [];
        const annual_debt_repayments: number[] = [];
        const annual_debt_repayments2: number[] = [];
        const annual_debt_repayments3: number[] = [];
        const annual_debt_repayments4: number[] = [];
        const annual_debt_repayments5: number[] = [];
        const debt_interest: number[] = [];
        const debt_interest2: number[] = [];
        const debt_interest3: number[] = [];
        const debt_interest4: number[] = [];
        const debt_interest5: number[] = [];
        const debt_principal_values: number[] = [];
        const debt_principal_values2: number[] = [];
        const debt_principal_values3: number[] = [];
        const debt_principal_values4: number[] = [];
        const debt_principal_values5: number[] = [];
        const kiwisaver_contributions: number[] = [];
        const partner_kiwisaver_contributions: number[] = [];
        const super_income_list: number[] = [];
        const partner_income_list: number[] = [];
        const main_income_list: number[] = [];
        const main_income_tax_list: number[] = [];
        const partner_income_tax_list: number[] = [];
        const rental_income_tax_list: number[] = [];
        const rental_income_tax_list2: number[] = [];
        const rental_income_tax_list3: number[] = [];
        const rental_income_tax_list4: number[] = [];
        const rental_income_tax_list5: number[] = [];
        const main_employee_kiwisaver: number[] = [];
        const main_employer_kiwisaver: number[] = [];
        const partner_employee_kiwisaver: number[] = [];
        const partner_employer_kiwisaver: number[] = [];
        const rental_income_list: number[] = [];
        const rental_income_list2: number[] = [];
        const rental_income_list3: number[] = [];
        const rental_income_list4: number[] = [];
        const rental_income_list5: number[] = [];
        const board_income_list: number[] = [];
        const board_income_list2: number[] = [];
        const board_income_list3: number[] = [];
        const board_income_list4: number[] = [];
        const board_income_list5: number[] = [];
        const lump_sum_payment_amount: number[] = [];
        const lump_sum_payment_amount2: number[] = [];
        const lump_sum_payment_amount3: number[] = [];
        const lump_sum_payment_amount4: number[] = [];
        const lump_sum_payment_amount5: number[] = [];

        // Initialize funds and variables
        let savings_fund = scenarioData.savings_amount || 0;

        // Get the withdrawal priorities (active funds)
        const withdrawalPriorities = (scenarioData as any).withdrawal_priorities || [1, 2, 3, 4, 5];

        // Initialize individual investment buckets - only funds in the priority list are active
        let investment_fund1 = withdrawalPriorities.includes(1) ? (initial_investment1 || 0) : 0;
        let investment_fund2 = withdrawalPriorities.includes(2) ? (initial_investment2 || 0) : 0;
        let investment_fund3 = withdrawalPriorities.includes(3) ? (initial_investment3 || 0) : 0;
        let investment_fund4 = withdrawalPriorities.includes(4) ? (initial_investment4 || 0) : 0;
        let investment_fund5 = withdrawalPriorities.includes(5) ? (initial_investment5 || 0) : 0;



        // If using legacy initial_investment, distribute it across buckets based on allocation percentages
        const legacyInvestment = initial_investment || 0;


        if (legacyInvestment > 0 &&
            investment_fund1 === 0 &&
            investment_fund2 === 0 &&
            investment_fund3 === 0 &&
            investment_fund4 === 0 &&
            investment_fund5 === 0) {

            // Distribute equally across all 5 funds
            investment_fund1 = legacyInvestment * 0.2;
            investment_fund2 = legacyInvestment * 0.2;
            investment_fund3 = legacyInvestment * 0.2;
            investment_fund4 = legacyInvestment * 0.2;
            investment_fund5 = legacyInvestment * 0.2;


        }

        // If only fund 1 has a value, and it's the same as initial_investment, distribute it
        if (legacyInvestment > 0 &&
            investment_fund1 === legacyInvestment &&
            investment_fund2 === 0 &&
            investment_fund3 === 0 &&
            investment_fund4 === 0 &&
            investment_fund5 === 0) {

            // Distribute equally across all 5 funds
            investment_fund1 = legacyInvestment * 0.2;
            investment_fund2 = legacyInvestment * 0.2;
            investment_fund3 = legacyInvestment * 0.2;
            investment_fund4 = legacyInvestment * 0.2;
            investment_fund5 = legacyInvestment * 0.2;

        }

        // Calculate total investment fund (sum of all buckets)
        let investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
        let min_investment_fund = investment_fund;
        let max_investment_fund = investment_fund;

        // Track min/max for each bucket
        let min_investment_fund1 = investment_fund1;
        let max_investment_fund1 = investment_fund1;
        let min_investment_fund2 = investment_fund2;
        let max_investment_fund2 = investment_fund2;
        let min_investment_fund3 = investment_fund3;
        let max_investment_fund3 = investment_fund3;
        let min_investment_fund4 = investment_fund4;
        let max_investment_fund4 = investment_fund4;
        let min_investment_fund5 = investment_fund5;
        let max_investment_fund5 = investment_fund5;

        let taxes = 0;
        let income = 0;
        let partner_income = 0;

        // Initialize property values and debt
        // If purchase details are provided and enabled, these will be set at the purchase age
        let property_value = (scenarioData.show_purchase_details && scenarioData.purchase_age) ? 0 : (scenarioData.property_value || 0);
        let property_value2 = (scenarioData.show_purchase_details2 && scenarioData.purchase_age2) ? 0 : (scenarioData.property_value2 || 0);
        let property_value3 = (scenarioData.show_purchase_details3 && scenarioData.purchase_age3) ? 0 : (scenarioData.property_value3 || 0);
        let property_value4 = (scenarioData.show_purchase_details4 && scenarioData.purchase_age4) ? 0 : (scenarioData.property_value4 || 0);
        let property_value5 = (scenarioData.show_purchase_details5 && scenarioData.purchase_age5) ? 0 : (scenarioData.property_value5 || 0);

        let debt_value = (scenarioData.show_purchase_details && scenarioData.purchase_age) ? 0 : (scenarioData.debt || 0);
        let debt_value2 = (scenarioData.show_purchase_details2 && scenarioData.purchase_age2) ? 0 : (scenarioData.debt2 || 0);
        let debt_value3 = (scenarioData.show_purchase_details3 && scenarioData.purchase_age3) ? 0 : (scenarioData.debt3 || 0);
        let debt_value4 = (scenarioData.show_purchase_details4 && scenarioData.purchase_age4) ? 0 : (scenarioData.debt4 || 0);
        let debt_value5 = (scenarioData.show_purchase_details5 && scenarioData.purchase_age5) ? 0 : (scenarioData.debt5 || 0);

        // Initialize debt years from the initial_debt_years values
        // These will be reset when a property is purchased
        let debt_years = scenarioData.initial_debt_years || 30;
        let debt_years2 = scenarioData.initial_debt_years2 || 30;
        let debt_years3 = scenarioData.initial_debt_years3 || 30;
        let debt_years4 = scenarioData.initial_debt_years4 || 30;
        let debt_years5 = scenarioData.initial_debt_years5 || 30;

        let partner_age = 0;
        let partner_kiwisaver_fund = 0;
        if (partner) {
        partner_age = partner_starting_age!;
        partner_kiwisaver_fund = scenarioData.partner_initial_kiwisaver || 0;
        }

        let kiwisaver_fund = scenarioData.initial_kiwiSaver || 0;
        let total_kiwisaver_fund = 0;

        const tax_brackets = [0.105, 0.175, 0.3, 0.33, 0.39];
        const tax_thresholds = [0, 14000, 48000, 70000, 180000];

        // Initialize min and max net wealth arrays if it's the first scenario
        if (scenario === 0) {
        minNetWealthAtAge = new Array(years.length).fill(Infinity);
        maxNetWealthAtAge = new Array(years.length).fill(-Infinity);
        }

        // Variables to track death, TPD, trauma, and redundancy events
        let mainPersonDeceased = false;
        let partnerDeceased = false;
        let mainPersonTPD = false;
        let partnerTPD = false;
        let mainPersonTrauma = false;
        let partnerTrauma = false;
        let mainTraumaEndAge = 0;
        let partnerTraumaEndAge = 0;
        let mainTraumaIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let partnerTraumaIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let traumaExpenseReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)

        // Redundancy tracking variables
        let mainPersonRedundant = false;
        let partnerRedundant = false;
        let mainRedundancyStartAge = 0;
        let partnerRedundancyStartAge = 0;
        let mainRedundancyEndAge = 0;
        let partnerRedundancyEndAge = 0;
        let mainRedundancyUnemploymentMonths = 0; // Total unemployment months
        let partnerRedundancyUnemploymentMonths = 0; // Total unemployment months
        let mainRedundancyIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let partnerRedundancyIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)

        // Recession tracking variables
        let recessionActive = false;
        let recessionStartAge = 0;
        let recessionEndAge = 0;
        let recessionReboundType = 'average'; // 'good', 'average', or 'bad'

        // Variables to track original and target fund values for recession recovery
        let recessionOriginalInvFund = 0;
        let recessionOriginalKsFund = 0;
        let recessionOriginalPartnerKsFund = 0;
        let recessionTargetInvFund = 0;
        let recessionTargetKsFund = 0;
        let recessionTargetPartnerKsFund = 0;

        // Maternity leave tracking variables
        let mainPersonMaternity = false;
        let partnerMaternity = false;
        let mainMaternityStartAge = 0;
        let partnerMaternityStartAge = 0;
        let mainMaternityEndAge = 0; // End of full maternity leave
        let partnerMaternityEndAge = 0; // End of full maternity leave
        let mainBackToWorkEndAge = 0; // End of back-to-work transition period
        let partnerBackToWorkEndAge = 0; // End of back-to-work transition period
        let mainMaternityLeaveMonths = 0; // Total maternity leave months
        let partnerMaternityLeaveMonths = 0; // Total maternity leave months
        let mainBackToWorkMonths = 0; // Total back-to-work transition months
        let partnerBackToWorkMonths = 0; // Total back-to-work transition months
        let mainMaternityIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let partnerMaternityIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let mainBackToWorkIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)
        let partnerBackToWorkIncomeReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)

        let currentYearInsurancePayout = 0; // Track insurance payouts for the current year
        let expenseReductionFactor = 1.0; // Default to no reduction (multiply by 1.0)

        // Function to check for redundancy events
        const checkForRedundancyEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for redundancy events at the current age that are enabled
            const redundancyEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'redundancy' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );

            // Process each redundancy event
            for (const event of redundancyEvents) {
                const person = event.person;
                const severancePay = event.severancePay || 0;
                const unemploymentPeriod = event.unemploymentPeriod || 0; // In months

                // Add severance pay to savings fund
                savings_fund += severancePay;
                // Calculate the end age for the redundancy effects
                // Convert months to years (fractional) and add to current age
                const unemploymentYears = unemploymentPeriod / 12;
                const redundancyEndAge = currentAge + unemploymentYears;

                // Apply income reduction based on the person affected
                if (person === 'main') {
                    mainPersonRedundant = true;
                    mainRedundancyStartAge = currentAge;
                    mainRedundancyEndAge = redundancyEndAge;
                    mainRedundancyUnemploymentMonths = unemploymentPeriod;

                    // Calculate income reduction factor for the first year
                    const firstYearMonths = Math.min(12, unemploymentPeriod);
                    mainRedundancyIncomeReductionFactor = (12 - firstYearMonths) / 12;

                    // Log information about the second year if applicable
                    if (unemploymentPeriod > 12) {
                        const secondYearMonths = Math.min(12, unemploymentPeriod - 12);
                        const secondYearReductionFactor = (12 - secondYearMonths) / 12;
                    }
                } else if (person === 'partner') {
                    partnerRedundant = true;
                    partnerRedundancyStartAge = currentAge;
                    partnerRedundancyEndAge = redundancyEndAge;
                    partnerRedundancyUnemploymentMonths = unemploymentPeriod;

                    // Calculate income reduction factor for the first year
                    const firstYearMonths = Math.min(12, unemploymentPeriod);
                    partnerRedundancyIncomeReductionFactor = (12 - firstYearMonths) / 12;

                    // Log information about the second year if applicable
                    if (unemploymentPeriod > 12) {
                        const secondYearMonths = Math.min(12, unemploymentPeriod - 12);
                        const secondYearReductionFactor = (12 - secondYearMonths) / 12;
                    }
                }
            }
        };

        // Function to calculate redundancy income reduction factor based on current age
        const calculateRedundancyIncomeReductionFactor = (currentAge: number, startAge: number, endAge: number, totalMonths: number) => {
            // If not in redundancy period, return 1.0 (no reduction)
            if (currentAge < startAge || currentAge >= endAge) {
                return 1.0;
            }

            // Calculate which year of redundancy we're in (0-based)
            const yearOfRedundancy = Math.floor(currentAge) - Math.floor(startAge);

            // Calculate months of unemployment in this year
            let monthsInThisYear;
            if (yearOfRedundancy === 0) {
                // First year
                monthsInThisYear = Math.min(12, totalMonths);
            } else {
                // Subsequent years
                const monthsRemaining = totalMonths - (yearOfRedundancy * 12);
                monthsInThisYear = Math.min(12, monthsRemaining);
            }

            // If no months in this year, return 1.0 (no reduction)
            if (monthsInThisYear <= 0) {
                return 1.0;
            }

            // Calculate reduction factor: (12 - months) / 12
            const reductionFactor = (12 - monthsInThisYear) / 12;
            return reductionFactor;
        };

        // Function to calculate recession impact based on fund characteristics
        const calculateRecessionImpact = (fundType: string, stdDev: number, baseMarketLoss: number): number => {
            // Get the fund's risk profile based on standard deviation
            // Higher std dev = higher equity allocation = greater impact
            let equityFactor = 1.0;

            // Use standard deviation as a proxy for equity allocation
            // Typical ranges: Conservative (4-5%), Moderate (6-8%), Balanced (8-10%), Growth (10-12%), High Growth (12-15%)
            if (stdDev <= 5) {
                // Conservative funds (low equity)
                equityFactor = 0.6; // 60% of base impact
            } else if (stdDev <= 8) {
                // Moderate funds (medium-low equity)
                equityFactor = 0.8; // 80% of base impact
            } else if (stdDev <= 10) {
                // Balanced funds (medium equity)
                equityFactor = 1.0; // 100% of base impact (baseline)
            } else if (stdDev <= 12) {
                // Growth funds (medium-high equity)
                equityFactor = 1.2; // 120% of base impact
            } else {
                // High Growth funds (high equity)
                equityFactor = 1.4; // 140% of base impact
            }

            // Calculate the adjusted market loss percentage
            const adjustedMarketLoss = baseMarketLoss * equityFactor;

            // Return the loss multiplier (e.g., 0.7 for a 30% loss)
            return 1 - (adjustedMarketLoss / 100);
        };

        // Function to check for recession events
        const checkForRecessionEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for recession events at the current age that are enabled
            const recessionEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'recession' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );

            // Process each recession event
            for (const event of recessionEvents) {
                const marketLoss = event.marketLoss || 30; // Default 30% market loss
                const reboundType = event.reboundType || 'average'; // Default to average rebound
                const reboundPeriod = event.reboundPeriod || 3; // Default 3 year rebound period

                // Get current fund types and characteristics
                let invFundStdDev = inv_std_dev * 100; // Convert to percentage
                let ksFundStdDev = ks_std_dev * 100;
                let partnerKsFundStdDev = partner_ks_std_dev * 100;

                // Get fund types from periods if available
                let invFundType = 'Balanced'; // Default
                let ksFundType = 'Balanced';
                let partnerKsFundType = 'Balanced';

                // Find current investment fund period
                if (data.fund_periods && data.fund_periods.length > 0) {
                    const currentInvPeriod = data.fund_periods.find(p =>
                        currentAge >= p.period[0] && currentAge <= p.period[1]);
                    if (currentInvPeriod) {
                        invFundType = currentInvPeriod.fundType;
                        invFundStdDev = currentInvPeriod.stdDev;
                    }
                }

                // Find current KiwiSaver fund period
                if (data.ks_periods && data.ks_periods.length > 0) {
                    const currentKsPeriod = data.ks_periods.find(p =>
                        currentAge >= p.period[0] && currentAge <= p.period[1]);
                    if (currentKsPeriod) {
                        ksFundType = currentKsPeriod.fundType;
                        ksFundStdDev = currentKsPeriod.stdDev;
                    }
                }

                // Find current partner KiwiSaver fund period
                if (partner && data.partner_ks_periods && data.partner_ks_periods.length > 0) {
                    const currentPartnerKsPeriod = data.partner_ks_periods.find(p =>
                        currentAge >= p.period[0] && currentAge <= p.period[1]);
                    if (currentPartnerKsPeriod) {
                        partnerKsFundType = currentPartnerKsPeriod.fundType;
                        partnerKsFundStdDev = currentPartnerKsPeriod.stdDev;
                    }
                }

                // Calculate fund-specific loss multipliers
                const invLossMultiplier = calculateRecessionImpact(invFundType, invFundStdDev, marketLoss);
                const ksLossMultiplier = calculateRecessionImpact(ksFundType, ksFundStdDev, marketLoss);
                const partnerKsLossMultiplier = partner ?
                    calculateRecessionImpact(partnerKsFundType, partnerKsFundStdDev, marketLoss) : 1.0;

                // Store original values before applying losses
                recessionOriginalInvFund = investment_fund;
                recessionOriginalKsFund = kiwisaver_fund;
                recessionOriginalPartnerKsFund = partner ? partner_kiwisaver_fund : 0;

                // Apply fund-specific market losses to each investment bucket
                investment_fund1 *= invLossMultiplier;
                investment_fund2 *= invLossMultiplier;
                investment_fund3 *= invLossMultiplier;
                investment_fund4 *= invLossMultiplier;
                investment_fund5 *= invLossMultiplier;

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                // Apply losses to KiwiSaver funds
                kiwisaver_fund *= ksLossMultiplier;
                if (partner) {
                    partner_kiwisaver_fund *= partnerKsLossMultiplier;
                }

                // Set recession parameters for the rebound period
                recessionActive = true;
                recessionStartAge = currentAge;
                recessionEndAge = currentAge + reboundPeriod;
                recessionReboundType = reboundType;

                // We already stored the original values before applying losses above

                // Store the target recovery values based on rebound type and fund characteristics
                // Higher equity funds recover more in good rebounds but less in bad rebounds
                if (reboundType === 'good') {
                    // Good rebound: Higher equity funds can exceed pre-recession value
                    const invRecoveryFactor = 1.0 + (invFundStdDev / 100); // e.g., 1.12 for Growth fund with 12% std dev
                    const ksRecoveryFactor = 1.0 + (ksFundStdDev / 100);
                    const partnerKsRecoveryFactor = 1.0 + (partnerKsFundStdDev / 100);

                    recessionTargetInvFund = recessionOriginalInvFund * invRecoveryFactor;
                    recessionTargetKsFund = recessionOriginalKsFund * ksRecoveryFactor;
                    recessionTargetPartnerKsFund = recessionOriginalPartnerKsFund * partnerKsRecoveryFactor;
                } else if (reboundType === 'bad') {
                    // Bad rebound: Higher equity funds recover less
                    const invRecoveryFactor = 1.0 - (invFundStdDev / 200); // e.g., 0.94 for Growth fund with 12% std dev
                    const ksRecoveryFactor = 1.0 - (ksFundStdDev / 200);
                    const partnerKsRecoveryFactor = 1.0 - (partnerKsFundStdDev / 200);

                    recessionTargetInvFund = recessionOriginalInvFund * invRecoveryFactor;
                    recessionTargetKsFund = recessionOriginalKsFund * ksRecoveryFactor;
                    recessionTargetPartnerKsFund = recessionOriginalPartnerKsFund * partnerKsRecoveryFactor;
                } else {
                    // Average rebound: All funds recover to approximately pre-recession value
                    recessionTargetInvFund = recessionOriginalInvFund;
                    recessionTargetKsFund = recessionOriginalKsFund;
                    recessionTargetPartnerKsFund = recessionOriginalPartnerKsFund;
                }

                // Loss percentages are calculated for potential future use
                // but are not currently used directly
            }
        };

        // Function to check for maternity leave events
        const checkForMaternityEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for maternity events at the current age that are enabled
            const maternityEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'maternity' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );

            // Process each maternity event
            for (const event of maternityEvents) {
                const person = event.person;
                const maternityLeaveMonths = event.maternityLeaveMonths || 0; // In months
                const backToWorkMonths = event.backToWorkMonths || 0; // In months
                const incomeReductionPercent = event.incomeReductionPercent || 0; // Percentage of income during maternity leave
                const backToWorkIncomePercent = event.backToWorkIncomePercent || 100; // Percentage of income during back-to-work period

                // Calculate income reduction factors
                const maternityIncomeReductionFactor = incomeReductionPercent / 100;
                const backToWorkIncomeReductionFactor = backToWorkIncomePercent / 100;

                // Calculate the end ages for maternity leave and back-to-work periods
                // Use precise decimal values for fractional years to handle months correctly
                const maternityLeaveYears = maternityLeaveMonths / 12;
                const maternityEndAge = currentAge + maternityLeaveYears;

                const backToWorkYears = backToWorkMonths / 12;
                const backToWorkEndAge = maternityEndAge + backToWorkYears;
                // Apply income reduction based on the person affected
                if (person === 'main') {
                    mainPersonMaternity = true;
                    mainMaternityStartAge = currentAge;
                    mainMaternityEndAge = maternityEndAge;
                    mainBackToWorkEndAge = backToWorkEndAge;
                    mainMaternityLeaveMonths = maternityLeaveMonths;
                    mainBackToWorkMonths = backToWorkMonths;
                    mainMaternityIncomeReductionFactor = maternityIncomeReductionFactor;
                    mainBackToWorkIncomeReductionFactor = backToWorkIncomeReductionFactor;
                } else if (person === 'partner') {
                    partnerMaternity = true;
                    partnerMaternityStartAge = currentAge;
                    partnerMaternityEndAge = maternityEndAge;
                    partnerBackToWorkEndAge = backToWorkEndAge;
                    partnerMaternityLeaveMonths = maternityLeaveMonths;
                    partnerBackToWorkMonths = backToWorkMonths;
                    partnerMaternityIncomeReductionFactor = maternityIncomeReductionFactor;
                    partnerBackToWorkIncomeReductionFactor = backToWorkIncomeReductionFactor;
                }
            }
        };

        // Function to calculate maternity income reduction factor for each month of the year
        const calculateMaternityIncomeReductionFactors = (currentAge: number, startAge: number, maternityEndAge: number, backToWorkEndAge: number, maternityFactor: number, backToWorkFactor: number) => {
            // Calculate reduction factors for each month of the year
            const monthlyFactors = [];

            for (let month = 0; month < 12; month++) {
                // Calculate the exact age in months for this month
                const exactAgeInMonths = Math.floor(currentAge * 12) + month;
                const startAgeMonths = Math.floor(startAge * 12);
                const maternityEndAgeMonths = Math.floor(maternityEndAge * 12);
                const backToWorkEndAgeMonths = Math.floor(backToWorkEndAge * 12);

                // If before maternity period or after back-to-work period, use 1.0 (no reduction)
                if (exactAgeInMonths < startAgeMonths || exactAgeInMonths >= backToWorkEndAgeMonths) {
                    monthlyFactors.push(1.0);
                }
                // If in maternity leave period
                else if (exactAgeInMonths < maternityEndAgeMonths) {
                    monthlyFactors.push(maternityFactor);
                }
                // If in back-to-work period
                else if (exactAgeInMonths >= maternityEndAgeMonths && exactAgeInMonths < backToWorkEndAgeMonths) {
                    monthlyFactors.push(backToWorkFactor);
                }
                else {
                    // Default (should not reach here)
                    monthlyFactors.push(1.0);
                }
            }

            // Calculate the average reduction factor for the year
            const averageFactor = monthlyFactors.reduce((sum, factor) => sum + factor, 0) / 12;

            return {
                monthlyFactors,
                averageFactor
            };
        };

        // Simplified function that returns just the average factor for backward compatibility
        // This function is kept for potential future use but is not currently used directly

        // Function to check for trauma events
        const checkForTraumaEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for trauma events at the current age that are enabled
            const traumaEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'trauma' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );


            // Process each trauma event
            for (const event of traumaEvents) {
                const person = event.person;
                const insurancePayout = event.insurancePayout || 0;
                const investmentAllocation = event.investmentAllocation || 50; // Default 50% allocation to investments
                const recoveryPeriod = event.recoveryPeriod || 1; // Default 1 year recovery period
                const incomeReduction = event.incomeReduction || 50; // Default 50% income reduction
                const expenseReduction = event.expenseReduction || 20; // Default 20% expense reduction

                // Determine how much goes to investments vs savings
                const investmentAmount = (insurancePayout * investmentAllocation) / 100;
                const savingsAmount = insurancePayout - investmentAmount;

                // Add insurance payout to respective funds
                // Insurance payouts are tax-free, so we add them directly to the funds
                investment_fund += investmentAmount;
                savings_fund += savingsAmount;

                // Track the total insurance payout for this year
                currentYearInsurancePayout += insurancePayout;

                // Calculate the end age for the trauma effects
                const traumaEndAge = currentAge + recoveryPeriod;

                // Apply income and expense reductions based on the person affected
                if (person === 'main') {
                    mainPersonTrauma = true;
                    mainTraumaEndAge = traumaEndAge;
                    mainTraumaIncomeReductionFactor = 1 - (incomeReduction / 100);
                } else if (person === 'partner') {
                    partnerTrauma = true;
                    partnerTraumaEndAge = traumaEndAge;
                    partnerTraumaIncomeReductionFactor = 1 - (incomeReduction / 100);
                }

                // Apply expense reduction for both main and partner trauma events
                traumaExpenseReductionFactor = 1 - (expenseReduction / 100);
            }
        };

        // Function to check for TPD events
        const checkForTPDEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for TPD events at the current age that are enabled
            const tpdEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'tpd' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );

            // Process each TPD event
            for (const event of tpdEvents) {
                const person = event.person;
                const insurancePayout = event.insurancePayout || 0;
                const investmentAllocation = event.investmentAllocation || 50; // Default 50% allocation to investments

                // Determine how much goes to investments vs savings
                const investmentAmount = (insurancePayout * investmentAllocation) / 100;
                const savingsAmount = insurancePayout - investmentAmount;

                // Add insurance payout to respective funds
                // Insurance payouts are tax-free, so we add them directly to the funds
                investment_fund += investmentAmount;
                savings_fund += savingsAmount;

                // Track the total insurance payout for this year
                currentYearInsurancePayout += insurancePayout;

                // Mark the appropriate person as TPD
                if (person === 'main') {
                    mainPersonTPD = true;
                    // Apply expense reduction if specified
                    if (event.expenseReduction) {
                        expenseReductionFactor = 1 - (event.expenseReduction / 100);
                    }
                } else if (person === 'partner') {
                    partnerTPD = true;
                    // Apply expense reduction if specified
                    if (event.expenseReduction) {
                        expenseReductionFactor = 1 - (event.expenseReduction / 100);
                    }
                }
                // Note: Unlike death events, TPD events do not affect KiwiSaver funds or superannuation rates
            }
        };

        // Function to check for death events
        const checkForDeathEvents = (currentAge: number) => {
            if (!scenarioData.whatIfEvents || !Array.isArray(scenarioData.whatIfEvents)) return;

            // Check for death events at the current age that are enabled
            const deathEvents = scenarioData.whatIfEvents.filter((event: any) =>
                event.type === 'death' &&
                event.age === currentAge &&
                event.enabled !== false // Only include events that are enabled
            );


            // Process each death event
            for (const event of deathEvents) {
                const person = event.person;
                const insurancePayout = event.insurancePayout || 0;
                const investmentAllocation = event.investmentAllocation || 50; // Default 50% allocation to investments

                // Determine how much goes to investments vs savings
                const investmentAmount = (insurancePayout * investmentAllocation) / 100;
                const savingsAmount = insurancePayout - investmentAmount;

                // Add insurance payout to respective funds
                // Insurance payouts are tax-free, so we add them directly to the funds
                investment_fund += investmentAmount;
                savings_fund += savingsAmount;

                // Track the total insurance payout for this year
                currentYearInsurancePayout += insurancePayout;

                // Mark the appropriate person as deceased
                if (person === 'main') {
                    mainPersonDeceased = true;
                    // Apply expense reduction if specified
                    if (event.expenseReduction) {
                        expenseReductionFactor = 1 - (event.expenseReduction / 100);
                    }
                } else if (person === 'partner') {
                    partnerDeceased = true;
                    // Apply expense reduction if specified
                    if (event.expenseReduction) {
                        expenseReductionFactor = 1 - (event.expenseReduction / 100);
                    }
                }

                // If partner dies, transfer their KiwiSaver to main person
                if (person === 'partner' && partner) {
                    kiwisaver_fund += partner_kiwisaver_fund;
                    partner_kiwisaver_fund = 0;
                }

                // If main person dies, transfer their KiwiSaver to partner
                if (person === 'main' && partner) {
                    partner_kiwisaver_fund += kiwisaver_fund;
                    kiwisaver_fund = 0;
                }
            }
        };

        // For each year in the simulation
        for (let i = 0; i < years.length; i++) {
        const age = years[i];

        // Check for death events at the current age
        checkForDeathEvents(age);

        // Check for TPD events at the current age
        checkForTPDEvents(age);

        // Check for trauma events at the current age
        checkForTraumaEvents(age);

        // Check for redundancy events at the current age
        checkForRedundancyEvents(age);

        // Check for maternity leave events at the current age
        checkForMaternityEvents(age);

        // Check for recession events at the current age
        checkForRecessionEvents(age);

        // Reset trauma effects if we've reached the end of the trauma period
        if (mainPersonTrauma && age >= mainTraumaEndAge) {
            mainPersonTrauma = false;
            mainTraumaIncomeReductionFactor = 1.0;
        }
        if (partnerTrauma && age >= partnerTraumaEndAge) {
            partnerTrauma = false;
            partnerTraumaIncomeReductionFactor = 1.0;
        }
        // Reset trauma expense reduction if both trauma effects have ended
        if (!mainPersonTrauma && !partnerTrauma) {
            traumaExpenseReductionFactor = 1.0;
        }

        // Reset recession effects if we've reached the end of the recession period
        if (recessionActive && age >= recessionEndAge) {
            recessionActive = false;
            recessionReboundType = 'average';
        }

        // Reset redundancy effects if we've reached the end of the redundancy period
        if (mainPersonRedundant && age >= mainRedundancyEndAge) {
            mainPersonRedundant = false;
            mainRedundancyIncomeReductionFactor = 1.0;
        } else if (mainPersonRedundant) {
            // Update the reduction factor for the current year if still in redundancy period
            mainRedundancyIncomeReductionFactor = calculateRedundancyIncomeReductionFactor(age, mainRedundancyStartAge, mainRedundancyEndAge, mainRedundancyUnemploymentMonths);
        }

        if (partnerRedundant && age >= partnerRedundancyEndAge) {
            partnerRedundant = false;
            partnerRedundancyIncomeReductionFactor = 1.0;
        } else if (partnerRedundant) {
            // Update the reduction factor for the current year if still in redundancy period
            partnerRedundancyIncomeReductionFactor = calculateRedundancyIncomeReductionFactor(age, partnerRedundancyStartAge, partnerRedundancyEndAge, partnerRedundancyUnemploymentMonths);
        }

        // Reset maternity leave effects
        // We don't need to reset anything here since we're calculating the reduction factors
        // on a monthly basis in the calculateMaternityIncomeReductionFactors function

        // For debugging purposes, let's log the current status
        if (mainPersonMaternity) {
            const ageMonths = Math.floor(age * 12);
            // End age months are calculated for potential future use
            // but only back-to-work end age is currently used
            const mainBackToWorkEndAgeMonths = Math.floor(mainBackToWorkEndAge * 12);

            if (ageMonths >= mainBackToWorkEndAgeMonths) {
                mainPersonMaternity = false;
                mainMaternityIncomeReductionFactor = 1.0;
                mainBackToWorkIncomeReductionFactor = 1.0;
            }
        }

        if (partnerMaternity) {
            const ageMonths = Math.floor(age * 12);
            // End age months are calculated for potential future use
            // but only back-to-work end age is currently used
            const partnerBackToWorkEndAgeMonths = Math.floor(partnerBackToWorkEndAge * 12);

            if (ageMonths >= partnerBackToWorkEndAgeMonths) {
                partnerMaternity = false;
                partnerMaternityIncomeReductionFactor = 1.0;
                partnerBackToWorkIncomeReductionFactor = 1.0;
            }
        }

        // Reset variables for the current year
        taxes = 0;
        income = 0;
        let main_income = 0;
        partner_income = 0;
        let additional_income_total = 0;
        let main_income_tax = 0;
        let partner_income_tax = 0;
        savings.push(savings_fund);

        // Handle property purchases with deposit sources
        // Property 1 purchase
        if (scenarioData.show_purchase_details && scenarioData.purchase_age && age === scenarioData.purchase_age) {
            // Calculate inflated property value at purchase age
            const yearsSinceStart = age - starting_age;
            const inflationFactor = Math.pow(1 + inflation_rate / 100, yearsSinceStart);
            property_value = scenarioData.property_value ? scenarioData.property_value * inflationFactor : 0;

            // Calculate deposit amount and debt
            const depositAmount = scenarioData.deposit_amount || 0;

            // Use the debt value from the input, not calculated from property value
            debt_value = scenarioData.debt ? scenarioData.debt * inflationFactor : 0;

            // Reset debt years to initial value
            debt_years = scenarioData.initial_debt_years || 30;

            // Process deposit sources if available
            if (scenarioData.deposit_sources) {
                // Ensure deposit_sources is a proper object with all properties
                const sources = typeof scenarioData.deposit_sources === 'object' ?
                    scenarioData.deposit_sources :
                    {
                        savings: 0,
                        investments: 0,
                        main_kiwisaver: 0,
                        partner_kiwisaver: 0,
                        gifting: 0,
                        other: 0
                    };

                // Reduce savings by the specified amount
                if (sources.savings && sources.savings > 0) {
                    savings_fund = Math.max(0, savings_fund - sources.savings);
                }

                // Reduce investments by the specified amount
                if (sources.investments && sources.investments > 0) {
                    // Check if we have specific investment fund withdrawals
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    // If no specific fund withdrawals, use the legacy approach with proportional withdrawal
                    if (!specificFundWithdrawals) {

                        const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                        if (totalInvestment > 0) {
                            // Calculate withdrawal from each bucket proportionally
                            const draw1 = sources.investments * (investment_fund1 / totalInvestment);
                            const draw2 = sources.investments * (investment_fund2 / totalInvestment);
                            const draw3 = sources.investments * (investment_fund3 / totalInvestment);
                            const draw4 = sources.investments * (investment_fund4 / totalInvestment);
                            const draw5 = sources.investments * (investment_fund5 / totalInvestment);

                            // Apply withdrawal to each bucket
                            investment_fund1 = Math.max(0, investment_fund1 - draw1);
                            investment_fund2 = Math.max(0, investment_fund2 - draw2);
                            investment_fund3 = Math.max(0, investment_fund3 - draw3);
                            investment_fund4 = Math.max(0, investment_fund4 - draw4);
                            investment_fund5 = Math.max(0, investment_fund5 - draw5);
                        }
                    }

                    // Update the total investment fund
                    investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                } else {
                    // Process specific fund withdrawals even if investments is not set
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    if (specificFundWithdrawals) {
                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }

                // Reduce main KiwiSaver by the specified amount
                if (sources.main_kiwisaver && sources.main_kiwisaver > 0) {
                    kiwisaver_fund = Math.max(0, kiwisaver_fund - sources.main_kiwisaver);
                }

                // Reduce partner KiwiSaver by the specified amount
                if (sources.partner_kiwisaver && sources.partner_kiwisaver > 0 && partner) {
                    partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - sources.partner_kiwisaver);
                }

                // Note: gifting and other sources don't reduce any funds
            }

            // Record the purchase in metrics
            metricArrays[i]['Property Purchase'].push(property_value);
            metricArrays[i]['Property Deposit'].push(depositAmount);
        }

        // Property 2 purchase
        if (scenarioData.show_purchase_details2 && scenarioData.purchase_age2 && age === scenarioData.purchase_age2) {
            // Calculate inflated property value at purchase age
            const yearsSinceStart = age - starting_age;
            const inflationFactor = Math.pow(1 + inflation_rate / 100, yearsSinceStart);
            property_value2 = scenarioData.property_value2 ? scenarioData.property_value2 * inflationFactor : 0;

            // Calculate deposit amount and debt
            const depositAmount = scenarioData.deposit_amount2 || 0;

            // Use the debt value from the input, not calculated from property value
            debt_value2 = scenarioData.debt2 ? scenarioData.debt2 * inflationFactor : 0;

            // Reset debt years to initial value
            debt_years2 = scenarioData.initial_debt_years2 || 30;

            // Process deposit sources if available
            if (scenarioData.deposit_sources2) {
                // Ensure deposit_sources is a proper object with all properties
                const sources = typeof scenarioData.deposit_sources2 === 'object' ?
                    scenarioData.deposit_sources2 :
                    {
                        savings: 0,
                        investments: 0,
                        main_kiwisaver: 0,
                        partner_kiwisaver: 0,
                        gifting: 0,
                        other: 0
                    };

                // Reduce savings by the specified amount
                if (sources.savings && sources.savings > 0) {
                    savings_fund = Math.max(0, savings_fund - sources.savings);
                }

                // Reduce investments by the specified amount
                if (sources.investments && sources.investments > 0) {
                    // Check if we have specific investment fund withdrawals
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    // If no specific fund withdrawals, use the legacy approach with proportional withdrawal
                    if (!specificFundWithdrawals) {
                        const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                        if (totalInvestment > 0) {
                            // Calculate withdrawal from each bucket proportionally
                            const draw1 = sources.investments * (investment_fund1 / totalInvestment);
                            const draw2 = sources.investments * (investment_fund2 / totalInvestment);
                            const draw3 = sources.investments * (investment_fund3 / totalInvestment);
                            const draw4 = sources.investments * (investment_fund4 / totalInvestment);
                            const draw5 = sources.investments * (investment_fund5 / totalInvestment);

                            // Apply withdrawal to each bucket
                            investment_fund1 = Math.max(0, investment_fund1 - draw1);
                            investment_fund2 = Math.max(0, investment_fund2 - draw2);
                            investment_fund3 = Math.max(0, investment_fund3 - draw3);
                            investment_fund4 = Math.max(0, investment_fund4 - draw4);
                            investment_fund5 = Math.max(0, investment_fund5 - draw5);
                        }
                    }

                    // Update the total investment fund
                    investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                } else {
                    // Process specific fund withdrawals even if investments is not set
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    if (specificFundWithdrawals) {
                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }

                // Reduce main KiwiSaver by the specified amount
                if (sources.main_kiwisaver && sources.main_kiwisaver > 0) {
                    kiwisaver_fund = Math.max(0, kiwisaver_fund - sources.main_kiwisaver);
                }

                // Reduce partner KiwiSaver by the specified amount
                if (sources.partner_kiwisaver && sources.partner_kiwisaver > 0 && partner) {
                    partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - sources.partner_kiwisaver);
                }
            }

            // Record the purchase in metrics
            metricArrays[i]['Property Purchase 2'].push(property_value2);
            metricArrays[i]['Property Deposit 2'].push(depositAmount);
        }

        // Property 3 purchase
        if (scenarioData.show_purchase_details3 && scenarioData.purchase_age3 && age === scenarioData.purchase_age3) {
            // Calculate inflated property value at purchase age
            const yearsSinceStart = age - starting_age;
            const inflationFactor = Math.pow(1 + inflation_rate / 100, yearsSinceStart);
            property_value3 = scenarioData.property_value3 ? scenarioData.property_value3 * inflationFactor : 0;

            // Calculate deposit amount and debt
            const depositAmount = scenarioData.deposit_amount3 || 0;

            // Use the debt value from the input, not calculated from property value
            debt_value3 = scenarioData.debt3 ? scenarioData.debt3 * inflationFactor : 0;

            // Reset debt years to initial value
            debt_years3 = scenarioData.initial_debt_years3 || 30;

            // Process deposit sources if available
            if (scenarioData.deposit_sources3) {
                // Ensure deposit_sources is a proper object with all properties
                const sources = typeof scenarioData.deposit_sources3 === 'object' ?
                    scenarioData.deposit_sources3 :
                    {
                        savings: 0,
                        investments: 0,
                        main_kiwisaver: 0,
                        partner_kiwisaver: 0,
                        gifting: 0,
                        other: 0
                    };

                // Reduce savings by the specified amount
                if (sources.savings && sources.savings > 0) {
                    savings_fund = Math.max(0, savings_fund - sources.savings);
                }

                // Reduce investments by the specified amount
                if (sources.investments && sources.investments > 0) {
                    // Check if we have specific investment fund withdrawals
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    // If no specific fund withdrawals, use the legacy approach with proportional withdrawal
                    if (!specificFundWithdrawals) {
                        const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                        if (totalInvestment > 0) {
                            // Calculate withdrawal from each bucket proportionally
                            const draw1 = sources.investments * (investment_fund1 / totalInvestment);
                            const draw2 = sources.investments * (investment_fund2 / totalInvestment);
                            const draw3 = sources.investments * (investment_fund3 / totalInvestment);
                            const draw4 = sources.investments * (investment_fund4 / totalInvestment);
                            const draw5 = sources.investments * (investment_fund5 / totalInvestment);

                            // Apply withdrawal to each bucket
                            investment_fund1 = Math.max(0, investment_fund1 - draw1);
                            investment_fund2 = Math.max(0, investment_fund2 - draw2);
                            investment_fund3 = Math.max(0, investment_fund3 - draw3);
                            investment_fund4 = Math.max(0, investment_fund4 - draw4);
                            investment_fund5 = Math.max(0, investment_fund5 - draw5);
                        }
                    }

                    // Update the total investment fund
                    investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                } else {
                    // Process specific fund withdrawals even if investments is not set
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    if (specificFundWithdrawals) {
                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }

                // Reduce main KiwiSaver by the specified amount
                if (sources.main_kiwisaver && sources.main_kiwisaver > 0) {
                    kiwisaver_fund = Math.max(0, kiwisaver_fund - sources.main_kiwisaver);
                }

                // Reduce partner KiwiSaver by the specified amount
                if (sources.partner_kiwisaver && sources.partner_kiwisaver > 0 && partner) {
                    partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - sources.partner_kiwisaver);
                }
            }

            // Record the purchase in metrics
            metricArrays[i]['Property Purchase 3'].push(property_value3);
            metricArrays[i]['Property Deposit 3'].push(depositAmount);
        }

        // Property 4 purchase
        if (scenarioData.show_purchase_details4 && scenarioData.purchase_age4 && age === scenarioData.purchase_age4) {
            // Calculate inflated property value at purchase age
            const yearsSinceStart = age - starting_age;
            const inflationFactor = Math.pow(1 + inflation_rate / 100, yearsSinceStart);
            property_value4 = scenarioData.property_value4 ? scenarioData.property_value4 * inflationFactor : 0;

            // Calculate deposit amount and debt
            const depositAmount = scenarioData.deposit_amount4 || 0;

            // Use the debt value from the input, not calculated from property value
            debt_value4 = scenarioData.debt4 ? scenarioData.debt4 * inflationFactor : 0;

            // Reset debt years to initial value
            debt_years4 = scenarioData.initial_debt_years4 || 30;

            // Process deposit sources if available
            if (scenarioData.deposit_sources4) {
                // Ensure deposit_sources is a proper object with all properties
                const sources = typeof scenarioData.deposit_sources4 === 'object' ?
                    scenarioData.deposit_sources4 :
                    {
                        savings: 0,
                        investments: 0,
                        main_kiwisaver: 0,
                        partner_kiwisaver: 0,
                        gifting: 0,
                        other: 0
                    };

                // Reduce savings by the specified amount
                if (sources.savings && sources.savings > 0) {
                    savings_fund = Math.max(0, savings_fund - sources.savings);
                }

                // Reduce investments by the specified amount
                if (sources.investments && sources.investments > 0) {
                    // Check if we have specific investment fund withdrawals
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    // If no specific fund withdrawals, use the legacy approach with proportional withdrawal
                    if (!specificFundWithdrawals) {
                        const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                        if (totalInvestment > 0) {
                            // Calculate withdrawal from each bucket proportionally
                            const draw1 = sources.investments * (investment_fund1 / totalInvestment);
                            const draw2 = sources.investments * (investment_fund2 / totalInvestment);
                            const draw3 = sources.investments * (investment_fund3 / totalInvestment);
                            const draw4 = sources.investments * (investment_fund4 / totalInvestment);
                            const draw5 = sources.investments * (investment_fund5 / totalInvestment);

                            // Apply withdrawal to each bucket
                            investment_fund1 = Math.max(0, investment_fund1 - draw1);
                            investment_fund2 = Math.max(0, investment_fund2 - draw2);
                            investment_fund3 = Math.max(0, investment_fund3 - draw3);
                            investment_fund4 = Math.max(0, investment_fund4 - draw4);
                            investment_fund5 = Math.max(0, investment_fund5 - draw5);
                        }
                    }

                    // Update the total investment fund
                    investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                } else {
                    // Process specific fund withdrawals even if investments is not set
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    if (specificFundWithdrawals) {
                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }

                // Reduce main KiwiSaver by the specified amount
                if (sources.main_kiwisaver && sources.main_kiwisaver > 0) {
                    kiwisaver_fund = Math.max(0, kiwisaver_fund - sources.main_kiwisaver);
                }

                // Reduce partner KiwiSaver by the specified amount
                if (sources.partner_kiwisaver && sources.partner_kiwisaver > 0 && partner) {
                    partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - sources.partner_kiwisaver);
                }
            }

            // Record the purchase in metrics
            metricArrays[i]['Property Purchase 4'].push(property_value4);
            metricArrays[i]['Property Deposit 4'].push(depositAmount);
        }

        // Property 5 purchase
        if (scenarioData.show_purchase_details5 && scenarioData.purchase_age5 && age === scenarioData.purchase_age5) {
            // Calculate inflated property value at purchase age
            const yearsSinceStart = age - starting_age;
            const inflationFactor = Math.pow(1 + inflation_rate / 100, yearsSinceStart);
            property_value5 = scenarioData.property_value5 ? scenarioData.property_value5 * inflationFactor : 0;

            // Calculate deposit amount and debt
            const depositAmount = scenarioData.deposit_amount5 || 0;

            // Use the debt value from the input, not calculated from property value
            debt_value5 = scenarioData.debt5 ? scenarioData.debt5 * inflationFactor : 0;

            // Reset debt years to initial value
            debt_years5 = scenarioData.initial_debt_years5 || 30;

            // Process deposit sources if available
            if (scenarioData.deposit_sources5) {
                // Ensure deposit_sources is a proper object with all properties
                const sources = typeof scenarioData.deposit_sources5 === 'object' ?
                    scenarioData.deposit_sources5 :
                    {
                        savings: 0,
                        investments: 0,
                        main_kiwisaver: 0,
                        partner_kiwisaver: 0,
                        gifting: 0,
                        other: 0
                    };

                // Reduce savings by the specified amount
                if (sources.savings && sources.savings > 0) {
                    savings_fund = Math.max(0, savings_fund - sources.savings);
                }

                // Reduce investments by the specified amount
                if (sources.investments && sources.investments > 0) {
                    // Check if we have specific investment fund withdrawals
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    // If no specific fund withdrawals, use the legacy approach with proportional withdrawal
                    if (!specificFundWithdrawals) {
                        const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                        if (totalInvestment > 0) {
                            // Calculate withdrawal from each bucket proportionally
                            const draw1 = sources.investments * (investment_fund1 / totalInvestment);
                            const draw2 = sources.investments * (investment_fund2 / totalInvestment);
                            const draw3 = sources.investments * (investment_fund3 / totalInvestment);
                            const draw4 = sources.investments * (investment_fund4 / totalInvestment);
                            const draw5 = sources.investments * (investment_fund5 / totalInvestment);

                            // Apply withdrawal to each bucket
                            investment_fund1 = Math.max(0, investment_fund1 - draw1);
                            investment_fund2 = Math.max(0, investment_fund2 - draw2);
                            investment_fund3 = Math.max(0, investment_fund3 - draw3);
                            investment_fund4 = Math.max(0, investment_fund4 - draw4);
                            investment_fund5 = Math.max(0, investment_fund5 - draw5);
                        }
                    }

                    // Update the total investment fund
                    investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                } else {
                    // Process specific fund withdrawals even if investments is not set
                    let specificFundWithdrawals = false;
                    let totalSpecificWithdrawals = 0;

                    // Process specific fund withdrawals if they exist
                    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
                        const fundKey = `fund${fundNumber}` as keyof typeof sources;
                        const fundValue = sources[fundKey];
                        if (fundValue !== undefined && fundValue > 0) {
                            specificFundWithdrawals = true;
                            totalSpecificWithdrawals += fundValue;

                            // Withdraw from the specific fund
                            switch (fundNumber) {
                                case 1:
                                    investment_fund1 = Math.max(0, investment_fund1 - fundValue);
                                    break;
                                case 2:
                                    investment_fund2 = Math.max(0, investment_fund2 - fundValue);
                                    break;
                                case 3:
                                    investment_fund3 = Math.max(0, investment_fund3 - fundValue);
                                    break;
                                case 4:
                                    investment_fund4 = Math.max(0, investment_fund4 - fundValue);
                                    break;
                                case 5:
                                    investment_fund5 = Math.max(0, investment_fund5 - fundValue);
                                    break;
                            }
                        }
                    }

                    if (specificFundWithdrawals) {
                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }

                // Reduce main KiwiSaver by the specified amount
                if (sources.main_kiwisaver && sources.main_kiwisaver > 0) {
                    kiwisaver_fund = Math.max(0, kiwisaver_fund - sources.main_kiwisaver);
                }

                // Reduce partner KiwiSaver by the specified amount
                if (sources.partner_kiwisaver && sources.partner_kiwisaver > 0 && partner) {
                    partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - sources.partner_kiwisaver);
                }
            }

            // Record the purchase in metrics
            metricArrays[i]['Property Purchase 5'].push(property_value5);
            metricArrays[i]['Property Deposit 5'].push(depositAmount);
        }

        // Main Income calculation - calculate once and store the correct value
        main_income = 0; // Reset main_income
        if (!mainPersonDeceased && !mainPersonTPD && income_period[0] <= age && age <= income_period[1]) {
            // Calculate base income with inflation
            let baseIncome = annual_income * Math.pow(1 + main_income_inflation_rate / 100, age - starting_age);

            // Apply trauma reduction if applicable
            if (mainPersonTrauma) {
                main_income = baseIncome * mainTraumaIncomeReductionFactor;
            }
            // Apply redundancy reduction if applicable
            else if (mainPersonRedundant) {
                // Calculate the appropriate reduction factor for the current age
                const reductionFactor = calculateRedundancyIncomeReductionFactor(age, mainRedundancyStartAge, mainRedundancyEndAge, mainRedundancyUnemploymentMonths);
                main_income = baseIncome * reductionFactor;
            }
            // Apply maternity leave reduction if applicable
            else if (mainPersonMaternity) {
                // Calculate the appropriate reduction factors for each month of the year
                // Monthly factors are calculated but only the average factor is used
                const { averageFactor } = calculateMaternityIncomeReductionFactors(age, mainMaternityStartAge, mainMaternityEndAge, mainBackToWorkEndAge, mainMaternityIncomeReductionFactor, mainBackToWorkIncomeReductionFactor);

                // Apply the average reduction factor for the year
                main_income = baseIncome * averageFactor;
            } else {
                main_income = baseIncome;
            }
        }

        // Partner Income calculation (if applicable) - calculate once and store the correct value
        partner_income = 0; // Reset partner_income
        if (partner && !partnerDeceased && !partnerTPD && partner_income_period[0] <= partner_age && partner_age <= partner_income_period[1]) {
            // Calculate base income with inflation
            let baseIncome = partner_annual_income * Math.pow(1 + partner_income_inflation_rate / 100, partner_age - partner_starting_age!);

            // Apply trauma reduction if applicable
            if (partnerTrauma) {
                partner_income = baseIncome * partnerTraumaIncomeReductionFactor;
            }
            // Apply redundancy reduction if applicable
            else if (partnerRedundant) {
                // Calculate the appropriate reduction factor for the current age
                const reductionFactor = calculateRedundancyIncomeReductionFactor(age, partnerRedundancyStartAge, partnerRedundancyEndAge, partnerRedundancyUnemploymentMonths);
                partner_income = baseIncome * reductionFactor;
            }
            // Apply maternity leave reduction if applicable
            else if (partnerMaternity) {
                // Calculate the appropriate reduction factors for each month of the year
                // Monthly factors are calculated but only the average factor is used
                const { averageFactor } = calculateMaternityIncomeReductionFactors(age, partnerMaternityStartAge, partnerMaternityEndAge, partnerBackToWorkEndAge, partnerMaternityIncomeReductionFactor, partnerBackToWorkIncomeReductionFactor);

                // Apply the average reduction factor for the year
                partner_income = baseIncome * averageFactor;
            } else {
                partner_income = baseIncome;
            }
        }

        // Calculate additional incomes
        let additional_income_main = 0;
        let additional_income_partner = 0;
        let additional_income_other = 0;

        for (const add_inc_obj of additional_incomes) {
            const add_inc_value = add_inc_obj.value;
            const add_inc_period = add_inc_obj.period;
            if (add_inc_period[0] <= age && age <= add_inc_period[1]) {
                // Use individual inflation rate if available, otherwise use global rate
                const inflationRate = add_inc_obj.inflation_rate !== undefined ? add_inc_obj.inflation_rate : inflation_rate;
                const inflated_value = add_inc_value * Math.pow(1 + inflationRate / 100, age - starting_age);

                // Track additional incomes separately based on tax type
                if (add_inc_obj.tax_type === 'main') {
                    additional_income_main += inflated_value;
                } else if (partner && add_inc_obj.tax_type === 'partner') {
                    additional_income_partner += inflated_value;
                } else {
                    // If no partner or tax_free, add to additional_income_other
                    additional_income_other += inflated_value;
                }

                // Add all additional incomes to additional_income_total for tracking
                additional_income_total += inflated_value;
            }
        }

        // Add superannuation calculation here
        let super_income = 0;
        if (superannuation) {
            // Get superannuation rates from input data or use defaults
            const single_super_rate = scenarioData.single_super_rate || 27000;
            const couple_one_super_rate = scenarioData.couple_one_super_rate || 19500;
            const couple_both_super_rate = scenarioData.couple_both_super_rate || 39500;
            const super_eligibility_age = scenarioData.super_eligibility_age || 65;

            // Both people are alive
            if (partner && !mainPersonDeceased && !partnerDeceased) {
                if ((age >= super_eligibility_age && partner_age < super_eligibility_age) ||
                    (partner_age >= super_eligibility_age && age < super_eligibility_age)) {
                    super_income = couple_one_super_rate * Math.pow(1 + inflation_rate / 100, age - starting_age);
                } else if (age >= super_eligibility_age && partner_age >= super_eligibility_age) {
                    super_income = couple_both_super_rate * Math.pow(1 + inflation_rate / 100, age - starting_age);
                }
            }
            // Only main person is alive (partner deceased or no partner)
            else if (!mainPersonDeceased && (partnerDeceased || !partner)) {
                if (age >= super_eligibility_age) {
                    super_income = single_super_rate * Math.pow(1 + inflation_rate / 100, age - starting_age);
                }
            }
            // Only partner is alive (main person deceased)
            else if (mainPersonDeceased && partner && !partnerDeceased) {
                if (partner_age >= super_eligibility_age) {
                    super_income = single_super_rate * Math.pow(1 + inflation_rate / 100, age - starting_age);
                }
            }
            // Both deceased - no superannuation
        }
        super_income_list.push(super_income);

        // Calculate rental income (taxable)
        let rental_income = 0;

        // Property 1 rental income
        let rental_income1 = 0;
        if (scenarioData.rental_income && scenarioData.rental_amount &&
            scenarioData.rental_start_age !== undefined && scenarioData.rental_end_age !== undefined) {

            // Check if property is not being sold (sell_main_property is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property || (scenarioData.main_property_sale_age !== undefined && scenarioData.main_property_sale_age >= age)) {
                if (age >= scenarioData.rental_start_age && age <= scenarioData.rental_end_age) {
                    const inflated_rental = scenarioData.rental_amount * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    rental_income1 = inflated_rental;
                    rental_income += inflated_rental;
                    // Rental income is tracked separately and not added to main_income to avoid double counting
                }
            }
        }

        // Property 2 rental income
        let rental_income2 = 0;
        // Make sure rental_income2 is a boolean
        if (typeof scenarioData.rental_income2 !== 'boolean') {
            scenarioData.rental_income2 = Boolean(scenarioData.rental_income2);
        }
        if (scenarioData.rental_income2 && scenarioData.rental_amount2 &&
            scenarioData.rental_start_age2 !== undefined && scenarioData.rental_end_age2 !== undefined) {

            // Check if property is not being sold (sell_main_property2 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property2 || (scenarioData.main_property_sale_age2 !== undefined && scenarioData.main_property_sale_age2 >= age)) {
                if (age >= scenarioData.rental_start_age2 && age <= scenarioData.rental_end_age2) {
                    const inflated_rental = scenarioData.rental_amount2 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    rental_income2 = inflated_rental;
                    rental_income += inflated_rental;
                    // Rental income is tracked separately and not added to main_income to avoid double counting
                }
            }
        }

        // Property 3 rental income
        let rental_income3 = 0;
        if (scenarioData.rental_income3 && scenarioData.rental_amount3 &&
            scenarioData.rental_start_age3 !== undefined && scenarioData.rental_end_age3 !== undefined) {

            // Check if property is not being sold (sell_main_property3 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property3 || (scenarioData.main_property_sale_age3 !== undefined && scenarioData.main_property_sale_age3 >= age)) {
                if (age >= scenarioData.rental_start_age3 && age <= scenarioData.rental_end_age3) {
                    const inflated_rental = scenarioData.rental_amount3 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    rental_income3 = inflated_rental;
                    rental_income += inflated_rental;
                    // Rental income is tracked separately and not added to main_income to avoid double counting
                }
            }
        }


        // Property 4 rental income
        let rental_income4 = 0;
        if (scenarioData.rental_income4 && scenarioData.rental_amount4 &&
            scenarioData.rental_start_age4 !== undefined && scenarioData.rental_end_age4 !== undefined) {

            // Check if property is not being sold (sell_main_property4 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property4 || (scenarioData.main_property_sale_age4 !== undefined && scenarioData.main_property_sale_age4 >= age)) {
                if (age >= scenarioData.rental_start_age4 && age <= scenarioData.rental_end_age4) {
                    const inflated_rental = scenarioData.rental_amount4 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    rental_income4 = inflated_rental;
                    rental_income += inflated_rental;
                    // Rental income is tracked separately and not added to main_income to avoid double counting
                }
            }
        }

        // Property 5 rental income
        let rental_income5 = 0;
        if (scenarioData.rental_income5 && scenarioData.rental_amount5 &&
            scenarioData.rental_start_age5 !== undefined && scenarioData.rental_end_age5 !== undefined) {

            // Check if property is not being sold (sell_main_property5 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property5 || (scenarioData.main_property_sale_age5 !== undefined && scenarioData.main_property_sale_age5 >= age)) {
                if (age >= scenarioData.rental_start_age5 && age <= scenarioData.rental_end_age5) {
                    const inflated_rental = scenarioData.rental_amount5 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    rental_income5 = inflated_rental;
                    rental_income += inflated_rental;
                    // Rental income is tracked separately and not added to main_income to avoid double counting
                }
            }
        }

        // Push individual property rental incomes instead of the total
        rental_income_list.push(rental_income1);
        rental_income_list2.push(rental_income2);
        rental_income_list3.push(rental_income3);
        rental_income_list4.push(rental_income4);
        rental_income_list5.push(rental_income5);

        // Calculate board income (non-taxable)
        let board_income = 0;
        let board_income1 = 0;

        // Property 1 board income
        if (scenarioData.board_income && scenarioData.board_amount &&
            scenarioData.board_start_age !== undefined && scenarioData.board_end_age !== undefined) {

            // Check if property is not being sold (sell_main_property is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property || (scenarioData.main_property_sale_age !== undefined && scenarioData.main_property_sale_age >= age)) {
                if (age >= scenarioData.board_start_age && age <= scenarioData.board_end_age) {
                    const inflated_board = scenarioData.board_amount * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    board_income1 = inflated_board;
                    board_income += inflated_board;
                    additional_income_total += inflated_board; // Add to non-taxable income
                }
            }
        }


        // Property 2 board income
        let board_income2 = 0;
        // Make sure board_income2 is a boolean
        if (typeof scenarioData.board_income2 !== 'boolean') {
            scenarioData.board_income2 = Boolean(scenarioData.board_income2);
        }
        if (scenarioData.board_income2 && scenarioData.board_amount2 &&
            scenarioData.board_start_age2 !== undefined && scenarioData.board_end_age2 !== undefined) {

            // Check if property is not being sold (sell_main_property2 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property2 || (scenarioData.main_property_sale_age2 !== undefined && scenarioData.main_property_sale_age2 >= age)) {
                if (age >= scenarioData.board_start_age2 && age <= scenarioData.board_end_age2) {
                    const inflated_board = scenarioData.board_amount2 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    board_income2 = inflated_board;
                    board_income += inflated_board;
                    additional_income_total += inflated_board; // Add to non-taxable income
                }
            }
        }


        // Property 3 board income
        let board_income3 = 0;
        if (scenarioData.board_income3 && scenarioData.board_amount3 &&
            scenarioData.board_start_age3 !== undefined && scenarioData.board_end_age3 !== undefined) {

            // Check if property is not being sold (sell_main_property3 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property3 || (scenarioData.main_property_sale_age3 !== undefined && scenarioData.main_property_sale_age3 >= age)) {
                if (age >= scenarioData.board_start_age3 && age <= scenarioData.board_end_age3) {
                    const inflated_board = scenarioData.board_amount3 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    board_income3 = inflated_board;
                    board_income += inflated_board;
                    additional_income_total += inflated_board; // Add to non-taxable income
                }
            }
        }


        // Property 4 board income
        let board_income4 = 0;
        if (scenarioData.board_income4 && scenarioData.board_amount4 &&
            scenarioData.board_start_age4 !== undefined && scenarioData.board_end_age4 !== undefined) {

            // Check if property is not being sold (sell_main_property4 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property4 || (scenarioData.main_property_sale_age4 !== undefined && scenarioData.main_property_sale_age4 >= age)) {
                if (age >= scenarioData.board_start_age4 && age <= scenarioData.board_end_age4) {
                    const inflated_board = scenarioData.board_amount4 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    board_income4 = inflated_board;
                    board_income += inflated_board;
                    additional_income_total += inflated_board; // Add to non-taxable income
                }
            }
        }

        // Property 5 board income
        let board_income5 = 0;
        if (scenarioData.board_income5 && scenarioData.board_amount5 &&
            scenarioData.board_start_age5 !== undefined && scenarioData.board_end_age5 !== undefined) {
            // Check if property is not being sold (sell_main_property5 is false) or hasn't been sold yet
            if (!scenarioData.sell_main_property5 || (scenarioData.main_property_sale_age5 !== undefined && scenarioData.main_property_sale_age5 >= age)) {
                if (age >= scenarioData.board_start_age5 && age <= scenarioData.board_end_age5) {
                    const inflated_board = scenarioData.board_amount5 * Math.pow(1 + inflation_rate / 100, age - starting_age);
                    board_income5 = inflated_board;
                    board_income += inflated_board;
                    additional_income_total += inflated_board; // Add to non-taxable income
                }
            }
        }

        // Push individual property board incomes instead of the total
        board_income_list.push(board_income1);
        board_income_list2.push(board_income2);
        board_income_list3.push(board_income3);
        board_income_list4.push(board_income4);
        board_income_list5.push(board_income5);

        // Update total income to include all income sources
        // For tax purposes, we separate main income, additional income, and rental income
        const main_only_taxable_income = main_income + additional_income_main;
        const partner_taxable_income = partner_income + additional_income_partner;

        // Calculate rental income tax separately for each property
        let rental_income_tax = 0;
        let rental_income_tax2 = 0;
        let rental_income_tax3 = 0;
        let rental_income_tax4 = 0;
        let rental_income_tax5 = 0;

        // Calculate tax for each rental property if it has income
        if (rental_income1 > 0) {
            rental_income_tax = calculateProgressiveTax(rental_income1, tax_brackets, tax_thresholds);
        }
        if (rental_income2 > 0) {
            rental_income_tax2 = calculateProgressiveTax(rental_income2, tax_brackets, tax_thresholds);
        }
        if (rental_income3 > 0) {
            rental_income_tax3 = calculateProgressiveTax(rental_income3, tax_brackets, tax_thresholds);
        }
        if (rental_income4 > 0) {
            rental_income_tax4 = calculateProgressiveTax(rental_income4, tax_brackets, tax_thresholds);
        }
        if (rental_income5 > 0) {
            rental_income_tax5 = calculateProgressiveTax(rental_income5, tax_brackets, tax_thresholds);
        }

        // Total income includes all sources
        income = main_only_taxable_income + partner_taxable_income + rental_income + additional_income_other + super_income;
        gross_income.push(income);

        // Create separate variables for KiwiSaver-eligible income (excluding additional incomes)
        const kiwisaver_eligible_main_income = !mainPersonDeceased && !mainPersonTPD ? main_income : 0;
        const kiwisaver_eligible_partner_income = partner && !partnerDeceased && !partnerTPD ? partner_income : 0;

        // Calculate main person's tax using progressive tax brackets on their taxable income (excluding rental income)
        main_income_tax = calculateProgressiveTax(main_only_taxable_income, tax_brackets, tax_thresholds);

        // Calculate partner's tax if applicable
        if (partner) {
            partner_income_tax = calculateProgressiveTax(partner_taxable_income, tax_brackets, tax_thresholds);
        }

        // Helper function to calculate progressive tax
        function calculateProgressiveTax(income: number, brackets: number[], thresholds: number[]): number {
            let tax = 0;

            for (let j = 0; j < brackets.length; j++) {
                const lowerThreshold = thresholds[j];
                const upperThreshold = j < thresholds.length - 1 ? thresholds[j + 1] : Infinity;

                // Calculate the amount of income that falls within this bracket
                const amountInBracket = Math.max(0, Math.min(income, upperThreshold) - lowerThreshold);

                // Apply the tax rate for this bracket
                tax += amountInBracket * brackets[j];

                // If we've accounted for all income, we can stop
                if (income <= upperThreshold) break;
            }

            return tax;
        }



        // Calculate net income after the split tax calculation
        const net_income = income - (main_income_tax + partner_income_tax);

        // Calculate total expenditure for the current age
        let totalExpenditure = 0;
        let additionalExpenditure = 0;

        // Calculate the combined expense reduction factor (from death/TPD and trauma events)
        const combinedExpenseReductionFactor = expenseReductionFactor * traumaExpenseReductionFactor;

        // Main expenses - apply expense reduction factor if a death, TPD, or trauma event has occurred
        let basicExpenses1 = 0;
        if (scenarioData.annual_expenses1 && scenarioData.expense_period1) {
            if (age >= scenarioData.expense_period1[0] && age <= scenarioData.expense_period1[1]) {
                const baseExpense = scenarioData.annual_expenses1 * (1 + expense1_inflation_rate / 100) ** (age - starting_age);
                // Apply combined expense reduction
                const reducedExpense = baseExpense * combinedExpenseReductionFactor;
                totalExpenditure += reducedExpense;
                basicExpenses1 = reducedExpense; // Store the reduced expense for metrics
            }
        }

        // Second expenses - apply expense reduction factor if a death, TPD, or trauma event has occurred
        let basicExpenses2 = 0;
        if (scenarioData.second_expense && scenarioData.annual_expenses2 && scenarioData.expense_period2) {
            if (age >= scenarioData.expense_period2[0] && age <= scenarioData.expense_period2[1]) {
                const baseExpense = scenarioData.annual_expenses2 * (1 + expense2_inflation_rate / 100) ** (age - starting_age);
                // Apply combined expense reduction
                const reducedExpense = baseExpense * combinedExpenseReductionFactor;
                totalExpenditure += reducedExpense;
                basicExpenses2 = reducedExpense; // Store the reduced expense for metrics
            }
        }

        // Additional expenses - apply expense reduction factor if a death, TPD, or trauma event has occurred
        if (scenarioData.additional_expenses) {
            for (const expense of scenarioData.additional_expenses) {
                if (age >= expense.period[0] && age <= expense.period[1]) {
                    // Check if this is a year when the expense should occur based on frequency
                    const yearsFromStart = age - expense.period[0];
                    if (yearsFromStart % (expense.frequency || 1) === 0) {
                        // Use individual inflation rate if available, otherwise use global rate
                        const expenseInflationRate = expense.inflation_rate !== undefined ? expense.inflation_rate : inflation_rate;
                        const baseExpense = expense.value * (1 + expenseInflationRate / 100) ** (age - starting_age);

                        // Apply combined expense reduction
                        const reducedExpense = baseExpense * combinedExpenseReductionFactor;
                        totalExpenditure += reducedExpense;
                        additionalExpenditure += reducedExpense;
                    }
                }
            }
        }

        // Store the basic expenses for metrics
        basic_expenses1_list.push(basicExpenses1);
        basic_expenses2_list.push(basicExpenses2);


        let inflated_lump_sum = 0
        let inflated_lump_sum2 = 0
        let inflated_lump_sum3 = 0
        let inflated_lump_sum4 = 0
        let inflated_lump_sum5 = 0

        // Handle lump sum payment for property 1
        if (age === scenarioData.lump_sum_payment_age && scenarioData.lump_sum_payment_amount && scenarioData.lump_sum_payment_source) {
            // Calculate inflated lump sum amount
            inflated_lump_sum = scenarioData.lump_sum_payment_amount * Math.pow(1 + inflation_rate / 100, age - starting_age);
            totalExpenditure += inflated_lump_sum;

            // Store the lump sum payment amount for this year
            lump_sum_payment_amount[i] = inflated_lump_sum;

            // Reduce the source fund
            if (scenarioData.lump_sum_payment_source === 'investments') {
                const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                if (totalInvestment >= inflated_lump_sum) {
                    // Distribute the withdrawal proportionally across the 5 investment buckets
                    if (totalInvestment > 0) {
                        // Calculate withdrawal from each bucket proportionally
                        const draw1 = inflated_lump_sum * (investment_fund1 / totalInvestment);
                        const draw2 = inflated_lump_sum * (investment_fund2 / totalInvestment);
                        const draw3 = inflated_lump_sum * (investment_fund3 / totalInvestment);
                        const draw4 = inflated_lump_sum * (investment_fund4 / totalInvestment);
                        const draw5 = inflated_lump_sum * (investment_fund5 / totalInvestment);

                        // Apply withdrawal to each bucket
                        investment_fund1 = Math.max(0, investment_fund1 - draw1);
                        investment_fund2 = Math.max(0, investment_fund2 - draw2);
                        investment_fund3 = Math.max(0, investment_fund3 - draw3);
                        investment_fund4 = Math.max(0, investment_fund4 - draw4);
                        investment_fund5 = Math.max(0, investment_fund5 - draw5);

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }
            } else if (scenarioData.lump_sum_payment_source === 'savings') {
                if (savings_fund >= inflated_lump_sum) {
                    savings_fund -= inflated_lump_sum;
                }
            }
        } else {
            // No lump sum payment this year
            lump_sum_payment_amount[i] = 0;
        }

        // Handle lump sum payment for property 2
        if (age === scenarioData.lump_sum_payment_age2 && scenarioData.lump_sum_payment_amount2 && scenarioData.lump_sum_payment_source2) {
            // Calculate inflated lump sum amount
            inflated_lump_sum2 = scenarioData.lump_sum_payment_amount2 * Math.pow(1 + inflation_rate / 100, age - starting_age);
            totalExpenditure += inflated_lump_sum2;

            // Store the lump sum payment amount for this year
            lump_sum_payment_amount2[i] = inflated_lump_sum2;

            // Reduce the source fund
            if (scenarioData.lump_sum_payment_source2 === 'investments') {
                const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                if (totalInvestment >= inflated_lump_sum2) {
                    // Distribute the withdrawal proportionally across the 5 investment buckets
                    if (totalInvestment > 0) {
                        // Calculate withdrawal from each bucket proportionally
                        const draw1 = inflated_lump_sum2 * (investment_fund1 / totalInvestment);
                        const draw2 = inflated_lump_sum2 * (investment_fund2 / totalInvestment);
                        const draw3 = inflated_lump_sum2 * (investment_fund3 / totalInvestment);
                        const draw4 = inflated_lump_sum2 * (investment_fund4 / totalInvestment);
                        const draw5 = inflated_lump_sum2 * (investment_fund5 / totalInvestment);

                        // Apply withdrawal to each bucket
                        investment_fund1 = Math.max(0, investment_fund1 - draw1);
                        investment_fund2 = Math.max(0, investment_fund2 - draw2);
                        investment_fund3 = Math.max(0, investment_fund3 - draw3);
                        investment_fund4 = Math.max(0, investment_fund4 - draw4);
                        investment_fund5 = Math.max(0, investment_fund5 - draw5);

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }
            } else if (scenarioData.lump_sum_payment_source2 === 'savings') {
                if (savings_fund >= inflated_lump_sum2) {
                    savings_fund -= inflated_lump_sum2;
                }
            }
        } else {
            // No lump sum payment this year
            lump_sum_payment_amount2[i] = 0;
        }

        // Handle lump sum payment for property 3
        if (age === scenarioData.lump_sum_payment_age3 && scenarioData.lump_sum_payment_amount3 && scenarioData.lump_sum_payment_source3) {
            // Calculate inflated lump sum amount
            inflated_lump_sum3 = scenarioData.lump_sum_payment_amount3 * Math.pow(1 + inflation_rate / 100, age - starting_age);
            totalExpenditure += inflated_lump_sum3;

            // Store the lump sum payment amount for this year
            lump_sum_payment_amount3[i] = inflated_lump_sum3;

            // Reduce the source fund
            if (scenarioData.lump_sum_payment_source3 === 'investments') {
                const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                if (totalInvestment >= inflated_lump_sum3) {
                    // Distribute the withdrawal proportionally across the 5 investment buckets
                    if (totalInvestment > 0) {
                        // Calculate withdrawal from each bucket proportionally
                        const draw1 = inflated_lump_sum3 * (investment_fund1 / totalInvestment);
                        const draw2 = inflated_lump_sum3 * (investment_fund2 / totalInvestment);
                        const draw3 = inflated_lump_sum3 * (investment_fund3 / totalInvestment);
                        const draw4 = inflated_lump_sum3 * (investment_fund4 / totalInvestment);
                        const draw5 = inflated_lump_sum3 * (investment_fund5 / totalInvestment);

                        // Apply withdrawal to each bucket
                        investment_fund1 = Math.max(0, investment_fund1 - draw1);
                        investment_fund2 = Math.max(0, investment_fund2 - draw2);
                        investment_fund3 = Math.max(0, investment_fund3 - draw3);
                        investment_fund4 = Math.max(0, investment_fund4 - draw4);
                        investment_fund5 = Math.max(0, investment_fund5 - draw5);

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }
            } else if (scenarioData.lump_sum_payment_source3 === 'savings') {
                if (savings_fund >= inflated_lump_sum3) {
                    savings_fund -= inflated_lump_sum3;
                }
            }
        } else {
            // No lump sum payment this year
            lump_sum_payment_amount3[i] = 0;
        }

        // Handle lump sum payment for property 4
        if (age === scenarioData.lump_sum_payment_age4 && scenarioData.lump_sum_payment_amount4 && scenarioData.lump_sum_payment_source4) {
            // Calculate inflated lump sum amount
            inflated_lump_sum4 = scenarioData.lump_sum_payment_amount4 * Math.pow(1 + inflation_rate / 100, age - starting_age);
            totalExpenditure += inflated_lump_sum4;

            // Store the lump sum payment amount for this year
            lump_sum_payment_amount4[i] = inflated_lump_sum4;

            // Reduce the source fund
            if (scenarioData.lump_sum_payment_source4 === 'investments') {
                const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                if (totalInvestment >= inflated_lump_sum4) {
                    // Distribute the withdrawal proportionally across the 5 investment buckets
                    if (totalInvestment > 0) {
                        // Calculate withdrawal from each bucket proportionally
                        const draw1 = inflated_lump_sum4 * (investment_fund1 / totalInvestment);
                        const draw2 = inflated_lump_sum4 * (investment_fund2 / totalInvestment);
                        const draw3 = inflated_lump_sum4 * (investment_fund3 / totalInvestment);
                        const draw4 = inflated_lump_sum4 * (investment_fund4 / totalInvestment);
                        const draw5 = inflated_lump_sum4 * (investment_fund5 / totalInvestment);

                        // Apply withdrawal to each bucket
                        investment_fund1 = Math.max(0, investment_fund1 - draw1);
                        investment_fund2 = Math.max(0, investment_fund2 - draw2);
                        investment_fund3 = Math.max(0, investment_fund3 - draw3);
                        investment_fund4 = Math.max(0, investment_fund4 - draw4);
                        investment_fund5 = Math.max(0, investment_fund5 - draw5);

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }
            } else if (scenarioData.lump_sum_payment_source4 === 'savings') {
                if (savings_fund >= inflated_lump_sum4) {
                    savings_fund -= inflated_lump_sum4;
                }
            }
        } else {
            // No lump sum payment this year
            lump_sum_payment_amount4[i] = 0;
        }

        // Handle lump sum payment for property 5
        if (age === scenarioData.lump_sum_payment_age5 && scenarioData.lump_sum_payment_amount5 && scenarioData.lump_sum_payment_source5) {
            // Calculate inflated lump sum amount
            inflated_lump_sum5 = scenarioData.lump_sum_payment_amount5 * Math.pow(1 + inflation_rate / 100, age - starting_age);
            totalExpenditure += inflated_lump_sum5;

            // Store the lump sum payment amount for this year
            lump_sum_payment_amount5[i] = inflated_lump_sum5;

            // Reduce the source fund
            if (scenarioData.lump_sum_payment_source5 === 'investments') {
                const totalInvestment = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                if (totalInvestment >= inflated_lump_sum5) {
                    // Distribute the withdrawal proportionally across the 5 investment buckets
                    if (totalInvestment > 0) {
                        // Calculate withdrawal from each bucket proportionally
                        const draw1 = inflated_lump_sum5 * (investment_fund1 / totalInvestment);
                        const draw2 = inflated_lump_sum5 * (investment_fund2 / totalInvestment);
                        const draw3 = inflated_lump_sum5 * (investment_fund3 / totalInvestment);
                        const draw4 = inflated_lump_sum5 * (investment_fund4 / totalInvestment);
                        const draw5 = inflated_lump_sum5 * (investment_fund5 / totalInvestment);

                        // Apply withdrawal to each bucket
                        investment_fund1 = Math.max(0, investment_fund1 - draw1);
                        investment_fund2 = Math.max(0, investment_fund2 - draw2);
                        investment_fund3 = Math.max(0, investment_fund3 - draw3);
                        investment_fund4 = Math.max(0, investment_fund4 - draw4);
                        investment_fund5 = Math.max(0, investment_fund5 - draw5);

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    }
                }
            } else if (scenarioData.lump_sum_payment_source5 === 'savings') {
                if (savings_fund >= inflated_lump_sum5) {
                    savings_fund -= inflated_lump_sum5;
                }
            }
        } else {
            // No lump sum payment this year
            lump_sum_payment_amount5[i] = 0;
        }

        // Monthly Debt Repayment
        const monthly_interest_rate = debt_ir / 100 / 12;
        // Use the current debt_years value, which is updated when a property is purchased
        const num_payments = debt_years * 12;

        let monthly_debt_repayment = 0;
        if (num_payments > 0 && debt_value > 0) {
            const isInterestOnlyPeriod = scenarioData.interest_only_period &&
            scenarioData.interest_only_start_age !== undefined &&
            scenarioData.interest_only_end_age !== undefined &&
            age >= scenarioData.interest_only_start_age &&
            age < scenarioData.interest_only_end_age;
            // Calculate base monthly payment
            const baseMonthlyPayment = calculateMonthlyPayment(
                debt_value,
                debt_ir,
                debt_years,
                isInterestOnlyPeriod
            );

            const additionalRepayment = (age >= additional_debt_repayments_start_age && age <= additional_debt_repayments_end_age)
                ? additional_debt_repayments
                : 0;

            monthly_debt_repayment = baseMonthlyPayment + additionalRepayment;
            debt_values.push(debt_value);

            let interest_payment = 0;
            let principal_payment = 0;

            for (let m = 0; m < 12; m++) {
            interest_payment = monthly_interest_rate * debt_value;
            if (!isInterestOnlyPeriod) {
                principal_payment = (inflated_lump_sum / 12) + monthly_debt_repayment - interest_payment;
            } else {
                principal_payment = 0;
            }
            debt_value -= principal_payment;
            }

            debt_years -= 1;
            debt_repayment_values.push(monthly_debt_repayment);
            debt_principal_values.push(principal_payment * 12);
            debt_interest.push(interest_payment * 12);
            annual_debt_repayments.push(monthly_debt_repayment * 12);
        } else {
            monthly_debt_repayment = 0;
            debt_repayment_values.push(0);
            debt_principal_values.push(0);
            debt_values.push(0);
            debt_interest.push(0);
            annual_debt_repayments.push(0);
        }

        // Monthly Debt2 Repayment
        const monthly_interest_rate2 = debt_ir2 / 100 / 12;
        // Use the current debt_years2 value, which is updated when a property is purchased
        const num_payments2 = debt_years2 * 12;

        let monthly_debt_repayment2 = 0;
        if (num_payments2 > 0 && debt_value2 > 0) {
            const isInterestOnlyPeriod2 = scenarioData.interest_only_period2 &&
            scenarioData.interest_only_start_age2 !== undefined &&
            scenarioData.interest_only_end_age2 !== undefined &&
            age >= scenarioData.interest_only_start_age2 &&
            age < scenarioData.interest_only_end_age2;
            // Calculate base monthly payment
            const baseMonthlyPayment2 = calculateMonthlyPayment(
                debt_value2,
                debt_ir2,
                debt_years2,
                isInterestOnlyPeriod2
            );

            const additionalRepayment2 = (age >= additional_debt_repayments_start_age2 && age <= additional_debt_repayments_end_age2)
                ? additional_debt_repayments2
                : 0;

            monthly_debt_repayment2 = baseMonthlyPayment2 + additionalRepayment2;
            debt_values2.push(debt_value2);

            let interest_payment2 = 0;
            let principal_payment2 = 0;
            for (let m = 0; m < 12; m++) {
            interest_payment2 = monthly_interest_rate2 * debt_value2;
            if (!isInterestOnlyPeriod2) {
                principal_payment2 = (inflated_lump_sum2 / 12) + monthly_debt_repayment2 - interest_payment2;
            } else {
                principal_payment2 = 0;
            }
            debt_value2 -= principal_payment2;
            }

            debt_years2 -= 1;
            debt_repayment_values2.push(monthly_debt_repayment2);
            debt_principal_values2.push(principal_payment2 * 12);
            debt_interest2.push(interest_payment2 * 12);
            annual_debt_repayments2.push(monthly_debt_repayment2 * 12);
        } else {
            monthly_debt_repayment2 = 0;
            debt_repayment_values2.push(0);
            debt_principal_values2.push(0);
            debt_values2.push(0);
            debt_interest2.push(0);
            annual_debt_repayments2.push(0);
        }

        // Monthly Debt3 Repayment
        const monthly_interest_rate3 = debt_ir3 / 100 / 12;
        // Use the current debt_years3 value, which is updated when a property is purchased
        const num_payments3 = debt_years3 * 12;

        let monthly_debt_repayment3 = 0;
        if (num_payments3 > 0 && debt_value3 > 0) {
            const isInterestOnlyPeriod3 = scenarioData.interest_only_period3 &&
            scenarioData.interest_only_start_age3 !== undefined &&
            scenarioData.interest_only_end_age3 !== undefined &&
            age >= scenarioData.interest_only_start_age3 &&
            age < scenarioData.interest_only_end_age3;
            // Calculate base monthly payment
            const baseMonthlyPayment3 = calculateMonthlyPayment(
                debt_value3,
                debt_ir3,
                debt_years3,
                isInterestOnlyPeriod3
            );

            // Add additional repayments only if within the specified age range
            const additionalRepayment3 = (age >= additional_debt_repayments_start_age3 && age <= additional_debt_repayments_end_age3)
                ? additional_debt_repayments3
                : 0;

            monthly_debt_repayment3 = baseMonthlyPayment3 + additionalRepayment3;
            debt_values3.push(debt_value3);

            let interest_payment3 = 0;
            let principal_payment3 = 0;
            for (let m = 0; m < 12; m++) {
            interest_payment3 = monthly_interest_rate3 * debt_value3;
            if (!isInterestOnlyPeriod3) {
                principal_payment3 = (inflated_lump_sum3 / 12) + monthly_debt_repayment3 - interest_payment3;
            } else {
                principal_payment3 = 0;
            }
            debt_value3 -= principal_payment3;
            }

            debt_years3 -= 1;
            debt_repayment_values3.push(monthly_debt_repayment3);
            debt_principal_values3.push(principal_payment3 * 12);
            debt_interest3.push(interest_payment3 * 12);
            annual_debt_repayments3.push(monthly_debt_repayment3 * 12);
        } else {
            monthly_debt_repayment3 = 0;
            debt_repayment_values3.push(0);
            debt_principal_values3.push(0);
            debt_values3.push(0);
            debt_interest3.push(0);
            annual_debt_repayments3.push(0);
        }

        // Monthly Debt4 Repayment
        const monthly_interest_rate4 = debt_ir4 / 100 / 12;
        // Use the current debt_years4 value, which is updated when a property is purchased
        const num_payments4 = debt_years4 * 12;

        let monthly_debt_repayment4 = 0;
        if (num_payments4 > 0 && debt_value4 > 0) {
            const isInterestOnlyPeriod4 = scenarioData.interest_only_period4 &&
            scenarioData.interest_only_start_age4 !== undefined &&
            scenarioData.interest_only_end_age4 !== undefined &&
            age >= scenarioData.interest_only_start_age4 &&
            age < scenarioData.interest_only_end_age4;
            // Calculate base monthly payment
            const baseMonthlyPayment4 = calculateMonthlyPayment(
                debt_value4,
                debt_ir4,
                debt_years4,
                isInterestOnlyPeriod4
            );

            // Add additional repayments only if within the specified age range
            const additionalRepayment4 = (age >= additional_debt_repayments_start_age4 && age <= additional_debt_repayments_end_age4)
                ? additional_debt_repayments4
                : 0;

            monthly_debt_repayment4 = baseMonthlyPayment4 + additionalRepayment4;
            debt_values4.push(debt_value4);

            let interest_payment4 = 0;
            let principal_payment4 = 0;
            for (let m = 0; m < 12; m++) {
            interest_payment4 = monthly_interest_rate4 * debt_value4;
            if (!isInterestOnlyPeriod4) {
                principal_payment4 = (inflated_lump_sum4 / 12) + monthly_debt_repayment4 - interest_payment4;
            } else {
                principal_payment4 = 0;
            }
            debt_value4 -= principal_payment4;
            }

            debt_years4 -= 1;
            debt_repayment_values4.push(monthly_debt_repayment4);
            debt_principal_values4.push(principal_payment4 * 12);
            debt_interest4.push(interest_payment4 * 12);
            annual_debt_repayments4.push(monthly_debt_repayment4 * 12);
        } else {
            monthly_debt_repayment4 = 0;
            debt_repayment_values4.push(0);
            debt_principal_values4.push(0);
            debt_values4.push(0);
            debt_interest4.push(0);
            annual_debt_repayments4.push(0);
        }

        // Monthly Debt5 Repayment
        const monthly_interest_rate5 = debt_ir5 / 100 / 12;
        // Use the current debt_years5 value, which is updated when a property is purchased
        const num_payments5 = debt_years5 * 12;

        let monthly_debt_repayment5 = 0;
        if (num_payments5 > 0 && debt_value5 > 0) {
            const isInterestOnlyPeriod5 = scenarioData.interest_only_period5 &&
            scenarioData.interest_only_start_age5 !== undefined &&
            scenarioData.interest_only_end_age5 !== undefined &&
            age >= scenarioData.interest_only_start_age5 &&
            age < scenarioData.interest_only_end_age5;
            // Calculate base monthly payment
            const baseMonthlyPayment5 = calculateMonthlyPayment(
                debt_value5,
                debt_ir5,
                debt_years5,
                isInterestOnlyPeriod5
            );

            // Add additional repayments only if within the specified age range
            const additionalRepayment5 = (age >= additional_debt_repayments_start_age5 && age <= additional_debt_repayments_end_age5)
                ? additional_debt_repayments5
                : 0;

            monthly_debt_repayment5 = baseMonthlyPayment5 + additionalRepayment5;
            debt_values5.push(debt_value5);

            let interest_payment5 = 0;
            let principal_payment5 = 0;
            for (let m = 0; m < 12; m++) {
            interest_payment5 = monthly_interest_rate5 * debt_value5;
            if (!isInterestOnlyPeriod5) {
                principal_payment5 = (inflated_lump_sum5 / 12) + monthly_debt_repayment5 - interest_payment5;
            } else {
                principal_payment5 = 0;
            }
            debt_value5 -= principal_payment5;
            }

            debt_years5 -= 1;
            debt_repayment_values5.push(monthly_debt_repayment5);
            debt_principal_values5.push(principal_payment5 * 12);
            debt_interest5.push(interest_payment5 * 12);
            annual_debt_repayments5.push(monthly_debt_repayment5 * 12);
        } else {
            monthly_debt_repayment5 = 0;
            debt_repayment_values5.push(0);
            debt_principal_values5.push(0);
            debt_values5.push(0);
            debt_interest5.push(0);
            annual_debt_repayments5.push(0);
        }


        // Property Sale
        if (age === scenarioData.main_property_sale_age && scenarioData.sell_main_property) {
            // Calculate proceeds after debt payoff
            const saleValue = scenarioData.main_prop_sale_value || 0;
            const sale_proceeds = Math.max(0, saleValue - debt_value);

            // Calculate debt paid from sale proceeds
            const debtPaid = Math.min(saleValue, debt_value);

            // Always extinguish the debt when selling
            debt_value = 0;
            debt_years = 0;

            // Allocate proceeds based on preference
            if (scenarioData.sale_allocate_to_investment) {
            investment_fund += sale_proceeds;
            } else {
            savings_fund += sale_proceeds;
            }

            property_value = 0;

            // Record metrics
            metricArrays[i]['Property Sale Proceeds'].push(saleValue);
            metricArrays[i]['Debt Paid'].push(debtPaid);
            metricArrays[i]['Lump Sum Payment Amount'].push(debtPaid);
        }

        // Property Sale 2
        if (age === scenarioData.main_property_sale_age2 && scenarioData.sell_main_property2) {
            // Calculate proceeds after debt payoff
            const saleValue2 = scenarioData.main_prop_sale_value2 || 0;
            const sale_proceeds2 = Math.max(0, saleValue2 - debt_value2);

            // Calculate debt paid from sale proceeds
            const debtPaid2 = Math.min(saleValue2, debt_value2);

            // Always extinguish the debt when selling
            debt_value2 = 0;
            debt_years2 = 0;

            // Allocate proceeds based on preference
            if (scenarioData.sale2_allocate_to_investment) {
            investment_fund += sale_proceeds2;
            } else {
            savings_fund += sale_proceeds2;
            }

            property_value2 = 0;

            // Record metrics
            metricArrays[i]['Property Sale Proceeds 2'].push(saleValue2);
            metricArrays[i]['Debt Paid 2'].push(debtPaid2);
            metricArrays[i]['Lump Sum Payment Amount 2'].push(debtPaid2);
        }

        // Property Sale 3
        if (age === scenarioData.main_property_sale_age3 && scenarioData.sell_main_property3) {
            // Calculate proceeds after debt payoff
            const saleValue3 = scenarioData.main_prop_sale_value3 || 0;
            const sale_proceeds3 = Math.max(0, saleValue3 - debt_value3);

            // Calculate debt paid from sale proceeds
            const debtPaid3 = Math.min(saleValue3, debt_value3);

            // Always extinguish the debt when selling
            debt_value3 = 0;
            debt_years3 = 0;

            // Allocate proceeds based on preference
            if (scenarioData.sale3_allocate_to_investment) {
            investment_fund += sale_proceeds3;
            } else {
            savings_fund += sale_proceeds3;
            }

            property_value3 = 0;

            // Record metrics
            metricArrays[i]['Property Sale Proceeds 3'].push(saleValue3);
            metricArrays[i]['Debt Paid 3'].push(debtPaid3);
            metricArrays[i]['Lump Sum Payment Amount 3'].push(debtPaid3);
        }

        // Property Sale 4
        if (age === scenarioData.main_property_sale_age4 && scenarioData.sell_main_property4) {
            // Calculate proceeds after debt payoff
            const saleValue4 = scenarioData.main_prop_sale_value4 || 0;
            const sale_proceeds4 = Math.max(0, saleValue4 - debt_value4);

            // Calculate debt paid from sale proceeds
            const debtPaid4 = Math.min(saleValue4, debt_value4);

            // Always extinguish the debt when selling
            debt_value4 = 0;
            debt_years4 = 0;

            // Allocate proceeds based on preference
            if (scenarioData.sale4_allocate_to_investment) {
            investment_fund += sale_proceeds4;
            } else {
            savings_fund += sale_proceeds4;
            }

            property_value4 = 0;

            // Record metrics
            metricArrays[i]['Property Sale Proceeds 4'].push(saleValue4);
            metricArrays[i]['Debt Paid 4'].push(debtPaid4);
            metricArrays[i]['Lump Sum Payment Amount 4'].push(debtPaid4);
        }

        // Property Sale 5
        if (age === scenarioData.main_property_sale_age5 && scenarioData.sell_main_property5) {
            // Calculate proceeds after debt payoff
            const saleValue5 = scenarioData.main_prop_sale_value5 || 0;
            const sale_proceeds5 = Math.max(0, saleValue5 - debt_value5);

            // Calculate debt paid from sale proceeds
            const debtPaid5 = Math.min(saleValue5, debt_value5);

            // Always extinguish the debt when selling
            debt_value5 = 0;
            debt_years5 = 0;

            // Allocate proceeds based on preference
            if (scenarioData.sale5_allocate_to_investment) {
            investment_fund += sale_proceeds5;
            } else {
            savings_fund += sale_proceeds5;
            }

            property_value5 = 0;

            // Record metrics
            metricArrays[i]['Property Sale Proceeds 5'].push(saleValue5);
            metricArrays[i]['Debt Paid 5'].push(debtPaid5);
            metricArrays[i]['Lump Sum Payment Amount 5'].push(debtPaid5);
        }

        // Check if downsize should occur
        if (scenarioData.downsize && age === scenarioData.downsize_age) {
            // Validate downsizing parameters
            if (scenarioData.new_property_value === undefined || scenarioData.new_property_value < 0) {
            }

            const newPropertyValueToday = Math.max(0, scenarioData.new_property_value || 0);
            const yearsToDownsize = scenarioData.downsize_age - scenarioData.starting_age;
            const inflationRate = scenarioData.inflation_rate || 0;
            let lump_sum_payment_amount = 0;

            // Calculate inflated property values
            const inflatedNewPropertyValue = newPropertyValueToday * Math.pow(1 + (inflationRate / 100), yearsToDownsize);
            const currentPropertyValue = property_value;  // Store for metrics

            // Calculate proceeds and transaction costs (assuming 4% for real estate fees, legal, etc.)
            const transactionCosts = currentPropertyValue * 0.04;
            let downsizeProceeds = Math.max(0, currentPropertyValue - inflatedNewPropertyValue - transactionCosts);

            // Handle debt payoff if selected
            if (scenarioData.pay_off_debt && debt_value > 0) {
            const debtPayment = Math.min(debt_value, downsizeProceeds);
            debt_value -= debtPayment;
            downsizeProceeds -= debtPayment;
            lump_sum_payment_amount += debtPayment;
            // Record the debt payment as a lump sum payment
            metricArrays[i]['Lump Sum Payment Amount'].push(debtPayment);
            }

            // Allocate remaining proceeds based on preference
            if (scenarioData.allocate_to_investment) {
                // Distribute the proceeds across the 5 investment buckets based on allocation percentages
                const total_allocation = investment_allocation1 + investment_allocation2 + investment_allocation3 +
                                        investment_allocation4 + investment_allocation5;

                if (total_allocation > 0) {
                    // Add proceeds to each bucket proportionally
                    investment_fund1 += downsizeProceeds * (investment_allocation1 / total_allocation);
                    investment_fund2 += downsizeProceeds * (investment_allocation2 / total_allocation);
                    investment_fund3 += downsizeProceeds * (investment_allocation3 / total_allocation);
                    investment_fund4 += downsizeProceeds * (investment_allocation4 / total_allocation);
                    investment_fund5 += downsizeProceeds * (investment_allocation5 / total_allocation);
                } else {
                    // If no allocation percentages are specified, distribute equally
                    investment_fund1 += downsizeProceeds * 0.2;
                    investment_fund2 += downsizeProceeds * 0.2;
                    investment_fund3 += downsizeProceeds * 0.2;
                    investment_fund4 += downsizeProceeds * 0.2;
                    investment_fund5 += downsizeProceeds * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                savings_fund += downsizeProceeds;
            }

            // Update property value and store downsizing metrics
            property_value = inflatedNewPropertyValue;

            // Store downsizing event metrics
            metricArrays[i]['Property Sale Proceeds'].push(downsizeProceeds + transactionCosts);
            metricArrays[i]['Transaction Costs'].push(transactionCosts);
            metricArrays[i]['Debt Paid'].push(scenarioData.pay_off_debt ? Math.min(debt_value, downsizeProceeds) : 0);
        }

        // Check if downsize should occur for property 2
        if (scenarioData.downsize && age === scenarioData.downsize_age2 && property_value2 > 0) {
            // Validate downsizing parameters
            if (scenarioData.new_property_value2 === undefined || scenarioData.new_property_value2 < 0) {
            }

            const newPropertyValueToday2 = Math.max(0, scenarioData.new_property_value2 || 0);
            const yearsToDownsize2 = scenarioData.downsize_age2 - (scenarioData.starting_age2 || scenarioData.starting_age);
            const inflationRate = scenarioData.inflation_rate || 0;
            let lump_sum_payment_amount2 = 0;

            // Calculate inflated property values
            const inflatedNewPropertyValue2 = newPropertyValueToday2 * Math.pow(1 + (inflationRate / 100), yearsToDownsize2);
            const currentPropertyValue2 = property_value2;  // Store for metrics

            // Calculate proceeds and transaction costs (assuming 4% for real estate fees, legal, etc.)
            const transactionCosts2 = currentPropertyValue2 * 0.04;
            let downsizeProceeds2 = Math.max(0, currentPropertyValue2 - inflatedNewPropertyValue2 - transactionCosts2);

            // Handle debt payoff if selected
            if (scenarioData.pay_off_debt2 && debt_value2 > 0) {
            const debtPayment2 = Math.min(debt_value2, downsizeProceeds2);
            debt_value2 -= debtPayment2;
            downsizeProceeds2 -= debtPayment2;
            lump_sum_payment_amount2 += debtPayment2;
            // Record the debt payment as a lump sum payment
            metricArrays[i]['Lump Sum Payment Amount 2'].push(debtPayment2);
            }

            // Allocate remaining proceeds based on preference
            if (scenarioData.sale2_allocate_to_investment) {
                // Distribute the proceeds across the 5 investment buckets based on allocation percentages
                const total_allocation = investment_allocation1 + investment_allocation2 + investment_allocation3 +
                                        investment_allocation4 + investment_allocation5;

                if (total_allocation > 0) {
                    // Add proceeds to each bucket proportionally
                    investment_fund1 += downsizeProceeds2 * (investment_allocation1 / total_allocation);
                    investment_fund2 += downsizeProceeds2 * (investment_allocation2 / total_allocation);
                    investment_fund3 += downsizeProceeds2 * (investment_allocation3 / total_allocation);
                    investment_fund4 += downsizeProceeds2 * (investment_allocation4 / total_allocation);
                    investment_fund5 += downsizeProceeds2 * (investment_allocation5 / total_allocation);
                } else {
                    // If no allocation percentages are specified, distribute equally
                    investment_fund1 += downsizeProceeds2 * 0.2;
                    investment_fund2 += downsizeProceeds2 * 0.2;
                    investment_fund3 += downsizeProceeds2 * 0.2;
                    investment_fund4 += downsizeProceeds2 * 0.2;
                    investment_fund5 += downsizeProceeds2 * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                savings_fund += downsizeProceeds2;
            }

            // Update property value and store downsizing metrics
            property_value2 = inflatedNewPropertyValue2;

            // Store downsizing event metrics
            metricArrays[i]['Property Sale Proceeds 2'].push(downsizeProceeds2 + transactionCosts2);
            metricArrays[i]['Transaction Costs 2'].push(transactionCosts2);
            metricArrays[i]['Debt Paid 2'].push(scenarioData.pay_off_debt2 ? Math.min(debt_value2, downsizeProceeds2) : 0);
        }

        // Check if downsize should occur for property 3
        if (scenarioData.downsize && age === scenarioData.downsize_age3 && property_value3 > 0) {
            // Validate downsizing parameters
            if (scenarioData.new_property_value3 === undefined || scenarioData.new_property_value3 < 0) {
            }

            const newPropertyValueToday3 = Math.max(0, scenarioData.new_property_value3 || 0);
            const yearsToDownsize3 = scenarioData.downsize_age3 - (scenarioData.starting_age3 || scenarioData.starting_age);
            const inflationRate = scenarioData.inflation_rate || 0;
            let lump_sum_payment_amount3 = 0;

            // Calculate inflated property values
            const inflatedNewPropertyValue3 = newPropertyValueToday3 * Math.pow(1 + (inflationRate / 100), yearsToDownsize3);
            const currentPropertyValue3 = property_value3;  // Store for metrics

            // Calculate proceeds and transaction costs (assuming 4% for real estate fees, legal, etc.)
            const transactionCosts3 = currentPropertyValue3 * 0.04;
            let downsizeProceeds3 = Math.max(0, currentPropertyValue3 - inflatedNewPropertyValue3 - transactionCosts3);

            // Handle debt payoff if selected
            if (scenarioData.pay_off_debt3 && debt_value3 > 0) {
            const debtPayment3 = Math.min(debt_value3, downsizeProceeds3);
            debt_value3 -= debtPayment3;
            downsizeProceeds3 -= debtPayment3;
            lump_sum_payment_amount3 += debtPayment3;
            // Record the debt payment as a lump sum payment
            metricArrays[i]['Lump Sum Payment Amount 3'].push(debtPayment3);
            }

            // Allocate remaining proceeds based on preference
            if (scenarioData.sale3_allocate_to_investment) {
                // Distribute the proceeds across the 5 investment buckets based on allocation percentages
                const total_allocation = investment_allocation1 + investment_allocation2 + investment_allocation3 +
                                        investment_allocation4 + investment_allocation5;

                if (total_allocation > 0) {
                    // Add proceeds to each bucket proportionally
                    investment_fund1 += downsizeProceeds3 * (investment_allocation1 / total_allocation);
                    investment_fund2 += downsizeProceeds3 * (investment_allocation2 / total_allocation);
                    investment_fund3 += downsizeProceeds3 * (investment_allocation3 / total_allocation);
                    investment_fund4 += downsizeProceeds3 * (investment_allocation4 / total_allocation);
                    investment_fund5 += downsizeProceeds3 * (investment_allocation5 / total_allocation);
                } else {
                    // If no allocation percentages are specified, distribute equally
                    investment_fund1 += downsizeProceeds3 * 0.2;
                    investment_fund2 += downsizeProceeds3 * 0.2;
                    investment_fund3 += downsizeProceeds3 * 0.2;
                    investment_fund4 += downsizeProceeds3 * 0.2;
                    investment_fund5 += downsizeProceeds3 * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                savings_fund += downsizeProceeds3;
            }

            // Update property value and store downsizing metrics
            property_value3 = inflatedNewPropertyValue3;

            // Store downsizing event metrics
            metricArrays[i]['Property Sale Proceeds 3'].push(downsizeProceeds3 + transactionCosts3);
            metricArrays[i]['Transaction Costs 3'].push(transactionCosts3);
            metricArrays[i]['Debt Paid 3'].push(scenarioData.pay_off_debt3 ? Math.min(debt_value3, downsizeProceeds3) : 0);
        }

        // Check if downsize should occur for property 4
        if (scenarioData.downsize && age === scenarioData.downsize_age4 && property_value4 > 0) {
            // Validate downsizing parameters
            if (scenarioData.new_property_value4 === undefined || scenarioData.new_property_value4 < 0) {
            }

            const newPropertyValueToday4 = Math.max(0, scenarioData.new_property_value4 || 0);
            const yearsToDownsize4 = scenarioData.downsize_age4 - (scenarioData.starting_age4 || scenarioData.starting_age);
            const inflationRate = scenarioData.inflation_rate || 0;
            let lump_sum_payment_amount4 = 0;

            // Calculate inflated property values
            const inflatedNewPropertyValue4 = newPropertyValueToday4 * Math.pow(1 + (inflationRate / 100), yearsToDownsize4);
            const currentPropertyValue4 = property_value4;  // Store for metrics

            // Calculate proceeds and transaction costs (assuming 4% for real estate fees, legal, etc.)
            const transactionCosts4 = currentPropertyValue4 * 0.04;
            let downsizeProceeds4 = Math.max(0, currentPropertyValue4 - inflatedNewPropertyValue4 - transactionCosts4);

            // Handle debt payoff if selected
            if (scenarioData.pay_off_debt4 && debt_value4 > 0) {
            const debtPayment4 = Math.min(debt_value4, downsizeProceeds4);
            debt_value4 -= debtPayment4;
            downsizeProceeds4 -= debtPayment4;
            lump_sum_payment_amount4 += debtPayment4;
            // Record the debt payment as a lump sum payment
            metricArrays[i]['Lump Sum Payment Amount 4'].push(debtPayment4);
            }

            // Allocate remaining proceeds based on preference
            if (scenarioData.sale4_allocate_to_investment) {
                // Distribute the proceeds across the 5 investment buckets based on allocation percentages
                const total_allocation = investment_allocation1 + investment_allocation2 + investment_allocation3 +
                                        investment_allocation4 + investment_allocation5;

                if (total_allocation > 0) {
                    // Add proceeds to each bucket proportionally
                    investment_fund1 += downsizeProceeds4 * (investment_allocation1 / total_allocation);
                    investment_fund2 += downsizeProceeds4 * (investment_allocation2 / total_allocation);
                    investment_fund3 += downsizeProceeds4 * (investment_allocation3 / total_allocation);
                    investment_fund4 += downsizeProceeds4 * (investment_allocation4 / total_allocation);
                    investment_fund5 += downsizeProceeds4 * (investment_allocation5 / total_allocation);
                } else {
                    // If no allocation percentages are specified, distribute equally
                    investment_fund1 += downsizeProceeds4 * 0.2;
                    investment_fund2 += downsizeProceeds4 * 0.2;
                    investment_fund3 += downsizeProceeds4 * 0.2;
                    investment_fund4 += downsizeProceeds4 * 0.2;
                    investment_fund5 += downsizeProceeds4 * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                savings_fund += downsizeProceeds4;
            }

            // Update property value and store downsizing metrics
            property_value4 = inflatedNewPropertyValue4;

            // Store downsizing event metrics
            metricArrays[i]['Property Sale Proceeds 4'].push(downsizeProceeds4 + transactionCosts4);
            metricArrays[i]['Transaction Costs 4'].push(transactionCosts4);
            metricArrays[i]['Debt Paid 4'].push(scenarioData.pay_off_debt4 ? Math.min(debt_value4, downsizeProceeds4) : 0);
        }

        // Check if downsize should occur for property 5
        if (scenarioData.downsize && age === scenarioData.downsize_age5 && property_value5 > 0) {
            // Validate downsizing parameters
            if (scenarioData.new_property_value5 === undefined || scenarioData.new_property_value5 < 0) {
            }

            const newPropertyValueToday5 = Math.max(0, scenarioData.new_property_value5 || 0);
            const yearsToDownsize5 = scenarioData.downsize_age5 - (scenarioData.starting_age5 || scenarioData.starting_age);
            const inflationRate = scenarioData.inflation_rate || 0;
            let lump_sum_payment_amount5 = 0;

            // Calculate inflated property values
            const inflatedNewPropertyValue5 = newPropertyValueToday5 * Math.pow(1 + (inflationRate / 100), yearsToDownsize5);
            const currentPropertyValue5 = property_value5;  // Store for metrics

            // Calculate proceeds and transaction costs (assuming 4% for real estate fees, legal, etc.)
            const transactionCosts5 = currentPropertyValue5 * 0.04;
            let downsizeProceeds5 = Math.max(0, currentPropertyValue5 - inflatedNewPropertyValue5 - transactionCosts5);

            // Handle debt payoff if selected
            if (scenarioData.pay_off_debt5 && debt_value5 > 0) {
            const debtPayment5 = Math.min(debt_value5, downsizeProceeds5);
            debt_value5 -= debtPayment5;
            downsizeProceeds5 -= debtPayment5;
            lump_sum_payment_amount5 += debtPayment5;
            // Record the debt payment as a lump sum payment
            metricArrays[i]['Lump Sum Payment Amount 5'].push(debtPayment5);
            }

            // Allocate remaining proceeds based on preference
            if (scenarioData.sale5_allocate_to_investment) {
                // Distribute the proceeds across the 5 investment buckets based on allocation percentages
                const total_allocation = investment_allocation1 + investment_allocation2 + investment_allocation3 +
                                        investment_allocation4 + investment_allocation5;

                if (total_allocation > 0) {
                    // Add proceeds to each bucket proportionally
                    investment_fund1 += downsizeProceeds5 * (investment_allocation1 / total_allocation);
                    investment_fund2 += downsizeProceeds5 * (investment_allocation2 / total_allocation);
                    investment_fund3 += downsizeProceeds5 * (investment_allocation3 / total_allocation);
                    investment_fund4 += downsizeProceeds5 * (investment_allocation4 / total_allocation);
                    investment_fund5 += downsizeProceeds5 * (investment_allocation5 / total_allocation);
                } else {
                    // If no allocation percentages are specified, distribute equally
                    investment_fund1 += downsizeProceeds5 * 0.2;
                    investment_fund2 += downsizeProceeds5 * 0.2;
                    investment_fund3 += downsizeProceeds5 * 0.2;
                    investment_fund4 += downsizeProceeds5 * 0.2;
                    investment_fund5 += downsizeProceeds5 * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                savings_fund += downsizeProceeds5;
            }

            // Update property value and store downsizing metrics
            property_value5 = inflatedNewPropertyValue5;

            // Store downsizing event metrics
            metricArrays[i]['Property Sale Proceeds 5'].push(downsizeProceeds5 + transactionCosts5);
            metricArrays[i]['Transaction Costs 5'].push(transactionCosts5);
            metricArrays[i]['Debt Paid 5'].push(scenarioData.pay_off_debt5 ? Math.min(debt_value5, downsizeProceeds5) : 0);
        }

        // Add property debt repayments to total expenditure
        if (monthly_debt_repayment > 0) {
            totalExpenditure += monthly_debt_repayment * 12;
        }
        if (monthly_debt_repayment2 > 0) {
            totalExpenditure += monthly_debt_repayment2 * 12;
        }
        if (monthly_debt_repayment3 > 0) {
            totalExpenditure += monthly_debt_repayment3 * 12;
        }
        if (monthly_debt_repayment4 > 0) {
            totalExpenditure += monthly_debt_repayment4 * 12;
        }
        if (monthly_debt_repayment5 > 0) {
            totalExpenditure += monthly_debt_repayment5 * 12;
        }

        // Check if KiwiSaver should be consolidated for main person or partner
        const shouldConsolidateMain = scenarioData.consolidate_kiwisaver &&
            age === (scenarioData.main_consolidate_kiwisaver_age || 65);
        const shouldConsolidatePartner = partner && scenarioData.partner_consolidate_kiwisaver &&
            partner_age === (scenarioData.partner_consolidate_kiwisaver_age || 65);

        // Handle main person's KiwiSaver consolidation
        if (shouldConsolidateMain && kiwisaver_fund > 0) {
            const consolidationAllocations = scenarioData.consolidation_allocations || [];
            const totalAllocation = consolidationAllocations.reduce((sum: any, alloc: { percentage: any; }) => sum + (alloc.percentage || 0), 0);

            if (totalAllocation > 0) {
                // Distribute KiwiSaver fund according to allocations
                consolidationAllocations.forEach((alloc: { fundNumber: any; percentage: number; }) => {
                    const fundIndex = alloc.fundNumber;
                    const percentage = alloc.percentage / 100;
                    switch(fundIndex) {
                        case 1: investment_fund1 += kiwisaver_fund * percentage; break;
                        case 2: investment_fund2 += kiwisaver_fund * percentage; break;
                        case 3: investment_fund3 += kiwisaver_fund * percentage; break;
                        case 4: investment_fund4 += kiwisaver_fund * percentage; break;
                        case 5: investment_fund5 += kiwisaver_fund * percentage; break;
                    }
                });
            } else {
                // Default to equal distribution if no allocations specified
                investment_fund1 += kiwisaver_fund * 0.2;
                investment_fund2 += kiwisaver_fund * 0.2;
                investment_fund3 += kiwisaver_fund * 0.2;
                investment_fund4 += kiwisaver_fund * 0.2;
                investment_fund5 += kiwisaver_fund * 0.2;
            }
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            kiwisaver_fund = 0;
        }

        // Handle partner's KiwiSaver consolidation
        if (shouldConsolidatePartner && partner_kiwisaver_fund > 0) {
            const partnerConsolidationAllocations = scenarioData.partner_consolidation_allocations || [];
            const totalAllocation = partnerConsolidationAllocations.reduce((sum: any, alloc: { percentage: any; }) => sum + (alloc.percentage || 0), 0);

            if (totalAllocation > 0) {
                // Distribute partner's KiwiSaver fund according to allocations
                partnerConsolidationAllocations.forEach((alloc: { fundNumber: any; percentage: number; }) => {
                    const fundIndex = alloc.fundNumber;
                    const percentage = alloc.percentage / 100;
                    switch(fundIndex) {
                        case 1: investment_fund1 += partner_kiwisaver_fund * percentage; break;
                        case 2: investment_fund2 += partner_kiwisaver_fund * percentage; break;
                        case 3: investment_fund3 += partner_kiwisaver_fund * percentage; break;
                        case 4: investment_fund4 += partner_kiwisaver_fund * percentage; break;
                        case 5: investment_fund5 += partner_kiwisaver_fund * percentage; break;
                    }
                });
            } else {
                // Default to equal distribution if no allocations specified
                investment_fund1 += partner_kiwisaver_fund * 0.2;
                investment_fund2 += partner_kiwisaver_fund * 0.2;
                investment_fund3 += partner_kiwisaver_fund * 0.2;
                investment_fund4 += partner_kiwisaver_fund * 0.2;
                investment_fund5 += partner_kiwisaver_fund * 0.2;
            }
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            partner_kiwisaver_fund = 0;
        }

        // Set contribution rates to 0 if KiwiSaver has been consolidated
        const mainKsConsolidated = scenarioData.consolidate_kiwisaver &&
            age > (scenarioData.main_consolidate_kiwisaver_age || 65);
        const partnerKsConsolidated = partner && scenarioData.partner_consolidate_kiwisaver &&
            partner_age > (scenarioData.partner_consolidate_kiwisaver_age || 65);

        // Set contribution rates to 0 if KiwiSaver has been consolidated
        let effective_kiwisaver_contribution = mainKsConsolidated ? 0 : kiwisaver_contribution;
        let effective_employer_contribution = mainKsConsolidated ? 0 : employer_contribution;
        let effective_partner_kiwisaver_contribution = partnerKsConsolidated ? 0 : partner_kiwisaver_contribution;
        let effective_partner_employer_contribution = partnerKsConsolidated ? 0 : partner_employer_contribution;

        // Yearly Saving - only contribute if person is alive
        const kiwisaver_contrib = !mainPersonDeceased ? ((effective_kiwisaver_contribution / 100) * kiwisaver_eligible_main_income) : 0;
        const partner_kiwisaver_contrib = partner && !partnerDeceased ? ((effective_partner_kiwisaver_contribution / 100) * kiwisaver_eligible_partner_income) : 0;

        // Calculate employer contributions - only contribute if person is alive
        const employer_ks_contrib = !mainPersonDeceased ? ((effective_employer_contribution / 100) * kiwisaver_eligible_main_income) : 0;
        const partner_employer_ks_contrib = partner && !partnerDeceased ? ((effective_partner_employer_contribution / 100) * kiwisaver_eligible_partner_income) : 0;

        // Add contributions to KiwiSaver funds
        kiwisaver_fund += kiwisaver_contrib + employer_ks_contrib;
        if (partner) {
            partner_kiwisaver_fund += partner_kiwisaver_contrib + partner_employer_ks_contrib;
        }

        // Store contribution values for tracking with separate employee and employer contributions
        main_employee_kiwisaver.push(kiwisaver_contrib);
        main_employer_kiwisaver.push(employer_ks_contrib);
        partner_employee_kiwisaver.push(partner_kiwisaver_contrib);
        partner_employer_kiwisaver.push(partner_employer_ks_contrib);
        kiwisaver_contributions.push(kiwisaver_contrib);
        partner_kiwisaver_contributions.push(partner_kiwisaver_contrib);

        totalExpenditure += kiwisaver_contrib

        let yearly_saving = net_income - totalExpenditure;

        // Check if we're in the contribution period for each fund
        // Fund 1
        const isInContributionPeriod1 = (scenarioData as any).contribution_period1 ?
            (age >= (scenarioData as any).contribution_period1[0] && age <= (scenarioData as any).contribution_period1[1]) :
            (scenarioData.contribution_period && age >= scenarioData.contribution_period[0] && age <= scenarioData.contribution_period[1]);

        // Fund 2
        const isInContributionPeriod2 = (scenarioData as any).contribution_period2 ?
            (age >= (scenarioData as any).contribution_period2[0] && age <= (scenarioData as any).contribution_period2[1]) :
            false;

        // Fund 3
        const isInContributionPeriod3 = (scenarioData as any).contribution_period3 ?
            (age >= (scenarioData as any).contribution_period3[0] && age <= (scenarioData as any).contribution_period3[1]) :
            false;

        // Fund 4
        const isInContributionPeriod4 = (scenarioData as any).contribution_period4 ?
            (age >= (scenarioData as any).contribution_period4[0] && age <= (scenarioData as any).contribution_period4[1]) :
            false;

        // Fund 5
        const isInContributionPeriod5 = (scenarioData as any).contribution_period5 ?
            (age >= (scenarioData as any).contribution_period5[0] && age <= (scenarioData as any).contribution_period5[1]) :
            false;

        // Calculate potential contributions for each fund
        const potentialContribution1 = isInContributionPeriod1 ?
            (scenarioData.annual_investment_contribution1 !== undefined ?
                scenarioData.annual_investment_contribution1 :
                (scenarioData.annual_investment_contribution || 0)) : 0;

        const potentialContribution2 = isInContributionPeriod2 ?
            (scenarioData.annual_investment_contribution2 || 0) : 0;

        const potentialContribution3 = isInContributionPeriod3 ?
            (scenarioData.annual_investment_contribution3 || 0) : 0;

        const potentialContribution4 = isInContributionPeriod4 ?
            (scenarioData.annual_investment_contribution4 || 0) : 0;

        const potentialContribution5 = isInContributionPeriod5 ?
            (scenarioData.annual_investment_contribution5 || 0) : 0;

        // Total contribution across all funds
        const totalContribution = potentialContribution1 + potentialContribution2 +
                                 potentialContribution3 + potentialContribution4 +
                                 potentialContribution5;

        // Update Savings and Investment Funds with fund-specific contributions
        if (totalContribution > 0) {
            // Deduct total contribution from yearly savings
            yearly_saving -= totalContribution;

            // Add contributions to each fund
            investment_fund1 += potentialContribution1;
            investment_fund2 += potentialContribution2;
            investment_fund3 += potentialContribution3;
            investment_fund4 += potentialContribution4;
            investment_fund5 += potentialContribution5;

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
        }

        // Check for one-off investments
        for (const investment of one_off_investments) {
            if (investment.age === age) {
                // Process the one-off investment

                // Check if specificFund property exists and is not undefined
                if ('specificFund' in investment && investment.specificFund !== undefined) {
                    // Allocate to a specific fund
                    const fundNumber = investment.specificFund;

                    switch (fundNumber) {
                        case 1:
                            investment_fund1 += investment.amount;
                            break;
                        case 2:
                            investment_fund2 += investment.amount;
                            break;
                        case 3:
                            investment_fund3 += investment.amount;
                            break;
                        case 4:
                            investment_fund4 += investment.amount;
                            break;
                        case 5:
                            investment_fund5 += investment.amount;
                            break;
                        default:
                            // Fallback to equal distribution if invalid fund number
                            investment_fund1 += investment.amount * 0.2;
                            investment_fund2 += investment.amount * 0.2;
                            investment_fund3 += investment.amount * 0.2;
                            investment_fund4 += investment.amount * 0.2;
                            investment_fund5 += investment.amount * 0.2;
                    }
                }
                // Check if allocations property exists and is not undefined
                else if ('allocations' in investment && investment.allocations !== undefined) {
                    // Custom percentage allocation
                    const allocations = investment.allocations;

                    // Calculate total allocation to normalize if needed
                    const totalAllocation =
                        (allocations.fund1 || 0) +
                        (allocations.fund2 || 0) +
                        (allocations.fund3 || 0) +
                        (allocations.fund4 || 0) +
                        (allocations.fund5 || 0);

                    if (totalAllocation > 0) {
                        // Distribute according to normalized allocations
                        investment_fund1 += investment.amount * ((allocations.fund1 || 0) / totalAllocation);
                        investment_fund2 += investment.amount * ((allocations.fund2 || 0) / totalAllocation);
                        investment_fund3 += investment.amount * ((allocations.fund3 || 0) / totalAllocation);
                        investment_fund4 += investment.amount * ((allocations.fund4 || 0) / totalAllocation);
                        investment_fund5 += investment.amount * ((allocations.fund5 || 0) / totalAllocation);
                    } else {
                        // Fallback to equal distribution if total allocation is 0
                        investment_fund1 += investment.amount * 0.2;
                        investment_fund2 += investment.amount * 0.2;
                        investment_fund3 += investment.amount * 0.2;
                        investment_fund4 += investment.amount * 0.2;
                        investment_fund5 += investment.amount * 0.2;
                    }
                } else {
                    // Default: equal distribution across all 5 investment buckets
                    investment_fund1 += investment.amount * 0.2;
                    investment_fund2 += investment.amount * 0.2;
                    investment_fund3 += investment.amount * 0.2;
                    investment_fund4 += investment.amount * 0.2;
                    investment_fund5 += investment.amount * 0.2;
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            }
        }


        // Generate random returns for investments and KiwiSaver for this scenario and year
        const getInvestmentReturn = (age: number, bucketNumber: number = 0) => {
            // If bucketNumber is specified (1-5), use the corresponding bucket's parameters
            // If bucketNumber is 0, use the legacy parameters

            // Determine which fund periods to use based on bucket number
            let fundPeriods;
            let annualReturn;
            let stdDev;

            if (bucketNumber === 1) {
                fundPeriods = data.fund_periods1;
                annualReturn = scenarioData.annual_investment_return1 || 5.0;
                stdDev = scenarioData.inv_std_dev1 || 0.0;
            } else if (bucketNumber === 2) {
                fundPeriods = data.fund_periods2;
                annualReturn = scenarioData.annual_investment_return2 || 5.0;
                stdDev = scenarioData.inv_std_dev2 || 0.0;
            } else if (bucketNumber === 3) {
                fundPeriods = data.fund_periods3;
                annualReturn = scenarioData.annual_investment_return3 || 5.0;
                stdDev = scenarioData.inv_std_dev3 || 0.0;
            } else if (bucketNumber === 4) {
                fundPeriods = data.fund_periods4;
                annualReturn = scenarioData.annual_investment_return4 || 5.0;
                stdDev = scenarioData.inv_std_dev4 || 0.0;
            } else if (bucketNumber === 5) {
                fundPeriods = data.fund_periods5;
                annualReturn = scenarioData.annual_investment_return5 || 5.0;
                stdDev = scenarioData.inv_std_dev5 || 0.0;
            } else {
                // Legacy behavior (bucketNumber = 0)
                fundPeriods = data.fund_periods;
                annualReturn = scenarioData.annual_investment_return || 5.0;
                stdDev = scenarioData.inv_std_dev || 0.0;
            }

            // Check if we have fund periods for this bucket
            if (fundPeriods && fundPeriods.length > 0) {
                // Sort periods by start age
                const sortedPeriods = [...fundPeriods].sort((a, b) => a.period[0] - b.period[0]);

                // Find the period that contains the current age
                const period = sortedPeriods.find(p => age >= p.period[0] && age <= p.period[1]);

                if (period) {
                    // If standard deviation is 0, return the mean (no randomness)
                    // The isPrebuiltCustom check is removed as it's not needed - we just need to check stdDev
                    if (period.stdDev === 0) {
                        return (period.return - (scenarioData.inflation_rate || 0.0)) / 100;
                    }

                    // Use the period's return and stdDev values directly
                    // These values already come from either built-in fund types or custom funds
                    return normalRandom(
                        (period.return - (scenarioData.inflation_rate || 0.0)) / 100,
                        (period.stdDev / 100),
                        random
                    );
                }
            }

            // If standard deviation is 0, return the mean (no randomness)
            if (stdDev === 0) {
                return (annualReturn - (scenarioData.inflation_rate || 0.0)) / 100;
            }

            // Fallback to default calculation if no fund period matches
            return normalRandom(
                (annualReturn - (scenarioData.inflation_rate || 0.0)) / 100,
                (stdDev / 100),
                random
            );
        };

        const getKiwiSaverReturn = (age: number, isPartner: boolean = false) => {
            const periods = isPartner ? data.partner_ks_periods : data.ks_periods;
            if (periods && periods.length > 0) {
                // Sort periods by start age
                const sortedPeriods = [...periods].sort((a, b) => a.period[0] - b.period[0]);

                // Find the period that contains the current age
                const period = sortedPeriods.find(p => age >= p.period[0] && age <= p.period[1]);

                if (period) {
                    // If standard deviation is 0, return the mean (no randomness)
                    // This ensures that custom funds with stdDev of 0 work correctly
                    if (period.stdDev === 0) {
                        return (period.return - (scenarioData.inflation_rate || 0.0)) / 100;
                    }

                    // Use the period's return and stdDev values directly
                    // These values already come from either built-in fund types or custom funds
                    return normalRandom(
                        (period.return - (scenarioData.inflation_rate || 0.0)) / 100,
                        (period.stdDev / 100),
                        random
                    );
                }
            }

            // Fallback to default calculation if no fund period matches
            const defaultReturn = isPartner ?
                (scenarioData.partner_annual_kiwisaver_return || 5.0) :
                (scenarioData.annual_kiwisaver_return || 3.0);
            const defaultStdDev = isPartner ?
                (scenarioData.partner_ks_std_dev || 0.0) :
                (scenarioData.ks_std_dev || 0.0);

            // If standard deviation is 0, return the mean (no randomness)
            if (defaultStdDev === 0) {
                return (defaultReturn - (scenarioData.inflation_rate || 0.0)) / 100;
            }

            return normalRandom(
                (defaultReturn - (scenarioData.inflation_rate || 0.0)) / 100,
                (defaultStdDev / 100),
                random
            );
        };

        const average_inv_return = getInvestmentReturn(age);
        const average_ks_return = getKiwiSaverReturn(age);
        let partner_average_ks_return = partner ? getKiwiSaverReturn(partner_age, true) : 0;

        // KiwiSaver is always taxed as PIE
        // Note: kiwisaverReturn is calculated later with adjustedKsReturn


        // Determine PIE rate based on main taxable income
        let ks_pie_rate = 0.28; // Default to 28%
        if (main_only_taxable_income <= 14000) {
            ks_pie_rate = 0.105;
        } else if (main_only_taxable_income <= 48000) {
            ks_pie_rate = 0.175;
        } else {
            ks_pie_rate = 0.28; // Cap at 28% for higher incomes
        }



        // Update Savings and Investment Funds
        if (yearly_saving > 0 && scenarioData.utilise_excess_cashflow) {
            // Positive cashflow - handle savings and investments
            if (savings_fund < cash_reserve) {
                // If savings is below cash reserve, prioritize filling it up
                if (savings_fund + yearly_saving >= cash_reserve) {
                    // If we have enough to fill the cash reserve and have excess
                    const excess = savings_fund + yearly_saving - cash_reserve;
                    savings_fund = cash_reserve;

                    // If utilise_excess_cashflow is enabled, allocate excess according to percentages
                    if (scenarioData.utilise_excess_cashflow) {
                        // Get the savings allocation percentage (default to 20% if not set)
                        const savingsAllocation = scenarioData.savings_allocation !== undefined ? scenarioData.savings_allocation / 100 : 0.2;

                        // Get active investment funds from withdrawal priorities
                        const activeFunds = scenarioData.withdrawal_priorities || [];

                        // Initialize allocation percentages for each fund
                        let allocation1 = 0;
                        let allocation2 = 0;
                        let allocation3 = 0;
                        let allocation4 = 0;
                        let allocation5 = 0;

                        // Only set allocation for active funds
                        if (activeFunds.includes(1)) {
                            allocation1 = scenarioData.investment_allocation1 !== undefined ? scenarioData.investment_allocation1 / 100 : 0.2;
                        }
                        if (activeFunds.includes(2)) {
                            allocation2 = scenarioData.investment_allocation2 !== undefined ? scenarioData.investment_allocation2 / 100 : 0.2;
                        }
                        if (activeFunds.includes(3)) {
                            allocation3 = scenarioData.investment_allocation3 !== undefined ? scenarioData.investment_allocation3 / 100 : 0.2;
                        }
                        if (activeFunds.includes(4)) {
                            allocation4 = scenarioData.investment_allocation4 !== undefined ? scenarioData.investment_allocation4 / 100 : 0.2;
                        }
                        if (activeFunds.includes(5)) {
                            allocation5 = scenarioData.investment_allocation5 !== undefined ? scenarioData.investment_allocation5 / 100 : 0.2;
                        }

                        // Calculate total allocation to normalize
                        const totalAllocation = savingsAllocation + allocation1 + allocation2 + allocation3 + allocation4 + allocation5;

                        // Add to savings fund based on normalized savings allocation
                        if (totalAllocation > 0) {
                            savings_fund += excess * (savingsAllocation / totalAllocation);
                        }

                        // Distribute the excess cashflow according to normalized allocation percentages
                        if (totalAllocation > 0) {
                            if (allocation1 > 0) investment_fund1 += excess * (allocation1 / totalAllocation);
                            if (allocation2 > 0) investment_fund2 += excess * (allocation2 / totalAllocation);
                            if (allocation3 > 0) investment_fund3 += excess * (allocation3 / totalAllocation);
                            if (allocation4 > 0) investment_fund4 += excess * (allocation4 / totalAllocation);
                            if (allocation5 > 0) investment_fund5 += excess * (allocation5 / totalAllocation);
                        }

                        // Update the total investment fund
                        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
                    } else {
                        // Otherwise, all excess goes to savings
                        savings_fund += excess;
                    }
                } else {
                    // Not enough to reach cash reserve, all goes to savings
                    savings_fund += yearly_saving;
                }
            } else if (scenarioData.utilise_excess_cashflow) {
                // Savings already meets cash reserve and utilise_excess_cashflow is enabled
                // Get the savings allocation percentage (default to 20% if not set)
                const savingsAllocation = scenarioData.savings_allocation !== undefined ? scenarioData.savings_allocation / 100 : 0.2;

                // Get active investment funds from withdrawal priorities
                const activeFunds = scenarioData.withdrawal_priorities || [];

                // Initialize allocation percentages for each fund
                let allocation1 = 0;
                let allocation2 = 0;
                let allocation3 = 0;
                let allocation4 = 0;
                let allocation5 = 0;

                // Only set allocation for active funds
                if (activeFunds.includes(1)) {
                    allocation1 = scenarioData.investment_allocation1 !== undefined ? scenarioData.investment_allocation1 / 100 : 0.2;
                }
                if (activeFunds.includes(2)) {
                    allocation2 = scenarioData.investment_allocation2 !== undefined ? scenarioData.investment_allocation2 / 100 : 0.2;
                }
                if (activeFunds.includes(3)) {
                    allocation3 = scenarioData.investment_allocation3 !== undefined ? scenarioData.investment_allocation3 / 100 : 0.2;
                }
                if (activeFunds.includes(4)) {
                    allocation4 = scenarioData.investment_allocation4 !== undefined ? scenarioData.investment_allocation4 / 100 : 0.2;
                }
                if (activeFunds.includes(5)) {
                    allocation5 = scenarioData.investment_allocation5 !== undefined ? scenarioData.investment_allocation5 / 100 : 0.2;
                }

                // Calculate total allocation to normalize
                const totalAllocation = savingsAllocation + allocation1 + allocation2 + allocation3 + allocation4 + allocation5;

                // Add to savings fund based on normalized savings allocation
                if (totalAllocation > 0) {
                    savings_fund += yearly_saving * (savingsAllocation / totalAllocation);
                }

                // Distribute the yearly saving according to normalized allocation percentages
                if (totalAllocation > 0) {
                    if (allocation1 > 0) investment_fund1 += yearly_saving * (allocation1 / totalAllocation);
                    if (allocation2 > 0) investment_fund2 += yearly_saving * (allocation2 / totalAllocation);
                    if (allocation3 > 0) investment_fund3 += yearly_saving * (allocation3 / totalAllocation);
                    if (allocation4 > 0) investment_fund4 += yearly_saving * (allocation4 / totalAllocation);
                    if (allocation5 > 0) investment_fund5 += yearly_saving * (allocation5 / totalAllocation);
                }

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            } else {
                // Savings already meets cash reserve but utilise_excess_cashflow is disabled
                savings_fund += yearly_saving;
            }
        } else if (yearly_saving < 0) {
            // Negative cashflow - handle drawdowns in the correct order
            let remainingDrawdown = Math.abs(yearly_saving);

            // 1. First, take from cash above the cash reserve
            const excessCash = Math.max(0, savings_fund - cash_reserve);
            if (excessCash > 0) {
                const drawFromExcessCash = Math.min(excessCash, remainingDrawdown);
                savings_fund -= drawFromExcessCash;
                remainingDrawdown -= drawFromExcessCash;
            }

            // 2. Then, take from investment funds if there's still a remaining drawdown
            if (remainingDrawdown > 0 && (investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5) > 0) {
                // We'll withdraw from each fund in priority order up to the remaining drawdown amount

                // Get the withdrawal priorities
                const withdrawalPriorities = (scenarioData as any).withdrawal_priorities || [1, 2, 3, 4, 5];

                // Create a map of fund values for easier access
                const fundValues = {
                    1: investment_fund1,
                    2: investment_fund2,
                    3: investment_fund3,
                    4: investment_fund4,
                    5: investment_fund5
                };

                // Withdraw from each fund in priority order
                let totalWithdrawn = 0;

                for (const fundNumber of withdrawalPriorities) {
                    if (remainingDrawdown <= 0) break;

                    // Get the current fund value
                    const currentFundValue = fundValues[fundNumber as keyof typeof fundValues];

                    if (currentFundValue > 0) {
                        // Calculate how much to withdraw from this fund
                        const withdrawAmount = Math.min(currentFundValue, remainingDrawdown);

                        // Update the fund value
                        fundValues[fundNumber as keyof typeof fundValues] -= withdrawAmount;

                        // Update the remaining drawdown
                        remainingDrawdown -= withdrawAmount;
                        totalWithdrawn += withdrawAmount;
                    }
                }

                // Update the individual fund values
                investment_fund1 = fundValues[1];
                investment_fund2 = fundValues[2];
                investment_fund3 = fundValues[3];
                investment_fund4 = fundValues[4];
                investment_fund5 = fundValues[5];

                // Update the total investment fund
                investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

                remainingDrawdown -= totalWithdrawn;
            }

            // 3. Then, take from KiwiSaver if over 65 and there's still a remaining drawdown
            if (remainingDrawdown > 0 && age >= 65) {
                // Recalculate total KiwiSaver to ensure it's accurate
                total_kiwisaver_fund = kiwisaver_fund + (partner ? partner_kiwisaver_fund : 0);

                if (total_kiwisaver_fund > 0) {
                    const drawFromKiwiSaver = Math.min(total_kiwisaver_fund, remainingDrawdown);

                    // Determine how much to take from each KiwiSaver account
                    if (drawFromKiwiSaver <= kiwisaver_fund) {
                        // If main KiwiSaver has enough funds
                        kiwisaver_fund -= drawFromKiwiSaver;
                    } else {
                        // If we need to draw from both KiwiSaver accounts
                        const remainingKsWithdrawal = drawFromKiwiSaver - kiwisaver_fund;
                        kiwisaver_fund = 0;
                        if (partner && partner_kiwisaver_fund > 0) {
                            partner_kiwisaver_fund = Math.max(0, partner_kiwisaver_fund - remainingKsWithdrawal);
                        }
                    }

                    remainingDrawdown -= drawFromKiwiSaver;

                    // Update total KiwiSaver after withdrawals
                    total_kiwisaver_fund = kiwisaver_fund + (partner ? partner_kiwisaver_fund : 0);
                }
            }

            // 4. Finally, take from cash reserve as a last resort
            if (remainingDrawdown > 0 && savings_fund > 0) {
                const drawFromCashReserve = Math.min(savings_fund, remainingDrawdown);
                savings_fund -= drawFromCashReserve;
                remainingDrawdown -= drawFromCashReserve;
            }
        }

        // Reset the insurance payout tracker for the next year
        // We'll use this at the beginning of each year's loop
        currentYearInsurancePayout = 0;

        // Update Investment and KiwiSaver Funds with returns
        // Get returns for each investment bucket
        let inv_return1 = getInvestmentReturn(age, 1);
        let inv_return2 = getInvestmentReturn(age, 2);
        let inv_return3 = getInvestmentReturn(age, 3);
        let inv_return4 = getInvestmentReturn(age, 4);
        let inv_return5 = getInvestmentReturn(age, 5);

        // Adjust returns based on recession status
        let adjustedInvReturn = average_inv_return;
        let adjustedInvReturn1 = inv_return1;
        let adjustedInvReturn2 = inv_return2;
        let adjustedInvReturn3 = inv_return3;
        let adjustedInvReturn4 = inv_return4;
        let adjustedInvReturn5 = inv_return5;
        let adjustedKsReturn = average_ks_return;
        let adjustedPartnerKsReturn = partner_average_ks_return;
        // These variables are kept for potential future use in recession calculations
        // but are currently not used directly

        // If in a recession rebound period, implement a realistic recovery path
        if (recessionActive && age < recessionEndAge) {
            const yearsIntoRebound = age - recessionStartAge;
            const totalReboundYears = recessionEndAge - recessionStartAge;
            const progressThroughRebound = yearsIntoRebound / totalReboundYears;

            // Define realistic annual return caps based on rebound type
            // These are the maximum annual returns we'll allow during recovery
            const maxAnnualReturn = {
                good: 0.20,     // 20% max annual return for good recovery
                average: 0.15,  // 15% max annual return for average recovery
                bad: 0.10       // 10% max annual return for bad recovery
            };

            // Define realistic annual return floors based on rebound type
            // These are the minimum annual returns we'll allow during recovery
            const minAnnualReturn = {
                good: 0.05,     // 5% min annual return for good recovery
                average: 0.02,  // 2% min annual return for average recovery
                bad: -0.02      // -2% min annual return for bad recovery (can still be negative)
            };

            // Calculate how much of the recovery should happen this year based on rebound type and progress
            let recoveryWeight;
            if (recessionReboundType === 'good') {
                // Good rebound: Faster early recovery (front-loaded)
                // This creates a curve that recovers more quickly in the early years
                recoveryWeight = Math.pow(1 - progressThroughRebound, 0.7);
            } else if (recessionReboundType === 'bad') {
                // Bad rebound: Slower early recovery (back-loaded)
                // This creates a curve that recovers more slowly in the early years
                recoveryWeight = Math.pow(1 - progressThroughRebound, 1.3);
            } else {
                // Average rebound: Linear recovery
                recoveryWeight = 1 - progressThroughRebound;
            }

            // Calculate the annual returns based on the recovery type and progress
            // We'll use a weighted approach between min and max returns
            let baseInvReturn, baseKsReturn, basePartnerKsReturn;

            if (recessionReboundType === 'good') {
                // For good recovery, we start with higher returns and gradually normalize
                baseInvReturn = maxAnnualReturn.good * recoveryWeight + minAnnualReturn.good * (1 - recoveryWeight);
                baseKsReturn = maxAnnualReturn.good * recoveryWeight + minAnnualReturn.good * (1 - recoveryWeight);
                basePartnerKsReturn = maxAnnualReturn.good * recoveryWeight + minAnnualReturn.good * (1 - recoveryWeight);
            } else if (recessionReboundType === 'bad') {
                // For bad recovery, we start with lower returns and gradually improve
                baseInvReturn = minAnnualReturn.bad * recoveryWeight + maxAnnualReturn.bad * (1 - recoveryWeight);
                baseKsReturn = minAnnualReturn.bad * recoveryWeight + maxAnnualReturn.bad * (1 - recoveryWeight);
                basePartnerKsReturn = minAnnualReturn.bad * recoveryWeight + maxAnnualReturn.bad * (1 - recoveryWeight);
            } else {
                // For average recovery, we use a balanced approach
                baseInvReturn = (maxAnnualReturn.average + minAnnualReturn.average) / 2;
                baseKsReturn = (maxAnnualReturn.average + minAnnualReturn.average) / 2;
                basePartnerKsReturn = (maxAnnualReturn.average + minAnnualReturn.average) / 2;
            }

            // Add some randomness based on volatility (reduced during recovery)
            const volatilityFactor = 0.5; // Reduce volatility by 50% during recovery

            // Override the returns with our calculated recovery rates
            adjustedInvReturn = baseInvReturn + (random() * inv_std_dev * volatilityFactor) - (inv_std_dev * volatilityFactor / 2);
            adjustedKsReturn = baseKsReturn + (random() * ks_std_dev * volatilityFactor) - (ks_std_dev * volatilityFactor / 2);
            if (partner) {
                adjustedPartnerKsReturn = basePartnerKsReturn + (random() * partner_ks_std_dev * volatilityFactor) - (partner_ks_std_dev * volatilityFactor / 2);
            }

            // Ensure returns stay within realistic bounds
            let minReturn = minAnnualReturn.average; // Default to average
            let maxReturn = maxAnnualReturn.average;

            if (recessionReboundType === 'good') {
                minReturn = minAnnualReturn.good;
                maxReturn = maxAnnualReturn.good;
            } else if (recessionReboundType === 'bad') {
                minReturn = minAnnualReturn.bad;
                maxReturn = maxAnnualReturn.bad;
            }

            adjustedInvReturn = Math.max(minReturn, Math.min(maxReturn, adjustedInvReturn));
            adjustedKsReturn = Math.max(minReturn, Math.min(maxReturn, adjustedKsReturn));
            if (partner) {
                adjustedPartnerKsReturn = Math.max(minReturn, Math.min(maxReturn, adjustedPartnerKsReturn));
            }
        }

        // Calculate returns with adjusted rates for each investment bucket
        // Only calculate returns for active funds (those in the withdrawal priorities)
        const investmentReturn1 = withdrawalPriorities.includes(1) ? investment_fund1 * adjustedInvReturn1 : 0;
        const investmentReturn2 = withdrawalPriorities.includes(2) ? investment_fund2 * adjustedInvReturn2 : 0;
        const investmentReturn3 = withdrawalPriorities.includes(3) ? investment_fund3 * adjustedInvReturn3 : 0;
        const investmentReturn4 = withdrawalPriorities.includes(4) ? investment_fund4 * adjustedInvReturn4 : 0;
        const investmentReturn5 = withdrawalPriorities.includes(5) ? investment_fund5 * adjustedInvReturn5 : 0;


        // Total investment return (sum of all buckets)
        const investmentReturn = investmentReturn1 + investmentReturn2 + investmentReturn3 + investmentReturn4 + investmentReturn5;

        // KiwiSaver returns
        const kiwisaverReturn = kiwisaver_fund * adjustedKsReturn;
        let partnerKiwisaverReturn = partner ? partner_kiwisaver_fund * adjustedPartnerKsReturn : 0;

        // Store individual KiwiSaver returns for tracking
        const mainKiwiSaverReturn = kiwisaverReturn;
        const partnerKiwiSaverReturn = partnerKiwisaverReturn;

        // Push individual KiwiSaver returns to their respective arrays
        main_kiwisaver_return_list.push(mainKiwiSaverReturn);
        partner_kiwisaver_return_list.push(partnerKiwiSaverReturn);

        // Investment Taxation
        // For taxation purposes, we'll use the individual investment fund returns

        // Calculate the taxable portion of the investment return
        // Insurance payouts are tax-free, so we exclude them from taxation
        // We only tax the return on the investment, not the insurance payout itself
        // Apply income portion to determine taxable amount (default to 60% if not specified)


        // Get individual fund income portions from fund periods if available
        // Use default values based on fund type if not specified
        let incomePortion1 = 0.6; // Default to 60% for balanced funds
        let incomePortion2 = 0.6;
        let incomePortion3 = 0.6;
        let incomePortion4 = 0.6;
        let incomePortion5 = 0.6;

        // Check if we have fund periods with income portion values
        if (scenarioData.fund_periods && scenarioData.fund_periods.length > 0) {
            // Find the current period for fund 1
            const currentPeriod1 = scenarioData.fund_periods.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentPeriod1 && currentPeriod1.incomePortion !== undefined) {
                incomePortion1 = currentPeriod1.incomePortion / 100;
            } else if (scenarioData.fund1_income_portion !== undefined) {
                incomePortion1 = scenarioData.fund1_income_portion / 100;
            }
        } else if (scenarioData.fund1_income_portion !== undefined) {
            incomePortion1 = scenarioData.fund1_income_portion / 100;
        }

        // Check fund 2 periods
        if (scenarioData.fund_periods2 && scenarioData.fund_periods2.length > 0) {
            const currentPeriod2 = scenarioData.fund_periods2.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentPeriod2 && currentPeriod2.incomePortion !== undefined) {
                incomePortion2 = currentPeriod2.incomePortion / 100;
            } else if (scenarioData.fund2_income_portion !== undefined) {
                incomePortion2 = scenarioData.fund2_income_portion / 100;
            }
        } else if (scenarioData.fund2_income_portion !== undefined) {
            incomePortion2 = scenarioData.fund2_income_portion / 100;
        }

        // Check fund 3 periods
        if (scenarioData.fund_periods3 && scenarioData.fund_periods3.length > 0) {
            const currentPeriod3 = scenarioData.fund_periods3.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentPeriod3 && currentPeriod3.incomePortion !== undefined) {
                incomePortion3 = currentPeriod3.incomePortion / 100;
            } else if (scenarioData.fund3_income_portion !== undefined) {
                incomePortion3 = scenarioData.fund3_income_portion / 100;
            }
        } else if (scenarioData.fund3_income_portion !== undefined) {
            incomePortion3 = scenarioData.fund3_income_portion / 100;
        }

        // Check fund 4 periods
        if (scenarioData.fund_periods4 && scenarioData.fund_periods4.length > 0) {
            const currentPeriod4 = scenarioData.fund_periods4.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentPeriod4 && currentPeriod4.incomePortion !== undefined) {
                incomePortion4 = currentPeriod4.incomePortion / 100;
            } else if (scenarioData.fund4_income_portion !== undefined) {
                incomePortion4 = scenarioData.fund4_income_portion / 100;
            }
        } else if (scenarioData.fund4_income_portion !== undefined) {
            incomePortion4 = scenarioData.fund4_income_portion / 100;
        }

        // Check fund 5 periods
        if (scenarioData.fund_periods5 && scenarioData.fund_periods5.length > 0) {
            const currentPeriod5 = scenarioData.fund_periods5.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentPeriod5 && currentPeriod5.incomePortion !== undefined) {
                incomePortion5 = currentPeriod5.incomePortion / 100;
            } else if (scenarioData.fund5_income_portion !== undefined) {
                incomePortion5 = scenarioData.fund5_income_portion / 100;
            }
        } else if (scenarioData.fund5_income_portion !== undefined) {
            incomePortion5 = scenarioData.fund5_income_portion / 100;
        }


        // Add returns to each investment bucket first
        investment_fund1 += investmentReturn1;
        investment_fund2 += investmentReturn2;
        investment_fund3 += investmentReturn3;
        investment_fund4 += investmentReturn4;
        investment_fund5 += investmentReturn5;

                // Track the investment returns
        investment_return_list1.push(investmentReturn1);
        investment_return_list2.push(investmentReturn2);
        investment_return_list3.push(investmentReturn3);
        investment_return_list4.push(investmentReturn4);
        investment_return_list5.push(investmentReturn5);

        // Update the total investment fund
        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

        // Initialize fund tax values for this year (will be updated if tax is applied)
        let tax1 = 0;
        let tax2 = 0;
        let tax3 = 0;
        let tax4 = 0;
        let tax5 = 0;

        // Apply income portion to each fund's return
        // Only calculate taxable returns for active funds
        const taxableInvestmentReturn1 = withdrawalPriorities.includes(1) ? investmentReturn1 * incomePortion1 : 0;
        const taxableInvestmentReturn2 = withdrawalPriorities.includes(2) ? investmentReturn2 * incomePortion2 : 0;
        const taxableInvestmentReturn3 = withdrawalPriorities.includes(3) ? investmentReturn3 * incomePortion3 : 0;
        const taxableInvestmentReturn4 = withdrawalPriorities.includes(4) ? investmentReturn4 * incomePortion4 : 0;
        const taxableInvestmentReturn5 = withdrawalPriorities.includes(5) ? investmentReturn5 * incomePortion5 : 0;

        // Total taxable investment return
        const taxableInvestmentReturn = taxableInvestmentReturn1 + taxableInvestmentReturn2 +
            taxableInvestmentReturn3 + taxableInvestmentReturn4 + taxableInvestmentReturn5;


        if (inv_tax === 'PIE') {
            // For PIE investments, use the person's total taxable income to determine PIE rate
            let total_taxable_income = main_only_taxable_income;
            let pie_rate = 0.28; // Default to 28%

            // Determine PIE rate based on total income
            if (total_taxable_income <= 14000) {
                pie_rate = 0.105;
            } else if (total_taxable_income <= 48000) {
                pie_rate = 0.175;
            } else if (total_taxable_income <= 70000) {
                pie_rate = 0.28;
            } else {
                pie_rate = 0.28; // Cap at 28% for higher incomes
            }

            const pie_tax_total = taxableInvestmentReturn * pie_rate;


            // Calculate tax for each bucket based on its taxable return
            if (taxableInvestmentReturn > 0) {
                // Calculate tax for each bucket based on its taxable return, not fund balance
                tax1 = withdrawalPriorities.includes(1) ? pie_rate * taxableInvestmentReturn1 : 0;
                tax2 = withdrawalPriorities.includes(2) ? pie_rate * taxableInvestmentReturn2 : 0;
                tax3 = withdrawalPriorities.includes(3) ? pie_rate * taxableInvestmentReturn3 : 0;
                tax4 = withdrawalPriorities.includes(4) ? pie_rate * taxableInvestmentReturn4 : 0;
                tax5 = withdrawalPriorities.includes(5) ? pie_rate * taxableInvestmentReturn5 : 0;

                // Apply tax to each bucket, but only for active funds
                if (withdrawalPriorities.includes(1)) investment_fund1 -= tax1;
                if (withdrawalPriorities.includes(2)) investment_fund2 -= tax2;
                if (withdrawalPriorities.includes(3)) investment_fund3 -= tax3;
                if (withdrawalPriorities.includes(4)) investment_fund4 -= tax4;
                if (withdrawalPriorities.includes(5)) investment_fund5 -= tax5;

            }

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

            pie_tax.push(pie_tax_total);
            mtr_tax.push(0);
        } else {
            // For MTR investments, add investment return to taxable income
            let total_taxable_income = main_only_taxable_income + taxableInvestmentReturn;

            // Calculate tax on total income (including investment return)
            const mtr_tax_total = calculateProgressiveTax(total_taxable_income, tax_brackets, tax_thresholds);

            // Calculate tax on main taxable income only
            const main_income_only_tax = calculateProgressiveTax(main_only_taxable_income, tax_brackets, tax_thresholds);

            // The difference is the tax on investment return
            const investment_tax = mtr_tax_total - main_income_only_tax;

            // Calculate tax for each bucket based on its taxable return
            if (taxableInvestmentReturn > 0) {
                // Calculate the effective tax rate on the total taxable return
                const effectiveTaxRate = investment_tax / taxableInvestmentReturn;

                // Apply this rate to each fund's taxable return
                tax1 = withdrawalPriorities.includes(1) ? effectiveTaxRate * taxableInvestmentReturn1 : 0;
                tax2 = withdrawalPriorities.includes(2) ? effectiveTaxRate * taxableInvestmentReturn2 : 0;
                tax3 = withdrawalPriorities.includes(3) ? effectiveTaxRate * taxableInvestmentReturn3 : 0;
                tax4 = withdrawalPriorities.includes(4) ? effectiveTaxRate * taxableInvestmentReturn4 : 0;
                tax5 = withdrawalPriorities.includes(5) ? effectiveTaxRate * taxableInvestmentReturn5 : 0;

                // Apply tax to each bucket, but only for active funds
                if (withdrawalPriorities.includes(1)) investment_fund1 -= tax1;
                if (withdrawalPriorities.includes(2)) investment_fund2 -= tax2;
                if (withdrawalPriorities.includes(3)) investment_fund3 -= tax3;
                if (withdrawalPriorities.includes(4)) investment_fund4 -= tax4;
                if (withdrawalPriorities.includes(5)) investment_fund5 -= tax5;

            }

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

            mtr_tax.push(investment_tax);
            pie_tax.push(0);
        }


        // Get KiwiSaver income portion (default to 60% if not specified)
        let kiwisaverIncomePortion = 0.6;

        // Check if we have KiwiSaver periods with income portion values
        if (scenarioData.ks_periods && scenarioData.ks_periods.length > 0) {
            // Find the current period for KiwiSaver
            const currentKsPeriod = scenarioData.ks_periods.find((p: any) =>
                age >= p.period[0] && age <= p.period[1]);
            if (currentKsPeriod && currentKsPeriod.incomePortion !== undefined) {
                kiwisaverIncomePortion = currentKsPeriod.incomePortion / 100;
            } else if (scenarioData.kiwisaver_income_portion !== undefined) {
                kiwisaverIncomePortion = scenarioData.kiwisaver_income_portion / 100;
            }
        } else if (scenarioData.kiwisaver_income_portion !== undefined) {
            kiwisaverIncomePortion = scenarioData.kiwisaver_income_portion / 100;
        }

        // Update KiwiSaver funds with returns first
        kiwisaver_fund += kiwisaverReturn;

        // Apply income portion to KiwiSaver return
        const taxableKiwisaverReturn = kiwisaverReturn * kiwisaverIncomePortion;

        const ks_pie_tax = taxableKiwisaverReturn * ks_pie_rate;
        main_ks_tax.push(ks_pie_tax);

        kiwisaver_fund -= ks_pie_tax;


        let partner_ks_pie_tax = 0;
        if (partner) {
            const partner_kiwisaver_return = partner_kiwisaver_fund * partner_average_ks_return;


            // Get partner KiwiSaver income portion (default to 60% if not specified)
            let partnerKiwisaverIncomePortion = 0.6;

            // Check if we have partner KiwiSaver periods with income portion values
            if (scenarioData.partner_ks_periods && scenarioData.partner_ks_periods.length > 0) {
                // Find the current period for partner KiwiSaver
                const currentPartnerKsPeriod = scenarioData.partner_ks_periods.find((p: any) =>
                    partner_age >= p.period[0] && partner_age <= p.period[1]);
                if (currentPartnerKsPeriod && currentPartnerKsPeriod.incomePortion !== undefined) {
                    partnerKiwisaverIncomePortion = currentPartnerKsPeriod.incomePortion / 100;
                } else if (scenarioData.partner_kiwisaver_income_portion !== undefined) {
                    partnerKiwisaverIncomePortion = scenarioData.partner_kiwisaver_income_portion / 100;
                }
            } else if (scenarioData.partner_kiwisaver_income_portion !== undefined) {
                partnerKiwisaverIncomePortion = scenarioData.partner_kiwisaver_income_portion / 100;
            }

            // Update partner KiwiSaver fund with returns first
            partner_kiwisaver_fund += partner_kiwisaver_return;

            // Apply income portion to partner's KiwiSaver return
            const taxablePartnerKiwisaverReturn = partner_kiwisaver_return * partnerKiwisaverIncomePortion;

            // Determine PIE rate based on partner's taxable income
            let partner_ks_pie_rate = 0.28; // Default to 28%
            if (partner_taxable_income <= 14000) {
                partner_ks_pie_rate = 0.105;
            } else if (partner_taxable_income <= 48000) {
                partner_ks_pie_rate = 0.175;
            } else {
                partner_ks_pie_rate = 0.28; // Cap at 28% for higher incomes
            }

            partner_ks_pie_tax = taxablePartnerKiwisaverReturn * partner_ks_pie_rate;

            partner_kiwisaver_fund -= partner_ks_pie_tax;
            partner_ks_tax.push(partner_ks_pie_tax);
        }

        ks_tax.push(ks_pie_tax + partner_ks_pie_tax);

        // Investment and KiwiSaver returns have already been added to the funds before tax calculation


        // Process KiwiSaver consolidation after all contributions and returns
        // Note: This is a duplicate of the consolidation logic above
        // We're keeping both implementations to ensure backward compatibility
        // The consolidation should only happen once per age, so this is a safety check

        // Check if consolidation has already happened in this age
        const mainAlreadyConsolidated = scenarioData.consolidate_kiwisaver &&
            age === (scenarioData.main_consolidate_kiwisaver_age || 65) && kiwisaver_fund === 0;

        const partnerAlreadyConsolidated = partner && scenarioData.partner_consolidate_kiwisaver &&
            partner_age === (scenarioData.partner_consolidate_kiwisaver_age || 65) && partner_kiwisaver_fund === 0;

        // Only proceed with consolidation if it hasn't happened yet
        if (!mainAlreadyConsolidated && scenarioData.consolidate_kiwisaver &&
            age === (scenarioData.main_consolidate_kiwisaver_age || 65) && kiwisaver_fund > 0) {

            const consolidationAllocations = scenarioData.consolidation_allocations || [];
            const totalAllocation = consolidationAllocations.reduce((sum: any, alloc: { percentage: any; }) => sum + (alloc.percentage || 0), 0);

            if (totalAllocation > 0) {
                // Distribute KiwiSaver fund according to allocations
                consolidationAllocations.forEach((alloc: { fundNumber: any; percentage: number; }) => {
                    const fundIndex = alloc.fundNumber;
                    const percentage = alloc.percentage / 100;
                    switch(fundIndex) {
                        case 1: investment_fund1 += kiwisaver_fund * percentage; break;
                        case 2: investment_fund2 += kiwisaver_fund * percentage; break;
                        case 3: investment_fund3 += kiwisaver_fund * percentage; break;
                        case 4: investment_fund4 += kiwisaver_fund * percentage; break;
                        case 5: investment_fund5 += kiwisaver_fund * percentage; break;
                    }
                });
            } else {
                // Default to equal distribution if no allocations specified
                investment_fund1 += kiwisaver_fund * 0.2;
                investment_fund2 += kiwisaver_fund * 0.2;
                investment_fund3 += kiwisaver_fund * 0.2;
                investment_fund4 += kiwisaver_fund * 0.2;
                investment_fund5 += kiwisaver_fund * 0.2;
            }

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            kiwisaver_fund = 0;
        }

        if (!partnerAlreadyConsolidated && partner && scenarioData.partner_consolidate_kiwisaver &&
            partner_age === (scenarioData.partner_consolidate_kiwisaver_age || 65) && partner_kiwisaver_fund > 0) {

            const partnerConsolidationAllocations = scenarioData.partner_consolidation_allocations || [];
            const totalAllocation = partnerConsolidationAllocations.reduce((sum: any, alloc: { percentage: any; }) => sum + (alloc.percentage || 0), 0);

            if (totalAllocation > 0) {
                // Distribute partner's KiwiSaver fund according to allocations
                partnerConsolidationAllocations.forEach((alloc: { fundNumber: any; percentage: number; }) => {
                    const fundIndex = alloc.fundNumber;
                    const percentage = alloc.percentage / 100;
                    switch(fundIndex) {
                        case 1: investment_fund1 += partner_kiwisaver_fund * percentage; break;
                        case 2: investment_fund2 += partner_kiwisaver_fund * percentage; break;
                        case 3: investment_fund3 += partner_kiwisaver_fund * percentage; break;
                        case 4: investment_fund4 += partner_kiwisaver_fund * percentage; break;
                        case 5: investment_fund5 += partner_kiwisaver_fund * percentage; break;
                    }
                });
            } else {
                // Default to equal distribution if no allocations specified
                investment_fund1 += partner_kiwisaver_fund * 0.2;
                investment_fund2 += partner_kiwisaver_fund * 0.2;
                investment_fund3 += partner_kiwisaver_fund * 0.2;
                investment_fund4 += partner_kiwisaver_fund * 0.2;
                investment_fund5 += partner_kiwisaver_fund * 0.2;
            }

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
            partner_kiwisaver_fund = 0;
        }

        // Always calculate total KiwiSaver as the exact sum of individual KiwiSavers
        // This ensures the total is always consistent with the sum of parts
        total_kiwisaver_fund = kiwisaver_fund + (partner ? partner_kiwisaver_fund : 0);

        // New logic to withdraw from KiwiSaver if investment fund is depleted and client is over 65
        if ((investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5) < 0 && age >= 65) {
            // Ensure we're using the correct total KiwiSaver calculation
            const totalKiwiSaverFunds = kiwisaver_fund + (partner ? partner_kiwisaver_fund : 0);
            const totalNegativeInvestment = -(investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5);
            const withdrawalAmount = Math.min(totalNegativeInvestment, totalKiwiSaverFunds);

            // Determine how much to withdraw from each KiwiSaver account
            let mainWithdrawal = 0;
            let partnerWithdrawal = 0;

            if (withdrawalAmount <= kiwisaver_fund) {
                // If main KiwiSaver has enough funds
                mainWithdrawal = withdrawalAmount;
                kiwisaver_fund -= mainWithdrawal;
            } else {
                // If we need to draw from both KiwiSaver accounts
                mainWithdrawal = kiwisaver_fund;
                partnerWithdrawal = Math.min(partner_kiwisaver_fund, withdrawalAmount - mainWithdrawal);
                kiwisaver_fund = 0;
                partner_kiwisaver_fund -= partnerWithdrawal;
            }

            // Distribute the withdrawal amount equally across the investment buckets
            investment_fund1 += withdrawalAmount * 0.2;
            investment_fund2 += withdrawalAmount * 0.2;
            investment_fund3 += withdrawalAmount * 0.2;
            investment_fund4 += withdrawalAmount * 0.2;
            investment_fund5 += withdrawalAmount * 0.2;

            // Update the total investment fund
            investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;
        }

        // Adjust for Negative Funds
        if (investment_fund1 < 0) {
            investment_fund1 = 0;
        }

        if (investment_fund2 < 0) {
            investment_fund2 = 0;
        }

        if (investment_fund3 < 0) {
            investment_fund3 = 0;
        }

        if (investment_fund4 < 0) {
            investment_fund4 = 0;
        }

        if (investment_fund5 < 0) {
            investment_fund5 = 0;
        }

        // Update the total investment fund
        investment_fund = investment_fund1 + investment_fund2 + investment_fund3 + investment_fund4 + investment_fund5;

        if (kiwisaver_fund < 0) {
            kiwisaver_fund = 0;
        }

        if (partner_kiwisaver_fund < 0) {
            partner_kiwisaver_fund = 0;
        }

        // Apply Cash Rate to savings fund
        const cash_rate = scenarioData.cash_rate || 0;
        if (cash_rate > 0) {
            // Apply cash rate to savings fund (independent of inflation)
            savings_fund *= (1 + (cash_rate / 100));
        }

        // Property Value Growth
        property_value *= (1 + (property_growth / 100));
        property_values.push(property_value);

        property_value2 *= (1 + (property_growth2 / 100));
        property_values2.push(property_value2);

        property_value3 *= (1 + (property_growth3 / 100));
        property_values3.push(property_value3);

        property_value4 *= (1 + (property_growth4 / 100));
        property_values4.push(property_value4);

        property_value5 *= (1 + (property_growth5 / 100));
        property_values5.push(property_value5);

        // Income and Expense Inflation
        annual_expenses1 *= (1 + expense1_inflation_rate / 100);
        if (scenarioData.second_expense) {
            annual_expenses2 *= (1 + expense2_inflation_rate / 100);
        } else {
            annual_expenses2 = 0;
        }
        // Only inflate the annual investment contributions when we're in the respective contribution periods
        if (isInContributionPeriod1) {
            annual_investment_contribution *= (1 + inflation_rate / 100);
            if (scenarioData.annual_investment_contribution1 !== undefined) {
                scenarioData.annual_investment_contribution1 *= (1 + inflation_rate / 100);
            }
        }

        if (isInContributionPeriod2 && scenarioData.annual_investment_contribution2 !== undefined) {
            scenarioData.annual_investment_contribution2 *= (1 + inflation_rate / 100);
        }

        if (isInContributionPeriod3 && scenarioData.annual_investment_contribution3 !== undefined) {
            scenarioData.annual_investment_contribution3 *= (1 + inflation_rate / 100);
        }

        if (isInContributionPeriod4 && scenarioData.annual_investment_contribution4 !== undefined) {
            scenarioData.annual_investment_contribution4 *= (1 + inflation_rate / 100);
        }

        if (isInContributionPeriod5 && scenarioData.annual_investment_contribution5 !== undefined) {
            scenarioData.annual_investment_contribution5 *= (1 + inflation_rate / 100);
        }

        // Update Ages
        if (partner) {
            partner_age += 1;
        }

        // Inflation for incomes is applied at the end of each year

        // Net Wealth
        let net_wealth_value = savings_fund + investment_fund + total_kiwisaver_fund;
        if (scenarioData.include_property_debt) {
            net_wealth_value += (property_value - debt_value);
        }
        if (scenarioData.include_property_debt2) {
            net_wealth_value += (property_value2 - debt_value2);
        }
        if (scenarioData.include_property_debt3) {
            net_wealth_value += (property_value3 - debt_value3);
        }
        if (scenarioData.include_property_debt4) {
            net_wealth_value += (property_value4 - debt_value4);
        }
        if (scenarioData.include_property_debt5) {
            net_wealth_value += (property_value5 - debt_value5);
        }

        // Update min/max for each investment bucket
        min_investment_fund1 = Math.min(min_investment_fund1, investment_fund1);
        max_investment_fund1 = Math.max(max_investment_fund1, investment_fund1);

        min_investment_fund2 = Math.min(min_investment_fund2, investment_fund2);
        max_investment_fund2 = Math.max(max_investment_fund2, investment_fund2);

        min_investment_fund3 = Math.min(min_investment_fund3, investment_fund3);
        max_investment_fund3 = Math.max(max_investment_fund3, investment_fund3);

        min_investment_fund4 = Math.min(min_investment_fund4, investment_fund4);
        max_investment_fund4 = Math.max(max_investment_fund4, investment_fund4);

        min_investment_fund5 = Math.min(min_investment_fund5, investment_fund5);
        max_investment_fund5 = Math.max(max_investment_fund5, investment_fund5);

        // Update min and max investment values for total investment
        min_investment_fund = min_investment_fund1 + min_investment_fund2 + min_investment_fund3 + min_investment_fund4 + min_investment_fund5;
        max_investment_fund = max_investment_fund1 + max_investment_fund2 + max_investment_fund3 + max_investment_fund4 + max_investment_fund5;


        // Update min and max net wealth at each age
        minNetWealthAtAge[i] = Math.min(minNetWealthAtAge[i], net_wealth_value);
        maxNetWealthAtAge[i] = Math.max(maxNetWealthAtAge[i], net_wealth_value);

        // Store the income values for this year
        // Include additional incomes and rental income for proper display in IncomeBreakdown component
        partner_income_list.push(partner_taxable_income); // Include partner's additional income
        main_income_list.push(main_income); // Base income without additional incomes or rental
        net_wealth.push(net_wealth_value);
        netWealthAtAges[i].push(net_wealth_value);
        // Note: income was already calculated correctly earlier with all components
        gross_income.push(income);
        net_incomes.push(net_income);
        // Track total investment fund
        investments.push(investment_fund);

        // Track individual investment buckets
        investments1.push(investment_fund1);
        investments2.push(investment_fund2);
        investments3.push(investment_fund3);
        investments4.push(investment_fund4);
        investments5.push(investment_fund5);

        // Track KiwiSaver funds
        kiwisaver.push(kiwisaver_fund);
        total_kiwisaver.push(total_kiwisaver_fund);
        main_kiwisaver.push(kiwisaver_fund);
        partner_kiwisaver.push(partner_kiwisaver_fund);

        // Track investment returns
        investment_return_list.push(investmentReturn);
        investment_return_list1.push(investmentReturn1);
        investment_return_list2.push(investmentReturn2);
        investment_return_list3.push(investmentReturn3);
        investment_return_list4.push(investmentReturn4);
        investment_return_list5.push(investmentReturn5);

        // Track individual fund taxes
        fund1_tax.push(tax1);
        fund2_tax.push(tax2);
        fund3_tax.push(tax3);
        fund4_tax.push(tax4);
        fund5_tax.push(tax5);

        // Track KiwiSaver returns
        kiwisaver_return_list.push(kiwisaverReturn + partnerKiwisaverReturn);

        // Track income portion values
        // Push individual income portion values to their respective arrays
        fund1_income_portion_list.push(incomePortion1 * 100);
        fund2_income_portion_list.push(incomePortion2 * 100);
        fund3_income_portion_list.push(incomePortion3 * 100);
        fund4_income_portion_list.push(incomePortion4 * 100);
        fund5_income_portion_list.push(incomePortion5 * 100);
        kiwisaver_income_portion_list.push(kiwisaverIncomePortion * 100);
        // For partner KiwiSaver income portion, we need to check if partner exists
        const partnerKiwisaverIncomePortion = partner && scenarioData.partner_kiwisaver_income_portion !== undefined ?
            scenarioData.partner_kiwisaver_income_portion / 100 : 0.6;
        partner_kiwisaver_income_portion_list.push(partner ? partnerKiwisaverIncomePortion * 100 : 0);


        // Track min/max investment values
        min_investment.push(min_investment_fund);
        max_investment.push(max_investment_fund);
        min_investment1.push(min_investment_fund1);
        max_investment1.push(max_investment_fund1);
        min_investment2.push(min_investment_fund2);
        max_investment2.push(max_investment_fund2);
        min_investment3.push(min_investment_fund3);
        max_investment3.push(max_investment_fund3);
        min_investment4.push(min_investment_fund4);
        max_investment4.push(max_investment_fund4);
        min_investment5.push(min_investment_fund5);
        max_investment5.push(max_investment_fund5);

        // Track investment contributions
        yearly_investment_contribution_list.push(totalContribution);

        // Track individual bucket contributions with their specific values
        yearly_investment_contribution_list1.push(potentialContribution1);
        yearly_investment_contribution_list2.push(potentialContribution2);
        yearly_investment_contribution_list3.push(potentialContribution3);
        yearly_investment_contribution_list4.push(potentialContribution4);
        yearly_investment_contribution_list5.push(potentialContribution5);
        yearly_kiwisaver_contribution.push(kiwisaver_contrib);
        const total_withdrawal = yearly_saving < 0 ? Math.abs(yearly_saving) : 0;
        total_drawing.push(total_withdrawal);
        yearly_savings.push(yearly_saving);
        total_expenditure.push(totalExpenditure);
        additional_expenditure.push(additionalExpenditure);
        main_income_tax_list.push(main_income_tax);
        partner_income_tax_list.push(partner_income_tax);
        rental_income_tax_list.push(rental_income_tax);
        rental_income_tax_list2.push(rental_income_tax2);
        rental_income_tax_list3.push(rental_income_tax3);
        rental_income_tax_list4.push(rental_income_tax4);
        rental_income_tax_list5.push(rental_income_tax5);
        annual_tax.push(main_income_tax + partner_income_tax + rental_income_tax + rental_income_tax2 + rental_income_tax3 + rental_income_tax4 + rental_income_tax5);

        // Store metrics in metricArrays
        metricArrays[i]['Savings Fund'].push(savings_fund);
        metricArrays[i]['Gross Income'].push(gross_income[i]);
        metricArrays[i]['Net Income'].push(net_incomes[i]);
        metricArrays[i]['Total Expenditure'].push(total_expenditure[i]);
        metricArrays[i]['Additional Expenditure'].push(additional_expenditure[i]);
        metricArrays[i]['Net Wealth'].push(net_wealth[i]);
        metricArrays[i]['Net Cashflow'].push(yearly_savings[i]);
        metricArrays[i]['Total Withdrawals'].push(total_drawing[i]);
        metricArrays[i]['Investments Fund'].push(investments[i]);
        metricArrays[i]['Investment Fund 1'].push(investments1[i]);
        metricArrays[i]['Investment Fund 2'].push(investments2[i]);
        metricArrays[i]['Investment Fund 3'].push(investments3[i]);
        metricArrays[i]['Investment Fund 4'].push(investments4[i]);
        metricArrays[i]['Investment Fund 5'].push(investments5[i]);
        metricArrays[i]['Total KiwiSaver'].push(total_kiwisaver[i]);
        metricArrays[i]['Main KiwiSaver'].push(main_kiwisaver[i]);
        metricArrays[i]['Partner KiwiSaver'].push(partner_kiwisaver[i]);
        metricArrays[i]['Annual Investment Return'].push(investment_return_list[i]);
        // Ensure investment return values are properly initialized
        metricArrays[i]['Annual Investment Return 1'].push(typeof investment_return_list1[i] !== 'undefined' ? investment_return_list1[i] : 0);
        metricArrays[i]['Annual Investment Return 2'].push(typeof investment_return_list2[i] !== 'undefined' ? investment_return_list2[i] : 0);
        metricArrays[i]['Annual Investment Return 3'].push(typeof investment_return_list3[i] !== 'undefined' ? investment_return_list3[i] : 0);
        metricArrays[i]['Annual Investment Return 4'].push(typeof investment_return_list4[i] !== 'undefined' ? investment_return_list4[i] : 0);
        metricArrays[i]['Annual Investment Return 5'].push(typeof investment_return_list5[i] !== 'undefined' ? investment_return_list5[i] : 0);
        metricArrays[i]['Annual KiwiSaver Return'].push(kiwisaver_return_list[i]);
        metricArrays[i]['Main KiwiSaver Return'].push(typeof main_kiwisaver_return_list[i] !== 'undefined' ? main_kiwisaver_return_list[i] : 0);
        metricArrays[i]['Partner KiwiSaver Return'].push(typeof partner_kiwisaver_return_list[i] !== 'undefined' ? partner_kiwisaver_return_list[i] : 0);
        metricArrays[i]['Minimum Investment Return'].push(min_investment[i]);
        metricArrays[i]['Maximum Investment Return'].push(max_investment[i]);
        metricArrays[i]['Annual Investment Contribution'].push(yearly_investment_contribution_list[i]);
        metricArrays[i]['Annual Investment Contribution 1'].push(yearly_investment_contribution_list1[i]);
        metricArrays[i]['Annual Investment Contribution 2'].push(yearly_investment_contribution_list2[i]);
        metricArrays[i]['Annual Investment Contribution 3'].push(yearly_investment_contribution_list3[i]);
        metricArrays[i]['Annual Investment Contribution 4'].push(yearly_investment_contribution_list4[i]);
        metricArrays[i]['Annual Investment Contribution 5'].push(yearly_investment_contribution_list5[i]);
        metricArrays[i]['Main Employee KiwiSaver'].push(main_employee_kiwisaver[i]);
        metricArrays[i]['Main Employer KiwiSaver'].push(main_employer_kiwisaver[i]);
        metricArrays[i]['Partner Employee KiwiSaver'].push(partner_employee_kiwisaver[i]);
        metricArrays[i]['Partner Employer KiwiSaver'].push(partner_employer_kiwisaver[i]);
        metricArrays[i]['Property Value'].push(property_values[i]);
        metricArrays[i]['Property Value 2'].push(property_values2[i]);
        metricArrays[i]['Property Value 3'].push(property_values3[i]);
        metricArrays[i]['Property Value 4'].push(property_values4[i]);
        metricArrays[i]['Property Value 5'].push(property_values5[i]);
        metricArrays[i]['Debt Value'].push(debt_values[i]);
        metricArrays[i]['Debt Value 2'].push(debt_values2[i]);
        metricArrays[i]['Debt Value 3'].push(debt_values3[i]);
        metricArrays[i]['Debt Value 4'].push(debt_values4[i]);
        metricArrays[i]['Debt Value 5'].push(debt_values5[i]);
        metricArrays[i]['Monthly Debt Repayment'].push(debt_repayment_values[i]);
        metricArrays[i]['Monthly Debt Repayment 2'].push(debt_repayment_values2[i]);
        metricArrays[i]['Monthly Debt Repayment 3'].push(debt_repayment_values3[i]);
        metricArrays[i]['Monthly Debt Repayment 4'].push(debt_repayment_values4[i]);
        metricArrays[i]['Monthly Debt Repayment 5'].push(debt_repayment_values5[i]);
        metricArrays[i]['Annual Debt Repayments'].push(annual_debt_repayments[i]);
        metricArrays[i]['Annual Debt Repayments 2'].push(annual_debt_repayments2[i]);
        metricArrays[i]['Annual Debt Repayments 3'].push(annual_debt_repayments3[i]);
        metricArrays[i]['Annual Debt Repayments 4'].push(annual_debt_repayments4[i]);
        metricArrays[i]['Annual Debt Repayments 5'].push(annual_debt_repayments5[i]);
        metricArrays[i]['Annual Interest Payments'].push(debt_interest[i]);
        metricArrays[i]['Annual Interest Payments 2'].push(debt_interest2[i]);
        metricArrays[i]['Annual Interest Payments 3'].push(debt_interest3[i]);
        metricArrays[i]['Annual Interest Payments 4'].push(debt_interest4[i]);
        metricArrays[i]['Annual Interest Payments 5'].push(debt_interest5[i]);
        metricArrays[i]['Annual Principal Repayments'].push(debt_principal_values[i]);
        metricArrays[i]['Annual Principal Repayments 2'].push(debt_principal_values2[i]);
        metricArrays[i]['Annual Principal Repayments 3'].push(debt_principal_values3[i]);
        metricArrays[i]['Annual Principal Repayments 4'].push(debt_principal_values4[i]);
        metricArrays[i]['Annual Principal Repayments 5'].push(debt_principal_values5[i]);
        metricArrays[i]['Income Tax'].push(annual_tax[i]);
        metricArrays[i]['MTR Investment Tax'].push(mtr_tax[i]);
        metricArrays[i]['PIE Investment tax'].push(pie_tax[i]);
        metricArrays[i]['KiwiSaver Tax'].push(ks_tax[i]);
        // Ensure fund tax values are properly initialized
        metricArrays[i]['Fund 1 Tax'].push(typeof fund1_tax[i] !== 'undefined' ? fund1_tax[i] : 0);
        metricArrays[i]['Fund 2 Tax'].push(typeof fund2_tax[i] !== 'undefined' ? fund2_tax[i] : 0);
        metricArrays[i]['Fund 3 Tax'].push(typeof fund3_tax[i] !== 'undefined' ? fund3_tax[i] : 0);
        metricArrays[i]['Fund 4 Tax'].push(typeof fund4_tax[i] !== 'undefined' ? fund4_tax[i] : 0);
        metricArrays[i]['Fund 5 Tax'].push(typeof fund5_tax[i] !== 'undefined' ? fund5_tax[i] : 0);
        metricArrays[i]['KiwiSaver Contributions'].push(kiwisaver_contributions[i]);
        metricArrays[i]['Partner KiwiSaver Contributions'].push(partner_kiwisaver_contributions[i]);
        metricArrays[i]['Main KiwiSaver Tax'].push(main_ks_tax[i]);
        metricArrays[i]['Partner KiwiSaver Tax'].push(partner_ks_tax[i]);

        // Add income portion metrics
        metricArrays[i]['Fund 1 Income Portion'].push(fund1_income_portion_list[i]);
        metricArrays[i]['Fund 2 Income Portion'].push(fund2_income_portion_list[i]);
        metricArrays[i]['Fund 3 Income Portion'].push(fund3_income_portion_list[i]);
        metricArrays[i]['Fund 4 Income Portion'].push(fund4_income_portion_list[i]);
        metricArrays[i]['Fund 5 Income Portion'].push(fund5_income_portion_list[i]);
        metricArrays[i]['KiwiSaver Income Portion'].push(kiwisaver_income_portion_list[i]);
        metricArrays[i]['Partner KiwiSaver Income Portion'].push(partner_kiwisaver_income_portion_list[i]);
        metricArrays[i]['Superannuation'].push(super_income_list[i]);
        metricArrays[i]['Partner Income'].push(partner_income_list[i]);
        metricArrays[i]['Main Income'].push(main_income_list[i]);
        metricArrays[i]['Main Income Tax'].push(main_income_tax_list[i]);
        metricArrays[i]['Partner Income Tax'].push(partner_income_tax_list[i]);
        metricArrays[i]['Rental Income Tax'].push(rental_income_tax_list[i]);
        metricArrays[i]['Rental Income Tax 2'].push(rental_income_tax_list2[i]);
        metricArrays[i]['Rental Income Tax 3'].push(rental_income_tax_list3[i]);
        metricArrays[i]['Rental Income Tax 4'].push(rental_income_tax_list4[i]);
        metricArrays[i]['Rental Income Tax 5'].push(rental_income_tax_list5[i]);
        metricArrays[i]['Property Sale Proceeds'].push(0);
        metricArrays[i]['Property Sale Proceeds 2'].push(0);
        metricArrays[i]['Property Sale Proceeds 3'].push(0);
        metricArrays[i]['Property Sale Proceeds 4'].push(0);
        metricArrays[i]['Property Sale Proceeds 5'].push(0);
        metricArrays[i]['Transaction Costs'].push(0);
        metricArrays[i]['Transaction Costs 2'].push(0);
        metricArrays[i]['Transaction Costs 3'].push(0);
        metricArrays[i]['Transaction Costs 4'].push(0);
        metricArrays[i]['Transaction Costs 5'].push(0);
        metricArrays[i]['Debt Paid'].push(0);
        metricArrays[i]['Debt Paid 2'].push(0);
        metricArrays[i]['Debt Paid 3'].push(0);
        metricArrays[i]['Debt Paid 4'].push(0);
        metricArrays[i]['Debt Paid 5'].push(0);
        metricArrays[i]['Rental Income'].push(rental_income_list[i]);
        metricArrays[i]['Rental Income 2'].push(rental_income_list2[i]);
        metricArrays[i]['Rental Income 3'].push(rental_income_list3[i]);
        metricArrays[i]['Rental Income 4'].push(rental_income_list4[i]);
        metricArrays[i]['Rental Income 5'].push(rental_income_list5[i]);
        metricArrays[i]['Board Income'].push(board_income_list[i]);
        metricArrays[i]['Board Income 2'].push(board_income_list2[i]);
        metricArrays[i]['Board Income 3'].push(board_income_list3[i]);
        metricArrays[i]['Board Income 4'].push(board_income_list4[i]);
        metricArrays[i]['Board Income 5'].push(board_income_list5[i]);
        metricArrays[i]['Lump Sum Payment Amount'].push(lump_sum_payment_amount[i]);
        metricArrays[i]['Lump Sum Payment Amount 2'].push(lump_sum_payment_amount2[i]);
        metricArrays[i]['Lump Sum Payment Amount 3'].push(lump_sum_payment_amount3[i]);
        metricArrays[i]['Lump Sum Payment Amount 4'].push(lump_sum_payment_amount4[i]);
        metricArrays[i]['Lump Sum Payment Amount 5'].push(lump_sum_payment_amount5[i]);
        metricArrays[i]['Basic Expenses 1'].push(basic_expenses1_list[i]);
        metricArrays[i]['Basic Expenses 2'].push(basic_expenses2_list[i]);
        }

        // After the simulation, store the net wealth at the ending age
        netWealthAtEndAge.push(net_wealth[net_wealth.length - 1]);
    }

    return { netWealthAtEndAge, cumulativeMetrics, minNetWealthAtAge, maxNetWealthAtAge, netWealthAtAges, metricArrays, totalScenarios };
}
