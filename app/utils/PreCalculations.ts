export function mulberry32(seed: number) {
    return function() {
      let t = seed += 0x6D2B79F5;
      t = Math.imul(t ^ t >>> 15, t | 1);
      t ^= t + Math.imul(t ^ t >>> 7, t | 61);
      return ((t ^ t >>> 14) >>> 0) / 4294967296;
    }
  }

  export function normalRandom(mean: number, stdDev: number, random: () => number): number {
    // If standard deviation is 0 or exactly zero, return the mean (no randomness)
    // This is important for custom funds where stdDev is explicitly set to 0
    if (stdDev === 0 || stdDev === 0.0) {
      return mean;
    }

    // Use Box-Muller transform
    let u = 0,
      v = 0;
    while (u === 0) u = random(); // Converting [0,1) to (0,1)
    while (v === 0) v = random();
    let num = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return num * stdDev + mean;
  }

  export function getPercentile(arr: number[], percentile: number): number {
    if (arr.length === 0) return 0;
    const sorted = arr.slice().sort((a, b) => a - b);
    const index = (percentile / 100) * (sorted.length - 1);
    const lower = Math.floor(index);
    const upper = lower + 1;
    const weight = index % 1;

    if (upper >= sorted.length) return sorted[lower];
    return sorted[lower] * (1 - weight) + sorted[upper] * weight;
  }

  export function calculateMonthlyPayment(principal: number, annualRate: number, years: number, isInterestOnly: boolean = false): number {
    if (principal <= 0 || years <= 0) return 0;

    const monthlyRate = annualRate / 12 / 100;
    const totalPayments = years * 12;

    if (isInterestOnly) {
      // For interest-only payments, just return the monthly interest amount
      return principal * monthlyRate;
    }

    // For principal + interest payments, use the standard amortization formula
    return (principal * monthlyRate * Math.pow(1 + monthlyRate, totalPayments)) /
           (Math.pow(1 + monthlyRate, totalPayments) - 1);
  }
