

export interface FinancialData {
  [key: string]: any; // Add index signature to allow string indexing

  // Investment metrics
  investment_metrics?: any;
  fund1?: any;
  fund2?: any;
  fund3?: any;
  fund4?: any;
  fund5?: any;
  fund1_return?: number;
  fund2_return?: number;
  fund3_return?: number;
  fund4_return?: number;
  fund5_return?: number;
  fund1_std_dev?: number;
  fund2_std_dev?: number;
  fund3_std_dev?: number;
  fund4_std_dev?: number;
  fund5_std_dev?: number;

  // Income portion for each fund (percentage of return that is taxable)
  investment_income_portion?: number; // Default income portion for all investment funds
  fund1_income_portion?: number;
  fund2_income_portion?: number;
  fund3_income_portion?: number;
  fund4_income_portion?: number;
  fund5_income_portion?: number;

  // Income portion for KiwiSaver funds
  kiwisaver_income_portion?: number;
  partner_kiwisaver_income_portion?: number;


  show_monte_carlo: any;
  show_repayments: any;
  household_id: any;
  name?: string;
  partner_name?: string;
  starting_age: number;
  ending_age: number;
  annual_income: number;
  inflation_rate?: number;
  main_income_inflation_rate?: number;
  income_period?: [number, number];
  partner_starting_age?: number;
  partner_annual_income?: number;
  partner_income_inflation_rate?: number;
  partner_income_period?: [number, number];
  superannuation?: boolean;
  savings_amount?: number;
  cash_reserve?: number;
  saving_percentage?: number;
  cash_rate?: number;
  savings_allocation?: number;
  annual_expenses1?: number;
  expense1_inflation_rate?: number;
  expense_period1?: [number, number];
  second_expense?: boolean;
  annual_expenses2?: number;
  expense2_inflation_rate?: number;
  expense_period2?: [number, number];
  additional_incomes?: Array<{ title: string; value: number; period: [number, number]; tax_type: string; inflation_rate?: number }>;
  additional_expenses?: Array<{ title: string; value: number; period: [number, number]; frequency: number; inflation_rate?: number }>;
  one_off_investments?: Array<{
    amount: number;
    age: number;
    details?: string;
    // Fund allocation properties
    specificFund?: number; // Fund number (1-5) if allocating to a specific fund
    allocations?: {
      fund1?: number; // Percentage allocation to fund 1
      fund2?: number; // Percentage allocation to fund 2
      fund3?: number; // Percentage allocation to fund 3
      fund4?: number; // Percentage allocation to fund 4
      fund5?: number; // Percentage allocation to fund 5
    };
  }>;
  whatIfEvents?: Array<{
    id: string;
    type: string;
    age: number;
    person?: string;
    insurancePayout?: number;
    investmentAllocation?: number;
    [key: string]: any;
  }>;
  // Investment bucket 1
  initial_investment1?: number;
  annual_investment_contribution1?: number;
  annual_investment_return1?: number;
  inv_std_dev1?: number;

  // Investment bucket 2
  initial_investment2?: number;
  annual_investment_contribution2?: number;
  annual_investment_return2?: number;
  inv_std_dev2?: number;

  // Investment bucket 3
  initial_investment3?: number;
  annual_investment_contribution3?: number;
  annual_investment_return3?: number;
  inv_std_dev3?: number;

  // Investment bucket 4
  initial_investment4?: number;
  annual_investment_contribution4?: number;
  annual_investment_return4?: number;
  inv_std_dev4?: number;

  // Investment bucket 5
  initial_investment5?: number;
  annual_investment_contribution5?: number;
  annual_investment_return5?: number;
  inv_std_dev5?: number;

  // Legacy fields (for backward compatibility)
  initial_investment?: number;
  annual_investment_contribution?: number;
  contribution_period?: [number, number];
  annual_investment_return?: number;
  inv_std_dev?: number;
  investment_return_period?: [number, number];
  inv_tax?: string;

  // Investment contribution allocation percentages (should sum to 100)
  investment_allocation1?: number;
  investment_allocation2?: number;
  investment_allocation3?: number;
  investment_allocation4?: number;
  investment_allocation5?: number;

  // Withdrawal priorities for investment funds (array of fund numbers 1-5)
  withdrawal_priorities?: number[];
  initial_kiwiSaver?: number;
  kiwisaver_contribution?: number;
  employer_contribution?: number;
  annual_kiwisaver_return?: number;
  ks_std_dev?: number;
  partner_initial_kiwisaver?: number;
  partner_kiwisaver_contribution?: number;
  partner_employer_contribution?: number;
  partner_annual_kiwisaver_return?: number;
  partner_ks_std_dev?: number;

  // KiwiSaver consolidation
  partner_consolidation_allocations?: Array<{ fundNumber: number; percentage: number }>;
  consolidation_allocations?: Array<{ fundNumber: number; percentage: number }>;

  // Main property
  property_value?: number;
  property_growth?: number;
  debt?: number;
  debt_ir?: number;
  initial_debt_years?: number;
  additional_debt_repayments?: number;
  additional_debt_repayments_start_age?: number;
  additional_debt_repayments_end_age?: number;
  include_property_debt?: boolean;
  show_purchase_details?: boolean;
  purchase_age?: number;
  deposit_amount?: number;
  deposit_sources?: {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  lump_sum_payment_age?: number;
  lump_sum_payment_amount?: number;
  lump_sum_payment_source?: 'investments' | 'savings';
  sell_main_property?: boolean;
  main_property_sale_age?: number;
  main_prop_sale_value?: number;
  pay_off_debt?: boolean;
  sale_allocate_to_investment?: boolean;
  downsize?: boolean;
  downsize_age?: number;
  new_property_value?: number;
  interest_only_period?: boolean;
  interest_only_start_age?: number;
  interest_only_end_age?: number;

  // Property 2
  property_value2?: number;
  property_growth2?: number;
  debt2?: number;
  debt_ir2?: number;
  initial_debt_years2?: number;
  additional_debt_repayments2?: number;
  additional_debt_repayments_start_age2?: number;
  additional_debt_repayments_end_age2?: number;
  include_property_debt2?: boolean;
  show_purchase_details2?: boolean;
  purchase_age2?: number;
  deposit_amount2?: number;
  deposit_sources2?: {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  lump_sum_payment_age2?: number;
  lump_sum_payment_amount2?: number;
  lump_sum_payment_source2?: 'investments' | 'savings';
  sell_main_property2?: boolean;
  main_property_sale_age2?: number;
  main_prop_sale_value2?: number;
  pay_off_debt2?: boolean;
  sale2_allocate_to_investment?: boolean;
  downsize_age2?: number;
  new_property_value2?: number;
  starting_age2?: number;
  interest_only_period2?: boolean;
  interest_only_start_age2?: number;
  interest_only_end_age2?: number;

  // Property 3
  property_value3?: number;
  property_growth3?: number;
  debt3?: number;
  debt_ir3?: number;
  initial_debt_years3?: number;
  additional_debt_repayments3?: number;
  additional_debt_repayments_start_age3?: number;
  additional_debt_repayments_end_age3?: number;
  include_property_debt3?: boolean;
  show_purchase_details3?: boolean;
  purchase_age3?: number;
  deposit_amount3?: number;
  deposit_sources3?: {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  lump_sum_payment_age3?: number;
  lump_sum_payment_amount3?: number;
  lump_sum_payment_source3?: 'investments' | 'savings';
  sell_main_property3?: boolean;
  main_property_sale_age3?: number;
  main_prop_sale_value3?: number;
  pay_off_debt3?: boolean;
  sale3_allocate_to_investment?: boolean;
  downsize_age3?: number;
  new_property_value3?: number;
  starting_age3?: number;
  interest_only_period3?: boolean;
  interest_only_start_age3?: number;
  interest_only_end_age3?: number;

  // Property 4
  property_value4?: number;
  property_growth4?: number;
  debt4?: number;
  debt_ir4?: number;
  initial_debt_years4?: number;
  additional_debt_repayments4?: number;
  additional_debt_repayments_start_age4?: number;
  additional_debt_repayments_end_age4?: number;
  include_property_debt4?: boolean;
  show_purchase_details4?: boolean;
  purchase_age4?: number;
  deposit_amount4?: number;
  deposit_sources4?: {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  lump_sum_payment_age4?: number;
  lump_sum_payment_amount4?: number;
  lump_sum_payment_source4?: 'investments' | 'savings';
  sell_main_property4?: boolean;
  main_property_sale_age4?: number;
  main_prop_sale_value4?: number;
  pay_off_debt4?: boolean;
  sale4_allocate_to_investment?: boolean;
  downsize_age4?: number;
  new_property_value4?: number;
  starting_age4?: number;
  interest_only_period4?: boolean;
  interest_only_start_age4?: number;
  interest_only_end_age4?: number;

  // Property 5
  property_value5?: number;
  property_growth5?: number;
  debt5?: number;
  debt_ir5?: number;
  initial_debt_years5?: number;
  additional_debt_repayments5?: number;
  additional_debt_repayments_start_age5?: number;
  additional_debt_repayments_end_age5?: number;
  include_property_debt5?: boolean;
  show_purchase_details5?: boolean;
  purchase_age5?: number;
  deposit_amount5?: number;
  deposit_sources5?: {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  lump_sum_payment_age5?: number;
  lump_sum_payment_amount5?: number;
  lump_sum_payment_source5?: 'investments' | 'savings';
  sell_main_property5?: boolean;
  main_property_sale_age5?: number;
  main_prop_sale_value5?: number;
  pay_off_debt5?: boolean;
  sale5_allocate_to_investment?: boolean;
  downsize_age5?: number;
  new_property_value5?: number;
  starting_age5?: number;
  interest_only_period5?: boolean;
  interest_only_start_age5?: number;
  interest_only_end_age5?: number;

  // Other fields
  kiwisaver_contributions?: number;
  partner_kiwisaver_contributions?: number;
  consolidate_kiwisaver?: boolean;
  partner_consolidate_kiwisaver?: boolean;
  main_consolidate_kiwisaver_age?: number;
  partner_consolidate_kiwisaver_age?: number;
  utilise_excess_cashflow?: boolean;
  allocate_to_investment: boolean;
  pay_off_debt_on_downsize?: boolean;
  num_simulations: number;
  confidence_interval: number;
  seed?: number;
  // Fund periods for each investment bucket
  fund_periods1?: Array<{
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;
  fund_periods2?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;
  fund_periods3?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;
  fund_periods4?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;
  fund_periods5?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;

  // Legacy field (for backward compatibility)
  fund_periods?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;

  ks_periods?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;
  partner_ks_periods?: Array<{
    incomePortion: undefined;
    fundType: string;
    period: [number, number];
    return: number;
    stdDev: number;
  }>;

  savings_owner?: string;

  includePartner?: boolean;

  // Rental income fields
  rental_income?: boolean;
  rental_amount?: number;
  rental_start_age?: number;
  rental_end_age?: number;
  rental_income2?: boolean;
  rental_amount2?: number;
  rental_start_age2?: number;
  rental_end_age2?: number;
  rental_income3?: boolean;
  rental_amount3?: number;
  rental_start_age3?: number;
  rental_end_age3?: number;
  rental_income4?: boolean;
  rental_amount4?: number;
  rental_start_age4?: number;
  rental_end_age4?: number;
  rental_income5?: boolean;
  rental_amount5?: number;
  rental_start_age5?: number;
  rental_end_age5?: number;

  // Board income fields
  board_income?: boolean;
  board_amount?: number;
  board_start_age?: number;
  board_end_age?: number;
  board_income2?: boolean;
  board_amount2?: number;
  board_start_age2?: number;
  board_end_age2?: number;
  board_income3?: boolean;
  board_amount3?: number;
  board_start_age3?: number;
  board_end_age3?: number;
  board_income4?: boolean;
  board_amount4?: number;
  board_start_age4?: number;
  board_end_age4?: number;
  board_income5?: boolean;
  board_amount5?: number;
  board_start_age5?: number;
  board_end_age5?: number;
}

export interface FinancialMetrics {
  Age: number;
  'Savings Fund': number;
  'Gross Income': number;
  'Net Income': number;
  'Total Expenditure': number;
  'Additional Expenditure': number;
  'Net Wealth': number;
  'Net Cashflow': number;
  'Total Withdrawals': number;
  'Investments Fund': number;
  'Investment Fund 1': number;
  'Investment Fund 2': number;
  'Investment Fund 3': number;
  'Investment Fund 4': number;
  'Investment Fund 5': number;
  'Total KiwiSaver': number;
  'Main KiwiSaver': number;
  'Partner KiwiSaver': number;
  'Annual Investment Return': number;
  'Annual Investment Return 1': number;
  'Annual Investment Return 2': number;
  'Annual Investment Return 3': number;
  'Annual Investment Return 4': number;
  'Annual Investment Return 5': number;
  'Annual KiwiSaver Return': number;
  'Minimum Investment Return': number;
  'Maximum Investment Return': number;
  'Annual Investment Contribution': number;
  'Annual Investment Contribution 1': number;
  'Annual Investment Contribution 2': number;
  'Annual Investment Contribution 3': number;
  'Annual Investment Contribution 4': number;
  'Annual Investment Contribution 5': number;

  'Property Value': number;
  'Property Value 2': number;
  'Property Value 3': number;
  'Property Value 4': number;
  'Property Value 5': number;
  'Debt Value': number;
  'Debt Value 2': number;
  'Debt Value 3': number;
  'Debt Value 4': number;
  'Debt Value 5': number;
  'Monthly Debt Repayment': number;
  'Monthly Debt Repayment 2': number;
  'Monthly Debt Repayment 3': number;
  'Monthly Debt Repayment 4': number;
  'Monthly Debt Repayment 5': number;
  'Annual Debt Repayments': number;
  'Annual Debt Repayments 2': number;
  'Annual Debt Repayments 3': number;
  'Annual Debt Repayments 4': number;
  'Annual Debt Repayments 5': number;
  'Annual Interest Payments': number;
  'Annual Interest Payments 2': number;
  'Annual Interest Payments 3': number;
  'Annual Interest Payments 4': number;
  'Annual Interest Payments 5': number;
  'Annual Principal Repayments': number;
  'Annual Principal Repayments 2': number;
  'Annual Principal Repayments 3': number;
  'Annual Principal Repayments 4': number;
  'Annual Principal Repayments 5': number;
  'Income Tax': number;
  'MTR Investment Tax': number;
  'PIE Investment tax': number;
  'KiwiSaver Tax': number;
  'KiwiSaver Contributions': number;
  'Partner KiwiSaver Contributions': number;
  'Superannuation': number;
  'Partner Income': number;
  'Main Income': number;
  'Main Income Tax': number;
  'Partner Income Tax': number;
  'Rental Income Tax': number;
  'Rental Income Tax 2': number;
  'Rental Income Tax 3': number;
  'Rental Income Tax 4': number;
  'Rental Income Tax 5': number;
  'Property Sale Proceeds': number;
  'Property Sale Proceeds 2': number;
  'Property Sale Proceeds 3': number;
  'Property Sale Proceeds 4': number;
  'Property Sale Proceeds 5': number;
  'Transaction Costs': number;
  'Transaction Costs 2': number;
  'Transaction Costs 3': number;
  'Transaction Costs 4': number;
  'Transaction Costs 5': number;
  'Debt Paid': number;
  'Debt Paid 2': number;
  'Debt Paid 3': number;
  'Debt Paid 4': number;
  'Debt Paid 5': number;
  'Rental Income': number;
  'Rental Income 2': number;
  'Rental Income 3': number;
  'Rental Income 4': number;
  'Rental Income 5': number;
  'Board Income': number;
  'Board Income 2': number;
  'Board Income 3': number;
  'Board Income 4': number;
  'Board Income 5': number;
  'Basic Expenses 1': number;
  'Basic Expenses 2': number;
  'Lump Sum Payment': number;
  'Lump Sum Payment 2'?: number;
  'Lump Sum Payment 3'?: number;
  'Lump Sum Payment 4'?: number;
  'Lump Sum Payment 5'?: number;
  'Lump Sum Payment Amount': number;
  'Lump Sum Payment Amount 2'?: number;
  'Lump Sum Payment Amount 3'?: number;
  'Lump Sum Payment Amount 4'?: number;
  'Lump Sum Payment Amount 5'?: number;
  'Property Purchase'?: number;
  'Property Deposit'?: number;
  'Property Purchase 2'?: number;
  'Property Deposit 2'?: number;
  'Property Purchase 3'?: number;
  'Property Deposit 3'?: number;
  'Property Purchase 4'?: number;
  'Property Deposit 4'?: number;
  'Property Purchase 5'?: number;
  'Property Deposit 5'?: number;
}

export type Metric = {
  'Age': number;
  'Savings Fund': number;
  'Gross Income': number;
  'Net Income': number;
  'Total Expenditure': number;
  'Additional Expenditure': number;
  'Net Wealth': number;
  'Net Cashflow': number;
  'Total Withdrawals': number;
  'Investments Fund': number;
  'Investment Fund 1': number;
  'Investment Fund 2': number;
  'Investment Fund 3': number;
  'Investment Fund 4': number;
  'Investment Fund 5': number;
  'Total KiwiSaver': number;
  'Main KiwiSaver': number;
  'Partner KiwiSaver': number;
  'Annual Investment Return': number;
  'Annual Investment Return 1': number;
  'Annual Investment Return 2': number;
  'Annual Investment Return 3': number;
  'Annual Investment Return 4': number;
  'Annual Investment Return 5': number;
  'Annual KiwiSaver Return': number;
  'Minimum Investment Return': number;
  'Maximum Investment Return': number;
  'Annual Investment Contribution': number;
  'Annual Investment Contribution 1': number;
  'Annual Investment Contribution 2': number;
  'Annual Investment Contribution 3': number;
  'Annual Investment Contribution 4': number;
  'Annual Investment Contribution 5': number;

  'Property Value': number;
  'Property Value 2': number;
  'Property Value 3': number;
  'Property Value 4': number;
  'Property Value 5': number;
  'Debt Value': number;
  'Debt Value 2': number;
  'Debt Value 3': number;
  'Debt Value 4': number;
  'Debt Value 5': number;
  'Monthly Debt Repayment': number;
  'Monthly Debt Repayment 2': number;
  'Monthly Debt Repayment 3': number;
  'Monthly Debt Repayment 4': number;
  'Monthly Debt Repayment 5': number;
  'Annual Debt Repayments': number;
  'Annual Debt Repayments 2': number;
  'Annual Debt Repayments 3': number;
  'Annual Debt Repayments 4': number;
  'Annual Debt Repayments 5': number;
  'Annual Interest Payments': number;
  'Annual Interest Payments 2': number;
  'Annual Interest Payments 3': number;
  'Annual Interest Payments 4': number;
  'Annual Interest Payments 5': number;
  'Annual Principal Repayments': number;
  'Annual Principal Repayments 2': number;
  'Annual Principal Repayments 3': number;
  'Annual Principal Repayments 4': number;
  'Annual Principal Repayments 5': number;
  'Income Tax': number;
  'MTR Investment Tax': number;
  'PIE Investment tax': number;
  'KiwiSaver Tax': number;
  'KiwiSaver Contributions': number;
  'Partner KiwiSaver Contributions': number;
  'Superannuation': number;
  'Partner Income': number;
  'Main Income': number;
  'Main Income Tax': number;
  'Partner Income Tax': number;
  'Rental Income Tax': number;
  'Rental Income Tax 2': number;
  'Rental Income Tax 3': number;
  'Rental Income Tax 4': number;
  'Rental Income Tax 5': number;
  'Property Sale Proceeds': number;
  'Property Sale Proceeds 2': number;
  'Property Sale Proceeds 3': number;
  'Property Sale Proceeds 4': number;
  'Property Sale Proceeds 5': number;
  'Transaction Costs': number;
  'Transaction Costs 2': number;
  'Transaction Costs 3': number;
  'Transaction Costs 4': number;
  'Transaction Costs 5': number;
  'Debt Paid': number;
  'Debt Paid 2': number;
  'Debt Paid 3': number;
  'Debt Paid 4': number;
  'Debt Paid 5': number;
  'Rental Income': number;
  'Rental Income 2'?: number;
  'Rental Income 3'?: number;
  'Rental Income 4'?: number;
  'Rental Income 5'?: number;
  'Board Income': number;
  'Board Income 2'?: number;
  'Board Income 3'?: number;
  'Board Income 4'?: number;
  'Board Income 5'?: number;
  'Basic Expenses 1': number;
  'Basic Expenses 2': number;
  'Lump Sum Payment Amount': number;
  'Lump Sum Payment Amount 2'?: number;
  'Lump Sum Payment Amount 3'?: number;
  'Lump Sum Payment Amount 4'?: number;
  'Lump Sum Payment Amount 5'?: number;
  'Property Purchase'?: number;
  'Property Deposit'?: number;
  'Property Purchase 2'?: number;
  'Property Deposit 2'?: number;
  'Property Purchase 3'?: number;
  'Property Deposit 3'?: number;
  'Property Purchase 4'?: number;
  'Property Deposit 4'?: number;
  'Property Purchase 5'?: number;
  'Property Deposit 5'?: number;
}
