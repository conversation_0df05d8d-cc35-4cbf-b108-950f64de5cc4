// financialCalculations.ts

import { useCallback } from 'react';

import { FinancialData, FinancialMetrics } from './financialTypes';

import { mulberry32, normalRandom, getPercentile, calculateMonthlyPayment } from '@/app/utils/PreCalculations';

import { financialLoop } from './financialLoop';

export function useFinancialCalculations(activeScenarioData: any) {
  const calculateFinancialLife = useCallback((data: FinancialData) => {
    // Add seed to data interface or use a default
    const seed = data.seed || 12345; // You'll need to add this to FinancialData interface
    const random = mulberry32(seed);


    const { netWealthAtEndAge, cumulativeMetrics, minNetWealthAtAge, maxNetWealthAtAge, totalScenarios, metricArrays } = financialLoop(data, random);

    // Calculate the chance of success
    const successfulScenarios = netWealthAtEndAge.filter((netWealth) => netWealth > 0).length;
    const failedScenarios = totalScenarios - successfulScenarios;
    const chanceOfSuccess = (successfulScenarios / totalScenarios) * 100;

    // Calculate the percentiles based on the confidence interval
    const lowerPercentile = (100 - data.confidence_interval) / 2;
    const upperPercentile = 100 - lowerPercentile;

    // Initialize arrays for best, worst, and average scenarios
    const worstNetWealthAtAge: number[] = [];
    const bestNetWealthAtAge: number[] = [];
    const averageNetWealthAtAge: number[] = [];

    // Calculate percentiles for each year
    metricArrays.forEach((yearMetrics, index) => {
      // Sort values for this year
      const netWealthValues = [...yearMetrics['Net Wealth']].sort((a, b) => a - b);

      // Calculate indices for the confidence interval
      const lowerIndex = Math.floor(netWealthValues.length * (lowerPercentile / 100));
      const upperIndex = Math.floor(netWealthValues.length * (upperPercentile / 100));

      // Extract values within the confidence interval
      const valuesWithinInterval = netWealthValues.slice(lowerIndex, upperIndex + 1);

      // Calculate statistics
      const sum = valuesWithinInterval.reduce((acc, val) => acc + val, 0);
      const average = sum / valuesWithinInterval.length;

      worstNetWealthAtAge.push(valuesWithinInterval[0]); // Lowest value within interval
      bestNetWealthAtAge.push(valuesWithinInterval[valuesWithinInterval.length - 1]); // Highest value within interval
      averageNetWealthAtAge.push(average);
    });

    // Add best and worst scenarios to the return object
    return {
      allMetrics: metricArrays.map((metrics, index) => ({
        Age: index + data.starting_age,
        'Savings Fund': Math.max(0, getPercentile(metrics['Savings Fund'], 50)),
        'Gross Income': getPercentile(metrics['Gross Income'], 50),
        'Net Income': getPercentile(metrics['Net Income'], 50),
        'Total Expenditure': getPercentile(metrics['Total Expenditure'], 50),
        'Additional Expenditure': getPercentile(metrics['Additional Expenditure'], 50),
        'Net Wealth': Math.max(0, getPercentile(metrics['Net Wealth'], 50)),
        'Net Cashflow': getPercentile(metrics['Net Cashflow'], 50),
        'Total Withdrawals': getPercentile(metrics['Total Withdrawals'], 50),
        'Investment Fund 1': Math.max(0, getPercentile(metrics['Investment Fund 1'], 50)),
        'Investment Fund 2': Math.max(0, getPercentile(metrics['Investment Fund 2'], 50)),
        'Investment Fund 3': Math.max(0, getPercentile(metrics['Investment Fund 3'], 50)),
        'Investment Fund 4': Math.max(0, getPercentile(metrics['Investment Fund 4'], 50)),
        'Investment Fund 5': Math.max(0, getPercentile(metrics['Investment Fund 5'], 50)),
        // Calculate Investments Fund as the exact sum of individual investment funds
        // This ensures consistency between the total and individual values
        'Investments Fund': Math.max(0,
          Math.max(0, getPercentile(metrics['Investment Fund 1'], 50)) +
          Math.max(0, getPercentile(metrics['Investment Fund 2'], 50)) +
          Math.max(0, getPercentile(metrics['Investment Fund 3'], 50)) +
          Math.max(0, getPercentile(metrics['Investment Fund 4'], 50)) +
          Math.max(0, getPercentile(metrics['Investment Fund 5'], 50))
        ),
        'Main KiwiSaver': Math.max(0, getPercentile(metrics['Main KiwiSaver'], 50)),
        'Partner KiwiSaver': Math.max(0, getPercentile(metrics['Partner KiwiSaver'], 50)),
        // Calculate Total KiwiSaver as the exact sum of Main and Partner KiwiSaver
        // This ensures consistency between the total and individual values
        'Total KiwiSaver': Math.max(0,
          Math.max(0, getPercentile(metrics['Main KiwiSaver'], 50)) +
          Math.max(0, getPercentile(metrics['Partner KiwiSaver'], 50))
        ),
        'Annual Investment Return': getPercentile(metrics['Annual Investment Return'], 50),
        'Annual KiwiSaver Return': getPercentile(metrics['Annual KiwiSaver Return'], 50),
        'Minimum Investment Fund': getPercentile(metrics['Minimum Investment Return'], 50),
        'Maximum Investment Fund': getPercentile(metrics['Maximum Investment Return'], 50),
        'Annual Investment Contribution': getPercentile(metrics['Annual Investment Contribution'], 50),
        'Main Employee KiwiSaver': getPercentile(metrics['Main Employee KiwiSaver'], 50),
        'Main Employer KiwiSaver': getPercentile(metrics['Main Employer KiwiSaver'], 50),
        'Partner Employee KiwiSaver': getPercentile(metrics['Partner Employee KiwiSaver'], 50),
        'Partner Employer KiwiSaver': getPercentile(metrics['Partner Employer KiwiSaver'], 50),
        'Property Value': getPercentile(metrics['Property Value'], 50),
        'Property Value 2': getPercentile(metrics['Property Value 2'], 50),
        'Property Value 3': getPercentile(metrics['Property Value 3'], 50),
        'Property Value 4': getPercentile(metrics['Property Value 4'], 50),
        'Property Value 5': getPercentile(metrics['Property Value 5'], 50),
        'Debt Value': getPercentile(metrics['Debt Value'], 50),
        'Debt Value 2': getPercentile(metrics['Debt Value 2'], 50),
        'Debt Value 3': getPercentile(metrics['Debt Value 3'], 50),
        'Debt Value 4': getPercentile(metrics['Debt Value 4'], 50),
        'Debt Value 5': getPercentile(metrics['Debt Value 5'], 50),
        'Monthly Debt Repayment': getPercentile(metrics['Monthly Debt Repayment'], 50),
        'Monthly Debt Repayment 2': getPercentile(metrics['Monthly Debt Repayment 2'], 50),
        'Monthly Debt Repayment 3': getPercentile(metrics['Monthly Debt Repayment 3'], 50),
        'Monthly Debt Repayment 4': getPercentile(metrics['Monthly Debt Repayment 4'], 50),
        'Monthly Debt Repayment 5': getPercentile(metrics['Monthly Debt Repayment 5'], 50),
        'Annual Debt Repayments': getPercentile(metrics['Annual Debt Repayments'], 50),
        'Annual Debt Repayments 2': getPercentile(metrics['Annual Debt Repayments 2'], 50),
        'Annual Debt Repayments 3': getPercentile(metrics['Annual Debt Repayments 3'], 50),
        'Annual Debt Repayments 4': getPercentile(metrics['Annual Debt Repayments 4'], 50),
        'Annual Debt Repayments 5': getPercentile(metrics['Annual Debt Repayments 5'], 50),
        'Annual Interest Payments': getPercentile(metrics['Annual Interest Payments'], 50),
        'Annual Interest Payments 2': getPercentile(metrics['Annual Interest Payments 2'], 50),
        'Annual Interest Payments 3': getPercentile(metrics['Annual Interest Payments 3'], 50),
        'Annual Interest Payments 4': getPercentile(metrics['Annual Interest Payments 4'], 50),
        'Annual Interest Payments 5': getPercentile(metrics['Annual Interest Payments 5'], 50),
        'Annual Principal Repayments': getPercentile(metrics['Annual Principal Repayments'], 50),
        'Annual Principal Repayments 2': getPercentile(metrics['Annual Principal Repayments 2'], 50),
        'Annual Principal Repayments 3': getPercentile(metrics['Annual Principal Repayments 3'], 50),
        'Annual Principal Repayments 4': getPercentile(metrics['Annual Principal Repayments 4'], 50),
        'Annual Principal Repayments 5': getPercentile(metrics['Annual Principal Repayments 5'], 50),
        'Income Tax': getPercentile(metrics['Income Tax'], 50),
        'MTR Investment Tax': getPercentile(metrics['MTR Investment Tax'], 50),
        'PIE Investment tax': getPercentile(metrics['PIE Investment tax'], 50),
        'KiwiSaver Tax': getPercentile(metrics['KiwiSaver Tax'], 50),
        'Main KiwiSaver Tax': getPercentile(metrics['Main KiwiSaver Tax'], 50),
        'Partner KiwiSaver Tax': getPercentile(metrics['Partner KiwiSaver Tax'], 50),
        'Fund 1 Tax': getPercentile(metrics['Fund 1 Tax'], 50),
        'Fund 2 Tax': getPercentile(metrics['Fund 2 Tax'], 50),
        'Fund 3 Tax': getPercentile(metrics['Fund 3 Tax'], 50),
        'Fund 4 Tax': getPercentile(metrics['Fund 4 Tax'], 50),
        'Fund 5 Tax': getPercentile(metrics['Fund 5 Tax'], 50),
        'Annual Investment Return 1': getPercentile(metrics['Annual Investment Return 1'], 50),
        'Annual Investment Return 2': getPercentile(metrics['Annual Investment Return 2'], 50),
        'Annual Investment Return 3': getPercentile(metrics['Annual Investment Return 3'], 50),
        'Annual Investment Return 4': getPercentile(metrics['Annual Investment Return 4'], 50),
        'Annual Investment Return 5': getPercentile(metrics['Annual Investment Return 5'], 50),
        'Main KiwiSaver Return': getPercentile(metrics['Main KiwiSaver Return'] || [], 50),
        'Partner KiwiSaver Return': getPercentile(metrics['Partner KiwiSaver Return'] || [], 50),
        'KiwiSaver Contributions': getPercentile(metrics['KiwiSaver Contributions'], 50),
        'Partner KiwiSaver Contributions': getPercentile(metrics['Partner KiwiSaver Contributions'], 50),
        'Superannuation': getPercentile(metrics['Superannuation'], 50),
        'Partner Income': getPercentile(metrics['Partner Income'], 50),
        'Main Income': getPercentile(metrics['Main Income'], 50),
        'Main Income Tax': getPercentile(metrics['Main Income Tax'], 50),
        'Partner Income Tax': getPercentile(metrics['Partner Income Tax'], 50),
        'Property Sale Proceeds': getPercentile(metrics['Property Sale Proceeds'], 50),
        'Property Sale Proceeds 2': getPercentile(metrics['Property Sale Proceeds 2'], 50),
        'Property Sale Proceeds 3': getPercentile(metrics['Property Sale Proceeds 3'], 50),
        'Property Sale Proceeds 4': getPercentile(metrics['Property Sale Proceeds 4'], 50),
        'Property Sale Proceeds 5': getPercentile(metrics['Property Sale Proceeds 5'], 50),
        'Transaction Costs': getPercentile(metrics['Transaction Costs'], 50),
        'Transaction Costs 2': getPercentile(metrics['Transaction Costs 2'], 50),
        'Transaction Costs 3': getPercentile(metrics['Transaction Costs 3'], 50),
        'Transaction Costs 4': getPercentile(metrics['Transaction Costs 4'], 50),
        'Transaction Costs 5': getPercentile(metrics['Transaction Costs 5'], 50),
        'Debt Paid': getPercentile(metrics['Debt Paid'], 50),
        'Debt Paid 2': getPercentile(metrics['Debt Paid 2'], 50),
        'Debt Paid 3': getPercentile(metrics['Debt Paid 3'], 50),
        'Debt Paid 4': getPercentile(metrics['Debt Paid 4'], 50),
        'Debt Paid 5': getPercentile(metrics['Debt Paid 5'], 50),
        'Rental Income': getPercentile(metrics['Rental Income'], 50),
        'Rental Income 2': getPercentile(metrics['Rental Income 2'], 50),
        'Rental Income 3': getPercentile(metrics['Rental Income 3'], 50),
        'Rental Income 4': getPercentile(metrics['Rental Income 4'], 50),
        'Rental Income 5': getPercentile(metrics['Rental Income 5'], 50),
        'Board Income': getPercentile(metrics['Board Income'], 50),
        'Board Income 2': getPercentile(metrics['Board Income 2'], 50),
        'Board Income 3': getPercentile(metrics['Board Income 3'], 50),
        'Board Income 4': getPercentile(metrics['Board Income 4'], 50),
        'Board Income 5': getPercentile(metrics['Board Income 5'], 50),
        'Basic Expenses 1': getPercentile(metrics['Basic Expenses 1'] || [], 50),
        'Basic Expenses 2': getPercentile(metrics['Basic Expenses 2'] || [], 50),
        'Lump Sum Payment Amount': getPercentile(metrics['Lump Sum Payment Amount'] || [], 50),
        'Lump Sum Payment Amount 2': getPercentile(metrics['Lump Sum Payment Amount 2'] || [], 50),
        'Lump Sum Payment Amount 3': getPercentile(metrics['Lump Sum Payment Amount 3'] || [], 50),
        'Lump Sum Payment Amount 4': getPercentile(metrics['Lump Sum Payment Amount 4'] || [], 50),
        'Lump Sum Payment Amount 5': getPercentile(metrics['Lump Sum Payment Amount 5'] || [], 50),
        'Property Purchase': getPercentile(metrics['Property Purchase'] || [], 50),
        'Property Deposit': getPercentile(metrics['Property Deposit'] || [], 50),
        'Property Purchase 2': getPercentile(metrics['Property Purchase 2'] || [], 50),
        'Property Deposit 2': getPercentile(metrics['Property Deposit 2'] || [], 50),
        'Property Purchase 3': getPercentile(metrics['Property Purchase 3'] || [], 50),
        'Property Deposit 3': getPercentile(metrics['Property Deposit 3'] || [], 50),
        'Property Purchase 4': getPercentile(metrics['Property Purchase 4'] || [], 50),
        'Property Deposit 4': getPercentile(metrics['Property Deposit 4'] || [], 50),
        'Property Purchase 5': getPercentile(metrics['Property Purchase 5'] || [], 50),
        'Property Deposit 5': getPercentile(metrics['Property Deposit 5'] || [], 50),
        'Fund 1 Income Portion': getPercentile(metrics['Fund 1 Income Portion'] || [], 50),
        'Fund 2 Income Portion': getPercentile(metrics['Fund 2 Income Portion'] || [], 50),
        'Fund 3 Income Portion': getPercentile(metrics['Fund 3 Income Portion'] || [], 50),
        'Fund 4 Income Portion': getPercentile(metrics['Fund 4 Income Portion'] || [], 50),
        'Fund 5 Income Portion': getPercentile(metrics['Fund 5 Income Portion'] || [], 50),
        'KiwiSaver Income Portion': getPercentile(metrics['KiwiSaver Income Portion'] || [], 50),
        'Partner KiwiSaver Income Portion': getPercentile(metrics['Partner KiwiSaver Income Portion'] || [], 50),
      })),
      chanceOfSuccess,
      successfulScenarios,
      failedScenarios,
      minNetWealthAtAge: minNetWealthAtAge.map(val => Math.max(0, val)),
      maxNetWealthAtAge: maxNetWealthAtAge.map(val => Math.max(0, val)),
      worstNetWealthAtAge: worstNetWealthAtAge.map(val => Math.max(0, val)),
      bestNetWealthAtAge: bestNetWealthAtAge.map(val => Math.max(0, val)),
      averageNetWealthAtAge: averageNetWealthAtAge.map(val => Math.max(0, val)),
    };
  }, []);

  return { calculateFinancialLife };
}
