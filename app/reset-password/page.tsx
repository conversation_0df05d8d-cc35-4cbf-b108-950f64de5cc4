"use client";

import { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { PasswordInputWithStrength } from "@/components/ui/PasswordStrengthIndicator";
import { validatePassword, validatePasswordConfirmation } from "@/utils/password-validation";

export default function ResetPassword() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Enhanced password validation
    if (!password || !confirmPassword) {
      setError("Please fill in all fields");
      return;
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setError(`Password requirements not met: ${passwordValidation.errors[0]}`);
      return;
    }

    // Check if passwords match
    const confirmValidation = validatePasswordConfirmation(password, confirmPassword);
    if (!confirmValidation.isValid) {
      setError(confirmValidation.error || "Passwords do not match");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const supabase = createClient();

      // Update the password
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        console.error("Password update error:", error);

        if (error.message.includes("auth session")) {
          setError("Your reset link has expired. Please request a new one.");
          toast.error("Reset link expired", {
            description: "Please request a new password reset link",
            position: "top-center",
            duration: 5000,
          });

          setTimeout(() => {
            router.push("/forgot-password");
          }, 3000);
          return;
        }

        setError(error.message);
        return;
      }

      // Success - redirect to sign in
      toast.success("Password updated successfully", {
        description: "You can now sign in with your new password",
        position: "top-center",
        duration: 5000,
      });

      setTimeout(() => {
        router.push("/sign-in");
      }, 2000);

    } catch (err: any) {
      console.error("Error updating password:", err);
      setError(err.message || "An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-w-full justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-medium mb-6">Set New Password</h1>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">New Password</Label>
            <PasswordInputWithStrength
              value={password}
              onChange={setPassword}
              name="password"
              placeholder="Create a strong password"
              required
              showRequirements={true}
              showGenerator={true}
              className="mb-4"
              onConfirmPasswordChange={setConfirmPassword}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm new password"
              required
              minLength={8}
            />
          </div>

          {error && (
            <div className="text-red-500 text-sm">{error}</div>
          )}

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Updating Password..." : "Reset Password"}
          </Button>

          <div className="text-center mt-4">
            <Link className="text-primary text-sm underline" href="/sign-in">
              Back to Sign In
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
