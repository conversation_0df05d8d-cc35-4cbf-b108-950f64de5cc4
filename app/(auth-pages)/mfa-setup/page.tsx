'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { EnrollMFA } from '@/components/mfa/EnrollMFA';
import { UnenrollMFA } from '@/components/mfa/UnenrollMFA';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, ShieldCheck } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { unenrollMFAAction } from '@/app/actions';

export default function MFASetupPage() {
  const [hasMFA, setHasMFA] = useState(false);
  const [showEnrollment, setShowEnrollment] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();
  const supabase = createClient();

  const checkMFAStatus = async () => {
    try {
      setLoading(true);

      // We already get the user below, so this line is not needed

      // Check if user is authenticated
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.log('User not authenticated, redirecting to sign-in');
        router.push('/sign-in');
        return;
      }

      console.log('User authenticated:', user.id);

      // Check MFA factors
      const { data, error } = await supabase.auth.mfa.listFactors();

      if (error) {
        console.error('Error listing factors:', error);
        throw error;
      }

      console.log('MFA factors:', data);

      // Check if any factors are verified
      const allFactors = [...(data.totp || []), ...(data.phone || [])];

      // Also check for unverified factors and clean them up
      const unverifiedFactors = allFactors.filter(factor => factor.status !== 'verified');
      if (unverifiedFactors.length > 0) {
        console.log('Found unverified factors, cleaning up:', unverifiedFactors);
        for (const factor of unverifiedFactors) {
          try {
            await supabase.auth.mfa.unenroll({ factorId: factor.id });
            console.log('Successfully unenrolled unverified factor:', factor.id);
          } catch (unenrollError) {
            console.error('Error unenrolling factor:', unenrollError);
          }
        }
      }

      const hasVerifiedFactors = allFactors.some(factor => factor.status === 'verified');
      console.log('Has verified MFA factors:', hasVerifiedFactors);

      setHasMFA(hasVerifiedFactors);
    } catch (err: any) {
      console.error('Error checking MFA status:', err);
      setError(err.message || 'Failed to check MFA status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkMFAStatus();
  }, []);

  const handleEnrollSuccess = () => {
    setShowEnrollment(false);
    checkMFAStatus();
  };

  const handleUnenrollSuccess = () => {
    checkMFAStatus();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex min-w-full justify-center items-center min-h-screen bg-gray-100 p-4">
      <div className="w-full max-w-md">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {showEnrollment ? (
          <EnrollMFA
            onEnrolled={handleEnrollSuccess}
            onCancelled={() => setShowEnrollment(false)}
          />
        ) : hasMFA ? (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ShieldCheck className="h-5 w-5 mr-2 text-green-500" />
                  Two-Factor Authentication
                </CardTitle>
                <CardDescription>
                  Your account is protected with two-factor authentication
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UnenrollMFA onUnenrolled={handleUnenrollSuccess} />
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => router.push('/protected')}>
                Back to Dashboard
              </Button>

              {/* Hidden reset button for troubleshooting */}
              <form action={unenrollMFAAction}>
                <input type="hidden" name="cleanupAll" value="true" />
                <Button
                  type="submit"
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    if (!confirm('This will reset ALL MFA factors. Are you sure?')) {
                      return false;
                    }
                  }}
                >
                  Reset All MFA Factors
                </Button>
              </form>
            </div>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Two-Factor Authentication</CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-4">
                <Shield className="h-10 w-10 text-primary flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-medium">Protect your account</h3>
                  <p className="text-sm text-muted-foreground">
                    Two-factor authentication adds an extra layer of security to your account.
                    In addition to your password, you'll need a code from your authenticator app
                    to sign in.
                  </p>
                </div>
              </div>

              <Button
                className="w-full"
                onClick={() => setShowEnrollment(true)}
              >
                Set up two-factor authentication
              </Button>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push('/protected')}
              >
                Later
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
