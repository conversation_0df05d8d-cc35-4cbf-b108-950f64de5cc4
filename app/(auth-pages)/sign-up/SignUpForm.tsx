'use client';

import { signUpAction } from "@/app/actions";
import { FormMessage } from "@/components/form-message";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { FormEvent, useEffect, useState } from "react";
import { toast } from "sonner";
import { PasswordInputWithStrength } from "@/components/ui/PasswordStrengthIndicator";
import { validatePassword, validatePasswordConfirmation } from "@/utils/password-validation";

export default function SignUpForm() {
  const searchParams = useSearchParams();
  const [orgName, setOrgName] = useState("");
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<string | null>(null);
  const [isOrgReadOnly, setIsOrgReadOnly] = useState(false);
  const [isEmailReadOnly, setIsEmailReadOnly] = useState(false);
  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  // Removed passwordError state as it's handled by the PasswordInputWithStrength component
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Check for invitation token in URL
    const token = searchParams.get('token');
    if (token) {
      console.log(`Found invitation token in URL: ${token}`);
      setInvitationToken(token);
      // Fetch organization details based on token
      fetchOrganizationDetails(token);
    } else {
      setIsLoading(false);
    }
  }, [searchParams]);

  const fetchOrganizationDetails = async (token: string) => {
    try {
      console.log(`Fetching organization details for token: ${token}`);

      // Call API to validate token and get organization details
      const response = await fetch(`/api/invitations/validate?token=${token}`);
      const data = await response.json();

      console.log('Validation response:', data);

      if (data.valid && data.organization) {
        console.log(`Organization found: ${data.organization.name}`);
        setOrgName(data.organization.name);
        setIsOrgReadOnly(true);

        if (data.email) {
          console.log(`Email found in invitation: ${data.email}`);
          setEmail(data.email);
          setIsEmailReadOnly(true);
        }

        if (data.role) {
          console.log(`Role found in invitation: ${data.role}`);
          setRole(data.role);

          // Display a message about the role
          const roleDisplay = data.role === 'owner'
            ? 'Organization Owner'
            : data.role === 'full'
              ? 'Full Rights Member'
              : 'Read-Only Member';

          toast.info(`You are being invited as: ${roleDisplay}`, {
            description: "This determines your access level within the organization.",
            duration: 5000
          });
        }
      } else {
        // If token is invalid, show error message
        console.error('Invalid invitation token response:', data);
        setValidationError(data.error || "Invalid invitation token");
        toast.error("Invalid invitation", {
          description: data.error || "The invitation link is invalid or has expired.",
        });
      }
    } catch (error) {
      console.error("Error validating invitation:", error);
      setValidationError("Error validating invitation");
      toast.error("Error validating invitation", {
        description: "Please try again or contact support.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced password validation when either field changes
  useEffect(() => {
    // Check if passwords match (password strength is handled by PasswordInputWithStrength component)
    const confirmValidation = validatePasswordConfirmation(password, confirmPassword);
    setPasswordsMatch(confirmValidation.isValid);
  }, [password, confirmPassword]);

  if (isLoading) {
    return <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }

  // Handle form submission with enhanced client-side validation
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Reset validation error
    setValidationError(null);

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setValidationError(`Password requirements not met: ${passwordValidation.errors[0]}`);
      return;
    }

    // Check if passwords match
    if (password !== confirmPassword) {
      setPasswordsMatch(false);
      setValidationError("Passwords do not match");
      return;
    }

    // Get form data
    const formData = new FormData(e.currentTarget);

    // Check if phone is provided
    if (!formData.get('phone')) {
      setValidationError("Phone number is required");
      return;
    }

    try {
      // Set submitting state to show loading indicator
      setIsSubmitting(true);

      // Submit the form if all validations pass
      await signUpAction(formData);
    } catch (error) {
      console.error('Error during sign-up:', error);
      setValidationError('An error occurred during sign-up. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="flex flex-col min-w-64" onSubmit={handleSubmit}>
      <h1 className="text-2xl font-medium">Sign up</h1>
      <p className="text-sm text-foreground">
        Already have an account?{" "}
        <Link className="text-primary font-medium underline" href="/sign-in">
          Sign in
        </Link>
      </p>

      {validationError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {validationError}
        </div>
      )}

      <div className="flex flex-col gap-2 [&>input]:mb-3 mt-8">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name *</Label>
            <Input name="firstName" placeholder="John" required />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name *</Label>
            <Input name="lastName" placeholder="Doe" required />
          </div>
        </div>

        <Label htmlFor="email">Email *</Label>
        <Input
          name="email"
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => !isEmailReadOnly && setEmail(e.target.value)}
          readOnly={isEmailReadOnly}
          className={isEmailReadOnly ? "bg-gray-100" : ""}
          required
        />

        <Label htmlFor="phone">Phone Number *</Label>
        <Input
          name="phone"
          type="tel"
          placeholder="+64 21 123 4567"
          required
        />

        <Label htmlFor="organizationName">Organization Name *</Label>
        <Input
          name="organizationName"
          placeholder="Your Company"
          value={orgName}
          onChange={(e) => !isOrgReadOnly && setOrgName(e.target.value)}
          readOnly={isOrgReadOnly}
          className={isOrgReadOnly ? "bg-gray-100" : ""}
          required
        />

        <Label htmlFor="password">Password *</Label>
        <PasswordInputWithStrength
          value={password}
          onChange={setPassword}
          name="password"
          placeholder="Create a strong password"
          required
          showRequirements={true}
          showGenerator={true}
          onConfirmPasswordChange={setConfirmPassword}
        />

        <Label htmlFor="confirmPassword" className={!passwordsMatch ? "text-red-500" : ""}>
          Confirm Password *
          {!passwordsMatch && <span className="ml-2 text-xs">(Passwords do not match)</span>}
        </Label>
        <Input
          type="password"
          name="confirmPassword"
          placeholder="Confirm your password"
          minLength={8}
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          className={!passwordsMatch ? "border-red-500" : ""}
          required
        />

        {/* Hidden fields to pass the invitation token and role if present */}
        {invitationToken && (
          <input type="hidden" name="invitationToken" value={invitationToken} />
        )}
        {role && (
          <input type="hidden" name="role" value={role} />
        )}

        <div className="mt-2 text-xs text-gray-500">
          * Required fields
        </div>

        <button
          type="submit"
          className="bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 rounded-md mt-2 flex items-center justify-center"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Signing up...
            </>
          ) : (
            'Sign up'
          )}
        </button>
        <FormMessage message={undefined} />
      </div>
    </form>
  );
}
