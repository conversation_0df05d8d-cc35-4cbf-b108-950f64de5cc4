import { FormMessage, Message } from "@/components/form-message";
import { Suspense } from "react";
import SignUpForm from "./SignUpForm";


export default function Signup({ searchParams }: { searchParams?: Message }) {
  if (searchParams && typeof searchParams === 'object' && "message" in searchParams) {
    return (
      <div className="w-full flex-1 flex items-center h-screen sm:max-w-md justify-center gap-2 p-4">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <div className="flex min-w-full justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <Suspense fallback={<div>Loading...</div>}>
          <SignUpForm />
        </Suspense>
      </div>
    </div>
  );
}
