"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Sidebar from '@/components/sidebars/Sidebar';
import { createClient } from '@/utils/supabase/client';
import type { Database } from '@/lib/database.types';
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, MoreVertical } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import GenerateDocumentsModal from '@/components/modals/GenerateDocumentsModal';
import UploadDocumentsModal from '@/components/modals/UploadDocumentsModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

interface Household {
  householdName: string;
}

interface SOADocument {
  id: number;
  household_id: number;
  created_at: string;
  file_name: string;
  file_type: string;
  file_size: number;
  households: {
    householdName: string;
  };
}

interface TokenDocument {
  member_id: any;
  id: number;
  household_id: number;
  created_at: string;
  status: string;
  token: string;
  expires_at: string;
  households: {
    members(members: any, member_id: any): unknown;
    householdName: string;
  };
}

interface Document {
  created_at: string | number | Date;
  id: number;
  type: 'other' | 'toe' | 'discovery' | 'risk' | 'soa' | 'tpa';
  name?: string;
  file_name?: string;
  status?: string;
  household_id: number;
  household_name: string;
  file_type?: string;
  file_size?: number;
  token?: string;
  expires_at?: string;
  file_url?: string;
  path?: string;
  details?: string;
}

const getMemberName = (members: any, memberId: number): string => {
  if (!members || !memberId) return '';
  
  const memberKey = `name${memberId}`;
  return members[memberKey] || '';
};

export default function DocumentLibrary() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocType, setSelectedDocType] = useState<string>('all');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const documentTypes = [
    { value: 'all', label: 'All Documents' },
    { value: 'soa', label: 'SOA' },
    { value: 'discovery', label: 'Discovery Document' },
    { value: 'toe', label: 'TOE' },
    { value: 'tpa', label: 'Third Party Authority' },
    { value: 'risk', label: 'Risk Profiler' },
    { value: 'other', label: 'Other Documents' }
  ];

  const fetchDocuments = async () => {
    const supabase = createClient();
    setLoading(true);
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('No authenticated user found');
      setLoading(false);
      return;
    }

    try {
      // Fetch SOA documents
      const { data: soaData, error: soaError } = await supabase
        .from('soa_documents')
        .select('*, households(householdName)')
        .order('created_at', { ascending: false }) as { data: SOADocument[] | null, error: any };

      // Fetch TOE documents
      const { data: toeData, error: toeError } = await supabase
        .from('toe_tokens')
        .select('*, households(householdName)')
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch Discovery documents
      const { data: discoveryData, error: discoveryError } = await supabase
        .from('discovery_tokens')
        .select('*, households(householdName)')
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch TPA documents
      const { data: tpaData, error: tpaError } = await supabase
        .from('tpa_tokens')
        .select('*, households:household_id(householdName, members)')
        .eq('created_by', user.id);

      // Fetch Risk Profiler documents
      const { data: riskData, error: riskError } = await supabase
        .from('risk_profiler_tokens')
        .select('*, households:household_id(householdName, members)')
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch Other documents
      const { data: otherData, error: otherError } = await supabase
        .from('other_documents')
        .select('*, households(householdName)')
        .order('created_at', { ascending: false });

      if (soaError) console.error('Error fetching SOA documents:', soaError);
      if (toeError) console.error('Error fetching TOE documents:', toeError);
      if (discoveryError) console.error('Error fetching Discovery documents:', discoveryError);
      if (tpaError) console.error('Error fetching TPA documents:', tpaError);
      if (riskError) console.error('Error fetching Risk Profiler documents:', riskError);
      if (otherError) console.error('Error fetching Other documents:', otherError);

      // Transform and combine all documents
      const allDocuments: Document[] = [
        ...(soaData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'soa' as const,
            name: doc.file_name,
            created_at: doc.created_at,
            status: 'completed',
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            file_type: doc.file_type,
            file_size: doc.file_size
          })) || []),
        ...(toeData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'toe' as const,
            name: 'Terms of Engagement',
            created_at: doc.created_at,
            status: doc.status,
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            token: doc.token,
            expires_at: doc.expires_at
          })) || []),
        ...(discoveryData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'discovery' as const,
            name: 'Discovery Document',
            created_at: doc.created_at,
            status: doc.status,
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            token: doc.token,
            expires_at: doc.expires_at
          })) || []),
        ...(tpaData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'tpa' as const,
            name: 'Third Party Authority',
            created_at: doc.created_at,
            status: doc.status,
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            token: doc.token,
            expires_at: doc.expires_at,
            details: doc.member_id ? `(${getMemberName(doc.households.members, doc.member_id)})` : ''
          })) || []),
        ...(riskData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'risk' as const,
            name: 'Risk Profiler',
            created_at: doc.created_at,
            status: doc.status,
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            token: doc.token,
            expires_at: doc.expires_at,
            details: doc.member_id ? `(${getMemberName(doc.households.members, doc.member_id)})` : ''
          })) || []),
        ...(otherData?.filter(doc => doc.households?.householdName)
          .map(doc => ({
            id: doc.id,
            type: 'other' as const,
            name: doc.file_name,
            created_at: doc.created_at,
            status: 'completed',
            household_id: doc.household_id,
            household_name: doc.households.householdName,
            file_type: doc.file_type,
            file_size: doc.file_size,
            file_url: doc.file_url,
            path: doc.path
          })) || [])
      ];

      setDocuments(allDocuments);
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
    
    setLoading(false);
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleSelectAll = (checked: boolean | string) => {
    if (checked === true) {
      const newSelected = new Set<string>();
      filteredDocuments.forEach(doc => {
        newSelected.add(`${doc.type}-${doc.id}`);
      });
      setSelectedDocuments(newSelected);
    } else {
      setSelectedDocuments(new Set());
    }
  };

  const handleSelectRow = (docId: string, checked: boolean | string) => {
    const [type, id] = docId.split('-');
    const doc = filteredDocuments.find(d => d.type === type && d.id === Number(id));
    
    // Allow selecting completed documents only for viewing/downloading
    const newSelected = new Set(selectedDocuments);
    if (checked === true) {
      newSelected.add(docId);
    } else {
      newSelected.delete(docId);
    }
    setSelectedDocuments(newSelected);
  };

  // Filter documents based on search query and selected type
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = (
      doc.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.file_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.household_name || '').toLowerCase().includes(searchQuery.toLowerCase())
    );
    const matchesType = selectedDocType === 'all' || doc.type === selectedDocType;
    return matchesSearch && matchesType;
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDelete = async () => {
    if (!documentToDelete) return;

    const supabase = createClient();
    let table = '';
    
    switch (documentToDelete.type) {
      case 'soa':
        table = 'soa_documents';
        break;
      case 'discovery':
        table = 'discovery_tokens';
        break;
      case 'toe':
        table = 'toe_tokens';
        break;
      case 'tpa':
        table = 'tpa_tokens';
        break;
      case 'risk':
        table = 'risk_profiler_tokens';
        break;
      case 'other':
        table = 'other_documents';
        break;
    }

    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', documentToDelete.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Document deleted successfully"
      });

      // Refresh the documents list
      await fetchDocuments();
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete document"
      });
    } finally {
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
    }
  };

  const handleViewDocument = async (doc: Document) => {
    if (doc.type === 'toe' || doc.type === 'discovery' || doc.type === 'risk' || doc.type === 'tpa') {
      const baseUrl = window.location.origin;
      let url = '';
      
      switch (doc.type) {
        case 'toe':
          url = `${baseUrl}/toe-form/${doc.token}`;
          break;
        case 'discovery':
          url = `${baseUrl}/discovery-form/${doc.token}`;
          break;
        case 'risk':
          url = `${baseUrl}/risk-profiler-form/${doc.token}`;
          break;
        case 'tpa':
          url = `${baseUrl}/tpa-form/${doc.token}`;
          break;
      }
      
      window.open(url, '_blank');
      return;
    }
    
    if (!doc.path) {
      toast({
        title: "Error",
        description: "Document path not found",
        variant: "destructive"
      });
      return;
    }
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage
        .from('documents')
        .createSignedUrl(doc.path, 60 * 60); // 1 hour expiry

      if (error) {
        console.error('Error creating signed URL:', error);
        toast({
          title: "Error",
          description: "Failed to view document",
          variant: "destructive"
        });
        return;
      }

      if (!data?.signedUrl) {
        toast({
          title: "Error",
          description: "Failed to generate document URL",
          variant: "destructive"
        });
        return;
      }

      window.open(data.signedUrl, '_blank');
    } catch (error) {
      console.error('Error viewing document:', error);
      toast({
        title: "Error",
        description: "Failed to view document",
        variant: "destructive"
      });
    }
  };

  const handleDownload = async (doc: Document) => {
    if (doc.type === 'toe' || doc.type === 'discovery' || doc.type === 'risk' || doc.type === 'tpa') {
      const baseUrl = window.location.origin;
      let url = '';
      
      switch (doc.type) {
        case 'toe':
          url = `${baseUrl}/toe-form/${doc.token}`;
          break;
        case 'discovery':
          url = `${baseUrl}/discovery-form/${doc.token}`;
          break;
        case 'risk':
          url = `${baseUrl}/risk-profiler-form/${doc.token}`;
          break;
        case 'tpa':
          url = `${baseUrl}/tpa-form/${doc.token}`;
          break;
      }
      
      // Open in new tab where user can use browser's print/save as PDF functionality
      window.open(url, '_blank');
      toast({
        title: "Info",
        description: "Document opened in new tab. Use browser's print function to save as PDF."
      });
      return;
    }
    
    if (!doc.path) {
      toast({
        title: "Error",
        description: "Document path not found",
        variant: "destructive"
      });
      return;
    }
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase.storage
        .from('documents')
        .download(doc.path);

      if (error) {
        console.error('Error downloading file:', error);
        toast({
          title: "Error",
          description: "Failed to download document",
          variant: "destructive"
        });
        return;
      }

      // Create blob URL and trigger download
      const url = URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.file_name || doc.name || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast({
        title: "Error",
        description: "Failed to download document",
        variant: "destructive"
      });
    }
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0) return;

    const supabase = createClient();
    const selectedDocs = filteredDocuments.filter(doc => 
      selectedDocuments.has(`${doc.type}-${doc.id}`)
    );

    try {
      for (const doc of selectedDocs) {
        let table = '';
        switch (doc.type) {
          case 'soa':
            table = 'soa_documents';
            break;
          case 'discovery':
            table = 'discovery_tokens';
            break;
          case 'toe':
            table = 'toe_tokens';
            break;
          case 'tpa':
            table = 'tpa_tokens';
            break;
          case 'risk':
            table = 'risk_profiler_tokens';
            break;
          case 'other':
            table = 'other_documents';
            break;
        }

        const { error } = await supabase
          .from(table)
          .delete()
          .eq('id', doc.id);

        if (error) throw error;
      }

      toast({
        title: "Success",
        description: `Successfully deleted ${selectedDocs.length} documents`
      });

      // Refresh the documents list
      await fetchDocuments();
      setSelectedDocuments(new Set());
    } catch (error) {
      console.error('Error deleting documents:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete some documents"
      });
    } finally {
      setShowDeleteDialog(false);
    }
  };

  const handleBulkAction = async (action: 'delete' | 'copy' | 'download' | 'view', format?: 'pdf' | 'docx') => {
    const selectedDocs = filteredDocuments.filter(doc => 
      selectedDocuments.has(`${doc.type}-${doc.id}`)
    );

    if (selectedDocs.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select at least one document"
      });
      return;
    }

    switch (action) {
      case 'delete':
        setShowDeleteDialog(true);
        break;
      case 'view':
        // Open all selected completed documents in new tabs
        selectedDocs
          .filter(doc => doc.status === 'completed' && doc.type !== 'soa')
          .forEach(doc => handleViewDocument(doc));
        break;
      case 'copy':
        // Copy all links to clipboard (only for non-completed documents)
        const links = selectedDocs
          .filter(doc => doc.type !== 'soa' && doc.token && doc.status !== 'completed')
          .map(doc => {
            const baseUrl = window.location.origin;
            switch (doc.type) {
              case 'toe':
                return `${baseUrl}/toe-form/${doc.token}`;
              case 'discovery':
                return `${baseUrl}/discovery-form/${doc.token}`;
              case 'risk':
                return `${baseUrl}/risk-profiler-form/${doc.token}`;
              case 'tpa':
                return `${baseUrl}/tpa-form/${doc.token}`;
              default:
                return '';
            }
          })
          .filter(link => link);

        if (links.length > 0) {
          try {
            await navigator.clipboard.writeText(links.join('\n'));
            toast({
              title: "Success",
              description: `Copied ${links.length} links to clipboard`
            });
          } catch (error) {
            toast({
              variant: "destructive",
              title: "Error",
              description: "Failed to copy links"
            });
          }
        }
        break;
      case 'download':
        // Download all selected documents
        const docsToDownload = selectedDocs.filter(doc => 
          doc.type === 'soa' || doc.status === 'completed'
        );
        if (docsToDownload.length > 0 && format) {
          docsToDownload.forEach(doc => handleDownload(doc));
        }
        break;
    }
  };

  function copyLink(doc: Document): void {
    if (!doc.token) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No link available for this document"
      });
      return;
    }

    const baseUrl = window.location.origin;
    let url = '';

    switch (doc.type) {
      case 'toe':
        url = `${baseUrl}/toe-form/${doc.token}`;
        break;
      case 'discovery':
        url = `${baseUrl}/discovery-form/${doc.token}`;
        break;
      case 'risk':
        url = `${baseUrl}/risk-profiler-form/${doc.token}`;
        break;
      case 'tpa':
        url = `${baseUrl}/tpa-form/${doc.token}`;
        break;
      default:
        toast({
          variant: "destructive",
          title: "Error",
          description: "Cannot generate link for this document type"
        });
        return;
    }

    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: "Success",
        description: "Link copied to clipboard"
      });
    }).catch(() => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to copy link to clipboard"
      });
    });
  }

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent>
            <div className="flex items-center space-x-4 mb-4 mt-4">
              {selectedDocuments.size > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      Bulk Actions ({selectedDocuments.size})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {Array.from(selectedDocuments).some(id => {
                      const doc = filteredDocuments.find(doc => `${doc.type}-${doc.id}` === id);
                      return doc && (doc.type === 'soa' || doc.status === 'completed');
                    }) && (
                      <DropdownMenuItem onClick={() => handleBulkAction('download', 'pdf')}>
                        Download PDF
                      </DropdownMenuItem>
                    )}
                    {Array.from(selectedDocuments).some(id => {
                      const doc = filteredDocuments.find(doc => `${doc.type}-${doc.id}` === id);
                      return doc && doc.type === 'soa';
                    }) && (
                      <DropdownMenuItem onClick={() => handleBulkAction('download', 'docx')}>
                        Download DOCX
                      </DropdownMenuItem>
                    )}
                    {Array.from(selectedDocuments).some(id => {
                      const doc = filteredDocuments.find(doc => `${doc.type}-${doc.id}` === id);
                      return doc && doc.status === 'completed' && doc.type !== 'soa';
                    }) && (
                      <DropdownMenuItem onClick={() => handleBulkAction('view')}>
                        View Selected
                      </DropdownMenuItem>
                    )}
                    {Array.from(selectedDocuments).some(id => {
                      const doc = filteredDocuments.find(doc => `${doc.type}-${doc.id}` === id);
                      return doc && doc.type !== 'soa' && doc.status !== 'completed';
                    }) && (
                      <DropdownMenuItem onClick={() => handleBulkAction('copy')}>
                        Copy Links
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => handleBulkAction('delete')}
                    >
                      Delete Selected
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name or household..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select
                value={selectedDocType}
                onValueChange={setSelectedDocType}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={() => setShowGenerateModal(true)}>
                Generate Digital Documents
              </Button>
              <Button variant="outline" onClick={() => setShowUploadModal(true)}>
                Upload Documents
              </Button>
            </div>

            {loading ? (
              <div>Loading documents...</div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={
                            filteredDocuments.length > 0 &&
                            filteredDocuments.every(doc => 
                              selectedDocuments.has(`${doc.type}-${doc.id}`)
                            )
                          }
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all"
                        />
                      </TableHead>
                      <TableHead>Document</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Household</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocuments.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center">
                          No documents found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredDocuments.map((doc) => (
                        <TableRow key={`${doc.type}-${doc.id}`}>
                          <TableCell>
                            <Checkbox
                              checked={selectedDocuments.has(`${doc.type}-${doc.id}`)}
                              onCheckedChange={(checked) => 
                                handleSelectRow(`${doc.type}-${doc.id}`, checked)
                              }
                              aria-label={`Select ${doc.name}`}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{doc.name || doc.file_name?.split('.')[0]}</TableCell>
                          <TableCell className="text-muted-foreground">
                            {doc.file_name || doc.details || ''}
                          </TableCell>
                          <TableCell>{documentTypes.find(t => t.value === doc.type)?.label}</TableCell>
                          <TableCell>{doc.household_name || `-`}</TableCell>
                          <TableCell>{new Date(doc.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(doc.status || '')}`}>
                              {doc.status || '-'}
                            </span>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {doc.type === 'other' && (
                                  <>
                                    <DropdownMenuItem onClick={() => handleViewDocument(doc)}>
                                      View Document
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleDownload(doc)}>
                                      Download
                                    </DropdownMenuItem>
                                    <DropdownMenuItem 
                                      onClick={() => {
                                        setDocumentToDelete(doc);
                                        setShowDeleteDialog(true);
                                      }}
                                      className="text-red-600"
                                    >
                                      Delete
                                    </DropdownMenuItem>
                                  </>
                                )}
                                {(doc.type === 'toe' || doc.type === 'discovery' || doc.type === 'risk' || doc.type === 'tpa') && (
                                  <>
                                    <DropdownMenuItem onClick={() => handleViewDocument(doc)}>
                                      View Document
                                    </DropdownMenuItem>
                                    {doc.status === 'completed' && (
                                      <DropdownMenuItem onClick={() => handleDownload(doc)}>
                                        Download
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem onClick={() => copyLink(doc)}>
                                      Copy Link
                                    </DropdownMenuItem>
                                    <DropdownMenuItem 
                                      onClick={() => {
                                        setDocumentToDelete(doc);
                                        setShowDeleteDialog(true);
                                      }}
                                      className="text-red-600"
                                    >
                                      Delete
                                    </DropdownMenuItem>
                                  </>
                                )}
                                {doc.type === 'soa' && (
                                  <>
                                    <DropdownMenuItem onClick={() => handleViewDocument(doc)}>
                                      View Document
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleDownload(doc)}>
                                      Download
                                    </DropdownMenuItem>
                                    <DropdownMenuItem 
                                      onClick={() => {
                                        setDocumentToDelete(doc);
                                        setShowDeleteDialog(true);
                                      }}
                                      className="text-red-600"
                                    >
                                      Delete
                                    </DropdownMenuItem>
                                  </>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <GenerateDocumentsModal 
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
        onSuccess={fetchDocuments}
      />
      <UploadDocumentsModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onSuccess={fetchDocuments}
      />
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedDocuments.size > 0 
                ? `This will permanently delete ${selectedDocuments.size} selected documents. This action cannot be undone.`
                : `This will permanently delete the document ${documentToDelete?.name || documentToDelete?.file_name}. This action cannot be undone.`
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={selectedDocuments.size > 0 ? handleBulkDelete : handleDelete}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
