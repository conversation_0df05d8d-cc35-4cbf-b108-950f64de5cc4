import { FUND_TYPES } from "@/app/constants/fundTypes";
import { ChartConfig } from "@/components/ui/chart";
import { CheckedState } from "@radix-ui/react-checkbox";

// First, export the EventType from WhatIfTab.tsx or define it here
export type EventType =
  | 'recession'
  | 'death'
  | 'tpd'
  | 'trauma'
  | 'redundancy'
  | 'maternity'
  | 'inheritance';

export interface InputData {
    [key: string]: any; // Add index signature to allow string indexing

    consolidation_allocations?: Array<{ fundNumber: number; percentage: number }>;
    partner_consolidation_allocations?: Array<{ fundNumber: number; percentage: number }>;
    investment_metrics?: any;
    fund1?: any;
    fund2?: any;
    fund3?: any;
    fund4?: any;
    fund5?: any;

    annual_investment_contribution1: number;
    pay_off_debt_on_downsize: any;
    initial_debt_years2: number;
    initial_debt_years3: number;
    initial_debt_years4: number;
    initial_debt_years5: number;
    property_title2: string;
    property_title3: string;
    property_title4: string;
    property_title5: string;
    whatIfEvents?: Array<{
        id: string;
        type: EventType;
        age: number;
        [key: string]: any;
    }>;
    lump_sum_payment_age?: number;
    lump_sum_payment_amount?: number;
    lump_sum_payment_source?: 'investments' | 'savings';
    lump_sum_payment_age2?: number;
    lump_sum_payment_amount2?: number;
    lump_sum_payment_source2?: 'investments' | 'savings';
    lump_sum_payment_age3?: number;
    lump_sum_payment_amount3?: number;
    lump_sum_payment_source3?: 'investments' | 'savings';
    lump_sum_payment_age4?: number;
    lump_sum_payment_amount4?: number;
    lump_sum_payment_source4?: 'investments' | 'savings';
    lump_sum_payment_age5?: number;
    lump_sum_payment_amount5?: number;
    lump_sum_payment_source5?: 'investments' | 'savings';
    property_expenses: string;
    expense1_inflation_rate: number;
    expense2_inflation_rate: number;
    main_income_inflation_rate: number;
    partner_income_inflation_rate: number;
    sale_allocate_to_investment: CheckedState | undefined;
    sale_investment_fund: string;
    downsize: CheckedState | undefined;
    downsize_age: string | number | readonly string[] | undefined;
    new_property_value: string | number | readonly string[] | undefined;
    fund_diversion: string | undefined;
    utilise_excess_cashflow: CheckedState | undefined;
    contribution_period: any;
    household_name: string;
    name: string;
    partner_name: string | number | readonly string[] | undefined;
    savings_owner: string | undefined;
    starting_age: number;
    ending_age: number;
    annual_income: number;
    income_period: [number, number];
    partner_starting_age: number;
    partner_ending_age?: number;
    partner_annual_income: number;
    partner_income_period: [number, number];
    superannuation: boolean;
    single_super_rate?: number;
    couple_one_super_rate?: number;
    couple_both_super_rate?: number;
    super_eligibility_age?: number;
    includePartner: boolean;
    savings_amount: number;
    cash_reserve: number;
    saving_percentage: number;
    cash_rate: number;
    savings_allocation?: number;
    annual_expenses1: number;
    expense_period1: [number, number];
    second_expense: boolean;
    annual_expenses2: number;
    expense_period2: [number, number];
    additional_incomes: Array<{
      frequency: boolean;
      title: string;
      value: number;
      period: [number, number];
      tax_type?: 'main' | 'partner' | 'tax_free';
      inflation_rate?: number;
    }>;
    additional_expenses?: Array<{
      inflation_rate?: number;
      frequency: number;
      title: string;
      value: number;
      period: [number, number];
    }>;
    one_off_investments?: Array<{
      amount: number;
      age: number;
      details?: string;
      specificFund?: number;
      allocations?: {
        fund1?: number;
        fund2?: number;
        fund3?: number;
        fund4?: number;
        fund5?: number;
      };
    }>;
    // Legacy investment fund (for backward compatibility)
    initial_investment: number;
    annual_investment_contribution: number;
    annual_investment_return: number;
    inv_std_dev: number;
    investment_return_period: [number, number];
    investment_description: string;
    inv_tax: string;

    // Investment fund 1
    initial_investment1?: number;
    annual_investment_return1?: number;
    inv_std_dev1?: number;
    investment_description1?: string;
    investment_allocation1?: number;

    // Investment fund 2
    initial_investment2?: number;
    annual_investment_contribution2?: number;
    annual_investment_return2?: number;
    inv_std_dev2?: number;
    investment_return_period2?: [number, number];
    investment_description2?: string;
    inv_tax2?: string;
    investment_allocation2?: number;

    // Investment fund 3
    initial_investment3?: number;
    annual_investment_contribution3?: number;
    annual_investment_return3?: number;
    inv_std_dev3?: number;
    investment_return_period3?: [number, number];
    investment_description3?: string;
    inv_tax3?: string;
    investment_allocation3?: number;

    // Investment fund 4
    initial_investment4?: number;
    annual_investment_contribution4?: number;
    annual_investment_return4?: number;
    inv_std_dev4?: number;
    investment_return_period4?: [number, number];
    investment_description4?: string;
    inv_tax4?: string;
    investment_allocation4?: number;

    // Investment fund 5
    initial_investment5?: number;
    annual_investment_contribution5?: number;
    annual_investment_return5?: number;
    inv_std_dev5?: number;
    investment_return_period5?: [number, number];
    investment_description5?: string;
    inv_tax5?: string;
    investment_allocation5?: number;

    // Withdrawal priorities for investment funds
    // Array of fund numbers (1-5) in order of withdrawal priority
    // First fund in the array will be withdrawn from first
    withdrawal_priorities?: number[];
    initial_kiwiSaver: number;
    kiwisaver_contribution: number;
    employer_contribution: number;
    annual_kiwisaver_return: number;
    ks_std_dev: number;
    partner_initial_kiwisaver: number;
    partner_kiwisaver_contribution: number;
    partner_employer_contribution: number;
    partner_annual_kiwisaver_return: number;
    partner_ks_std_dev: number;
    property_value: number;
    property_title?: string;
    debt: number;
    property_growth: number;
    debt_ir: number;
    initial_debt_years: number;
    additional_debt_repayments: number;
    additional_debt_repayments_start_age?: number;
    additional_debt_repayments_end_age?: number;
    include_property_debt: boolean;
    purchase_age?: number;
    deposit_amount?: number;
    deposit_sources?: {
      savings?: number;
      investments?: number;
      main_kiwisaver?: number;
      partner_kiwisaver?: number;
      gifting?: number;
      other?: number;
    };
    inflation_rate: number;
    sell_main_property: boolean;
    main_property_sale_age: number;
    main_prop_sale_value: number;
    pay_off_debt: boolean;
    show_monte_carlo: boolean;
    num_simulations: number;
    confidence_interval: number;
    worst_scenario: number[];
    best_scenario: number[];
    average_scenario: number[];
    show_repayments: boolean;
    show_cashflow_breakdown: boolean;
    show_purchase_details?: boolean;
    household_id: number;
    seed: number;
    inv_fund_type: string;
    ks_fund_type: string;
    partner_ks_fund_type?: string;
    fund_periods: Array<{
      incomePortion: any;
      fundId: any;
      fundType: string;
      period: [number, number];
      return: number;
      stdDev: number;
    }>;
    ks_periods?: Array<{
      fundId(fundType: string, fundId: any): unknown;
      fundType: keyof typeof FUND_TYPES;
      period: [number, number];
      return: number;
      stdDev: number;
    }>;
    partner_ks_periods?: Array<{
      fundId(fundType: string, fundId: any): unknown;
      fundType: keyof typeof FUND_TYPES;
      period: [number, number];
      return: number;
      stdDev: number;
    }>;
    consolidate_kiwisaver?: boolean;
    consolidate_kiwisaver_age?: number;
    partner_consolidate_kiwisaver?: boolean;
    partner_consolidate_kiwisaver_age?: number;
    allocate_to_investment: boolean;
    // Second property fields
    property_value2?: number;
    property_growth2?: number;
    debt2?: number;
    debt_ir2?: number;
    debt_years2?: number;
    additional_debt_repayments2?: number;
    additional_debt_repayments_start_age2?: number;
    additional_debt_repayments_end_age2?: number;
    include_property_debt2?: boolean;
    show_purchase_details2?: boolean;
    purchase_age2?: number;
    deposit_amount2?: number;
    deposit_sources2?: {
      savings?: number;
      investments?: number;
      main_kiwisaver?: number;
      partner_kiwisaver?: number;
      gifting?: number;
      other?: number;
    };
    main_property_sale_age2?: number;
    main_prop_sale_value2?: number;
    pay_off_debt2?: boolean;
    sale2_allocate_to_investment?: boolean;
    is_second_property?: boolean;
    sell_property?: boolean;
    sell_main_property2?: boolean;
    // Third property fields
    property_value3?: number;
    property_growth3?: number;
    debt3?: number;
    debt_ir3?: number;
    debt_years3?: number;
    additional_debt_repayments3?: number;
    additional_debt_repayments_start_age3?: number;
    additional_debt_repayments_end_age3?: number;
    show_purchase_details3?: boolean;
    purchase_age3?: number;
    deposit_amount3?: number;
    deposit_sources3?: {
      savings?: number;
      investments?: number;
      main_kiwisaver?: number;
      partner_kiwisaver?: number;
      gifting?: number;
      other?: number;
    };
    starting_age3?: number;
    downsize_age3?: number;
    new_property_value3?: number;
    sell_main_property3?: boolean;
    main_property_sale_age3?: number;
    main_prop_sale_value3?: number;
    pay_off_debt3?: boolean;
    sale3_allocate_to_investment?: boolean;
    // Fourth property fields
    property_value4?: number;
    property_growth4?: number;
    debt4?: number;
    debt_ir4?: number;
    debt_years4?: number;
    additional_debt_repayments4?: number;
    additional_debt_repayments_start_age4?: number;
    additional_debt_repayments_end_age4?: number;
    show_purchase_details4?: boolean;
    purchase_age4?: number;
    deposit_amount4?: number;
    deposit_sources4?: {
      savings?: number;
      investments?: number;
      main_kiwisaver?: number;
      partner_kiwisaver?: number;
      gifting?: number;
      other?: number;
    };
    starting_age4?: number;
    downsize_age4?: number;
    new_property_value4?: number;
    sell_main_property4?: boolean;
    main_property_sale_age4?: number;
    main_prop_sale_value4?: number;
    pay_off_debt4?: boolean;
    sale4_allocate_to_investment?: boolean;
    // Fifth property fields
    property_value5?: number;
    property_growth5?: number;
    debt5?: number;
    debt_ir5?: number;
    debt_years5?: number;
    additional_debt_repayments5?: number;
    additional_debt_repayments_start_age5?: number;
    additional_debt_repayments_end_age5?: number;
    show_purchase_details5?: boolean;
    purchase_age5?: number;
    deposit_amount5?: number;
    deposit_sources5?: {
      savings?: number;
      investments?: number;
      main_kiwisaver?: number;
      partner_kiwisaver?: number;
      gifting?: number;
      other?: number;
    };
    starting_age5?: number;
    downsize_age5?: number;
    new_property_value5?: number;
    sell_main_property5?: boolean;
    main_property_sale_age5?: number;
    main_prop_sale_value5?: number;
    pay_off_debt5?: boolean;
    sale5_allocate_to_investment?: boolean;
    // Property index for tracking which property (1-5) is being referenced
    property_index?: number;
    interest_only_period?: boolean;
    interest_only_start_age?: number;
    interest_only_end_age?: number;
    // Interest-only periods for property 2
    interest_only_period2?: boolean;
    interest_only_start_age2?: number;
    interest_only_end_age2?: number;
    // Interest-only periods for property 3
    interest_only_period3?: boolean;
    interest_only_start_age3?: number;
    interest_only_end_age3?: number;
    // Interest-only periods for property 4
    interest_only_period4?: boolean;
    interest_only_start_age4?: number;
    interest_only_end_age4?: number;
    // Interest-only periods for property 5
    interest_only_period5?: boolean;
    interest_only_start_age5?: number;
    interest_only_end_age5?: number;

    // Rental/Board income fields for properties
    rental_income?: boolean;
    rental_amount?: number;
    rental_start_age?: number;
    rental_end_age?: number;

    rental_income2?: boolean;
    rental_amount2?: number;
    rental_start_age2?: number;
    rental_end_age2?: number;

    rental_income3?: boolean;
    rental_amount3?: number;
    rental_start_age3?: number;
    rental_end_age3?: number;

    rental_income4?: boolean;
    rental_amount4?: number;
    rental_start_age4?: number;
    rental_end_age4?: number;

    rental_income5?: boolean;
    rental_amount5?: number;
    rental_start_age5?: number;
    rental_end_age5?: number;

    board_income?: boolean;
    board_amount?: number;
    board_start_age?: number;
    board_end_age?: number;

    board_income2?: boolean;
    board_amount2?: number;
    board_start_age2?: number;
    board_end_age2?: number;

    board_income3?: boolean;
    board_amount3?: number;
    board_start_age3?: number;
    board_end_age3?: number;

    board_income4?: boolean;
    board_amount4?: number;
    board_start_age4?: number;
    board_end_age4?: number;

    board_income5?: boolean;
    board_amount5?: number;
    board_start_age5?: number;
    board_end_age5?: number;
  }

  export const chartConfig: ChartConfig = {
    netWealth: {
      label: 'Net Wealth',
      color: 'hsl(var(--chart-2))',
    },
    income: {
      label: 'Income',
      color: 'hsl(var(--chart-2))',
    },
    expenses: {
      label: 'Expenses',
      color: 'hsl(var(--chart-1))',
    },
    savings: {
      label: 'Savings Fund',
      color: 'hsl(var(--chart-3))',
    },
    investment: {
      label: 'Investment Fund',
      color: 'hsl(var(--chart-4))',
    },
    investment1: {
      label: 'Investment Fund 1',
      color: 'hsl(var(--chart-4))',
    },
    investment2: {
      label: 'Investment Fund 2',
      color: 'hsl(var(--chart-6))',
    },
    investment3: {
      label: 'Investment Fund 3',
      color: 'hsl(var(--chart-7))',
    },
    investment4: {
      label: 'Investment Fund 4',
      color: 'hsl(var(--chart-8))',
    },
    investment5: {
      label: 'Investment Fund 5',
      color: 'hsl(var(--chart-9))',
    },
    kiwisaver: {
      label: 'KiwiSaver',
      color: 'hsl(var(--chart-5))',
    },
    property: {
      label: 'Property Value',
      color: 'hsl(200, 100%, 50%)',
    },
    debt: {
      label: 'Debt Value',
      color: 'hsl(0, 100%, 50%)',
    },
    principal: {
      label: 'Principal Repayment',
      color: 'hsl(120, 70%, 40%)',
    },
    interest: {
      label: 'Interest Payment',
      color: 'hsl(30, 100%, 50%)',
    },
  };
