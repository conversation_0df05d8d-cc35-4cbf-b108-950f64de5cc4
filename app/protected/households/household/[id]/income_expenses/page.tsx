'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import Income_ExpenseTable, { Income as TableIncome, Expense as TableExpense } from '@/components/tables/Income_ExpenseTable';
import IncomeModal from '@/components/modals/IncomeModal';
import ExpenditureModal from '@/components/modals/ExpenditureModal';
import { Asset, Liability } from '@/components/tables/Assets_LiabilitiesTable';

// Using the interfaces from the table component
type Income = TableIncome;
type Expense = TableExpense;

export default function IncomeExpenses() {
  const params = useParams();
  const householdId = params.id as string;
  const [incomes, setIncomes] = useState<Income[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [liabilities, setLiabilities] = useState<Liability[]>([]);
  const [isIncomeModalOpen, setIsIncomeModalOpen] = useState(false);
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
  const [incomeToEdit, setIncomeToEdit] = useState<Income | undefined>(undefined);
  const [expenseToEdit, setExpenseToEdit] = useState<Expense | undefined>(undefined);

  const fetchData = async () => {
    const supabase = createClient();

    // Fetch household data to get member names
    const { data: householdData, error: householdError } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    let memberMap: {[key: string]: string} = {};
    if (!householdError && householdData?.members) {
      const members = householdData.members;

      // Map member IDs to names
      // 0 = Household
      memberMap['0'] = 'Household';

      // 1 = Main member (name1)
      if (members.name1) {
        memberMap['1'] = members.name1;
      }

      // 2 = Partner (name2)
      if (members.name2) {
        memberMap['2'] = members.name2;
      }
    }

    // Fetch income
    const { data: incomesData, error: incomesError } = await supabase
      .from('income')
      .select('*')
      .eq('household_id', householdId);

    if (incomesError) {
      console.error('Error fetching income:', incomesError);
    } else {
      // Add member names to income data
      const processedIncomes = (incomesData || []).map(income => {
        let memberId = '0'; // Default to household

        if (income.member_id !== undefined && income.member_id !== null) {
          memberId = income.member_id.toString();
        }

        return {
          ...income,
          member_name: memberMap[memberId] || `Member ${memberId}`
        };
      });

      setIncomes(processedIncomes);
    }

    // Fetch expenses
    const { data: expensesData, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('household_id', householdId);

    if (expensesError) {
      console.error('Error fetching expenses:', expensesError);
    } else {
      setExpenses(expensesData || []);
    }

    // Fetch assets
    const { data: assetsData, error: assetsError } = await supabase
      .from('assets')
      .select('*')
      .eq('household_id', householdId);

    if (assetsError) {
      console.error('Error fetching assets:', assetsError);
    } else {
      setAssets(assetsData || []);
    }

    // Fetch liabilities
    const { data: liabilitiesData, error: liabilitiesError } = await supabase
      .from('liabilities')
      .select('*')
      .eq('household_id', householdId);

    if (liabilitiesError) {
      console.error('Error fetching liabilities:', liabilitiesError);
    } else {
      setLiabilities(liabilitiesData || []);
    }
  };

  useEffect(() => {
    fetchData();
  }, [householdId]);

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader>
        <CardTitle>Income & Expenses</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <Income_ExpenseTable
          incomes={incomes}
          expenses={expenses}
          onDataChange={fetchData}
          householdId={householdId}
          onAddIncome={() => {
            setIncomeToEdit(undefined);
            setIsIncomeModalOpen(true);
          }}
          onAddExpense={() => {
            setExpenseToEdit(undefined);
            setIsExpenseModalOpen(true);
          }}
          onEditIncome={(income) => {
            setIncomeToEdit(income);
            setIsIncomeModalOpen(true);
          }}
          onEditExpense={(expense) => {
            setExpenseToEdit(expense);
            setIsExpenseModalOpen(true);
          }}
          assets={assets}
          liabilities={liabilities}
        />
        <IncomeModal
          isOpen={isIncomeModalOpen}
          onClose={() => {
            setIsIncomeModalOpen(false);
            setIncomeToEdit(undefined);
          }}
          onSuccess={fetchData}
          householdId={householdId}
          incomeToEdit={incomeToEdit}
        />
        <ExpenditureModal
          isOpen={isExpenseModalOpen}
          onClose={() => {
            setIsExpenseModalOpen(false);
            setExpenseToEdit(undefined);
          }}
          onSuccess={fetchData}
          householdId={householdId}
          expenseToEdit={expenseToEdit}
        />
      </CardContent>
    </Card>
  );
}
