'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { verifyHouseholdAccessClient } from '@/utils/client-authorization';
import { validateHouseholdId } from '@/utils/validation';
import AssetsModal from '@/components/modals/AssetsModal';
import LiabilitiesModal from '@/components/modals/LiabilitiesModal';
import Assets_LiabilitiesTable, { Asset, Liability } from '@/components/tables/Assets_LiabilitiesTable';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { createClient } from '@/utils/supabase/client';

export default function AssetsLiabilities() {
  const params = useParams();
  const router = useRouter();
  const [householdId, setHouseholdId] = useState<string>('');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [showAssetsModal, setShowAssetsModal] = useState(false);
  const [showLiabilitiesModal, setShowLiabilitiesModal] = useState(false);
  const [assetToEdit, setAssetToEdit] = useState<Asset | undefined>(undefined);
  const [liabilityToEdit, setLiabilityToEdit] = useState<Liability | undefined>(undefined);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [liabilities, setLiabilities] = useState<Liability[]>([]);

  const fetchData = async () => {
    if (!householdId || !isAuthorized) return;

    const supabase = createClient();

    // Fetch assets with specific column selection
    const { data: assetsData, error: assetsError } = await supabase
      .from('assets')
      .select(`
        id, name, value, type, household_id, created_at, updated_at,
        details, property_type, rental_income, provider, linked_income_id,
        fund_type, member_id
      `)
      .eq('household_id', householdId)
      .order('created_at', { ascending: false });

    if (assetsError) {
      setAssets([]);
    } else {
      setAssets(assetsData as Asset[] || []);
    }

    // Fetch liabilities with specific column selection
    const { data: liabilitiesData, error: liabilitiesError } = await supabase
      .from('liabilities')
      .select(`
        id, name, amount, type, household_id, created_at, updated_at,
        interest_rate, lender, payment_amount, payment_frequency,
        details, linked_asset_id, loan_type, linked_expense_id
      `)
      .eq('household_id', householdId)
      .order('created_at', { ascending: false });

    if (liabilitiesError) {
      setLiabilities([]);
    } else {
      setLiabilities(liabilitiesData as Liability[] || []);
    }
  };

  // Authorization check effect
  useEffect(() => {
    const checkAccess = async () => {
      if (params.id) {
        try {
          // Validate and sanitize the household ID
          const validatedId = validateHouseholdId(params.id as string);

          // Verify user has access to this household
          const authResult = await verifyHouseholdAccessClient(validatedId);

          if (!authResult || !authResult.hasAccess) {
            router.push('/protected/households');
            return;
          }

          setHouseholdId(validatedId);
          setIsAuthorized(true);
        } catch (error) {
          router.push('/protected/households');
        } finally {
          setIsLoading(false);
        }
      }
    };

    checkAccess();
  }, [params.id, router]);

  // Data fetching effect
  useEffect(() => {
    if (isAuthorized && householdId) {
      fetchData();
    }
  }, [householdId, isAuthorized]);

  const handleAssetSuccess = () => {
    fetchData();
    setShowAssetsModal(false);
    setAssetToEdit(undefined);
  };

  const handleLiabilitySuccess = () => {
    fetchData();
    setShowLiabilitiesModal(false);
    setLiabilityToEdit(undefined);
  };

  if (isLoading) {
    return (
      <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div>Loading...</div>
        </CardContent>
      </Card>
    );
  }

  if (!isAuthorized) {
    return (
      <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div>Access denied. Redirecting...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader>
        <CardTitle>Assets & Liabilities</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <Assets_LiabilitiesTable
          assets={assets}
          liabilities={liabilities}
          onDataChange={fetchData}
          householdId={householdId}
          onAddAsset={() => {
            setAssetToEdit(undefined);
            setShowAssetsModal(true);
          }}
          onAddLiability={() => {
            setLiabilityToEdit(undefined);
            setShowLiabilitiesModal(true);
          }}
          onEditAsset={(asset: Asset) => {
            setAssetToEdit(asset);
            setShowAssetsModal(true);
          }}
          onEditLiability={(liability: Liability) => {
            setLiabilityToEdit(liability);
            setShowLiabilitiesModal(true);
          }}
        />

        <AssetsModal
          isOpen={showAssetsModal}
          onClose={() => {
            setShowAssetsModal(false);
            setAssetToEdit(undefined);
          }}
          householdId={householdId}
          onSuccess={handleAssetSuccess}
          asset={assetToEdit}
        />

        <LiabilitiesModal
          isOpen={showLiabilitiesModal}
          onClose={() => {
            setShowLiabilitiesModal(false);
            setLiabilityToEdit(undefined);
          }}
          householdId={householdId}
          onSuccess={handleLiabilitySuccess}
          liability={liabilityToEdit}
        />
      </CardContent>
    </Card>
  );
}
