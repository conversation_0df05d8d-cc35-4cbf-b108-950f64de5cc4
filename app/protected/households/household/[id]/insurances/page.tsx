'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import InsuranceTable, { Insurance } from '@/components/tabs/InsuranceTable';
import { InsuranceModal } from '@/components/modals/InsuranceModal';

interface HouseholdMembers {
  name1: string;
  name2?: string;
}

export default function Insurances() {
  const params = useParams();
  const householdId = params.id as string;
  const [insurances, setInsurances] = useState<Insurance[]>([]);
  const [isInsuranceModalOpen, setIsInsuranceModalOpen] = useState(false);
  const [selectedInsurance, setSelectedInsurance] = useState<Insurance | undefined>(undefined);
  const [householdMembers, setHouseholdMembers] = useState<HouseholdMembers | undefined>();

  const fetchInsurances = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('insurances')
      .select('*')
      .eq('household_id', householdId);

    if (error) {
      console.error('Error fetching insurances:', error);
    } else {
      setInsurances(data || []);
    }
  };

  const fetchHouseholdMembers = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
    } else if (data && data.members) {
      setHouseholdMembers({
        name1: data.members.name1 || '',
        name2: data.members.name2 || ''
      });
    }
  };

  useEffect(() => {
    fetchInsurances();
    fetchHouseholdMembers();
  }, [householdId]);

  const handleEditInsurance = (insurance: Insurance) => {
    setSelectedInsurance(insurance);
    setIsInsuranceModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsInsuranceModalOpen(false);
    setSelectedInsurance(undefined);
  };

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader>
        <CardTitle>Insurance Policies</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <InsuranceTable
          data={insurances}
          onDataChange={fetchInsurances}
          householdId={householdId}
          onAddInsurance={() => {
            setSelectedInsurance(undefined);
            setIsInsuranceModalOpen(true);
          }}
          onEditInsurance={handleEditInsurance}
          householdMembers={householdMembers}
        />
        <InsuranceModal
          isOpen={isInsuranceModalOpen}
          onClose={handleCloseModal}
          onSuccess={fetchInsurances}
          householdId={householdId}
          insurance={selectedInsurance}
          householdMembers={householdMembers}
        />
      </CardContent>
    </Card>
  );
}
