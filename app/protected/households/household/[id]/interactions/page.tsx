'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import InteractionsTable from '@/components/tables/InteractionsTable';
import { useParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';

export default function Interactions() {
  const params = useParams();
  const householdId = parseInt(params.id as string);
  const [isLoading, setIsLoading] = useState(true);
  const [householdName, setHouseholdName] = useState('');

  useEffect(() => {
    const fetchHouseholdName = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('householdName')
        .eq('id', householdId)
        .single();

      if (error) {
        console.error('Error fetching household:', error);
      } else if (data) {
        setHouseholdName(data.householdName);
      }
      setIsLoading(false);
    };

    fetchHouseholdName();
  }, [householdId]);

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardContent className="flex-grow pt-4">
        <InteractionsTable
          householdId={householdId}
        />
      </CardContent>
    </Card>
  );
}
