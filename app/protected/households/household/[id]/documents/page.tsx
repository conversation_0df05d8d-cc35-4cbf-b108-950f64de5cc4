'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, MoreVertical, Trash2 } from 'lucide-react'; // Added Trash2 for bulk delete button
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import GenerateDocumentsModal from '@/components/modals/GenerateDocumentsModal';
import UploadDocumentsModal from '@/components/modals/UploadDocumentsModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";


// Interface definitions remain the same...

interface SOADocument {
  id: number;
  household_id: number;
  created_at: string;
  file_name: string;
  file_type: string;
  file_size: number;
  households: {
    householdName: string;
  };
}

interface TokenDocument {
  id: number;
  household_id: number;
  created_at: string;
  status: string;
  token: string;
  expires_at: string;
  households: {
    householdName: string;
  };
}

interface Document {
  created_at: string | number | Date;
  id: number;
  type: 'other' | 'toe' | 'discovery' | 'risk' | 'soa';
  name?: string;
  file_name?: string;
  status?: string;
  household_id: number;
  household_name: string;
  file_type?: string;
  file_size?: number;
  token?: string;
  expires_at?: string;
  file_url?: string;
  path?: string;
}

export default function Documents() {
  const params = useParams();
  const householdId = parseInt(params.id as string, 10);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocType, setSelectedDocType] = useState<string>('all');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());
  const [householdName, setHouseholdName] = useState<string>('');
  const { toast } = useToast(); // Ensure toast is destructured

  const documentTypes = [
    { value: 'all', label: 'All Documents' },
    { value: 'soa', label: 'SOA' },
    { value: 'discovery', label: 'Discovery Document' },
    { value: 'toe', label: 'TOE' },
    { value: 'risk', label: 'Risk Profiler' },
    { value: 'other', label: 'Other Documents' }
  ];

  const fetchHouseholdName = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('households')
      .select('householdName')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household name:', error);
    } else if (data) {
      setHouseholdName(data.householdName);
    }
  };

  const fetchDocuments = async () => {
    const supabase = createClient();
    setLoading(true);
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('No authenticated user found');
      setLoading(false);
      return;
    }

    try {
      // Fetch SOA documents
      const { data: soaData, error: soaError } = await supabase
        .from('soa_documents')
        .select('*, households(householdName)')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false }) as { data: SOADocument[] | null, error: any };

      // Fetch TOE documents
      const { data: toeData, error: toeError } = await supabase
        .from('toe_tokens')
        .select('*, households(householdName)')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch Discovery documents
      const { data: discoveryData, error: discoveryError } = await supabase
        .from('discovery_tokens')
        .select('*, households(householdName)')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch Risk Profiler documents
      const { data: riskData, error: riskError } = await supabase
        .from('risk_profiler_tokens')
        .select('*, households(householdName)')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false }) as { data: TokenDocument[] | null, error: any };

      // Fetch Other documents
      const { data: otherData, error: otherError } = await supabase
        .from('other_documents')
        .select('*, households(householdName)')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false });

      if (soaError) console.error('Error fetching SOA documents:', soaError);
      if (toeError) console.error('Error fetching TOE documents:', toeError);
      if (discoveryError) console.error('Error fetching Discovery documents:', discoveryError);
      if (riskError) console.error('Error fetching Risk Profiler documents:', riskError);
      if (otherError) console.error('Error fetching Other documents:', otherError);

      // Transform and combine all documents
      const allDocuments: Document[] = [
        ...(soaData?.map(doc => ({
          id: doc.id,
          type: 'soa' as const,
          name: doc.file_name,
          created_at: doc.created_at,
          status: 'completed',
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          file_type: doc.file_type,
          file_size: doc.file_size
        })) || []),
        ...(toeData?.map(doc => ({
          id: doc.id,
          type: 'toe' as const,
          name: 'Terms of Engagement',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          token: doc.token,
          expires_at: doc.expires_at
        })) || []),
        ...(discoveryData?.map(doc => ({
          id: doc.id,
          type: 'discovery' as const,
          name: 'Discovery Document',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          token: doc.token,
          expires_at: doc.expires_at
        })) || []),
        ...(riskData?.map(doc => ({
          id: doc.id,
          type: 'risk' as const,
          name: 'Risk Profiler',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          token: doc.token,
          expires_at: doc.expires_at
        })) || []),
        ...(otherData?.map(doc => ({
          id: doc.id,
          type: 'other' as const,
          name: doc.file_name,
          created_at: doc.created_at,
          status: 'completed',
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          file_type: doc.file_type,
          file_size: doc.file_size,
          path: doc.path
        })) || [])
      ];

      setDocuments(allDocuments);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "Error",
        description: "Failed to fetch documents. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // --- Start: Added Helper Functions ---

  const handleSelectAll = (checked: boolean | string) => {
    if (checked === true) {
      const newSelected = new Set<string>();
      filteredDocuments.forEach(doc => {
        // Only allow selecting documents that can be bulk deleted for now
        // Or adjust bulk actions based on selection
        newSelected.add(`${doc.type}-${doc.id}`);
      });
      setSelectedDocuments(newSelected);
    } else {
      setSelectedDocuments(new Set());
    }
  };

  const handleSelectRow = (docId: string, checked: boolean | string) => {
    const newSelected = new Set(selectedDocuments);
    if (checked === true) {
      newSelected.add(docId);
    } else {
      newSelected.delete(docId);
    }
    setSelectedDocuments(newSelected);
  };

  const handleViewDocument = async (doc: Document) => {
    const supabase = createClient(); 
    
    if (doc.type === 'toe' || doc.type === 'discovery' || doc.type === 'risk') { // Removed TPA as likely not relevant here
      const baseUrl = window.location.origin;
      let url = '';
      
      switch (doc.type) {
        case 'toe':
          url = `${baseUrl}/toe-form/${doc.token}`;
          break;
        case 'discovery':
          url = `${baseUrl}/discovery-form/${doc.token}`;
          break;
        case 'risk':
          url = `${baseUrl}/risk-profiler-form/${doc.token}`;
          break;
      }
      
      if (url && doc.token) { // Check token exists
        window.open(url, '_blank');
      } else {
         toast({
          title: "Info",
          description: "No viewable link for this document type or token missing.",
          variant: "destructive"
        });
      }
      return;
    }
    
    // Handle 'soa' and 'other' types (file-based)
    if (!doc.path) {
      toast({
        title: "Error",
        description: "Document path not found",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Ensure storage bucket is correct ('documents' assumed)
      const { data, error } = await supabase.storage
        .from('documents') 
        .createSignedUrl(doc.path, 60 * 60); // 1 hour expiry

      if (error) throw error;
      if (!data?.signedUrl) throw new Error("Failed to generate URL");

      window.open(data.signedUrl, '_blank');
    } catch (error) {
      console.error('Error creating signed URL:', error);
      toast({
        title: "Error",
        description: "Failed to view document. Check storage permissions and path.",
        variant: "destructive"
      });
    }
  };

  const handleDownload = async (doc: Document) => {
    const supabase = createClient();

    if (doc.type === 'toe' || doc.type === 'discovery' || doc.type === 'risk') {
       // If status is completed, maybe allow download (e.g., PDF generation if implemented)
       // For now, just open view link and instruct user
       if (doc.status === 'completed') {
         // Potentially add PDF generation here if needed, like in DiscoveryDocumentsList
         handleViewDocument(doc); // Open the form URL
         toast({
            title: "Info",
            description: "Document opened. Use browser's print/save function."
          });
       } else {
          toast({
            title: "Info",
            description: "Cannot download incomplete form. View or copy link instead.",
          });
       }
      return;
    }

    // Handle 'soa' and 'other'
    if (!doc.path) {
      toast({
        title: "Error",
        description: "Document path not found",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Ensure storage bucket is correct ('documents' assumed)
      const { data, error } = await supabase.storage
        .from('documents') 
        .download(doc.path);

      if (error) throw error;
      if (!data) throw new Error("No data received for download");

      const url = URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.file_name || doc.name || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast({
        title: "Error",
        description: "Failed to download document. Check storage permissions and path.",
        variant: "destructive"
      });
    }
  };

  const copyLink = (doc: Document) => {
    if (!doc.token || (doc.type !== 'toe' && doc.type !== 'discovery' && doc.type !== 'risk')) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No link available for this document type or status"
      });
      return;
    }

    const baseUrl = window.location.origin;
    let url = '';

    switch (doc.type) {
      case 'toe':
        url = `${baseUrl}/toe-form/${doc.token}`;
        break;
      case 'discovery':
        url = `${baseUrl}/discovery-form/${doc.token}`;
        break;
      case 'risk':
        url = `${baseUrl}/risk-profiler-form/${doc.token}`;
        break;
    }

    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: "Success",
        description: "Link copied to clipboard"
      });
    }).catch(() => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to copy link to clipboard"
      });
    });
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0) return;

    const supabase = createClient();
    const selectedDocs = filteredDocuments.filter(doc => 
      selectedDocuments.has(`${doc.type}-${doc.id}`)
    );
    
    let successCount = 0;
    let errorCount = 0;

    setLoading(true); 

    for (const doc of selectedDocs) {
      let table = '';
      switch (doc.type) {
        case 'soa': table = 'soa_documents'; break;
        case 'discovery': table = 'discovery_tokens'; break;
        case 'toe': table = 'toe_tokens'; break;
        case 'risk': table = 'risk_profiler_tokens'; break;
        case 'other': table = 'other_documents'; break;
      }

      if (table) {
        const { error } = await supabase.from(table).delete().eq('id', doc.id);
        if (error) {
          console.error(`Error deleting ${doc.type} document ${doc.id}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      } else {
        errorCount++; 
      }
    }

    setLoading(false);
    setSelectedDocuments(new Set()); 
    setShowDeleteDialog(false); // Close dialog

    if (successCount > 0) {
       toast({
          title: "Success",
          description: `Successfully deleted ${successCount} documents.`
        });
    }
    if (errorCount > 0) {
       toast({
          title: "Error",
          description: `Failed to delete ${errorCount} documents.`,
          variant: "destructive"
        });
    }

    fetchDocuments(); // Refresh list
  };

  // --- End: Added Helper Functions ---


  useEffect(() => {
    fetchDocuments();
    fetchHouseholdName();
  }, [householdId]);

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedDocType === 'all' || doc.type === selectedDocType;
    return matchesSearch && matchesType;
  });

  // Single document delete (triggered from row action or potentially bulk if dialog isn't updated)
  const handleDeleteDocument = async () => {
    if (!documentToDelete && selectedDocuments.size === 0) return; // Check both single and bulk selection state

    // If bulk delete is triggered via this dialog, call handleBulkDelete
    if (selectedDocuments.size > 0 && !documentToDelete) {
       await handleBulkDelete();
       return;
    }
    
    // Handle single delete
    if (!documentToDelete) return; 

    const supabase = createClient();
    let error = null;

    try {
      switch (documentToDelete.type) {
        case 'soa':
          const { error: soaError } = await supabase
            .from('soa_documents')
            .delete()
            .eq('id', documentToDelete.id);
          error = soaError;
          break;
        case 'toe':
          const { error: toeError } = await supabase
            .from('toe_tokens')
            .delete()
            .eq('id', documentToDelete.id);
          error = toeError;
          break;
        case 'discovery':
          const { error: discoveryError } = await supabase
            .from('discovery_tokens')
            .delete()
            .eq('id', documentToDelete.id);
          error = discoveryError;
          break;
        case 'risk':
          const { error: riskError } = await supabase
            .from('risk_profiler_tokens')
            .delete()
            .eq('id', documentToDelete.id);
          error = riskError;
          break;
        case 'other':
          const { error: otherError } = await supabase
            .from('other_documents')
            .delete()
            .eq('id', documentToDelete.id);
          error = otherError;
          break;
      }

      if (error) {
        throw error;
      }

      toast({
        title: "Success",
        description: "Document deleted successfully",
      });

      fetchDocuments();
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setShowDeleteDialog(false);
      setDocumentToDelete(null); // Reset single delete target
      // Bulk delete handles its own state reset
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader>
        <CardTitle>Documents for {householdName}</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow flex flex-col"> {/* Added flex flex-col */}
        <div className="mb-4 space-y-4">
          <div className="flex justify-between items-center space-x-2"> {/* Added space-x-2 */}
            {/* Search and Filter */}
            <div className="flex items-center space-x-2 flex-grow"> {/* Added flex-grow */}
              {/* Bulk Actions Button */}
              {selectedDocuments.size > 0 && (
                 <Button 
                    variant="outline" 
                    size="sm" 
                    className="ml-2"
                    onClick={() => {
                      // Set state to indicate bulk delete for the dialog
                      setDocumentToDelete(null); // Ensure single delete isn't targeted
                      setShowDeleteDialog(true); 
                    }}
                  >
                   <Trash2 className="h-4 w-4 mr-2" />
                   Delete ({selectedDocuments.size})
                 </Button>
              )}
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-full max-w-xs" // Adjusted width
                />
              </div>
              <Select value={selectedDocType} onValueChange={setSelectedDocType}>
                <SelectTrigger className="w-[200px]"> {/* Adjusted width */}
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {/* Action Buttons */}
            <div className="flex items-center space-x-2 flex-shrink-0"> {/* Added flex-shrink-0 */}
              <Button onClick={() => setShowGenerateModal(true)}>
                Generate Document
              </Button>
              <Button variant="outline" onClick={() => setShowUploadModal(true)}> {/* Changed variant */}
                Upload Document
              </Button>
            </div>
          </div>
        </div>

        {/* Table Container */}
        <div className="rounded-md border flex-grow overflow-auto"> {/* Added flex-grow and overflow-auto */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12"> {/* Adjusted width */}
                  <Checkbox
                    checked={
                      filteredDocuments.length > 0 &&
                      selectedDocuments.size === filteredDocuments.length
                    }
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all rows"
                  />
                </TableHead>
                {/* Removed extra </TableHead> here */}
                <TableHead>Document Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right w-[50px]">Actions</TableHead> {/* Added text-right */}
              </TableRow>
            </TableHeader>
            <TableBody>
             {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    Loading documents...
                  </TableCell>
                </TableRow>
              ) : filteredDocuments.length === 0 ? (
                 <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No documents found for this household.
                  </TableCell>
                </TableRow>
              ) : (
                filteredDocuments.map((document) => (
                  <TableRow 
                    key={`${document.type}-${document.id}`}
                    data-state={selectedDocuments.has(`${document.type}-${document.id}`) && "selected"}
                  >
                    <TableCell>
                      <Checkbox
                        checked={selectedDocuments.has(`${document.type}-${document.id}`)}
                        onCheckedChange={(checked) => 
                          handleSelectRow(`${document.type}-${document.id}`, checked)
                        }
                        aria-label={`Select document ${document.name}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{document.name || document.file_name}</TableCell>
                    <TableCell>{documentTypes.find(t => t.value === document.type)?.label}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(document.status || 'unknown')}`}>
                        {document.status || 'N/A'}
                      </span>
                    </TableCell>
                    <TableCell>{new Date(document.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right"> {/* Added text-right */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {/* Conditional Actions based on type/status */}
                          {(document.type === 'soa' || document.type === 'other' || (document.status === 'completed' && (document.type === 'toe' || document.type === 'discovery' || document.type === 'risk'))) && (
                            <DropdownMenuItem onClick={() => handleViewDocument(document)}>
                              View Document
                            </DropdownMenuItem>
                          )}
                           {(document.type === 'soa' || document.type === 'other' || (document.status === 'completed' && (document.type === 'toe' || document.type === 'discovery' || document.type === 'risk'))) && (
                            <DropdownMenuItem onClick={() => handleDownload(document)}>
                              Download
                            </DropdownMenuItem>
                          )}
                          {(document.type === 'toe' || document.type === 'discovery' || document.type === 'risk') && document.token && document.status !== 'completed' && (
                             <DropdownMenuItem onClick={() => copyLink(document)}>
                              Copy Link
                            </DropdownMenuItem>
                          )}
                           {(document.type === 'toe' || document.type === 'discovery' || document.type === 'risk') && document.token && document.status === 'completed' && (
                             <DropdownMenuItem onClick={() => handleViewDocument(document)}> 
                              View Completed Form {/* Explicit label */}
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => {
                              setSelectedDocuments(new Set()); // Clear bulk selection when acting on single item
                              setDocumentToDelete(document);
                              setShowDeleteDialog(true);
                            }}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Modals and Dialogs */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                {selectedDocuments.size > 0 && !documentToDelete 
                  ? `This action cannot be undone. This will permanently delete the ${selectedDocuments.size} selected document(s).`
                  : `This action cannot be undone. This will permanently delete the document "${documentToDelete?.name || documentToDelete?.file_name}".`
                }
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => {setSelectedDocuments(new Set()); setDocumentToDelete(null);}}>Cancel</AlertDialogCancel> 
              {/* Clear selection on cancel */}
              <AlertDialogAction 
                onClick={handleDeleteDocument} // This now handles both single and bulk via logic inside
                className="bg-red-600 hover:bg-red-700" // Destructive style
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {showGenerateModal && (
          <GenerateDocumentsModal
            isOpen={showGenerateModal}
            onClose={() => setShowGenerateModal(false)}
            onSuccess={fetchDocuments}
            preselectedHousehold={{ id: householdId, householdName }}
          />
        )}

        {showUploadModal && (
          <UploadDocumentsModal
            isOpen={showUploadModal}
            onClose={() => setShowUploadModal(false)}
            onSuccess={fetchDocuments}
            preselectedHousehold={{ id: householdId, householdName }}
          />
        )}
      </CardContent>
    </Card>
  );
}
