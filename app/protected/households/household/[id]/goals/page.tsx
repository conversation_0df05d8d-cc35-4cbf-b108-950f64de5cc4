'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import GoalsTable from '@/components/tables/GoalsTable';

interface HouseholdMembers {
  name1: string;
  name2?: string;
}

export default function Goals() {
  const params = useParams();
  const householdId = params.id as string;
  const [householdMembers, setHouseholdMembers] = useState<HouseholdMembers | undefined>();


  const fetchHouseholdMembers = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
    } else if (data && data.members) {
      setHouseholdMembers({
        name1: data.members.name1 || '',
        name2: data.members.name2 || ''
      });
    }
  };

  useEffect(() => {
    fetchHouseholdMembers();
  }, [householdId]);

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardContent className="flex-grow mt-4 overflow-y-auto">
        <GoalsTable householdId={householdId} householdMembers={householdMembers} />
      </CardContent>
    </Card>
  );
}
