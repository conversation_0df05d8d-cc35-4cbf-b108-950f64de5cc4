'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import CreateScenarioModal from '@/components/modals/CreateScenarioModal';
import PresentationModal from '@/components/modals/PresentationModal';
import { createClient } from '@/utils/supabase/client';
import { verifyHouseholdAccessClient } from '@/utils/client-authorization';
import { validateHouseholdId } from '@/utils/validation';
import AllScenariosTable from '@/components/tables/AllScenariosTable';
import { Loader2 } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

interface AllScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  household_id: number;
}

export default function Scenarios() {
  const params = useParams();
  const router = useRouter();
  const [householdId, setHouseholdId] = useState<number>(0);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [allScenarios, setAllScenarios] = useState<AllScenario[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [householdName, setHouseholdName] = useState('');

  const fetchScenarios = async () => {
    if (!householdId || !isAuthorized) return;

    setIsLoading(true);
    const supabase = createClient();

    try {
      // Fetch household name with specific column selection
      const { data: householdData, error: householdError } = await supabase
        .from('households')
        .select('householdName')
        .eq('id', householdId)
        .single();

      if (householdError) throw householdError;
      if (householdData) {
        setHouseholdName(householdData.householdName);
      }

      // Fetch scenarios for this household with specific column selection
      const { data: scenariosData, error: scenariosError } = await supabase
        .from('scenarios_data1')
        .select('id, scenario_name, household_id, created_at, last_edited_at')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false });

      if (scenariosError) throw scenariosError;

      // Map the data to include household_name
      const scenariosWithHouseholdName = (scenariosData || []).map(scenario => ({
        ...scenario,
        household_name: householdName
      }));

      setAllScenarios(scenariosWithHouseholdName);
    } catch (error) {
      setAllScenarios([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Authorization check effect
  useEffect(() => {
    const checkAccess = async () => {
      if (params.id) {
        try {
          // Validate and sanitize the household ID
          const validatedId = validateHouseholdId(params.id as string);
          const householdIdNum = parseInt(validatedId, 10);

          // Verify user has access to this household
          const authResult = await verifyHouseholdAccessClient(validatedId);

          if (!authResult || !authResult.hasAccess) {
            router.push('/protected/households');
            return;
          }

          setHouseholdId(householdIdNum);
          setIsAuthorized(true);
        } catch (error) {
          router.push('/protected/households');
        }
      }
    };

    checkAccess();
  }, [params.id, router]);

  // Data fetching effect
  useEffect(() => {
    if (isAuthorized && householdId) {
      fetchScenarios();
    }
  }, [householdId, isAuthorized]);

  if (!isAuthorized) {
    return (
      <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
        <CardContent className="flex items-center justify-center h-full">
          <div>Access denied. Redirecting...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-2xl font-bold">Scenarios</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <AllScenariosTable
              data={allScenarios}
              onDataChange={fetchScenarios}
              onCreateScenario={() => setIsModalOpen(true)}
              onPresentScenarios={() => setIsPresentationModalOpen(true)}
              preselectedHousehold={{ id: householdId, householdName }}
              viewMode={'user'}
              onViewModeChange={(value: 'user' | 'organization') => {
                // Not implemented for household scenarios
              }}
          />
        )}
      </CardContent>

      <CreateScenarioModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={fetchScenarios}
        preselectedHousehold={{ id: householdId, householdName }}
      />

      <PresentationModal
        isOpen={isPresentationModalOpen}
        onClose={() => setIsPresentationModalOpen(false)}
        preselectedHousehold={{ id: householdId, householdName }}
      />
    </Card>
  );
}
