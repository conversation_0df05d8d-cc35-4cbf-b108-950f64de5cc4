'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { verifyHouseholdAccessClient } from '@/utils/client-authorization';
import { validateHouseholdId } from '@/utils/validation';
import HouseholdData from '@/components/HouseholdData';


interface HouseholdData {
  id: string;
  householdData: {
    householdName: string;
    name1: string;
    age1: string;
    income1: string;
    kiwisaverValue1: string;
    kiwisaverContribution1: string;
    employerContribution1: string;
    kiwisaverProfile1: string;
    name2: string;
    age2: string;
    income2: string;
    kiwisaverValue2: string;
    kiwisaverContribution2: string;
    employerContribution2: string;
    kiwisaverProfile2: string;
    annualExpenses: number;
    propertyValue: number;
    debtValue: number;
    investmentValue: number;
    riskProfile: string;
    savingsValue: number;
  };
}

export default function HouseholdPage() {
  const params = useParams();
  const router = useRouter();
  const [householdData, setHouseholdData] = useState<HouseholdData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    const fetchHouseholdData = async () => {
      if (params.id) {
        try {
          // Validate and sanitize the household ID
          const validatedId = validateHouseholdId(params.id as string);

          // Verify user has access to this household
          const authResult = await verifyHouseholdAccessClient(validatedId);

          if (!authResult || !authResult.hasAccess) {
            router.push('/protected/households');
            return;
          }

          setIsAuthorized(true);

          const supabase = createClient();
          // Use specific column selection instead of select('*')
          const { data, error } = await supabase
            .from('households')
            .select('id, householdName, members, created_at, updated_at')
            .eq('id', validatedId)
            .single();

          if (error) {
            router.push('/protected/households');
          } else if (data) {
            // Use the members object directly, handle null case
            const members = data.members || {};

            // Ensure all fields are present in the data
            const fullData: HouseholdData = {
              id: data.id,
              householdData: {
                householdName: data.householdName || '',
                name1: members.name1 || '',
                name2: members.name2 || '',
                age1: members.age1 || '',
                age2: members.age2 || '',
                income1: members.income1 || '',
                income2: members.income2 || '',
                kiwisaverValue1: members.kiwisaverValue1 || '',
                kiwisaverValue2: members.kiwisaverValue2 || '',
                kiwisaverContribution1: members.kiwisaverContribution1 || '',
                kiwisaverContribution2: members.kiwisaverContribution2 || '',
                employerContribution1: members.employerContribution1 || '',
                employerContribution2: members.employerContribution2 || '',
                kiwisaverProfile1: members.kiwisaverProfile1 || '',
                kiwisaverProfile2: members.kiwisaverProfile2 || '',
                annualExpenses: members.annualExpenses || 0,
                propertyValue: members.propertyValue || 0,
                debtValue: members.debtValue || 0,
                investmentValue: members.investmentValue || 0,
                riskProfile: members.riskProfile || '',
                savingsValue: members.savingsValue || 0,
              }
            };
            setHouseholdData(fullData);
          }
        } catch (error) {
          router.push('/protected/households');
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchHouseholdData();
  }, [params.id, router]);

  return (
    <div className="flex h-screen">
      <div className="flex-grow p-1.5 overflow-hidden flex flex-col">
        {isLoading ? (
          <div>Loading...</div>
        ) : !isAuthorized ? (
          <div>Access denied. Redirecting...</div>
        ) : householdData ? (
          <HouseholdData householdData={householdData} />
        ) : (
          <div>No household data found.</div>
        )}
      </div>
    </div>
  );
}
