'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import GenerateDocumentsModal from '@/components/modals/GenerateDocumentsModal';
import { riskQuestions10Q, riskQuestions25Q, calculateNormalizedRiskScore } from '@/app/risk-profiler-form/questions';
import { CurrentRiskProfile } from '@/components/CurrentRiskProfile';
import { InvestmentProfileRecommendations } from '@/components/InvestmentProfileRecommendations';
import { RiskProfilerHistory } from '@/components/RiskProfileHistory';

interface RiskProfilePageProps {
  params: {
    id: string;
  };
}

export default function RiskProfilePage({ params }: RiskProfilePageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [riskProfiles, setRiskProfiles] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [householdName, setHouseholdName] = useState('');
  const { toast } = useToast();

  const [members, setMembers] = useState<{ id: number; firstName: string; lastName: string }[]>([]);
  const [memberRiskProfiles, setMemberRiskProfiles] = useState<Record<number, any[]>>({});
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);

  const getQuestionById = (questionId: string, profilerType: string, profile: any) => {
    // First check if this is a custom template profile
    if (profile.template_id && profile.responses?.customQuestions) {
      console.log('Looking for question in custom questions:', questionId, profile.responses.customQuestions);

      // Check if customQuestions is an array
      if (Array.isArray(profile.responses.customQuestions)) {
        // Look for the question in the custom questions
        const customQuestion = profile.responses.customQuestions.find((q: any) => q.id === questionId);
        if (customQuestion) {
          console.log('Found custom question:', customQuestion);
          // Ensure the question has the necessary properties
          return {
            ...customQuestion,
            // Ensure type is set
            type: customQuestion.type || 'radio',
            // Ensure includeInScore is set correctly
            includeInScore: customQuestion.type === 'textarea' ? false : (customQuestion.includeInScore !== false)
          };
        }
      } else {
        console.error('customQuestions is not an array:', profile.responses.customQuestions);
      }
    }

    // Fall back to default questions
    const questions = profilerType === '10q' ? riskQuestions10Q : riskQuestions25Q;
    return questions.find(q => q.id === questionId);
  };

  const getScoreForAnswer = (questionId: string, answerText: string | string[], profilerType: string, profile: any) => {
    const question = getQuestionById(questionId, profilerType, profile);
    if (!question) return null;

    // If it's a textarea question or explicitly excluded from score, return 'N/A'
    if (question.type === 'textarea' || question.includeInScore === false) {
      return 'N/A';
    }

    // For checkbox questions (multiple selections)
    if (question.type === 'checkbox' && Array.isArray(answerText)) {
      if (answerText.length === 0) return 'N/A';

      // Calculate the average score of selected options
      let optionSum = 0;
      let validOptionsCount = 0;

      answerText.forEach(selectedText => {
        const option = question.options.find((opt: { text: string; }) => opt.text === selectedText);
        if (option) {
          // Ensure score is a number
          const score = typeof option.score === 'number' ? option.score : parseInt(option.score);
          if (!isNaN(score)) {
            optionSum += score;
            validOptionsCount++;
          }
        }
      });

      // Return the average score
      if (validOptionsCount > 0) {
        const averageScore = optionSum / validOptionsCount;
        return Math.round(averageScore * 10) / 10; // Round to 1 decimal place
      }

      return 'N/A';
    }

    // For radio questions, find the option and return its score
    const option = question.options.find((opt: { text: string | string[]; }) => opt.text === answerText);
    return option ? option.score : null;
  };

  const recalculateRiskScore = (profile: any) => {
    if (!profile || !profile.responses || !profile.responses.riskResponses || !profile.profiler_type) {
      return profile.risk_score || 0;
    }

    // If this is a custom template profile
    if (profile.template_id && profile.responses.customQuestions) {
      // Calculate score using custom questions
      let totalScore = 0;
      let maxPossibleScore = 0;

      // Get all questions that should be included in the score
      const scoringQuestions = profile.responses.customQuestions.filter(
        (q: any) => q.type !== 'textarea' && q.includeInScore !== false
      );

      scoringQuestions.forEach((question: any) => {
        const answerText = profile.responses.riskResponses[question.id];
        if (!answerText) return;

        const selectedOption = question.options.find(
          (opt: any) => opt.text === answerText
        );

        if (selectedOption) {
          totalScore += selectedOption.score;
        }

        // Calculate the maximum possible score for this question
        const maxQuestionScore = Math.max(...question.options.map((opt: any) => opt.score));
        maxPossibleScore += maxQuestionScore;
      });

      // Normalize the score to a 0-100 scale and round to an integer
      if (maxPossibleScore > 0) {
        return Math.round((totalScore / maxPossibleScore) * 100);
      }

      return profile.risk_score || 0;
    }

    // For standard profiles, use the existing calculation function
    return calculateNormalizedRiskScore(
      profile.responses.riskResponses,
      profile.profiler_type as '10q' | '25q'
    );
  };

  useEffect(() => {
    const fetchHouseholdData = async () => {
      setIsLoading(true);
      const supabase = createClient();

      try {
        const { data: household, error: householdError } = await supabase
          .from('households')
          .select('householdName, members')
          .eq('id', params.id)
          .single();

        if (householdError) throw householdError;

        if (household) {
          setHouseholdName(household.householdName);

          const membersData = [];
          if (household.members) {
            if (household.members.name1) {
              membersData.push({
                id: 1,
                firstName: household.members.name1,
                lastName: '',
                age: household.members.age1
              });
            }
            if (household.members.name2) {
              membersData.push({
                id: 2,
                firstName: household.members.name2,
                lastName: '',
                age: household.members.age2
              });
            }
          }

          setMembers(membersData);

          if (membersData.length > 0) {
            setSelectedMemberId(membersData[0].id);

            const memberProfiles: Record<number, any[]> = {};

            for (const member of membersData) {
              const { data: profileData, error: profileError } = await supabase
                .from('risk_profiler_tokens')
                .select('*')
                .eq('household_id', params.id)
                .eq('member_id', member.id)
                .eq('status', 'completed')
                .order('completed_at', { ascending: false });

              if (profileError) throw profileError;
              memberProfiles[member.id] = profileData || [];
            }

            setMemberRiskProfiles(memberProfiles);
          }
        }

        const { data: generalProfiles, error: profilesError } = await supabase
          .from('risk_profiler_tokens')
          .select('*')
          .eq('household_id', params.id)
          .is('member_id', null)
          .eq('status', 'completed')
          .order('completed_at', { ascending: false });

        if (profilesError) throw profilesError;
        setRiskProfiles(generalProfiles || []);

      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load data"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchHouseholdData();
  }, [params.id, toast]);

  const getRiskCategory = (score: number) => {
    if (score < 20) return { category: 'Very Conservative', color: 'bg-blue-500' };
    if (score < 40) return { category: 'Conservative', color: 'bg-cyan-500' };
    if (score < 60) return { category: 'Moderate', color: 'bg-green-500' };
    if (score < 80) return { category: 'Growth', color: 'bg-amber-500' };
    return { category: 'Aggressive', color: 'bg-red-500' };
  };

  const getSuggestedAllocation = (category: string) => {
    switch (category) {
      case 'Very Conservative':
        return [
          { name: 'Cash', value: 30, color: '#94a3b8' },
          { name: 'Fixed Interest', value: 55, color: '#64748b' },
          { name: 'Australasian Equities', value: 5, color: '#0ea5e9' },
          { name: 'International Equities', value: 10, color: '#3b82f6' },
          { name: 'Property', value: 0, color: '#10b981' },
        ];
      case 'Conservative':
        return [
          { name: 'Cash', value: 20, color: '#94a3b8' },
          { name: 'Fixed Interest', value: 50, color: '#64748b' },
          { name: 'Australasian Equities', value: 10, color: '#0ea5e9' },
          { name: 'International Equities', value: 15, color: '#3b82f6' },
          { name: 'Property', value: 5, color: '#10b981' },
        ];
      case 'Moderate':
        return [
          { name: 'Cash', value: 10, color: '#94a3b8' },
          { name: 'Fixed Interest', value: 40, color: '#64748b' },
          { name: 'Australasian Equities', value: 15, color: '#0ea5e9' },
          { name: 'International Equities', value: 25, color: '#3b82f6' },
          { name: 'Property', value: 10, color: '#10b981' },
        ];
      case 'Growth':
        return [
          { name: 'Cash', value: 5, color: '#94a3b8' },
          { name: 'Fixed Interest', value: 25, color: '#64748b' },
          { name: 'Australasian Equities', value: 20, color: '#0ea5e9' },
          { name: 'International Equities', value: 40, color: '#3b82f6' },
          { name: 'Property', value: 10, color: '#10b981' },
        ];
      case 'Aggressive':
        return [
          { name: 'Cash', value: 2, color: '#94a3b8' },
          { name: 'Fixed Interest', value: 8, color: '#64748b' },
          { name: 'Australasian Equities', value: 25, color: '#0ea5e9' },
          { name: 'International Equities', value: 55, color: '#3b82f6' },
          { name: 'Property', value: 10, color: '#10b981' },
        ];
      default:
        return [];
    }
  };

  const getAllocationBands = (category: string) => {
    switch (category) {
      case 'Very Conservative':
        return {
          optimal: { min: 0, max: 20 },
          acceptable: { min: 0, max: 30 },
          color: 'bg-blue-500'
        };
      case 'Conservative':
        return {
          optimal: { min: 20, max: 40 },
          acceptable: { min: 10, max: 50 },
          color: 'bg-cyan-500'
        };
      case 'Moderate':
        return {
          optimal: { min: 40, max: 60 },
          acceptable: { min: 30, max: 70 },
          color: 'bg-green-500'
        };
      case 'Growth':
        return {
          optimal: { min: 60, max: 80 },
          acceptable: { min: 50, max: 90 },
          color: 'bg-amber-500'
        };
      case 'Aggressive':
        return {
          optimal: { min: 80, max: 100 },
          acceptable: { min: 70, max: 100 },
          color: 'bg-red-500'
        };
      default:
        return {
          optimal: { min: 0, max: 0 },
          acceptable: { min: 0, max: 0 },
          color: 'bg-gray-500'
        };
    }
  };

  const getOptimalGradient = (category: string) => {
    switch (category) {
      case 'Very Conservative': return 'bg-gradient-to-r from-blue-600 to-blue-400';
      case 'Conservative': return 'bg-gradient-to-r from-cyan-600 to-cyan-400';
      case 'Moderate': return 'bg-gradient-to-r from-green-600 to-green-400';
      case 'Growth': return 'bg-gradient-to-r from-amber-600 to-amber-400';
      case 'Aggressive': return 'bg-gradient-to-r from-red-600 to-red-400';
      default: return 'bg-gradient-to-r from-gray-600 to-gray-400';
    }
  };

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex flex-col space-y-1.5">
          <CardTitle>Risk Profile</CardTitle>
          <CardDescription>
            View and manage risk profiles for {householdName}
          </CardDescription>
        </div>

        <div className="flex items-center gap-4">
          {members.length > 0 && (
            <Tabs
              value={selectedMemberId?.toString() || "household"}
              onValueChange={(value) => setSelectedMemberId(value === "household" ? null : parseInt(value))}
            >
              <TabsList>
                <TabsTrigger value="household">Household</TabsTrigger>
                {members.map(member => (
                  <TabsTrigger key={member.id} value={member.id.toString()}>
                    {member.firstName} {member.lastName}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}
          <Button onClick={() => setIsModalOpen(true)}>Generate New Risk Profile</Button>
        </div>
      </CardHeader>

      <CardContent className="flex-grow overflow-y-auto">
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        ) : (
          <>
            {selectedMemberId === null ? (
              riskProfiles.length === 0 ? (
                <Card>
                  <CardHeader>
                    <CardTitle>No Risk Profiles</CardTitle>
                    <CardDescription>
                      No completed risk profiles found for this household.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Generate a new risk profile to assess the household's risk tolerance.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <Tabs defaultValue="latest" className="w-full">
                  <TabsList>
                    <TabsTrigger value="latest">Latest Profile</TabsTrigger>
                    <TabsTrigger value="history">History</TabsTrigger>
                  </TabsList>

                  <TabsContent value="latest" className="space-y-4">
                    <CurrentRiskProfile
                      profile={riskProfiles[0]}
                      getRiskCategory={getRiskCategory}
                      getQuestionById={(questionId, profilerType) => getQuestionById(questionId, profilerType, riskProfiles[0])}
                      getScoreForAnswer={(questionId, answerText, profilerType) => getScoreForAnswer(questionId, answerText, profilerType, riskProfiles[0])}
                      riskScore={recalculateRiskScore(riskProfiles[0])}
                    />

                    <InvestmentProfileRecommendations
                      score={recalculateRiskScore(riskProfiles[0])}
                      getRiskCategory={getRiskCategory}
                      getSuggestedAllocation={getSuggestedAllocation}
                      getAllocationBands={getAllocationBands}
                      getOptimalGradient={getOptimalGradient}
                    />
                  </TabsContent>

                  <TabsContent value="history">
                    <RiskProfilerHistory
                      profiles={riskProfiles}
                      getRiskCategory={getRiskCategory}
                    />
                  </TabsContent>
                </Tabs>
              )
            ) : (
              !memberRiskProfiles[selectedMemberId] || memberRiskProfiles[selectedMemberId].length === 0 ? (
                <Card>
                  <CardHeader>
                    <CardTitle>No Risk Profiles</CardTitle>
                    <CardDescription>
                      No completed risk profiles found for this member.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Generate a new risk profile to assess this member's risk tolerance.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <Tabs defaultValue="latest" className="w-full">
                  <TabsList>
                    <TabsTrigger value="latest">Latest Profile</TabsTrigger>
                    <TabsTrigger value="history">History</TabsTrigger>
                  </TabsList>

                  <TabsContent value="latest" className="space-y-4">
                    <CurrentRiskProfile
                      profile={memberRiskProfiles[selectedMemberId][0]}
                      getRiskCategory={getRiskCategory}
                      getQuestionById={(questionId, profilerType) => getQuestionById(questionId, profilerType, memberRiskProfiles[selectedMemberId][0])}
                      getScoreForAnswer={(questionId, answerText, profilerType) => getScoreForAnswer(questionId, answerText, profilerType, memberRiskProfiles[selectedMemberId][0])}
                      riskScore={recalculateRiskScore(memberRiskProfiles[selectedMemberId][0])}
                    />

                    <InvestmentProfileRecommendations
                      score={recalculateRiskScore(memberRiskProfiles[selectedMemberId][0])}
                      getRiskCategory={getRiskCategory}
                      getSuggestedAllocation={getSuggestedAllocation}
                      getAllocationBands={getAllocationBands}
                      getOptimalGradient={getOptimalGradient}
                    />
                  </TabsContent>

                  <TabsContent value="history">
                    <RiskProfilerHistory
                      profiles={memberRiskProfiles[selectedMemberId]}
                      getRiskCategory={getRiskCategory}
                    />
                  </TabsContent>
                </Tabs>
              )
            )}
          </>
        )}
      </CardContent>

      <GenerateDocumentsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        preselectedHousehold={{ id: parseInt(params.id), householdName }}
      />
    </Card>
  );
}
