'use client';

import { useParams } from 'next/navigation';
import HouseholdsSidebar from '@/components/sidebars/HouseholdsSidebar';

export default function HouseholdLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const householdId = params.id as string;

  return (
    <div className="flex h-screen">
      <div className="flex-grow p-1.5 overflow-hidden">
        {children}
      </div>
    </div>
  );
}
