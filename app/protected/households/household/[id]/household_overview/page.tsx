'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { useParams, useRouter } from 'next/navigation';
import { validateHouseholdId } from '@/utils/validation';
import { Phone, Mail, Calendar, MapPin, Building, Hash, Briefcase, Globe, Users, User, Clock, FileText, Heart, Users2, UserCheck, UserPlus, Pencil, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AddFamilyModal from '@/components/modals/AddFamilyModal';
import AddProfessionalModal from '@/components/modals/AddProfessionalModal';
import HouseholdInfoModal from '@/components/modals/HouseholdInfoModal';
import ConfirmationDialog from '@/components/modals/ConfirmationDialog';
import { HouseholdData } from '@/types/household';

interface FamilyMember {
  id: string;
  name: string;
  family_relationship: string;
  phone: string;
  email: string;
  notes?: string;
}

interface ProfessionalContact {
  id: string;
  name: string;
  profession: string;
  phone: string;
  email: string;
  notes?: string;
}

interface RelationshipsState {
  family: FamilyMember[];
  professional: ProfessionalContact[];
}

export default function HouseholdOverviewPage() {
  const params = useParams();
  const router = useRouter();
  const [household, setHousehold] = useState<HouseholdData | null>(null);
  const [showHouseholdInfoModal, setShowHouseholdInfoModal] = useState(false);
  const [activeSectionToEdit, setActiveSectionToEdit] = useState<'household' | 'individual' | 'additional' | null>(null);

  const [loading, setLoading] = useState(true);

  const [relationships, setRelationships] = useState<RelationshipsState>({
    family: [],
    professional: []
  });
  const [showAddFamilyModal, setShowAddFamilyModal] = useState(false);
  const [showAddProfessionalModal, setShowAddProfessionalModal] = useState(false);
  const [selectedFamilyMember, setSelectedFamilyMember] = useState<FamilyMember | undefined>(undefined);
  const [selectedProfessional, setSelectedProfessional] = useState<ProfessionalContact | undefined>(undefined);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [relationshipToDelete, setRelationshipToDelete] = useState<{id: string, type: 'family' | 'professional'} | null>(null);
  const { toast } = useToast();

  const fetchHousehold = async () => {
    if (!params.id) {
      setLoading(false);
      return;
    }

    try {
      // Validate and sanitize the household ID
      const validatedId = validateHouseholdId(params.id as string);

      const supabase = createClient();

      // Try to fetch the household - RLS policies will prevent unauthorized access
      const { data, error } = await supabase
        .from('households')
        .select(`
          id, householdName, address, street, city, state, zip_code, country,
          marital_status, preferred_contact, best_time_to_call, alternative_contact,
          primary_advisor, last_review, next_review, notes, members,
          created_at, updated_at
        `)
        .eq('id', validatedId)
        .single();

      if (error || !data) {
        // If error or no data, user doesn't have access
        router.push('/protected/households');
      } else {
        setHousehold(data as any);
      }
    } catch (error) {
      router.push('/protected/households');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHousehold();
    fetchRelationships();
  }, [params.id]);

  const InfoItem = ({ icon: Icon, label, value }: { icon: any, label: string, value: string }) => (
    <div className="flex items-center space-x-2 text-sm">
      <Icon className="h-4 w-4 text-gray-500" />
      <span className="text-gray-600">{label}:</span>
      <span className="font-medium">{value}</span>
    </div>
  );



  const fetchRelationships = async () => {
    if (!params.id) return;

    try {
      // Validate and sanitize the household ID
      const validatedId = validateHouseholdId(params.id as string);

      const supabase = createClient();
      // Use specific column selection instead of select('*')
      const { data: relationshipsData, error } = await supabase
        .from('relationships')
        .select('id, name, relationship_type, family_relationship, profession, phone, email, notes, created_at')
        .eq('household_id', validatedId)
        .order('created_at', { ascending: false });

      if (error) {
        toast({
          title: "Error fetching relationships",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

    const familyRelationships = relationshipsData
      .filter(r => r.relationship_type === 'family')
      .map(r => ({
        id: r.id,
        name: r.name,
        family_relationship: r.family_relationship,
        phone: r.phone,
        email: r.email,
        notes: r.notes
      }));

    const professionalRelationships = relationshipsData
      .filter(r => r.relationship_type === 'professional')
      .map(r => ({
        id: r.id,
        name: r.name,
        profession: r.profession,
        phone: r.phone,
        email: r.email,
        notes: r.notes
      }));

      setRelationships({
        family: familyRelationships,
        professional: professionalRelationships
      });
    } catch (error) {
      toast({
        title: "Error fetching relationships",
        description: "Failed to load relationship data",
        variant: "destructive",
      });
    }
  };

  const calculateAge = (dateOfBirth: string): number => {
    if (!dateOfBirth) return 0;
    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }

    return age;
  };

  const handleHouseholdUpdate = async () => {
    await fetchHousehold();
  };

  const handleEditSection = (section: 'household' | 'individual' | 'additional') => {
    setActiveSectionToEdit(section);
    setShowHouseholdInfoModal(true);
  };

  const handleUpdateFamilyMember = async (member: FamilyMember) => {
    const supabase = createClient();
    const { error } = await supabase
      .from('relationships')
      .update({
        name: member.name,
        family_relationship: member.family_relationship,
        phone: member.phone,
        email: member.email,
        notes: member.notes
      })
      .eq('id', member.id);

    if (error) {
      toast({
        title: "Error updating family member",
        description: error.message,
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Family member updated",
      description: "The family member has been updated successfully.",
    });

    // Update the local state
    setRelationships(prev => ({
      ...prev,
      family: prev.family.map(fm =>
        fm.id === member.id ? member : fm
      )
    }));
  };

  const handleUpdateProfessionalContact = async (professional: ProfessionalContact) => {
    const supabase = createClient();
    const { error } = await supabase
      .from('relationships')
      .update({
        name: professional.name,
        profession: professional.profession,
        phone: professional.phone,
        email: professional.email,
        notes: professional.notes
      })
      .eq('id', professional.id);

    if (error) {
      toast({
        title: "Error updating professional contact",
        description: error.message,
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Professional contact updated",
      description: "The professional contact has been updated successfully.",
    });

    // Update the local state
    setRelationships(prev => ({
      ...prev,
      professional: prev.professional.map(pc =>
        pc.id === professional.id ? professional : pc
      )
    }));
  };

  const handleEditFamilyMember = (member: FamilyMember) => {
    setSelectedFamilyMember(member);
    setIsEditing(true);
    setShowAddFamilyModal(true);
  };

  const handleEditProfessionalContact = (professional: ProfessionalContact) => {
    setSelectedProfessional(professional);
    setIsEditing(true);
    setShowAddProfessionalModal(true);
  };

  const handleDeleteRelationship = async () => {
    if (!relationshipToDelete) return;

    const supabase = createClient();
    const { error } = await supabase
      .from('relationships')
      .delete()
      .eq('id', relationshipToDelete.id);

    if (error) {
      toast({
        title: "Error deleting relationship",
        description: error.message,
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Relationship deleted",
      description: "The relationship has been deleted successfully.",
    });

    // Update the local state
    if (relationshipToDelete.type === 'family') {
      setRelationships(prev => ({
        ...prev,
        family: prev.family.filter(fm => fm.id !== relationshipToDelete.id)
      }));
    } else {
      setRelationships(prev => ({
        ...prev,
        professional: prev.professional.filter(pc => pc.id !== relationshipToDelete.id)
      }));
    }

    setRelationshipToDelete(null);
  };

  const handleConfirmDelete = (id: string, type: 'family' | 'professional') => {
    setRelationshipToDelete({ id, type });
    setShowDeleteConfirmation(true);
  };

  return (
    <div className="flex h-screen">
      <div className="flex-grow overflow-hidden">
        <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
          <CardHeader className="border-b">
            <div className="flex justify-between items-start w-full">
              <CardTitle className="text-2xl font-bold">
                {loading ? "Loading..." : household?.householdName}
              </CardTitle>
              {!loading && household?.members && (
                <div className="space-y-1 text-sm">
                  <div className="font-medium">
                    {household.members.name1} {household.members.last_name1 || ''}
                    {household.members.date_of_birth1 ? ` (${calculateAge(household.members.date_of_birth1)})` : ''}
                  </div>
                  {household.members.name2 && (
                    <div className="font-medium">
                      {household.members.name2} {household.members.last_name2 || ''}
                      {household.members.date_of_birth2 ? ` (${calculateAge(household.members.date_of_birth2)})` : ''}
                    </div>
                  )}
                  <div>Email: {household.members.email1 || ''}</div>
                  <div>Phone: {household.members.phone1 || ''}</div>
                  <div>Address: {household.address || ''}</div>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="flex-1 overflow-y-auto space-y-4 p-6">
            {loading ? (
              <div>Loading household details...</div>
            ) : household ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Contact & Basic Information Section */}
                  <Card className="shadow-sm">
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="text-lg">Household Information</CardTitle>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditSection('household')}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium mb-2">Household Details</h3>
                          <InfoItem icon={User} label="Household Name" value={household.householdName || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={Heart} label="Marital Status" value={household.marital_status || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={Phone} label="Preferred Contact" value={household.preferred_contact || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={Clock} label="Best Time to Call" value={household.best_time_to_call || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={Users} label="Alternative Contact" value={household.alternative_contact || ''} />
                        </div>

                        <div>
                          <h3 className="text-sm font-medium mb-2">Address Information</h3>
                          <InfoItem icon={MapPin} label="Address" value={household.address || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={MapPin} label="Postal Address" value={household.notes?.startsWith('Postal Address:') ? household.notes.substring(15) : ''} />
                          <div className="mb-2" />
                          <InfoItem icon={MapPin} label="Street" value={household.street || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={MapPin} label="City" value={household.city || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={MapPin} label="State" value={household.state || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={MapPin} label="Post Code" value={household.zip_code || ''} />
                          <div className="mb-2" />
                          <InfoItem icon={Globe} label="Country" value={household.country || ''} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Individual Metrics Section */}
                  <Card className="shadow-sm">
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="text-lg">Individual Metrics</CardTitle>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditSection('individual')}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        {household.members && (
                          <div>
                            <h3 className="text-sm font-medium mb-2">{household.members.name1} {household.members.last_name1 || ''}</h3>
                            <InfoItem icon={Briefcase} label="Occupation" value={household.members.occupation1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Building} label="Employer" value={household.members.employer1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Calendar} label="Date of Birth" value={household.members.date_of_birth1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={FileText} label="IRD Number" value={household.members.tax_file_number1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Globe} label="Citizenship" value={household.members.citizenship1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Hash} label="Tax Residency" value={household.members.tax_residency1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Users2} label="Employment Status" value={household.members.employment_status1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Mail} label="Email" value={household.members.email1 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Phone} label="Phone" value={household.members.phone1 || ''} />
                          </div>
                        )}
                        {household.members && household.members.name2 && (
                          <div>
                            <h3 className="text-sm font-medium mb-2">{household.members.name2} {household.members.last_name2 || ''}</h3>
                            <InfoItem icon={Briefcase} label="Occupation" value={household.members.occupation2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Building} label="Employer" value={household.members.employer2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Calendar} label="Date of Birth" value={household.members.date_of_birth2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={FileText} label="IRD Number" value={household.members.tax_file_number2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Globe} label="Citizenship" value={household.members.citizenship2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Hash} label="Tax Residency" value={household.members.tax_residency2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Users2} label="Employment Status" value={household.members.employment_status2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Mail} label="Email" value={household.members.email2 || ''} />
                            <div className="mb-2" />
                            <InfoItem icon={Phone} label="Phone" value={household.members.phone2 || ''} />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Additional Details Section */}
                <Card className="shadow-sm">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-lg">Additional Details</CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditSection('additional')}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-6">
                      <div>
                        <h3 className="text-sm font-medium mb-4">Advisory Information</h3>
                        <InfoItem icon={UserCheck} label="Primary Advisor" value={household.primary_advisor || ''} />
                        <div className="mb-4" />
                        <InfoItem icon={Calendar} label="Last Review" value={household.last_review || ''} />
                        <div className="mb-4" />
                        <InfoItem icon={Calendar} label="Next Review" value={household.next_review || ''} />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-4">Additional Information</h3>
                        <InfoItem icon={Calendar} label="Created At" value={new Date(household.created_at).toLocaleDateString()} />
                        <div className="mb-4" />
                        <InfoItem icon={Calendar} label="Updated At" value={household.updated_at ? new Date(household.updated_at).toLocaleDateString() : 'Not updated'} />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-4">Notes</h3>
                        <div className="text-sm">
                          {household.notes?.startsWith('Postal Address:') ?
                            'See Postal Address above' :
                            (household.notes || 'No additional notes')}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Relationships Section */}
                <Card className="shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Relationships</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="family" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="family" className="flex items-center gap-2">
                          <Users2 className="h-4 w-4" />
                          Family
                        </TabsTrigger>
                        <TabsTrigger value="professional" className="flex items-center gap-2">
                          <Briefcase className="h-4 w-4" />
                          Professional
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="family" className="mt-4">
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <h3 className="text-sm font-semibold">Family Members</h3>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2"
                              onClick={() => {
                                setSelectedFamilyMember(undefined);
                                setIsEditing(false);
                                setShowAddFamilyModal(true);
                              }}
                            >
                              <UserPlus className="h-4 w-4" />
                              Add Family Member
                            </Button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {relationships.family.length > 0 ? (
                              relationships.family.map((member) => (
                                <Card key={member.id}>
                                  <CardHeader className="flex flex-row items-center justify-between">
                                    <div>
                                      <CardTitle className="text-base">{member.name}</CardTitle>
                                      <p className="text-sm text-muted-foreground">{member.family_relationship}</p>
                                    </div>
                                    <div className="flex space-x-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleEditFamilyMember(member)}
                                      >
                                        <Pencil className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleConfirmDelete(member.id, 'family')}
                                      >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                      </Button>
                                    </div>
                                  </CardHeader>
                                  <CardContent className="space-y-3">
                                    <InfoItem icon={Phone} label="Phone" value={member.phone || ''} />
                                    <InfoItem icon={Mail} label="Email" value={member.email || ''} />
                                    {member.notes?.includes('Age:') && (
                                      <InfoItem icon={Calendar} label="Age" value={member.notes.includes('Age:') ? member.notes.split('Age:')[1].split(',')[0].trim() : ''} />
                                    )}
                                    {member.notes?.includes('Support:') && (
                                      <InfoItem icon={Heart} label="Support" value={member.notes.includes('Support:') ? member.notes.split('Support:')[1].trim() : ''} />
                                    )}
                                    {member.notes && !member.notes.includes('Age:') && !member.notes.includes('Support:') && (
                                      <div className="text-sm">
                                        <span className="text-gray-600">Notes:</span>
                                        <p className="mt-1 text-gray-700">{member.notes}</p>
                                      </div>
                                    )}
                                  </CardContent>
                                </Card>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground col-span-2 text-center py-4">
                                No family members added yet
                              </p>
                            )}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="professional" className="mt-4">
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <h3 className="text-sm font-semibold">Professional Contacts</h3>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2"
                              onClick={() => {
                                setSelectedProfessional(undefined);
                                setIsEditing(false);
                                setShowAddProfessionalModal(true);
                              }}
                            >
                              <UserPlus className="h-4 w-4" />
                              Add Professional
                            </Button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {relationships.professional.length > 0 ? (
                              relationships.professional.map((professional) => (
                                <Card key={professional.id}>
                                  <CardHeader className="flex flex-row items-center justify-between">
                                    <div>
                                      <CardTitle className="text-base">{professional.name}</CardTitle>
                                      <p className="text-sm text-muted-foreground">{professional.profession}</p>
                                    </div>
                                    <div className="flex space-x-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleEditProfessionalContact(professional)}
                                      >
                                        <Pencil className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleConfirmDelete(professional.id, 'professional')}
                                      >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                      </Button>
                                    </div>
                                  </CardHeader>
                                  <CardContent className="space-y-3">
                                    <InfoItem icon={Phone} label="Phone" value={professional.phone || ''} />
                                    <InfoItem icon={Mail} label="Email" value={professional.email || ''} />
                                    <InfoItem icon={Building} label="Company" value={professional.notes?.includes('Company:') ? professional.notes.split('Company:')[1].trim() : ''} />
                                    {professional.notes && !professional.notes.includes('Company:') && (
                                      <div className="text-sm">
                                        <span className="text-gray-600">Notes:</span>
                                        <p className="mt-1 text-gray-700">{professional.notes}</p>
                                      </div>
                                    )}
                                  </CardContent>
                                </Card>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground col-span-2 text-center py-4">
                                No professional contacts added yet
                              </p>
                            )}
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </>
            ) : (
              <div>No household data found.</div>
            )}
          </CardContent>
        </Card>
      </div>

      <AddFamilyModal
        isOpen={showAddFamilyModal}
        onClose={() => {
          setShowAddFamilyModal(false);
          setSelectedFamilyMember(undefined);
          setIsEditing(false);
        }}
        onAdd={async (member) => {
          const supabase = createClient();
          const { error } = await supabase
            .from('relationships')
            .insert({
              household_id: params.id,
              name: member.name,
              relationship_type: 'family',
              family_relationship: member.family_relationship,
              phone: member.phone,
              email: member.email,
              notes: member.notes
            });

          if (error) {
            toast({
              title: "Error adding family member",
              description: error.message,
              variant: "destructive",
            });
            return;
          }

          fetchRelationships();
          setShowAddFamilyModal(false);
        }}
        memberToEdit={selectedFamilyMember}
        isEditing={isEditing}
        onUpdate={handleUpdateFamilyMember}
      />

      <AddProfessionalModal
        isOpen={showAddProfessionalModal}
        onClose={() => {
          setShowAddProfessionalModal(false);
          setSelectedProfessional(undefined);
          setIsEditing(false);
        }}
        onAdd={async (professional) => {
          const supabase = createClient();
          const { error } = await supabase
            .from('relationships')
            .insert({
              household_id: params.id,
              name: professional.name,
              relationship_type: 'professional',
              profession: professional.profession,
              phone: professional.phone,
              email: professional.email,
              notes: professional.notes
            });

          if (error) {
            toast({
              title: "Error adding professional",
              description: error.message,
              variant: "destructive",
            });
            return;
          }

          fetchRelationships();
          setShowAddProfessionalModal(false);
        }}
        professionalToEdit={selectedProfessional}
        isEditing={isEditing}
        onUpdate={handleUpdateProfessionalContact}
      />
      <HouseholdInfoModal
        isOpen={showHouseholdInfoModal}
        onClose={() => {
          setShowHouseholdInfoModal(false);
          setActiveSectionToEdit(null);
        }}
        householdData={household}
        onUpdate={handleHouseholdUpdate}
        section={activeSectionToEdit}
      />

      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteRelationship}
        title="Delete Relationship"
        description="Are you sure you want to delete this relationship? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}
