'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { TasksTable } from '@/components/tables/TasksTable';
import TaskModal from '@/components/modals/TaskModal';

interface Task {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  comments: {
    id: number;
    task_id: number;
    content: string;
    created_at: string;
    user_id: string;
  }[];
}

interface TaskWithHousehold {
  id: number;
  household_id: number;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  task_comments: {
    id: number;
    task_id: number;
    content: string;
    created_at: string;
    user_id: string;
  }[];
  households: {
    householdName: string;
  };
}

export default function Tasks() {
  const params = useParams();
  const searchParams = useSearchParams();
  const householdId = parseInt(params.id as string, 10);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | undefined>(undefined);
  const [householdName, setHouseholdName] = useState<string>('');
  const supabase = createClient();

  const fetchHouseholdName = async () => {
    const { data, error } = await supabase
      .from('households')
      .select('householdName')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household name:', error);
    } else if (data) {
      setHouseholdName(data.householdName);
    }
  };

  const fetchTasks = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('No authenticated user found');
      return;
    }

    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_comments (
          id,
          content,
          created_at,
          user_id
        ),
        households (
          householdName
        )
      `)
      .eq('household_id', householdId)
      .order('created_at', { ascending: false }) as { data: TaskWithHousehold[] | null, error: any };

    if (error) {
      console.error('Error fetching tasks:', error);
    } else if (data) {
      const tasksWithHouseholdNames = data.map(task => ({
        ...task,
        household_name: task.households?.householdName || 'Unknown Household',
        comments: task.task_comments || []
      })) as Task[];
      setTasks(tasksWithHouseholdNames);
    }
  };

  useEffect(() => {
    fetchTasks();
    fetchHouseholdName();
  }, [householdId]);

  const handleCreateTask = () => {
    setSelectedTask(undefined);
    setIsModalOpen(true);
  };

  const handleEditTask = (task: Task) => {
    setSelectedTask(task);
    setIsModalOpen(true);
  };

  // Add this useEffect to handle the view parameter
  useEffect(() => {
    const viewTaskId = searchParams.get('view');
    
    if (viewTaskId) {
      console.log(`Task view parameter detected: ${viewTaskId}`);
      
      // Find the task in our loaded data
      const taskToView = tasks.find(
        task => task.id.toString() === viewTaskId
      );
      
      if (taskToView) {
        console.log('Found task to view:', taskToView);
        // Open the modal with the task
        setSelectedTask(taskToView);
        setIsModalOpen(true);
      } else if (tasks.length > 0) {
        // Only fetch if we already loaded tasks but didn't find this one
        console.log('Task not found in current data, fetching...');
        fetchSpecificTask(parseInt(viewTaskId));
      }
      // If tasks.length is 0, we'll wait for fetchTasks to complete first
    }
  }, [searchParams, tasks]); // Re-run when searchParams or tasks change

  // Add this function to fetch a specific task by ID
  const fetchSpecificTask = async (taskId: number) => {
    console.log(`Fetching specific task: ${taskId}`);
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_comments (
          id,
          content,
          created_at,
          user_id
        ),
        households (
          householdName
        )
      `)
      .eq('id', taskId)
      .single() as { data: TaskWithHousehold | null, error: any };
      
    if (error) {
      console.error('Error fetching specific task:', error);
      return;
    }
    
    if (data) {
      console.log('Successfully fetched task:', data);
      
      // Format the task to match your Task interface
      const formattedTask = {
        ...data,
        household_name: data.households?.householdName || 'Unknown Household',
        comments: data.task_comments || []
      } as Task;
      
      // Open the modal with the fetched task
      setSelectedTask(formattedTask);
      setIsModalOpen(true);
    }
  };

  return (
    <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
      <CardContent className="flex-grow pt-4">
        <TasksTable
          data={tasks}
          onDataChange={fetchTasks}
          onCreateTask={handleCreateTask}
          onEditTask={handleEditTask}
          fixedHouseholdId={householdId.toString()}
          fixedHouseholdName={householdName}
        />
      </CardContent>

      {isModalOpen && (
        <TaskModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedTask(undefined);
          }}
          task={selectedTask}
          onSave={fetchTasks}
          householdId={householdId}
          householdName={householdName}
        />
      )}
    </Card>
  );
}
