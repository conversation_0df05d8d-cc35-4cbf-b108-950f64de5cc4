import { requireAuth } from '@/utils/auth-guard';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import HouseholdsClient from './HouseholdsClient';

interface Household {
  id: number;
  householdName: string;
  members: string;
  city: string;
  state: string;
  primary_advisor: string;
  last_review: string;
  next_review: string;
  user_id: string;
  org_id: string | null;
  created_at: string;
  updated_at: string;
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

/**
 * Server component that validates authentication and fetches data
 * This cannot be bypassed by disabling JavaScript
 */
export default async function HouseholdsPage() {
  // Server-side authentication check - cannot be bypassed
  const user = await requireAuth();

  if (!user) {
    redirect('/sign-in');
  }

  // Server-side data fetching with proper authorization
  const supabase = createClient();

  try {
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_id, org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      redirect('/sign-in');
    }

    // Fetch households with proper authorization - default to user view
    let householdsQuery = supabase.from('households')
      .select('id, householdName, members, city, state, primary_advisor, last_review, next_review, user_id, org_id, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    const { data: households, error: householdsError } = await householdsQuery;

    if (householdsError) {
      // If there's an error, still render the page but with empty data
      return (
        <HouseholdsClient
          initialHouseholds={[]}
          userProfile={profile as UserProfile}
          user={user}
        />
      );
    }

    // Pass server-fetched data to client component
    return (
      <HouseholdsClient
        initialHouseholds={households as Household[]}
        userProfile={profile as UserProfile}
        user={user}
      />
    );

  } catch (error) {
    // If any server-side operation fails, redirect to sign-in
    redirect('/sign-in');
  }
}
