import { Suspense } from 'react';
import TasksPageClient from '@/components/tasks/TasksPageClient'; // Import the new client component

// This remains a Server Component
export default function TasksPage() {
  return (
    // Wrap the client component in Suspense
    // The fallback can be any loading indicator component or simple text
    <Suspense fallback={<div>Loading tasks...</div>}>
      <TasksPageClient />
    </Suspense>
  );
}
