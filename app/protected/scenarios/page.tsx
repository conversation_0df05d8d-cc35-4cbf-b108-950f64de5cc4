import { requireAuth } from '@/utils/auth-guard';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import ScenariosClient from './ScenariosClient';

interface RecentScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_viewed_at?: string;
}

interface AllScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  household_id: number;
  user_id: string;
  org_id: string | undefined;
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

/**
 * Server component that validates authentication and fetches data
 * This cannot be bypassed by disabling JavaScript
 */
export default async function ScenariosPage() {
  // Server-side authentication check - cannot be bypassed
  const user = await requireAuth();

  if (!user) {
    redirect('/sign-in');
  }

  // Server-side data fetching with proper authorization
  const supabase = createClient();

  try {
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_id, org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      redirect('/sign-in');
    }

    // Fetch recent scenarios (last 5) with proper authorization
    let recentQuery = supabase.from('scenarios_data1')
      .select('id, scenario_name, household_name, created_at, last_viewed_at, household_id')
      .eq('user_id', user.id)
      .order('last_viewed_at', { ascending: false })
      .limit(5);

    const { data: recentScenarios, error: recentError } = await recentQuery;

    // Fetch all scenarios with proper authorization
    let allQuery = supabase.from('scenarios_data1')
      .select('id, scenario_name, household_name, created_at, last_edited_at, household_id, user_id, org_id')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    const { data: allScenarios, error: allError } = await allQuery;

    if (recentError || allError) {
      // If there's an error, still render the page but with empty data
      return (
        <ScenariosClient
          initialRecentScenarios={[]}
          initialAllScenarios={[]}
          userProfile={profile as UserProfile}
          user={user}
        />
      );
    }

    // Pass server-fetched data to client component
    return (
      <ScenariosClient
        initialRecentScenarios={recentScenarios as RecentScenario[]}
        initialAllScenarios={allScenarios as AllScenario[]}
        userProfile={profile as UserProfile}
        user={user}
      />
    );

  } catch (error) {
    // If any server-side operation fails, redirect to sign-in
    redirect('/sign-in');
  }
}
