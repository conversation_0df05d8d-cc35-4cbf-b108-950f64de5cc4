"use client";
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import CreateScenarioModal from '@/components/modals/CreateScenarioModal';
import { createClient } from '@/utils/supabase/client';
import RecentScenarioCard from '@/components/RecentScenarioCard';
import AllScenariosTable from '@/components/tables/AllScenariosTable';
import PresentationModal from '@/components/modals/PresentationModal';
import { Loader2 } from 'lucide-react';

// Define interfaces for your scenario data
interface RecentScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_viewed_at?: string;
}

interface AllScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  household_id: number;
  user_id: string;
  org_id: string | undefined;
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

interface ScenariosClientProps {
  initialRecentScenarios: RecentScenario[];
  initialAllScenarios: AllScenario[];
  userProfile: UserProfile;
  user: any;
}

export default function ScenariosClient({
  initialRecentScenarios,
  initialAllScenarios,
  userProfile,
  user
}: ScenariosClientProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [recentScenarios, setRecentScenarios] = useState<RecentScenario[]>(initialRecentScenarios);
  const [allScenarios, setAllScenarios] = useState<AllScenario[]>(initialAllScenarios);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');

  useEffect(() => {
    // Ensure viewMode has a default value if not in localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      // Set default to 'user' if nothing is stored
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
      localStorage.setItem('scenariosViewMode', 'user');
    }

    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };

    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  useEffect(() => {
    if (userProfile.user_id) {
      fetchScenarios();

      // Set up real-time subscription
      const supabase = createClient();
      const channel = supabase
        .channel('scenarios-changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'scenarios_data1'
          },
          (payload) => {
            // Refresh scenarios when any change occurs
            fetchScenarios();
          }
        )
        .subscribe();

      // Cleanup subscription on unmount
      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [viewMode, userProfile]);

  const fetchScenarios = async () => {
    setIsLoading(true);
    const supabase = createClient();

    try {
      // For recent scenarios
      let recentQuery = supabase.rpc('get_scenarios_with_household_names_recent');

      // Apply view mode filter if profile data is available
      if (userProfile.user_id) {
        if (viewMode === 'user') {
          recentQuery = supabase.from('scenarios_data1')
            .select('id, scenario_name, household_name, created_at, last_viewed_at, household_id')
            .eq('user_id', userProfile.user_id)
            .order('last_viewed_at', { ascending: false })
            .limit(5);
        } else if (viewMode === 'organization' && userProfile.org_id) {
          recentQuery = supabase.from('scenarios_data1')
            .select('id, scenario_name, household_name, created_at, last_viewed_at, household_id')
            .eq('org_id', userProfile.org_id)
            .order('last_viewed_at', { ascending: false })
            .limit(5);
        }
      }

      const { data: recentData, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent scenarios with household names:', recentError);

        // Fallback to regular query if RPC fails
        const { data: fallbackRecentData, error: fallbackRecentError } = await supabase
          .from('scenarios_data1')
          .select('id, scenario_name, household_name, created_at, last_viewed_at, household_id')
          .order('last_viewed_at', { ascending: false })
          .limit(5);

        if (fallbackRecentError) throw fallbackRecentError;
        setRecentScenarios(fallbackRecentData || []);
      } else {
        setRecentScenarios(recentData || []);
      }

      // For all scenarios - modify to include filtering by view mode
      let allQuery = supabase.from('scenarios_data1')
        .select('id, scenario_name, household_name, created_at, last_edited_at, household_id, user_id, org_id');

      // Apply view mode filter if profile data is available
      if (userProfile.user_id) {
        if (viewMode === 'user') {
          allQuery = allQuery.eq('user_id', userProfile.user_id);
        } else if (viewMode === 'organization' && userProfile.org_id) {
          allQuery = allQuery.eq('org_id', userProfile.org_id);
        }
      }

      allQuery = allQuery.order('created_at', { ascending: false });

      const { data: allData, error: allError } = await allQuery;

      if (allError) {
        console.error('Error fetching scenarios:', allError);
        setAllScenarios([]);
      } else {
        setAllScenarios(allData || []);
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error in fetchScenarios:', error);
      setIsLoading(false);
    }
  };

  const handleScenarioCreation = (householdId: string, scenarioName: string, userId: string, householdName: string | null, orgId?: string) => {
    // This function is now only used for the "Open in Scenario Builder" option
    // The actual scenario creation happens in the builder, not here
    setIsModalOpen(false);
    setIsInitializing(true);
    setTimeout(() => {
      setIsInitializing(false);
      fetchScenarios(); // Refresh scenarios after creation
    }, 3000);
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <div className="flex-1 flex flex-col pt-2 pr-2 pl-0 mt-[50px]">
          <div className="flex flex-col h-full gap-2 overflow-hidden" style={{ height: 'calc(100vh - 50px - 8px)' }}>
            <Card className="flex-shrink-0">
              <CardHeader>
                <CardTitle>Recent Scenarios</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    {recentScenarios.map((scenario) => (
                      <RecentScenarioCard key={scenario.id} scenario={scenario} />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="flex-1 flex flex-col min-h-0">
              <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
                <CardTitle>All Scenarios</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden min-h-0">
                {isLoading ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : (
                  <AllScenariosTable
                    data={allScenarios}
                    onDataChange={fetchScenarios}
                    onCreateScenario={() => setIsModalOpen(true)}
                    onPresentScenarios={() => setIsPresentationModalOpen(true)}
                    viewMode={viewMode}
                    onViewModeChange={(value) => {
                      setViewMode(value);
                      localStorage.setItem('viewMode', value);
                      localStorage.setItem('scenariosViewMode', value);
                      window.dispatchEvent(new Event('viewModeChange'));
                    }}
                  />
                )}
              </CardContent>
            </Card>
          </div>
      </div>

      <CreateScenarioModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onCreateScenario={handleScenarioCreation}
      />

      <PresentationModal
        isOpen={isPresentationModalOpen}
        onClose={() => setIsPresentationModalOpen(false)}
      />

      {isInitializing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <p className="text-lg font-semibold">Initialising Scenario Builder</p>
          </div>
        </div>
      )}
    </div>
  );
}
