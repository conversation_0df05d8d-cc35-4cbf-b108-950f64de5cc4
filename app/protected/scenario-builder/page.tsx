"use client";

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useScenarioMetrics } from '@/hooks/useScenarioMetrics';
import { applyScenarioMetrics } from '@/utils/applyScenarioMetrics';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
// Removed Accordion and Tabs imports
import { Loader2 } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import ScenarioSavedModal from '@/components/modals/ScenarioSaveModal';
import ScenarioBuilderTabs from '@/components/ScenarioBuilderTabs'; // Import the new component
import { FUND_TYPES, FundType } from '@/app/constants/fundTypes';
import React from 'react'; // Ensure React is imported

// Interface ScenarioData remains the same
interface ScenarioData {
  householdId: string;
  name: string;
  personal: {
    name: string;
    starting_age: number | null;
    ending_age: number | null;
    include_partner: boolean;
    partner_name: string;
    partner_starting_age: number | null;
  };
  income: {
    annual_income: number | null;
    income_period: [number | null, number | null];
    partner_annual_income: number | null;
    partner_income_period: [number | null, number | null];
    include_superannuation: boolean;
  };
  savings: {
    savings_amount: number | null;
    cash_reserve: number | null;
    saving_percentage: number | null;
  };
  expenditure: {
    annual_expenses1: number | null;
    expense_period1: [number | null, number | null];
    annual_expenses2: number | null;
    expense_period2: [number | null, number | null];
    additional_expenses: Array<{ title: string; value: number; period: [number, number]; frequency: number }>;
  };
  investment: {
    annual_investment_return?: number;
    inv_std_dev?: number;
    initial_investment: number | null;
    annual_investment_contribution: number | null;
    fundType: FundType;
    fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
  };
  kiwiSaver: {
    initial_kiwisaver: number | null;
    kiwisaver_contribution: number | null;
    employer_contribution: number | null;
    annual_kiwisaver_return?: number | null;
    ks_std_dev?: number | null;
    partner_initial_kiwisaver: number | null;
    partner_kiwisaver_contribution: number | null;
    partner_employer_contribution: number | null;
    partner_annual_kiwisaver_return?: number | null;
    partner_ks_std_dev?: number | null;
    fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
    partner_fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
  };
  property: {
    property_value: number | null;
    property_growth: number | null;
    debt: number | null;
    debt_ir: number | null;
    initial_debt_years: number | null;
    additional_debt_repayments: number | null;
    show_repayments: boolean;
    sell_main_property: boolean;
    main_property_sale_age: number | null;
    main_prop_sale_value: number | null;
    sale_allocate_to_investment: boolean;
    downsize: boolean;
    downsize_age: number | null;
    new_property_value: number | null;
    allocate_to_investment: boolean;
    pay_off_debt: boolean;
    additional_properties: Array<{
      property_value: number;
      property_growth: number;
      debt: number;
      debt_ir: number;
      initial_debt_years: number;
      additional_debt_repayments: number;
      sell_property: boolean;
      show_repayments: boolean;
    }>;
  };
  economic: {
    inflation_rate: number | null;
  };
  monteCarlo: {
    simulations: number | null;
    confidence_interval: number | null;
  };
  misc: {
    household_name: string | null;
  };
}

// Removed tabSections definition

export default function ScenarioBuilder() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-screen"><Loader2 className="h-8 w-8 animate-spin" /> Loading Scenario Builder...</div>}>
      <ScenarioBuilderContent />
    </Suspense>
  );
}

function ScenarioBuilderContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createClient();

  // Get the current user ID
  const [userId, setUserId] = useState<string | undefined>(undefined);

  // Fetch user ID on component mount
  useEffect(() => {
    const fetchUserId = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (!error && data.user) {
        setUserId(data.user.id);
      }
    };

    fetchUserId();
  }, []);

  // Fetch scenario metrics
  const { metrics, isLoading: metricsLoading } = useScenarioMetrics(userId);

  // Initialize default scenario data
  const getDefaultScenarioData = (): ScenarioData => ({
    householdId: searchParams.get('householdId') || '',
    name: searchParams.get('name') || 'New Scenario',
    personal: { name: '', starting_age: null, ending_age: 95, include_partner: false, partner_name: '', partner_starting_age: null },
    income: { annual_income: null, income_period: [null, null], partner_annual_income: null, partner_income_period: [null, null], include_superannuation: false },
    savings: { savings_amount: null, cash_reserve: null, saving_percentage: 100 },
    expenditure: { annual_expenses1: null, expense_period1: [null, null], annual_expenses2: null, expense_period2: [null, null], additional_expenses: [] },
    investment: { initial_investment: null, annual_investment_contribution: null, fundType: 'Balanced', fund_periods: [] },
    kiwiSaver: { initial_kiwisaver: null, kiwisaver_contribution: 3, employer_contribution: 3, partner_initial_kiwisaver: null, partner_kiwisaver_contribution: 3, partner_employer_contribution: 3, fund_periods: [], partner_fund_periods: [] },
    property: { property_value: null, property_growth: 3, debt: null, debt_ir: 5, initial_debt_years: 30, additional_debt_repayments: 0, show_repayments: false, sell_main_property: false, main_property_sale_age: null, main_prop_sale_value: null, sale_allocate_to_investment: false, downsize: false, downsize_age: null, new_property_value: null, allocate_to_investment: false, pay_off_debt: false, additional_properties: [] },
    economic: { inflation_rate: 2.0 },
    monteCarlo: { simulations: 100, confidence_interval: 80 },
    misc: { household_name: null },
  });

  // Initialize scenario data
  const [scenarioData, setScenarioData] = useState<ScenarioData>(getDefaultScenarioData);

  // Function to apply scenario metrics to scenario data
  const applyScenarioMetricsToScenarioData = useCallback((metrics: any, data: ScenarioData): ScenarioData => {
    const updatedData = { ...data };

    // Apply personal settings
    if (metrics.toggle_states.personal?.include_partner) {
      updatedData.personal.include_partner = metrics.default_values.personal?.include_partner?.value ?? false;
    }

    // Apply income settings
    if (metrics.toggle_states.income?.include_superannuation) {
      updatedData.income.include_superannuation = metrics.default_values.income?.include_superannuation?.value ?? false;
    }

    // Apply savings settings
    if (metrics.toggle_states.savings?.cash_reserve) {
      updatedData.savings.cash_reserve = metrics.default_values.savings?.cash_reserve?.value ?? null;
    }

    if (metrics.toggle_states.savings?.saving_percentage) {
      updatedData.savings.saving_percentage = metrics.default_values.savings?.saving_percentage?.value ?? 100;
    }

    if (metrics.toggle_states.property?.property_inflation_rate_toggle) {
      updatedData.property.property_growth = metrics.default_values.property?.property_inflation_rate?.value ?? 3;
    }

    // Apply KiwiSaver settings
    if (metrics.toggle_states.kiwisaver?.kiwisaver_contribution) {
      updatedData.kiwiSaver.kiwisaver_contribution = metrics.default_values.kiwisaver?.kiwisaver_contribution?.value ?? 3;
    }

    if (metrics.toggle_states.kiwisaver?.employer_contribution) {
      updatedData.kiwiSaver.employer_contribution = metrics.default_values.kiwisaver?.employer_contribution?.value ?? 3;
    }

    // Apply economic settings
    if (metrics.toggle_states.misc?.inflation_rate_toggle) {
      updatedData.economic.inflation_rate = metrics.default_values.misc?.inflation_rate?.value ?? 2.0;
    }

    // Apply Monte Carlo settings
    if (metrics.toggle_states.misc?.num_simulations_toggle) {
      updatedData.monteCarlo.simulations = metrics.default_values.misc?.num_simulations?.value ?? 1000;
    }

    if (metrics.toggle_states.misc?.confidence_interval_toggle) {
      updatedData.monteCarlo.confidence_interval = metrics.default_values.misc?.confidence_interval?.value ?? 95;
    }

    return updatedData;
  }, []);

  // Function to load an existing scenario
  const loadScenario = useCallback(async (scenarioId: string) => {
    try {
      const { data, error } = await supabase
        .from('scenarios_data1')
        .select('*')
        .eq('id', parseInt(scenarioId))
        .single();

      if (error) {
        console.error('Error loading scenario:', error);
        return;
      }

      if (data) {
        // Convert database data to ScenarioData format
        const loadedData: ScenarioData = {
          householdId: data.household_id?.toString() || '',
          name: data.scenario_name || 'Loaded Scenario',
          personal: {
            name: data.name || '',
            starting_age: data.starting_age,
            ending_age: data.ending_age || 95,
            include_partner: !!data.partner_name,
            partner_name: data.partner_name || '',
            partner_starting_age: data.partner_starting_age
          },
          income: {
            annual_income: data.annual_income,
            income_period: data.income_period || [null, null],
            partner_annual_income: data.partner_annual_income,
            partner_income_period: data.partner_income_period || [null, null],
            include_superannuation: data.include_superannuation || false
          },
          savings: {
            savings_amount: data.savings_amount,
            cash_reserve: data.cash_reserve,
            saving_percentage: data.saving_percentage || 100
          },
          expenditure: {
            annual_expenses1: data.annual_expenses1,
            expense_period1: data.expense_period1 || [null, null],
            annual_expenses2: data.annual_expenses2,
            expense_period2: data.expense_period2 || [null, null],
            additional_expenses: data.additional_expenses || []
          },
          investment: {
            initial_investment: data.initial_investment,
            annual_investment_contribution: data.annual_investment_contribution,
            annual_investment_return: data.annual_investment_return,
            inv_std_dev: data.inv_std_dev,
            fundType: data.fund_type || 'Balanced',
            fund_periods: data.fund_periods || []
          },
          kiwiSaver: {
            initial_kiwisaver: data.initial_kiwisaver,
            kiwisaver_contribution: data.kiwisaver_contribution || 3,
            employer_contribution: data.employer_contribution || 3,
            annual_kiwisaver_return: data.annual_kiwisaver_return,
            ks_std_dev: data.ks_std_dev,
            partner_initial_kiwisaver: data.partner_initial_kiwisaver,
            partner_kiwisaver_contribution: data.partner_kiwisaver_contribution || 3,
            partner_employer_contribution: data.partner_employer_contribution || 3,
            partner_annual_kiwisaver_return: data.partner_annual_kiwisaver_return,
            partner_ks_std_dev: data.partner_ks_std_dev,
            fund_periods: data.kiwisaver_fund_periods || [],
            partner_fund_periods: data.partner_kiwisaver_fund_periods || []
          },
          property: {
            property_value: data.property_value,
            property_growth: data.property_growth || 3,
            debt: data.debt,
            debt_ir: data.debt_ir || 5,
            initial_debt_years: data.initial_debt_years || 30,
            additional_debt_repayments: data.additional_debt_repayments || 0,
            show_repayments: data.show_repayments || false,
            sell_main_property: data.sell_main_property || false,
            main_property_sale_age: data.main_property_sale_age,
            main_prop_sale_value: data.main_prop_sale_value,
            sale_allocate_to_investment: data.sale_allocate_to_investment || false,
            downsize: data.downsize || false,
            downsize_age: data.downsize_age,
            new_property_value: data.new_property_value,
            allocate_to_investment: data.allocate_to_investment || false,
            pay_off_debt: data.pay_off_debt || false,
            additional_properties: data.additional_properties || []
          },
          economic: {
            inflation_rate: data.inflation_rate || 2.0
          },
          monteCarlo: {
            simulations: data.simulations || 1000,
            confidence_interval: data.confidence_interval || 95
          },
          misc: {
            household_name: data.household_name || null
          }
        };

        // Apply scenario metrics to the loaded data
        const updatedData = applyScenarioMetricsToScenarioData(metrics, loadedData);
        setScenarioData(updatedData);
      }
    } catch (error) {
      console.error('Error in loadScenario:', error);
    }
  }, [supabase, metrics, applyScenarioMetricsToScenarioData]);

  // Apply scenario metrics when they are loaded
  useEffect(() => {
    if (!metricsLoading && userId) {
      // Check if we're loading from URL data
      const dataParam = searchParams.get('data');
      if (dataParam) {
        try {
          const parsedData = JSON.parse(dataParam);
          const updatedData = applyScenarioMetricsToScenarioData(metrics, parsedData);
          setScenarioData(updatedData);
          return;
        } catch (error) {
          console.error('Error parsing scenario data from URL:', error);
        }
      }

      // Check if we're editing an existing scenario
      const scenarioId = searchParams.get('scenarioId');
      if (scenarioId) {
        loadScenario(scenarioId);
      } else {
        // For new scenarios, apply the metrics to the default values
        const defaultData = getDefaultScenarioData();
        const updatedData = applyScenarioMetricsToScenarioData(metrics, defaultData);
        setScenarioData(updatedData);
      }
    }
  }, [metricsLoading, userId, searchParams, metrics, loadScenario]);



  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [savedScenarioId, setSavedScenarioId] = useState<number | null>(null);

  // Validate mandatory fields
  const validateMandatoryFields = () => {
    // Check personal fields
    if (scenarioData.personal.starting_age === null || scenarioData.personal.ending_age === null) {
      return false;
    }

    // Check partner starting age if partner is included
    if (scenarioData.personal.include_partner && scenarioData.personal.partner_starting_age === null) {
      return false;
    }

    // Check expense fields
    if (
      scenarioData.expenditure.annual_expenses1 === null ||
      scenarioData.expenditure.expense_period1[0] === null ||
      scenarioData.expenditure.expense_period1[1] === null ||
      scenarioData.expenditure.annual_expenses2 === null ||
      scenarioData.expenditure.expense_period2[0] === null ||
      scenarioData.expenditure.expense_period2[1] === null
    ) {
      return false;
    }

    return true;
  };

  // handleSave logic remains the same
  const handleSave = async () => {
    setIsSaving(true);
    const supabase = createClient();

    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError || !userData.user) {
        console.error('Error getting user or user not found:', userError);
        setIsSaving(false);
        return;
      }
      const userId = userData.user.id;

      let householdName = scenarioData.misc.household_name;
      if (!householdName && scenarioData.householdId) {
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', parseInt(scenarioData.householdId))
          .single();
        if (!householdError && householdData) {
          householdName = householdData.householdName;
        } else if (householdError) {
          console.warn('Could not fetch household name:', householdError.message);
        }
      }

      const formatFundPeriods = (periods: ScenarioData['investment']['fund_periods']) => {
        return periods?.map(period => {
          const fundTypeData = period.fundType !== 'Custom' ? FUND_TYPES[period.fundType] : null;
          return {
            fundType: period.fundType,
            period: period.period,
            return: fundTypeData?.return ?? period.return,
            stdDev: fundTypeData?.stdDev ?? period.stdDev
          };
        }) || [];
      };

      const formattedInvestmentPeriods = formatFundPeriods(scenarioData.investment?.fund_periods);
      const formattedKiwiSaverPeriods = formatFundPeriods(scenarioData.kiwiSaver?.fund_periods);
      const formattedPartnerKiwiSaverPeriods = formatFundPeriods(scenarioData.kiwiSaver?.partner_fund_periods);

      const mainFundTypeData = scenarioData.investment?.fundType !== 'Custom' ? FUND_TYPES[scenarioData.investment?.fundType] : null;
      const investmentReturn = mainFundTypeData?.return ?? scenarioData.investment?.annual_investment_return;
      const investmentStdDev = mainFundTypeData?.stdDev ?? scenarioData.investment?.inv_std_dev;

      const { data: insertedData, error } = await supabase
        .from('scenarios_data1')
        .insert({
          scenario_name: scenarioData.name,
          household_id: parseInt(scenarioData.householdId),
          user_id: userId,
          name: scenarioData.personal.name,
          partner_name: scenarioData.personal.partner_name,
          starting_age: scenarioData.personal.starting_age,
          partner_starting_age: scenarioData.personal.partner_starting_age,
          ending_age: scenarioData.personal.ending_age,
          annual_income: scenarioData.income.annual_income,
          income_period: scenarioData.income.income_period,
          partner_annual_income: scenarioData.income.partner_annual_income,
          partner_income_period: scenarioData.income.partner_income_period,
          include_superannuation: scenarioData.income.include_superannuation,
          savings_amount: scenarioData.savings.savings_amount,
          cash_reserve: scenarioData.savings.cash_reserve,
          saving_percentage: scenarioData.savings.saving_percentage,
          annual_expenses1: scenarioData.expenditure.annual_expenses1,
          expense_period1: scenarioData.expenditure.expense_period1,
          annual_expenses2: scenarioData.expenditure.annual_expenses2,
          expense_period2: scenarioData.expenditure.expense_period2,
          initial_investment: scenarioData.investment.initial_investment,
          annual_investment_contribution: scenarioData.investment.annual_investment_contribution,
          annual_investment_return: investmentReturn,
          inv_std_dev: investmentStdDev,
          fund_type: scenarioData.investment.fundType,
          fund_periods: formattedInvestmentPeriods,
          initial_kiwisaver: scenarioData.kiwiSaver.initial_kiwisaver,
          kiwisaver_contribution: scenarioData.kiwiSaver.kiwisaver_contribution,
          employer_contribution: scenarioData.kiwiSaver.employer_contribution,
          ks_periods: formattedKiwiSaverPeriods,
          partner_ks_periods: formattedPartnerKiwiSaverPeriods,
          partner_initial_kiwisaver: scenarioData.kiwiSaver.partner_initial_kiwisaver,
          partner_kiwisaver_contribution: scenarioData.kiwiSaver.partner_kiwisaver_contribution,
          partner_employer_contribution: scenarioData.kiwiSaver.partner_employer_contribution,
          property_value: scenarioData.property.property_value,
          debt: scenarioData.property.debt,
          property_growth: scenarioData.property.property_growth,
          debt_ir: scenarioData.property.debt_ir,
          initial_debt_years: scenarioData.property.initial_debt_years,
          additional_debt_repayments: scenarioData.property.additional_debt_repayments,
          show_repayments: scenarioData.property.show_repayments,
          sell_main_property: scenarioData.property.sell_main_property,
          main_property_sale_age: scenarioData.property.main_property_sale_age,
          main_prop_sale_value: scenarioData.property.main_prop_sale_value,
          sale_allocate_to_investment: scenarioData.property.sale_allocate_to_investment,
          downsize: scenarioData.property.downsize,
          downsize_age: scenarioData.property.downsize_age,
          new_property_value: scenarioData.property.new_property_value,
          allocate_to_investment: scenarioData.property.allocate_to_investment,
          pay_off_debt: scenarioData.property.pay_off_debt,
          additional_properties: scenarioData.property.additional_properties,
          inflation_rate: scenarioData.economic.inflation_rate,
          simulations: scenarioData.monteCarlo.simulations,
          confidence_interval: scenarioData.monteCarlo.confidence_interval,
          created_at: new Date().toISOString(),
          last_edited_at: new Date().toISOString(),
          last_viewed_at: new Date().toISOString(),
          household_name: householdName
        })
        .select()
        .single();

      if (error) throw error;

      setIsSaving(false);
      setShowSuccessModal(true);
      setSavedScenarioId(insertedData.id);

    } catch (error) {
      console.error('Error saving scenario:', error);
      setIsSaving(false);
    }
  };

  const handleScenarioNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setScenarioData({ ...scenarioData, name: e.target.value });
  };

  return (
    <div className="flex flex-col h-[calc(100vh-50px)] mt-[50px] pl-0 pt-2 pr-2 pb-2">
      <Card className="flex-grow flex flex-col">
        <CardHeader className="border-b pb-2"> {/* Adjusted padding */}
          <Label htmlFor="scenarioName" className="text-xs text-muted-foreground">Scenario Name</Label>
          <Input
            id="scenarioName"
            value={scenarioData.name}
            onChange={handleScenarioNameChange}
            className="text-lg font-semibold p-0 border-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0"
            placeholder="Enter Scenario Name"
          />
        </CardHeader>
        {/* Use the new ScenarioBuilderTabs component */}
        <ScenarioBuilderTabs scenarioData={scenarioData} setScenarioData={setScenarioData} metrics={metrics} />
        {/* Save button remains at the bottom */}
        <div className="p-4 border-t mt-auto">
          <Button onClick={handleSave} disabled={isSaving || !validateMandatoryFields()} size="lg" className="w-full md:w-auto">
            {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Scenario'
              )}
            </Button>
          </div>
      </Card>

      {/* Modals remain the same */}
      {isSaving && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-background p-6 rounded-lg shadow-lg flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
            <p className="text-lg font-semibold">Saving Scenario</p>
          </div>
        </div>
      )}
      {showSuccessModal && savedScenarioId !== null && (
        <ScenarioSavedModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          scenarioName={scenarioData.name}
          scenarioId={savedScenarioId}
        />
      )}
    </div>
  );
}
