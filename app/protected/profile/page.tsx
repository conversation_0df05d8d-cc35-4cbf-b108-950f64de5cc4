'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

function ProfileContent() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new admin profile page
    router.push('/protected/admin/profile');
  }, [router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Redirecting to admin profile...</span>
    </div>
  );
}

// Main component that wraps ProfileContent
export default function ProfilePage() {
  return <ProfileContent />;
}
