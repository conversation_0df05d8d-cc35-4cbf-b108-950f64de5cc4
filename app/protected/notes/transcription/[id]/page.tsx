"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { use<PERSON>arams, useRouter } from 'next/navigation';
import { Loader2, ArrowLeft, Save, Maximize2, CheckSquare, Volume2, Video, Play, X } from 'lucide-react';
import dynamic from 'next/dynamic';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from 'sonner';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import ActionItemsModal from '@/components/modals/ActionItemsModal';
import MediaPlayer from '@/components/MediaPlayer';

// Import the editor with dynamic loading to avoid SSR issues
const NoteEditor = dynamic(() => import('@/components/tiptap/NoteEditor'), {
  ssr: false,
  loading: () => <div className="flex justify-center p-4"><Loader2 className="h-8 w-8 animate-spin" /></div>
});

interface Note {
  id: string;
  title: string;
  content: string;
  ai_content: string | null;
  household_id: string;
  household_name?: string;
  created_at: string;
  last_edited_at: string;
  note_type: string;
  action_items?: string[];
  completed_action_items?: string[];
  summary?: string;
  metadata?: {
    original_filename: any;
    media_file_path?: string;
    media_file_type?: string;
    transcription_status?: string;
    error_message?: string;
  };
}





const TranscriptionPage = () => {
  const [note, setNote] = useState<Note | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [aiContent, setAiContent] = useState<string | null>(null);
  const [isEditorMaximized, setIsEditorMaximized] = useState(false);
  const [selectedHousehold, setSelectedHousehold] = useState<string | null>(null);
  const [householdName, setHouseholdName] = useState<string>('');
  const [isShowingSummary, setIsShowingSummary] = useState(false);
  const [actionItems, setActionItems] = useState<string[]>([]);
  const [completedActionItems, setCompletedActionItems] = useState<string[]>([]);
  const [isActionItemsModalOpen, setIsActionItemsModalOpen] = useState(false);
  const [isMediaPlayerOpen, setIsMediaPlayerOpen] = useState(false);
  const params = useParams();
  const router = useRouter();
  const supabase = createClient();
  const noteId = params.id as string;

  useEffect(() => {
    const fetchHouseholdName = async () => {
      if (selectedHousehold) {
        const { data, error } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', selectedHousehold)
          .single();

        if (error) {
          console.error('Error fetching household name:', error);
        } else if (data) {
          setHouseholdName(data.householdName);
        }
      } else {
        setHouseholdName('');
      }
    };

    fetchHouseholdName();
  }, [selectedHousehold]);

  useEffect(() => {
    const fetchNote = async () => {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('No authenticated user found');
        setIsLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('notes')
        .select(`
          id,
          title,
          content,
          ai_content,
          household_id,
          created_at,
          last_edited_at,
          note_type,
          summary,
          action_items,
          completed_action_items,
          metadata
        `)
        .eq('id', noteId)
        .eq('note_type', 'transcription')
        .single();

      if (error) {
        console.error('Error fetching transcription:', error);
        setIsLoading(false);
        toast.error('Error loading transcription');
        router.push('/protected/notes');
        return;
      }

      if (data) {
        console.log('Fetched note data:', data);
        console.log('Media metadata:', data.metadata);

        setNote(data);
        setTitle(data.title);
        setContent(data.content);
        setAiContent(data.ai_content);
        // Always default to showing normal content, not summary
        setIsShowingSummary(false);
        setSelectedHousehold(data.household_id);
        setActionItems(data.action_items || []);
        setCompletedActionItems(data.completed_action_items || []);
      }

      setIsLoading(false);
    };

    if (noteId) {
      fetchNote();
    }
  }, [noteId, router]);



  const handleSave = async () => {
    if (!note) return;

    setIsSaving(true);

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('notes')
        .update({
          title,
          content,
          ai_content: aiContent,
          household_id: selectedHousehold === 'null' ? null : selectedHousehold,
          last_edited_at: new Date().toISOString(),
          action_items: actionItems,
          completed_action_items: completedActionItems
        })
        .eq('id', note.id);

      if (error) throw error;

      toast.success('Transcription saved successfully');
    } catch (error) {
      console.error('Error saving transcription:', error);
      toast.error('Failed to save transcription');
    } finally {
      setIsSaving(false);
    }
  };

  const handleGoBack = () => {
    router.push('/protected/notes');
  };

  const handleContentChange = (newContent: string) => {
    // Update the appropriate content based on which version is being shown
    if (isShowingSummary && aiContent) {
      setAiContent(newContent);
    } else {
      setContent(newContent);
    }
  };

  const handleSaveActionItems = (completed: string[], removed?: string[]) => {
    setCompletedActionItems(completed);

    // Remove items if provided
    if (removed && removed.length > 0) {
      setActionItems(prev => prev.filter(item => !removed.includes(item)));
    }

    handleSave();
    toast.success('Action items updated');
  };

  const toggleSummaryView = (value: boolean) => {
    // This only updates the local UI state and doesn't affect what's saved to the database
    setIsShowingSummary(value);
  };



  const handleMaximizeEditor = () => {
    setIsEditorMaximized(true);
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <div className="flex items-center gap-2 mb-2 px-4">
          <Button variant="ghost" size="sm" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Notes
          </Button>
          <div className="flex-1" />
          <Button
            onClick={() => setIsActionItemsModalOpen(true)}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <CheckSquare className="h-4 w-4" />
            Action Items {actionItems.length > 0 && `(${actionItems.length})`}
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="gap-2"
          >
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            Save
          </Button>
        </div>

        <div className="flex-1 min-h-0 pr-2 pl-0 pb-0 pt-2">
          <Card className="h-[calc(100vh-62px)] flex flex-col">
            <CardHeader className="flex-shrink-0 pb-2 flex flex-col space-y-4">
              <div className="flex flex-row items-center justify-between">
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Transcription Title"
                  className="text-xl font-bold border-none focus-visible:ring-0 px-0 flex-1 mr-2 w-[500px]"
                />
                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="summary-toggle"
                      checked={isShowingSummary}
                      onCheckedChange={toggleSummaryView}
                    />
                    <Label htmlFor="summary-toggle">Summary View</Label>
                  </div>

                  <Button
                    onClick={() => setIsActionItemsModalOpen(true)}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <CheckSquare className="h-4 w-4" />
                    Action Items {actionItems.length > 0 && `(${actionItems.length})`}
                  </Button>

                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Household:</span>
                    <span className="text-sm">{householdName || 'None'}</span>
                  </div>
                  <Button
                    onClick={handleSave}
                    disabled={isSaving}
                    size="sm"
                    className="gap-2"
                  >
                    {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                    Save
                  </Button>
                </div>
              </div>

              {/* Media Player Button */}
              {note?.metadata?.media_file_path && (
                <div className="mt-4 border rounded-md p-4 bg-muted/10">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {note.metadata.media_file_type?.startsWith('audio/') ? (
                        <Volume2 className="h-5 w-5 text-primary" />
                      ) : (
                        <Video className="h-5 w-5 text-primary" />
                      )}
                      <h3 className="font-medium">
                        {note.metadata.media_file_type?.startsWith('audio/') ? 'Audio' : 'Video'} Recording
                      </h3>
                      {note.metadata.original_filename && (
                        <span className="text-sm text-muted-foreground ml-2">
                          ({note.metadata.original_filename})
                        </span>
                      )}
                    </div>
                    <Button
                      onClick={() => setIsMediaPlayerOpen(!isMediaPlayerOpen)}
                      variant="outline"
                      size="sm"
                      className="gap-2"
                    >
                      {isMediaPlayerOpen ? (
                        <>
                          <X className="h-4 w-4" />
                          Hide Player
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4" />
                          {note.metadata.media_file_type?.startsWith('audio/') ? 'Play Audio' : 'Play Video'}
                        </>
                      )}
                    </Button>
                  </div>

                  {isMediaPlayerOpen && (
                    <div className="mt-2">
                      <MediaPlayer
                        filePath={note.metadata.media_file_path}
                        fileType={note.metadata.media_file_type || ''}
                        className="w-full max-w-full"
                      />
                    </div>
                  )}
                </div>
              )}
            </CardHeader>
            <CardContent className="flex-1 overflow-auto pt-0">
              {note && (
                <NoteEditor
                  content={isShowingSummary && aiContent ? aiContent : content}
                  onChange={handleContentChange}
                  onMaximize={handleMaximizeEditor}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <ActionItemsModal
        isOpen={isActionItemsModalOpen}
        onClose={() => setIsActionItemsModalOpen(false)}
        actionItems={actionItems}
        onSaveActionItems={handleSaveActionItems}
        householdId={note?.household_id ? Number(note.household_id) : undefined}
        noteId={note?.id}
      />

      <Dialog open={isEditorMaximized} onOpenChange={setIsEditorMaximized}>
        <DialogContent
          className="max-w-[100vw] w-[100vw] h-[100vh] p-0 m-0 rounded-none border-0"
        >
          <div className="h-full overflow-hidden">
            {note && (
              <NoteEditor
                content={isShowingSummary && aiContent ? aiContent : content}
                onChange={handleContentChange}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TranscriptionPage;
