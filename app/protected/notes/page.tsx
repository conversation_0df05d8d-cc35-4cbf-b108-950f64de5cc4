"use client";

import { useState, useEffect } from 'react';
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { NotesTable } from '@/components/tables/NotesTable';
import CreateNoteModal from '@/components/modals/CreateNoteModal';
import TranscriptionModal from '@/components/modals/TranscriptionModal';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface Note {
  id: string;
  title: string;
  household_id: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  user_id: string;
  created_by: string;
  note_type: string;
  summary: string | null;
}

// Add this interface for the user map
interface UserMap {
  [key: string]: string;
}

const NotesPage = () => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTranscriptionModalOpen, setIsTranscriptionModalOpen] = useState(false);
  const [userId, setUserId] = useState<string>('');
  const [orgId, setOrgId] = useState<string>('');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const fetchUserAndNotes = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserId(user.id);

        // Fetch user profile to get org_id
        const { data: profileData } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', user.id)
          .single();

        if (profileData?.org_id) {
          setOrgId(profileData.org_id);
        }

        fetchNotes();
      } else {
        setIsLoading(false);
      }
    };

    fetchUserAndNotes();
  }, []);

  const fetchNotes = async () => {
    setIsLoading(true);
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No authenticated user found');
      setIsLoading(false);
      return;
    }

    // First get the notes
    const { data: notesData, error: notesError } = await supabase
      .from('notes')
      .select('*')
      .eq('user_id', user.id)
      .order('last_edited_at', { ascending: false });

    if (notesError) {
      console.error('Error fetching notes:', notesError);
      setIsLoading(false);
      return;
    }

    // Get all user_ids to fetch names
    const userIds = [...new Set(notesData.map(note => note.user_id))];

    // Fetch user profiles
    const { data: profilesData } = await supabase
      .from('profiles')
      .select('user_id, name')
      .in('user_id', userIds);

    // Create a map of user_id to name with proper typing
    const userMap: UserMap = {};
    if (profilesData) {
      profilesData.forEach(profile => {
        userMap[profile.user_id] = profile.name || 'Unknown User';
      });
    }

    // Then get the household names for each note
    const formattedNotes: Note[] = [];

    for (const note of notesData) {
      if (note.household_id) {
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', note.household_id)
          .single();

        formattedNotes.push({
          id: note.id,
          title: note.title,
          household_id: note.household_id,
          household_name: householdData?.householdName || 'Unknown',
          created_at: note.created_at,
          last_edited_at: note.last_edited_at,
          user_id: note.user_id,
          created_by: userMap[note.user_id] || 'Unknown User',
          note_type: note.note_type || 'general',
          summary: note.summary
        });
      } else {
        formattedNotes.push({
          id: note.id,
          title: note.title,
          household_id: note.household_id,
          household_name: 'Unknown',
          created_at: note.created_at,
          last_edited_at: note.last_edited_at,
          user_id: note.user_id,
          created_by: userMap[note.user_id] || 'Unknown User',
          note_type: note.note_type || 'general',
          summary: note.summary
        });
      }
    }

    setNotes(formattedNotes);
    setIsLoading(false);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    fetchNotes();
  };

  const handleTranscriptionModalClose = () => {
    setIsTranscriptionModalOpen(false);
    fetchNotes();
  };

  const handleCreateNote = (noteId: string) => {
    router.push(`/protected/notes/note/${noteId}`);
  };

  const handleTranscriptionComplete = (noteId: string) => {
    toast.success('Transcription complete!');
    router.push(`/protected/notes/note/${noteId}`);
  };

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
          <Card className="h-full mt-[50px] flex flex-col">
            <CardContent className="flex-1 overflow-auto">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <NotesTable
                  data={notes}
                  onDataChange={fetchNotes}
                  onCreateNote={() => setIsModalOpen(true)}
                  onCreateTranscription={() => setIsTranscriptionModalOpen(true)}
                />
              )}
            </CardContent>
          </Card>
        </div>
      <CreateNoteModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onCreateNote={handleCreateNote}
        userId={userId}
        orgId={orgId}
      />
      <TranscriptionModal
        isOpen={isTranscriptionModalOpen}
        onClose={handleTranscriptionModalClose}
        onTranscriptionComplete={handleTranscriptionComplete}
        userId={userId}
        orgId={orgId}
      />
    </div>
  );
};

export default NotesPage;
