"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Loader2, ArrowLeft, Save, Wand2, CheckSquare, Maximize2 } from 'lucide-react';
import dynamic from 'next/dynamic';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import RefactorNoteModal from '@/components/modals/RefactorNoteModal';
import ActionItemsModal from '@/components/modals/ActionItemsModal';
import { toast } from 'sonner';
import { Dialog, DialogContent } from "@/components/ui/noteDialong";

// Import the editor with dynamic loading to avoid SSR issues
const NoteEditor = dynamic(() => import('@/components/tiptap/NoteEditor'), {
  ssr: false,
  loading: () => <div className="flex justify-center p-4"><Loader2 className="h-8 w-8 animate-spin" /></div>
});

interface Note {
  id: string;
  title: string;
  content: string;
  ai_content?: string;
  household_id: string;
  household_name?: string;
  created_at: string;
  last_edited_at: string;
  note_type: string;
  action_items?: string[];
  completed_action_items?: string[];
}

// Helper function to extract action items
const extractActionItems = (content: any) => {
  if (!content) return [];

  // Parse content if it's a string
  const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;

  const actionItems: string[] = [];
  let inActionItemsSection = false;

  // Function to recursively search for action items in the content
  const searchForActionItems = (node: any) => {
    // Check if this is a heading with "Action Items"
    if (node.type === 'heading' &&
        node.content &&
        node.content.some((child: any) =>
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = true;
      return;
    }

    // If we're in the action items section and this is a list item, extract it
    if (inActionItemsSection && node.type === 'listItem') {
      let itemText = '';

      // Extract text from the list item
      const extractText = (n: any) => {
        if (n.text) {
          itemText += n.text + ' ';
        }
        if (n.content && Array.isArray(n.content)) {
          n.content.forEach(extractText);
        }
      };

      extractText(node);
      if (itemText.trim()) {
        actionItems.push(itemText.trim());
      }
    }

    // If we find another heading after the action items section, stop collecting
    if (inActionItemsSection && node.type === 'heading' &&
        node.content &&
        !node.content.some((child: any) =>
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = false;
    }

    // Continue searching in child nodes
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(searchForActionItems);
    }
  };

  // Start the search from the top level
  if (parsedContent.content && Array.isArray(parsedContent.content)) {
    parsedContent.content.forEach(searchForActionItems);
  }

  return actionItems;
};

// Add this function to generate a summary
const generateSummary = async (content: string | null): Promise<string | null> => {
  if (!content) return null;

  try {
    const response = await fetch('/api/refactor-note', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content,
        refactorType: 'brief-summary'
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate summary');
    }

    const data = await response.json();

    // Extract text from the TipTap JSON structure
    let summaryText = '';
    const extractText = (node: any) => {
      if (node.text) {
        summaryText += node.text + ' ';
      }
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(extractText);
      }
    };

    if (data.refactoredContent && data.refactoredContent.content) {
      data.refactoredContent.content.forEach(extractText);
    }

    return summaryText.trim();
  } catch (error) {
    console.error('Error generating summary:', error);
    return null;
  }
};

const NotePage = () => {
  const [note, setNote] = useState<Note | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isRefactoring, setIsRefactoring] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [aiContent, setAiContent] = useState<string | null>(null);
  const [isShowingAi, setIsShowingAi] = useState(false);
  const [noteType, setNoteType] = useState('general');
  const [isRefactorModalOpen, setIsRefactorModalOpen] = useState(false);
  const [isActionItemsModalOpen, setIsActionItemsModalOpen] = useState(false);
  const [actionItems, setActionItems] = useState<string[]>([]);
  const [completedActionItems, setCompletedActionItems] = useState<string[]>([]);
  const [isEditorMaximized, setIsEditorMaximized] = useState(false);
  const [refactorType, setRefactorType] = useState('format');
  const params = useParams();
  const router = useRouter();
  const supabase = createClient();
  const noteId = params.id as string;

  useEffect(() => {
    const fetchNote = async () => {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('No authenticated user found');
        setIsLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('notes')
        .select(`
          id,
          title,
          content,
          ai_content,
          household_id,
          created_at,
          last_edited_at,
          note_type,
          action_items,
          completed_action_items,
          summary
        `)
        .eq('id', noteId)
        .single();

      if (error) {
        console.error('Error fetching note:', error);
        setIsLoading(false);
        return;
      }

      if (data) {
        setNote(data);
        setTitle(data.title);
        setContent(data.content);
        setAiContent(data.ai_content || '');
        // Always default to showing normal content, not AI content
        setIsShowingAi(false);
        setNoteType(data.note_type || 'general');
        setActionItems(data.action_items || []);
        setCompletedActionItems(data.completed_action_items || []);
      }

      setIsLoading(false);
    };

    if (noteId) {
      fetchNote();
    }
  }, [noteId]);

  // Add useEffect to extract action items when AI content changes
  useEffect(() => {
    if (aiContent) {
      const extractedItems = extractActionItems(aiContent);
      setActionItems(extractedItems);
    }
  }, [aiContent]);

  const handleSave = async () => {
    if (!note) return;

    setIsSaving(true);

    try {
      // Always generate summary from the content that's currently being shown
      const currentContent = isShowingAi ? aiContent : content;
      const summary = await generateSummary(currentContent);

      const supabase = createClient();
      const { error } = await supabase
        .from('notes')
        .update({
          title,
          // Always save both content versions separately
          content: content,
          last_edited_at: new Date().toISOString(),
          note_type: noteType,
          ai_content: aiContent,
          action_items: actionItems,
          completed_action_items: completedActionItems,
          summary: summary // Add the summary to the update
        })
        .eq('id', note.id);

      if (error) throw error;

      toast.success('Note saved successfully');
    } catch (error) {
      console.error('Error saving note:', error);
      toast.error('Failed to save note');
    }

    setIsSaving(false);
  };

  const handleBack = () => {
    router.push('/protected/notes');
  };

  const handleContentChange = (newContent: string) => {
    // Update the appropriate content based on which version is being shown
    if (isShowingAi && aiContent) {
      setAiContent(newContent);
    } else {
      setContent(newContent);
    }
  };

  const handleOpenRefactorModal = () => {
    setAiContent(null); // Reset AI content when opening modal
    setIsRefactorModalOpen(true);
  };

  const handleRefactor = async (type: string) => {
    if (!content) return;

    setIsRefactoring(true);

    try {
      const response = await fetch('/api/refactor-note', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          refactorType: type
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to refactor note');
      }

      const data = await response.json();

      // Make sure we're setting valid TipTap JSON content
      setAiContent(data.refactoredContent);
    } catch (error) {
      console.error('Error refactoring note:', error);
      toast.error('Failed to refactor note');
    }

    setIsRefactoring(false);
  };

  const handleRefactorTypeChange = (type: string) => {
    setRefactorType(type);
  };

  const handleAcceptRefactoring = () => {
    setIsShowingAi(true);
    setIsRefactorModalOpen(false);

    // Extract action items from the refactored content
    if (aiContent) {
      const extractedItems = extractActionItems(aiContent);
      setActionItems(extractedItems);
    }

    toast.success('Refactored note applied');
  };

  const handleSaveActionItems = (completed: string[], removed?: string[]) => {
    setCompletedActionItems(completed);

    // Remove items if provided
    if (removed && removed.length > 0) {
      setActionItems(prev => prev.filter(item => !removed.includes(item)));
    }

    handleSave();
    toast.success('Action items updated');
  };

  const toggleAiContent = (value: boolean) => {
    // This only updates the local UI state and doesn't affect what's saved to the database
    setIsShowingAi(value);
  };

  const handleMaximizeEditor = () => {
    setIsEditorMaximized(true);
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <div className="flex items-center gap-2 mb-2 px-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Notes
          </Button>
          <div className="flex-1" />
          {isShowingAi && actionItems.length > 0 && (
            <Button
              onClick={() => setIsActionItemsModalOpen(true)}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              <CheckSquare className="h-4 w-4" />
              Action Items ({actionItems.length})
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="gap-2"
          >
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            Save
          </Button>
        </div>

        <div className="flex-1 min-h-0 pr-2 pl-0 pb-0 pt-2">
          <Card className="h-[calc(100vh-62px)] flex flex-col">
            <CardHeader className="flex-shrink-0 pb-2 flex flex-row items-center justify-between">
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Note Title"
                className="text-xl font-bold border-none focus-visible:ring-0 px-0 flex-1 mr-2"
              />
              <div className="flex items-center gap-4">
                {aiContent && (
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="ai-toggle"
                      checked={isShowingAi}
                      onCheckedChange={toggleAiContent}
                    />
                    <Label htmlFor="ai-toggle">Enhanced Version</Label>
                  </div>
                )}

                {isShowingAi && actionItems.length > 0 && (
                  <Button
                    onClick={() => setIsActionItemsModalOpen(true)}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <CheckSquare className="h-4 w-4" />
                    Action Items ({actionItems.length})
                  </Button>
                )}

                <div className="w-32">
                  <Select onValueChange={setNoteType} value={noteType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="important">Important</SelectItem>
                      <SelectItem value="info">Information</SelectItem>
                      <SelectItem value="task">Task</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={handleOpenRefactorModal}
                >
                  <Wand2 className="h-4 w-4" /> Refactor
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  size="sm"
                  className="gap-2"
                >
                  {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                  Save
                </Button>
              </div>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto pt-0">
              {note && (
                <NoteEditor
                  content={isShowingAi && aiContent ? aiContent : content}
                  onChange={handleContentChange}
                  onMaximize={handleMaximizeEditor}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <RefactorNoteModal
        isOpen={isRefactorModalOpen}
        onClose={() => setIsRefactorModalOpen(false)}
        originalContent={content}
        refactoredContent={aiContent}
        onRefactor={handleRefactor}
        onAccept={handleAcceptRefactoring}
        isLoading={isRefactoring}
        refactorType={refactorType}
        onRefactorTypeChange={handleRefactorTypeChange}
      />

      <ActionItemsModal
        isOpen={isActionItemsModalOpen}
        onClose={() => setIsActionItemsModalOpen(false)}
        actionItems={actionItems}
        onSaveActionItems={handleSaveActionItems}
        householdId={note?.household_id ? Number(note.household_id) : undefined}
        noteId={note?.id}
      />

      <Dialog open={isEditorMaximized} onOpenChange={setIsEditorMaximized}>
        <DialogContent
          className="max-w-[100vw] w-[100vw] h-[100vh] p-0 m-0 rounded-none border-0"
          fullscreenEnabled={true}
        >
          <div className="h-full overflow-hidden">
            {note && (
              <NoteEditor
                content={isShowingAi && aiContent ? aiContent : content}
                onChange={handleContentChange}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NotePage;
