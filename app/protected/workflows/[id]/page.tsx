
'use client';

import { use<PERSON><PERSON>back, useRef, useState, useMemo, useEffect, useLayoutEffect } from 'react';
import { useParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';
import dagre from 'dagre';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Node,
  Edge,
  Connection,
  ReactFlowInstance,
  OnInit,
  Panel,
  useKeyPress,
  MarkerType,
  NodeChange,
  applyNodeChanges,
  EdgeChange,
  reconnectEdge,
  IsValidConnection
} from '@xyflow/react';
import { emitEdgeCreated, emitEdgeRemoved } from '@/lib/workflow-events';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Copy, LayoutGrid } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import '@xyflow/react/dist/style.css';
import TaskNode from '@/components/nodes/TaskNode';
import DecisionNode from '@/components/nodes/DecisionNode';
import EventNode from '@/components/nodes/EventNode';
import CalendarEventNode from '@/components/nodes/CalendarEventNode';
import DocumentEventNode from '@/components/nodes/DocumentEventNode';
import NotificationNode from '@/components/nodes/NotificationNode';
import EmailNode from '@/components/nodes/EmailNode';
import EndNode from '@/components/nodes/EndNode';
import StartNode from '@/components/nodes/StartNode';
import { PlaceholderNode } from '@/components/nodes/PlaceholderNode';
import { EXPAND_ALL_NODES_EVENT, MINIMIZE_ALL_NODES_EVENT } from '@/components/sidebars/WorkflowSidebar';
import { NodeBase } from '@xyflow/system';
import FloatingEdge from '@/components/edges/FloatingEdge';
// Sidebar is now handled by the layout

// Custom node types
const nodeTypes = {
  taskNode: TaskNode,
  decisionNode: DecisionNode,
  eventNode: EventNode,
  calendarEventNode: CalendarEventNode,
  documentEventNode: DocumentEventNode,
  notificationNode: NotificationNode,
  emailNode: EmailNode,
  endNode: EndNode,
  startNode: StartNode,
  placeholderNode: PlaceholderNode
};

// Custom edge types
const edgeTypes = {
  floating: FloatingEdge
};

// No initial delete function needed for empty canvas

// No initial nodes or edges needed

export default function WorkflowDetailPage() {
  const params = useParams();
  const workflowId = params.id as string;
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Function to automatically layout nodes using dagre
  const getLayoutedElements = (nodes: Node[], edges: Edge[], direction = 'LR') => {
    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));

    // Set graph direction and node spacing
    // Increase nodesep (vertical spacing) to prevent overlapping
    dagreGraph.setGraph({
      rankdir: direction,
      nodesep: 200,  // Increased vertical spacing between nodes
      ranksep: 250,  // Increased horizontal spacing between ranks
      marginx: 50,   // Add margin on x-axis
      marginy: 50,   // Add margin on y-axis
      acyclicer: 'greedy', // Help with cycles in the graph
      ranker: 'network-simplex' // Use network simplex for better layout
    });

    // Add nodes to the graph with their dimensions
    // Use different dimensions based on node type for better spacing
    const getNodeDimensions = (node: Node) => {
      let width = 300;
      let height = 200;

      // Adjust height based on node type to prevent overlaps
      if (node.type === 'eventNode') {
        height = 250;
      } else if (node.type === 'decisionNode') {
        height = 300;
      } else if (node.type === 'notificationNode') {
        height = 180;
      }

      return { width, height };
    };

    nodes.forEach((node) => {
      const { width, height } = getNodeDimensions(node);
      dagreGraph.setNode(node.id, { width, height });
    });

    // Add edges to the graph
    edges.forEach((edge) => {
      dagreGraph.setEdge(edge.source, edge.target);
    });

    // Calculate the layout
    dagre.layout(dagreGraph);

    // Get the positioned nodes
    const layoutedNodes = nodes.map((node) => {
      const nodeWithPosition = dagreGraph.node(node.id);
      const { width, height } = getNodeDimensions(node);
      return {
        ...node,
        position: {
          x: nodeWithPosition.x - width / 2, // Center the node horizontally
          y: nodeWithPosition.y - height / 2, // Center the node vertically
        },
      };
    });

    // Post-process to fix any remaining overlaps
    const processedNodes = fixRemainingOverlaps(layoutedNodes);

    return { nodes: processedNodes, edges };
  };

  // Helper function to detect and fix any remaining overlaps
  const fixRemainingOverlaps = (nodes: Node[]) => {
    const nodesCopy = [...nodes];

    // Define node dimensions based on type
    const getNodeDimensions = (node: Node) => {
      let width = 300;
      let height = 200;

      if (node.type === 'eventNode') {
        height = 250;
      } else if (node.type === 'decisionNode') {
        height = 300;
      } else if (node.type === 'notificationNode') {
        height = 180;
      }

      return { width, height };
    };

    // Check if two nodes intersect
    const nodesIntersect = (nodeA: Node, nodeB: Node) => {
      const dimA = getNodeDimensions(nodeA);
      const dimB = getNodeDimensions(nodeB);

      // Calculate boundaries for each node
      const aLeft = nodeA.position.x - dimA.width / 2;
      const aRight = nodeA.position.x + dimA.width / 2;
      const aTop = nodeA.position.y - dimA.height / 2;
      const aBottom = nodeA.position.y + dimA.height / 2;

      const bLeft = nodeB.position.x - dimB.width / 2;
      const bRight = nodeB.position.x + dimB.width / 2;
      const bTop = nodeB.position.y - dimB.height / 2;
      const bBottom = nodeB.position.y + dimB.height / 2;

      // Check for intersection
      const horizontalOverlap = aRight > bLeft && aLeft < bRight;
      const verticalOverlap = aBottom > bTop && aTop < bBottom;

      return horizontalOverlap && verticalOverlap;
    };

    // Group nodes by similar x positions (same rank)
    const groupNodesByRank = () => {
      // Sort nodes by x position (left to right)
      nodesCopy.sort((a, b) => a.position.x - b.position.x);

      const ranks: Node[][] = [];
      let currentRank: Node[] = [];
      let lastX = -Infinity;

      nodesCopy.forEach(node => {
        if (Math.abs(node.position.x - lastX) > 150) {
          if (currentRank.length > 0) {
            ranks.push(currentRank);
          }
          currentRank = [node];
        } else {
          currentRank.push(node);
        }
        lastX = node.position.x;
      });

      if (currentRank.length > 0) {
        ranks.push(currentRank);
      }

      return ranks;
    };

    // First pass: group nodes by rank and sort vertically
    const ranks = groupNodesByRank();

    // For each rank, sort nodes by y and ensure proper spacing
    ranks.forEach(rankNodes => {
      // Sort by y position
      rankNodes.sort((a, b) => a.position.y - b.position.y);

      // Ensure minimum vertical spacing using intersection detection
      for (let i = 1; i < rankNodes.length; i++) {
        const prevNode = rankNodes[i-1];
        const currentNode = rankNodes[i];

        // Check if nodes intersect
        if (nodesIntersect(prevNode, currentNode)) {
          // Calculate dimensions
          const prevDim = getNodeDimensions(prevNode);
          const currentDim = getNodeDimensions(currentNode);

          // Calculate minimum y position to avoid overlap
          const minY = prevNode.position.y + (prevDim.height / 2) + (currentDim.height / 2) + 50; // 50px extra padding

          // Adjust position if overlapping
          currentNode.position.y = Math.max(currentNode.position.y, minY);
        }
      }
    });

    // Second pass: check for any remaining intersections across all nodes
    let hasIntersections = true;
    let iterations = 0;
    const maxIterations = 5; // Prevent infinite loops

    while (hasIntersections && iterations < maxIterations) {
      hasIntersections = false;
      iterations++;

      // Check each node against all others
      for (let i = 0; i < nodesCopy.length; i++) {
        for (let j = i + 1; j < nodesCopy.length; j++) {
          const nodeA = nodesCopy[i];
          const nodeB = nodesCopy[j];

          if (nodesIntersect(nodeA, nodeB)) {
            hasIntersections = true;

            // Calculate dimensions
            const dimA = getNodeDimensions(nodeA);
            const dimB = getNodeDimensions(nodeB);

            // Determine which direction to move (prefer vertical separation)
            const xDist = Math.abs(nodeA.position.x - nodeB.position.x);
            const yDist = Math.abs(nodeA.position.y - nodeB.position.y);

            if (xDist < yDist) {
              // Move horizontally
              const xSeparation = (dimA.width / 2) + (dimB.width / 2) + 50;
              if (nodeA.position.x < nodeB.position.x) {
                nodeB.position.x = nodeA.position.x + xSeparation;
              } else {
                nodeA.position.x = nodeB.position.x + xSeparation;
              }
            } else {
              // Move vertically
              const ySeparation = (dimA.height / 2) + (dimB.height / 2) + 50;
              if (nodeA.position.y < nodeB.position.y) {
                nodeB.position.y = nodeA.position.y + ySeparation;
              } else {
                nodeA.position.y = nodeB.position.y + ySeparation;
              }
            }
          }
        }
      }
    }

    return nodesCopy;
  };

  // Force a resize event after the component mounts to ensure ReactFlow renders correctly
  useLayoutEffect(() => {
    const timer = setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
    return () => clearTimeout(timer);
  }, []);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  // Create a ref for the delete function to avoid dependency issues
  const onDeleteNodeRef = useRef<(nodeId: string) => void>(() => {});



  // Function to delete a node
  const onDeleteNode = useCallback((nodeId: string) => {
    onDeleteNodeRef.current(nodeId);
  }, []);

  // State to track if we're loading a saved workflow
  const [isLoadingSavedWorkflow, setIsLoadingSavedWorkflow] = useState(true);

  // Initialize with empty state first to avoid showing demo workflow
  // Use type assertions to avoid TypeScript errors
  // Note: There will be some TypeScript warnings about implicit 'any' types,
  // but these are less critical and don't affect functionality
  const [nodes, setNodes, onNodesChange] = useNodesState([]) as [any, any, any];
  const [edges, setEdges, onEdgesChange] = useEdgesState([]) as [any, any, any];

  // Load saved workflow data when the component mounts
  useEffect(() => {
    // Skip if we don't have the necessary dependencies
    if (!workflowId || !reactFlowInstance) {
      return;
    }

    // Flag to prevent multiple loads
    let isMounted = true;

    const loadSavedWorkflow = async () => {
      try {
        const supabase = createClient();

        // Fetch the workflow data from the database
        const { data, error } = await supabase
          .from('workflows')
          .select('flow_data')
          .eq('id', workflowId)
          .single();

        if (error) {
          console.error('Error loading workflow:', error);
          if (isMounted) setIsLoadingSavedWorkflow(false);
          return;
        }

        // If we have saved flow data, use it to restore the workflow
        if (data && isMounted) {
          // Handle case where flow_data might be null
          const flowData = data.flow_data || { nodes: [], edges: [] };

          // Make sure all nodes have the onDelete function and preserve their data
          if (flowData.nodes && Array.isArray(flowData.nodes)) {
            // First, create a map of node connections to help with decision nodes
            const nodeConnections = new Map();

            if (flowData.edges && Array.isArray(flowData.edges)) {
              flowData.edges.forEach((edge: any) => {
                if (!nodeConnections.has(edge.target)) {
                  nodeConnections.set(edge.target, []);
                }
                nodeConnections.get(edge.target).push({
                  source: edge.source,
                  sourceHandle: edge.sourceHandle
                });
              });
            }

            // Process nodes to ensure they have all required handlers
            const nodesWithHandlers = flowData.nodes.map((node: any) => {
              // Start with the basic node data
              const nodeWithHandlers = {
                ...node,
                data: {
                  ...node.data,
                  onDelete: onDeleteNode,
                  onChange: (field: string, value: any) => {
                  }
                }
              };

              // For decision nodes, ensure they have inputNodeId and inputNodeType
              if (node.type === 'decisionNode') {
                // Find the input node for this decision node
                const connections = nodeConnections.get(node.id) || [];
                if (connections.length > 0) {
                  const inputNodeId = connections[0].source;
                  const inputNode = flowData.nodes.find((n: any) => n.id === inputNodeId);

                  if (inputNode) {
                    // Store the input node information in the decision node data
                    nodeWithHandlers.data.inputNodeId = inputNodeId;
                    nodeWithHandlers.data.inputNodeType = inputNode.type;

                    // If we don't have path conditions yet, generate them based on the input node
                    if (!node.data.pathConditions || !Array.isArray(node.data.pathConditions) || node.data.pathConditions.length === 0) {
                      if (inputNode.type === 'taskNode' && Array.isArray(inputNode.data.outcomes)) {
                        // Generate path conditions from task outcomes
                        nodeWithHandlers.data.pathConditions = inputNode.data.outcomes.map((outcome: any) => ({
                          id: outcome.id,
                          label: `If ${outcome.label}`,
                          color: outcome.color,
                          description: `Path for ${outcome.label}`
                        }));
                      } else if (inputNode.type === 'eventNode') {
                        // Use event-specific conditions
                        nodeWithHandlers.data.pathConditions = [
                          { id: 'completed', label: 'If Completed', color: 'green', description: 'Path for Event Completion' },
                          { id: 'cancelled', label: 'If Cancelled', color: 'red', description: 'Path for Event Cancellation' },
                          { id: 'rescheduled', label: 'If Rescheduled', color: 'yellow', description: 'Path for Event Rescheduling' }
                        ];
                      }
                    }
                  }
                }
              }

              return nodeWithHandlers;
            });

            // Set nodes and edges in a single batch to avoid multiple re-renders
            setNodes(nodesWithHandlers);
            if (flowData.edges) {
              setEdges(flowData.edges);
            }

            // Fit the view to show all nodes after a delay to ensure rendering is complete
            setTimeout(() => {
              if (reactFlowInstance && isMounted) {
                reactFlowInstance.fitView({ padding: 0.2 });
              }
            }, 200);
          }
        }
      } catch (error) {
        console.error('Error loading saved workflow:', error);
      } finally {
        if (isMounted) setIsLoadingSavedWorkflow(false);
      }
    };

    // Load the saved workflow
    loadSavedWorkflow();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [reactFlowInstance, workflowId, setNodes, setEdges, onDeleteNode]);

  // We don't need to update nodes with the delete function on initial load anymore
  // since we're starting with an empty canvas

  // Function to validate connections based on node types
  const isValidConnection = useCallback((params: Edge | Connection) => {
    // Handle both Edge and Connection types
    const source = params.source;
    const target = params.target;

    const sourceNode = nodes.find((node: { id: string; }) => node.id === source);
    const targetNode = nodes.find((node: { id: string; }) => node.id === target);

    if (!sourceNode || !targetNode) return false;

    // Connection rules based on node types
    const sourceType = sourceNode.type;
    const targetType = targetNode.type;

    // Rule 1: Start nodes can connect to task nodes, event nodes, calendar event nodes, and document event nodes
    if (sourceType === 'startNode') {
      return targetType === 'taskNode' || targetType === 'eventNode' ||
             targetType === 'calendarEventNode' || targetType === 'documentEventNode';
    }

    // Rule 2: Task nodes can connect to decision nodes and end nodes
    if (sourceType === 'taskNode') {
      return targetType === 'decisionNode' || targetType === 'endNode';
    }

    // Rule 3: Decision nodes can connect to task nodes, event nodes, calendar event nodes, document event nodes, and end nodes
    if (sourceType === 'decisionNode') {
      return targetType === 'taskNode' || targetType === 'eventNode' ||
             targetType === 'calendarEventNode' || targetType === 'documentEventNode' ||
             targetType === 'endNode';
    }

    // Rule 4: Event nodes can only connect to decision nodes
    if (sourceType === 'eventNode') {
      return targetType === 'decisionNode';
    }

    // Rule 5: Calendar event nodes can only connect to decision nodes
    if (sourceType === 'calendarEventNode') {
      return targetType === 'decisionNode';
    }

    // Rule 6: Document event nodes can only connect to decision nodes
    if (sourceType === 'documentEventNode') {
      return targetType === 'decisionNode';
    }

    // Rule 7: End nodes can't connect to anything
    if (sourceType === 'endNode') {
      return false;
    }

    // Rule 8: Notification nodes can only connect to task, event, calendar event, document event, and end nodes
    if (sourceType === 'notificationNode') {
      return targetType === 'taskNode' || targetType === 'eventNode' ||
             targetType === 'calendarEventNode' || targetType === 'documentEventNode' ||
             targetType === 'endNode';
    }

    // Default: don't allow connection
    return false;
  }, [nodes]);

  // Custom edge change handler to detect edge removals
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    // Check for edge removals
    const removedEdges = changes
      .filter(change => change.type === 'remove')
      .map(change => {
        // Safely access the id property based on the change type
        const edgeId = change.type === 'remove' ? change.id : undefined;
        return edgeId ? edges.find((edge: { id: string; }) => edge.id === edgeId) : undefined;
      })
      .filter(Boolean) as Edge[];

    // Process edge changes
    onEdgesChange(changes);

    // Emit edge removed events
    removedEdges.forEach(edge => {
      if (edge.source && edge.target) {
        emitEdgeRemoved({
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle || undefined,
          targetHandle: edge.targetHandle || undefined
        });
      }
    });
  }, [edges, onEdgesChange]);

  // Handle edge reconnection
  const onReconnect = useCallback((oldEdge: Edge, newConnection: Connection) => {

    // Update the edges with the reconnected edge
    setEdges((eds: Edge[]) => reconnectEdge(oldEdge, newConnection, eds));

    // Emit events for the reconnection
    setTimeout(() => {
      // First emit a removal of the old connection
      emitEdgeRemoved({
        source: oldEdge.source,
        target: oldEdge.target,
        sourceHandle: oldEdge.sourceHandle || undefined,
        targetHandle: oldEdge.targetHandle || undefined
      });

      // Then emit creation of the new connection
      emitEdgeCreated({
        source: newConnection.source || '',
        target: newConnection.target || '',
        sourceHandle: newConnection.sourceHandle || undefined,
        targetHandle: newConnection.targetHandle || undefined
      });
    }, 0);
  }, [setEdges, isValidConnection]);

  // Function to update notification connection points visibility
  const updateNotificationHandles = useCallback(() => {
    // Find all notification nodes
    const notificationNodes = nodes.filter((node: { type: string; }) => node.type === 'notificationNode');

    // For each notification node, find connected nodes and update handle visibility
    notificationNodes.forEach((notificationNode: { id: any; position: { y: any; }; }) => {
      // Find edges connected to this notification node
      const connectedEdges = edges.filter((edge: { source: any; }) => edge.source === notificationNode.id);

      connectedEdges.forEach((edge: { target: any; }) => {
        const targetNode = nodes.find((node: { id: any; }) => node.id === edge.target);
        if (!targetNode) return;

        // Get DOM elements for the handles
        const targetTopHandle = document.querySelector(`[data-nodeid="${targetNode.id}"] [data-handleid="notification-top"]`);
        const targetBottomHandle = document.querySelector(`[data-nodeid="${targetNode.id}"] [data-handleid="notification-bottom"]`);

        if (targetTopHandle && targetBottomHandle) {
          // Determine which handle to show based on relative positions
          const notificationY = notificationNode.position.y;
          const targetY = targetNode.position.y;

          if (notificationY < targetY) {
            // Notification is above target, show top handle
            (targetTopHandle as HTMLElement).style.visibility = 'visible';
            (targetBottomHandle as HTMLElement).style.visibility = 'hidden';
          } else {
            // Notification is below target, show bottom handle
            (targetTopHandle as HTMLElement).style.visibility = 'hidden';
            (targetBottomHandle as HTMLElement).style.visibility = 'visible';
          }
        }
      });
    });
  }, [nodes, edges]);

  // Override the default onNodesChange to update notification handles after node movement
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    // Apply changes without modifying node data to avoid infinite loops
    setNodes((nds: NodeBase[]) => applyNodeChanges(changes, nds));

    // Check if any of the changes are position changes
    const hasPositionChanges = changes.some(change =>
      change.type === 'position' || change.type === 'dimensions'
    );

    // If positions changed, update notification handles after a short delay
    if (hasPositionChanges) {
      setTimeout(() => {
        updateNotificationHandles();
      }, 50);
    }
  }, [setNodes, updateNotificationHandles]);

  // Note: Auto-layout functionality has been removed from the UI
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [nodeCounter, setNodeCounter] = useState(3); // Start after initial nodes

  // Keyboard shortcuts
  const deletePressed = useKeyPress('Delete');
  const backspacePressed = useKeyPress('Backspace');

  // Memoize nodeTypes and edgeTypes to prevent unnecessary re-renders
  const memoizedNodeTypes = useMemo(() => nodeTypes, []);
  const memoizedEdgeTypes = useMemo(() => edgeTypes, []);

  // Delete selected nodes
  const onDeleteNodes = useCallback(() => {
    if (selectedNodes.length === 0) return;

    // Create node remove changes for each selected node
    const nodesToRemove = selectedNodes.map(id => ({
      id,
      type: 'remove' as const,
    }));

    // Apply the changes
    onNodesChange(nodesToRemove);

    // Clear selection
    setSelectedNodes([]);
  }, [selectedNodes, onNodesChange]);

  // Handle keyboard shortcuts
  useEffect(() => {
    if ((deletePressed || backspacePressed) && selectedNodes.length > 0) {
      onDeleteNodes();
    }
  }, [deletePressed, backspacePressed, selectedNodes, onDeleteNodes]);

  // Update notification handles when nodes or edges change - with debounce
  useEffect(() => {
    // Skip during initial loading
    if (isLoadingSavedWorkflow) {
      return;
    }

    // Use a small delay to ensure the DOM has been updated and debounce frequent updates
    const timer = setTimeout(() => {
      updateNotificationHandles();
    }, 200);
    return () => clearTimeout(timer);
  }, [nodes, edges, updateNotificationHandles, isLoadingSavedWorkflow]);

  // Add event listeners for expand/minimize all nodes
  useEffect(() => {
    // Function to expand all nodes
    const handleExpandAllNodes = () => {
      setNodes((nds: any[]) => {
        // Only update nodes that need to change to avoid unnecessary re-renders
        return nds.map((node: { data: { isExpanded: boolean; }; }) => {
          if (node.data.isExpanded === true) {
            return node; // Skip nodes that are already expanded
          }
          return {
            ...node,
            data: {
              ...node.data,
              isExpanded: true
            }
          };
        });
      });
    };

    // Function to minimize all nodes
    const handleMinimizeAllNodes = () => {
      setNodes((nds: any[]) => {
        // Only update nodes that need to change to avoid unnecessary re-renders
        return nds.map((node: { data: { isExpanded: boolean; }; }) => {
          if (node.data.isExpanded === false) {
            return node; // Skip nodes that are already minimized
          }
          return {
            ...node,
            data: {
              ...node.data,
              isExpanded: false
            }
          };
        });
      });
    };

    // Add event listeners
    window.addEventListener(EXPAND_ALL_NODES_EVENT, handleExpandAllNodes);
    window.addEventListener(MINIMIZE_ALL_NODES_EVENT, handleMinimizeAllNodes);

    // Clean up event listeners
    return () => {
      window.removeEventListener(EXPAND_ALL_NODES_EVENT, handleExpandAllNodes);
      window.removeEventListener(MINIMIZE_ALL_NODES_EVENT, handleMinimizeAllNodes);
    };
  }, [setNodes, onDeleteNode]);



  const onConnect = useCallback((connection: Connection) => {
    // Get source and target nodes
    const sourceNode = nodes.find((node: { id: string; }) => node.id === connection.source);
    const targetNode = nodes.find((node: { id: string; }) => node.id === connection.target);

    if (!sourceNode || !targetNode) return;

    // Prepare for edge creation

    // Special handling for notification nodes
    if (sourceNode.type === 'notificationNode' || targetNode.type === 'notificationNode') {
      // If connecting from a notification node to another node
      if (sourceNode.type === 'notificationNode') {
        // Determine if the notification should connect from top or bottom
        const sourceY = sourceNode.position.y;
        const targetY = targetNode.position.y;

        // If target is above the notification, use the top handle
        if (targetY < sourceY) {
          connection.sourceHandle = 'source-top';
        } else {
          // Otherwise use the bottom handle
          connection.sourceHandle = 'source-bottom';
        }
      }

      // If connecting to a notification node
      if (targetNode.type === 'notificationNode') {
        // This is not allowed in the current design
        // Notification nodes can only be sources, not targets
        return;
      }
    }

    // If connecting to a node that can have notification connections
    if (connection.source && nodes.find((n: { id: string; }) => n.id === connection.source)?.type === 'notificationNode') {
      const sourceNode = nodes.find((n: { id: string; }) => n.id === connection.source);
      const targetNode = nodes.find((n: { id: string; }) => n.id === connection.target);

      if (sourceNode && targetNode) {
        // Determine if notification should connect to top or bottom of target
        const sourceY = sourceNode.position.y;
        const targetY = targetNode.position.y;

        // If notification is above the target, use the top handle
        if (sourceY < targetY) {
          connection.targetHandle = 'notification-top';
        } else {
          // Otherwise use the bottom handle
          connection.targetHandle = 'notification-bottom';
        }
      }
    }

    // Create edge with better routing
    const newEdge = {
      ...connection,
      type: 'smoothstep', // Use smoothstep for better curves
      animated: true,     // Add animation to edges
      style: { stroke: '#555', strokeWidth: 2 }, // Better styling
      markerEnd: MarkerType.Arrow
    };

    const updatedEdges = addEdge(newEdge, edges);
    setEdges(updatedEdges);

    // Emit edge created event after the edge is added
    setTimeout(() => {
      emitEdgeCreated({
        source: connection.source || '',
        target: connection.target || '',
        sourceHandle: connection.sourceHandle || undefined,
        targetHandle: connection.targetHandle || undefined
      });
    }, 0);
  }, [setEdges, nodes, edges]);

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onInit: OnInit = useCallback((instance: ReactFlowInstance) => {
    setReactFlowInstance(instance);

    // Initialize notification handles after a short delay
    setTimeout(() => {
      updateNotificationHandles();
    }, 300);
  }, [updateNotificationHandles]);

  // Track selected nodes
  const onSelectionChange = useCallback(({ nodes }: { nodes: Node[] }) => {
    setSelectedNodes(nodes.map((node: Node) => node.id));
  }, []);

  // Update the onDeleteNode function implementation
  useEffect(() => {
    onDeleteNodeRef.current = (nodeId: string) => {
      console.log("onDeleteNode called with nodeId:", nodeId);

      // Get the node being deleted
      const nodeToDelete = nodes.find((node: { id: string; }) => node.id === nodeId);
      console.log("Node to delete:", nodeToDelete);

      // If it's a notification node, trigger the notification-node-deleted event
      if (nodeToDelete && nodeToDelete.type === 'notificationNode' && nodeToDelete.data && nodeToDelete.data.parentNodeId) {
        console.log("Notification node being deleted, parent:", nodeToDelete.data.parentNodeId);
        // Dispatch event to notify the parent node that the notification was deleted
        window.dispatchEvent(new CustomEvent('notification-node-deleted', {
          detail: { parentNodeId: nodeToDelete.data.parentNodeId }
        }));
      }

      // Remove the node
      setNodes((nds: any[]) => {
        console.log("Filtering nodes to remove:", nodeId);
        return nds.filter((node: { id: string; }) => node.id !== nodeId);
      });

      // Also remove any connected edges
      setEdges((eds: any[]) => {
        console.log("Filtering edges connected to:", nodeId);
        return eds.filter((edge: { source: string; target: string; }) => edge.source !== nodeId && edge.target !== nodeId);
      });
    };
  }, [setNodes, setEdges, nodes]);

  // Add a new node at the center of the viewport
  const onAddNode = useCallback((nodeType = 'taskNode', nodeData = {}) => {
    if (!reactFlowInstance) return;

    // Define node dimensions based on type
    const getNodeDimensions = (node: { type?: string }) => {
      let width = 300;
      let height = 200;

      if (node.type === 'eventNode') {
        height = 250;
      } else if (node.type === 'decisionNode') {
        height = 300;
      } else if (node.type === 'notificationNode') {
        height = 180;
      } else if (node.type === 'emailNode') {
        height = 350; // Email node is taller due to more fields
      }

      return { width, height };
    };

    // Check if two nodes intersect with a safety margin
    const nodesIntersect = (nodeA: any, nodeB: Node) => {
      const dimA = getNodeDimensions(nodeA);
      const dimB = getNodeDimensions(nodeB);

      // Add a safety margin to ensure nodes don't overlap
      const safetyMargin = 50;

      // Calculate boundaries for each node with safety margin
      const aLeft = nodeA.position.x - (dimA.width / 2) - safetyMargin;
      const aRight = nodeA.position.x + (dimA.width / 2) + safetyMargin;
      const aTop = nodeA.position.y - (dimA.height / 2) - safetyMargin;
      const aBottom = nodeA.position.y + (dimA.height / 2) + safetyMargin;

      const bLeft = nodeB.position.x - (dimB.width / 2) - safetyMargin;
      const bRight = nodeB.position.x + (dimB.width / 2) + safetyMargin;
      const bTop = nodeB.position.y - (dimB.height / 2) - safetyMargin;
      const bBottom = nodeB.position.y + (dimB.height / 2) + safetyMargin;

      // Check for intersection
      const horizontalOverlap = aRight > bLeft && aLeft < bRight;
      const verticalOverlap = aBottom > bTop && aTop < bBottom;

      return horizontalOverlap && verticalOverlap;
    };

    // Get the center position of the current viewport
    const { x, y, zoom } = reactFlowInstance.getViewport();
    const centerX = x + (reactFlowWrapper.current?.clientWidth || 0) / 2 / zoom - 150;
    const centerY = y + (reactFlowWrapper.current?.clientHeight || 0) / 2 / zoom - 100;

    // Generate a unique ID using timestamp and random number to ensure uniqueness
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 10000);
    const newNodeId = `node-${timestamp}-${randomSuffix}`;

    console.log("Generated unique node ID:", newNodeId);

    // Create a temporary node to check for intersections
    const tempNode = {
      id: newNodeId,
      type: nodeType,
      position: { x: centerX, y: centerY },
    };

    // Find a position that doesn't overlap with existing nodes
    let foundPosition = false;
    const maxAttempts = 30; // Increase max attempts to find a good position

    // Try to find a non-overlapping position
    for (let attempts = 0; attempts < maxAttempts && !foundPosition; attempts++) {
      foundPosition = true;

      // Check against all existing nodes
      for (const node of nodes) {
        if (nodesIntersect(tempNode, node)) {
          foundPosition = false;

          // Try different positions with increasing randomness and distance
          const randomFactor = Math.min(1, attempts / 10) * 800; // Increase randomness with attempts
          const distanceFactor = 200 + (attempts * 50); // Increase distance with each attempt

          // Try a new position with some randomness and increasing distance
          if (attempts < 10) {
            // First try positions to the right and below
            tempNode.position.x = centerX + distanceFactor + (Math.random() * 100);
            tempNode.position.y = centerY + (Math.random() * 200);
          } else if (attempts < 20) {
            // Then try positions in all directions with more randomness
            tempNode.position.x = centerX + (Math.random() - 0.5) * randomFactor;
            tempNode.position.y = centerY + (Math.random() - 0.5) * randomFactor;
          } else {
            // Finally try positions far away from the center
            tempNode.position.x = centerX + (Math.random() > 0.5 ? 1 : -1) * (800 + Math.random() * 400);
            tempNode.position.y = centerY + (Math.random() > 0.5 ? 1 : -1) * (400 + Math.random() * 200);
          }
          break;
        }
      }
    }

    // If we still couldn't find a non-overlapping position, place it far away in a guaranteed new position
    if (!foundPosition) {
      // Place it far to the right of all existing nodes
      const rightmostNode = nodes.reduce((furthest: number, node: { position: { x: number; }; }) => {
        return node.position.x > furthest ? node.position.x : furthest;
      }, centerX);

      tempNode.position.x = rightmostNode + 600;
      tempNode.position.y = centerY + (Math.random() - 0.5) * 400;
    }

    // Increment the node counter by a larger value to avoid ID conflicts
    setNodeCounter(prev => prev + 100);

    const defaultData = {
      title: 'New Task',
      importance: 'medium',
      status: 'not started',
      nextAction: 'complete',
      onDelete: onDeleteNode,
      id: newNodeId
    };

    const newNode = {
      id: newNodeId,
      type: nodeType,
      position: { x: tempNode.position.x, y: tempNode.position.y },
      data: { ...defaultData, ...nodeData },
    };

    console.log("Adding new node:", newNode);
    console.log("Current nodes before adding:", nodes);

    // Use a callback to ensure we're working with the latest state
    // IMPORTANT: This must ONLY add the new node without replacing existing ones
    setNodes((nds: any[]) => {
      console.log("Nodes in callback:", nds);
      // Create a new array with all existing nodes plus the new one
      const updatedNodes = [...nds, newNode];
      console.log("Updated nodes after adding:", updatedNodes);
      return updatedNodes;
    });

    return newNodeId;
  }, [reactFlowInstance, setNodes, nodeCounter, nodes, onDeleteNode]);



  // Duplicate selected nodes
  const onDuplicateNodes = useCallback(() => {
    if (selectedNodes.length === 0 || !reactFlowInstance) return;

    // Define node dimensions based on type
    const getNodeDimensions = (node: Node) => {
      let width = 300;
      let height = 200;

      if (node.type === 'eventNode') {
        height = 250;
      } else if (node.type === 'decisionNode') {
        height = 300;
      } else if (node.type === 'notificationNode') {
        height = 180;
      } else if (node.type === 'emailNode') {
        height = 350; // Email node is taller due to more fields
      }

      return { width, height };
    };

    // Check if two nodes intersect with a safety margin
    const nodesIntersect = (nodeA: Node, nodeB: Node) => {
      const dimA = getNodeDimensions(nodeA);
      const dimB = getNodeDimensions(nodeB);

      // Add a safety margin to ensure nodes don't overlap
      const safetyMargin = 50;

      // Calculate boundaries for each node with safety margin
      const aLeft = nodeA.position.x - (dimA.width / 2) - safetyMargin;
      const aRight = nodeA.position.x + (dimA.width / 2) + safetyMargin;
      const aTop = nodeA.position.y - (dimA.height / 2) - safetyMargin;
      const aBottom = nodeA.position.y + (dimA.height / 2) + safetyMargin;

      const bLeft = nodeB.position.x - (dimB.width / 2) - safetyMargin;
      const bRight = nodeB.position.x + (dimB.width / 2) + safetyMargin;
      const bTop = nodeB.position.y - (dimB.height / 2) - safetyMargin;
      const bBottom = nodeB.position.y + (dimB.height / 2) + safetyMargin;

      // Check for intersection
      const horizontalOverlap = aRight > bLeft && aLeft < bRight;
      const verticalOverlap = aBottom > bTop && aTop < bBottom;

      return horizontalOverlap && verticalOverlap;
    };

    const selectedNodeObjects = nodes.filter((node: { id: string; }) => selectedNodes.includes(node.id));
    const existingNodes = nodes.filter((node: { id: string; }) => !selectedNodes.includes(node.id));

    // Create duplicated nodes one by one
    const newNodes: Node[] = [];

    // Process each selected node
    selectedNodeObjects.forEach((node: { position: { x: number; y: number; }; data: any; }, index: number) => {
      // Generate a unique ID using timestamp and random number to ensure uniqueness
      const timestamp = Date.now();
      const randomSuffix = Math.floor(Math.random() * 10000) + index; // Add index to ensure uniqueness within the loop
      const newNodeId = `node-${timestamp}-${randomSuffix}`;

      console.log("Generated unique node ID for duplicate:", newNodeId);

      // Create a duplicate node with initial offset position
      const duplicateNode = {
        ...node,
        id: newNodeId,
        position: {
          x: node.position.x + 300, // Initial offset to the right
          y: node.position.y,
        },
        data: {
          ...node.data,
          onDelete: onDeleteNode,
          id: newNodeId
        },
        selected: false
      };

      // Try to find a non-overlapping position
      let foundPosition = false;
      const maxAttempts = 30; // Increase max attempts to find a good position

      for (let attempts = 0; attempts < maxAttempts && !foundPosition; attempts++) {
        foundPosition = true;

        // Check against all existing nodes and previously created duplicates
        const nodesToCheck = [...existingNodes, ...newNodes];

        for (const otherNode of nodesToCheck) {
          if (nodesIntersect(duplicateNode, otherNode)) {
            foundPosition = false;

            // Try different positions with increasing randomness and distance
            const randomFactor = Math.min(1, attempts / 10) * 800; // Increase randomness with attempts
            const distanceFactor = 200 + (attempts * 50); // Increase distance with each attempt

            // Try a new position with some randomness and increasing distance
            if (attempts < 10) {
              // First try positions to the right and below
              duplicateNode.position.x = node.position.x + distanceFactor + (Math.random() * 100);
              duplicateNode.position.y = node.position.y + (Math.random() * 200);
            } else if (attempts < 20) {
              // Then try positions in all directions with more randomness
              duplicateNode.position.x = node.position.x + (Math.random() - 0.5) * randomFactor;
              duplicateNode.position.y = node.position.y + (Math.random() - 0.5) * randomFactor;
            } else {
              // Finally try positions far away from the original
              duplicateNode.position.x = node.position.x + (Math.random() > 0.5 ? 1 : -1) * (800 + Math.random() * 400);
              duplicateNode.position.y = node.position.y + (Math.random() > 0.5 ? 1 : -1) * (400 + Math.random() * 200);
            }
            break;
          }
        }
      }

      // If we still couldn't find a non-overlapping position, place it far away in a guaranteed new position
      if (!foundPosition) {
        // Find the rightmost node among all nodes
        const rightmostNode = [...existingNodes, ...newNodes].reduce((furthest: number, n: any) => {
          return n.position.x > furthest ? n.position.x : furthest;
        }, node.position.x);

        duplicateNode.position.x = rightmostNode + 600;
        duplicateNode.position.y = node.position.y + (Math.random() - 0.5) * 400;
      }

      // Add the new node to our array
      newNodes.push(duplicateNode);
    });

    // Increment the node counter by a larger value to avoid ID conflicts
    setNodeCounter(prev => prev + (selectedNodeObjects.length * 100));
    setNodes((nds: any) => [...nds, ...newNodes]);
    setSelectedNodes([]);
  }, [selectedNodes, nodes, nodeCounter, reactFlowInstance, setNodes, onDeleteNode]);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      if (!reactFlowWrapper.current || !reactFlowInstance) return;

      // Define node dimensions based on type
      const getNodeDimensions = (node: { type?: string }) => {
        let width = 300;
        let height = 200;

        if (node.type === 'eventNode') {
          height = 250;
        } else if (node.type === 'decisionNode') {
          height = 300;
        } else if (node.type === 'notificationNode') {
          height = 180;
        } else if (node.type === 'emailNode') {
          height = 350; // Email node is taller due to more fields
        }

        return { width, height };
      };

      // Check if two nodes intersect with a safety margin
      const nodesIntersect = (nodeA: any, nodeB: Node) => {
        const dimA = getNodeDimensions(nodeA);
        const dimB = getNodeDimensions(nodeB);

        // Add a safety margin to ensure nodes don't overlap
        const safetyMargin = 50;

        // Calculate boundaries for each node with safety margin
        const aLeft = nodeA.position.x - (dimA.width / 2) - safetyMargin;
        const aRight = nodeA.position.x + (dimA.width / 2) + safetyMargin;
        const aTop = nodeA.position.y - (dimA.height / 2) - safetyMargin;
        const aBottom = nodeA.position.y + (dimA.height / 2) + safetyMargin;

        const bLeft = nodeB.position.x - (dimB.width / 2) - safetyMargin;
        const bRight = nodeB.position.x + (dimB.width / 2) + safetyMargin;
        const bTop = nodeB.position.y - (dimB.height / 2) - safetyMargin;
        const bBottom = nodeB.position.y + (dimB.height / 2) + safetyMargin;

        // Check for intersection
        const horizontalOverlap = aRight > bLeft && aLeft < bRight;
        const verticalOverlap = aBottom > bTop && aTop < bBottom;

        return horizontalOverlap && verticalOverlap;
      };

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();

      // Get position relative to the canvas
      const initialPosition = reactFlowInstance.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      // Get node type and data from drag event if available
      let nodeType = 'taskNode';
      let nodeData = {};

      try {
        const reactFlowType = event.dataTransfer.getData('application/reactflow');
        if (reactFlowType) {
          nodeType = reactFlowType;
        }

        const jsonData = event.dataTransfer.getData('application/json');
        if (jsonData) {
          nodeData = JSON.parse(jsonData);
        }
      } catch (error) {
        console.error('Error parsing drag data:', error);
      }

      // Generate a unique ID using timestamp and random number to ensure uniqueness
      const timestamp = Date.now();
      const randomSuffix = Math.floor(Math.random() * 10000);
      const newNodeId = `node-${timestamp}-${randomSuffix}`;

      console.log("Generated unique node ID for drop:", newNodeId);

      // Create a temporary node to check for intersections
      const tempNode = {
        id: newNodeId,
        type: nodeType,
        position: { x: initialPosition.x, y: initialPosition.y },
      };

      // Find a position that doesn't overlap with existing nodes
      let foundPosition = false;
      const maxAttempts = 30; // Increase max attempts to find a good position

      // Try to find a non-overlapping position
      for (let attempts = 0; attempts < maxAttempts && !foundPosition; attempts++) {
        foundPosition = true;

        // Check against all existing nodes
        for (const node of nodes) {
          if (nodesIntersect(tempNode, node)) {
            foundPosition = false;

            // Try different positions with increasing randomness and distance
            const randomFactor = Math.min(1, attempts / 10) * 800; // Increase randomness with attempts
            const distanceFactor = 200 + (attempts * 50); // Increase distance with each attempt

            // Try a new position with some randomness and increasing distance
            if (attempts < 10) {
              // First try positions to the right and below
              tempNode.position.x = initialPosition.x + distanceFactor + (Math.random() * 100);
              tempNode.position.y = initialPosition.y + (Math.random() * 200);
            } else if (attempts < 20) {
              // Then try positions in all directions with more randomness
              tempNode.position.x = initialPosition.x + (Math.random() - 0.5) * randomFactor;
              tempNode.position.y = initialPosition.y + (Math.random() - 0.5) * randomFactor;
            } else {
              // Finally try positions far away from the center
              tempNode.position.x = initialPosition.x + (Math.random() > 0.5 ? 1 : -1) * (800 + Math.random() * 400);
              tempNode.position.y = initialPosition.y + (Math.random() > 0.5 ? 1 : -1) * (400 + Math.random() * 200);
            }
            break;
          }
        }
      }

      // If we still couldn't find a non-overlapping position, place it far away in a guaranteed new position
      if (!foundPosition) {
        // Place it far to the right of all existing nodes
        const rightmostNode = nodes.reduce((furthest: number, node: any) => {
          return node.position.x > furthest ? node.position.x : furthest;
        }, initialPosition.x);

        tempNode.position.x = rightmostNode + 600;
        tempNode.position.y = initialPosition.y + (Math.random() - 0.5) * 400;
      }

      // Increment the node counter by a larger value to avoid ID conflicts
      setNodeCounter(prev => prev + 100);

      const defaultData = {
        title: 'New Task',
        importance: 'medium',
        status: 'not started',
        nextAction: 'complete',
        onChange: (field: string, value: any) => {
        },
        onDelete: onDeleteNode,
        id: newNodeId
      };

      const newNode = {
        id: newNodeId,
        type: nodeType,
        position: { x: tempNode.position.x, y: tempNode.position.y },
        data: { ...defaultData, ...nodeData },
      };

      // IMPORTANT: This must ONLY add the new node without replacing existing ones
      setNodes((nds: any[]) => [...nds, newNode]);
    },
    [reactFlowInstance, setNodes, nodeCounter, nodes, onDeleteNode]
  );

  // Function to expand all nodes
  const expandAllNodes = useCallback(() => {
    setNodes((nds: any[]) => {
      // Only update nodes that need to change to avoid unnecessary re-renders
      return nds.map((node: { data: { isExpanded: boolean; }; }) => {
        if (node.data.isExpanded === true) {
          return node; // Skip nodes that are already expanded
        }
        return {
          ...node,
          data: {
            ...node.data,
            isExpanded: true
          }
        };
      });
    });
  }, [setNodes]);

  // Function to minimize all nodes
  const minimizeAllNodes = useCallback(() => {
    setNodes((nds: any[]) => {
      // Only update nodes that need to change to avoid unnecessary re-renders
      return nds.map((node: { data: { isExpanded: boolean; }; }) => {
        if (node.data.isExpanded === false) {
          return node; // Skip nodes that are already minimized
        }
        return {
          ...node,
          data: {
            ...node.data,
            isExpanded: false
          }
        };
      });
    });
  }, [setNodes]);

  // Function to save the workflow state
  const saveWorkflow = useCallback(async (returnData = false) => {
    if (!reactFlowInstance) {
      toast.error('Unable to save workflow. Missing required data.');
      return null;
    }

    // Get the current flow state using toObject
    const flowData = reactFlowInstance.toObject();

    // If returnData is true, just return the flow data without saving
    if (returnData) {
      return flowData;
    }

    // Otherwise, save to the database
    if (!workflowId) {
      toast.error('Unable to save workflow. Missing workflow ID.');
      return null;
    }

    try {
      // Create Supabase client
      const supabase = createClient();

      // Update the workflow in the database
      const { error } = await supabase
        .from('workflows')
        .update({
          flow_data: flowData,
          updated_at: new Date().toISOString()
        })
        .eq('id', workflowId);

      if (error) {
        console.error('Error saving workflow:', error);
        toast.error('Failed to save workflow. Please try again.');
      } else {
        toast.success('Workflow saved successfully!');
      }
      return flowData;
    } catch (error) {
      console.error('Error saving workflow:', error);
      toast.error('An unexpected error occurred while saving the workflow.');
      return null;
    }
  }, [reactFlowInstance, workflowId]);

  // Expose the functions to the window object for direct access
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Wrap onAddNode to add extra logging
      const wrappedAddNode = (nodeType: string, nodeData: any) => {
        console.log("WRAPPED ADD NODE CALLED");
        console.log("Current node count before adding:", nodes.length);
        console.log("Current nodes:", nodes.map((n: any) => n.id));
        console.log("Adding node type:", nodeType);
        console.log("With data:", nodeData);

        const result = onAddNode(nodeType, nodeData);

        // Log after a short delay to see the updated state
        setTimeout(() => {
          console.log("Node count after adding:", nodes.length);
          console.log("Updated nodes:", nodes.map((n: any) => n.id));

          // Check for duplicate IDs
          const nodeIds = nodes.map((n: any) => n.id);
          const uniqueIds = new Set(nodeIds);
          if (nodeIds.length !== uniqueIds.size) {
            console.error("DUPLICATE NODE IDs DETECTED!");
            console.error("Duplicate IDs:", nodeIds.filter((id: string, index: number) => nodeIds.indexOf(id) !== index));
          }
        }, 100);

        return result;
      };

      (window as any).expandAllWorkflowNodes = expandAllNodes;
      (window as any).minimizeAllWorkflowNodes = minimizeAllNodes;
      (window as any).addWorkflowNode = wrappedAddNode; // Use wrapped version
      (window as any).duplicateWorkflowNodes = onDuplicateNodes;
      (window as any).saveWorkflow = saveWorkflow;
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).expandAllWorkflowNodes;
        delete (window as any).minimizeAllWorkflowNodes;
        delete (window as any).addWorkflowNode;
        delete (window as any).duplicateWorkflowNodes;
        delete (window as any).saveWorkflow;
      }
    };
  }, [expandAllNodes, minimizeAllNodes, onAddNode, onDuplicateNodes, saveWorkflow, nodes]);

  return (
    <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
      <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
        <CardContent className="flex-1 p-0">
          <div
            ref={reactFlowWrapper}
            style={{ width: '100%', height: '100%' }}
            className="h-full w-full"
            onDragOver={onDragOver}
            onDrop={onDrop}
          >
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={handleNodesChange}
              onEdgesChange={handleEdgesChange}
              proOptions={{ hideAttribution: true }}
              onConnect={onConnect}
              onReconnect={onReconnect}
              onInit={onInit}
              onSelectionChange={onSelectionChange}
              nodeTypes={memoizedNodeTypes}
              edgeTypes={memoizedEdgeTypes}
              isValidConnection={isValidConnection}
              fitView
              fitViewOptions={{
                padding: 0.2, // Add padding around the graph
                minZoom: 0.5, // Allow zooming out more
                maxZoom: 2,   // Limit maximum zoom
              }}
              defaultViewport={{ x: 0, y: 0, zoom: 0.8 }} // Start slightly zoomed out
              style={{ width: '100%', height: '100%' }}
              className="h-full w-full"
              selectionOnDrag
              multiSelectionKeyCode="Control"
              deleteKeyCode="Delete"
              edgesReconnectable={true}
            >
              <Background />
              <Controls />
              <MiniMap />
              {/* Panel buttons moved to sidebar */}
            </ReactFlow>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

