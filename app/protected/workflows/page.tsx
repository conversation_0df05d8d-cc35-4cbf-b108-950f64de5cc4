'use client';

import React, { useEffect, useState } from 'react';
import { WorkflowsTable } from '@/components/tables/WorkflowsTable';
import CreateWorkflowModal from '@/components/modals/CreateWorkflowModal';
import Sidebar from '@/components/sidebars/Sidebar';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Construction } from 'lucide-react';

interface Household {
  id: number;
  householdName: string;
  members: string;
}

interface Workflow {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  created_at: string;
  updated_at: string;
}

export default function WorkflowsPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [households, setHouseholds] = useState<Household[]>([]);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const supabase = createClient();
  const router = useRouter();

  const fetchHouseholds = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('No authenticated user found');
      return;
    }

    const { data, error } = await supabase
      .from('households')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching households:', error);
    } else {
      setHouseholds(data || []);
    }
  };

  const fetchWorkflows = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('No authenticated user found');
      return;
    }

    const { data, error } = await supabase
      .from('workflows')
      .select(`
        *,
        households (
          householdName
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching workflows:', error);
    } else {
      const workflowsWithHouseholdNames = data.map(workflow => ({
        ...workflow,
        household_name: workflow.households?.householdName || 'Unknown',
      }));
      setWorkflows(workflowsWithHouseholdNames);
    }
  };

  useEffect(() => {
    fetchHouseholds();
    fetchWorkflows();
  }, []);

  const handleModalClose = () => {
    setIsModalOpen(false);
    fetchWorkflows();
  };

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <div className="flex-1 min-h-0">
          <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
            <CardContent>
            <WorkflowsTable 
              data={workflows} 
              onCreateWorkflow={() => setIsModalOpen(true)}
              />
            <div className="flex flex-col items-center justify-center h-full space-y-6 py-12">
                  <Construction className="h-24 w-24 text-amber-500" />
                  <h2 className="text-2xl font-bold text-center">This feature is currently under construction</h2>
                  <p className="text-center text-muted-foreground max-w-md">
                    This is being worked on as we speak. For now, it is just the canvas and wont create any workflows.
                  </p>
                </div>
              <CreateWorkflowModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                households={households}
              />
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
  );
}