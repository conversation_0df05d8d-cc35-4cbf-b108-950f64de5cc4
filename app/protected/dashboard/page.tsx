"use client";
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import Sidebar from '@/components/sidebars/Sidebar';
import { createClient } from '@/utils/supabase/client';
import RecentScenarioCard from '@/components/RecentScenarioCard';
import { Loader2 } from 'lucide-react';
import { startOfWeek, endOfWeek, parseISO, format } from 'date-fns';
import TaskModal from '@/components/modals/TaskModal';
import WeekCalendarView from '@/components/calendar/WeekCalendarView';
import TasksDueSection from '@/components/dashboard/TasksDueSection';

// Define interfaces for your data
interface RecentScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_viewed_at?: string;
}

interface Document {
  id: number;
  type: 'soa' | 'discovery' | 'toe' | 'risk' | 'other';
  name: string;
  created_at: string;
  status: string;
  household_id: number;
  household_name: string;
  expires_at?: string;
}

interface Task {
  id: number;
  title: string;
  due_date: string;
  household_name: string;
  household_id: number;
  importance: string;
  content: string;
  status: string;
  comments: {
    id: number;
    task_id: number;
    content: string;
    created_at: string;
    user_id: string;
  }[];
}

export default function Dashboard() {
  const [recentScenarios, setRecentScenarios] = useState<RecentScenario[]>([]);
  const [pendingDocuments, setPendingDocuments] = useState<Document[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [fullTaskData, setFullTaskData] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});

  useEffect(() => {
    // Ensure viewMode has a default value if not in localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      // Set default to 'user' if nothing is stored
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
      localStorage.setItem('scenariosViewMode', 'user');
    }

    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };

    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  useEffect(() => {
    const fetchUserProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();

        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    };

    fetchUserProfile();
  }, []);

  useEffect(() => {
    if (profileData.user_id) {
      fetchDashboardData();
    }
  }, [viewMode, profileData]);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    const supabase = createClient();

    try {
      // For recent scenarios
      let recentQuery;

      // Apply view mode filter if profile data is available
      if (profileData.user_id) {
        if (viewMode === 'user') {
          recentQuery = supabase.from('scenarios_data1')
            .select('id, scenario_name, household_name, created_at, last_viewed_at')
            .eq('user_id', profileData.user_id)
            .order('last_viewed_at', { ascending: false })
            .limit(5);
        } else if (viewMode === 'organization' && profileData.org_id) {
          recentQuery = supabase.from('scenarios_data1')
            .select('id, scenario_name, household_name, created_at, last_viewed_at')
            .eq('org_id', profileData.org_id)
            .order('last_viewed_at', { ascending: false })
            .limit(5);
        } else {
          // Fallback to RPC if no specific filter
          recentQuery = supabase.rpc('get_scenarios_with_household_names_recent');
        }
      } else {
        // Fallback to RPC if no profile data
        recentQuery = supabase.rpc('get_scenarios_with_household_names_recent');
      }

      const { data: recentData, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent scenarios:', recentError);

        // Fallback to regular query if RPC fails
        const { data: fallbackRecentData, error: fallbackRecentError } = await supabase
          .from('scenarios_data1')
          .select('id, scenario_name, household_name, created_at, last_viewed_at')
          .order('last_viewed_at', { ascending: false })
          .limit(5);

        if (fallbackRecentError) throw fallbackRecentError;
        setRecentScenarios(fallbackRecentData || []);
      } else {
        setRecentScenarios(recentData || []);
      }

      // Fetch TOE documents
      const { data: toeData, error: toeError } = await supabase
        .from('toe_tokens')
        .select('*, households(householdName)')
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      // Fetch Discovery documents
      const { data: discoveryData, error: discoveryError } = await supabase
        .from('discovery_tokens')
        .select('*, households(householdName)')
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      // Fetch Risk Profiler documents
      const { data: riskData, error: riskError } = await supabase
        .from('risk_profiler_tokens')
        .select('*, households(householdName)')
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      // Transform and combine all pending documents
      const allPendingDocuments: Document[] = [
        ...(toeData?.map(doc => ({
          id: doc.id,
          type: 'toe' as const,
          name: 'Terms of Engagement',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          expires_at: doc.expires_at
        })) || []),
        ...(discoveryData?.map(doc => ({
          id: doc.id,
          type: 'discovery' as const,
          name: 'Discovery Document',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          expires_at: doc.expires_at
        })) || []),
        ...(riskData?.map(doc => ({
          id: doc.id,
          type: 'risk' as const,
          name: 'Risk Profiler',
          created_at: doc.created_at,
          status: doc.status,
          household_id: doc.household_id,
          household_name: doc.households.householdName,
          expires_at: doc.expires_at
        })) || [])
      ];

      setPendingDocuments(allPendingDocuments);

      // Fetch all tasks for the current week (for calendar view only)
      const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 }); // Start week on Monday
      const weekEnd = endOfWeek(new Date(), { weekStartsOn: 1 }); // End week on Sunday

      // Set time to beginning and end of day to ensure we get all tasks
      weekStart.setHours(0, 0, 0, 0);
      weekEnd.setHours(23, 59, 59, 999);

      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          due_date,
          importance,
          households (householdName),
          household_id
        `)
        .gte('due_date', weekStart.toISOString())
        .lte('due_date', weekEnd.toISOString())
        .order('due_date', { ascending: true });

      if (tasksError) throw tasksError;

      const formattedTasks = tasksData?.map(task => ({
        id: task.id,
        title: task.title,
        due_date: task.due_date,
        // Safely access householdName from the potentially array-like structure
        household_name: task.households?.[0]?.householdName || 'Unknown Household',
        household_id: task.household_id,
        importance: task.importance || 'medium',
        content: '',
        status: '',
        comments: []
      })) || [];

      setTasks(formattedTasks);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTaskDetails = async (taskId: number) => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('Error fetching task details:', error);
      return null;
    }

    return data;
  };

  return (
    <div className="flex h-[calc(100vh-50px)] mt-[50px]">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">

        {/* Recent Scenarios Section */}
        <Card className="flex-0 mb-2">
          <CardHeader>
            <CardTitle>Recent Scenarios</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {recentScenarios.map((scenario) => (
                  <RecentScenarioCard key={scenario.id} scenario={scenario} />
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 pt-2 md:grid-cols-2 gap-6 flex-grow overflow-hidden h-[calc(100vh-200px)]">
          {/* Tasks Due Section */}
          <div className="h-full overflow-hidden">
            <TasksDueSection />
          </div>

          {/* Calendar View */}
          <div className="h-full overflow-hidden">
            <WeekCalendarView
              tasks={tasks}
              onTaskClick={async (task) => {
                setSelectedTask(task);
                const fullTask = await fetchTaskDetails(task.id);
                setFullTaskData(fullTask);
                setIsTaskModalOpen(true);
              }}
              onTaskUpdate={fetchDashboardData}
            />
          </div>
        </div>

      </div>
      {selectedTask && fullTaskData && (
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={() => {
            setIsTaskModalOpen(false);
            setSelectedTask(null);
            setFullTaskData(null);
          }}
          task={{
            id: fullTaskData.id,
            title: fullTaskData.title,
            due_date: fullTaskData.due_date,
            household_id: fullTaskData.household_id,
            importance: fullTaskData.importance || '',
            content: fullTaskData.content || '',
            status: fullTaskData.status || '',
            comments: fullTaskData.comments || []
          }}
          onSave={async () => {
            await fetchDashboardData();
          }}
          householdName={selectedTask.household_name}
          readonly={true}
        />
      )}
    </div>
  );
}
