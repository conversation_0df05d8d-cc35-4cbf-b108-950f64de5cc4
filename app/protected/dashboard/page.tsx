import { requireAuth } from '@/utils/auth-guard';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { startOfWeek, endOfWeek } from 'date-fns';
import DashboardClient from './DashboardClient';

// Define interfaces for your data
interface RecentScenario {
  id: number;
  scenario_name: string;
  household_name: string;
  created_at: string;
  last_viewed_at?: string;
}

interface Task {
  id: number;
  title: string;
  due_date: string;
  household_name: string;
  household_id: number;
  importance: string;
  content: string;
  status: string;
  comments: {
    id: number;
    task_id: number;
    content: string;
    created_at: string;
    user_id: string;
  }[];
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

/**
 * Server component that validates authentication and fetches data
 * This cannot be bypassed by disabling JavaScript
 */
export default async function DashboardPage() {
  // Server-side authentication check - cannot be bypassed
  const user = await requireAuth();

  if (!user) {
    redirect('/sign-in');
  }

  // Server-side data fetching with proper authorization
  const supabase = createClient();

  try {
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_id, org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      redirect('/sign-in');
    }

    // Fetch recent scenarios with proper authorization
    let recentQuery = supabase.from('scenarios_data1')
      .select('id, scenario_name, household_name, created_at, last_viewed_at')
      .eq('user_id', user.id)
      .order('last_viewed_at', { ascending: false })
      .limit(5);

    const { data: recentScenarios, error: recentError } = await recentQuery;

    // Fetch all tasks for the current week with proper authorization
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    const weekEnd = endOfWeek(new Date(), { weekStartsOn: 1 });

    weekStart.setHours(0, 0, 0, 0);
    weekEnd.setHours(23, 59, 59, 999);

    const { data: tasksData, error: tasksError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        due_date,
        importance,
        households (householdName),
        household_id
      `)
      .eq('user_id', user.id)
      .gte('due_date', weekStart.toISOString())
      .lte('due_date', weekEnd.toISOString())
      .order('due_date', { ascending: true });

    const formattedTasks = tasksData?.map(task => ({
      id: task.id,
      title: task.title,
      due_date: task.due_date,
      household_name: task.households?.[0]?.householdName || 'Unknown Household',
      household_id: task.household_id,
      importance: task.importance || 'medium',
      content: '',
      status: '',
      comments: []
    })) || [];

    if (recentError || tasksError) {
      // If there's an error, still render the page but with empty data
      return (
        <DashboardClient
          initialRecentScenarios={[]}
          initialTasks={[]}
          userProfile={profile as UserProfile}
          user={user}
        />
      );
    }

    // Pass server-fetched data to client component
    return (
      <DashboardClient
        initialRecentScenarios={recentScenarios as RecentScenario[]}
        initialTasks={formattedTasks as Task[]}
        userProfile={profile as UserProfile}
        user={user}
      />
    );

  } catch (error) {
    // If any server-side operation fails, redirect to sign-in
    redirect('/sign-in');
  }
}
