"use client";

import React, { useState, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Loader2 } from "lucide-react";
import Sidebar from '@/components/sidebars/Sidebar';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import CreateScribbleModal from '@/components/modals/CreateScribbleModal';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

interface Scribble {
  id: number;
  title: string;
  created_at: string;
  last_edited_at: string;
}

export default function Scribbles() {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [scribbles, setScribbles] = useState<Scribble[]>([]);
  const [recentScribbles, setRecentScribbles] = useState<Scribble[]>([]);
  const router = useRouter();

  useEffect(() => {
    fetchScribbles();
  }, []);

  const fetchScribbles = async () => {
    setIsLoading(true);
    const supabase = createClient();
    
    try {
      const { data: recentData, error: recentError } = await supabase
        .from('scribbles')
        .select('*')
        .order('last_edited_at', { ascending: false })
        .limit(5);

      if (recentError) throw recentError;
      setRecentScribbles(recentData || []);

      const { data: allData, error: allError } = await supabase
        .from('scribbles')
        .select('*')
        .order('created_at', { ascending: false });

      if (allError) throw allError;
      setScribbles(allData || []);
    } catch (error) {
      console.error('Error fetching scribbles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (scribble: Scribble) => {
    router.push(`/protected/scribbles/pad?id=${scribble.id}`);
  };

  const handleDelete = async (scribble: Scribble) => {
    const supabase = createClient();
    const { error } = await supabase
      .from('scribbles')
      .delete()
      .eq('id', scribble.id);

    if (error) {
      console.error('Error deleting scribble:', error);
    } else {
      fetchScribbles(); // Refresh the table
    }
  };

  const columns: ColumnDef<Scribble>[] = [
    {
      accessorKey: "title",
      header: "Title",
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        return new Date(row.getValue("created_at")).toLocaleString();
      },
    },
    {
      accessorKey: "last_edited_at",
      header: "Last Edited",
      cell: ({ row }) => {
        return new Date(row.getValue("last_edited_at")).toLocaleString();
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const scribble = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(scribble)}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDelete(scribble)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: scribbles,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  return (
    <div className="flex h-screen">
      <div className="flex-grow p-1 overflow-hidden flex flex-col">
        <main className="flex-grow flex flex-col space-y-4">
          <Card className="h-[30vh] mt-1 mb-1">
            <CardHeader>
              <CardTitle>Recent Scribbles</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {recentScribbles.map((scribble) => (
                    <Card key={scribble.id} className="p-4">
                      <h3 className="font-semibold">{scribble.title}</h3>
                      <p className="text-sm text-gray-500">Last edited: {new Date(scribble.last_edited_at).toLocaleString()}</p>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="flex-grow">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>All Scribbles</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between py-4">
                <Input
                  placeholder="Filter scribbles..."
                  value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
                  onChange={(event) =>
                    table.getColumn("title")?.setFilterValue(event.target.value)
                  }
                  className="max-w-sm"
                />
                <Button onClick={() => setIsCreateModalOpen(true)}>Create Scribble</Button>
              </div>
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                          {headerGroup.headers.map((header) => {
                            return (
                              <TableHead key={header.id}>
                                {header.isPlaceholder
                                  ? null
                                  : flexRender(
                                      header.column.columnDef.header,
                                      header.getContext()
                                    )}
                              </TableHead>
                            );
                          })}
                        </TableRow>
                      ))}
                    </TableHeader>
                    <TableBody>
                      {table.getRowModel().rows?.length ? (
                        table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            data-state={row.getIsSelected() && "selected"}
                          >
                            {row.getVisibleCells().map((cell) => (
                              <TableCell key={cell.id}>
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={columns.length} className="h-24 text-center">
                            No results.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
      <CreateScribbleModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateScribble={(householdId) => {
          router.push(`/protected/scribbles/pad`);
        }}
      />
    </div>
  );
}