"use client";

import Sidebar from '@/components/sidebars/Sidebar';
import { Card, CardContent } from '@/components/ui/card';
import dynamic from 'next/dynamic';
import { useState, useEffect } from 'react';

const Excalidraw = dynamic(() => import('@excalidraw/excalidraw').then((mod) => mod.Excalidraw), {
  ssr: false,
  loading: () => <p>Loading Excalidraw...</p>
});

export default function ScribbleEditor() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="flex h-screen">
      <div className="flex-grow p-2">
        <Card className="h-full overflow-y-auto">
          <CardContent className="h-full">
            <div className="flex flex-col items-center justify-center h-full">
              {mounted && (
                <div style={{ width: '100%', height: '100%' }}>
                  <Excalidraw
                    initialData={{ appState: { viewBackgroundColor: '#ffffff' } }}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}