'use client';

import { Suspense, useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, UserPlus, Users, Building, Settings, Mail, Phone, Globe, MapPin } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { useSearchParams } from 'next/navigation';
import { createOrganizationInvitation, updateOrganizationDetails, cancelOrganizationInvitation, getOrganizationMembers, getOrganizationInvitations } from '@/app/actions/organization';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import LogoUpload from '@/components/LogoUpload';
import { toast } from 'sonner';

interface Member {
  id: string;
  user_id: string;
  name: string;
  email: string;
  org_role: 'owner' | 'full' | 'readonly';
}

interface Invitation {
  id: string;
  email: string;
  created_at: string;
  expires_at: string;
  status: string;
  role: 'owner' | 'full' | 'readonly';
}

function OrganisationContent() {
  const searchParams = useSearchParams();
  const supabase = createClient();
  const [activeTab, setActiveTab] = useState('details');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'owner' | 'full' | 'readonly'>('full');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRecreatingBucket, setIsRecreatingBucket] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);

  const [organizationData, setOrganizationData] = useState<any>({
    id: '',
    name: '',
    email: '',
    phone: '',
    website: '',
    address: '',
    logo_path: null
  });

  const [members, setMembers] = useState<Member[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);

  // Check for success/error messages in URL
  useEffect(() => {
    const message = searchParams.get('message');
    const type = searchParams.get('type');

    if (message) {
      if (type === 'success') {
        setSuccessMessage(message);
        // Clear success message after 5 seconds
        setTimeout(() => setSuccessMessage(''), 5000);
      } else if (type === 'error') {
        setError(message);
        // Clear error message after 5 seconds
        setTimeout(() => setError(''), 5000);
      }
    }
  }, [searchParams]);

  // Fetch organization data on component mount
  useEffect(() => {
    fetchOrganizationData();
  }, []);

  const fetchOrganizationData = async () => {
    setLoading(true);
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      setUserId(user.id);

      // Get user's profile with organization info
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('org_id, org_name')
        .eq('user_id', user.id)
        .single();

      if (profileError || !profile || !profile.org_id) {
        throw new Error('No organization found for user');
      }

      // Get organization details
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', profile.org_id)
        .single();

      if (orgError) {
        throw orgError;
      }

      setOrganizationData(organization);

      // Fetch members and invitations
      await fetchMembersAndInvitations();
    } catch (err: any) {
      console.error('Error fetching organization data:', err);
      setError(err.message || 'Failed to load organization data');
    } finally {
      setLoading(false);
    }
  };

  const fetchMembersAndInvitations = async () => {
    try {
      // Fetch members
      const membersResult = await getOrganizationMembers();
      if (membersResult.error) {
        console.error('Error fetching members:', membersResult.error);
      } else if (membersResult.data) {
        // Add org_role from profiles table
        const membersWithRoles = await Promise.all(
          membersResult.data.map(async (member) => {
            // Fetch the profile to get the org_role
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .select('org_role')
              .eq('user_id', member.user_id)
              .single();

            if (profileError) {
              console.error(`Error fetching role for member ${member.id}:`, profileError);
              return { ...member, org_role: 'readonly' as const }; // Default to readonly
            }

            return {
              ...member,
              org_role: (profile?.org_role || 'readonly') as 'owner' | 'full' | 'readonly'
            };
          })
        );

        setMembers(membersWithRoles);
      }

      // Fetch invitations
      const invitationsResult = await getOrganizationInvitations();
      if (invitationsResult.error) {
        console.error('Error fetching invitations:', invitationsResult.error);
      } else if (invitationsResult.data) {
        // Add role if missing
        const invitationsWithRoles = invitationsResult.data.map(invitation => {
          // Use type assertion to access role property
          const invitationWithRole = invitation as any;
          return {
            ...invitation,
            role: (invitationWithRole.role || 'readonly') as 'owner' | 'full' | 'readonly'
          };
        });

        setInvitations(invitationsWithRoles);
      }
    } catch (err) {
      console.error('Error fetching members and invitations:', err);
    }
  };

  const handleInviteSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('email', inviteEmail);
      formData.append('role', inviteRole);
      formData.append('returnPath', '/protected/admin/organisation');

      await createOrganizationInvitation(formData);

      // Close modal and reset form
      setIsInviteModalOpen(false);
      setInviteEmail('');
      setInviteRole('full'); // Reset to default

      // Show success toast
      toast.success(`Invitation sent to ${inviteEmail}`);

      // Refresh data
      await fetchMembersAndInvitations();
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      const formData = new FormData();
      formData.append('invitationId', invitationId);
      formData.append('returnPath', '/protected/admin/organisation');

      await cancelOrganizationInvitation(formData);

      // Show success toast
      toast.success('Invitation cancelled successfully');

      // Refresh data
      await fetchMembersAndInvitations();
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast.error('Failed to cancel invitation');
    }
  };

  const handleUpdateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('name', organizationData.name);
      formData.append('email', organizationData.email || '');
      formData.append('phone', organizationData.phone || '');
      formData.append('website', organizationData.website || '');
      formData.append('address', organizationData.address || '');
      formData.append('returnPath', '/protected/admin/organisation');

      await updateOrganizationDetails(formData);

      // Show success toast
      toast.success('Organisation details updated successfully');

      // Refresh data
      await fetchOrganizationData();
    } catch (error) {
      console.error('Error updating organization:', error);
      toast.error('Failed to update organisation details');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogoUpdate = async (logoPath: string | null) => {
    try {
      // Update organization data with new logo path
      setOrganizationData({
        ...organizationData,
        logo_path: logoPath
      });

      // Update organization in database
      const { error } = await supabase
        .from('organizations')
        .update({ logo_path: logoPath })
        .eq('id', organizationData.id);

      if (error) {
        throw error;
      }

      toast.success('Logo updated successfully');
    } catch (error) {
      console.error('Error updating logo:', error);
      toast.error('Failed to update logo');
    }
  };

  const handleRoleChange = async (memberId: string, newRole: string) => {
    try {
      // Find the member
      const member = members.find(m => m.id === memberId);
      if (!member) {
        throw new Error('Member not found');
      }

      // Check if current user is the owner
      const { data: currentUserProfile, error: profileError } = await supabase.auth.getUser();
      if (profileError || !currentUserProfile.user) {
        throw new Error('Failed to get current user');
      }

      // Get current user's profile to check role
      const { data: myProfile, error: myProfileError } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', currentUserProfile.user.id)
        .single();

      if (myProfileError || !myProfile) {
        throw new Error('Failed to get your profile');
      }

      // Only owners and full rights users can change roles
      if (myProfile.org_role !== 'owner' && myProfile.org_role !== 'full') {
        toast.error('You do not have permission to change roles');
        return;
      }

      // Full rights users cannot change other full rights users or owners
      if (myProfile.org_role === 'full' && (member.org_role === 'owner' || member.org_role === 'full')) {
        toast.error('You can only change roles of read-only members');
        return;
      }

      // Update the member's role in the profiles table
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ org_role: newRole })
        .eq('user_id', member.user_id);

      if (updateError) {
        throw updateError;
      }

      // Update the local state
      setMembers(prevMembers =>
        prevMembers.map(m =>
          m.id === memberId
            ? { ...m, org_role: newRole as 'owner' | 'full' | 'readonly' }
            : m
        )
      );

      toast.success(`Role updated to ${newRole === 'full' ? 'Full Rights' : 'Read Only'}`);
    } catch (error: any) {
      console.error('Error updating role:', error);
      toast.error(error.message || 'Failed to update role');
    }
  };

  const handleInvitationRoleChange = async (invitationId: string, newRole: string) => {
    try {
      // Find the invitation
      const invitation = invitations.find(i => i.id === invitationId);
      if (!invitation) {
        throw new Error('Invitation not found');
      }

      // Check if current user is the owner
      const { data: currentUserProfile, error: profileError } = await supabase.auth.getUser();
      if (profileError || !currentUserProfile.user) {
        throw new Error('Failed to get current user');
      }

      // Get current user's profile to check role
      const { data: myProfile, error: myProfileError } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', currentUserProfile.user.id)
        .single();

      if (myProfileError || !myProfile) {
        throw new Error('Failed to get your profile');
      }

      // Only owners and full rights users can change invitation roles
      if (myProfile.org_role !== 'owner' && myProfile.org_role !== 'full') {
        toast.error('You do not have permission to change invitation roles');
        return;
      }

      // Full rights users cannot set owner role
      if (myProfile.org_role === 'full' && newRole === 'owner') {
        toast.error('Only organisation owners can assign the Owner role');
        return;
      }

      // Update the invitation role in the database
      const { error: updateError } = await supabase
        .from('organization_invitations')
        .update({ role: newRole })
        .eq('id', invitationId);

      if (updateError) {
        throw updateError;
      }

      // Update the local state
      setInvitations(prevInvitations =>
        prevInvitations.map(i =>
          i.id === invitationId
            ? { ...i, role: newRole as 'owner' | 'full' | 'readonly' }
            : i
        )
      );

      toast.success(`Invitation role updated to ${
        newRole === 'owner' ? 'Owner' :
        newRole === 'full' ? 'Full Rights' :
        'Read Only'
      }`);
    } catch (error: any) {
      console.error('Error updating invitation role:', error);
      toast.error(error.message || 'Failed to update invitation role');
    }
  };

  const handleRecreateStorageBucket = async () => {
    try {
      // Check if current user has permission (owner or full rights)
      const { data: currentUserProfile, error: profileError } = await supabase.auth.getUser();
      if (profileError || !currentUserProfile.user) {
        throw new Error('Failed to get current user');
      }

      // Get current user's profile to check role
      const { data: myProfile, error: myProfileError } = await supabase
        .from('profiles')
        .select('org_role')
        .eq('user_id', currentUserProfile.user.id)
        .single();

      if (myProfileError || !myProfile) {
        throw new Error('Failed to get your profile');
      }

      // Only owners and full rights users can recreate storage bucket
      if (myProfile.org_role !== 'owner' && myProfile.org_role !== 'full') {
        toast.error('You do not have permission to recreate the storage bucket');
        return;
      }

      setIsRecreatingBucket(true);

      // Call the API to recreate the storage bucket
      const response = await fetch('/api/recreate-storage-bucket', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to recreate storage bucket');
      }

      await response.json(); // Parse response but we don't need the result

      toast.success('Storage bucket recreated successfully', {
        description: 'The storage bucket has been recreated with proper permissions.'
      });

    } catch (error: any) {
      console.error('Error recreating storage bucket:', error);
      toast.error('Failed to recreate storage bucket', {
        description: error.message || 'Please try again later'
      });
    } finally {
      setIsRecreatingBucket(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-100px)]">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>Loading organisation data...</span>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardHeader>
            <CardTitle>Organisation Management</CardTitle>
            <CardDescription>
              Manage your organisation details, members, and invitations
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {error && (
              <div className="mx-6 mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="mx-6 mt-2 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                {successMessage}
              </div>
            )}

            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-grow flex flex-col">
              <TabsList className="grid grid-cols-2 w-full border-b rounded-none px-6">
                <TabsTrigger value="details" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  <span>Organisation Details</span>
                </TabsTrigger>
                <TabsTrigger value="members" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Members</span>
                </TabsTrigger>
              </TabsList>

              {/* Organisation Details Tab */}
              <TabsContent value="details" className="p-6 overflow-y-auto">
                <form onSubmit={handleUpdateOrganization} className="space-y-6">
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="w-full md:w-1/3">
                      {userId && (
                        <LogoUpload
                          userId={userId}
                          currentLogoPath={organizationData.logo_path}
                          onLogoUpdate={handleLogoUpdate}
                        />
                      )}
                    </div>

                    <div className="w-full md:w-2/3 space-y-4">
                      <div>
                        <Label htmlFor="name">Organisation Name</Label>
                        <Input
                          id="name"
                          value={organizationData.name}
                          onChange={(e) => setOrganizationData({
                            ...organizationData,
                            name: e.target.value
                          })}
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="email" className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          <span>Email Address</span>
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={organizationData.email || ''}
                          onChange={(e) => setOrganizationData({
                            ...organizationData,
                            email: e.target.value
                          })}
                        />
                      </div>

                      <div>
                        <Label htmlFor="phone" className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          <span>Phone Number</span>
                        </Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={organizationData.phone || ''}
                          onChange={(e) => setOrganizationData({
                            ...organizationData,
                            phone: e.target.value
                          })}
                        />
                      </div>

                      <div>
                        <Label htmlFor="website" className="flex items-center gap-1">
                          <Globe className="h-4 w-4" />
                          <span>Website</span>
                        </Label>
                        <Input
                          id="website"
                          type="url"
                          value={organizationData.website || ''}
                          onChange={(e) => setOrganizationData({
                            ...organizationData,
                            website: e.target.value
                          })}
                        />
                      </div>

                      <div>
                        <Label htmlFor="address" className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>Address</span>
                        </Label>
                        <Textarea
                          id="address"
                          value={organizationData.address || ''}
                          onChange={(e) => setOrganizationData({
                            ...organizationData,
                            address: e.target.value
                          })}
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              {/* Members Tab */}
              <TabsContent value="members" className="p-6 overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium">Organisation Members</h3>
                  <Button onClick={() => setIsInviteModalOpen(true)}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite Member
                  </Button>
                </div>

                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Current Members</h4>
                    <div className="border rounded-md divide-y">
                      {members.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          No members found
                        </div>
                      ) : (
                        members.map((member) => (
                          <div key={member.id} className="p-4 flex items-center justify-between">
                            <div className="flex items-center">
                              <Avatar className="h-10 w-10 mr-3">
                                <AvatarFallback>
                                  {member.name?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{member.name || 'Unnamed User'}</div>
                                <div className="text-sm text-gray-500">{member.email}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                member.org_role === 'owner'
                                  ? 'bg-blue-100 text-blue-800'
                                  : member.org_role === 'full'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                              }`}>
                                {member.org_role === 'owner'
                                  ? 'Owner'
                                  : member.org_role === 'full'
                                    ? 'Full Rights'
                                    : 'Read Only'}
                              </span>
                              {member.org_role !== 'owner' && (
                                <Select defaultValue={member.org_role} onValueChange={(value) => handleRoleChange(member.id, value)}>
                                  <SelectTrigger className="w-[130px] h-8">
                                    <SelectValue placeholder="Change role" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="full">Full Rights</SelectItem>
                                    <SelectItem value="readonly">Read Only</SelectItem>
                                  </SelectContent>
                                </Select>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  {invitations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-3">Pending Invitations</h4>
                      <div className="border rounded-md divide-y">
                        {invitations.map((invitation) => (
                          <div key={invitation.id} className="p-4 flex items-center justify-between">
                            <div>
                              <div className="font-medium">{invitation.email}</div>
                              <div className="text-xs text-gray-500">
                                Invited {new Date(invitation.created_at).toLocaleDateString()}
                                {' • '}
                                Expires {new Date(invitation.expires_at).toLocaleDateString()}
                                {' • '}
                                Role: <span className="font-medium">
                                  {invitation.role === 'owner'
                                    ? 'Owner'
                                    : invitation.role === 'full'
                                      ? 'Full Rights'
                                      : 'Read Only'}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Select
                                defaultValue={invitation.role || 'readonly'}
                                onValueChange={(value) => handleInvitationRoleChange(invitation.id, value)}
                              >
                                <SelectTrigger className="w-[130px] h-8">
                                  <SelectValue placeholder="Change role" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="owner">Owner</SelectItem>
                                  <SelectItem value="full">Full Rights</SelectItem>
                                  <SelectItem value="readonly">Read Only</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCancelInvitation(invitation.id)}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Invite Member Modal */}
      <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
            <DialogDescription>
              Send an invitation to join your organisation. The recipient will receive an email with a sign-up link.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleInviteSubmit}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="invite-email">Email Address</Label>
                <Input
                  id="invite-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="invite-role">Role</Label>
                <Select value={inviteRole} onValueChange={(value) => setInviteRole(value as 'owner' | 'full' | 'readonly')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="owner">Organisation Owner</SelectItem>
                    <SelectItem value="full">Full Rights</SelectItem>
                    <SelectItem value="readonly">Read Only</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground mt-1">
                  <p><strong>Owner:</strong> Can do everything, including managing other members</p>
                  <p><strong>Full Rights:</strong> Can do everything except modify the owner</p>
                  <p><strong>Read Only:</strong> Can only view organisation data</p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsInviteModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Invitation'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function OrganisationPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading organisation...</span>
    </div>}>
      <OrganisationContent />
    </Suspense>
  );
}
