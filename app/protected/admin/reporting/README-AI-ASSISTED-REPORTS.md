# AI-Assisted Report Creation

This feature enhances the custom report creation process by adding an AI-assisted option. Users can choose between manually creating a custom report or using AI to generate one based on a natural language description.

## Overview

The AI-assisted report creation feature leverages Google's Gemini 2.0 Flash model to interpret the user's request and configure the appropriate report settings. This makes it easier for non-technical users to create complex reports without needing to understand the underlying data structure or reporting system.

## How It Works

1. The user selects "AI-Assisted Report" in the reporting interface
2. The user is presented with a natural language interface for describing the desired report
3. The user enters a description of the report they want to create
4. The system sends this description to Google's Gemini 2.0 Flash model
5. The AI interprets the description and generates a report configuration
6. The user can review the AI's interpretation and the generated configuration
7. The user can either:
   - Start over if they want to try a different description
   - Create the report with the generated configuration
8. When the user creates the report, it's added to their custom reports

## Technical Implementation

### Components

- `CreateCustomReportModal.tsx`: The main modal component for AI-assisted report creation
- `AIPromptInterface.tsx`: The interface for entering a natural language description
- `AIReportPreview.tsx`: A component for previewing the AI-generated report configuration

For manual report creation, the application uses the existing `CustomReportBuilder.tsx` component, which provides a comprehensive interface with tabs for fields, filters, grouping & sorting, calculations, and visualizations.

### API

- `/api/ai-report`: An API endpoint that processes natural language descriptions and returns report configurations

### Integration with Google AI

The feature uses the Vercel AI SDK with Google's Gemini 2.0 Flash model to generate structured report configurations from natural language descriptions.

### Available Data Sources and Columns

The AI-assisted report creation supports the following data sources:

**Households Table**
- `id`: Unique identifier for the household
- `created_at`: Timestamp when the household was created
- `householdName`: Name of the household
- `members`: JSON data containing household members
- `user_id`: User ID associated with the household
- `address`: Full address
- `phone`: Phone number
- `email`: Email address
- `occupation`: Occupation
- `employer`: Employer name
- `marital_status`: Marital status
- `date_of_birth`: Date of birth
- `tax_file_number`: Tax file number
- `notes`: Additional notes
- `street`: Street address
- `city`: City
- `state`: State or province
- `zip_code`: Postal code
- `country`: Country
- `property_type`: Type of property
- `preferred_contact`: Preferred contact method
- `best_time_to_call`: Best time to call
- `alternative_contact`: Alternative contact information
- `total_assets`: Total assets value
- `investment_strategy`: Investment strategy
- `risk_tolerance`: Risk tolerance level
- `primary_advisor`: Primary financial advisor
- `last_review`: Date of last review
- `next_review`: Date of next review
- `additional_info`: Additional JSON information
- `org_id`: Organization ID
- `updated_at`: Timestamp when the household was last updated

**Assets Table**
- `id`: Unique identifier for the asset
- `household_id`: Reference to the household
- `name`: Name of the asset
- `type`: Type of asset
- `value`: Value of the asset
- `details`: Additional details
- `created_at`: Timestamp when the asset was created
- `updated_at`: Timestamp when the asset was last updated
- `property_type`: Type of property
- `rental_income`: Rental income
- `provider`: Provider of the asset
- `linked_income_id`: Reference to linked income

**Expenses Table**
- `id`: Unique identifier for the expense
- `household_id`: Reference to the household
- `name`: Name of the expense
- `amount`: Amount of the expense
- `frequency`: Frequency of the expense
- `category`: Category of the expense
- `details`: Additional details
- `created_at`: Timestamp when the expense was created
- `updated_at`: Timestamp when the expense was last updated
- `linked_liability_id`: Reference to linked liability

**Goals Table**
- `id`: Unique identifier for the goal
- `created_at`: Timestamp when the goal was created
- `household_id`: Reference to the household
- `title`: Title of the goal
- `type`: Type of goal
- `details`: Additional details
- `start_date`: Start date of the goal
- `achieved_date`: Date when the goal was achieved
- `status`: Status of the goal
- `member`: Member associated with the goal
- `target_amount`: Target amount for the goal
- `priority`: Priority of the goal
- `user_id`: User ID associated with the goal
- `org_id`: Organization ID

**Income Table**
- `id`: Unique identifier for the income
- `household_id`: Reference to the household
- `source`: Source of the income
- `amount`: Amount of the income
- `frequency`: Frequency of the income
- `details`: Additional details
- `created_at`: Timestamp when the income was created
- `updated_at`: Timestamp when the income was last updated
- `income_type`: Type of income
- `linked_asset_id`: Reference to linked asset
- `member_id`: Reference to the member

**Insurances Table**
- `id`: Unique identifier for the insurance
- `household_id`: Reference to the household
- `type`: Type of insurance
- `provider`: Provider of the insurance
- `policy_number`: Policy number
- `premium`: Premium amount
- `frequency`: Frequency of premium payment
- `coverage_amount`: Coverage amount
- `renewal_date`: Renewal date
- `details`: Additional details
- `created_at`: Timestamp when the insurance was created
- `updated_at`: Timestamp when the insurance was last updated
- `policy_owner`: Owner of the policy
- `person_insured`: Person insured

**Interactions Table**
- `id`: Unique identifier for the interaction
- `household_id`: Reference to the household
- `title`: Title of the interaction
- `content`: Content of the interaction
- `date`: Date of the interaction
- `type`: Type of interaction
- `created_at`: Timestamp when the interaction was created
- `file_url`: URL to associated file

**Liabilities Table**
- `id`: Unique identifier for the liability
- `household_id`: Reference to the household
- `name`: Name of the liability
- `amount`: Amount of the liability
- `interest_rate`: Interest rate
- `lender`: Lender name
- `payment_amount`: Payment amount
- `payment_frequency`: Frequency of payment
- `details`: Additional details
- `created_at`: Timestamp when the liability was created
- `updated_at`: Timestamp when the liability was last updated
- `linked_asset_id`: Reference to linked asset
- `loan_type`: Type of loan
- `type`: Type of liability
- `linked_expense_id`: Reference to linked expense

**Recommendations Table**
- `id`: Unique identifier for the recommendation
- `household_id`: Reference to the household
- `title`: Title of the recommendation
- `type`: Type of recommendation
- `details`: Additional details
- `created_date`: Date when the recommendation was created
- `implementation_date`: Date of implementation
- `status`: Status of the recommendation
- `member`: Member associated with the recommendation
- `financial_impact`: Financial impact
- `priority`: Priority of the recommendation
- `adviser_notes`: Adviser notes
- `user_id`: User ID associated with the recommendation
- `org_id`: Organization ID
- `created_at`: Timestamp when the recommendation was created
- `updated_at`: Timestamp when the recommendation was last updated

**Tasks Table**
- `id`: Unique identifier for the task
- `title`: Title of the task
- `content`: Content of the task
- `due_date`: Due date of the task
- `household_id`: Reference to the household
- `importance`: Importance of the task
- `created_at`: Timestamp when the task was created
- `updated_at`: Timestamp when the task was last updated
- `user_id`: User ID associated with the task
- `status`: Status of the task
- `org_id`: Organization ID
- `assigned_to`: User ID of the assignee
- `metadata`: Additional metadata

## Example Prompts

Here are some example prompts that users can use:

### Households
- "Show me a report of all households with high risk tolerance, sorted by total assets"
- "Create a report of households due for review, showing their household name, primary advisor, and next review date"
- "I need a report showing households by city with a pie chart breakdown"

### Assets
- "Show me all assets with a value over $100,000, grouped by type"
- "Create a report of property assets with their rental income"

### Expenses
- "Generate a report of monthly expenses by category with a pie chart"
- "Show me the top 10 highest expenses, sorted by amount"

### Income
- "Create a report of all income sources with their frequency and amounts"
- "Show me income by source with a bar chart visualization"

### Liabilities
- "Generate a report of liabilities with their interest rates and lenders"
- "Show me all loans with payment amounts and frequencies"

### Insurances
- "Create a report of insurance policies with their premiums and coverage amounts"
- "Show me insurance policies due for renewal in the next 3 months"

### Goals
- "Generate a report of financial goals by status with their target amounts"
- "Show me goals sorted by priority with their progress status"

### Tasks
- "Create a report of high importance tasks due in the next month"
- "Show me overdue tasks assigned to specific team members"

### Interactions
- "Generate a report of client interactions from the last quarter, grouped by type"
- "Show me all file attachments from client interactions"

### Recommendations
- "Create a report of recommendations with their financial impact and status"
- "Show me high-priority recommendations that haven't been implemented yet"

## Benefits

- Makes report creation accessible to non-technical users
- Reduces the time and effort required to create custom reports
- Provides a more intuitive way to express reporting needs
- Serves as a starting point that users can further refine

## Future Enhancements

- Implement a feedback loop to improve AI-generated reports over time
- Add support for more complex report types and visualizations
- Implement a conversational interface for refining report requirements
- Add voice input support for describing reports
- Create a library of AI-generated report templates
