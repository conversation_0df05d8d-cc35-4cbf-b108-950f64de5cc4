'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CustomReport } from '../lib/types';
import { getCustomReports } from '../lib/custom-reports';
import { createClient } from '@/utils/supabase/client';

interface ReportContextType {
  reports: CustomReport[];
  isLoading: boolean;
  error: string | null;
  refreshReports: () => Promise<void>;
  lastUpdated: number;
}

const ReportContext = createContext<ReportContextType | undefined>(undefined);

export function ReportProvider({ children }: { children: ReactNode }) {
  const [reports, setReports] = useState<CustomReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState(Date.now());

  // Load reports on component mount and set up realtime subscription
  useEffect(() => {
    refreshReports();

    // Set up realtime subscription to custom_reports table
    const supabase = createClient();
    const channel = supabase
      .channel('custom-reports-changes')
      .on('postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'custom_reports'
        },
        () => {
          console.log('Custom reports table changed, refreshing data...');
          refreshReports();
        }
      )
      .subscribe();

    // Clean up subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Function to refresh reports
  const refreshReports = async () => {
    setError(null);

    try {
      const fetchedReports = await getCustomReports();
      setReports(fetchedReports);
      setLastUpdated(Date.now());
    } catch (err) {
      console.error('Error loading reports:', err);
      setError(err instanceof Error ? err.message : 'Failed to load reports');
    } finally {
      setIsLoading(false);
    }
  };

  // Expose refreshReports globally for access outside of React components
  // This is a workaround for accessing context functions outside of components
  if (typeof window !== 'undefined') {
    (window as any).__REPORT_CONTEXT_REFRESH__ = refreshReports;
  }

  return (
    <ReportContext.Provider value={{ reports, isLoading, error, refreshReports, lastUpdated }}>
      {children}
    </ReportContext.Provider>
  );
}

export function useReports() {
  const context = useContext(ReportContext);
  if (context === undefined) {
    throw new Error('useReports must be used within a ReportProvider');
  }
  return context;
}
