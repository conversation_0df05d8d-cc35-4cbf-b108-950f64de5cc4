# Financial CRM Reporting System

This directory contains the implementation of the reporting system for the Financial CRM application.

## Overview

The reporting system allows users to:

1. Create and view reports on the web
2. Export reports to Excel
3. Download reports as PDFs
4. Run reports on-demand (one-time)
5. Schedule reports to run on an ongoing basis

## Report Types

The system includes the following report categories:

1. **Client Reports**
   - Client Demographics Report
   - Client Financial Snapshot
   - Client Portfolio Overview

2. **Financial Reports**
   - Asset Allocation Report
   - Liability Analysis Report
   - Net Worth Trends Report
   - Cash Flow Analysis Report

3. **Performance Reports**
   - Investment Performance Report
   - Goal Progress Report
   - Financial Plan Adherence Report

4. **Operational Reports**
   - Client Acquisition Report
   - Advisor Productivity Report
   - Organization Performance Report

5. **Compliance Reports**
   - Review Schedule Report
   - Documentation Status Report
   - Regulatory Requirements Report

## Report Scheduling

The system allows users to schedule reports to run automatically on a recurring basis. Scheduled reports can be delivered via:

1. **Email** - Reports are sent to specified email addresses
2. **In-app Notification** - Users receive a notification when a report is ready
3. **Download Link** - Users can download the report from a link in a notification

### Scheduling Options

Reports can be scheduled with the following frequencies:

- **Daily** - Runs every day at the specified time
- **Weekly** - Runs on a specific day of the week at the specified time
- **Monthly** - Runs on a specific day of the month at the specified time
- **Quarterly** - Runs on a specific day of a specific month in each quarter
- **Yearly** - Runs on a specific day of a specific month each year

### Scheduled Reports Management

Users can manage their scheduled reports through the "Scheduled" tab in the reporting interface. From there, they can:

- View all scheduled reports
- Enable or disable schedules
- Edit schedule parameters
- Delete schedules
- Run schedules manually
- View execution history

## Implementation Details

### Database Tables

The reporting system uses the following database tables:

#### Standard Reporting Tables

1. **reports** - Stores report configurations
2. **report_results** - Stores the results of report runs
3. **report_schedules** - Stores schedule configurations
4. **report_executions** - Tracks the execution of scheduled reports

#### Advanced Reporting Tables

5. **custom_reports** - Stores custom report definitions with fields, filters, grouping, sorting, calculations, and visualizations
6. **report_templates** - Stores report templates for reuse

### API Endpoints

The system provides the following API endpoints:

1. **Report Management**
   - `GET /api/reports` - Get all reports
   - `GET /api/reports/:id` - Get a specific report
   - `POST /api/reports` - Create a new report
   - `PUT /api/reports/:id` - Update a report
   - `DELETE /api/reports/:id` - Delete a report

2. **Report Execution**
   - `POST /api/reports/:id/run` - Run a report
   - `GET /api/reports/:id/results` - Get report results

3. **Schedule Management**
   - `GET /api/reports/schedules` - Get all scheduled reports
   - `GET /api/reports/schedules/:id` - Get a specific scheduled report
   - `POST /api/reports/schedules` - Create a new scheduled report
   - `PUT /api/reports/schedules/:id` - Update a scheduled report
   - `DELETE /api/reports/schedules/:id` - Delete a scheduled report
   - `POST /api/reports/schedules/:id/run` - Manually run a scheduled report
   - `GET /api/reports/schedules/:id/executions` - Get execution history

4. **Automated Execution**
   - `GET /api/cron/run-scheduled-reports` - Run all scheduled reports that are due (called by a cron job)

### Components

The system includes the following key components:

#### Standard Reporting Components

1. **ReportingTabs** - Tab navigation for report categories
2. **ReportsList** - Table displaying available reports
3. **ReportConfigModal** - Modal for configuring report parameters
4. **ReportViewer** - Component for displaying report results
5. **ReportRunner** - Component for executing reports and managing results
6. **ScheduleReportModal** - Modal for configuring report schedules
7. **ScheduledReportsList** - Table displaying all scheduled reports
8. **ScheduledReportDetail** - Detailed view of a scheduled report

#### Advanced Reporting Components

9. **CustomReportBuilder** - Build custom reports
10. **FieldSelector** - Select fields for custom reports
11. **AdvancedFilterBuilder** - Build complex filters
12. **GroupingAndSorting** - Configure grouping and sorting
13. **CalculationsBuilder** - Add custom calculations
14. **VisualizationBuilder** - Create visualizations
15. **CustomReportsList** - View custom reports
16. **FavoriteReportsList** - View favorite reports
17. **TemplateReportsList** - View report templates
18. **ComparativeAnalysis** - Compare data across time periods
19. **TrendVisualization** - Visualize trends and forecasts
20. **DatabaseSetup** - Set up the database for advanced reporting features

## Setting Up Automated Report Execution

To enable automated report execution, you need to set up a cron job to call the `/api/cron/run-scheduled-reports` endpoint at regular intervals (e.g., every minute).

### Example Cron Job Configuration

Using a service like Vercel Cron Jobs:

```json
{
  "crons": [
    {
      "path": "/api/cron/run-scheduled-reports",
      "schedule": "* * * * *"
    }
  ]
}
```

Or using a traditional cron job:

```
* * * * * curl -X GET -H "Authorization: Bearer YOUR_SECRET_TOKEN" https://your-domain.com/api/cron/run-scheduled-reports
```

## Advanced Reporting Features

The system now includes the following advanced reporting features:

1. **Custom Report Builder** - Create custom reports with advanced filtering, grouping, and visualization options
   - Select data sources (households, assets, liabilities, etc.)
   - Choose fields to include in the report
   - Apply grouping and aggregation
   - Configure sorting and filtering
   - Add custom calculations
   - Create visualizations

2. **Advanced Filtering** - Apply complex filters with AND/OR logic
   - Multiple filter conditions with logical operators
   - Date range filters with relative options
   - Numeric range filters with comparison operators
   - Text filters with pattern matching
   - Saved filter presets

3. **Comparative Analysis** - Compare data across different time periods
   - Period-over-period comparisons (YoY, MoM, QoQ)
   - Benchmark comparisons against targets
   - Variance analysis with highlighting
   - Goal vs. actual comparisons
   - Percentage change calculations

4. **Trend Visualization** - Analyze trends over time with forecasting capabilities
   - Time series charts with trend lines
   - Moving averages and statistical indicators
   - Basic forecasting based on historical data
   - Heat maps for identifying patterns
   - Drill-down capabilities

5. **Report Templates and Favorites** - Save and reuse report configurations
   - Saving custom reports as templates
   - Creating a template library with categorization
   - Marking reports as favorites
   - Building a dashboard of favorite reports
   - Sharing templates between users

## Future Enhancements

1. **AI-Powered Insights** - Automatically generate insights from report data
2. **Natural Language Queries** - Allow users to query data using natural language
3. **Advanced Forecasting** - Implement more sophisticated forecasting algorithms
4. **Collaborative Reporting** - Enable collaborative report building and sharing
5. **Mobile Reporting** - Optimize the reporting interface for mobile devices
