import { createClient } from '@/utils/supabase/client';
import { ReportType, ReportParams, ReportData } from '../types';
import { applyFilters } from '../data-fetching';
import { ColumnDef } from '@tanstack/react-table';
import { Users } from 'lucide-react';

/**
 * Client Demographics Report
 */
export const clientDemographicsReport: ReportType = {
  id: 'client-demographics',
  name: 'Client Demographics Report',
  description: 'Provides demographic information about clients',
  category: 'client',
  icon: Users,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Build query based on parameters
    let query = supabase
      .from('households')
      .select('*, members')
      .order('created_at', { ascending: false });

    // Apply filters
    query = applyFilters(query, params);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching household data: ${error.message}`);
    }

    // Transform data for report
    const transformedData = this.transformData ? this.transformData(data) : data;

    // Prepare chart data
    const ageGroups = {
      'Under 30': 0,
      '30-40': 0,
      '41-50': 0,
      '51-60': 0,
      '61-70': 0,
      'Over 70': 0
    };

    // Calculate age distribution
    transformedData.forEach((household: any) => {
      if (household.date_of_birth) {
        const birthDate = new Date(household.date_of_birth);
        const age = new Date().getFullYear() - birthDate.getFullYear();

        if (age < 30) ageGroups['Under 30']++;
        else if (age <= 40) ageGroups['30-40']++;
        else if (age <= 50) ageGroups['41-50']++;
        else if (age <= 60) ageGroups['51-60']++;
        else if (age <= 70) ageGroups['61-70']++;
        else ageGroups['Over 70']++;
      }
    });

    return {
      id: `client-demographics-${Date.now()}`,
      reportId: 'client-demographics',
      runAt: new Date(),
      parameters: params,
      data: transformedData,
      columns: this.getColumns(),
      summary: {
        totalClients: transformedData.length,
        ageDistribution: ageGroups
      },
      chartData: {
        labels: Object.keys(ageGroups),
        datasets: [{
          label: 'Age Distribution',
          data: Object.values(ageGroups),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  transformData(data: any[]): any[] {
    return data.map(household => {
      // Extract primary member from members JSON if available
      let primaryMember = { name: '', email: '', phone: '' };

      if (household.members && Array.isArray(household.members)) {
        const primary = household.members.find((m: any) => m.isPrimary);
        if (primary) {
          primaryMember = primary;
        }
      }

      return {
        id: household.id,
        name: household.householdName || 'Unnamed Household',
        email: household.email || primaryMember.email || '',
        phone: household.phone || primaryMember.phone || '',
        address: household.address || `${household.street || ''} ${household.city || ''} ${household.state || ''} ${household.zip_code || ''}`.trim(),
        occupation: household.occupation || '',
        dateOfBirth: household.date_of_birth,
        maritalStatus: household.marital_status || '',
        lastReview: household.last_review,
        nextReview: household.next_review,
        primaryAdvisor: household.primary_advisor || '',
        riskTolerance: household.risk_tolerance || '',
        totalAssets: household.total_assets || '',
        createdAt: household.created_at
      };
    });
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'name',
        header: 'Household Name',
      },
      {
        accessorKey: 'email',
        header: 'Email',
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
      },
      {
        accessorKey: 'address',
        header: 'Address',
      },
      {
        accessorKey: 'occupation',
        header: 'Occupation',
      },
      {
        accessorKey: 'maritalStatus',
        header: 'Marital Status',
      },
      {
        accessorKey: 'primaryAdvisor',
        header: 'Primary Advisor',
      },
      {
        accessorKey: 'riskTolerance',
        header: 'Risk Tolerance',
      },
      {
        accessorKey: 'totalAssets',
        header: 'Total Assets',
      },
      {
        accessorKey: 'lastReview',
        header: 'Last Review',
        cell: ({ row }) => {
          const date = row.getValue('lastReview');
          return date ? new Date(date as string).toLocaleDateString() : 'N/A';
        },
      },
      {
        accessorKey: 'nextReview',
        header: 'Next Review',
        cell: ({ row }) => {
          const date = row.getValue('nextReview');
          return date ? new Date(date as string).toLocaleDateString() : 'N/A';
        },
      }
    ];
  }
};

/**
 * Client Financial Snapshot Report
 */
export const clientFinancialSnapshotReport: ReportType = {
  id: 'financial-snapshot',
  name: 'Client Financial Snapshot',
  description: 'Comprehensive overview of a client\'s current financial situation',
  category: 'client',
  icon: Users,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      // Return empty data if no client ID is provided
      return {
        id: `financial-snapshot-${Date.now()}`,
        reportId: 'financial-snapshot',
        runAt: new Date(),
        parameters: params,
        data: [],
        columns: this.getColumns(),
        summary: {
          householdName: 'No Client Selected',
          totalAssets: 0,
          totalLiabilities: 0,
          netWorth: 0,
          annualIncome: 0,
          annualExpenses: 0,
          cashFlow: 0
        },
        recommendedChartType: 'bar'
      };
    }

    // Get household data - use a default if no client ID is provided
    let household = { householdName: 'No Client Selected' };

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', params.filters.clientId)
        .single();

      if (error) {
        throw new Error(`Error fetching household data: ${error.message}`);
      }

      household = data || household;
    }

    // Get assets - use an empty array if no client ID is provided
    let assets = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('assets')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching assets data: ${error.message}`);
      }

      assets = data || [];
    }

    // Get liabilities - use an empty array if no client ID is provided
    let liabilities = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('liabilities')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching liabilities data: ${error.message}`);
      }

      liabilities = data || [];
    }

    // Get income - use an empty array if no client ID is provided
    let income = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('income')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching income data: ${error.message}`);
      }

      income = data || [];
    }

    // Get expenses - use an empty array if no client ID is provided
    let expenses = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching expenses data: ${error.message}`);
      }

      expenses = data || [];
    }

    // Calculate totals
    const totalAssets = assets.reduce((sum, asset) => sum + (parseFloat(asset.value) || 0), 0);
    const totalLiabilities = liabilities.reduce((sum, liability) => sum + (parseFloat(liability.amount) || 0), 0);
    const netWorth = totalAssets - totalLiabilities;

    // Calculate annual income
    const annualIncome = income.reduce((sum, inc) => {
      let amount = parseFloat(inc.amount) || 0;

      // Convert to annual amount based on frequency
      switch (inc.frequency) {
        case 'weekly':
          amount *= 52;
          break;
        case 'fortnightly':
          amount *= 26;
          break;
        case 'monthly':
          amount *= 12;
          break;
        case 'quarterly':
          amount *= 4;
          break;
        case 'annually':
          // Already annual
          break;
      }

      return sum + amount;
    }, 0);

    // Calculate annual expenses
    const annualExpenses = expenses.reduce((sum, exp) => {
      let amount = parseFloat(exp.amount) || 0;

      // Convert to annual amount based on frequency
      switch (exp.frequency) {
        case 'weekly':
          amount *= 52;
          break;
        case 'fortnightly':
          amount *= 26;
          break;
        case 'monthly':
          amount *= 12;
          break;
        case 'quarterly':
          amount *= 4;
          break;
        case 'annually':
          // Already annual
          break;
      }

      return sum + amount;
    }, 0);

    // Prepare data for display
    const snapshotData = [
      {
        id: 1,
        category: 'Assets',
        value: totalAssets,
        details: `${assets.length} assets`
      },
      {
        id: 2,
        category: 'Liabilities',
        value: totalLiabilities,
        details: `${liabilities.length} liabilities`
      },
      {
        id: 3,
        category: 'Net Worth',
        value: netWorth,
        details: 'Assets - Liabilities'
      },
      {
        id: 4,
        category: 'Annual Income',
        value: annualIncome,
        details: `${income.length} income sources`
      },
      {
        id: 5,
        category: 'Annual Expenses',
        value: annualExpenses,
        details: `${expenses.length} expense categories`
      },
      {
        id: 6,
        category: 'Cash Flow',
        value: annualIncome - annualExpenses,
        details: 'Income - Expenses'
      }
    ];

    return {
      id: `financial-snapshot-${Date.now()}`,
      reportId: 'financial-snapshot',
      runAt: new Date(),
      parameters: params,
      data: snapshotData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        totalAssets,
        totalLiabilities,
        netWorth,
        annualIncome,
        annualExpenses,
        cashFlow: annualIncome - annualExpenses
      },
      chartData: {
        labels: ['Assets', 'Liabilities', 'Net Worth', 'Annual Income', 'Annual Expenses', 'Cash Flow'],
        datasets: [{
          label: 'Financial Overview',
          data: [totalAssets, totalLiabilities, netWorth, annualIncome, annualExpenses, annualIncome - annualExpenses],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ]
        }]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'category',
        header: 'Category',
      },
      {
        accessorKey: 'value',
        header: 'Value',
        cell: ({ row }) => {
          const value = row.getValue('value');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'details',
        header: 'Details',
      }
    ];
  }
};

/**
 * Client Portfolio Overview Report
 */
export const clientPortfolioOverviewReport: ReportType = {
  id: 'portfolio-overview',
  name: 'Client Portfolio Overview',
  description: 'Overview of a client\'s investment portfolio',
  category: 'client',
  icon: Users,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      // Return empty data if no client ID is provided
      return {
        id: `portfolio-overview-${Date.now()}`,
        reportId: 'portfolio-overview',
        runAt: new Date(),
        parameters: params,
        data: [],
        columns: this.getColumns(),
        summary: {
          householdName: 'No Client Selected',
          totalAssets: 0,
          assetCount: 0,
          assetTypes: 0
        },
        recommendedChartType: 'pie'
      };
    }

    // Get household data - use a default if no client ID is provided
    let household = { householdName: 'No Client Selected' };

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', params.filters.clientId)
        .single();

      if (error) {
        throw new Error(`Error fetching household data: ${error.message}`);
      }

      household = data || household;
    }

    // Get assets - use an empty array if no client ID is provided
    let assets = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('assets')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching assets data: ${error.message}`);
      }

      assets = data || [];
    }

    // Group assets by type
    const assetsByType: Record<string, number> = {};

    assets.forEach(asset => {
      const type = asset.type || 'Other';
      const value = parseFloat(asset.value) || 0;

      if (!assetsByType[type]) {
        assetsByType[type] = 0;
      }

      assetsByType[type] += value;
    });

    // Convert to array for display
    const portfolioData = Object.entries(assetsByType).map(([type, value], index) => ({
      id: index + 1,
      assetType: type,
      value,
      percentage: 0 // Will calculate after total is known
    }));

    // Calculate total and percentages
    const totalAssets = portfolioData.reduce((sum, item) => sum + item.value, 0);

    portfolioData.forEach(item => {
      item.percentage = totalAssets > 0 ? (item.value / totalAssets) * 100 : 0;
    });

    // Sort by value descending
    portfolioData.sort((a, b) => b.value - a.value);

    return {
      id: `portfolio-overview-${Date.now()}`,
      reportId: 'portfolio-overview',
      runAt: new Date(),
      parameters: params,
      data: portfolioData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        totalAssets,
        assetCount: assets.length,
        assetTypes: Object.keys(assetsByType).length
      },
      chartData: {
        labels: portfolioData.map(item => item.assetType),
        datasets: [{
          label: 'Asset Allocation',
          data: portfolioData.map(item => item.value),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(201, 203, 207, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'assetType',
        header: 'Asset Type',
      },
      {
        accessorKey: 'value',
        header: 'Value',
        cell: ({ row }) => {
          const value = row.getValue('value');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'percentage',
        header: 'Percentage',
        cell: ({ row }) => {
          const value = row.getValue('percentage');
          return typeof value === 'number'
            ? `${value.toFixed(2)}%`
            : value;
        },
      }
    ];
  }
};

/**
 * List of all client reports
 */
export const clientReports: ReportType[] = [
  clientDemographicsReport,
  clientFinancialSnapshotReport,
  clientPortfolioOverviewReport
];
