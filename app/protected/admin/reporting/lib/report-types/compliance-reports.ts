import { createClient } from '@/utils/supabase/client';
import { ReportType, ReportParams, ReportData } from '../types';
import { applyFilters } from '../data-fetching';
import { ColumnDef } from '@tanstack/react-table';
import { ClipboardCheck } from 'lucide-react';

/**
 * Review Schedule Report
 */
export const reviewScheduleReport: ReportType = {
  id: 'review-schedule',
  name: 'Review Schedule Report',
  description: 'Tracks client review schedules and completion',
  category: 'compliance',
  icon: ClipboardCheck,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households with review dates
    let query = supabase
      .from('households')
      .select('id, householdName, last_review, next_review, primary_advisor, org_id');

    // Apply filters
    query = applyFilters(query, params);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching household data: ${error.message}`);
    }

    // Transform data for display
    const today = new Date();
    const reviewData = data.map((household: any) => {
      const lastReview = household.last_review ? new Date(household.last_review) : null;
      const nextReview = household.next_review ? new Date(household.next_review) : null;

      // Calculate days until next review
      let daysUntilReview = null;
      let reviewStatus = 'Not Scheduled';

      if (nextReview) {
        daysUntilReview = Math.ceil((nextReview.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntilReview < 0) {
          reviewStatus = 'Overdue';
        } else if (daysUntilReview <= 30) {
          reviewStatus = 'Due Soon';
        } else {
          reviewStatus = 'Scheduled';
        }
      }

      // Calculate days since last review
      let daysSinceLastReview = null;

      if (lastReview) {
        daysSinceLastReview = Math.ceil((today.getTime() - lastReview.getTime()) / (1000 * 60 * 60 * 24));
      }

      return {
        id: household.id,
        householdName: household.householdName || 'Unnamed Household',
        lastReview: lastReview ? lastReview.toLocaleDateString() : 'Never',
        daysSinceLastReview,
        nextReview: nextReview ? nextReview.toLocaleDateString() : 'Not Scheduled',
        daysUntilReview,
        reviewStatus,
        primaryAdvisor: household.primary_advisor || 'Unassigned',
        orgId: household.org_id
      };
    });

    // Sort by days until review (ascending, with nulls at the end)
    reviewData.sort((a, b) => {
      if (a.daysUntilReview === null && b.daysUntilReview === null) return 0;
      if (a.daysUntilReview === null) return 1;
      if (b.daysUntilReview === null) return -1;
      return a.daysUntilReview - b.daysUntilReview;
    });

    // Count by status
    const statusCounts = {
      'Overdue': reviewData.filter(item => item.reviewStatus === 'Overdue').length,
      'Due Soon': reviewData.filter(item => item.reviewStatus === 'Due Soon').length,
      'Scheduled': reviewData.filter(item => item.reviewStatus === 'Scheduled').length,
      'Not Scheduled': reviewData.filter(item => item.reviewStatus === 'Not Scheduled').length
    };

    return {
      id: `review-schedule-${Date.now()}`,
      reportId: 'review-schedule',
      runAt: new Date(),
      parameters: params,
      data: reviewData,
      columns: this.getColumns(),
      summary: {
        totalClients: reviewData.length,
        overdueReviews: statusCounts['Overdue'],
        dueSoonReviews: statusCounts['Due Soon'],
        scheduledReviews: statusCounts['Scheduled'],
        notScheduledReviews: statusCounts['Not Scheduled']
      },
      chartData: {
        labels: Object.keys(statusCounts),
        datasets: [{
          label: 'Review Status',
          data: Object.values(statusCounts),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)', // Overdue
            'rgba(255, 206, 86, 0.6)', // Due Soon
            'rgba(75, 192, 192, 0.6)', // Scheduled
            'rgba(201, 203, 207, 0.6)' // Not Scheduled
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'householdName',
        header: 'Household',
      },
      {
        accessorKey: 'primaryAdvisor',
        header: 'Advisor',
      },
      {
        accessorKey: 'lastReview',
        header: 'Last Review',
      },
      {
        accessorKey: 'daysSinceLastReview',
        header: 'Days Since Last',
        cell: ({ row }: { row: any }) => {
          const value = row.getValue('daysSinceLastReview');
          return value !== null ? value : 'N/A';
        },
      },
      {
        accessorKey: 'nextReview',
        header: 'Next Review',
      },
      {
        accessorKey: 'daysUntilReview',
        header: 'Days Until Next',
        cell: ({ row }: { row: any }) => {
          const value = row.getValue('daysUntilReview');
          return value !== null ? value : 'N/A';
        },
      },
      {
        accessorKey: 'reviewStatus',
        header: 'Status',
        cell: ({ row }: { row: any }) => {
          const status = row.getValue('reviewStatus') as string;
          // Status styling will be handled in the UI
          return status;
        },
      }
    ];
  }
};

/**
 * Documentation Status Report
 */
export const documentationStatusReport: ReportType = {
  id: 'documentation-status',
  name: 'Documentation Status Report',
  description: 'Tracks status of required client documentation',
  category: 'compliance',
  icon: ClipboardCheck,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households
    let householdQuery = supabase
      .from('households')
      .select('id, householdName, primary_advisor, org_id');

    // Apply filters
    householdQuery = applyFilters(householdQuery, params);

    const { data: households, error: householdError } = await householdQuery;

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Get attachments
    const { data: attachments, error: attachmentsError } = await supabase
      .from('attachments')
      .select('*');

    if (attachmentsError) {
      throw new Error(`Error fetching attachments data: ${attachmentsError.message}`);
    }

    // Get SOA documents
    const { data: soaDocuments, error: soaError } = await supabase
      .from('soa_documents')
      .select('*');

    if (soaError) {
      throw new Error(`Error fetching SOA documents data: ${soaError.message}`);
    }

    // Get other documents
    const { data: otherDocuments, error: otherDocsError } = await supabase
      .from('other_documents')
      .select('*');

    if (otherDocsError) {
      throw new Error(`Error fetching other documents data: ${otherDocsError.message}`);
    }

    // Define required document types
    const requiredDocTypes = [
      'Identification',
      'Financial Services Guide',
      'Statement of Advice',
      'Fee Disclosure Statement',
      'Client Agreement',
      'Risk Profile',
      'Privacy Policy'
    ];

    // Process documentation status for each household
    const documentationData = households.map((household: any) => {
      // Get documents for this household
      const householdAttachments = attachments.filter((att: any) => att.household_id === household.id);
      const householdSoaDocuments = soaDocuments.filter((soa: any) => soa.household_id === household.id);
      const householdOtherDocuments = otherDocuments.filter((doc: any) => doc.household_id === household.id);

      // Combine all documents
      const allDocuments = [
        ...householdAttachments,
        ...householdSoaDocuments,
        ...householdOtherDocuments
      ];

      // Check which required documents are present
      const documentStatus: Record<string, boolean> = {};

      requiredDocTypes.forEach(docType => {
        documentStatus[docType] = allDocuments.some((doc: any) =>
          doc.type === docType ||
          doc.name?.includes(docType) ||
          doc.description?.includes(docType)
        );
      });

      // Calculate completion percentage
      const completedDocs = Object.values(documentStatus).filter(status => status).length;
      const completionPercentage = (completedDocs / requiredDocTypes.length) * 100;

      // Determine overall status
      let overallStatus = 'Incomplete';
      if (completionPercentage === 100) {
        overallStatus = 'Complete';
      } else if (completionPercentage >= 75) {
        overallStatus = 'Mostly Complete';
      } else if (completionPercentage >= 50) {
        overallStatus = 'Partially Complete';
      }

      return {
        id: household.id,
        householdName: household.householdName || 'Unnamed Household',
        primaryAdvisor: household.primary_advisor || 'Unassigned',
        orgId: household.org_id,
        documentCount: allDocuments.length,
        completedDocs,
        totalRequiredDocs: requiredDocTypes.length,
        completionPercentage,
        overallStatus,
        documentStatus
      };
    });

    // Sort by completion percentage (ascending)
    documentationData.sort((a, b) => a.completionPercentage - b.completionPercentage);

    // Count by status
    const statusCounts = {
      'Complete': documentationData.filter(item => item.overallStatus === 'Complete').length,
      'Mostly Complete': documentationData.filter(item => item.overallStatus === 'Mostly Complete').length,
      'Partially Complete': documentationData.filter(item => item.overallStatus === 'Partially Complete').length,
      'Incomplete': documentationData.filter(item => item.overallStatus === 'Incomplete').length
    };

    // Prepare data for display
    const displayData = documentationData.map(household => {
      // Create a row for each household with document status
      const row: any = {
        id: household.id,
        householdName: household.householdName,
        primaryAdvisor: household.primaryAdvisor,
        completionPercentage: household.completionPercentage,
        overallStatus: household.overallStatus
      };

      // Add columns for each document type
      requiredDocTypes.forEach(docType => {
        row[docType.replace(/\s+/g, '')] = household.documentStatus[docType] ? 'Yes' : 'No';
      });

      return row;
    });

    return {
      id: `documentation-status-${Date.now()}`,
      reportId: 'documentation-status',
      runAt: new Date(),
      parameters: params,
      data: displayData,
      columns: this.getColumns(),
      summary: {
        totalClients: documentationData.length,
        completeDocumentation: statusCounts['Complete'],
        mostlyCompleteDocumentation: statusCounts['Mostly Complete'],
        partiallyCompleteDocumentation: statusCounts['Partially Complete'],
        incompleteDocumentation: statusCounts['Incomplete'],
        averageCompletionPercentage: documentationData.reduce((sum, item) => sum + item.completionPercentage, 0) / documentationData.length
      },
      chartData: {
        labels: Object.keys(statusCounts),
        datasets: [{
          label: 'Documentation Status',
          data: Object.values(statusCounts),
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)', // Complete
            'rgba(54, 162, 235, 0.6)', // Mostly Complete
            'rgba(255, 206, 86, 0.6)', // Partially Complete
            'rgba(255, 99, 132, 0.6)'  // Incomplete
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    // Store required document types in a class property to access it later
    const requiredDocTypes = [
      'Identification',
      'Financial Services Guide',
      'Statement of Advice',
      'Fee Disclosure Statement',
      'Client Agreement',
      'Risk Profile',
      'Privacy Policy'
    ];

    const baseColumns: ColumnDef<any, any>[] = [
      {
        accessorKey: 'householdName',
        header: 'Household',
      },
      {
        accessorKey: 'primaryAdvisor',
        header: 'Advisor',
      },
      {
        accessorKey: 'completionPercentage',
        header: 'Completion',
        cell: ({ row }: { row: any }) => {
          const value = row.getValue('completionPercentage') as number;
          return typeof value === 'number'
            ? `${value.toFixed(0)}%`
            : value;
        },
      },
      {
        accessorKey: 'overallStatus',
        header: 'Status',
        cell: ({ row }: { row: any }) => {
          const status = row.getValue('overallStatus') as string;
          // Status styling will be handled in the UI
          return status;
        },
      }
    ];

    // Add columns for each document type
    const docColumns = requiredDocTypes.map(docType => ({
      accessorKey: docType.replace(/\s+/g, ''),
      header: docType,
      cell: ({ row }: { row: any }) => {
        const value = row.getValue(docType.replace(/\s+/g, '')) as string;
        return value === 'Yes' ? '✓' : '✗';
      },
    }));

    return [...baseColumns, ...docColumns];
  }
};

/**
 * Regulatory Requirements Report
 */
export const regulatoryRequirementsReport: ReportType = {
  id: 'regulatory-requirements',
  name: 'Regulatory Requirements Report',
  description: 'Tracks compliance with regulatory requirements',
  category: 'compliance',
  icon: ClipboardCheck,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households
    let householdQuery = supabase
      .from('households')
      .select('id, householdName, primary_advisor, org_id, last_review, created_at');

    // Apply filters
    householdQuery = applyFilters(householdQuery, params);

    const { data: households, error: householdError } = await householdQuery;

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Get SOA documents
    const { data: soaDocuments, error: soaError } = await supabase
      .from('soa_documents')
      .select('*');

    if (soaError) {
      throw new Error(`Error fetching SOA documents data: ${soaError.message}`);
    }

    // Get other documents
    const { data: otherDocuments, error: otherDocsError } = await supabase
      .from('other_documents')
      .select('*');

    if (otherDocsError) {
      throw new Error(`Error fetching other documents data: ${otherDocsError.message}`);
    }

    // Define regulatory requirements
    const regulatoryRequirements = [
      {
        id: 'annual-review',
        name: 'Annual Review',
        description: 'Client must have an annual review',
        checkFunction: (household: any, _docs: any[]) => {
          if (!household.last_review) return false;

          const lastReview = new Date(household.last_review);
          const today = new Date();
          const daysSinceLastReview = Math.ceil((today.getTime() - lastReview.getTime()) / (1000 * 60 * 60 * 24));

          return daysSinceLastReview <= 365;
        }
      },
      {
        id: 'fee-disclosure',
        name: 'Fee Disclosure Statement',
        description: 'Client must have a current Fee Disclosure Statement',
        checkFunction: (_household: any, docs: any[]) => {
          const fdsDocuments = docs.filter((doc: any) =>
            doc.type === 'Fee Disclosure Statement' ||
            doc.name?.includes('Fee Disclosure') ||
            doc.description?.includes('Fee Disclosure')
          );

          if (fdsDocuments.length === 0) return false;

          // Check if any FDS is less than a year old
          const today = new Date();
          return fdsDocuments.some((doc: any) => {
            if (!doc.created_at) return false;

            const docDate = new Date(doc.created_at);
            const daysSinceCreation = Math.ceil((today.getTime() - docDate.getTime()) / (1000 * 60 * 60 * 24));

            return daysSinceCreation <= 365;
          });
        }
      },
      {
        id: 'client-agreement',
        name: 'Client Agreement',
        description: 'Client must have a signed Client Agreement',
        checkFunction: (_household: any, docs: any[]) => {
          return docs.some((doc: any) =>
            doc.type === 'Client Agreement' ||
            doc.name?.includes('Client Agreement') ||
            doc.description?.includes('Client Agreement')
          );
        }
      },
      {
        id: 'privacy-policy',
        name: 'Privacy Policy',
        description: 'Client must have acknowledged the Privacy Policy',
        checkFunction: (_household: any, docs: any[]) => {
          return docs.some((doc: any) =>
            doc.type === 'Privacy Policy' ||
            doc.name?.includes('Privacy Policy') ||
            doc.description?.includes('Privacy Policy')
          );
        }
      },
      {
        id: 'identification',
        name: 'Client Identification',
        description: 'Client must have identification documents on file',
        checkFunction: (_household: any, docs: any[]) => {
          return docs.some((doc: any) =>
            doc.type === 'Identification' ||
            doc.name?.includes('ID') ||
            doc.name?.includes('Identification') ||
            doc.description?.includes('Identification')
          );
        }
      },
      {
        id: 'soa',
        name: 'Statement of Advice',
        description: 'Client must have a Statement of Advice',
        checkFunction: (_household: any, docs: any[]) => {
          return docs.some((doc: any) =>
            doc.type === 'Statement of Advice' ||
            doc.name?.includes('SOA') ||
            doc.name?.includes('Statement of Advice') ||
            doc.description?.includes('Statement of Advice')
          );
        }
      }
    ];

    // Process regulatory compliance for each household
    const complianceData = households.map((household: any) => {
      // Get documents for this household
      const householdSoaDocuments = soaDocuments.filter((soa: any) => soa.household_id === household.id);
      const householdOtherDocuments = otherDocuments.filter((doc: any) => doc.household_id === household.id);

      // Combine all documents
      const allDocuments = [
        ...householdSoaDocuments,
        ...householdOtherDocuments
      ];

      // Check compliance with each requirement
      const requirementStatus: Record<string, boolean> = {};

      regulatoryRequirements.forEach(req => {
        requirementStatus[req.id] = req.checkFunction(household, allDocuments);
      });

      // Calculate compliance percentage
      const metRequirements = Object.values(requirementStatus).filter(status => status).length;
      const compliancePercentage = (metRequirements / regulatoryRequirements.length) * 100;

      // Determine overall compliance status
      let complianceStatus = 'Non-Compliant';
      if (compliancePercentage === 100) {
        complianceStatus = 'Fully Compliant';
      } else if (compliancePercentage >= 80) {
        complianceStatus = 'Mostly Compliant';
      } else if (compliancePercentage >= 50) {
        complianceStatus = 'Partially Compliant';
      }

      return {
        id: household.id,
        householdName: household.householdName || 'Unnamed Household',
        primaryAdvisor: household.primary_advisor || 'Unassigned',
        orgId: household.org_id,
        metRequirements,
        totalRequirements: regulatoryRequirements.length,
        compliancePercentage,
        complianceStatus,
        requirementStatus
      };
    });

    // Sort by compliance percentage (ascending)
    complianceData.sort((a, b) => a.compliancePercentage - b.compliancePercentage);

    // Count by status
    const statusCounts = {
      'Fully Compliant': complianceData.filter(item => item.complianceStatus === 'Fully Compliant').length,
      'Mostly Compliant': complianceData.filter(item => item.complianceStatus === 'Mostly Compliant').length,
      'Partially Compliant': complianceData.filter(item => item.complianceStatus === 'Partially Compliant').length,
      'Non-Compliant': complianceData.filter(item => item.complianceStatus === 'Non-Compliant').length
    };

    // Prepare data for display
    const displayData = complianceData.map(household => {
      // Create a row for each household with requirement status
      const row: any = {
        id: household.id,
        householdName: household.householdName,
        primaryAdvisor: household.primaryAdvisor,
        compliancePercentage: household.compliancePercentage,
        complianceStatus: household.complianceStatus
      };

      // Add columns for each requirement
      regulatoryRequirements.forEach(req => {
        row[req.id] = household.requirementStatus[req.id] ? 'Yes' : 'No';
      });

      return row;
    });

    return {
      id: `regulatory-requirements-${Date.now()}`,
      reportId: 'regulatory-requirements',
      runAt: new Date(),
      parameters: params,
      data: displayData,
      columns: this.getColumns(),
      summary: {
        totalClients: complianceData.length,
        fullyCompliant: statusCounts['Fully Compliant'],
        mostlyCompliant: statusCounts['Mostly Compliant'],
        partiallyCompliant: statusCounts['Partially Compliant'],
        nonCompliant: statusCounts['Non-Compliant'],
        averageCompliancePercentage: complianceData.reduce((sum, item) => sum + item.compliancePercentage, 0) / complianceData.length
      },
      chartData: {
        labels: Object.keys(statusCounts),
        datasets: [{
          label: 'Compliance Status',
          data: Object.values(statusCounts),
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)', // Fully Compliant
            'rgba(54, 162, 235, 0.6)', // Mostly Compliant
            'rgba(255, 206, 86, 0.6)', // Partially Compliant
            'rgba(255, 99, 132, 0.6)'  // Non-Compliant
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    // Define regulatory requirements in the method to access them
    const regulatoryRequirements = [
      {
        id: 'annual-review',
        name: 'Annual Review',
      },
      {
        id: 'fee-disclosure',
        name: 'Fee Disclosure Statement',
      },
      {
        id: 'client-agreement',
        name: 'Client Agreement',
      },
      {
        id: 'privacy-policy',
        name: 'Privacy Policy',
      },
      {
        id: 'identification',
        name: 'Client Identification',
      },
      {
        id: 'soa',
        name: 'Statement of Advice',
      }
    ];

    const baseColumns: ColumnDef<any, any>[] = [
      {
        accessorKey: 'householdName',
        header: 'Household',
      },
      {
        accessorKey: 'primaryAdvisor',
        header: 'Advisor',
      },
      {
        accessorKey: 'compliancePercentage',
        header: 'Compliance',
        cell: ({ row }: { row: any }) => {
          const value = row.getValue('compliancePercentage') as number;
          return typeof value === 'number'
            ? `${value.toFixed(0)}%`
            : value;
        },
      },
      {
        accessorKey: 'complianceStatus',
        header: 'Status',
        cell: ({ row }: { row: any }) => {
          const status = row.getValue('complianceStatus') as string;
          // Status styling will be handled in the UI
          return status;
        },
      }
    ];

    // Add columns for each requirement
    const reqColumns = regulatoryRequirements.map(req => ({
      accessorKey: req.id,
      header: req.name,
      cell: ({ row }: { row: any }) => {
        const value = row.getValue(req.id) as string;
        return value === 'Yes' ? '✓' : '✗';
      },
    }));

    return [...baseColumns, ...reqColumns];
  }
};

/**
 * List of all compliance reports
 */
export const complianceReports: ReportType[] = [
  reviewScheduleReport,
  documentationStatusReport,
  regulatoryRequirementsReport
];
