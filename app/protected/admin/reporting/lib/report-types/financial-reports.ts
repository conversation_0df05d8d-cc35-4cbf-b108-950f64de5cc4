import { createClient } from '@/utils/supabase/client';
import { ReportType, ReportParams, ReportData } from '../types';
import { applyFilters } from '../data-fetching';
import { ColumnDef } from '@tanstack/react-table';
import { Bar<PERSON><PERSON> } from 'lucide-react';

/**
 * Asset Allocation Report
 */
export const assetAllocationReport: ReportType = {
  id: 'asset-allocation',
  name: 'Asset Allocation Report',
  description: 'Detailed breakdown of assets by type and category',
  category: 'financial',
  icon: BarChart,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get assets with household information
    let query = supabase
      .from('assets')
      .select('*, households(householdName, id)');

    // Apply filters to the households part of the query
    if (params.filters.clientFilter === 'specific' && params.filters.clientId) {
      query = query.eq('household_id', params.filters.clientId);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching asset data: ${error.message}`);
    }

    // Group assets by type
    const assetsByType: Record<string, number> = {};

    data.forEach((asset: any) => {
      const type = asset.type || 'Other';
      const value = parseFloat(asset.value) || 0;

      if (!assetsByType[type]) {
        assetsByType[type] = 0;
      }

      assetsByType[type] += value;
    });

    // Convert to array for display
    const allocationData = Object.entries(assetsByType).map(([type, value], index) => ({
      id: index + 1,
      assetType: type,
      value,
      percentage: 0 // Will calculate after total is known
    }));

    // Calculate total and percentages
    const totalAssets = allocationData.reduce((sum, item) => sum + item.value, 0);

    allocationData.forEach(item => {
      item.percentage = totalAssets > 0 ? (item.value / totalAssets) * 100 : 0;
    });

    // Sort by value descending
    allocationData.sort((a, b) => b.value - a.value);

    // Transform the original data for detailed view
    const transformedData = data.map((asset: any) => ({
      id: asset.id,
      name: asset.name || 'Unnamed Asset',
      type: asset.type || 'Other',
      value: parseFloat(asset.value) || 0,
      householdName: asset.households?.householdName || 'Unknown Household',
      householdId: asset.households?.id || asset.household_id,
      provider: asset.provider || '',
      details: asset.details || ''
    }));

    return {
      id: `asset-allocation-${Date.now()}`,
      reportId: 'asset-allocation',
      runAt: new Date(),
      parameters: params,
      data: transformedData,
      columns: this.getColumns(),
      summary: {
        totalAssets,
        assetCount: data.length,
        assetTypes: Object.keys(assetsByType).length,
        allocation: allocationData
      },
      chartData: {
        labels: allocationData.map(item => item.assetType),
        datasets: [{
          label: 'Asset Allocation',
          data: allocationData.map(item => item.value),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(201, 203, 207, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'householdName',
        header: 'Household',
      },
      {
        accessorKey: 'name',
        header: 'Asset Name',
      },
      {
        accessorKey: 'type',
        header: 'Asset Type',
      },
      {
        accessorKey: 'value',
        header: 'Value',
        cell: ({ row }) => {
          const value = row.getValue('value');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'provider',
        header: 'Provider',
      },
      {
        accessorKey: 'details',
        header: 'Details',
      }
    ];
  }
};

/**
 * Liability Analysis Report
 */
export const liabilityAnalysisReport: ReportType = {
  id: 'liability-analysis',
  name: 'Liability Analysis Report',
  description: 'Analysis of client liabilities and debt',
  category: 'financial',
  icon: BarChart,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get liabilities with household information
    let query = supabase
      .from('liabilities')
      .select('*, households(householdName, id)');

    // Apply filters to the households part of the query
    if (params.filters.clientFilter === 'specific' && params.filters.clientId) {
      query = query.eq('household_id', params.filters.clientId);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching liability data: ${error.message}`);
    }

    // Group liabilities by type
    const liabilitiesByType: Record<string, number> = {};

    data.forEach((liability: any) => {
      const type = liability.type || 'Other';
      const amount = parseFloat(liability.amount) || 0;

      if (!liabilitiesByType[type]) {
        liabilitiesByType[type] = 0;
      }

      liabilitiesByType[type] += amount;
    });

    // Convert to array for display
    const analysisData = Object.entries(liabilitiesByType).map(([type, amount], index) => ({
      id: index + 1,
      liabilityType: type,
      amount,
      percentage: 0 // Will calculate after total is known
    }));

    // Calculate total and percentages
    const totalLiabilities = analysisData.reduce((sum, item) => sum + item.amount, 0);

    analysisData.forEach(item => {
      item.percentage = totalLiabilities > 0 ? (item.amount / totalLiabilities) * 100 : 0;
    });

    // Sort by amount descending
    analysisData.sort((a, b) => b.amount - a.amount);

    // Transform the original data for detailed view
    const transformedData = data.map((liability: any) => ({
      id: liability.id,
      name: liability.name || 'Unnamed Liability',
      type: liability.type || 'Other',
      amount: parseFloat(liability.amount) || 0,
      interestRate: liability.interest_rate || 0,
      householdName: liability.households?.householdName || 'Unknown Household',
      householdId: liability.households?.id || liability.household_id,
      provider: liability.provider || '',
      details: liability.details || ''
    }));

    return {
      id: `liability-analysis-${Date.now()}`,
      reportId: 'liability-analysis',
      runAt: new Date(),
      parameters: params,
      data: transformedData,
      columns: this.getColumns(),
      summary: {
        totalLiabilities,
        liabilityCount: data.length,
        liabilityTypes: Object.keys(liabilitiesByType).length,
        analysis: analysisData
      },
      chartData: {
        labels: analysisData.map(item => item.liabilityType),
        datasets: [{
          label: 'Liability Distribution',
          data: analysisData.map(item => item.amount),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ]
        }]
      },
      recommendedChartType: 'pie'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'householdName',
        header: 'Household',
      },
      {
        accessorKey: 'name',
        header: 'Liability Name',
      },
      {
        accessorKey: 'type',
        header: 'Liability Type',
      },
      {
        accessorKey: 'amount',
        header: 'Amount',
        cell: ({ row }) => {
          const value = row.getValue('amount');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'interestRate',
        header: 'Interest Rate',
        cell: ({ row }) => {
          const value = row.getValue('interestRate');
          return typeof value === 'number'
            ? `${value}%`
            : value;
        },
      },
      {
        accessorKey: 'provider',
        header: 'Provider',
      }
    ];
  }
};

/**
 * Net Worth Trends Report
 */
export const netWorthTrendsReport: ReportType = {
  id: 'net-worth-trend',
  name: 'Net Worth Trend Report',
  description: 'Historical analysis of client net worth over time',
  category: 'financial',
  icon: BarChart,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // For this report, we'll simulate historical data since we don't have actual historical records
    // In a real implementation, you would fetch historical snapshots from a time-series table

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      throw new Error('Client ID is required for Net Worth Trend report');
    }

    // Get current assets and liabilities with specific column selection
    const { data: assets, error: assetsError } = await supabase
      .from('assets')
      .select('id, name, value, asset_type, description, purchase_date, current_value, household_id, created_at')
      .eq('household_id', params.filters.clientId);

    if (assetsError) {
      throw new Error(`Error fetching assets data: ${assetsError.message}`);
    }

    const { data: liabilities, error: liabilitiesError } = await supabase
      .from('liabilities')
      .select('*')
      .eq('household_id', params.filters.clientId);

    if (liabilitiesError) {
      throw new Error(`Error fetching liabilities data: ${liabilitiesError.message}`);
    }

    // Get household info
    const { data: household, error: householdError } = await supabase
      .from('households')
      .select('*')
      .eq('id', params.filters.clientId)
      .single();

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Calculate current totals
    const totalAssets = assets.reduce((sum, asset) => sum + (parseFloat(asset.value) || 0), 0);
    const totalLiabilities = liabilities.reduce((sum, liability) => sum + (parseFloat(liability.amount) || 0), 0);
    const currentNetWorth = totalAssets - totalLiabilities;

    // Generate simulated historical data
    // For demonstration purposes, we'll create 12 months of data
    const today = new Date();
    const months = [];
    const assetValues: any[] = [];
    const liabilityValues: any[] = [];
    const netWorthValues: number[] = [];

    // Start from 11 months ago
    for (let i = 11; i >= 0; i--) {
      const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short', year: 'numeric' });

      // Simulate some variation in the values
      // In a real implementation, you would use actual historical data
      const randomFactor = 0.95 + (Math.random() * 0.1); // Between 0.95 and 1.05
      const growthFactor = 1 + (0.005 * (11 - i)); // Slight growth over time

      const monthAssets = totalAssets * randomFactor * growthFactor;
      const monthLiabilities = totalLiabilities * (0.98 + (Math.random() * 0.04)); // Slight variation in liabilities
      const monthNetWorth = monthAssets - monthLiabilities;

      months.push(monthName);
      assetValues.push(monthAssets);
      liabilityValues.push(monthLiabilities);
      netWorthValues.push(monthNetWorth);
    }

    // Create data for table display
    const trendData = months.map((month, index) => ({
      id: index + 1,
      month,
      assets: assetValues[index],
      liabilities: liabilityValues[index],
      netWorth: netWorthValues[index]
    }));

    return {
      id: `net-worth-trend-${Date.now()}`,
      reportId: 'net-worth-trend',
      runAt: new Date(),
      parameters: params,
      data: trendData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        currentAssets: totalAssets,
        currentLiabilities: totalLiabilities,
        currentNetWorth,
        netWorthChange: netWorthValues[netWorthValues.length - 1] - netWorthValues[0],
        netWorthChangePercentage: ((netWorthValues[netWorthValues.length - 1] - netWorthValues[0]) / netWorthValues[0]) * 100
      },
      chartData: {
        labels: months,
        datasets: [
          {
            label: 'Assets',
            data: assetValues,
            backgroundColor: ['rgba($1)'],
            borderColor: 'rgba(75, 192, 192, 1)',
            fill: true
          },
          {
            label: 'Liabilities',
            data: liabilityValues,
            backgroundColor: ['rgba($1)'],
            borderColor: 'rgba(255, 99, 132, 1)',
            fill: true
          },
          {
            label: 'Net Worth',
            data: netWorthValues,
            backgroundColor: ['rgba($1)'],
            borderColor: 'rgba(54, 162, 235, 1)',
            fill: true
          }
        ]
      },
      recommendedChartType: 'line'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'month',
        header: 'Month',
      },
      {
        accessorKey: 'assets',
        header: 'Assets',
        cell: ({ row }) => {
          const value = row.getValue('assets');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'liabilities',
        header: 'Liabilities',
        cell: ({ row }) => {
          const value = row.getValue('liabilities');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'netWorth',
        header: 'Net Worth',
        cell: ({ row }) => {
          const value = row.getValue('netWorth');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      }
    ];
  }
};

/**
 * Cash Flow Analysis Report
 */
export const cashFlowAnalysisReport: ReportType = {
  id: 'cash-flow-analysis',
  name: 'Cash Flow Analysis Report',
  description: 'Analysis of income and expenses to determine cash flow',
  category: 'financial',
  icon: BarChart,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      throw new Error('Client ID is required for Cash Flow Analysis report');
    }

    // Get income data
    const { data: income, error: incomeError } = await supabase
      .from('income')
      .select('*')
      .eq('household_id', params.filters.clientId);

    if (incomeError) {
      throw new Error(`Error fetching income data: ${incomeError.message}`);
    }

    // Get expense data
    const { data: expenses, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('household_id', params.filters.clientId);

    if (expensesError) {
      throw new Error(`Error fetching expenses data: ${expensesError.message}`);
    }

    // Get household info
    const { data: household, error: householdError } = await supabase
      .from('households')
      .select('*')
      .eq('id', params.filters.clientId)
      .single();

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Normalize income to monthly amounts
    const monthlyIncome = income.map((inc: any) => {
      let amount = parseFloat(inc.amount) || 0;
      let monthlyAmount = amount;

      // Convert to monthly amount based on frequency
      switch (inc.frequency) {
        case 'weekly':
          monthlyAmount = amount * 4.33; // Average weeks in a month
          break;
        case 'fortnightly':
          monthlyAmount = amount * 2.17; // Average fortnights in a month
          break;
        case 'monthly':
          // Already monthly
          break;
        case 'quarterly':
          monthlyAmount = amount / 3;
          break;
        case 'annually':
          monthlyAmount = amount / 12;
          break;
      }

      return {
        id: inc.id,
        source: inc.source || 'Unnamed Income',
        type: inc.income_type || 'Other',
        originalAmount: amount,
        frequency: inc.frequency || 'monthly',
        monthlyAmount,
        details: inc.details || ''
      };
    });

    // Normalize expenses to monthly amounts
    const monthlyExpenses = expenses.map((exp: any) => {
      let amount = parseFloat(exp.amount) || 0;
      let monthlyAmount = amount;

      // Convert to monthly amount based on frequency
      switch (exp.frequency) {
        case 'weekly':
          monthlyAmount = amount * 4.33; // Average weeks in a month
          break;
        case 'fortnightly':
          monthlyAmount = amount * 2.17; // Average fortnights in a month
          break;
        case 'monthly':
          // Already monthly
          break;
        case 'quarterly':
          monthlyAmount = amount / 3;
          break;
        case 'annually':
          monthlyAmount = amount / 12;
          break;
      }

      return {
        id: exp.id,
        category: exp.category || 'Unnamed Expense',
        originalAmount: amount,
        frequency: exp.frequency || 'monthly',
        monthlyAmount,
        details: exp.details || ''
      };
    });

    // Calculate totals
    const totalMonthlyIncome = monthlyIncome.reduce((sum, inc) => sum + inc.monthlyAmount, 0);
    const totalMonthlyExpenses = monthlyExpenses.reduce((sum, exp) => sum + exp.monthlyAmount, 0);
    const monthlyCashFlow = totalMonthlyIncome - totalMonthlyExpenses;

    // Group income by type
    const incomeByType: Record<string, number> = {};
    monthlyIncome.forEach(inc => {
      const type = inc.type || 'Other';
      if (!incomeByType[type]) {
        incomeByType[type] = 0;
      }
      incomeByType[type] += inc.monthlyAmount;
    });

    // Group expenses by category
    const expensesByCategory: Record<string, number> = {};
    monthlyExpenses.forEach(exp => {
      const category = exp.category || 'Other';
      if (!expensesByCategory[category]) {
        expensesByCategory[category] = 0;
      }
      expensesByCategory[category] += exp.monthlyAmount;
    });

    // Combine income and expenses for display
    const cashFlowData = [
      ...monthlyIncome.map(inc => ({
        id: `income-${inc.id}`,
        type: 'Income',
        category: inc.source,
        subcategory: inc.type,
        frequency: inc.frequency,
        monthlyAmount: inc.monthlyAmount,
        details: inc.details
      })),
      ...monthlyExpenses.map(exp => ({
        id: `expense-${exp.id}`,
        type: 'Expense',
        category: exp.category,
        subcategory: '',
        frequency: exp.frequency,
        monthlyAmount: -exp.monthlyAmount, // Negative for expenses
        details: exp.details
      }))
    ];

    return {
      id: `cash-flow-analysis-${Date.now()}`,
      reportId: 'cash-flow-analysis',
      runAt: new Date(),
      parameters: params,
      data: cashFlowData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        totalMonthlyIncome,
        totalMonthlyExpenses,
        monthlyCashFlow,
        annualCashFlow: monthlyCashFlow * 12,
        incomeByType,
        expensesByCategory
      },
      chartData: {
        labels: [
          ...Object.keys(incomeByType).map(type => `Income: ${type}`),
          ...Object.keys(expensesByCategory).map(category => `Expense: ${category}`)
        ],
        datasets: [{
          label: 'Monthly Cash Flow',
          data: [
            ...Object.values(incomeByType),
            ...Object.values(expensesByCategory).map(amount => -amount)
          ],
          backgroundColor: [
            ...Array(Object.keys(incomeByType).length).fill('rgba(75, 192, 192, 0.6)'),
            ...Array(Object.keys(expensesByCategory).length).fill('rgba(255, 99, 132, 0.6)')
          ]
        }]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'type',
        header: 'Type',
      },
      {
        accessorKey: 'category',
        header: 'Category',
      },
      {
        accessorKey: 'subcategory',
        header: 'Subcategory',
      },
      {
        accessorKey: 'frequency',
        header: 'Frequency',
        cell: ({ row }) => {
          const value = row.getValue('frequency');
          return typeof value === 'string'
            ? value.charAt(0).toUpperCase() + value.slice(1)
            : value;
        },
      },
      {
        accessorKey: 'monthlyAmount',
        header: 'Monthly Amount',
        cell: ({ row }) => {
          const value = row.getValue('monthlyAmount');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'details',
        header: 'Details',
      }
    ];
  }
};

/**
 * List of all financial reports
 */
export const financialReports: ReportType[] = [
  assetAllocationReport,
  liabilityAnalysisReport,
  netWorthTrendsReport,
  cashFlowAnalysisReport
];

