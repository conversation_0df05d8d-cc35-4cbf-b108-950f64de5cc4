import { createClient } from '@/utils/supabase/client';
import { ReportType, ReportParams, ReportData } from '../types';
import { applyFilters } from '../data-fetching';
import { ColumnDef } from '@tanstack/react-table';
import { Building } from 'lucide-react';

/**
 * Client Acquisition Report
 */
export const clientAcquisitionReport: ReportType = {
  id: 'client-acquisition',
  name: 'Client Acquisition Report',
  description: 'Analysis of new client acquisition',
  category: 'operational',
  icon: Building,
  defaultParams: {
    filters: {
      dateRange: 'this-year',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households with creation date
    let query = supabase
      .from('households')
      .select('id, householdName, created_at, org_id, primary_advisor');

    // Apply filters
    query = applyFilters(query, params);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Error fetching household data: ${error.message}`);
    }

    // Group by month
    const acquisitionByMonth: Record<string, number> = {};
    const acquisitionByAdvisor: Record<string, number> = {};

    data.forEach((household: any) => {
      if (household.created_at) {
        const date = new Date(household.created_at);
        const monthName = date.toLocaleString('default', { month: 'short', year: 'numeric' });

        if (!acquisitionByMonth[monthName]) {
          acquisitionByMonth[monthName] = 0;
        }

        acquisitionByMonth[monthName]++;

        // Group by advisor
        const advisor = household.primary_advisor || 'Unassigned';
        if (!acquisitionByAdvisor[advisor]) {
          acquisitionByAdvisor[advisor] = 0;
        }

        acquisitionByAdvisor[advisor]++;
      }
    });

    // Convert to array and sort by date
    const monthlyData = Object.entries(acquisitionByMonth).map(([month, count]) => ({
      month,
      count
    })).sort((a, b) => {
      const dateA = new Date(a.month);
      const dateB = new Date(b.month);
      return dateA.getTime() - dateB.getTime();
    });

    // Convert advisor data to array
    const advisorData = Object.entries(acquisitionByAdvisor).map(([advisor, count]) => ({
      advisor,
      count
    })).sort((a, b) => b.count - a.count);

    // Combine data for display
    const acquisitionData = [
      ...monthlyData.map((item, index) => ({
        id: `month-${index}`,
        category: 'Month',
        name: item.month,
        count: item.count
      })),
      ...advisorData.map((item, index) => ({
        id: `advisor-${index}`,
        category: 'Advisor',
        name: item.advisor,
        count: item.count
      }))
    ];

    return {
      id: `client-acquisition-${Date.now()}`,
      reportId: 'client-acquisition',
      runAt: new Date(),
      parameters: params,
      data: acquisitionData,
      columns: this.getColumns(),
      summary: {
        totalClients: data.length,
        monthlyAcquisition: monthlyData,
        advisorAcquisition: advisorData
      },
      chartData: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
          label: 'New Clients',
          data: monthlyData.map(item => item.count),
          backgroundColor: ['rgba(75, 192, 192, 0.6)'],
          borderColor: 'rgba(75, 192, 192, 1)',
          fill: false
        }]
      },
      recommendedChartType: 'line'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'category',
        header: 'Category',
      },
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'count',
        header: 'Client Count',
      }
    ];
  }
};

/**
 * Advisor Productivity Report
 */
export const advisorProductivityReport: ReportType = {
  id: 'advisor-productivity',
  name: 'Advisor Productivity Report',
  description: 'Analysis of advisor productivity and performance',
  category: 'operational',
  icon: Building,
  defaultParams: {
    filters: {
      dateRange: 'this-year',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households with advisor information
    let householdQuery = supabase
      .from('households')
      .select('id, householdName, primary_advisor, created_at');

    // Apply filters
    householdQuery = applyFilters(householdQuery, params);

    const { data: households, error: householdError } = await householdQuery;

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Get interactions
    const { data: interactions, error: interactionsError } = await supabase
      .from('interactions')
      .select('*');

    if (interactionsError) {
      throw new Error(`Error fetching interactions data: ${interactionsError.message}`);
    }

    // Get tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*');

    if (tasksError) {
      throw new Error(`Error fetching tasks data: ${tasksError.message}`);
    }

    // Group data by advisor
    const advisors: Record<string, {
      name: string;
      clientCount: number;
      interactionCount: number;
      taskCount: number;
      completedTaskCount: number;
      averageTaskCompletionTime: number;
      clientAcquisition: number;
    }> = {};

    // Process households
    households.forEach((household: any) => {
      const advisor = household.primary_advisor || 'Unassigned';

      if (!advisors[advisor]) {
        advisors[advisor] = {
          name: advisor,
          clientCount: 0,
          interactionCount: 0,
          taskCount: 0,
          completedTaskCount: 0,
          averageTaskCompletionTime: 0,
          clientAcquisition: 0
        };
      }

      advisors[advisor].clientCount++;

      // Check if this is a new client (created within the date range)
      if (household.created_at) {
        const createdDate = new Date(household.created_at);
        const now = new Date();
        const startOfYear = new Date(now.getFullYear(), 0, 1);

        if (createdDate >= startOfYear) {
          advisors[advisor].clientAcquisition++;
        }
      }
    });

    // Process interactions
    interactions.forEach((interaction: any) => {
      if (interaction.advisor) {
        const advisor = interaction.advisor;

        if (!advisors[advisor]) {
          advisors[advisor] = {
            name: advisor,
            clientCount: 0,
            interactionCount: 0,
            taskCount: 0,
            completedTaskCount: 0,
            averageTaskCompletionTime: 0,
            clientAcquisition: 0
          };
        }

        advisors[advisor].interactionCount++;
      }
    });

    // Process tasks
    tasks.forEach((task: any) => {
      if (task.assigned_to) {
        const advisor = task.assigned_to;

        if (!advisors[advisor]) {
          advisors[advisor] = {
            name: advisor,
            clientCount: 0,
            interactionCount: 0,
            taskCount: 0,
            completedTaskCount: 0,
            averageTaskCompletionTime: 0,
            clientAcquisition: 0
          };
        }

        advisors[advisor].taskCount++;

        if (task.status === 'completed') {
          advisors[advisor].completedTaskCount++;

          // Calculate completion time if available
          if (task.completed_at && task.created_at) {
            const completedDate = new Date(task.completed_at);
            const createdDate = new Date(task.created_at);
            const completionTime = (completedDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24); // in days

            if (!advisors[advisor].averageTaskCompletionTime) {
              advisors[advisor].averageTaskCompletionTime = completionTime;
            } else {
              advisors[advisor].averageTaskCompletionTime =
                (advisors[advisor].averageTaskCompletionTime * (advisors[advisor].completedTaskCount - 1) + completionTime) /
                advisors[advisor].completedTaskCount;
            }
          }
        }
      }
    });

    // Convert to array for display
    const productivityData = Object.values(advisors).map((advisor, index) => ({
      id: index + 1,
      name: advisor.name,
      clientCount: advisor.clientCount,
      interactionCount: advisor.interactionCount,
      taskCount: advisor.taskCount,
      completedTaskCount: advisor.completedTaskCount,
      taskCompletionRate: advisor.taskCount > 0 ? (advisor.completedTaskCount / advisor.taskCount) * 100 : 0,
      averageTaskCompletionTime: advisor.averageTaskCompletionTime,
      clientAcquisition: advisor.clientAcquisition,
      interactionsPerClient: advisor.clientCount > 0 ? advisor.interactionCount / advisor.clientCount : 0
    }));

    // Sort by client count descending
    productivityData.sort((a, b) => b.clientCount - a.clientCount);

    return {
      id: `advisor-productivity-${Date.now()}`,
      reportId: 'advisor-productivity',
      runAt: new Date(),
      parameters: params,
      data: productivityData,
      columns: this.getColumns(),
      summary: {
        totalAdvisors: productivityData.length,
        totalClients: households.length,
        totalInteractions: interactions.length,
        totalTasks: tasks.length,
        completedTasks: tasks.filter((task: any) => task.status === 'completed').length
      },
      chartData: {
        labels: productivityData.map(advisor => advisor.name),
        datasets: [
          {
            label: 'Client Count',
            data: productivityData.map(advisor => advisor.clientCount),
            backgroundColor: productivityData.map(() => 'rgba(75, 192, 192, 0.6)')
          },
          {
            label: 'Interaction Count',
            data: productivityData.map(advisor => advisor.interactionCount),
            backgroundColor: productivityData.map(() => 'rgba(54, 162, 235, 0.6)')
          },
          {
            label: 'Task Completion Rate (%)',
            data: productivityData.map(advisor => advisor.taskCompletionRate),
            backgroundColor: productivityData.map(() => 'rgba(255, 206, 86, 0.6)')
          }
        ]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'name',
        header: 'Advisor',
      },
      {
        accessorKey: 'clientCount',
        header: 'Clients',
      },
      {
        accessorKey: 'clientAcquisition',
        header: 'New Clients',
      },
      {
        accessorKey: 'interactionCount',
        header: 'Interactions',
      },
      {
        accessorKey: 'interactionsPerClient',
        header: 'Interactions/Client',
        cell: ({ row }) => {
          const value = row.getValue('interactionsPerClient') as number;
          return typeof value === 'number'
            ? value.toFixed(1)
            : value;
        },
      },
      {
        accessorKey: 'taskCount',
        header: 'Tasks',
      },
      {
        accessorKey: 'completedTaskCount',
        header: 'Completed Tasks',
      },
      {
        accessorKey: 'taskCompletionRate',
        header: 'Completion Rate',
        cell: ({ row }) => {
          const value = row.getValue('taskCompletionRate') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)}%`
            : value;
        },
      },
      {
        accessorKey: 'averageTaskCompletionTime',
        header: 'Avg. Completion Time',
        cell: ({ row }) => {
          const value = row.getValue('averageTaskCompletionTime') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)} days`
            : value;
        },
      }
    ];
  }
};

/**
 * Organization Performance Report
 */
export const organizationPerformanceReport: ReportType = {
  id: 'organization-performance',
  name: 'Organization Performance Report',
  description: 'Overview of organization performance metrics',
  category: 'operational',
  icon: Building,
  defaultParams: {
    filters: {
      dateRange: 'this-year',
      clientFilter: 'all'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Get households
    let householdQuery = supabase
      .from('households')
      .select('id, householdName, created_at, org_id');

    // Apply filters
    householdQuery = applyFilters(householdQuery, params);

    const { data: households, error: householdError } = await householdQuery;

    if (householdError) {
      throw new Error(`Error fetching household data: ${householdError.message}`);
    }

    // Get assets
    const { data: assets, error: assetsError } = await supabase
      .from('assets')
      .select('*');

    if (assetsError) {
      throw new Error(`Error fetching assets data: ${assetsError.message}`);
    }

    // Get interactions
    const { data: interactions, error: interactionsError } = await supabase
      .from('interactions')
      .select('*');

    if (interactionsError) {
      throw new Error(`Error fetching interactions data: ${interactionsError.message}`);
    }

    // Get tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*');

    if (tasksError) {
      throw new Error(`Error fetching tasks data: ${tasksError.message}`);
    }

    // Get organizations
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*');

    if (profilesError) {
      throw new Error(`Error fetching profiles data: ${profilesError.message}`);
    }

    // Group data by organization
    const organizations: Record<string, {
      id: string;
      name: string;
      clientCount: number;
      assetValue: number;
      interactionCount: number;
      taskCount: number;
      completedTaskCount: number;
      newClientCount: number;
    }> = {};

    // Initialize organizations
    profiles.forEach((profile: any) => {
      if (profile.org_id && profile.org_name) {
        organizations[profile.org_id] = {
          id: profile.org_id,
          name: profile.org_name,
          clientCount: 0,
          assetValue: 0,
          interactionCount: 0,
          taskCount: 0,
          completedTaskCount: 0,
          newClientCount: 0
        };
      }
    });

    // Process households
    households.forEach((household: any) => {
      if (household.org_id && organizations[household.org_id]) {
        organizations[household.org_id].clientCount++;

        // Check if this is a new client (created within the date range)
        if (household.created_at) {
          const createdDate = new Date(household.created_at);
          const now = new Date();
          const startOfYear = new Date(now.getFullYear(), 0, 1);

          if (createdDate >= startOfYear) {
            organizations[household.org_id].newClientCount++;
          }
        }
      }
    });

    // Process assets
    assets.forEach((asset: any) => {
      // Find the household for this asset
      const household = households.find((h: any) => h.id === asset.household_id);

      if (household && household.org_id && organizations[household.org_id]) {
        organizations[household.org_id].assetValue += parseFloat(asset.value) || 0;
      }
    });

    // Process interactions
    interactions.forEach((interaction: any) => {
      // Find the household for this interaction
      const household = households.find((h: any) => h.id === interaction.household_id);

      if (household && household.org_id && organizations[household.org_id]) {
        organizations[household.org_id].interactionCount++;
      }
    });

    // Process tasks
    tasks.forEach((task: any) => {
      // Find the household for this task
      const household = households.find((h: any) => h.id === task.household_id);

      if (household && household.org_id && organizations[household.org_id]) {
        organizations[household.org_id].taskCount++;

        if (task.status === 'completed') {
          organizations[household.org_id].completedTaskCount++;
        }
      }
    });

    // Convert to array for display
    const performanceData = Object.values(organizations).map((org, index) => ({
      id: index + 1,
      orgId: org.id,
      name: org.name,
      clientCount: org.clientCount,
      assetValue: org.assetValue,
      averageClientValue: org.clientCount > 0 ? org.assetValue / org.clientCount : 0,
      interactionCount: org.interactionCount,
      interactionsPerClient: org.clientCount > 0 ? org.interactionCount / org.clientCount : 0,
      taskCompletionRate: org.taskCount > 0 ? (org.completedTaskCount / org.taskCount) * 100 : 0,
      newClientCount: org.newClientCount,
      clientGrowthRate: org.clientCount > 0 ? (org.newClientCount / org.clientCount) * 100 : 0
    }));

    // Sort by client count descending
    performanceData.sort((a, b) => b.clientCount - a.clientCount);

    return {
      id: `organization-performance-${Date.now()}`,
      reportId: 'organization-performance',
      runAt: new Date(),
      parameters: params,
      data: performanceData,
      columns: this.getColumns(),
      summary: {
        totalOrganizations: performanceData.length,
        totalClients: households.length,
        totalAssetValue: assets.reduce((sum, asset: any) => sum + (parseFloat(asset.value) || 0), 0),
        totalInteractions: interactions.length,
        totalTasks: tasks.length,
        completedTasks: tasks.filter((task: any) => task.status === 'completed').length
      },
      chartData: {
        labels: performanceData.map(org => org.name),
        datasets: [
          {
            label: 'Client Count',
            data: performanceData.map(org => org.clientCount),
            backgroundColor: performanceData.map(() => 'rgba(75, 192, 192, 0.6)')
          },
          {
            label: 'New Clients',
            data: performanceData.map(org => org.newClientCount),
            backgroundColor: performanceData.map(() => 'rgba(54, 162, 235, 0.6)')
          },
          {
            label: 'Task Completion Rate (%)',
            data: performanceData.map(org => org.taskCompletionRate),
            backgroundColor: performanceData.map(() => 'rgba(255, 206, 86, 0.6)')
          }
        ]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'name',
        header: 'Organization',
      },
      {
        accessorKey: 'clientCount',
        header: 'Clients',
      },
      {
        accessorKey: 'newClientCount',
        header: 'New Clients',
      },
      {
        accessorKey: 'clientGrowthRate',
        header: 'Growth Rate',
        cell: ({ row }) => {
          const value = row.getValue('clientGrowthRate') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)}%`
            : value;
        },
      },
      {
        accessorKey: 'assetValue',
        header: 'Total Assets',
        cell: ({ row }) => {
          const value = row.getValue('assetValue');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'averageClientValue',
        header: 'Avg. Client Value',
        cell: ({ row }) => {
          const value = row.getValue('averageClientValue');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'interactionCount',
        header: 'Interactions',
      },
      {
        accessorKey: 'interactionsPerClient',
        header: 'Interactions/Client',
        cell: ({ row }) => {
          const value = row.getValue('interactionsPerClient') as number;
          return typeof value === 'number'
            ? value.toFixed(1)
            : value;
        },
      },
      {
        accessorKey: 'taskCompletionRate',
        header: 'Task Completion Rate',
        cell: ({ row }) => {
          const value = row.getValue('taskCompletionRate') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)}%`
            : value;
        },
      }
    ];
  }
};

/**
 * List of all operational reports
 */
export const operationalReports: ReportType[] = [
  clientAcquisitionReport,
  advisorProductivityReport,
  organizationPerformanceReport
];
