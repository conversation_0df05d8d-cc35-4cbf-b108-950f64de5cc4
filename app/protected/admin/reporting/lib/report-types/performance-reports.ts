import { createClient } from '@/utils/supabase/client';
import { ReportType, ReportParams, ReportData } from '../types';
import { applyFilters } from '../data-fetching';
import { ColumnDef } from '@tanstack/react-table';
import { TrendingUp } from 'lucide-react';

/**
 * Investment Performance Report
 */
export const investmentPerformanceReport: ReportType = {
  id: 'investment-performance',
  name: 'Investment Performance Report',
  description: 'Analysis of investment performance over time',
  category: 'performance',
  icon: TrendingUp,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      // Return empty data if no client ID is provided
      return {
        id: `investment-performance-${Date.now()}`,
        reportId: 'investment-performance',
        runAt: new Date(),
        parameters: params,
        data: [],
        columns: this.getColumns(),
        summary: {
          householdName: 'No Client Selected',
          totalInitialInvestment: 0,
          totalCurrentValue: 0,
          totalGainLoss: 0,
          totalReturnPercentage: 0,
          investmentCount: 0
        },
        recommendedChartType: 'line'
      };
    }

    // Get assets - use an empty array if no client ID is provided
    let assets = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('assets')
        .select('*')
        .eq('household_id', params.filters.clientId)
        .in('type', ['investment', 'superannuation', 'shares', 'managed_fund']);

      if (error) {
        throw new Error(`Error fetching assets data: ${error.message}`);
      }

      assets = data || [];
    }

    // Get household info - use a default if no client ID is provided
    let household = { householdName: 'No Client Selected' };

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', params.filters.clientId)
        .single();

      if (error) {
        throw new Error(`Error fetching household data: ${error.message}`);
      }

      household = data || household;
    }

    // For this report, we'll simulate performance data since we don't have actual historical records
    // In a real implementation, you would fetch historical performance from a time-series table

    // Generate simulated performance data for each investment
    const performanceData = assets.map((asset: any) => {
      const currentValue = parseFloat(asset.value) || 0;

      // Simulate initial investment (60-90% of current value)
      const initialInvestmentFactor = 0.6 + (Math.random() * 0.3);
      const initialInvestment = currentValue * initialInvestmentFactor;

      // Calculate gain/loss
      const gainLoss = currentValue - initialInvestment;
      const returnPercentage = (gainLoss / initialInvestment) * 100;

      // Simulate annualized return (between 2% and 15%)
      const annualizedReturn = 2 + (Math.random() * 13);

      return {
        id: asset.id,
        name: asset.name || 'Unnamed Investment',
        type: asset.type || 'Other',
        initialInvestment,
        currentValue,
        gainLoss,
        returnPercentage,
        annualizedReturn,
        provider: asset.provider || '',
        details: asset.details || ''
      };
    });

    // Calculate totals
    const totalInitialInvestment = performanceData.reduce((sum, inv) => sum + inv.initialInvestment, 0);
    const totalCurrentValue = performanceData.reduce((sum, inv) => sum + inv.currentValue, 0);
    const totalGainLoss = totalCurrentValue - totalInitialInvestment;
    const totalReturnPercentage = (totalGainLoss / totalInitialInvestment) * 100;

    // Sort by return percentage descending
    performanceData.sort((a, b) => b.returnPercentage - a.returnPercentage);

    // Generate simulated historical performance data
    // For demonstration purposes, we'll create 12 months of data
    const today = new Date();
    const months = [];
    const portfolioValues = [];

    // Start from 11 months ago
    let cumulativeReturn = 1;
    for (let i = 11; i >= 0; i--) {
      const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short', year: 'numeric' });

      // Simulate monthly return (between -2% and +3%)
      const monthlyReturn = 0.98 + (Math.random() * 0.05);
      cumulativeReturn *= monthlyReturn;

      const portfolioValue = totalInitialInvestment * cumulativeReturn;

      months.push(monthName);
      portfolioValues.push(portfolioValue);
    }

    return {
      id: `investment-performance-${Date.now()}`,
      reportId: 'investment-performance',
      runAt: new Date(),
      parameters: params,
      data: performanceData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        totalInitialInvestment,
        totalCurrentValue,
        totalGainLoss,
        totalReturnPercentage,
        investmentCount: performanceData.length
      },
      chartData: {
        labels: months,
        datasets: [{
          label: 'Portfolio Value',
          data: portfolioValues,
          backgroundColor: portfolioValues.map(() => 'rgba(54, 162, 235, 0.2)'),
          borderColor: 'rgba(54, 162, 235, 1)',
          fill: false
        }]
      },
      recommendedChartType: 'line'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'name',
        header: 'Investment Name',
      },
      {
        accessorKey: 'type',
        header: 'Type',
      },
      {
        accessorKey: 'initialInvestment',
        header: 'Initial Investment',
        cell: ({ row }) => {
          const value = row.getValue('initialInvestment');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'currentValue',
        header: 'Current Value',
        cell: ({ row }) => {
          const value = row.getValue('currentValue');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'gainLoss',
        header: 'Gain/Loss',
        cell: ({ row }) => {
          const value = row.getValue('gainLoss') as number;
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value)
            : value;
        },
      },
      {
        accessorKey: 'returnPercentage',
        header: 'Return %',
        cell: ({ row }) => {
          const value = row.getValue('returnPercentage') as number;
          return typeof value === 'number'
            ? `${value.toFixed(2)}%`
            : value;
        },
      },
      {
        accessorKey: 'annualizedReturn',
        header: 'Annualized Return',
        cell: ({ row }) => {
          const value = row.getValue('annualizedReturn') as number;
          return typeof value === 'number'
            ? `${value.toFixed(2)}%`
            : value;
        },
      },
      {
        accessorKey: 'provider',
        header: 'Provider',
      }
    ];
  }
};

/**
 * Goal Progress Report
 */
export const goalProgressReport: ReportType = {
  id: 'goal-progress',
  name: 'Goal Progress Report',
  description: 'Tracks progress toward financial goals',
  category: 'performance',
  icon: TrendingUp,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      // Return empty data if no client ID is provided
      return {
        id: `goal-progress-${Date.now()}`,
        reportId: 'goal-progress',
        runAt: new Date(),
        parameters: params,
        data: [],
        columns: this.getColumns(),
        summary: {
          householdName: 'No Client Selected',
          goalCount: 0,
          completedGoals: 0,
          inProgressGoals: 0
        },
        recommendedChartType: 'bar'
      };
    }

    // Get goals - use an empty array if no client ID is provided
    let goals = [];

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('goals')
        .select('*')
        .eq('household_id', params.filters.clientId);

      if (error) {
        throw new Error(`Error fetching goals data: ${error.message}`);
      }

      goals = data || [];
    }

    // Get household info - use a default if no client ID is provided
    let household = { householdName: 'No Client Selected' };

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', params.filters.clientId)
        .single();

      if (error) {
        throw new Error(`Error fetching household data: ${error.message}`);
      }

      household = data || household;
    }

    // Transform goals data
    const progressData = goals.map((goal: any) => {
      const targetAmount = parseFloat(goal.target_amount) || 0;
      // Since there's no current_amount field, we'll simulate progress based on status
      let currentAmount = 0;

      if (goal.status === 'Completed' || goal.status === 'Achieved') {
        currentAmount = targetAmount;
      } else if (goal.status === 'In Progress') {
        // Simulate progress between 10% and 90%
        currentAmount = targetAmount * (0.1 + (Math.random() * 0.8));
      } else if (goal.status === 'Not Started') {
        currentAmount = 0;
      } else {
        // Default to 50% progress for other statuses
        currentAmount = targetAmount * 0.5;
      }
      const progressPercentage = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;

      // Calculate time remaining
      let timeRemaining = '';
      if (goal.target_date) {
        const targetDate = new Date(goal.target_date);
        const today = new Date();

        if (targetDate > today) {
          const diffTime = targetDate.getTime() - today.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays > 365) {
            const years = Math.floor(diffDays / 365);
            timeRemaining = `${years} year${years !== 1 ? 's' : ''}`;
          } else if (diffDays > 30) {
            const months = Math.floor(diffDays / 30);
            timeRemaining = `${months} month${months !== 1 ? 's' : ''}`;
          } else {
            timeRemaining = `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
          }
        } else {
          timeRemaining = 'Overdue';
        }
      }

      // Calculate required monthly contribution
      let requiredMonthlyContribution = 0;
      if (goal.target_date) {
        const targetDate = new Date(goal.target_date);
        const today = new Date();

        if (targetDate > today) {
          const diffTime = targetDate.getTime() - today.getTime();
          const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));

          if (diffMonths > 0) {
            requiredMonthlyContribution = (targetAmount - currentAmount) / diffMonths;
          }
        }
      }

      return {
        id: goal.id,
        name: goal.title || 'Unnamed Goal',
        description: goal.details || '',
        targetAmount,
        currentAmount,
        progressPercentage,
        targetDate: goal.target_date ? new Date(goal.target_date).toLocaleDateString() : 'No target date',
        timeRemaining,
        requiredMonthlyContribution,
        priority: goal.priority || 'Medium',
        status: goal.status || 'In Progress'
      };
    });

    // Sort by progress percentage descending
    progressData.sort((a, b) => b.progressPercentage - a.progressPercentage);

    return {
      id: `goal-progress-${Date.now()}`,
      reportId: 'goal-progress',
      runAt: new Date(),
      parameters: params,
      data: progressData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        goalCount: progressData.length,
        completedGoals: progressData.filter(goal => goal.progressPercentage >= 100).length,
        inProgressGoals: progressData.filter(goal => goal.progressPercentage < 100).length
      },
      chartData: {
        labels: progressData.map(goal => goal.name),
        datasets: [{
          label: 'Progress (%)',
          data: progressData.map(goal => goal.progressPercentage),
          backgroundColor: progressData.map(goal =>
            goal.progressPercentage >= 100
              ? 'rgba(75, 192, 192, 0.6)'
              : goal.progressPercentage >= 50
                ? 'rgba(255, 206, 86, 0.6)'
                : 'rgba(255, 99, 132, 0.6)'
          )
        }]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'name',
        header: 'Goal Name',
      },
      {
        accessorKey: 'description',
        header: 'Description',
      },
      {
        accessorKey: 'targetAmount',
        header: 'Target Amount',
        cell: ({ row }) => {
          const value = row.getValue('targetAmount');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'currentAmount',
        header: 'Current Amount',
        cell: ({ row }) => {
          const value = row.getValue('currentAmount');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'progressPercentage',
        header: 'Progress',
        cell: ({ row }) => {
          const value = row.getValue('progressPercentage') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)}%`
            : value;
        },
      },
      {
        accessorKey: 'targetDate',
        header: 'Target Date',
      },
      {
        accessorKey: 'timeRemaining',
        header: 'Time Remaining',
      },
      {
        accessorKey: 'requiredMonthlyContribution',
        header: 'Required Monthly',
        cell: ({ row }) => {
          const value = row.getValue('requiredMonthlyContribution');
          return typeof value === 'number'
            ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value as number)
            : value;
        },
      },
      {
        accessorKey: 'priority',
        header: 'Priority',
      },
      {
        accessorKey: 'status',
        header: 'Status',
      }
    ];
  }
};

/**
 * Financial Plan Adherence Report
 */
export const financialPlanAdherenceReport: ReportType = {
  id: 'financial-plan-adherence',
  name: 'Financial Plan Adherence Report',
  description: 'Measures adherence to the financial plan',
  category: 'performance',
  icon: TrendingUp,
  defaultParams: {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'specific'
    }
  },

  async fetchData(params: ReportParams): Promise<ReportData> {
    const supabase = createClient();

    // Ensure we have a client ID
    if (params.filters.clientFilter === 'specific' && !params.filters.clientId) {
      // Return empty data if no client ID is provided
      return {
        id: `financial-plan-adherence-${Date.now()}`,
        reportId: 'financial-plan-adherence',
        runAt: new Date(),
        parameters: params,
        data: [],
        columns: this.getColumns(),
        summary: {
          householdName: 'No Client Selected',
          overallAdherence: 0,
          statusCounts: {
            'On Track': 0,
            'Slightly Off Track': 0,
            'Needs Attention': 0,
            'Exceeding Target': 0
          }
        },
        recommendedChartType: 'bar'
      };
    }

    // Get household info - use a default if no client ID is provided
    let household = { householdName: 'No Client Selected' };

    if (params.filters.clientId) {
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', params.filters.clientId)
        .single();

      if (error) {
        throw new Error(`Error fetching household data: ${error.message}`);
      }

      household = data || household;
    }

    // For this report, we'll simulate adherence data since we don't have actual plan vs. actual data
    // In a real implementation, you would fetch plan data and actual data from appropriate tables

    // Define key financial plan areas
    const planAreas = [
      'Savings Rate',
      'Debt Reduction',
      'Investment Contributions',
      'Emergency Fund',
      'Insurance Coverage',
      'Retirement Contributions',
      'Discretionary Spending',
      'Tax Planning',
      'Estate Planning'
    ];

    // Generate simulated adherence data
    const adherenceData = planAreas.map((area, index) => {
      // Simulate target and actual values
      const targetValue = 100;
      const actualValue = Math.floor(60 + (Math.random() * 60)); // Between 60% and 120% of target
      const adherencePercentage = (actualValue / targetValue) * 100;

      // Determine status based on adherence
      let status = 'On Track';
      if (adherencePercentage < 80) {
        status = 'Needs Attention';
      } else if (adherencePercentage < 95) {
        status = 'Slightly Off Track';
      } else if (adherencePercentage > 110) {
        status = 'Exceeding Target';
      }

      return {
        id: index + 1,
        area,
        targetValue,
        actualValue,
        adherencePercentage,
        status,
        lastReviewDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toLocaleDateString(), // Random date in last 90 days
        notes: ''
      };
    });

    // Calculate overall adherence
    const overallAdherence = adherenceData.reduce((sum, item) => sum + item.adherencePercentage, 0) / adherenceData.length;

    // Count items by status
    const statusCounts = {
      'On Track': adherenceData.filter(item => item.status === 'On Track').length,
      'Slightly Off Track': adherenceData.filter(item => item.status === 'Slightly Off Track').length,
      'Needs Attention': adherenceData.filter(item => item.status === 'Needs Attention').length,
      'Exceeding Target': adherenceData.filter(item => item.status === 'Exceeding Target').length
    };

    return {
      id: `financial-plan-adherence-${Date.now()}`,
      reportId: 'financial-plan-adherence',
      runAt: new Date(),
      parameters: params,
      data: adherenceData,
      columns: this.getColumns(),
      summary: {
        householdName: household.householdName || 'Unnamed Household',
        overallAdherence,
        statusCounts
      },
      chartData: {
        labels: adherenceData.map(item => item.area),
        datasets: [{
          label: 'Adherence (%)',
          data: adherenceData.map(item => item.adherencePercentage),
          backgroundColor: adherenceData.map(item => {
            if (item.status === 'Needs Attention') return 'rgba(255, 99, 132, 0.6)';
            if (item.status === 'Slightly Off Track') return 'rgba(255, 206, 86, 0.6)';
            if (item.status === 'On Track') return 'rgba(75, 192, 192, 0.6)';
            return 'rgba(54, 162, 235, 0.6)'; // Exceeding Target
          })
        }]
      },
      recommendedChartType: 'bar'
    };
  },

  getColumns(): ColumnDef<any, any>[] {
    return [
      {
        accessorKey: 'area',
        header: 'Plan Area',
      },
      {
        accessorKey: 'targetValue',
        header: 'Target',
        cell: ({ row }) => {
          return '100%';
        },
      },
      {
        accessorKey: 'actualValue',
        header: 'Actual',
        cell: ({ row }) => {
          const value = row.getValue('actualValue');
          return typeof value === 'number'
            ? `${value}%`
            : value;
        },
      },
      {
        accessorKey: 'adherencePercentage',
        header: 'Adherence',
        cell: ({ row }) => {
          const value = row.getValue('adherencePercentage') as number;
          return typeof value === 'number'
            ? `${value.toFixed(1)}%`
            : value;
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
      },
      {
        accessorKey: 'lastReviewDate',
        header: 'Last Review',
      },
      {
        accessorKey: 'notes',
        header: 'Notes',
      }
    ];
  }
};

/**
 * List of all performance reports
 */
export const performanceReports: ReportType[] = [
  investmentPerformanceReport,
  goalProgressReport,
  financialPlanAdherenceReport
];
