import { createClient } from '@/utils/supabase/client';
import { ReportConfig, ReportData, ReportParams } from './types';

/**
 * Saves a report configuration to Supabase
 * 
 * @param report The report configuration to save
 * @returns The ID of the saved report
 */
export async function saveReportConfig(report: ReportConfig): Promise<string> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();
  
  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }
  
  // Check if this is an update or a new report
  if (report.id) {
    // Update existing report
    const { data, error } = await supabase
      .from('reports')
      .update({
        name: report.name,
        description: report.description,
        type: report.type,
        category: report.category,
        parameters: report.filters,
        output_format: report.outputFormat,
        chart_type: report.chartType,
        schedule: report.schedule,
        updated_at: new Date().toISOString()
      })
      .eq('id', report.id)
      .select('id')
      .single();
    
    if (error) {
      throw new Error(`Error updating report: ${error.message}`);
    }
    
    return data.id;
  } else {
    // Create new report
    const { data, error } = await supabase
      .from('reports')
      .insert({
        name: report.name,
        description: report.description,
        type: report.type,
        category: report.category,
        parameters: report.filters,
        output_format: report.outputFormat,
        chart_type: report.chartType,
        schedule: report.schedule,
        user_id: userData.user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();
    
    if (error) {
      throw new Error(`Error saving report: ${error.message}`);
    }
    
    return data.id;
  }
}

/**
 * Saves a report result to Supabase
 * 
 * @param reportId The ID of the report configuration
 * @param result The report result to save
 * @returns The ID of the saved report result
 */
export async function saveReportResult(reportId: string, result: ReportData): Promise<string> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();
  
  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }
  
  // Save report result
  const { data, error } = await supabase
    .from('report_results')
    .insert({
      report_id: reportId,
      parameters: result.parameters,
      data: result.data,
      summary: result.summary,
      chart_data: result.chartData,
      user_id: userData.user.id,
      created_at: new Date().toISOString()
    })
    .select('id')
    .single();
  
  if (error) {
    throw new Error(`Error saving report result: ${error.message}`);
  }
  
  return data.id;
}

/**
 * Gets all saved reports for the current user
 * 
 * @returns An array of report configurations
 */
export async function getSavedReports(): Promise<ReportConfig[]> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();
  
  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }
  
  // Get reports
  const { data, error } = await supabase
    .from('reports')
    .select('*')
    .eq('user_id', userData.user.id)
    .order('created_at', { ascending: false });
  
  if (error) {
    throw new Error(`Error fetching reports: ${error.message}`);
  }
  
  // Transform to ReportConfig format
  return data.map(report => ({
    id: report.id,
    name: report.name,
    description: report.description,
    type: report.type,
    category: report.category,
    filters: report.parameters,
    outputFormat: report.output_format,
    chartType: report.chart_type,
    schedule: report.schedule
  }));
}

/**
 * Gets a specific report configuration by ID
 * 
 * @param reportId The ID of the report to get
 * @returns The report configuration
 */
export async function getReportById(reportId: string): Promise<ReportConfig> {
  const supabase = createClient();
  
  // Get report
  const { data, error } = await supabase
    .from('reports')
    .select('*')
    .eq('id', reportId)
    .single();
  
  if (error) {
    throw new Error(`Error fetching report: ${error.message}`);
  }
  
  // Transform to ReportConfig format
  return {
    id: data.id,
    name: data.name,
    description: data.description,
    type: data.type,
    category: data.category,
    filters: data.parameters,
    outputFormat: data.output_format,
    chartType: data.chart_type,
    schedule: data.schedule
  };
}

/**
 * Deletes a report configuration
 * 
 * @param reportId The ID of the report to delete
 */
export async function deleteReport(reportId: string): Promise<void> {
  const supabase = createClient();
  
  // Delete report
  const { error } = await supabase
    .from('reports')
    .delete()
    .eq('id', reportId);
  
  if (error) {
    throw new Error(`Error deleting report: ${error.message}`);
  }
}

/**
 * Gets report history for a specific report
 * 
 * @param reportId The ID of the report
 * @returns An array of report results
 */
export async function getReportHistory(reportId: string): Promise<any[]> {
  const supabase = createClient();
  
  // Get report history
  const { data, error } = await supabase
    .from('report_results')
    .select('*')
    .eq('report_id', reportId)
    .order('created_at', { ascending: false });
  
  if (error) {
    throw new Error(`Error fetching report history: ${error.message}`);
  }
  
  return data;
}
