import { CustomReport, FilterCondition } from './types';

/**
 * Find which table a field belongs to
 */
export function findFieldTableInfo(fieldName: string, defaultTable: string, joinTables: any[]): { tableName: string, fieldExists: boolean } {
  // If the field already has a table reference, extract it
  if (fieldName.includes('.')) {
    const [tableName] = fieldName.split('.');
    return { tableName, fieldExists: true };
  }

  // Common fields in different tables
  const tableFieldMap: Record<string, string[]> = {
    'households': ['id', 'householdName', 'members', 'user_id', 'address', 'phone', 'email',
                  'occupation', 'employer', 'marital_status', 'date_of_birth', 'tax_file_number',
                  'notes', 'street', 'city', 'state', 'zip_code', 'country', 'property_type',
                  'preferred_contact', 'best_time_to_call', 'alternative_contact',
                  'investment_strategy', 'risk_tolerance', 'primary_advisor', 'last_review',
                  'next_review', 'additional_info', 'org_id', 'created_at', 'updated_at'],
    'assets': ['id', 'household_id', 'name', 'type', 'value', 'details', 'created_at', 'updated_at',
              'property_type', 'rental_income', 'provider', 'linked_income_id'],
    'income': ['id', 'household_id', 'source', 'amount', 'frequency', 'details', 'created_at',
              'updated_at', 'income_type', 'linked_asset_id', 'member_id'],
    'expenses': ['id', 'household_id', 'name', 'amount', 'frequency', 'category', 'details',
                'created_at', 'updated_at', 'linked_liability_id'],
    'liabilities': ['id', 'household_id', 'name', 'amount', 'interest_rate', 'lender', 'details',
                  'created_at', 'updated_at', 'linked_asset_id', 'linked_expense_id'],
    'goals': ['id', 'household_id', 'title', 'description', 'target_date', 'target_amount',
            'current_amount', 'status', 'priority', 'created_at', 'updated_at'],
    'tasks': ['id', 'household_id', 'title', 'description', 'due_date', 'status', 'priority',
            'assigned_to', 'created_at', 'updated_at'],
    'interactions': ['id', 'household_id', 'title', 'content', 'date', 'type', 'created_at', 'updated_at'],
    'recommendations': ['id', 'household_id', 'title', 'description', 'status', 'financial_impact',
                      'created_at', 'updated_at']
  };

  // First, check if the field exists in the default table
  if (tableFieldMap[defaultTable] && tableFieldMap[defaultTable].includes(fieldName)) {
    return { tableName: defaultTable, fieldExists: true };
  }

  // If not found in the default table, check if it exists in any of the joined tables
  for (const join of joinTables) {
    const tableName = join.table;
    if (tableFieldMap[tableName] && tableFieldMap[tableName].includes(fieldName)) {
      return { tableName, fieldExists: true };
    }
  }

  // If not found in joined tables, check all other tables
  for (const [tableName, fields] of Object.entries(tableFieldMap)) {
    if (tableName !== defaultTable && !joinTables.some(join => join.table === tableName)) {
      if (fields.includes(fieldName)) {
        return { tableName, fieldExists: true };
      }
    }
  }

  // If not found anywhere, default to the primary table
  return { tableName: defaultTable, fieldExists: false };
}

/**
 * Build an SQL query from a custom report definition
 */
export function buildSqlQuery(report: CustomReport): string {
  console.log('REPORT FLOW - Building SQL query for report:', {
    reportId: report.id,
    reportName: report.name,
    dataSource: report.dataSource
  });
  console.log('REPORT FLOW - Report fields for SQL:', report.fields);

  // Check if the report has a custom SQL query in the tableDefinition
  // Only use it if we're not explicitly trying to rebuild the query
  const forceRebuild = report.fields.some(f => f.id && f.id.includes('_'));

  if (!forceRebuild && report.tableDefinition && report.tableDefinition.includes('SQL Query:')) {
    const sqlQueryMatch = report.tableDefinition.match(/SQL Query: ([\s\S]*?)(?:\n\n|$)/);
    if (sqlQueryMatch && sqlQueryMatch[1]) {
      const sql = sqlQueryMatch[1].trim();
      console.log('REPORT FLOW - Using custom SQL from tableDefinition:', sql);
      return sql;
    }
  }

  console.log('REPORT FLOW - Building new SQL query from fields');

  // Get joins from the report
  const joinTables = report.joins || [];

  // Start building the SELECT clause
  let selectClause = 'SELECT ';

  // Determine the primary table
  const primaryTable = report.dataSource;

  // Generate the field selects
  // Check if we need to add automatic joins for fields from other tables
  const fieldsFromOtherTables = report.fields
    .filter(field => field.isVisible)
    .filter(field => {
      // If the field has a table reference in sourceField, extract it
      if (field.sourceField && field.sourceField.includes('.')) {
        const [tableName] = field.sourceField.split('.');
        return tableName !== primaryTable && !joinTables.some(join => join.table === tableName);
      }

      // If the field has a table reference in id, extract it
      if (field.id && field.id.includes('_')) {
        const [tablePrefix] = field.id.split('_');
        return tablePrefix !== primaryTable && !joinTables.some(join => join.table === tablePrefix);
      }

      return false;
    });

  // Add automatic joins for fields from other tables
  fieldsFromOtherTables.forEach(field => {
    let tableName: string | undefined;

    // Extract table name from sourceField or id
    if (field.sourceField && field.sourceField.includes('.')) {
      [tableName] = field.sourceField.split('.');
    } else if (field.id && field.id.includes('_')) {
      [tableName] = field.id.split('_');
    } else {
      return;
    }

    // Skip if we already have a join for this table
    if (joinTables.some(join => join.table === tableName)) {
      return;
    }

    // Add a join for this table
    joinTables.push({
      table: tableName,
      joinType: 'LEFT',
      joinField: tableName === 'households' ? 'id' : 'household_id',
      foreignField: tableName === 'households' ? 'household_id' : 'id'
    });
  });

  const fieldSelects = report.fields
    .filter(field => field.isVisible)
    .map(field => {
      // Check if the field name contains a table reference (e.g., "table.field")
      const hasTableRef = field.sourceField && field.sourceField.includes('.');

      // Check if the field name contains an aggregation (e.g., "SUM(field)")
      const hasAggregation = field.sourceField && field.sourceField.match(/^(SUM|AVG|MIN|MAX|COUNT)\s*\(/i);

      // Check if the field id contains a table reference (e.g., "table_field")
      const hasTablePrefixInId = field.id && field.id.includes('_');

      let sqlFragment;

      if (hasTableRef) {
        // If it already has a table reference, use it as is
        const [tableName, fieldName] = field.sourceField.split('.');
        sqlFragment = `"${tableName}"."${fieldName}" AS "${field.name}"`;
      } else if (hasAggregation) {
        // If it has an aggregation, use it as is
        sqlFragment = `${field.sourceField} AS "${field.name}"`;
      } else if (hasTablePrefixInId) {
        // If the field id has a table prefix, use that
        const [tableName] = field.id.split('_');
        if (tableName !== primaryTable) {
          sqlFragment = `"${tableName}"."${field.sourceField || field.name}" AS "${field.name}"`;
        } else {
          sqlFragment = `"${primaryTable}"."${field.sourceField || field.name}" AS "${field.name}"`;
        }
      } else {
        // Check if this field might be from a joined table
        const fieldTableInfo = findFieldTableInfo(field.sourceField || field.name, report.dataSource, joinTables);

        if (fieldTableInfo.tableName !== primaryTable) {
          // If the field is from a joined table, qualify it with that table name
          sqlFragment = `"${fieldTableInfo.tableName}"."${field.sourceField || field.name}" AS "${field.name}"`;
        } else {
          // Quote the field name to handle case sensitivity
          const fieldName = `"${field.sourceField || field.name}"`;

          // Apply aggregation if specified
          if (field.aggregation) {
            sqlFragment = `${field.aggregation.toUpperCase()}(${fieldName}) AS "${field.name}"`;
          }
          // If we have joins, qualify the field with the table name
          else if (joinTables.length > 0) {
            sqlFragment = `"${primaryTable}".${fieldName} AS "${field.name}"`;
          }
          else {
            sqlFragment = `${fieldName} AS "${field.name}"`;
          }
        }
      }

      return sqlFragment;
    });

  // Add calculations
  if (report.calculations && report.calculations.length > 0) {
    const calculationSelects = report.calculations.map(calc => {
      // Replace field references with actual field names
      let formula = calc.formula;
      report.fields.forEach(field => {
        formula = formula.replace(
          new RegExp(`\\[${field.name}\\]`, 'g'),
          field.sourceField
        );
      });

      return `(${formula}) AS "${calc.name}"`;
    });

    fieldSelects.push(...calculationSelects);
  }

  selectClause += fieldSelects.join(', ');

  // Add FROM clause with quoted table name
  const fromClause = ` FROM "${primaryTable}"`;

  // Add JOIN clauses if joins exist
  let joinClause = '';
  if (joinTables.length > 0) {
    joinClause = joinTables.map((join: any) => {
      const joinType = (join.joinType || 'LEFT').toUpperCase();

      // Handle case sensitivity for join fields
      const sourceField = join.joinField === 'id' ? 'id' : `"${join.joinField}"`;
      const targetField = join.foreignField === 'id' ? 'id' : `"${join.foreignField}"`;

      return ` ${joinType} JOIN "${join.table}" ON "${primaryTable}".${sourceField} = "${join.table}".${targetField}`;
    }).join('');
  }

  // Add WHERE clause if filters exist
  let whereClause = '';
  if (report.filters && Array.isArray(report.filters) && report.filters.length > 0) {
    whereClause = ' WHERE ' + buildWhereClause(report.filters, report, joinTables);
  }

  // Add GROUP BY clause if grouping is specified
  let groupByClause = '';
  if (report.groupBy && Array.isArray(report.groupBy) && report.groupBy.length > 0) {
    const groupFields = report.groupBy.map(fieldId => {
      const field = report.fields.find(f => f.id === fieldId);

      if (field) {
        // Check if the field name contains a table reference
        if (field.sourceField.includes('.')) {
          return field.sourceField;
        }

        // Check if this field might be from a joined table
        const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);

        if (fieldTableInfo.tableName !== report.dataSource) {
          // If the field is from a joined table, qualify it with that table name
          return `"${fieldTableInfo.tableName}"."${field.sourceField}"`;
        }

        // If we have joins, qualify the field with the table name
        if (joinTables.length > 0) {
          return `"${primaryTable}"."${field.sourceField}"`;
        }

        // Quote the field name
        return `"${field.sourceField}"`;
      }

      // If fieldId is a string that contains a table reference
      if (typeof fieldId === 'string' && fieldId.includes('.')) {
        const [table, field] = fieldId.split('.');
        return `"${table}"."${field}"`;
      }

      return `"${fieldId}"`;
    });

    groupByClause = ' GROUP BY ' + groupFields.join(', ');
  }

  // Add ORDER BY clause if sorting is specified
  let orderByClause = '';
  if (report.sortBy && Array.isArray(report.sortBy) && report.sortBy.length > 0) {
    const sortFields = report.sortBy.map(sort => {
      const field = report.fields.find(f => f.id === sort.field);

      if (field) {
        // Check if the field name contains a table reference or aggregation
        if (field.sourceField.includes('.') || field.sourceField.match(/^(SUM|AVG|MIN|MAX|COUNT)\s*\(/i)) {
          return `${field.sourceField} ${sort.direction.toUpperCase()}`;
        }

        // Check if this field might be from a joined table
        const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);

        if (fieldTableInfo.tableName !== report.dataSource) {
          // If the field is from a joined table, qualify it with that table name
          return `"${fieldTableInfo.tableName}"."${field.sourceField}" ${sort.direction.toUpperCase()}`;
        }

        // If we have joins, qualify the field with the table name
        if (joinTables.length > 0) {
          return `"${primaryTable}"."${field.sourceField}" ${sort.direction.toUpperCase()}`;
        }

        // Quote the field name
        return `"${field.sourceField}" ${sort.direction.toUpperCase()}`;
      }

      // If sort.field is a string that contains a table reference
      if (typeof sort.field === 'string' && sort.field.includes('.')) {
        return `${sort.field} ${sort.direction.toUpperCase()}`;
      }

      return `"${sort.field}" ${sort.direction.toUpperCase()}`;
    });

    orderByClause = ' ORDER BY ' + sortFields.join(', ');
  }

  // Add LIMIT clause if specified
  let limitClause = '';
  if (report.result_limit) {
    limitClause = ` LIMIT ${report.result_limit}`;
  }

  // Combine all clauses
  const finalSql = selectClause + fromClause + joinClause + whereClause + groupByClause + orderByClause + limitClause;

  console.log('REPORT FLOW - Generated SQL query:', finalSql);

  return finalSql;
}

/**
 * Build a WHERE clause from filter conditions
 */
function buildWhereClause(filters: FilterCondition[], report?: CustomReport, joinTables?: any[]): string {
  if (!filters || !Array.isArray(filters) || filters.length === 0) return '';

  return filters.map((filter, index) => {
    const condition = buildFilterCondition(filter, report, joinTables);

    // Add AND/OR logic for all but the last filter
    if (index < filters.length - 1) {
      return `(${condition}) ${filter.logic?.toUpperCase() || 'AND'}`;
    }

    return `(${condition})`;
  }).join(' ');
}

/**
 * Build a single filter condition
 */
function buildFilterCondition(filter: FilterCondition, report?: CustomReport, joinTables?: any[]): string {
  // Check if the field name contains a table reference (e.g., "table.field")
  const hasTableRef = filter.field.includes('.');

  // Format the field name appropriately
  let field;
  if (hasTableRef) {
    // If it has a table reference, split and quote each part
    const [table, fieldName] = filter.field.split('.');
    field = `"${table}"."${fieldName}"`;
  } else if (report && joinTables && joinTables.length > 0) {
    // Check if this field might be from a joined table
    const fieldTableInfo = findFieldTableInfo(filter.field, report.dataSource, joinTables);

    if (fieldTableInfo.tableName !== report.dataSource) {
      // If the field is from a joined table, qualify it with that table name
      field = `"${fieldTableInfo.tableName}"."${filter.field}"`;
    } else {
      // Otherwise, just quote the field name
      field = `"${filter.field}"`;
    }
  } else {
    // If no report or joins provided, just quote the field name
    field = `"${filter.field}"`;
  }

  const value = filter.value;

  switch (filter.operator) {
    case 'equals':
      return `${field} = '${value}'`;
    case 'notEquals':
      return `${field} <> '${value}'`;
    case 'contains':
      return `${field} LIKE '%${value}%'`;
    case 'startsWith':
      return `${field} LIKE '${value}%'`;
    case 'endsWith':
      return `${field} LIKE '%${value}'`;
    case 'greaterThan':
      return `${field} > '${value}'`;
    case 'lessThan':
      return `${field} < '${value}'`;
    case 'between':
      return `${field} BETWEEN '${value}' AND '${filter.valueEnd}'`;
    case 'in':
      const values = Array.isArray(filter.valueList)
        ? filter.valueList
        : String(value).split(',').map(v => v.trim());
      return `${field} IN (${values.map(v => `'${v}'`).join(', ')})`;
    default:
      return `${field} = '${value}'`;
  }
}
