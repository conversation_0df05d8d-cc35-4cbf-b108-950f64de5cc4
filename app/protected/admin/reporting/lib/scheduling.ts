import { createClient } from '@/utils/supabase/client';
import { ReportSchedule, ReportExecution } from './types';

/**
 * Calculates the next run date based on schedule parameters
 *
 * @param frequency The frequency of the schedule
 * @param dayOfWeek The day of the week (0-6, Sunday to Saturday)
 * @param dayOfMonth The day of the month (1-31)
 * @param month The month (0-11, January to December)
 * @param time The time in 24-hour format (HH:MM)
 * @returns The next run date
 */
export function calculateNextRunDate(
  frequency: string,
  dayOfWeek: number,
  dayOfMonth: number,
  month: number,
  time: string
): Date {
  const now = new Date();
  const [hours, minutes] = time.split(':').map(Number);
  let nextRun = new Date(now);

  // Set the time
  nextRun.setHours(hours, minutes, 0, 0);

  // If the time is in the past, start from tomorrow
  if (nextRun <= now) {
    nextRun.setDate(nextRun.getDate() + 1);
  }

  switch (frequency) {
    case 'daily':
      // Already set for the next day
      break;

    case 'weekly':
      // Set to the next occurrence of the specified day of week
      const currentDay = nextRun.getDay();
      const daysUntilTarget = (dayOfWeek - currentDay + 7) % 7;

      // If today is the target day but time has passed, we need to go to next week
      if (daysUntilTarget === 0 && nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 7);
      } else {
        nextRun.setDate(nextRun.getDate() + daysUntilTarget);
      }
      break;

    case 'monthly':
      // Set to the specified day of the current month
      nextRun.setDate(dayOfMonth);

      // If that day has already passed this month, move to next month
      if (nextRun <= now) {
        nextRun.setMonth(nextRun.getMonth() + 1);
      }

      // Handle invalid dates (e.g., February 30)
      if (nextRun.getDate() !== dayOfMonth) {
        // Go to the last day of the month
        nextRun.setDate(0);
      }
      break;

    case 'quarterly':
      // Calculate the next quarter month based on the specified month
      const currentMonth = now.getMonth();
      const currentQuarter = Math.floor(currentMonth / 3);
      const targetQuarterMonth = currentQuarter * 3 + month % 3;

      nextRun.setDate(dayOfMonth);
      nextRun.setMonth(targetQuarterMonth);

      // If that date has already passed, move to the next quarter
      if (nextRun <= now) {
        nextRun.setMonth(targetQuarterMonth + 3);
      }

      // Handle invalid dates
      if (nextRun.getDate() !== dayOfMonth) {
        nextRun.setDate(0);
      }
      break;

    case 'yearly':
      // Set to the specified month and day
      nextRun.setMonth(month);
      nextRun.setDate(dayOfMonth);

      // If that date has already passed this year, move to next year
      if (nextRun <= now) {
        nextRun.setFullYear(nextRun.getFullYear() + 1);
      }

      // Handle invalid dates
      if (nextRun.getDate() !== dayOfMonth) {
        // Go to the last day of the month
        nextRun.setDate(0);
      }
      break;
  }

  return nextRun;
}

/**
 * Creates a new report schedule
 *
 * @param schedule The schedule to create
 * @returns The ID of the created schedule
 */
export async function createSchedule(schedule: Omit<ReportSchedule, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
  const supabase = createClient();

  // Ensure reportId is a string
  const reportId = String(schedule.reportId);

  const { data, error } = await supabase
    .from('report_schedules')
    .insert({
      report_id: reportId,
      name: schedule.name,
      frequency: schedule.frequency,
      day_of_week: schedule.dayOfWeek,
      day_of_month: schedule.dayOfMonth,
      month: schedule.month,
      time: schedule.time,
      next_run_at: schedule.nextRunAt,
      enabled: schedule.enabled,
      delivery: schedule.delivery,
      user_id: schedule.userId,
      parameters: schedule.parameters
    })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Error creating schedule: ${error.message}`);
  }

  return data.id;
}

/**
 * Gets all schedules for a user
 *
 * @returns An array of schedules
 */
export async function getSchedules(): Promise<ReportSchedule[]> {
  const supabase = createClient();

  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  try {
    // First try to get schedules with report details
    const { data, error } = await supabase
      .from('report_schedules')
      .select('*, reports(name, description, type, category)')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Transform the data to match our interface
    return data.map(item => ({
      id: item.id,
      reportId: item.report_id,
      reportName: item.reports?.name || 'Unknown Report',
      reportType: item.reports?.type || 'unknown',
      reportCategory: item.reports?.category || 'unknown',
      name: item.name,
      frequency: item.frequency,
      dayOfWeek: item.day_of_week,
      dayOfMonth: item.day_of_month,
      month: item.month,
      time: item.time,
      nextRunAt: new Date(item.next_run_at),
      lastRunAt: item.last_run_at ? new Date(item.last_run_at) : undefined,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
      enabled: item.enabled,
      delivery: item.delivery,
      userId: item.user_id,
      parameters: item.parameters
    }));
  } catch (error) {
    console.error('Error with joined query, falling back to basic query:', error);

    // If the join fails, fall back to just getting the schedules
    const { data, error: fallbackError } = await supabase
      .from('report_schedules')
      .select('*')
      .order('created_at', { ascending: false });

    if (fallbackError) {
      throw new Error(`Error fetching schedules: ${fallbackError.message}`);
    }

    // Get report details separately if needed
    const reportIds = data.map(item => item.report_id).filter(Boolean);
    let reportDetails: Record<string, any> = {};

    if (reportIds.length > 0) {
      try {
        const { data: reportsData } = await supabase
          .from('reports')
          .select('id, name, type, category')
          .in('id', reportIds);

        if (reportsData) {
          reportDetails = reportsData.reduce((acc, report) => {
            acc[report.id] = report;
            return acc;
          }, {} as Record<string, any>);
        }
      } catch (reportError) {
        console.error('Error fetching report details:', reportError);
      }
    }

    // Transform the data to match our interface
    return data.map(item => {
      const report = reportDetails[item.report_id] || {};

      return {
        id: item.id,
        reportId: item.report_id,
        reportName: report.name || item.name || 'Unknown Report',
        reportType: report.type || 'unknown',
        reportCategory: report.category || 'unknown',
        name: item.name,
        frequency: item.frequency,
        dayOfWeek: item.day_of_week,
        dayOfMonth: item.day_of_month,
        month: item.month,
        time: item.time,
        nextRunAt: new Date(item.next_run_at),
        lastRunAt: item.last_run_at ? new Date(item.last_run_at) : undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
        enabled: item.enabled,
        delivery: item.delivery,
        userId: item.user_id,
        parameters: item.parameters
      };
    });
  }
}

/**
 * Gets a specific schedule by ID
 *
 * @param scheduleId The ID of the schedule to get
 * @returns The schedule
 */
export async function getScheduleById(scheduleId: string): Promise<ReportSchedule> {
  const supabase = createClient();

  try {
    // First try to get schedule with report details
    const { data, error } = await supabase
      .from('report_schedules')
      .select('*, reports(name, description, type, category)')
      .eq('id', scheduleId)
      .single();

    if (error) {
      throw error;
    }

    return {
      id: data.id,
      reportId: data.report_id,
      reportName: data.reports?.name || 'Unknown Report',
      reportType: data.reports?.type || 'unknown',
      reportCategory: data.reports?.category || 'unknown',
      name: data.name,
      frequency: data.frequency,
      dayOfWeek: data.day_of_week,
      dayOfMonth: data.day_of_month,
      month: data.month,
      time: data.time,
      nextRunAt: new Date(data.next_run_at),
      lastRunAt: data.last_run_at ? new Date(data.last_run_at) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      enabled: data.enabled,
      delivery: data.delivery,
      userId: data.user_id,
      parameters: data.parameters
    };
  } catch (error) {
    console.error('Error with joined query, falling back to basic query:', error);

    // If the join fails, fall back to just getting the schedule
    const { data, error: fallbackError } = await supabase
      .from('report_schedules')
      .select('*')
      .eq('id', scheduleId)
      .single();

    if (fallbackError) {
      throw new Error(`Error fetching schedule: ${fallbackError.message}`);
    }

    // Get report details separately if needed
    let reportDetails = {};

    try {
      const { data: reportData, error: reportError } = await supabase
        .from('reports')
        .select('id, name, type, category')
        .eq('id', data.report_id)
        .maybeSingle();

      if (!reportError && reportData) {
        reportDetails = reportData;
      }
    } catch (reportError) {
      console.error('Error fetching report details:', reportError);
    }

    return {
      id: data.id,
      reportId: data.report_id,
      reportName: (reportDetails as any).name || data.name || 'Unknown Report',
      reportType: (reportDetails as any).type || 'unknown',
      reportCategory: (reportDetails as any).category || 'unknown',
      name: data.name,
      frequency: data.frequency,
      dayOfWeek: data.day_of_week,
      dayOfMonth: data.day_of_month,
      month: data.month,
      time: data.time,
      nextRunAt: new Date(data.next_run_at),
      lastRunAt: data.last_run_at ? new Date(data.last_run_at) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      enabled: data.enabled,
      delivery: data.delivery,
      userId: data.user_id,
      parameters: data.parameters
    };
  }
}

/**
 * Updates a schedule
 *
 * @param scheduleId The ID of the schedule to update
 * @param updates The updates to apply
 * @returns The updated schedule
 */
export async function updateSchedule(
  scheduleId: string,
  updates: Partial<Omit<ReportSchedule, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<ReportSchedule> {
  const supabase = createClient();

  // Calculate next run date if frequency or time parameters are updated
  if (
    updates.frequency ||
    updates.dayOfWeek !== undefined ||
    updates.dayOfMonth !== undefined ||
    updates.month !== undefined ||
    updates.time
  ) {
    // Get current schedule to fill in missing parameters
    const currentSchedule = await getScheduleById(scheduleId);

    const frequency = updates.frequency || currentSchedule.frequency;
    const dayOfWeek = updates.dayOfWeek !== undefined ? updates.dayOfWeek : currentSchedule.dayOfWeek || 1;
    const dayOfMonth = updates.dayOfMonth !== undefined ? updates.dayOfMonth : currentSchedule.dayOfMonth || 1;
    const month = updates.month !== undefined ? updates.month : currentSchedule.month || 0;
    const time = updates.time || currentSchedule.time;

    updates.nextRunAt = calculateNextRunDate(frequency, dayOfWeek, dayOfMonth, month, time);
  }

  // Prepare the update object
  const updateData: any = {
    updated_at: new Date().toISOString()
  };

  if (updates.name) updateData.name = updates.name;
  if (updates.frequency) updateData.frequency = updates.frequency;
  if (updates.dayOfWeek !== undefined) updateData.day_of_week = updates.dayOfWeek;
  if (updates.dayOfMonth !== undefined) updateData.day_of_month = updates.dayOfMonth;
  if (updates.month !== undefined) updateData.month = updates.month;
  if (updates.time) updateData.time = updates.time;
  if (updates.nextRunAt) updateData.next_run_at = updates.nextRunAt.toISOString();
  if (updates.lastRunAt) updateData.last_run_at = updates.lastRunAt.toISOString();
  if (updates.enabled !== undefined) updateData.enabled = updates.enabled;
  if (updates.delivery) updateData.delivery = updates.delivery;
  if (updates.parameters) updateData.parameters = updates.parameters;

  const { data, error } = await supabase
    .from('report_schedules')
    .update(updateData)
    .eq('id', scheduleId)
    .select('*, reports(name, description, type, category)')
    .single();

  if (error) {
    throw new Error(`Error updating schedule: ${error.message}`);
  }

  return {
    id: data.id,
    reportId: data.report_id,
    reportName: data.reports?.name || 'Unknown Report',
    reportType: data.reports?.type || 'unknown',
    reportCategory: data.reports?.category || 'unknown',
    name: data.name,
    frequency: data.frequency,
    dayOfWeek: data.day_of_week,
    dayOfMonth: data.day_of_month,
    month: data.month,
    time: data.time,
    nextRunAt: new Date(data.next_run_at),
    lastRunAt: data.last_run_at ? new Date(data.last_run_at) : undefined,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    enabled: data.enabled,
    delivery: data.delivery,
    userId: data.user_id,
    parameters: data.parameters
  };
}

/**
 * Deletes a schedule
 *
 * @param scheduleId The ID of the schedule to delete
 */
export async function deleteSchedule(scheduleId: string): Promise<void> {
  const supabase = createClient();

  const { error } = await supabase
    .from('report_schedules')
    .delete()
    .eq('id', scheduleId);

  if (error) {
    throw new Error(`Error deleting schedule: ${error.message}`);
  }
}

/**
 * Enables or disables a schedule
 *
 * @param scheduleId The ID of the schedule to update
 * @param enabled Whether the schedule should be enabled
 * @returns The updated schedule
 */
export async function setScheduleEnabled(scheduleId: string, enabled: boolean): Promise<ReportSchedule> {
  return updateSchedule(scheduleId, { enabled });
}

/**
 * Creates a new execution record for a scheduled report
 *
 * @param scheduleId The ID of the schedule
 * @param reportId The ID of the report
 * @returns The ID of the created execution record
 */
export async function createExecution(scheduleId: string, reportId: string): Promise<string> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('report_executions')
    .insert({
      schedule_id: scheduleId,
      report_id: reportId,
      status: 'pending'
    })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Error creating execution record: ${error.message}`);
  }

  return data.id;
}

/**
 * Updates an execution record
 *
 * @param executionId The ID of the execution record
 * @param updates The updates to apply
 */
export async function updateExecution(
  executionId: string,
  updates: Partial<Omit<ReportExecution, 'id' | 'scheduleId' | 'reportId' | 'startedAt'>>
): Promise<void> {
  const supabase = createClient();

  // Prepare the update object
  const updateData: any = {};

  if (updates.completedAt) updateData.completed_at = updates.completedAt.toISOString();
  if (updates.status) updateData.status = updates.status;
  if (updates.error) updateData.error = updates.error;
  if (updates.resultId) updateData.result_id = updates.resultId;
  if (updates.deliveryStatus) updateData.delivery_status = updates.deliveryStatus;

  const { error } = await supabase
    .from('report_executions')
    .update(updateData)
    .eq('id', executionId);

  if (error) {
    throw new Error(`Error updating execution record: ${error.message}`);
  }
}

/**
 * Gets execution history for a schedule
 *
 * @param scheduleId The ID of the schedule
 * @returns An array of execution records
 */
export async function getExecutionHistory(scheduleId: string): Promise<ReportExecution[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('report_executions')
    .select('*')
    .eq('schedule_id', scheduleId)
    .order('started_at', { ascending: false });

  if (error) {
    throw new Error(`Error fetching execution history: ${error.message}`);
  }

  return data.map(item => ({
    id: item.id,
    scheduleId: item.schedule_id,
    reportId: item.report_id,
    startedAt: new Date(item.started_at),
    completedAt: item.completed_at ? new Date(item.completed_at) : undefined,
    status: item.status,
    error: item.error,
    resultId: item.result_id,
    deliveryStatus: item.delivery_status
  }));
}

/**
 * Gets schedules that need to be run
 *
 * @returns An array of schedules that need to be run
 */
export async function getSchedulesDueToRun(): Promise<ReportSchedule[]> {
  const supabase = createClient();

  const now = new Date();

  const { data, error } = await supabase
    .from('report_schedules')
    .select('*, reports(name, description, type, category)')
    .eq('enabled', true)
    .lte('next_run_at', now.toISOString());

  if (error) {
    throw new Error(`Error fetching schedules due to run: ${error.message}`);
  }

  return data.map(item => ({
    id: item.id,
    reportId: item.report_id,
    reportName: item.reports?.name || 'Unknown Report',
    reportType: item.reports?.type || 'unknown',
    reportCategory: item.reports?.category || 'unknown',
    name: item.name,
    frequency: item.frequency,
    dayOfWeek: item.day_of_week,
    dayOfMonth: item.day_of_month,
    month: item.month,
    time: item.time,
    nextRunAt: new Date(item.next_run_at),
    lastRunAt: item.last_run_at ? new Date(item.last_run_at) : undefined,
    createdAt: new Date(item.created_at),
    updatedAt: new Date(item.updated_at),
    enabled: item.enabled,
    delivery: item.delivery,
    userId: item.user_id,
    parameters: item.parameters
  }));
}

/**
 * Updates the next run date for a schedule
 *
 * @param scheduleId The ID of the schedule
 * @param lastRunAt The last run date
 * @returns The updated schedule
 */
export async function updateScheduleAfterRun(scheduleId: string, lastRunAt: Date): Promise<ReportSchedule> {
  const schedule = await getScheduleById(scheduleId);

  // Calculate the next run date
  const nextRunAt = calculateNextRunDate(
    schedule.frequency,
    schedule.dayOfWeek || 1,
    schedule.dayOfMonth || 1,
    schedule.month || 0,
    schedule.time
  );

  return updateSchedule(scheduleId, {
    lastRunAt,
    nextRunAt
  });
}
