import { createClient } from '@/utils/supabase/client';
import { ReportParams, ReportData, ReportType, CustomReport } from './types';
import { runCustomReport } from './custom-reports';

// Import report types if the file exists
let reportTypes: any[] = [];
try {
  reportTypes = require('./report-types').reportTypes;
} catch (error) {
  console.warn('Report types not found, using empty array');
  reportTypes = [];
}

/**
 * Fetches data for a specific report type with the given parameters
 *
 * @param reportType The type of report to fetch data for
 * @param params The parameters to use for fetching the data
 * @returns The report data
 */
export async function fetchReportData(
  reportType: string,
  params: ReportParams
): Promise<ReportData> {
  // Check if this is a custom report
  if (reportType.startsWith('custom-')) {
    try {
      // Extract the report ID from the reportType string
      const reportId = reportType.replace('custom-', '');

      // Fetch the custom report definition with specific column selection
      const supabase = createClient();
      const { data, error } = await supabase
        .from('custom_reports')
        .select('id, name, description, report_type, parameters, created_by, last_run, created_at, updated_at, data_source, fields, filters, group_by, sort_by, calculations, visualizations, is_template, is_favorite')
        .eq('id', reportId)
        .single();

      if (error) {
        throw new Error(`Error fetching custom report: ${error.message}`);
      }

      if (!data) {
        throw new Error(`Custom report with ID ${reportId} not found`);
      }

      // Transform the database record to a CustomReport object
      const customReport: CustomReport = {
        id: data.id,
        name: data.name,
        description: data.description,
        dataSource: data.data_source,
        fields: data.fields,
        filters: data.filters,
        groupBy: data.group_by,
        sortBy: data.sort_by,
        calculations: data.calculations,
        visualizations: data.visualizations,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        createdBy: data.created_by,
        isTemplate: data.is_template,
        isFavorite: data.is_favorite
      };

      // Run the custom report
      return await runCustomReport(customReport);
    } catch (error) {
      console.error('Error running custom report:', error);
      throw error;
    }
  }

  // For standard reports, find the report type implementation
  const reportImpl = reportTypes.find(r => r.id === reportType);

  if (!reportImpl) {
    throw new Error(`Report type "${reportType}" not found`);
  }

  // Call the report's data fetching function
  return await reportImpl.fetchData(params);
}

/**
 * Transforms raw data from the database into the format needed for display
 *
 * @param data The raw data from the database
 * @param reportType The type of report
 * @returns The transformed data
 */
export function transformReportData(data: any[], reportType: ReportType): any[] {
  // If the report type has a custom transform function, use it
  if (reportType.transformData) {
    return reportType.transformData(data);
  }

  // Otherwise, return the data as is
  return data;
}

/**
 * Applies filters to a Supabase query based on report parameters
 *
 * @param query The Supabase query to apply filters to
 * @param params The report parameters containing filters
 * @returns The modified query with filters applied
 */
export function applyFilters(query: any, params: ReportParams): any {
  const { filters, dateRange } = params;

  // Apply organization filter if specified
  if (filters.orgId) {
    query = query.eq('org_id', filters.orgId);
  }

  // Apply date range filters if specified
  if (dateRange?.start) {
    query = query.gte('created_at', dateRange.start);
  }

  if (dateRange?.end) {
    query = query.lte('created_at', dateRange.end);
  }

  // Apply client filter if specified
  if (filters.clientFilter === 'specific') {
    if (filters.clientId) {
      // Check if we're querying the households table directly
      if (query._from && query._from.includes('households')) {
        query = query.eq('id', filters.clientId);
      } else {
        // For other tables, assume the client ID is stored in household_id
        query = query.eq('household_id', filters.clientId);
      }
    }
    // If no clientId is provided, the report should handle this case
  } else if (filters.clientFilter === 'active') {
    // Logic for active clients
    // This would depend on how "active" is defined in your system
    // For now, we'll consider clients with a last_review date in the last year as active
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    if (query._from && query._from.includes('households')) {
      query = query.gte('last_review', oneYearAgo.toISOString());
    }
  } else if (filters.clientFilter === 'inactive') {
    // Logic for inactive clients
    // For now, we'll consider clients without a last_review date or with a last_review date older than a year as inactive
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    if (query._from && query._from.includes('households')) {
      query = query.or(`last_review.is.null,last_review.lt.${oneYearAgo.toISOString()}`);
    }
  }
  // If clientFilter is 'all', we don't apply any additional filters

  return query;
}

/**
 * Caches report data for improved performance
 *
 * @param cacheKey The key to use for caching
 * @param data The data to cache
 * @param ttl Time to live in seconds
 */
export function cacheReportData(cacheKey: string, data: any, ttl: number = 300): void {
  // Simple in-memory cache implementation
  // In a production app, you might use a more robust caching solution
  const cache = getReportCache();

  cache[cacheKey] = {
    data,
    expiry: Date.now() + (ttl * 1000)
  };

  // Store in sessionStorage for persistence across page refreshes
  try {
    sessionStorage.setItem('reportCache', JSON.stringify(cache));
  } catch (error) {
    console.error('Error storing report cache:', error);
  }
}

/**
 * Gets cached report data if available
 *
 * @param cacheKey The key to look up in the cache
 * @returns The cached data or null if not found or expired
 */
export function getCachedReportData(cacheKey: string): any | null {
  const cache = getReportCache();
  const cachedItem = cache[cacheKey];

  if (cachedItem && cachedItem.expiry > Date.now()) {
    return cachedItem.data;
  }

  return null;
}

/**
 * Gets the report cache object
 *
 * @returns The report cache object
 */
function getReportCache(): Record<string, { data: any, expiry: number }> {
  try {
    const cached = sessionStorage.getItem('reportCache');
    return cached ? JSON.parse(cached) : {};
  } catch (error) {
    console.error('Error retrieving report cache:', error);
    return {};
  }
}

/**
 * Generates a cache key for a report
 *
 * @param reportType The type of report
 * @param params The report parameters
 * @returns A cache key string
 */
export function generateCacheKey(reportType: string, params: ReportParams): string {
  return `${reportType}:${JSON.stringify(params)}`;
}
