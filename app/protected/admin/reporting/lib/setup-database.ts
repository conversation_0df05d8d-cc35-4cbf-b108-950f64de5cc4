import { createClient } from '@/utils/supabase/client';
import { toast } from '@/hooks/use-toast';

/**
 * Sets up the database tables and functions for the advanced reporting features
 */
export async function setupAdvancedReportingDatabase(): Promise<boolean> {
  const supabase = createClient();

  try {
    // Check if the custom_reports table exists
    const { error: checkError } = await supabase
      .from('custom_reports')
      .select('id')
      .limit(1);

    // If the table exists, we don't need to run the setup
    if (!checkError) {
      console.log('Advanced reporting database already set up');
      return true;
    }

    // Use inline SQL for database setup
    const sql = `
    -- Custom Reports Tables

    -- Table for storing custom report definitions
    CREATE TABLE IF NOT EXISTS custom_reports (
      id UUID PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      data_source TEXT NOT NULL,
      fields JSONB NOT NULL,
      filters JSONB NOT NULL,
      group_by JSONB,
      sort_by <PERSON>SONB,
      calculations JSONB,
      visualizations JSON<PERSON>,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      created_by UUID NOT NULL REFERENCES auth.users(id),
      is_template BOOLEAN NOT NULL DEFAULT FALSE,
      is_favorite BOOLEAN NOT NULL DEFAULT FALSE
    );

    -- Function to run custom SQL queries safely
    CREATE OR REPLACE FUNCTION run_custom_query(query_text TEXT)
    RETURNS JSONB
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      result JSONB;
    BEGIN
      -- Execute the query and convert results to JSON
      EXECUTE 'SELECT json_agg(row_to_json(t)) FROM (' || query_text || ') t' INTO result;
      RETURN result;
    EXCEPTION
      WHEN OTHERS THEN
        RETURN json_build_object('error', SQLERRM);
    END;
    $$;

    -- Function to get table columns
    CREATE OR REPLACE FUNCTION get_table_columns(p_table_name TEXT)
    RETURNS TABLE (
      column_name TEXT,
      data_type TEXT
    )
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      RETURN QUERY
      SELECT c.column_name::TEXT, c.data_type::TEXT
      FROM information_schema.columns c
      WHERE c.table_schema = 'public'
        AND c.table_name = p_table_name
      ORDER BY c.ordinal_position;
    END;
    $$;

    -- Row-level security policies
    ALTER TABLE custom_reports ENABLE ROW LEVEL SECURITY;

    -- Policy for selecting custom reports
    CREATE POLICY select_custom_reports ON custom_reports
      FOR SELECT
      USING (
        created_by = auth.uid() OR
        is_template = TRUE
      );

    -- Policy for inserting custom reports
    CREATE POLICY insert_custom_reports ON custom_reports
      FOR INSERT
      WITH CHECK (
        created_by = auth.uid()
      );

    -- Policy for updating custom reports
    CREATE POLICY update_custom_reports ON custom_reports
      FOR UPDATE
      USING (
        created_by = auth.uid()
      );

    -- Policy for deleting custom reports
    CREATE POLICY delete_custom_reports ON custom_reports
      FOR DELETE
      USING (
        created_by = auth.uid()
      );
    `;

    // Run the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      throw error;
    }

    console.log('Advanced reporting database set up successfully');
    return true;
  } catch (error) {
    console.error('Error setting up advanced reporting database:', error);
    toast({
      title: 'Database Setup Error',
      description: `Failed to set up advanced reporting database: ${error instanceof Error ? error.message : String(error)}`,
      variant: 'destructive'
    });
    return false;
  }
}

/**
 * Checks if the advanced reporting database is set up
 */
export async function isAdvancedReportingDatabaseSetup(): Promise<boolean> {
  const supabase = createClient();

  try {
    // Check if the custom_reports table exists
    const { error } = await supabase
      .from('custom_reports')
      .select('id')
      .limit(1);

    return !error;
  } catch (error) {
    console.error('Error checking advanced reporting database:', error);
    return false;
  }
}
