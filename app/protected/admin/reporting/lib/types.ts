import { ColumnDef } from '@tanstack/react-table';

/**
 * Column definition for AI-generated reports
 */
export interface Column {
  name: string;
  table: string;
  alias?: string;
  aggregate?: string;
  displayName?: string;
}

/**
 * Filter definition for AI-generated reports
 */
export interface Filter {
  field: string;
  operator: string;
  value: string;
  table?: string;
}

/**
 * Join table definition for AI-generated reports
 */
export interface JoinTable {
  table: string;
  joinField: string;
  foreignField: string;
  joinType: string;
  type?: string;
}

/**
 * Group by field definition for AI-generated reports
 */
export interface GroupByField {
  field: string;
  table?: string;
}

/**
 * Sort field definition for AI-generated reports
 */
export interface SortField {
  field: string;
  table?: string;
  aggregate?: string;
  direction?: 'asc' | 'desc';
}

/**
 * Report parameters for configuring report generation
 */
export interface ReportParams {
  dateRange?: {
    start?: string;
    end?: string;
  };
  filters: Record<string, any>;
  groupBy?: string[];
  sortBy?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

/**
 * Report field definition for custom reports
 */
export interface ReportField {
  id: string;
  name: string;
  sourceField: string;
  displayName: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
  format?: string;
  aggregation?: 'sum' | 'avg' | 'min' | 'max' | 'count';
  isVisible: boolean;
  width?: number;
}

/**
 * Filter condition for advanced filtering
 */
export interface FilterCondition {
  id: string;
  field: string;
  operator: 'equals' | 'notEquals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in';
  value: any;
  valueEnd?: any; // For 'between' operator
  valueList?: any[]; // For 'in' operator
  logic?: 'and' | 'or'; // Relation to next condition
}

/**
 * Sort option for custom reports
 */
export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Calculation definition for custom reports
 */
export interface Calculation {
  id: string;
  name: string;
  formula: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
  format?: string;
}

/**
 * Visualization definition for custom reports
 */
export interface Visualization {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'heatmap';
  title: string;
  xAxis?: string;
  yAxis?: string;
  series: VisualizationSeries[];
  options?: Record<string, any>;
}

/**
 * Visualization series definition
 */
export interface VisualizationSeries {
  field: string;
  name: string;
  color?: string;
  type?: 'bar' | 'line' | 'area';
}

/**
 * Custom report definition
 */
export interface CustomReport {
  id: string;
  name: string;
  description?: string;
  tableDefinition?: string; // Technical description including SQL details
  dataSource: string; // Table or view name
  fields: ReportField[];
  filters: FilterCondition[];
  groupBy?: string[];
  sortBy?: SortOption[];
  calculations?: Calculation[];
  visualizations?: Visualization[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // User ID
  isTemplate: boolean;
  isFavorite: boolean;
  // Additional properties for joins and related tables
  related_tables?: any[];
  joins?: any[];
  result_limit?: number;
  metadata?: any;
}

/**
 * Report data returned from a report generation
 */
export interface ReportData {
  id: string;
  reportId: string;
  runAt: Date;
  parameters: ReportParams;
  data: any[];
  columns: ColumnDef<any, any>[];
  summary?: Record<string, any>;
  chartData?: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string[];
      borderColor?: string;
      fill?: boolean;
    }[];
  };
  recommendedChartType?: 'bar' | 'line' | 'pie' | 'area' | 'card';
}

/**
 * Report type definition
 */
export interface ReportType {
  id: string;
  name: string;
  description: string;
  category: 'client' | 'financial' | 'performance' | 'operational' | 'compliance';
  icon: any; // This should be a Lucide icon component
  defaultParams: Record<string, any>;
  fetchData: (params: ReportParams) => Promise<ReportData>;
  transformData?: (data: any[]) => any[];
  getColumns: () => ColumnDef<any, any>[];
}

/**
 * Report configuration
 */
export interface ReportConfig {
  id?: string;
  name: string;
  description: string;
  type: string;
  category: string;
  filters: Record<string, any>;
  outputFormat: 'table' | 'chart';
  chartType?: 'bar' | 'line' | 'pie' | 'area' | 'card';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    dayOfWeek?: number; // 0-6, Sunday to Saturday (for weekly)
    dayOfMonth?: number; // 1-31 (for monthly, quarterly, yearly)
    month?: number; // 0-11 (for quarterly and yearly)
    time?: string; // HH:MM in 24-hour format
  };
}

/**
 * Export options for reports
 */
export interface ExportOptions {
  format: 'excel' | 'pdf';
  fileName: string;
  includeFilters: boolean;
  orientation?: 'portrait' | 'landscape';
  pageSize?: 'a4' | 'letter';
}

/**
 * Report history entry
 */
export interface ReportHistory {
  id: string;
  reportId: string;
  runAt: Date;
  parameters: ReportParams;
  userId: string;
  status: 'success' | 'error';
  errorMessage?: string;
}

/**
 * Report schedule definition
 */
export interface ReportSchedule {
  id: string;
  reportId: string;
  reportName?: string;
  reportType?: string;
  reportCategory?: string;
  name: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  dayOfWeek?: number; // 0-6, Sunday to Saturday (for weekly)
  dayOfMonth?: number; // 1-31 (for monthly)
  month?: number; // 0-11 (for quarterly and yearly)
  time: string; // HH:MM in 24-hour format
  nextRunAt: Date;
  lastRunAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  enabled: boolean;
  delivery: {
    method: 'email' | 'notification' | 'download';
    recipients?: string[]; // Email addresses
    subject?: string; // For email
    message?: string; // For email or notification
  };
  userId: string;
  parameters?: Record<string, any>; // Report parameters
}

/**
 * Report execution definition
 */
export interface ReportExecution {
  id: string;
  scheduleId: string;
  reportId: string;
  startedAt: Date;
  completedAt?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
  resultId?: string; // Reference to the report result
  deliveryStatus?: {
    method: 'email' | 'notification' | 'download';
    status: 'pending' | 'sent' | 'failed';
    sentAt?: Date;
    error?: string;
  };
}
