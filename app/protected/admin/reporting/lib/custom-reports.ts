import { createClient } from '@/utils/supabase/client';
import { CustomReport, ReportData, FilterCondition, Visualization } from './types';

/**
 * Save a custom report to the database
 */
export async function saveCustomReport(report: CustomReport): Promise<string> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  // Ensure the report has a valid ID
  if (!report.id) {
    report.id = crypto.randomUUID();
  }

  // Set created/updated timestamps
  const now = new Date();
  if (!report.createdAt) {
    report.createdAt = now;
  }
  report.updatedAt = now;
  report.createdBy = userData.user.id;

  // Insert or update the report
  const { data, error } = await supabase
    .from('custom_reports')
    .upsert({
      id: report.id,
      name: report.name,
      description: report.description || '',
      table_definition: report.tableDefinition || '',
      data_source: report.dataSource,
      fields: report.fields,
      filters: report.filters,
      group_by: report.groupBy || [],
      sort_by: report.sortBy || [],
      calculations: report.calculations || [],
      visualizations: report.visualizations || [],
      created_at: report.createdAt,
      updated_at: report.updatedAt,
      created_by: report.createdBy,
      is_template: report.isTemplate,
      is_favorite: report.isFavorite,
      related_tables: report.related_tables || [],
      joins: report.joins || [],
      result_limit: report.result_limit,
      metadata: report.metadata || {}
    })
    .select('id')
    .single();

  if (error) {
    console.error('Error saving custom report:', error);
    throw new Error(`Error saving report: ${error.message}`);
  }

  return data.id;
}

/**
 * Get all custom reports for the current user
 */
export async function getCustomReports(): Promise<CustomReport[]> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('custom_reports')
    .select('*')
    .or(`created_by.eq.${userData.user.id},is_template.eq.true`)
    .order('updated_at', { ascending: false });

  if (error) {
    console.error('Error fetching custom reports:', error);
    throw new Error(`Error fetching reports: ${error.message}`);
  }

  // Transform database records to CustomReport objects
  return data.map(record => ({
    id: record.id,
    name: record.name,
    description: record.description,
    tableDefinition: record.table_definition,
    dataSource: record.data_source,
    fields: record.fields,
    filters: record.filters,
    groupBy: record.group_by,
    sortBy: record.sort_by,
    calculations: record.calculations,
    visualizations: record.visualizations,
    createdAt: new Date(record.created_at),
    updatedAt: new Date(record.updated_at),
    createdBy: record.created_by,
    isTemplate: record.is_template,
    isFavorite: record.is_favorite,
    related_tables: record.related_tables || [],
    joins: record.joins || [],
    result_limit: record.result_limit,
    metadata: record.metadata || {}
  }));
}

/**
 * Get a specific custom report by ID
 */
export async function getCustomReportById(id: string): Promise<CustomReport> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('custom_reports')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching custom report:', error);
    throw new Error(`Error fetching report: ${error.message}`);
  }

  // Transform database record to CustomReport object
  return {
    id: data.id,
    name: data.name,
    description: data.description,
    tableDefinition: data.table_definition,
    dataSource: data.data_source,
    fields: data.fields,
    filters: data.filters,
    groupBy: data.group_by,
    sortBy: data.sort_by,
    calculations: data.calculations,
    visualizations: data.visualizations,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    createdBy: data.created_by,
    isTemplate: data.is_template,
    isFavorite: data.is_favorite,
    related_tables: data.related_tables || [],
    joins: data.joins || [],
    result_limit: data.result_limit,
    metadata: data.metadata || {}
  };
}

/**
 * Delete a custom report
 */
export async function deleteCustomReport(id: string): Promise<void> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  // Check if the user owns the report
  const { data: reportData, error: reportError } = await supabase
    .from('custom_reports')
    .select('created_by')
    .eq('id', id)
    .single();

  if (reportError) {
    console.error('Error fetching report for deletion:', reportError);
    throw new Error(`Error fetching report: ${reportError.message}`);
  }

  if (reportData.created_by !== userData.user.id) {
    throw new Error('You do not have permission to delete this report');
  }

  // Delete the report
  const { error } = await supabase
    .from('custom_reports')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting custom report:', error);
    throw new Error(`Error deleting report: ${error.message}`);
  }
}

/**
 * Toggle favorite status of a report
 */
export async function toggleFavoriteReport(id: string, isFavorite: boolean): Promise<void> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  const { error } = await supabase
    .from('custom_reports')
    .update({ is_favorite: isFavorite })
    .eq('id', id)
    .eq('created_by', userData.user.id);

  if (error) {
    console.error('Error updating favorite status:', error);
    throw new Error(`Error updating favorite status: ${error.message}`);
  }
}

/**
 * Save a report as a template
 */
export async function saveReportAsTemplate(id: string): Promise<string> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  // Get the report to clone
  const { data: reportData, error: reportError } = await supabase
    .from('custom_reports')
    .select('*')
    .eq('id', id)
    .single();

  if (reportError) {
    console.error('Error fetching report for template:', reportError);
    throw new Error(`Error fetching report: ${reportError.message}`);
  }

  // Create a new report as a template
  const templateReport = {
    id: crypto.randomUUID(),
    name: `${reportData.name} (Template)`,
    description: reportData.description,
    table_definition: reportData.table_definition,
    data_source: reportData.data_source,
    fields: reportData.fields,
    filters: reportData.filters,
    group_by: reportData.group_by,
    sort_by: reportData.sort_by,
    calculations: reportData.calculations,
    visualizations: reportData.visualizations,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: userData.user.id,
    is_template: true,
    is_favorite: false,
    related_tables: reportData.related_tables || [],
    joins: reportData.joins || [],
    result_limit: reportData.result_limit,
    metadata: reportData.metadata || {}
  };

  const { data, error } = await supabase
    .from('custom_reports')
    .insert(templateReport)
    .select('id')
    .single();

  if (error) {
    console.error('Error saving report template:', error);
    throw new Error(`Error saving template: ${error.message}`);
  }

  return data.id;
}

/**
 * Get all report templates
 */
export async function getReportTemplates(): Promise<CustomReport[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('custom_reports')
    .select('*')
    .eq('is_template', true)
    .order('name');

  if (error) {
    console.error('Error fetching report templates:', error);
    throw new Error(`Error fetching templates: ${error.message}`);
  }

  // Transform database records to CustomReport objects
  return data.map(record => ({
    id: record.id,
    name: record.name,
    description: record.description,
    tableDefinition: record.table_definition,
    dataSource: record.data_source,
    fields: record.fields,
    filters: record.filters,
    groupBy: record.group_by,
    sortBy: record.sort_by,
    calculations: record.calculations,
    visualizations: record.visualizations,
    createdAt: new Date(record.created_at),
    updatedAt: new Date(record.updated_at),
    createdBy: record.created_by,
    isTemplate: record.is_template,
    isFavorite: record.is_favorite,
    related_tables: record.related_tables || [],
    joins: record.joins || [],
    result_limit: record.result_limit,
    metadata: record.metadata || {}
  }));
}

/**
 * Get all favorite reports for the current user
 */
export async function getFavoriteReports(): Promise<CustomReport[]> {
  const supabase = createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('custom_reports')
    .select('*')
    .eq('created_by', userData.user.id)
    .eq('is_favorite', true)
    .order('updated_at', { ascending: false });

  if (error) {
    console.error('Error fetching favorite reports:', error);
    throw new Error(`Error fetching favorites: ${error.message}`);
  }

  // Transform database records to CustomReport objects
  return data.map(record => ({
    id: record.id,
    name: record.name,
    description: record.description,
    tableDefinition: record.table_definition,
    dataSource: record.data_source,
    fields: record.fields,
    filters: record.filters,
    groupBy: record.group_by,
    sortBy: record.sort_by,
    calculations: record.calculations,
    visualizations: record.visualizations,
    createdAt: new Date(record.created_at),
    updatedAt: new Date(record.updated_at),
    createdBy: record.created_by,
    isTemplate: record.is_template,
    isFavorite: record.is_favorite,
    related_tables: record.related_tables || [],
    joins: record.joins || [],
    result_limit: record.result_limit,
    metadata: record.metadata || {}
  }));
}

/**
 * Validate a SQL query by executing it
 */
export async function validateSqlQuery(query: string): Promise<{ isValid: boolean; error?: string; data?: any }> {
  const supabase = createClient();

  try {
    // Execute the query
    const { data, error } = await supabase.rpc('run_custom_query', {
      query_text: query
    });

    if (error) {
      console.error('Error validating SQL query:', error);
      return { isValid: false, error: error.message };
    }

    // Check if data is an array
    if (!Array.isArray(data)) {
      console.error('Expected array of results but got:', data);
      return { isValid: false, error: 'Invalid query result format' };
    }

    return { isValid: true, data };
  } catch (error) {
    console.error('Error validating SQL query:', error);
    return { isValid: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Run a custom report and return the results
 */
export async function runCustomReport(report: CustomReport): Promise<ReportData> {
  const supabase = createClient();

  try {
    console.log('REPORT FLOW - Running custom report:', {
      reportId: report.id,
      reportName: report.name,
      dataSource: report.dataSource
    });
    console.log('REPORT FLOW - Report fields:', report.fields);
    console.log('REPORT FLOW - Report joins:', report.joins);
    console.log('REPORT FLOW - Report related tables:', report.related_tables);

    // Check if this is an AI-generated report with joins
    const isAIGenerated = (report.tableDefinition && (
      report.tableDefinition.includes('SQL Query:') ||
      report.tableDefinition.includes('Joins:')
    )) || (report.description && (
      report.description.includes('SQL Query:') ||
      report.description.includes('Joins:') ||
      report.description.includes('AI-Generated Report')
    ));
    console.log('Is AI-generated report:', isAIGenerated);

    // For AI-generated reports, check if the fields have been updated
    // If so, we need to regenerate the SQL query
    if (isAIGenerated && report.fields.length > 0) {
      console.log('REPORT FLOW - Checking if AI-generated report fields have been updated');

      try {
        // Ensure groupBy is an array
        if (!report.groupBy || !Array.isArray(report.groupBy)) {
          report.groupBy = [];
        }

        // Import the buildSqlQuery function
        const { buildSqlQuery } = await import('./custom-reports-utils');

        // Generate a new SQL query based on the updated fields
        const newSql = buildSqlQuery(report);
        console.log('REPORT FLOW - Generated new SQL query for AI report:', newSql);
      } catch (error) {
        console.error('Error generating SQL query in runCustomReport:', error);
      }
    }

    // Check if we need to add joins from related tables
    // First, check if any fields are from tables other than the data source
    const fieldsFromOtherTables = report.fields.filter(field => {
      if (field.sourceField.includes('.')) {
        // Field already has a table reference
        return false;
      }

      // Check if this field exists in the data source table
      const tableFieldMap: Record<string, string[]> = {
        'households': ['id', 'householdName', 'members', 'user_id', 'address', 'phone', 'email',
                      'occupation', 'employer', 'marital_status', 'date_of_birth', 'tax_file_number',
                      'notes', 'street', 'city', 'state', 'zip_code', 'country', 'property_type',
                      'preferred_contact', 'best_time_to_call', 'alternative_contact',
                      'investment_strategy', 'risk_tolerance', 'primary_advisor', 'last_review',
                      'next_review', 'additional_info', 'org_id', 'created_at', 'updated_at'],
        'assets': ['id', 'household_id', 'name', 'type', 'value', 'details', 'created_at', 'updated_at',
                  'property_type', 'rental_income', 'provider', 'linked_income_id'],
        'income': ['id', 'household_id', 'source', 'amount', 'frequency', 'details', 'created_at',
                  'updated_at', 'income_type', 'linked_asset_id', 'member_id'],
        'expenses': ['id', 'household_id', 'name', 'amount', 'frequency', 'category', 'details',
                    'created_at', 'updated_at', 'linked_liability_id'],
        'liabilities': ['id', 'household_id', 'name', 'amount', 'interest_rate', 'lender', 'details',
                      'created_at', 'updated_at', 'linked_asset_id', 'linked_expense_id'],
        'goals': ['id', 'household_id', 'title', 'description', 'target_date', 'target_amount',
                'current_amount', 'status', 'priority', 'created_at', 'updated_at'],
        'tasks': ['id', 'household_id', 'title', 'description', 'due_date', 'status', 'priority',
                'assigned_to', 'created_at', 'updated_at'],
        'interactions': ['id', 'household_id', 'title', 'content', 'date', 'type', 'created_at', 'updated_at'],
        'recommendations': ['id', 'household_id', 'title', 'description', 'status', 'financial_impact',
                          'created_at', 'updated_at']
      };

      // Check if field exists in data source table
      const existsInDataSource = tableFieldMap[report.dataSource]?.includes(field.sourceField);

      // If it doesn't exist in the data source, find which table it belongs to
      if (!existsInDataSource) {
        // Find which table contains this field
        for (const [tableName, fields] of Object.entries(tableFieldMap)) {
          if (tableName !== report.dataSource && fields.includes(field.sourceField)) {
            console.log(`Field "${field.sourceField}" found in table "${tableName}" instead of "${report.dataSource}"`);
            return true;
          }
        }
      }

      return false;
    });

    console.log('Fields from other tables:', fieldsFromOtherTables);

    if (!isAIGenerated && fieldsFromOtherTables.length > 0) {
      console.log('Report contains fields from multiple tables but no joins defined');

      // If we have fields from multiple tables but no joins defined, we need to add joins
      if (!report.joins) {
        report.joins = [];
      }

      // Create a map of tables that need to be joined
      const tableFieldMap: Record<string, string[]> = {
        'households': ['id', 'householdName', 'members', 'user_id', 'address', 'phone', 'email',
                      'occupation', 'employer', 'marital_status', 'date_of_birth', 'tax_file_number',
                      'notes', 'street', 'city', 'state', 'zip_code', 'country', 'property_type',
                      'preferred_contact', 'best_time_to_call', 'alternative_contact',
                      'investment_strategy', 'risk_tolerance', 'primary_advisor', 'last_review',
                      'next_review', 'additional_info', 'org_id', 'created_at', 'updated_at'],
        'assets': ['id', 'household_id', 'name', 'type', 'value', 'details', 'created_at', 'updated_at',
                  'property_type', 'rental_income', 'provider', 'linked_income_id'],
        'income': ['id', 'household_id', 'source', 'amount', 'frequency', 'details', 'created_at',
                  'updated_at', 'income_type', 'linked_asset_id', 'member_id'],
        'expenses': ['id', 'household_id', 'name', 'amount', 'frequency', 'category', 'details',
                    'created_at', 'updated_at', 'linked_liability_id'],
        'liabilities': ['id', 'household_id', 'name', 'amount', 'interest_rate', 'lender', 'details',
                      'created_at', 'updated_at', 'linked_asset_id', 'linked_expense_id'],
        'goals': ['id', 'household_id', 'title', 'description', 'target_date', 'target_amount',
                'current_amount', 'status', 'priority', 'created_at', 'updated_at'],
        'tasks': ['id', 'household_id', 'title', 'description', 'due_date', 'status', 'priority',
                'assigned_to', 'created_at', 'updated_at'],
        'interactions': ['id', 'household_id', 'title', 'content', 'date', 'type', 'created_at', 'updated_at'],
        'recommendations': ['id', 'household_id', 'title', 'description', 'status', 'financial_impact',
                          'created_at', 'updated_at']
      };

      // For each field from another table, add a join
      fieldsFromOtherTables.forEach(field => {
        // Find which table contains this field
        for (const [tableName, fields] of Object.entries(tableFieldMap)) {
          if (tableName !== report.dataSource && fields.includes(field.sourceField)) {
            // Only add if not already joined
            if (!report.joins?.some(join => join.table === tableName)) {
              console.log(`Adding join for table "${tableName}" to access field "${field.sourceField}"`);

              // Determine join direction based on tables
              let joinConfig;

              if (tableName === 'households') {
                // If joining to households, use household_id -> id
                joinConfig = {
                  table: tableName,
                  joinType: 'LEFT',
                  joinField: 'household_id',
                  foreignField: 'id'
                };
              } else if (report.dataSource === 'households') {
                // If joining from households, use id -> household_id
                joinConfig = {
                  table: tableName,
                  joinType: 'LEFT',
                  joinField: 'id',
                  foreignField: 'household_id'
                };
              } else {
                // For other tables, join through households
                // First, add a join to households if not already present
                if (!report.joins?.some(join => join.table === 'households')) {
                  report.joins?.push({
                    table: 'households',
                    joinType: 'LEFT',
                    joinField: 'household_id',
                    foreignField: 'id'
                  });
                }

                // Then add a join from households to the target table
                joinConfig = {
                  table: tableName,
                  joinType: 'LEFT',
                  joinField: 'id',
                  foreignField: 'household_id'
                };
              }

              report.joins?.push(joinConfig);
            }
            break;
          }
        }
      });

      console.log('Updated joins:', report.joins);
    }

    // Build the SQL query based on the report definition
    const query = buildSqlQuery(report);

    // Log the SQL query for debugging
    console.log('Generated SQL query:', query);

    // Execute the query
    console.log('Executing query via Supabase RPC');
    const { data, error } = await supabase.rpc('run_custom_query', {
      query_text: query
    });

    if (error) {
      console.error('Error running custom report:', error);
      throw new Error(`Error running report: ${error.message}`);
    }

    // Log the data structure to understand what we're getting back
    console.log('Query result data:', data);

    // Check if data is an array
    if (!Array.isArray(data)) {
      console.error('Expected array of results but got:', data);
      throw new Error('Invalid query result format');
    }

    // If data is empty, return empty results
    if (data.length === 0) {
      return {
        id: `custom-${report.id}-${Date.now()}`,
        reportId: report.id,
        runAt: new Date(),
        parameters: { filters: [] },
        data: [],
        columns: generateColumns(report),
        recommendedChartType: 'bar' as 'bar'
      };
    }

    let transformedData;
    let columns;

    if (isAIGenerated) {
      // For AI-generated reports, we'll use the data directly
      transformedData = data;

      // Generate columns dynamically based on the first row of data
      if (data.length > 0) {
        const firstRow = data[0];
        columns = Object.keys(firstRow).map(key => {
          const value = firstRow[key];
          const isNumeric = typeof value === 'number';
          const isCurrency = isNumeric && (
            key.toLowerCase().includes('amount') ||
            key.toLowerCase().includes('income') ||
            key.toLowerCase().includes('assets') ||
            key.toLowerCase().includes('total')
          );

          return {
            accessorKey: key,
            header: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' '),
            cell: ({ getValue }: any) => {
              const cellValue = getValue();

              if (typeof cellValue === 'number') {
                if (isCurrency) {
                  return formatCurrency(cellValue);
                }
                return cellValue.toLocaleString();
              }

              if (typeof cellValue === 'object' && cellValue !== null) {
                return JSON.stringify(cellValue);
              }

              return cellValue;
            }
          };
        });
      } else {
        // Fallback to generated columns if no data
        columns = generateColumns(report);
      }
    } else {
      // For manually created reports, use the standard transformation
      transformedData = transformReportData(data, report);
      columns = generateColumns(report);
    }

    // Generate chart data if visualizations are defined
    const chartData = report.visualizations && report.visualizations.length > 0
      ? generateChartData(transformedData, report.visualizations[0])
      : undefined;

    // Determine the recommended chart type
    const recommendedChartType = report.visualizations && report.visualizations.length > 0
      ? report.visualizations[0].type
      : 'table';

    // Return the report data
    return {
      id: `custom-${report.id}-${Date.now()}`,
      reportId: report.id,
      runAt: new Date(),
      parameters: {
        filters: {},
        groupBy: report.groupBy,
        sortBy: report.sortBy && report.sortBy.length > 0
          ? {
              field: report.sortBy[0].field,
              direction: report.sortBy[0].direction
            }
          : undefined
      },
      data: transformedData,
      columns: columns,
      chartData,
      recommendedChartType: recommendedChartType as any
    };
  } catch (error) {
    console.error('Error running custom report:', error);
    throw new Error(`Error running report: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Find which table a field belongs to
 */
function findFieldTableInfo(fieldName: string, defaultTable: string, joinTables: any[]): { tableName: string, fieldExists: boolean } {
  console.log(`Finding table for field "${fieldName}" (default table: "${defaultTable}", joins:`, joinTables, ')');

  // If the field already has a table reference, extract it
  if (fieldName && fieldName.includes('.')) {
    const [tableName] = fieldName.split('.');
    console.log(`Field "${fieldName}" has explicit table reference: "${tableName}"`);
    return { tableName, fieldExists: true };
  }

  // If the field name looks like it might be a table_field format
  if (fieldName && fieldName.includes('_')) {
    const parts = fieldName.split('_');
    // Check if the first part is a valid table name
    const possibleTableName = parts[0];
    const commonTableNames = ['households', 'assets', 'income', 'expenses', 'liabilities', 'goals', 'tasks', 'interactions', 'recommendations'];

    if (commonTableNames.includes(possibleTableName)) {
      console.log(`Field "${fieldName}" appears to be from table "${possibleTableName}" based on name format`);
      return { tableName: possibleTableName, fieldExists: true };
    }
  }

  // Common fields in different tables
  const tableFieldMap: Record<string, string[]> = {
    'households': ['id', 'householdName', 'members', 'user_id', 'address', 'phone', 'email',
                  'occupation', 'employer', 'marital_status', 'date_of_birth', 'tax_file_number',
                  'notes', 'street', 'city', 'state', 'zip_code', 'country', 'property_type',
                  'preferred_contact', 'best_time_to_call', 'alternative_contact',
                  'investment_strategy', 'risk_tolerance', 'primary_advisor', 'last_review',
                  'next_review', 'additional_info', 'org_id', 'created_at', 'updated_at'],
    'assets': ['id', 'household_id', 'name', 'type', 'value', 'details', 'created_at', 'updated_at',
              'property_type', 'rental_income', 'provider', 'linked_income_id'],
    'income': ['id', 'household_id', 'source', 'amount', 'frequency', 'details', 'created_at',
              'updated_at', 'income_type', 'linked_asset_id', 'member_id'],
    'expenses': ['id', 'household_id', 'name', 'amount', 'frequency', 'category', 'details',
                'created_at', 'updated_at', 'linked_liability_id'],
    'liabilities': ['id', 'household_id', 'name', 'amount', 'interest_rate', 'lender', 'details',
                   'created_at', 'updated_at', 'linked_asset_id', 'linked_expense_id'],
    'goals': ['id', 'household_id', 'title', 'description', 'target_date', 'target_amount',
             'current_amount', 'status', 'priority', 'created_at', 'updated_at'],
    'tasks': ['id', 'household_id', 'title', 'description', 'due_date', 'status', 'priority',
             'assigned_to', 'created_at', 'updated_at'],
    'interactions': ['id', 'household_id', 'title', 'content', 'date', 'type', 'created_at', 'updated_at'],
    'recommendations': ['id', 'household_id', 'title', 'description', 'status', 'financial_impact',
                       'created_at', 'updated_at']
  };

  // First, check if the field exists in the default table
  console.log(`Checking if field "${fieldName}" exists in default table "${defaultTable}"`);
  if (tableFieldMap[defaultTable] && tableFieldMap[defaultTable].includes(fieldName)) {
    console.log(`Field "${fieldName}" found in default table "${defaultTable}"`);
    return { tableName: defaultTable, fieldExists: true };
  }

  // If not found in the default table, check if it exists in any of the joined tables
  for (const join of joinTables) {
    const tableName = join.table;
    console.log(`Checking if field "${fieldName}" exists in joined table "${tableName}"`);

    if (tableFieldMap[tableName] && tableFieldMap[tableName].includes(fieldName)) {
      console.log(`Field "${fieldName}" found in joined table "${tableName}"`);
      return { tableName, fieldExists: true };
    }
  }

  // If not found in joined tables, check all other tables
  for (const [tableName, fields] of Object.entries(tableFieldMap)) {
    if (tableName !== defaultTable && !joinTables.some(join => join.table === tableName)) {
      if (fields.includes(fieldName)) {
        console.log(`Field "${fieldName}" found in non-joined table "${tableName}"`);
        return { tableName, fieldExists: true };
      }
    }
  }

  // If not found anywhere, default to the primary table
  console.log(`Field "${fieldName}" not found in any table, defaulting to "${defaultTable}"`);
  return { tableName: defaultTable, fieldExists: false };
}

/**
 * Build an SQL query from a custom report definition
 */
function buildSqlQuery(report: CustomReport): string {
  // First check if we have a tableDefinition with SQL query
  if (report.tableDefinition && report.tableDefinition.includes('SQL Query:')) {
    const sqlQueryMatch = report.tableDefinition.match(/SQL Query: ([\s\S]*?)(?:\n\n|$)/);
    if (sqlQueryMatch && sqlQueryMatch[1]) {
      // Use the SQL query from the tableDefinition
      return sqlQueryMatch[1].trim();
    }
  }

  // Fallback to checking the description for backward compatibility
  if (report.description && report.description.includes('SQL Query:')) {
    const sqlQueryMatch = report.description.match(/SQL Query: ([\s\S]*?)(?:\n\n|$)/);
    if (sqlQueryMatch && sqlQueryMatch[1]) {
      // Use the SQL query from the description
      return sqlQueryMatch[1].trim();
    }
  }

  // Check if this is an AI-generated report with joins in the tableDefinition or description
  let joinTables: any[] = [];

  // First check tableDefinition
  if (report.tableDefinition && report.tableDefinition.includes('Joins:')) {
    const joinsMatch = report.tableDefinition.match(/Joins: ([\s\S]*?)(?:\n\n|$)/);
    if (joinsMatch && joinsMatch[1]) {
      try {
        joinTables = JSON.parse(joinsMatch[1].trim());
      } catch (e) {
        console.error('Error parsing joins from tableDefinition:', e);
      }
    }
  }

  // Fallback to description for backward compatibility
  if (joinTables.length === 0 && report.description && report.description.includes('Joins:')) {
    const joinsMatch = report.description.match(/Joins: ([\s\S]*?)(?:\n\n|$)/);
    if (joinsMatch && joinsMatch[1]) {
      try {
        joinTables = JSON.parse(joinsMatch[1].trim());
      } catch (e) {
        console.error('Error parsing joins from description:', e);
      }
    }
  }

  // Check if the report has related tables or joins defined in its properties
  if (!joinTables.length && report.joins && Array.isArray(report.joins) && report.joins.length > 0) {
    joinTables = report.joins;
  }

  // Check if this is an AI-generated report with a limit in the tableDefinition or description
  let limit: number | undefined;

  // First check tableDefinition
  if (report.tableDefinition && report.tableDefinition.includes('Limit:')) {
    const limitMatch = report.tableDefinition.match(/Limit: (\d+)/);
    if (limitMatch && limitMatch[1]) {
      limit = parseInt(limitMatch[1], 10);
    }
  }
  // Fallback to description for backward compatibility
  else if (report.description && report.description.includes('Limit:')) {
    const limitMatch = report.description.match(/Limit: (\d+)/);
    if (limitMatch && limitMatch[1]) {
      limit = parseInt(limitMatch[1], 10);
    }
  }
  // Use result_limit if available
  else if (report.result_limit) {
    limit = report.result_limit;
  }

  // Start building the SELECT clause
  let selectClause = 'SELECT ';

  console.log('Building SQL query for report:', {
    reportId: report.id,
    reportName: report.name,
    dataSource: report.dataSource,
    fields: report.fields,
    joins: joinTables
  });

  // Determine the primary table based on the fields
  let primaryTable = report.dataSource;

  // If the data source is not the primary table for most fields, we might need to adjust
  const fieldTableCounts: Record<string, number> = {};

  report.fields.forEach(field => {
    if (!field.sourceField.includes('.')) {
      const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);
      fieldTableCounts[fieldTableInfo.tableName] = (fieldTableCounts[fieldTableInfo.tableName] || 0) + 1;
    }
  });

  console.log('Field table counts:', fieldTableCounts);

  // Find the table with the most fields
  let maxCount = 0;
  let maxTable = primaryTable;

  Object.entries(fieldTableCounts).forEach(([table, count]) => {
    if (count > maxCount) {
      maxCount = count;
      maxTable = table;
    }
  });

  // If most fields are from a different table, consider using that as the primary table
  if (maxTable !== primaryTable && maxCount > (fieldTableCounts[primaryTable] || 0)) {
    console.log(`Most fields (${maxCount}) are from table "${maxTable}" instead of data source "${primaryTable}"`);

    // Only change the primary table if it's not already in the joins
    if (!joinTables.some(join => join.table === maxTable)) {
      console.log(`Changing primary table from "${primaryTable}" to "${maxTable}"`);
      primaryTable = maxTable;
    }
  }

  // Now that we know the primary table, generate the field selects
  const fieldSelects = report.fields
    .filter(field => field.isVisible)
    .map(field => {
      // Check if the field name contains a table reference (e.g., "table.field")
      const hasTableRef = field.sourceField.includes('.');

      // Check if the field name contains an aggregation (e.g., "SUM(field)")
      const hasAggregation = field.sourceField.match(/^(SUM|AVG|MIN|MAX|COUNT)\s*\(/i);

      // Check if this field might be from a joined table
      const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);

      console.log(`Field "${field.name}" (source: "${field.sourceField}"):`, {
        hasTableRef,
        hasAggregation,
        fieldTableInfo,
        dataSource: report.dataSource,
        primaryTable
      });

      let sqlFragment;

      if (hasTableRef || hasAggregation) {
        // If it already has a table reference or aggregation, use it as is
        sqlFragment = `${field.sourceField} AS "${field.name}"`;
      } else if (fieldTableInfo.tableName !== primaryTable) {
        // If the field is from a joined table, qualify it with that table name
        sqlFragment = `"${fieldTableInfo.tableName}"."${field.sourceField}" AS "${field.name}"`;
      } else {
        // Quote the field name to handle case sensitivity
        const fieldName = `"${field.sourceField}"`;

        // Apply aggregation if specified
        if (field.aggregation) {
          sqlFragment = `${field.aggregation.toUpperCase()}(${fieldName}) AS "${field.name}"`;
        }
        // If we have joins, qualify the field with the table name
        else if (joinTables.length > 0) {
          sqlFragment = `"${primaryTable}".${fieldName} AS "${field.name}"`;
        }
        else {
          sqlFragment = `${fieldName} AS "${field.name}"`;
        }
      }

      console.log(`SQL fragment for field "${field.name}": ${sqlFragment}`);
      return sqlFragment;
    });

  // Add calculations
  if (report.calculations && report.calculations.length > 0) {
    const calculationSelects = report.calculations.map(calc => {
      // Replace field references with actual field names
      let formula = calc.formula;
      report.fields.forEach(field => {
        formula = formula.replace(
          new RegExp(`\\[${field.name}\\]`, 'g'),
          field.sourceField
        );
      });

      return `(${formula}) AS "${calc.name}"`;
    });

    fieldSelects.push(...calculationSelects);
  }

  selectClause += fieldSelects.join(', ');

  // Add FROM clause with quoted table name
  const fromClause = ` FROM "${primaryTable}"`;

  // Add JOIN clauses if joins exist
  let joinClause = '';
  if (joinTables.length > 0) {
    // If we changed the primary table, we need to add the original data source as a join
    if (primaryTable !== report.dataSource && !joinTables.some(join => join.table === report.dataSource)) {
      console.log(`Adding join for original data source "${report.dataSource}"`);
      joinTables.push({
        table: report.dataSource,
        joinType: 'LEFT',
        joinField: 'household_id',
        foreignField: 'id'
      });
    }

    joinClause = joinTables.map((join: any) => {
      // Skip if this is the primary table
      if (join.table === primaryTable) {
        console.log(`Skipping join for table "${join.table}" as it's now the primary table`);
        return '';
      }

      const joinType = (join.joinType || 'LEFT').toUpperCase();

      // Handle case sensitivity for join fields
      const sourceField = join.joinField === 'id' ? 'id' : `"${join.joinField}"`;
      const targetField = join.foreignField === 'id' ? 'id' : `"${join.foreignField}"`;

      // Determine the correct join direction
      let joinSql;
      if (join.table === report.dataSource && primaryTable !== report.dataSource) {
        // If joining to the original data source, reverse the join direction
        joinSql = ` ${joinType} JOIN "${join.table}" ON "${primaryTable}"."${join.foreignField}" = "${join.table}"."${join.joinField}"`;
      } else {
        joinSql = ` ${joinType} JOIN "${join.table}" ON "${primaryTable}".${sourceField} = "${join.table}".${targetField}`;
      }

      console.log(`Join SQL for table "${join.table}": ${joinSql}`);
      return joinSql;
    }).filter(Boolean).join('');
  }

  // Add WHERE clause if filters exist
  let whereClause = '';
  if (report.filters && report.filters.length > 0) {
    // Create a modified report with the new primary table for the WHERE clause
    const modifiedReport = { ...report, dataSource: primaryTable };
    whereClause = ' WHERE ' + buildWhereClause(report.filters, modifiedReport, joinTables);
  }

  // Add GROUP BY clause if grouping is specified
  let groupByClause = '';
  if (report.groupBy && report.groupBy.length > 0) {
    const groupFields = report.groupBy.map(fieldId => {
      const field = report.fields.find(f => f.id === fieldId);

      if (field) {
        // Check if the field name contains a table reference
        if (field.sourceField.includes('.')) {
          return field.sourceField;
        }

        // Check if this field might be from a joined table
        const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);

        if (fieldTableInfo.tableName !== report.dataSource) {
          // If the field is from a joined table, qualify it with that table name
          return `"${fieldTableInfo.tableName}"."${field.sourceField}"`;
        }

        // If we have joins, qualify the field with the table name
        if (joinTables.length > 0) {
          return `"${primaryTable}"."${field.sourceField}"`;
        }

        // Quote the field name
        return `"${field.sourceField}"`;
      }

      // If fieldId is a string that contains a table reference
      if (typeof fieldId === 'string' && fieldId.includes('.')) {
        const [table, field] = fieldId.split('.');
        return `"${table}"."${field}"`;
      }

      return `"${fieldId}"`;
    });

    groupByClause = ' GROUP BY ' + groupFields.join(', ');
  }

  // Add ORDER BY clause if sorting is specified
  let orderByClause = '';
  if (report.sortBy && report.sortBy.length > 0) {
    const sortFields = report.sortBy.map(sort => {
      const field = report.fields.find(f => f.id === sort.field);

      if (field) {
        // Check if the field name contains a table reference or aggregation
        if (field.sourceField.includes('.') || field.sourceField.match(/^(SUM|AVG|MIN|MAX|COUNT)\s*\(/i)) {
          return `${field.sourceField} ${sort.direction.toUpperCase()}`;
        }

        // Check if this field might be from a joined table
        const fieldTableInfo = findFieldTableInfo(field.sourceField, report.dataSource, joinTables);

        if (fieldTableInfo.tableName !== report.dataSource) {
          // If the field is from a joined table, qualify it with that table name
          return `"${fieldTableInfo.tableName}"."${field.sourceField}" ${sort.direction.toUpperCase()}`;
        }

        // If we have joins, qualify the field with the table name
        if (joinTables.length > 0) {
          return `"${primaryTable}"."${field.sourceField}" ${sort.direction.toUpperCase()}`;
        }

        // Quote the field name
        return `"${field.sourceField}" ${sort.direction.toUpperCase()}`;
      }

      // If sort.field is a string that contains a table reference
      if (typeof sort.field === 'string' && sort.field.includes('.')) {
        return `${sort.field} ${sort.direction.toUpperCase()}`;
      }

      return `"${sort.field}" ${sort.direction.toUpperCase()}`;
    });

    orderByClause = ' ORDER BY ' + sortFields.join(', ');
  }

  // Add LIMIT clause if specified
  let limitClause = '';
  if (limit) {
    limitClause = ` LIMIT ${limit}`;
  }

  // Combine all clauses
  return selectClause + fromClause + joinClause + whereClause + groupByClause + orderByClause + limitClause;
}

/**
 * Build a WHERE clause from filter conditions
 */
function buildWhereClause(filters: FilterCondition[], report?: CustomReport, joinTables?: any[]): string {
  if (filters.length === 0) return '';

  return filters.map((filter, index) => {
    const condition = buildFilterCondition(filter, report, joinTables);

    // Add AND/OR logic for all but the last filter
    if (index < filters.length - 1) {
      return `(${condition}) ${filter.logic?.toUpperCase() || 'AND'}`;
    }

    return `(${condition})`;
  }).join(' ');
}

/**
 * Build a single filter condition
 */
function buildFilterCondition(filter: FilterCondition, report?: CustomReport, joinTables?: any[]): string {
  // Check if the field name contains a table reference (e.g., "table.field")
  const hasTableRef = filter.field.includes('.');

  // Format the field name appropriately
  let field;
  if (hasTableRef) {
    // If it has a table reference, split and quote each part
    const [table, fieldName] = filter.field.split('.');
    field = `"${table}"."${fieldName}"`;
  } else if (report && joinTables && joinTables.length > 0) {
    // Check if this field might be from a joined table
    const fieldTableInfo = findFieldTableInfo(filter.field, report.dataSource, joinTables);

    if (fieldTableInfo.tableName !== report.dataSource) {
      // If the field is from a joined table, qualify it with that table name
      field = `"${fieldTableInfo.tableName}"."${filter.field}"`;
    } else {
      // Otherwise, just quote the field name
      field = `"${filter.field}"`;
    }
  } else {
    // If no report or joins provided, just quote the field name
    field = `"${filter.field}"`;
  }

  const value = filter.value;

  switch (filter.operator) {
    case 'equals':
      return `${field} = '${value}'`;
    case 'notEquals':
      return `${field} <> '${value}'`;
    case 'contains':
      return `${field} LIKE '%${value}%'`;
    case 'startsWith':
      return `${field} LIKE '${value}%'`;
    case 'endsWith':
      return `${field} LIKE '%${value}'`;
    case 'greaterThan':
      return `${field} > '${value}'`;
    case 'lessThan':
      return `${field} < '${value}'`;
    case 'between':
      return `${field} BETWEEN '${value}' AND '${filter.valueEnd}'`;
    case 'in':
      const values = Array.isArray(filter.valueList)
        ? filter.valueList
        : String(value).split(',').map(v => v.trim());
      return `${field} IN (${values.map(v => `'${v}'`).join(', ')})`;
    default:
      return `${field} = '${value}'`;
  }
}

/**
 * Transform raw data based on the report definition
 */
function transformReportData(data: any[], report: CustomReport): any[] {
  console.log('REPORT FLOW - Transforming report data:', {
    reportId: report.id,
    reportName: report.name,
    dataSource: report.dataSource,
    dataLength: data.length
  });
  console.log('REPORT FLOW - Report fields for transformation:', report.fields);
  console.log('REPORT FLOW - First row of data:', data.length > 0 ? data[0] : 'No data');

  // Apply formatting to fields
  const transformedData = data.map(row => {
    const formattedRow: Record<string, any> = {};

    // Format fields
    report.fields.forEach(field => {
      if (!field.isVisible) return;

      const value = row[field.name];

      // Handle objects and arrays by converting them to a string representation
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          formattedRow[field.name] = JSON.stringify(value);
        } else {
          // For objects, create a simplified string representation
          formattedRow[field.name] = JSON.stringify(value);
        }
      }
      // Apply formatting based on data type and format
      else if (field.dataType === 'number' && field.format && value !== null && value !== undefined) {
        switch (field.format) {
          case 'currency':
            formattedRow[field.name] = formatCurrency(value);
            break;
          case 'percent':
            formattedRow[field.name] = formatPercent(value);
            break;
          case 'integer':
            formattedRow[field.name] = Math.round(value);
            break;
          default:
            formattedRow[field.name] = value;
        }
      } else if (field.dataType === 'date' && field.format && value) {
        formattedRow[field.name] = formatDate(value, field.format);
      } else {
        formattedRow[field.name] = value;
      }
    });

    // Format calculations
    if (report.calculations) {
      report.calculations.forEach(calc => {
        const value = row[calc.name];

        // Handle objects and arrays
        if (typeof value === 'object' && value !== null) {
          formattedRow[calc.name] = JSON.stringify(value);
        }
        else if (calc.dataType === 'number' && calc.format && value !== null && value !== undefined) {
          switch (calc.format) {
            case 'currency':
              formattedRow[calc.name] = formatCurrency(value);
              break;
            case 'percent':
              formattedRow[calc.name] = formatPercent(value);
              break;
            case 'integer':
              formattedRow[calc.name] = Math.round(value);
              break;
            default:
              formattedRow[calc.name] = value;
          }
        } else {
          formattedRow[calc.name] = value;
        }
      });
    }

    return formattedRow;
  });

  console.log('REPORT FLOW - Transformed data:', transformedData.length > 0 ? transformedData.slice(0, 1) : 'No data');
  return transformedData;
}

/**
 * Generate columns for the report table
 */
function generateColumns(report: CustomReport): any[] {
  console.log('REPORT FLOW - Generating columns for report:', {
    reportId: report.id,
    reportName: report.name,
    dataSource: report.dataSource
  });
  console.log('REPORT FLOW - Report fields for columns:', report.fields);

  const columns: any[] = [];

  // Check if this is an AI-generated report with a SQL query in the description
  const isAIGenerated = report.description && (
    report.description.includes('SQL Query:') ||
    report.description.includes('Joins:') ||
    report.description.includes('AI-Generated Report')
  );

  // If it's an AI-generated report, we need to handle the result differently
  if (isAIGenerated) {
    // For AI-generated reports, we'll use the first row of data to determine columns
    // This will be handled in the runCustomReport function
    // Here we'll just create columns based on the field definitions
    report.fields
      .filter(field => field.isVisible)
      .forEach(field => {
        columns.push({
          accessorKey: field.name,
          header: field.displayName || field.name,
          cell: ({ getValue }: any) => {
            const value = getValue();

            // Format the value based on the field type
            if (typeof value === 'number') {
              // Check if it might be a currency value (based on field name)
              if (field.name.toLowerCase().includes('amount') ||
                  field.name.toLowerCase().includes('income') ||
                  field.name.toLowerCase().includes('assets') ||
                  field.name.toLowerCase().includes('total')) {
                return formatCurrency(value);
              }
              return value.toLocaleString();
            }

            // Handle objects and arrays
            if (typeof value === 'object' && value !== null) {
              return JSON.stringify(value);
            }

            return value;
          }
        });
      });

    return columns;
  }

  // Standard column generation for manually created reports
  // Add columns for visible fields
  report.fields
    .filter(field => field.isVisible)
    .forEach(field => {
      columns.push({
        accessorKey: field.name,
        header: field.displayName,
        cell: ({ getValue }: any) => {
          const value = getValue();

          // Handle objects and arrays
          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              return JSON.stringify(value);
            } else {
              return JSON.stringify(value);
            }
          }

          return value;
        }
      });
    });

  // Add columns for calculations
  if (report.calculations) {
    report.calculations.forEach(calc => {
      columns.push({
        accessorKey: calc.name,
        header: calc.name,
        cell: ({ getValue }: any) => {
          const value = getValue();

          // Handle objects and arrays
          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              return JSON.stringify(value);
            } else {
              return JSON.stringify(value);
            }
          }

          return value;
        }
      });
    });
  }

  console.log('REPORT FLOW - Generated columns:', columns);
  return columns;
}

/**
 * Generate chart data from report data
 */
function generateChartData(data: any[], visualization: Visualization): any {
  if (!visualization || !data || data.length === 0) {
    return undefined;
  }

  // Check if this is an AI-generated report with a different data structure
  const isAIGenerated = visualization.title && visualization.title.includes('AI-Generated');

  // For AI-generated reports, we need to find the appropriate fields in the data
  if (isAIGenerated && data.length > 0) {
    // For pie charts
    if (visualization.type === 'pie') {
      // Find appropriate fields for the chart
      const keys = Object.keys(data[0]);
      const labelField = keys.find(k =>
        k.toLowerCase().includes('name') ||
        k.toLowerCase().includes('category') ||
        k.toLowerCase().includes('type')
      ) || keys[0];

      const valueField = keys.find(k =>
        k.toLowerCase().includes('amount') ||
        k.toLowerCase().includes('total') ||
        k.toLowerCase().includes('value') ||
        k.toLowerCase().includes('income') ||
        typeof data[0][k] === 'number'
      ) || keys[1];

      return {
        labels: data.map(item => String(item[labelField])),
        datasets: [{
          label: labelField,
          data: data.map(item => Number(item[valueField])),
          backgroundColor: generateColors(data.length)
        }]
      };
    }

    // For other chart types
    const keys = Object.keys(data[0]);
    const labelField = keys.find(k =>
      k.toLowerCase().includes('name') ||
      k.toLowerCase().includes('category') ||
      k.toLowerCase().includes('date') ||
      k.toLowerCase().includes('month') ||
      k.toLowerCase().includes('year')
    ) || keys[0];

    // Find numeric fields for the series
    const numericFields = keys.filter(k =>
      typeof data[0][k] === 'number' ||
      k.toLowerCase().includes('amount') ||
      k.toLowerCase().includes('total') ||
      k.toLowerCase().includes('value') ||
      k.toLowerCase().includes('income')
    );

    return {
      labels: data.map(item => String(item[labelField])),
      datasets: numericFields.map((field) => {
        const dataset: any = {
          label: field.charAt(0).toUpperCase() + field.slice(1).replace(/_/g, ' '),
          data: data.map(item => Number(item[field])),
          borderColor: generateColors(1)[0],
          backgroundColor: generateColors(1)[0]
        };

        if (visualization.type === 'area') {
          dataset.fill = true;
        }

        return dataset;
      })
    };
  }

  // Standard chart generation for manually created reports
  // For pie charts
  if (visualization.type === 'pie') {
    const series = visualization.series[0];
    if (!series) return undefined;

    return {
      labels: data.map(item => String(item[visualization.xAxis || ''])),
      datasets: [{
        label: series.name,
        data: data.map(item => Number(item[series.field])),
        backgroundColor: generateColors(data.length)
      }]
    };
  }

  // For other chart types
  return {
    labels: data.map(item => String(item[visualization.xAxis || ''])),
    datasets: visualization.series.map(series => {
      const dataset: any = {
        label: series.name,
        data: data.map(item => Number(item[series.field])),
        borderColor: series.color,
        backgroundColor: series.color
      };

      if (visualization.type === 'area' || series.type === 'area') {
        dataset.fill = true;
      }

      return dataset;
    })
  };
}

/**
 * Generate an array of colors for charts
 */
function generateColors(count: number): string[] {
  const baseColors = [
    'rgba(255, 99, 132, 0.6)',
    'rgba(54, 162, 235, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(75, 192, 192, 0.6)',
    'rgba(153, 102, 255, 0.6)',
    'rgba(255, 159, 64, 0.6)',
    'rgba(201, 203, 207, 0.6)',
    'rgba(255, 99, 132, 0.6)'
  ];

  // If we need more colors than in our base set, generate them
  if (count <= baseColors.length) {
    return baseColors.slice(0, count);
  }

  const colors = [...baseColors];

  for (let i = baseColors.length; i < count; i++) {
    const r = Math.floor(Math.random() * 255);
    const g = Math.floor(Math.random() * 255);
    const b = Math.floor(Math.random() * 255);
    colors.push(`rgba(${r}, ${g}, ${b}, 0.6)`);
  }

  return colors;
}

/**
 * Format a number as currency
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
}

/**
 * Format a number as percentage
 */
function formatPercent(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value / 100);
}

/**
 * Format a date based on the specified format
 */
function formatDate(value: string, format: string): string {
  const date = new Date(value);

  switch (format) {
    case 'date':
      return date.toLocaleDateString();
    case 'datetime':
      return date.toLocaleString();
    case 'year':
      return date.getFullYear().toString();
    case 'month':
      return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    case 'quarter':
      const quarter = Math.floor(date.getMonth() / 3) + 1;
      return `Q${quarter} ${date.getFullYear()}`;
    default:
      return date.toLocaleDateString();
  }
}
