import { createClient } from '@/utils/supabase/client';
import { ReportData, ReportSchedule } from './types';
import { exportToExcel } from './export/excel';
import { exportToPDF } from './export/pdf';

/**
 * Delivers a report via email
 * 
 * @param schedule The report schedule
 * @param reportData The report data
 * @returns Whether the delivery was successful
 */
export async function deliverReportViaEmail(
  schedule: ReportSchedule,
  reportData: ReportData
): Promise<boolean> {
  try {
    // In a real implementation, this would use an email service like SendGrid or AWS SES
    // For now, we'll just log the email details
    
    console.log('Delivering report via email:');
    console.log(`- To: ${schedule.delivery.recipients?.join(', ')}`);
    console.log(`- Subject: ${schedule.delivery.subject || `${schedule.name} Report`}`);
    console.log(`- Message: ${schedule.delivery.message || 'Please find attached your scheduled report.'}`);
    
    // In a real implementation, we would generate the attachments and send the email
    // For now, we'll just return success
    
    return true;
  } catch (error) {
    console.error('Error delivering report via email:', error);
    return false;
  }
}

/**
 * Delivers a report via in-app notification
 * 
 * @param schedule The report schedule
 * @param reportData The report data
 * @returns Whether the delivery was successful
 */
export async function deliverReportViaNotification(
  schedule: ReportSchedule,
  reportData: ReportData,
  resultId: string
): Promise<boolean> {
  try {
    const supabase = createClient();
    
    // Create a notification
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: schedule.userId,
        title: `${schedule.name} Report`,
        message: schedule.delivery.message || `Your scheduled report "${schedule.name}" is ready.`,
        type: 'report',
        data: {
          reportId: reportData.reportId,
          resultId: resultId
        },
        read: false
      });
    
    if (error) {
      throw new Error(`Error creating notification: ${error.message}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error delivering report via notification:', error);
    return false;
  }
}

/**
 * Delivers a report via download link
 * 
 * @param schedule The report schedule
 * @param reportData The report data
 * @returns Whether the delivery was successful
 */
export async function deliverReportViaDownload(
  schedule: ReportSchedule,
  reportData: ReportData,
  resultId: string
): Promise<boolean> {
  try {
    // In a real implementation, this would generate a download link and store it
    // For now, we'll just create a notification with a link to the report result
    
    const supabase = createClient();
    
    // Create a notification
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: schedule.userId,
        title: `${schedule.name} Report Download`,
        message: `Your scheduled report "${schedule.name}" is ready for download.`,
        type: 'report_download',
        data: {
          reportId: reportData.reportId,
          resultId: resultId
        },
        read: false
      });
    
    if (error) {
      throw new Error(`Error creating download notification: ${error.message}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error delivering report via download:', error);
    return false;
  }
}

/**
 * Delivers a report based on the schedule's delivery method
 * 
 * @param schedule The report schedule
 * @param reportData The report data
 * @param resultId The ID of the saved report result
 * @returns The delivery status
 */
export async function deliverReport(
  schedule: ReportSchedule,
  reportData: ReportData,
  resultId: string
): Promise<{
  method: 'email' | 'notification' | 'download';
  status: 'sent' | 'failed';
  sentAt?: Date;
  error?: string;
}> {
  try {
    const method = schedule.delivery.method;
    let success = false;
    
    switch (method) {
      case 'email':
        success = await deliverReportViaEmail(schedule, reportData);
        break;
        
      case 'notification':
        success = await deliverReportViaNotification(schedule, reportData, resultId);
        break;
        
      case 'download':
        success = await deliverReportViaDownload(schedule, reportData, resultId);
        break;
    }
    
    if (success) {
      return {
        method,
        status: 'sent',
        sentAt: new Date()
      };
    } else {
      return {
        method,
        status: 'failed',
        error: 'Failed to deliver report'
      };
    }
  } catch (error) {
    return {
      method: schedule.delivery.method,
      status: 'failed',
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
