import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// A4 dimensions in pixels at 96 DPI
export const A4_WIDTH_PX = 794;
export const A4_HEIGHT_PX = 1123;

/**
 * Exports the report to PDF format
 * 
 * @param reportElement The HTML element containing the report
 * @param fileName The name of the file to save
 * @param options Optional PDF export options
 */
export async function exportToPDF(
  reportElement: HTMLElement,
  fileName: string,
  options: PDFExportOptions = {}
): Promise<void> {
  // Create container for PDF content
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  document.body.appendChild(container);
  
  try {
    // Clone the report element
    const clonedReport = reportElement.cloneNode(true) as HTMLElement;
    container.appendChild(clonedReport);
    
    // Apply PDF-specific styling
    applyPDFStyling(clonedReport);
    
    // Get sections to paginate
    const sections = Array.from(clonedReport.children) as HTMLElement[];
    
    // Paginate content
    await paginateContent(container, sections);
    
    // Create PDF
    const pdf = new jsPDF({
      orientation: options.orientation || 'portrait',
      unit: 'px',
      format: [A4_WIDTH_PX, A4_HEIGHT_PX],
    });
    
    // Get all pages
    const pages = container.querySelectorAll('.a4-page');
    
    // Add each page to PDF
    for (let i = 0; i < pages.length; i++) {
      if (i > 0) {
        pdf.addPage();
      }
      
      const canvas = await html2canvas(pages[i] as HTMLElement, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, A4_WIDTH_PX, A4_HEIGHT_PX);
    }
    
    // Save PDF
    pdf.save(`${fileName}.pdf`);
  } finally {
    // Clean up
    document.body.removeChild(container);
  }
}

/**
 * Applies PDF-specific styling to the report element
 * 
 * @param element The element to style
 */
function applyPDFStyling(element: HTMLElement): void {
  // Add a title if it doesn't exist
  if (!element.querySelector('h1, h2, h3')) {
    const title = document.createElement('h2');
    title.textContent = 'Report';
    title.style.marginBottom = '20px';
    element.insertBefore(title, element.firstChild);
  }
  
  // Style tables
  const tables = element.querySelectorAll('table');
  tables.forEach(table => {
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.style.marginBottom = '20px';
    
    // Style table headers
    const headers = table.querySelectorAll('th');
    headers.forEach(header => {
      header.style.backgroundColor = '#f3f4f6';
      header.style.padding = '8px';
      header.style.borderBottom = '2px solid #e5e7eb';
      header.style.textAlign = 'left';
      header.style.fontWeight = 'bold';
    });
    
    // Style table cells
    const cells = table.querySelectorAll('td');
    cells.forEach(cell => {
      cell.style.padding = '8px';
      cell.style.borderBottom = '1px solid #e5e7eb';
    });
    
    // Style table rows
    const rows = table.querySelectorAll('tr');
    rows.forEach((row, index) => {
      if (index % 2 === 1) {
        row.style.backgroundColor = '#f9fafb';
      }
    });
  });
  
  // Remove any interactive elements
  const buttons = element.querySelectorAll('button');
  buttons.forEach(button => button.remove());
  
  const inputs = element.querySelectorAll('input, select');
  inputs.forEach(input => input.remove());
  
  // Remove pagination controls
  const paginationControls = element.querySelectorAll('.pagination');
  paginationControls.forEach(control => control.remove());
}

/**
 * Paginates content for PDF export
 * 
 * @param container The container element
 * @param sections The sections to paginate
 */
export async function paginateContent(
  container: HTMLElement,
  sections: HTMLElement[]
): Promise<void> {
  // Create first page
  const firstPage = document.createElement('div');
  firstPage.className = 'a4-page';
  firstPage.style.width = `${A4_WIDTH_PX}px`;
  firstPage.style.height = `${A4_HEIGHT_PX}px`;
  firstPage.style.padding = '40px';
  firstPage.style.boxSizing = 'border-box';
  firstPage.style.position = 'relative';
  firstPage.style.backgroundColor = '#ffffff';
  firstPage.style.overflow = 'hidden';
  container.appendChild(firstPage);
  
  // Add report title and date
  const header = document.createElement('div');
  header.style.marginBottom = '20px';
  
  const title = document.createElement('h1');
  title.textContent = 'Report';
  title.style.marginBottom = '10px';
  
  const date = document.createElement('p');
  date.textContent = `Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`;
  date.style.color = '#6b7280';
  
  header.appendChild(title);
  header.appendChild(date);
  firstPage.appendChild(header);
  
  // Add content to pages
  let currentPage = firstPage;
  let currentY = header.offsetHeight + 40; // Start after header with some margin
  
  for (const section of sections) {
    // Skip empty sections
    if (!section.innerHTML.trim()) continue;
    
    // Clone the section
    const clonedSection = section.cloneNode(true) as HTMLElement;
    
    // Temporarily add to current page to measure height
    currentPage.appendChild(clonedSection);
    
    // Check if it fits on current page
    if (currentY + clonedSection.offsetHeight > A4_HEIGHT_PX - 40) {
      // Remove from current page
      currentPage.removeChild(clonedSection);
      
      // Create new page
      const newPage = document.createElement('div');
      newPage.className = 'a4-page';
      newPage.style.width = `${A4_WIDTH_PX}px`;
      newPage.style.height = `${A4_HEIGHT_PX}px`;
      newPage.style.padding = '40px';
      newPage.style.boxSizing = 'border-box';
      newPage.style.position = 'relative';
      newPage.style.backgroundColor = '#ffffff';
      newPage.style.overflow = 'hidden';
      container.appendChild(newPage);
      
      // Add page number
      const pageNumber = document.createElement('div');
      pageNumber.style.position = 'absolute';
      pageNumber.style.bottom = '20px';
      pageNumber.style.right = '20px';
      pageNumber.style.fontSize = '12px';
      pageNumber.style.color = '#6b7280';
      pageNumber.textContent = `Page ${container.querySelectorAll('.a4-page').length}`;
      newPage.appendChild(pageNumber);
      
      // Reset current page and Y position
      currentPage = newPage;
      currentY = 40;
      
      // Add section to new page
      currentPage.appendChild(clonedSection);
      currentY += clonedSection.offsetHeight + 20; // Add some margin
    } else {
      // It fits, update Y position
      currentY += clonedSection.offsetHeight + 20; // Add some margin
    }
  }
  
  // Add page numbers to all pages
  const pages = container.querySelectorAll('.a4-page');
  pages.forEach((page, index) => {
    // Check if page already has a page number
    if (!page.querySelector('.page-number')) {
      const pageNumber = document.createElement('div');
      pageNumber.className = 'page-number';
      pageNumber.style.position = 'absolute';
      pageNumber.style.bottom = '20px';
      pageNumber.style.right = '20px';
      pageNumber.style.fontSize = '12px';
      pageNumber.style.color = '#6b7280';
      pageNumber.textContent = `Page ${index + 1} of ${pages.length}`;
      page.appendChild(pageNumber);
    }
  });
}

/**
 * PDF export options
 */
export interface PDFExportOptions {
  orientation?: 'portrait' | 'landscape';
  pageSize?: 'a4' | 'letter';
  includeFilters?: boolean;
  fileName?: string;
}
