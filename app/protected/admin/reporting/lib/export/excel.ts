import { ColumnDef } from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

/**
 * Exports data to Excel format
 *
 * @param data The data to export
 * @param fileName The name of the file to save
 * @param columns Optional column definitions for formatting
 * @param sheetName Optional name for the worksheet
 */
export function exportToExcel(
  data: any[],
  fileName: string,
  columns?: ColumnDef<any, any>[],
  sheetName: string = 'Report'
): void {
  // If we have column definitions, use them to format the data
  let formattedData = data;

  if (columns) {
    formattedData = data.map(row => {
      const formattedRow: Record<string, any> = {};

      columns.forEach(column => {
        // Handle both accessorKey and accessorFn patterns
        const accessorKey = 'accessorKey' in column ? column.accessorKey as string : undefined;
        const id = column.id;

        // Skip if we can't determine how to access the data
        if (!accessorKey && !id) return;

        // Get the raw value using accessor<PERSON>ey or id as fallback
        const key = accessorKey || id;
        // Ensure key is not undefined before using it as an index
        if (key === undefined) return;
        const value = row[key];

        // Check if there's a cell formatter
        if (column.cell && typeof column.cell === 'function') {
          // Create a mock context that the cell formatter can use
          const mockContext = {
            getValue: () => value,
            row: {
              getValue: (key: string) => row[key],
              original: row
            }
          };

          // Call the formatter
          const formattedValue = column.cell(mockContext as any);

          // If the formatter returns a React element, try to extract the text content
          if (formattedValue && typeof formattedValue === 'object' && 'props' in formattedValue) {
            // For React elements, use the children if it's a string
            if (typeof formattedValue.props.children === 'string') {
              formattedRow[column.header as string] = formattedValue.props.children;
            } else {
              // Otherwise, just use the original value
              formattedRow[column.header as string] = value;
            }
          } else if (typeof formattedValue === 'string' || typeof formattedValue === 'number') {
            // For simple values returned by the formatter
            formattedRow[column.header as string] = formattedValue;
          } else {
            // Default to the original value
            formattedRow[column.header as string] = value;
          }
        } else {
          // No formatter, use the header as the key if available
          const key = column.header ? (column.header as string) : accessorKey;
          // Ensure key is not undefined before using it as an index
          if (key !== undefined) {
            formattedRow[key] = value;
          }
        }
      });

      return formattedRow;
    });
  }

  // Convert data to worksheet
  const worksheet = XLSX.utils.json_to_sheet(formattedData);

  // Set column widths
  const columnWidths = calculateColumnWidths(formattedData);
  worksheet['!cols'] = columnWidths.map(width => ({ wch: width }));

  // Create workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // Generate Excel file
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

  // Save file
  saveAs(blob, `${fileName}.xlsx`);
}

/**
 * Calculates appropriate column widths based on data content
 *
 * @param data The data to analyze
 * @returns An array of column widths
 */
function calculateColumnWidths(data: any[]): number[] {
  if (data.length === 0) return [];

  // Get column names from the first row
  const columnNames = Object.keys(data[0]);

  // Initialize widths with the length of column names
  const widths = columnNames.map(name => Math.min(50, Math.max(10, name.length + 2)));

  // Check content of each cell to determine width
  data.forEach(row => {
    columnNames.forEach((name, index) => {
      const value = row[name];
      const valueStr = value !== null && value !== undefined ? String(value) : '';
      widths[index] = Math.min(50, Math.max(widths[index], valueStr.length + 2));
    });
  });

  return widths;
}
