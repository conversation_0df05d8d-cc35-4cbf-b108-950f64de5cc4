import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Column {
  name: string;
  table: string;
  alias?: string;
  aggregate?: string;
}

interface Filter {
  field: string;
  operator: string;
  value: string;
  table?: string;
}

interface JoinTable {
  table: string;
  joinField: string;
  foreignField: string;
  joinType: string;
}

interface GroupByField {
  field: string;
  table?: string;
}

interface SortField {
  field: string;
  table?: string;
  aggregate?: string;
}

interface AIReportPreviewProps {
  config: {
    title: string;
    description: string;
    tableDefinition?: string;
    type: string;
    dataSource: string;
    joinTables?: JoinTable[];
    filters: Filter[];
    columns: Column[] | string[];
    sortBy?: SortField | string;
    sortDirection?: 'asc' | 'desc';
    chartType?: string;
    groupBy?: GroupByField[] | string;
    limit?: number;
    sqlQuery?: string;
  };
  explanation?: string;
}

export function AIReportPreview({ config, explanation }: AIReportPreviewProps) {
  // Check if columns is an array of strings or objects
  const isColumnsArray = Array.isArray(config.columns) &&
    (config.columns.length === 0 || typeof config.columns[0] === 'string');

  // Check if groupBy is a string or an array of objects
  const isGroupByString = typeof config.groupBy === 'string';

  // Check if sortBy is a string or an object
  const isSortByString = typeof config.sortBy === 'string';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{config.title}</CardTitle>
        <p className="text-sm text-muted-foreground">{config.description}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium mb-1">Report Type</h4>
            <Badge variant="outline">{config.type}</Badge>
          </div>
          <div>
            <h4 className="text-sm font-medium mb-1">Data Source</h4>
            <Badge variant="outline">{config.dataSource}</Badge>
          </div>
        </div>

        {config.joinTables && config.joinTables.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Joins</h4>
            <div className="space-y-2">
              {config.joinTables.map((join, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <Badge variant="outline">{join.joinType}</Badge>
                  <span className="font-medium">{join.table}</span>
                  <span className="text-muted-foreground">ON</span>
                  <span>{config.dataSource}.{join.joinField}</span>
                  <span className="text-muted-foreground">=</span>
                  <span>{join.table}.{join.foreignField}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {config.filters.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Filters</h4>
            <div className="space-y-2">
              {config.filters.map((filter, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <span className="font-medium">
                    {filter.table ? `${filter.table}.${filter.field}` : filter.field}
                  </span>
                  <span className="text-muted-foreground">{filter.operator}</span>
                  <span>{filter.value}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div>
          <h4 className="text-sm font-medium mb-2">Columns</h4>
          <div className="flex flex-wrap gap-2">
            {isColumnsArray ? (
              // Handle array of strings
              (config.columns as string[]).map((column, index) => (
                <Badge key={index} variant="secondary">{column}</Badge>
              ))
            ) : (
              // Handle array of objects
              (config.columns as Column[]).map((column, index) => (
                <Badge key={index} variant="secondary">
                  {column.table}.{column.name}
                  {column.aggregate ? ` (${column.aggregate})` : ''}
                  {column.alias ? ` as ${column.alias}` : ''}
                </Badge>
              ))
            )}
          </div>
        </div>

        {config.groupBy && (
          <div>
            <h4 className="text-sm font-medium mb-1">Group By</h4>
            {isGroupByString ? (
              // Handle string
              <Badge variant="outline">{config.groupBy as string}</Badge>
            ) : (
              // Handle array of objects
              <div className="flex flex-wrap gap-2">
                {(config.groupBy as GroupByField[]).map((group, index) => (
                  <Badge key={index} variant="outline">
                    {group.table ? `${group.table}.${group.field}` : group.field}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {config.sortBy && (
          <div>
            <h4 className="text-sm font-medium mb-1">Sorting</h4>
            <div className="flex items-center gap-2">
              {isSortByString ? (
                // Handle string
                <span className="text-sm">{config.sortBy as string}</span>
              ) : (
                // Handle object
                <span className="text-sm">
                  {(config.sortBy as SortField).table ?
                    `${(config.sortBy as SortField).table}.${(config.sortBy as SortField).field}` :
                    (config.sortBy as SortField).field}
                  {(config.sortBy as SortField).aggregate ?
                    ` (${(config.sortBy as SortField).aggregate})` : ''}
                </span>
              )}
              <Badge variant="outline">{config.sortDirection || 'asc'}</Badge>
            </div>
          </div>
        )}

        {config.limit && (
          <div>
            <h4 className="text-sm font-medium mb-1">Limit</h4>
            <Badge variant="outline">{config.limit}</Badge>
          </div>
        )}

        {config.chartType && (
          <div>
            <h4 className="text-sm font-medium mb-1">Chart Type</h4>
            <Badge variant="outline">{config.chartType}</Badge>
          </div>
        )}

        {(config.sqlQuery || config.tableDefinition) && (
          <div>
            <h4 className="text-sm font-medium mb-1 flex items-center gap-2">
              SQL Query
              {explanation && explanation.includes('SQL query was fixed') && (
                <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
                  Validated & Fixed
                </Badge>
              )}
              {explanation && explanation.includes('could not be validated') && (
                <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
                  Validation Failed
                </Badge>
              )}
            </h4>
            <div className="bg-muted p-2 rounded-md text-xs overflow-x-auto">
              <pre>{config.sqlQuery || (config.tableDefinition && config.tableDefinition.includes('SQL Query:') ?
                config.tableDefinition.match(/SQL Query: ([\s\S]*?)(?:\n\n|$)/)?.[1]?.trim() :
                'No SQL query available')}</pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
