'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Sparkles, Database, FilterX, Loader2, Pencil, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { AIPromptInterface } from './AIPromptInterface';
import { AIReportEditPreview } from './AIReportEditPreview';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CustomReport, ReportField, FilterCondition, SortOption } from '../lib/types';
import { TableSelector } from './custom-report/TableSelector';
import { SortingAndFiltering } from './custom-report/SortingAndFiltering';
import { ReportPreview } from './custom-report/ReportPreview';

interface EditReportModalProps {
  report: CustomReport;
  onClose: () => void;
  onSave: (updatedReport: CustomReport) => void;
}

export function EditReportModal({ report, onClose, onSave }: EditReportModalProps) {
  // Determine if this is an AI-generated report
  const isAIGenerated = (report.tableDefinition && (
    report.tableDefinition.includes('SQL Query:') ||
    report.tableDefinition.includes('Joins:') ||
    report.tableDefinition.includes('AI-Generated Report')
  )) || (report.description && (
    report.description.includes('SQL Query:') ||
    report.description.includes('Joins:') ||
    report.description.includes('AI-Generated Report')
  ));

  console.log('Is AI-generated report in EditReportModal:', isAIGenerated);

  // Set the active tab based on whether the report is AI-generated or manually created
  const [activeTab, setActiveTab] = useState<string>(isAIGenerated ? 'ai' : 'manual');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedConfig, setGeneratedConfig] = useState<any>(null);
  const [explanation, setExplanation] = useState<string>('');
  const [previewData, setPreviewData] = useState<any[]>([]);

  // State for manual editing
  const [manualEditReport, setManualEditReport] = useState<CustomReport>({...report});
  const [manualEditTab, setManualEditTab] = useState<string>('columns');

  // Initialize manual edit report when component mounts
  useEffect(() => {
    // Log the original report fields for debugging
    console.log('ORIGINAL REPORT FIELDS:', report.fields);

    // Make sure we have all the necessary properties
    const initialReport = {
      ...report,
      fields: report.fields || [],
      filters: report.filters || [],
      sortBy: report.sortBy || [],
      groupBy: report.groupBy || [],
      joins: report.joins || []
    };

    // Ensure all fields have the required properties
    initialReport.fields = initialReport.fields.map(field => {
      // Create a complete field object with all required properties
      const completeField = {
        id: field.id || `${report.dataSource}_${field.name}`,
        name: field.name,
        sourceField: field.sourceField || field.name,
        displayName: field.displayName || field.name,
        dataType: field.dataType || 'string',
        isVisible: field.isVisible !== false,
        width: field.width || 150
      };

      console.log(`Processing field: ${field.name}`, completeField);
      return completeField;
    });

    // Log the processed report fields for debugging
    console.log('PROCESSED REPORT FIELDS:', initialReport.fields);

    // Set the manual edit report with all fields
    setManualEditReport(initialReport);
  }, [report]);

  const handleGenerateWithAI = async (prompt: string) => {
    if (!prompt.trim()) {
      toast.error('Please enter a description of the changes you want to make');
      return;
    }

    setIsGenerating(true);
    try {
      // Step 1: Generate the updated report configuration
      const response = await fetch('/api/ai-report-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          currentReport: {
            id: report.id,
            name: report.name,
            description: report.description,
            tableDefinition: report.tableDefinition,
            dataSource: report.dataSource,
            fields: report.fields,
            filters: report.filters,
            groupBy: report.groupBy,
            sortBy: report.sortBy,
            calculations: report.calculations,
            visualizations: report.visualizations,
            joins: report.joins,
            related_tables: report.related_tables,
            result_limit: report.result_limit
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate report updates');
      }

      const data = await response.json();
      let updatedReport = data.updatedReport;
      let explanation = data.explanation;

      // Step 2: Validate the SQL query if one was generated
      if (updatedReport.sqlQuery) {
        // Import the validateSqlQuery function
        const { validateSqlQuery } = await import('../lib/custom-reports');

        // Try to validate and fix the SQL query up to 5 times
        let isValid = false;
        let attempts = 0;
        let consecutiveFailures = 0;
        let lastError = '';
        let query = updatedReport.sqlQuery;

        while (!isValid && attempts < 5 && consecutiveFailures < 5) {
          attempts++;
          console.log(`Validating SQL query (attempt ${attempts}/5):`, query);

          try {
            const validation = await validateSqlQuery(query);

            if (validation.isValid) {
              isValid = true;
              console.log('SQL query is valid');
              // Update the SQL query in the report config
              updatedReport.sqlQuery = query;
              // Reset consecutive failures counter
              consecutiveFailures = 0;

              // Get preview data (first 5 rows)
              setPreviewData(validation.data.slice(0, 5));
            } else {
              lastError = validation.error || 'Unknown error';
              console.error(`SQL validation error (attempt ${attempts}/5):`, lastError);
              consecutiveFailures++;

              if (attempts < 5) {
                // Try to fix the query using the AI
                try {
                  const fixResponse = await fetch('/api/fix-sql-query', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query, error: lastError }),
                  });

                  if (fixResponse.ok) {
                    const fixData = await fixResponse.json();
                    query = fixData.fixedQuery;
                    console.log(`Fixed SQL query (attempt ${attempts}/5):`, query);
                  } else {
                    console.error('Failed to fix SQL query');
                    consecutiveFailures++;
                  }
                } catch (fixError) {
                  console.error('Error fixing SQL query:', fixError);
                  consecutiveFailures++;
                }
              }
            }
          } catch (validationError) {
            console.error('Error during SQL validation:', validationError);
            lastError = validationError instanceof Error ? validationError.message : String(validationError);
            consecutiveFailures++;
          }
        }

        if (!isValid) {
          toast.error(`Failed to validate SQL query after ${attempts} attempts: ${lastError}`);
          setIsGenerating(false);
          return;
        }

        // Add the validation result to the explanation
        explanation += `\n\nSQL query was validated and ${isValid ? 'is valid' : 'could not be validated'}.`;
      }

      setGeneratedConfig(updatedReport);
      setExplanation(explanation);

      toast.success('Report updates generated successfully');
    } catch (error) {
      console.error('Error generating report updates:', error);
      toast.error('Failed to generate report updates');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle data source change in manual editing
  const handleDataSourceChange = (dataSource: string) => {
    setManualEditReport(prev => {
      const updatedReport = {
        ...prev,
        dataSource
      };

      // Don't automatically switch to preview tab when data source changes

      return updatedReport;
    });
  };

  // Handle selected fields change in manual editing
  const handleSelectedFieldsChange = (fields: ReportField[]) => {
    console.log('EDIT REPORT MODAL - SELECTED FIELDS CHANGED:', fields);

    // Make sure we preserve all fields, including those from other tables
    setManualEditReport(prev => {
      // Log the current fields for debugging
      console.log('EDIT REPORT MODAL - CURRENT FIELDS:', prev.fields);

      // Create a new fields array with all the selected fields
      const updatedFields = [...fields];

      console.log('EDIT REPORT MODAL - UPDATED FIELDS:', updatedFields);

      // Don't automatically switch to preview tab when fields change

      return {
        ...prev,
        fields: updatedFields
      };
    });
  };

  // Handle filters change in manual editing
  const handleFiltersChange = (filters: FilterCondition[]) => {
    setManualEditReport(prev => {
      const updatedReport = {
        ...prev,
        filters
      };

      // Don't automatically switch to preview tab when filters change

      return updatedReport;
    });
  };

  // Handle sorting change in manual editing
  const handleSortingChange = (sortBy: SortOption[]) => {
    setManualEditReport(prev => {
      const updatedReport = {
        ...prev,
        sortBy
      };

      // Don't automatically switch to preview tab when sorting changes

      return updatedReport;
    });
  };

  // Handle save for both AI and manual editing
  const handleSave = () => {
    if (isAIGenerated) {
      // For AI-generated reports, only allow AI editing
      if (!generatedConfig) {
        toast.error('Please generate report updates first');
        return;
      }

      // Prepare the updated report by merging AI changes with the current manual state
      const updatedReport: CustomReport = {
        ...manualEditReport, // Start with the current manual edit state
        ...generatedConfig, // Apply AI generated changes
        // Ensure essential original properties are preserved
        id: report.id,
        createdAt: report.createdAt,
        createdBy: report.createdBy,
        isTemplate: report.isTemplate,
        isFavorite: report.isFavorite,
      };

      console.log('REPORT FLOW - Saving AI-generated report:', updatedReport);
      onSave(updatedReport);
      onClose();
    } else {
      // For manually created reports, only allow manual editing
      if (manualEditReport.fields.length === 0) {
        toast.error('Please select at least one column');
        return;
      }

      // Create a deep copy of the report to ensure all changes are captured
      const finalReport: CustomReport = JSON.parse(JSON.stringify(manualEditReport));

      // Ensure groupBy is an array
      if (!finalReport.groupBy || !Array.isArray(finalReport.groupBy)) {
        finalReport.groupBy = [];
      }

      // Log the final report for debugging
      console.log('REPORT FLOW - Saving manually edited report:', finalReport);
      console.log('REPORT FLOW - Fields in final report:', finalReport.fields);

      onSave(finalReport);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b flex items-center justify-between">
          <h2 className="text-xl font-semibold">Edit Report: {report.name}</h2>
        </div>

        <div className="flex-1 overflow-auto p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {isAIGenerated ? (
              // Only show AI editing for AI-generated reports
              <TabsList className="grid w-full grid-cols-1 mb-4">
                <TabsTrigger value="ai" className="flex items-center gap-1">
                  <Sparkles className="h-4 w-4" />
                  AI Assist
                </TabsTrigger>
              </TabsList>
            ) : (
              // Only show manual editing for manually created reports
              <TabsList className="grid w-full grid-cols-1 mb-4">
                <TabsTrigger value="manual" className="flex items-center gap-1">
                  <Pencil className="h-4 w-4" />
                  Manual
                </TabsTrigger>
              </TabsList>
            )}

            <TabsContent value="ai" className="mt-0">
              {!generatedConfig ? (
                <AIPromptInterface
                  onGenerate={handleGenerateWithAI}
                  isGenerating={isGenerating}
                  placeholder="Describe the changes you want to make to this report..."
                  buttonText="Update Report"
                />
              ) : (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">AI Interpretation</h3>
                  <p className="text-sm text-muted-foreground">{explanation}</p>

                  <AIReportEditPreview
                    config={generatedConfig}
                    explanation={explanation}
                    previewData={previewData}
                  />

                  <div className="flex justify-start">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setGeneratedConfig(null);
                        setExplanation('');
                        setPreviewData([]);
                      }}
                    >
                      Start Over
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="manual" className="mt-0">
              <div className="space-y-4">
                <Tabs value={manualEditTab} onValueChange={setManualEditTab} className="w-full">
                  <div className="flex justify-between items-center mb-4">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="columns" className="flex items-center gap-1">
                        <Database className="h-4 w-4" />
                        Columns
                      </TabsTrigger>
                      <TabsTrigger value="filters" className="flex items-center gap-1">
                        <FilterX className="h-4 w-4" />
                        Filters & Sorting
                      </TabsTrigger>
                      <TabsTrigger value="preview" className="flex items-center gap-1">
                        <Sparkles className="h-4 w-4" />
                        Preview
                      </TabsTrigger>
                    </TabsList>

                    {manualEditTab === 'preview' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Force a re-render of the preview by creating a new copy of the report
                          setManualEditReport(prev => ({...JSON.parse(JSON.stringify(prev))}));
                        }}
                        className="ml-2 flex items-center gap-1"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Refresh
                      </Button>
                    )}
                  </div>

                  {/* Columns tab */}
                  <TabsContent value="columns" className="mt-0">
                    <TableSelector
                      dataSource={manualEditReport.dataSource}
                      selectedFields={manualEditReport.fields}
                      onDataSourceChange={handleDataSourceChange}
                      onSelectedFieldsChange={handleSelectedFieldsChange}
                    />
                  </TabsContent>

                  {/* Filters & Sorting tab */}
                  <TabsContent value="filters" className="mt-0">
                    <SortingAndFiltering
                      fields={manualEditReport.fields}
                      filters={manualEditReport.filters || []}
                      sortBy={manualEditReport.sortBy || []}
                      onFiltersChange={handleFiltersChange}
                      onSortingChange={handleSortingChange}
                    />
                  </TabsContent>

                  {/* Preview tab */}
                  <TabsContent value="preview" className="mt-0">
                    <ReportPreview
                      key={JSON.stringify(manualEditReport)}
                      report={manualEditReport}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="p-6 border-t flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={activeTab === 'ai' ? !generatedConfig : manualEditReport.fields.length === 0}
            className="flex items-center gap-2"
          >
            {isAIGenerated ? (
              <>
                <Sparkles className="h-4 w-4" />
                Apply AI Changes
              </>
            ) : (
              <>
                <Pencil className="h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
}
