import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface AIPromptInterfaceProps {
  onGenerate: (prompt: string) => Promise<void>;
  isGenerating: boolean;
  placeholder?: string;
  buttonText?: string;
  labelText?: string;
  helpText?: string;
}

export function AIPromptInterface({
  onGenerate,
  isGenerating,
  placeholder = "E.g., I need a report showing income sources with their amounts and frequency, grouped by household, with a bar chart visualization",
  buttonText = "Generate Report",
  labelText = "Describe the report you want to create",
  helpText = "Be specific about what data you want to see, any filters, and how you want it visualized."
}: AIPromptInterfaceProps) {
  const [prompt, setPrompt] = useState('');
  const [showExamples, setShowExamples] = useState(false);

  const examples = [
    "Show me a report of all households with high risk tolerance, sorted by total assets",
    "Create a report of assets with their values and types, grouped by household",
    "I need a report showing expenses by category with a pie chart breakdown",
    "Generate a report of income sources with their amounts and frequency",
    "Show me liabilities with their interest rates and lenders",
    "Create a report of insurance policies with their premiums and coverage amounts",
    "Show me goals by status with their target amounts",
    "Generate a report of tasks due in the next month, sorted by importance",
    "Show me client interactions from the last quarter, grouped by type",
    "Create a report of recommendations with their financial impact and status"
  ];

  const handleUseExample = (example: string) => {
    setPrompt(example);
    setShowExamples(false);
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="ai-prompt" className="text-base font-medium">
          {labelText}
        </Label>
        <p className="text-sm text-muted-foreground mb-2">
          {helpText}
        </p>
        <Textarea
          id="ai-prompt"
          placeholder={placeholder}
          className="h-32"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
        />
      </div>

      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowExamples(!showExamples)}
        >
          {showExamples ? 'Hide Examples' : 'Show Examples'}
        </Button>

        <Button
          onClick={() => onGenerate(prompt)}
          disabled={isGenerating || !prompt.trim()}
        >
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            buttonText
          )}
        </Button>
      </div>

      {showExamples && (
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-sm font-medium mb-2">Example Prompts</h3>
            <ul className="space-y-2">
              {examples.map((example, index) => (
                <li key={index}>
                  <Button
                    variant="link"
                    className="h-auto p-0 text-left text-sm"
                    onClick={() => handleUseExample(example)}
                  >
                    {example}
                  </Button>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
