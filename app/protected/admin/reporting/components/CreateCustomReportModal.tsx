import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Sparkles } from 'lucide-react';
import { toast } from 'sonner';
import { AIPromptInterface } from './AIPromptInterface';
import { AIReportPreview } from './AIReportPreview';

interface CreateCustomReportModalProps {
  onClose: () => void;
  onSave: (config: any) => void;
}

export function CreateCustomReportModal({ onClose, onSave }: CreateCustomReportModalProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedConfig, setGeneratedConfig] = useState<any>(null);
  const [explanation, setExplanation] = useState<string>('');

  const handleGenerateWithAI = async (prompt: string) => {
    if (!prompt.trim()) {
      toast.error('Please enter a description of the report you want to create');
      return;
    }

    setIsGenerating(true);
    try {
      // Step 1: Generate the initial report configuration
      const response = await fetch('/api/ai-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate report configuration');
      }

      const data = await response.json();
      let reportConfig = data.reportConfig;
      let explanation = data.explanation;

      // Step 2: Validate the SQL query if one was generated
      if (reportConfig.sqlQuery) {
        // Import the validateSqlQuery function
        const { validateSqlQuery } = await import('../lib/custom-reports');

        // Try to validate and fix the SQL query up to 5 times
        let isValid = false;
        let attempts = 0;
        let consecutiveFailures = 0;
        let lastError = '';
        let query = reportConfig.sqlQuery;

        while (!isValid && attempts < 5 && consecutiveFailures < 5) {
          attempts++;
          console.log(`Validating SQL query (attempt ${attempts}/5):`, query);

          try {
            const validation = await validateSqlQuery(query);

            if (validation.isValid) {
              isValid = true;
              console.log('SQL query is valid');
              // Update the SQL query in the report config
              reportConfig.sqlQuery = query;
              // Reset consecutive failures counter
              consecutiveFailures = 0;
            } else {
              lastError = validation.error || 'Unknown error';
              console.error(`SQL validation error (attempt ${attempts}/5):`, lastError);
              consecutiveFailures++;

              if (attempts < 5) {
                // Try to fix the query using the AI
                try {
                  const fixResponse = await fetch('/api/fix-sql-query', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query, error: lastError }),
                  });

                  if (fixResponse.ok) {
                    const fixData = await fixResponse.json();
                    query = fixData.fixedQuery;
                    console.log(`Fixed SQL query (attempt ${attempts}/5):`, query);
                  } else {
                    console.error('Failed to fix SQL query');
                    consecutiveFailures++;
                  }
                } catch (fixError) {
                  console.error('Error fixing SQL query:', fixError);
                  consecutiveFailures++;
                }
              }
            }
          } catch (validationError) {
            console.error('Error during SQL validation:', validationError);
            lastError = validationError instanceof Error ? validationError.message : String(validationError);
            consecutiveFailures++;
          }
        }

        if (!isValid) {
          // Add a note about the SQL validation failure to the explanation
          explanation += `\n\nNote: The generated SQL query could not be validated after ${attempts} attempts. Last error: ${lastError}`;
        } else if (attempts > 1) {
          // Add a note about the SQL validation success after fixes
          explanation += `\n\nNote: The SQL query was fixed and validated successfully after ${attempts} attempts.`;
          // Update the SQL query in the report config
          reportConfig.sqlQuery = query;
        }
      }

      setGeneratedConfig(reportConfig);
      setExplanation(explanation);

      toast.success('Report configuration generated successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report configuration');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = () => {
    if (!generatedConfig || !generatedConfig.title) {
      toast.error('Please generate a report configuration first');
      return;
    }

    onSave(generatedConfig);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b flex items-center justify-between">
          <h2 className="text-xl font-semibold">AI-Assisted Report Creation</h2>
          <Sparkles className="h-5 w-5 text-primary" />
        </div>

        <div className="flex-1 overflow-auto p-6">
          {!generatedConfig ? (
            <AIPromptInterface
              onGenerate={handleGenerateWithAI}
              isGenerating={isGenerating}
            />
          ) : (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">AI Interpretation</h3>
              <p className="text-sm text-muted-foreground">{explanation}</p>

              <AIReportPreview config={generatedConfig} explanation={explanation} />

              <div className="flex justify-start">
                <Button
                  variant="outline"
                  onClick={() => {
                    setGeneratedConfig(null);
                    setExplanation('');
                  }}
                >
                  Start Over
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="p-6 border-t flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!generatedConfig || !generatedConfig.title}
            className="flex items-center gap-2"
          >
            <Sparkles className="h-4 w-4" />
            Create AI Report
          </Button>
        </div>
      </Card>
    </div>
  );
}
