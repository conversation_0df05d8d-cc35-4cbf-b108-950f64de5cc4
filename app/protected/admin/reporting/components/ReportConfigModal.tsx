'use client';

import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';

interface ReportConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: ReportConfig) => void;
  initialConfig?: ReportConfig;
}

export interface ReportConfig {
  id?: string;
  name: string;
  description: string;
  type: string;
  category: string;
  filters: Record<string, any>;
  outputFormat: 'table' | 'chart';
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    day?: number;
    time?: string;
  };
}

export function ReportConfigModal({ 
  isOpen, 
  onClose, 
  onSave,
  initialConfig 
}: ReportConfigModalProps) {
  const [config, setConfig] = useState<ReportConfig>(initialConfig || {
    name: '',
    description: '',
    type: 'client-demographics',
    category: 'client',
    filters: {},
    outputFormat: 'table',
  });
  
  const [activeTab, setActiveTab] = useState('general');
  
  const handleSave = () => {
    onSave(config);
    onClose();
  };
  
  const updateConfig = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  const updateFilter = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value
      }
    }));
  };
  
  const updateSchedule = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      schedule: {
        ...(prev.schedule || { enabled: false, frequency: 'monthly' }),
        [key]: value
      }
    }));
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Configure Report</DialogTitle>
          <DialogDescription>
            Customize your report settings and parameters.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="output">Output</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Report Name</Label>
                <Input 
                  id="name" 
                  value={config.name} 
                  onChange={(e) => updateConfig('name', e.target.value)}
                  placeholder="Enter report name"
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input 
                  id="description" 
                  value={config.description} 
                  onChange={(e) => updateConfig('description', e.target.value)}
                  placeholder="Enter report description"
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="type">Report Type</Label>
                <Select 
                  value={config.type} 
                  onValueChange={(value) => updateConfig('type', value)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="client-demographics">Client Demographics</SelectItem>
                    <SelectItem value="financial-snapshot">Financial Snapshot</SelectItem>
                    <SelectItem value="asset-allocation">Asset Allocation</SelectItem>
                    <SelectItem value="net-worth-trend">Net Worth Trend</SelectItem>
                    <SelectItem value="investment-performance">Investment Performance</SelectItem>
                    <SelectItem value="goal-progress">Goal Progress</SelectItem>
                    <SelectItem value="client-acquisition">Client Acquisition</SelectItem>
                    <SelectItem value="review-schedule">Review Schedule</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={config.category} 
                  onValueChange={(value) => updateConfig('category', value)}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="client">Client</SelectItem>
                    <SelectItem value="financial">Financial</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="operational">Operational</SelectItem>
                    <SelectItem value="compliance">Compliance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="filters" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="date-range">Date Range</Label>
                <Select 
                  value={config.filters.dateRange || 'all-time'} 
                  onValueChange={(value) => updateFilter('dateRange', value)}
                >
                  <SelectTrigger id="date-range">
                    <SelectValue placeholder="Select date range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-time">All Time</SelectItem>
                    <SelectItem value="this-year">This Year</SelectItem>
                    <SelectItem value="last-year">Last Year</SelectItem>
                    <SelectItem value="this-quarter">This Quarter</SelectItem>
                    <SelectItem value="last-quarter">Last Quarter</SelectItem>
                    <SelectItem value="this-month">This Month</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {config.filters.dateRange === 'custom' && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input 
                      id="start-date" 
                      type="date" 
                      value={config.filters.startDate || ''} 
                      onChange={(e) => updateFilter('startDate', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="end-date">End Date</Label>
                    <Input 
                      id="end-date" 
                      type="date" 
                      value={config.filters.endDate || ''} 
                      onChange={(e) => updateFilter('endDate', e.target.value)}
                    />
                  </div>
                </div>
              )}
              
              <div className="grid gap-2">
                <Label htmlFor="client-filter">Client Filter</Label>
                <Select 
                  value={config.filters.clientFilter || 'all'} 
                  onValueChange={(value) => updateFilter('clientFilter', value)}
                >
                  <SelectTrigger id="client-filter">
                    <SelectValue placeholder="Select client filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Clients</SelectItem>
                    <SelectItem value="active">Active Clients</SelectItem>
                    <SelectItem value="inactive">Inactive Clients</SelectItem>
                    <SelectItem value="specific">Specific Client</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {config.filters.clientFilter === 'specific' && (
                <div className="grid gap-2">
                  <Label htmlFor="client-id">Client</Label>
                  <Input 
                    id="client-id" 
                    placeholder="Search for client"
                    value={config.filters.clientId || ''} 
                    onChange={(e) => updateFilter('clientId', e.target.value)}
                  />
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="output" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="output-format">Output Format</Label>
                <Select 
                  value={config.outputFormat} 
                  onValueChange={(value: 'table' | 'chart') => updateConfig('outputFormat', value)}
                >
                  <SelectTrigger id="output-format">
                    <SelectValue placeholder="Select output format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="table">Table</SelectItem>
                    <SelectItem value="chart">Chart</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {config.outputFormat === 'chart' && (
                <div className="grid gap-2">
                  <Label htmlFor="chart-type">Chart Type</Label>
                  <Select 
                    value={config.chartType || 'bar'} 
                    onValueChange={(value) => updateConfig('chartType', value)}
                  >
                    <SelectTrigger id="chart-type">
                      <SelectValue placeholder="Select chart type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bar">Bar Chart</SelectItem>
                      <SelectItem value="line">Line Chart</SelectItem>
                      <SelectItem value="pie">Pie Chart</SelectItem>
                      <SelectItem value="area">Area Chart</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <div className="grid gap-2">
                <Label>Export Options</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="export-excel" 
                    checked={config.filters.exportExcel || false}
                    onCheckedChange={(checked) => updateFilter('exportExcel', checked)}
                  />
                  <Label htmlFor="export-excel">Export to Excel</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="export-pdf" 
                    checked={config.filters.exportPdf || false}
                    onCheckedChange={(checked) => updateFilter('exportPdf', checked)}
                  />
                  <Label htmlFor="export-pdf">Export to PDF</Label>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="schedule" className="space-y-4">
            <div className="grid gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="schedule-enabled" 
                  checked={config.schedule?.enabled || false}
                  onCheckedChange={(checked) => {
                    if (!config.schedule) {
                      setConfig(prev => ({
                        ...prev,
                        schedule: {
                          enabled: !!checked,
                          frequency: 'monthly'
                        }
                      }));
                    } else {
                      updateSchedule('enabled', !!checked);
                    }
                  }}
                />
                <Label htmlFor="schedule-enabled">Schedule this report</Label>
              </div>
              
              {config.schedule?.enabled && (
                <>
                  <div className="grid gap-2">
                    <Label htmlFor="frequency">Frequency</Label>
                    <Select 
                      value={config.schedule.frequency} 
                      onValueChange={(value: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly') => 
                        updateSchedule('frequency', value)
                      }
                    >
                      <SelectTrigger id="frequency">
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {config.schedule.frequency === 'weekly' && (
                    <div className="grid gap-2">
                      <Label htmlFor="day-of-week">Day of Week</Label>
                      <Select 
                        value={String(config.schedule.day || 1)} 
                        onValueChange={(value) => updateSchedule('day', parseInt(value))}
                      >
                        <SelectTrigger id="day-of-week">
                          <SelectValue placeholder="Select day" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">Monday</SelectItem>
                          <SelectItem value="2">Tuesday</SelectItem>
                          <SelectItem value="3">Wednesday</SelectItem>
                          <SelectItem value="4">Thursday</SelectItem>
                          <SelectItem value="5">Friday</SelectItem>
                          <SelectItem value="6">Saturday</SelectItem>
                          <SelectItem value="0">Sunday</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  {['monthly', 'quarterly', 'yearly'].includes(config.schedule.frequency) && (
                    <div className="grid gap-2">
                      <Label htmlFor="day-of-month">Day of Month</Label>
                      <Select 
                        value={String(config.schedule.day || 1)} 
                        onValueChange={(value) => updateSchedule('day', parseInt(value))}
                      >
                        <SelectTrigger id="day-of-month">
                          <SelectValue placeholder="Select day" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 31 }, (_, i) => (
                            <SelectItem key={i + 1} value={String(i + 1)}>
                              {i + 1}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  <div className="grid gap-2">
                    <Label htmlFor="time">Time</Label>
                    <Input 
                      id="time" 
                      type="time" 
                      value={config.schedule.time || '09:00'} 
                      onChange={(e) => updateSchedule('time', e.target.value)}
                    />
                  </div>
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Report</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
