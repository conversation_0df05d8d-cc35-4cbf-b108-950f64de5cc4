'use client';

import { useState, useEffect, JSX } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ReportResultTable } from './ReportResultTable';
import { ReportResultChart } from './ReportResultChart';
import { Loader2, ArrowRight, Calendar } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { ReportData } from '../lib/types';
import { reportTypes } from '../lib/report-types';

export function ComparativeAnalysis() {
  const [reportType, setReportType] = useState('');
  const [periodType, setPeriodType] = useState('year-over-year');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPeriodData, setCurrentPeriodData] = useState<ReportData | null>(null);
  const [previousPeriodData, setPreviousPeriodData] = useState<ReportData | null>(null);
  const [comparisonData, setComparisonData] = useState<any[] | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'chart'>('table');

  // Available report types for comparison
  const availableReportTypes = reportTypes.filter(report => 
    ['financial', 'performance'].includes(report.category)
  );

  // Run the comparative analysis
  async function runComparison() {
    if (!reportType) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Find the selected report type
      const selectedReport = reportTypes.find(r => r.id === reportType);
      if (!selectedReport) {
        throw new Error('Report type not found');
      }
      
      // Determine date ranges based on period type
      const { currentPeriodRange, previousPeriodRange } = getDateRanges(periodType);
      
      // Run report for current period
      const currentPeriodResult = await selectedReport.fetchData({
        dateRange: currentPeriodRange,
        filters: {
          clientFilter: 'all'
        }
      });
      
      // Run report for previous period
      const previousPeriodResult = await selectedReport.fetchData({
        dateRange: previousPeriodRange,
        filters: {
          clientFilter: 'all'
        }
      });
      
      // Store the results
      setCurrentPeriodData(currentPeriodResult);
      setPreviousPeriodData(previousPeriodResult);
      
      // Generate comparison data
      const comparison = generateComparisonData(
        currentPeriodResult.data, 
        previousPeriodResult.data,
        currentPeriodResult.columns
      );
      
      setComparisonData(comparison);
    } catch (err) {
      console.error('Error running comparison:', err);
      setError(err instanceof Error ? err.message : 'Failed to run comparison');
    } finally {
      setIsLoading(false);
    }
  }

  // Get date ranges for current and previous periods
  function getDateRanges(periodType: string): { 
    currentPeriodRange: { start: string, end: string }, 
    previousPeriodRange: { start: string, end: string } 
  } {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentQuarter = Math.floor(currentMonth / 3);
    
    switch (periodType) {
      case 'year-over-year':
        return {
          currentPeriodRange: {
            start: `${currentYear}-01-01`,
            end: `${currentYear}-12-31`
          },
          previousPeriodRange: {
            start: `${currentYear - 1}-01-01`,
            end: `${currentYear - 1}-12-31`
          }
        };
        
      case 'quarter-over-quarter':
        const currentQuarterStart = new Date(currentYear, currentQuarter * 3, 1);
        const currentQuarterEnd = new Date(currentYear, currentQuarter * 3 + 3, 0);
        
        const previousQuarterStart = new Date(currentYear, (currentQuarter - 1) * 3, 1);
        const previousQuarterEnd = new Date(currentYear, (currentQuarter - 1) * 3 + 3, 0);
        
        return {
          currentPeriodRange: {
            start: currentQuarterStart.toISOString().split('T')[0],
            end: currentQuarterEnd.toISOString().split('T')[0]
          },
          previousPeriodRange: {
            start: previousQuarterStart.toISOString().split('T')[0],
            end: previousQuarterEnd.toISOString().split('T')[0]
          }
        };
        
      case 'month-over-month':
        const currentMonthStart = new Date(currentYear, currentMonth, 1);
        const currentMonthEnd = new Date(currentYear, currentMonth + 1, 0);
        
        const previousMonthStart = new Date(currentYear, currentMonth - 1, 1);
        const previousMonthEnd = new Date(currentYear, currentMonth, 0);
        
        return {
          currentPeriodRange: {
            start: currentMonthStart.toISOString().split('T')[0],
            end: currentMonthEnd.toISOString().split('T')[0]
          },
          previousPeriodRange: {
            start: previousMonthStart.toISOString().split('T')[0],
            end: previousMonthEnd.toISOString().split('T')[0]
          }
        };
        
      default:
        return {
          currentPeriodRange: {
            start: `${currentYear}-01-01`,
            end: `${currentYear}-12-31`
          },
          previousPeriodRange: {
            start: `${currentYear - 1}-01-01`,
            end: `${currentYear - 1}-12-31`
          }
        };
    }
  }

  // Generate comparison data between current and previous periods
  function generateComparisonData(
    currentData: any[], 
    previousData: any[],
    columns: any[]
  ): any[] {
    // If the data structure is different, we can't compare directly
    if (currentData.length === 0 || previousData.length === 0) {
      return [];
    }
    
    // For simple data structures, we can compare by matching keys
    const comparisonData = currentData.map(currentItem => {
      // Try to find a matching item in the previous period
      // This is a simple approach - in a real app, you'd need a more robust matching strategy
      const key = Object.keys(currentItem)[0]; // Use the first property as a key
      const previousItem = previousData.find(item => item[key] === currentItem[key]);
      
      const result: Record<string, any> = { ...currentItem };
      
      // Add comparison columns for numeric values
      Object.keys(currentItem).forEach(key => {
        if (typeof currentItem[key] === 'number') {
          const currentValue = currentItem[key];
          const previousValue = previousItem ? previousItem[key] : 0;
          
          // Calculate absolute change
          result[`${key}_change`] = currentValue - previousValue;
          
          // Calculate percentage change
          result[`${key}_pct_change`] = previousValue !== 0
            ? ((currentValue - previousValue) / Math.abs(previousValue)) * 100
            : currentValue !== 0 ? 100 : 0;
        }
      });
      
      return result;
    });
    
    return comparisonData;
  }

  // Generate columns for the comparison table
  function generateComparisonColumns(): any[] {
    if (!currentPeriodData || !comparisonData || comparisonData.length === 0) {
      return [];
    }
    
    const firstRow = comparisonData[0];
    const columns: any[] = [];
    
    // Add columns for each property in the data
    Object.keys(firstRow).forEach(key => {
      // Skip change columns - we'll add them after their base columns
      if (key.endsWith('_change') || key.endsWith('_pct_change')) {
        return;
      }
      
      // Add the base column
      columns.push({
        accessorKey: key,
        header: formatColumnName(key),
        cell: ({ getValue }: any) => {
          const value = getValue();
          return formatCellValue(value);
        }
      });
      
      // If we have change columns for this property, add them
      if (key in firstRow && `${key}_change` in firstRow) {
        columns.push({
          accessorKey: `${key}_change`,
          header: 'Change',
          cell: ({ getValue }: any) => {
            const value = getValue();
            return formatChangeValue(value);
          }
        });
        
        columns.push({
          accessorKey: `${key}_pct_change`,
          header: '% Change',
          cell: ({ getValue }: any) => {
            const value = getValue();
            return formatPercentageChange(value);
          }
        });
      }
    });
    
    return columns;
  }

  // Format column names for display
  function formatColumnName(key: string): string {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Format cell values based on type
  function formatCellValue(value: any): string {
    if (typeof value === 'number') {
      // Check if it looks like a currency value
      if (value > 100 || value < -100) {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value);
      }
      
      return value.toFixed(2);
    }
    
    return String(value);
  }

  // Format change values
  function formatChangeValue(value: number): JSX.Element {
    const formattedValue = typeof value === 'number'
      ? new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value)
      : String(value);
    
    const color = value > 0 ? 'text-green-600' : value < 0 ? 'text-red-600' : '';
    
    return (
      <span className={color}>
        {value > 0 ? '+' : ''}{formattedValue}
      </span>
    );
  }

  // Format percentage change values
  function formatPercentageChange(value: number): JSX.Element {
    const formattedValue = typeof value === 'number'
      ? `${value > 0 ? '+' : ''}${value.toFixed(2)}%`
      : String(value);
    
    const color = value > 0 ? 'text-green-600' : value < 0 ? 'text-red-600' : '';
    
    return <span className={color}>{formattedValue}</span>;
  }

  // Generate chart data for comparison
  function generateComparisonChartData(): any {
    if (!currentPeriodData || !previousPeriodData) {
      return null;
    }
    
    // Use the chart data from the original reports if available
    if (currentPeriodData.chartData && previousPeriodData.chartData) {
      return {
        labels: currentPeriodData.chartData.labels,
        datasets: [
          {
            ...currentPeriodData.chartData.datasets[0],
            label: `Current Period (${getPeriodLabel(periodType, true)})`,
          },
          {
            ...previousPeriodData.chartData.datasets[0],
            label: `Previous Period (${getPeriodLabel(periodType, false)})`,
            backgroundColor: 'rgba(153, 102, 255, 0.6)',
            borderColor: 'rgba(153, 102, 255, 1)',
          }
        ]
      };
    }
    
    return null;
  }

  // Get a human-readable label for the period
  function getPeriodLabel(periodType: string, isCurrent: boolean): string {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentQuarter = Math.floor(currentMonth / 3) + 1;
    
    switch (periodType) {
      case 'year-over-year':
        return isCurrent ? `${currentYear}` : `${currentYear - 1}`;
        
      case 'quarter-over-quarter':
        return isCurrent 
          ? `Q${currentQuarter} ${currentYear}` 
          : `Q${currentQuarter > 1 ? currentQuarter - 1 : 4} ${currentQuarter > 1 ? currentYear : currentYear - 1}`;
        
      case 'month-over-month':
        const monthNames = [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        return isCurrent 
          ? `${monthNames[currentMonth]} ${currentYear}` 
          : `${monthNames[currentMonth > 0 ? currentMonth - 1 : 11]} ${currentMonth > 0 ? currentYear : currentYear - 1}`;
        
      default:
        return isCurrent ? 'Current Period' : 'Previous Period';
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Comparative Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Configuration Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="report-type">Report Type</Label>
              <Select
                value={reportType}
                onValueChange={setReportType}
              >
                <SelectTrigger id="report-type">
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  {availableReportTypes.map(report => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="period-type">Comparison Period</Label>
              <Select
                value={periodType}
                onValueChange={setPeriodType}
              >
                <SelectTrigger id="period-type">
                  <SelectValue placeholder="Select period type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="year-over-year">Year over Year</SelectItem>
                  <SelectItem value="quarter-over-quarter">Quarter over Quarter</SelectItem>
                  <SelectItem value="month-over-month">Month over Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                onClick={runComparison} 
                disabled={!reportType || isLoading}
                className="w-full"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Run Comparison
              </Button>
            </div>
          </div>
          
          {/* Error Message */}
          {error && (
            <div className="bg-destructive/10 text-destructive p-3 rounded-md">
              {error}
            </div>
          )}
          
          {/* Results Section */}
          {currentPeriodData && previousPeriodData && (
            <div className="space-y-4">
              {/* Period Information */}
              <div className="flex items-center justify-center space-x-4 p-4 bg-muted rounded-md">
                <div className="text-center">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-1" />
                    Previous Period
                  </div>
                  <div className="font-medium">{getPeriodLabel(periodType, false)}</div>
                </div>
                
                <ArrowRight className="h-5 w-5 text-muted-foreground" />
                
                <div className="text-center">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-1" />
                    Current Period
                  </div>
                  <div className="font-medium">{getPeriodLabel(periodType, true)}</div>
                </div>
              </div>
              
              {/* View Toggle */}
              <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as 'table' | 'chart')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="table">Table View</TabsTrigger>
                  <TabsTrigger value="chart">Chart View</TabsTrigger>
                </TabsList>
                
                <TabsContent value="table">
                  {comparisonData && comparisonData.length > 0 ? (
                    <ReportResultTable
                      columns={generateComparisonColumns()}
                      data={comparisonData}
                    />
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No comparison data available
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="chart">
                  {generateComparisonChartData() ? (
                    <div className="h-[400px]">
                      <ReportResultChart
                        reportData={{
                          ...currentPeriodData,
                          chartData: generateComparisonChartData()
                        }}
                        type={currentPeriodData.recommendedChartType}
                      />
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No chart data available for comparison
                    </div>
                  )}
                </TabsContent>
              </Tabs>
              
              {/* Summary Section */}
              {currentPeriodData.summary && previousPeriodData.summary && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Previous Period Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {Object.entries(previousPeriodData.summary).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-muted-foreground">{formatColumnName(key)}</span>
                            <span>{formatCellValue(value)}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Current Period Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {Object.entries(currentPeriodData.summary).map(([key, value]) => {
                          const previousValue = previousPeriodData.summary?.[key];
                          const change = typeof value === 'number' && typeof previousValue === 'number'
                            ? value - previousValue
                            : null;
                          
                          const percentChange = typeof value === 'number' && typeof previousValue === 'number' && previousValue !== 0
                            ? ((value - previousValue) / Math.abs(previousValue)) * 100
                            : null;
                          
                          return (
                            <div key={key} className="flex justify-between">
                              <span className="text-muted-foreground">{formatColumnName(key)}</span>
                              <div className="text-right">
                                <div>{formatCellValue(value)}</div>
                                {change !== null && (
                                  <div className="text-xs">
                                    {formatChangeValue(change)} ({formatPercentageChange(percentChange || 0)})
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
