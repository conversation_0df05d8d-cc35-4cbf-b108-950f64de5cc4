'use client';

import { useEffect } from 'react';
import { migrateAdvancedReporting } from '../lib/migrate-client';

export function MigrationRunner() {
  useEffect(() => {
    // Run the migration when the component mounts
    const runMigration = async () => {
      try {
        await migrateAdvancedReporting();
      } catch (error) {
        console.error('Error running migration:', error);
      }
    };

    runMigration();
  }, []);

  // This component doesn't render anything
  return null;
}
