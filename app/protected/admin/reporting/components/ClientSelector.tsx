'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';

interface ClientSelectorProps {
  value?: string;
  onChange: (value: string) => void;
}

export function ClientSelector({ value, onChange }: ClientSelectorProps) {
  const [open, setOpen] = useState(false);
  const [clients, setClients] = useState<{ id: string; name: string }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    async function fetchClients() {
      setLoading(true);
      setError(null);
      
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('households')
          .select('id, "householdName"')
          .order('householdName', { ascending: true });
        
        if (error) {
          throw new Error(`Error fetching clients: ${error.message}`);
        }
        
        // Transform data to the format we need
        const formattedClients = data.map(client => ({
          id: client.id.toString(),
          name: client.householdName || 'Unnamed Household'
        }));
        
        setClients(formattedClients);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching clients');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    fetchClients();
  }, []);
  
  // Find the selected client name
  const selectedClient = clients.find(client => client.id === value);
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={loading}
        >
          {loading ? (
            <Skeleton className="h-4 w-[100px]" />
          ) : value && selectedClient ? (
            selectedClient.name
          ) : (
            "Select client..."
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search clients..." />
          <CommandEmpty>
            {error ? `Error: ${error}` : "No client found."}
          </CommandEmpty>
          <CommandGroup className="max-h-[300px] overflow-auto">
            {clients.map((client) => (
              <CommandItem
                key={client.id}
                value={client.id}
                onSelect={() => {
                  onChange(client.id === value ? '' : client.id);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value === client.id ? "opacity-100" : "opacity-0"
                  )}
                />
                {client.name}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
