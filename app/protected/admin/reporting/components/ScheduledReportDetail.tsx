'use client';

import { useState, useEffect } from 'react';
import { ReportSchedule, ReportExecution } from '../lib/types';
import { getExecutionHistory } from '../lib/scheduling';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Play, Edit, Trash2, AlertTriangle, Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';

interface ScheduledReportDetailProps {
  schedule: ReportSchedule;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onRun: () => void;
  isRunning: boolean;
}

export function ScheduledReportDetail({
  schedule,
  onClose,
  onEdit,
  onDelete,
  onRun,
  isRunning
}: ScheduledReportDetailProps) {
  const [executions, setExecutions] = useState<ReportExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchExecutionHistory();
  }, [schedule.id]);

  async function fetchExecutionHistory() {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/reports/schedules/${schedule.id}/executions`);
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to fetch execution history');
      }
      
      const data = await response.json();
      setExecutions(data.executions);
    } catch (err) {
      console.error('Error fetching execution history:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch execution history');
    } finally {
      setIsLoading(false);
    }
  }

  function formatFrequency(schedule: ReportSchedule): string {
    switch (schedule.frequency) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return `Weekly on ${days[schedule.dayOfWeek || 0]}`;
      case 'monthly':
        return `Monthly on day ${schedule.dayOfMonth}`;
      case 'quarterly':
        const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
        const quarterIndex = Math.floor((schedule.month || 0) / 3);
        return `Quarterly (${quarters[quarterIndex]})`;
      case 'yearly':
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        return `Yearly in ${months[schedule.month || 0]}`;
      default:
        return schedule.frequency;
    }
  }

  function formatDeliveryMethod(method: string): string {
    switch (method) {
      case 'email':
        return 'Email';
      case 'notification':
        return 'In-app Notification';
      case 'download':
        return 'Download Link';
      default:
        return method;
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-destructive" />;
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" className="mr-2" onClick={onClose}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle>{schedule.name}</CardTitle>
              <CardDescription>
                {schedule.reportName} ({schedule.reportCategory})
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRun}
              disabled={isRunning}
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Run Now
                </>
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button variant="destructive" size="sm" onClick={onDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="details">
          <TabsList>
            <TabsTrigger value="details">Schedule Details</TabsTrigger>
            <TabsTrigger value="history">Execution History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Schedule</h3>
                  <p className="text-base">{formatFrequency(schedule)}</p>
                  <p className="text-sm text-muted-foreground">at {schedule.time}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Next Run</h3>
                  <p className="text-base">
                    {schedule.nextRunAt.toLocaleString(undefined, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>

                {schedule.lastRunAt && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Last Run</h3>
                    <p className="text-base">
                      {schedule.lastRunAt.toLocaleString(undefined, {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                )}

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <div className="flex items-center mt-1">
                    <Switch
                      checked={schedule.enabled}
                      onCheckedChange={() => {}}
                      disabled
                    />
                    <span className="ml-2">
                      {schedule.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Delivery Method</h3>
                  <p className="text-base">{formatDeliveryMethod(schedule.delivery.method)}</p>
                </div>

                {schedule.delivery.method === 'email' && schedule.delivery.recipients && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Recipients</h3>
                    <ul className="list-disc list-inside text-sm">
                      {schedule.delivery.recipients.map((email, index) => (
                        <li key={index}>{email}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {schedule.delivery.subject && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Subject</h3>
                    <p className="text-sm">{schedule.delivery.subject}</p>
                  </div>
                )}

                {schedule.delivery.message && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Message</h3>
                    <p className="text-sm">{schedule.delivery.message}</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-4">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading execution history...</span>
              </div>
            ) : error ? (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : executions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No execution history found.</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Run the report to see execution history.
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Started At</TableHead>
                    <TableHead>Completed At</TableHead>
                    <TableHead>Delivery</TableHead>
                    <TableHead>Result</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {executions.map((execution) => (
                    <TableRow key={execution.id}>
                      <TableCell>
                        <div className="flex items-center">
                          {getStatusIcon(execution.status)}
                          <span className="ml-2 capitalize">{execution.status}</span>
                        </div>
                        {execution.error && (
                          <p className="text-xs text-destructive mt-1">{execution.error}</p>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(execution.startedAt).toLocaleString(undefined, {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </TableCell>
                      <TableCell>
                        {execution.completedAt
                          ? new Date(execution.completedAt).toLocaleString(undefined, {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          : '-'}
                      </TableCell>
                      <TableCell>
                        {execution.deliveryStatus ? (
                          <Badge
                            variant={execution.deliveryStatus.status === 'sent' ? 'outline' : 'destructive'}
                          >
                            {execution.deliveryStatus.status}
                          </Badge>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>
                        {execution.resultId ? (
                          <Button variant="link" className="p-0 h-auto">
                            View Result
                          </Button>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
