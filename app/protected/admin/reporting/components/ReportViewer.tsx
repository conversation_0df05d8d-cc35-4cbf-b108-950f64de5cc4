
'use client';

import { useState, useEffect } from 'react';
import { ReportRunner } from './ReportRunner';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CardContent } from '@/components/ui/card'; // Import CardContent
import { Loader2, AlertTriangle } from 'lucide-react';
import { ReportParams } from '../lib/types';

interface ReportViewerProps {
  report: {
    customReportResult: any;
    id: string;
    name: string;
    description: string;
    category: string;
    type?: string;
  };
  onClose: () => void;
  onEdit?: () => void;
}

export function ReportViewer({ report, onClose, onEdit }: ReportViewerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Force re-render when report changes
  const [key, setKey] = useState(Date.now());

  // Update key when report changes to force re-render
  useEffect(() => {
    console.log('REPORT FLOW - ReportViewer received updated report:', report);
    if (report.customReportResult) {
      console.log('REPORT FLOW - Report has customReportResult:', report.customReportResult);
      console.log('REPORT FLOW - Report customReportResult columns:', report.customReportResult.columns);
      console.log('REPORT FLOW - Report customReportResult data:', report.customReportResult.data);
    }

    // Force a complete re-render by updating the key
    setKey(Date.now());
  }, [report, JSON.stringify(report.customReportResult)]);

  // Create default parameters for the report
  const defaultParams: ReportParams = {
    filters: {
      dateRange: 'all-time',
      clientFilter: 'all',
      // Add any other default filters here
    }
  };

  // If the report is a client-specific report, adjust the parameters
  if (report.category === 'client' && report.id !== 'client-demographics') {
    defaultParams.filters.clientFilter = 'specific';
    // We'll let the report handle the case where clientId is not provided
  }

  // If we don't have a report type, use the ID as the type
  const reportType = report.type || report.id;

  // Check if this is a custom report with results already available
  if (reportType === 'custom' && report.customReportResult) {
    return (
      <CardContent className="flex-1 overflow-hidden p-0 h-full min-w-0">
        <ReportRunner
          key={key} // Add key to force re-render when report changes
          reportType={`custom-${report.id}`}
          reportName={report.name}
          parameters={defaultParams}
          onClose={onClose}
          onEdit={onEdit}
          isCustomReport={true}
          customReportResult={report.customReportResult as any}
        />
      </CardContent>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
        <p className="text-lg font-medium">Loading report...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="h-full">
      <ReportRunner
        key={key} // Add key to force re-render when report changes
        reportType={reportType}
        reportName={report.name}
        parameters={defaultParams}
        onClose={onClose}
        onEdit={reportType === 'custom' ? onEdit : undefined}
        isCustomReport={reportType === 'custom'}
      />
    </div>
  );
}
