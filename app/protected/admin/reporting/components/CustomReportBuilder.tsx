'use client';

import { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createClient } from '@/utils/supabase/client';
import { FieldSelector } from './custom-report/FieldSelector';
import { AdvancedFilterBuilder } from './custom-report/AdvancedFilterBuilder';
import { GroupingAndSorting } from './custom-report/GroupingAndSorting';
import { CalculationsBuilder } from './custom-report/CalculationsBuilder';
import { VisualizationBuilder } from './custom-report/VisualizationBuilder';
import { CustomReport, ReportField, FilterCondition, SortOption } from '../lib/types';
import { Loader2, Save, Star } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface CustomReportBuilderProps {
  initialReport?: CustomReport;
  onSave: (report: CustomReport) => void;
  onClose: () => void;
}

export function CustomReportBuilder({
  initialReport,
  onSave,
  onClose
}: CustomReportBuilderProps) {
  const [report, setReport] = useState<CustomReport>(initialReport || {
    id: '',
    name: 'New Custom Report',
    description: '',
    dataSource: '',
    fields: [],
    filters: [],
    groupBy: [],
    sortBy: [],
    calculations: [],
    visualizations: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: '',
    isTemplate: false,
    isFavorite: false,
  });

  const [availableFields, setAvailableFields] = useState<ReportField[]>([]);
  const [activeTab, setActiveTab] = useState('fields');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch available fields for the selected data source
  useEffect(() => {
    if (report.dataSource) {
      fetchAvailableFields(report.dataSource);
    }
  }, [report.dataSource]);

  async function fetchAvailableFields(dataSource: string) {
    setIsLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      // Query information_schema directly using raw SQL
      const { data: schemaData, error: schemaError } = await supabase
        .rpc('run_custom_query', {
          query_text: `
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = '${dataSource}'
            ORDER BY ordinal_position
          `
        });

      if (schemaError) {
        throw new Error(`Error fetching fields: ${schemaError.message}`);
      }

      // Log the data structure to understand what we're getting back
      console.log('Schema data:', schemaData);

      // Make sure we have an array to work with
      const columns = Array.isArray(schemaData) ? schemaData : [];

      if (columns.length === 0) {
        setAvailableFields([]);
        setError(`No columns found for table '${dataSource}'`);
        return;
      }

      // Transform columns to report fields
      const fields = columns.map((column: any) => ({
        id: `${dataSource}.${column.column_name}`,
        name: column.column_name,
        sourceField: column.column_name,
        displayName: formatFieldName(column.column_name),
        dataType: mapDataType(column.data_type),
        isVisible: true,
      }));

      setAvailableFields(fields);
    } catch (error) {
      console.error('Error fetching available fields:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch fields');
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to format field names
  function formatFieldName(name: string): string {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Helper function to map database types to report types
  function mapDataType(dbType: string): 'string' | 'number' | 'date' | 'boolean' {
    if (['integer', 'numeric', 'decimal', 'real', 'double precision', 'bigint'].includes(dbType.toLowerCase())) {
      return 'number';
    } else if (['timestamp', 'date', 'time', 'timestamp with time zone'].includes(dbType.toLowerCase())) {
      return 'date';
    } else if (['boolean'].includes(dbType.toLowerCase())) {
      return 'boolean';
    } else {
      return 'string';
    }
  }

  // Update report fields
  function updateFields(fields: ReportField[]) {
    setReport(prev => ({
      ...prev,
      fields
    }));
  }

  // Update report filters
  function updateFilters(filters: FilterCondition[]) {
    setReport(prev => ({
      ...prev,
      filters
    }));
  }

  // Update grouping
  function updateGrouping(groupBy: string[]) {
    setReport(prev => ({
      ...prev,
      groupBy
    }));
  }

  // Update sorting
  function updateSorting(sortBy: SortOption[]) {
    setReport(prev => ({
      ...prev,
      sortBy
    }));
  }

  // Update calculations
  function updateCalculations(calculations: any[]) {
    setReport(prev => ({
      ...prev,
      calculations
    }));
  }

  // Update visualizations
  function updateVisualizations(visualizations: any[]) {
    setReport(prev => ({
      ...prev,
      visualizations
    }));
  }

  // Toggle favorite status
  function toggleFavorite() {
    setReport(prev => ({
      ...prev,
      isFavorite: !prev.isFavorite
    }));
  }

  // Toggle template status
  function toggleTemplate() {
    setReport(prev => ({
      ...prev,
      isTemplate: !prev.isTemplate
    }));
  }



  // Save the report
  async function handleSave() {
    setIsSaving(true);
    setError(null);

    try {
      const supabase = createClient();
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError || !userData.user) {
        throw new Error('User not authenticated');
      }

      const updatedReport = {
        ...report,
        updatedAt: new Date(),
        createdBy: userData.user.id,
      };

      // If it's a new report, generate an ID
      if (!updatedReport.id) {
        updatedReport.id = crypto.randomUUID();
        updatedReport.createdAt = new Date();
      }

      onSave(updatedReport);
    } catch (error) {
      console.error('Error saving report:', error);
      setError(error instanceof Error ? error.message : 'Failed to save report');
    } finally {
      setIsSaving(false);
    }
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <Input
                value={report.name}
                onChange={(e) => setReport(prev => ({ ...prev, name: e.target.value }))}
                className="text-xl font-bold mb-2"
                placeholder="Report Name"
              />
              <Textarea
                value={report.description || ''}
                onChange={(e) => setReport(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Report description"
                className="resize-none h-20"
              />
            </div>
            <div className="flex items-center space-x-4 ml-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={report.isFavorite}
                  onCheckedChange={toggleFavorite}
                  id="favorite"
                />
                <Label htmlFor="favorite" className="flex items-center">
                  <Star className="h-4 w-4 mr-1" />
                  Favorite
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={report.isTemplate}
                  onCheckedChange={toggleTemplate}
                  id="template"
                />
                <Label htmlFor="template">Save as Template</Label>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <Label htmlFor="dataSource">Data Source</Label>
            <Select
              value={report.dataSource}
              onValueChange={(value) => setReport(prev => ({ ...prev, dataSource: value }))}
            >
              <SelectTrigger id="dataSource">
                <SelectValue placeholder="Select a data source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Select a data source</SelectItem>
                <SelectItem value="households">Households</SelectItem>
                <SelectItem value="assets">Assets</SelectItem>
                <SelectItem value="liabilities">Liabilities</SelectItem>
                <SelectItem value="income">Income</SelectItem>
                <SelectItem value="expenses">Expenses</SelectItem>
                <SelectItem value="goals">Goals</SelectItem>
                <SelectItem value="profiles">Profiles</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-10">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Loading fields...</span>
            </div>
          ) : (
            <>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4 grid grid-cols-5">
                  <TabsTrigger value="fields">Fields</TabsTrigger>
                  <TabsTrigger value="filters">Filters</TabsTrigger>
                  <TabsTrigger value="grouping">Grouping & Sorting</TabsTrigger>
                  <TabsTrigger value="calculations">Calculations</TabsTrigger>
                  <TabsTrigger value="visualization">Visualization</TabsTrigger>
                </TabsList>

                <TabsContent value="fields">
                  <FieldSelector
                    availableFields={availableFields}
                    selectedFields={report.fields}
                    onChange={updateFields}
                  />
                </TabsContent>

                <TabsContent value="filters">
                  <AdvancedFilterBuilder
                    fields={availableFields}
                    filters={report.filters}
                    onChange={updateFilters}
                  />
                </TabsContent>

                <TabsContent value="grouping">
                  <GroupingAndSorting
                    fields={availableFields}
                    groupBy={report.groupBy || []}
                    sortBy={report.sortBy || []}
                    onGroupingChange={updateGrouping}
                    onSortingChange={updateSorting}
                  />
                </TabsContent>

                <TabsContent value="calculations">
                  <CalculationsBuilder
                    fields={availableFields}
                    calculations={report.calculations || []}
                    onChange={updateCalculations}
                  />
                </TabsContent>

                <TabsContent value="visualization">
                  <VisualizationBuilder
                    fields={availableFields}
                    visualizations={report.visualizations || []}
                    onChange={updateVisualizations}
                  />
                </TabsContent>
              </Tabs>

              <div className="mt-6 flex justify-end space-x-2">
                <Button variant="outline" onClick={onClose}>Cancel</Button>
                <Button onClick={handleSave} disabled={isSaving} className="flex items-center">
                  {isSaving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Save className="h-4 w-4 mr-2" />
                  Save Report
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </DndProvider>
  );
}
