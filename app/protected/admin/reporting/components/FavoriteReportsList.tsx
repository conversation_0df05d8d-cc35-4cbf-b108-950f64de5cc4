'use client';

import { useState } from 'react';
import { CustomReport } from '../lib/types';
import { toggleFavoriteReport } from '../lib/custom-reports';
import { useReports } from '../context/ReportContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Play, Edit, Star, StarOff } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface FavoriteReportsListProps {
  onRunReport: (report: CustomReport) => void;
  onEditReport: (report: CustomReport) => void;
}

export function FavoriteReportsList({
  onRunReport,
  onEditReport
}: FavoriteReportsListProps) {
  const { reports: allReports, isLoading, error: contextError, refreshReports } = useReports();
  const [error, setError] = useState<string | null>(contextError);

  // Filter only favorite reports
  const reports = allReports.filter(report => report.isFavorite);

  // Handle removing from favorites
  async function handleRemoveFromFavorites(report: CustomReport) {
    try {
      await toggleFavoriteReport(report.id, false);
      await refreshReports(); // Refresh the reports list after removing from favorites
    } catch (err) {
      console.error('Error removing from favorites:', err);
      setError(err instanceof Error ? err.message : 'Failed to update favorite status');
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading favorite reports...</span>
      </div>
    );
  }

  if (reports.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <p className="text-muted-foreground">No favorite reports found.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Mark reports as favorites to see them here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {reports.map((report) => (
          <Card key={report.id} className="flex flex-col">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{report.name}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleRemoveFromFavorites(report)}
                >
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                {report.description || 'No description'}
              </p>
            </CardHeader>
            <CardContent className="flex-1">
              <div className="text-sm">
                <div className="flex justify-between mb-1">
                  <span className="text-muted-foreground">Data Source:</span>
                  <span>{report.dataSource}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Updated:</span>
                  <span>{new Date(report.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditReport(report)}
                  className="flex items-center"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  onClick={() => onRunReport(report)}
                  className="flex items-center"
                >
                  <Play className="h-4 w-4 mr-1" />
                  Run
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
