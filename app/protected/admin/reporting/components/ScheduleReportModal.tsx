'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { createClient } from '@/utils/supabase/client';
import { calculateNextRunDate } from '../lib/scheduling';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface ScheduleReportModalProps {
  reportId: string;
  reportName: string;
  isOpen: boolean;
  onClose: () => void;
  onSchedule: (scheduleId: string) => void;
}

export function ScheduleReportModal({
  reportId,
  reportName,
  isOpen,
  onClose,
  onSchedule
}: ScheduleReportModalProps) {
  const [name, setName] = useState(`${reportName} Schedule`);
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'>('monthly');
  const [dayOfWeek, setDayOfWeek] = useState<number>(1); // Monday
  const [dayOfMonth, setDayOfMonth] = useState<number>(1);
  const [month, setMonth] = useState<number>(0); // January
  const [time, setTime] = useState('09:00');
  const [deliveryMethod, setDeliveryMethod] = useState<'email' | 'notification' | 'download'>('email');
  const [recipients, setRecipients] = useState<string>('');
  const [subject, setSubject] = useState<string>(`${reportName} Report`);
  const [message, setMessage] = useState<string>(`Here is your scheduled ${reportName} report.`);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function handleSubmit() {
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate inputs
      if (!name.trim()) {
        throw new Error('Schedule name is required');
      }

      if (deliveryMethod === 'email' && !recipients.trim()) {
        throw new Error('At least one recipient email is required');
      }

      const supabase = createClient();
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError || !userData.user) {
        throw new Error('User not authenticated');
      }

      // Calculate next run date based on schedule
      const nextRunAt = calculateNextRunDate(frequency, dayOfWeek, dayOfMonth, month, time);

      // Create schedule in database
      const { data, error } = await supabase
        .from('report_schedules')
        .insert({
          report_id: String(reportId), // Ensure reportId is a string
          name,
          frequency,
          day_of_week: frequency === 'weekly' ? dayOfWeek : null,
          day_of_month: ['monthly', 'quarterly', 'yearly'].includes(frequency) ? dayOfMonth : null,
          month: ['quarterly', 'yearly'].includes(frequency) ? month : null,
          time,
          next_run_at: nextRunAt,
          enabled: true,
          delivery: {
            method: deliveryMethod,
            recipients: deliveryMethod === 'email' ? recipients.split(',').map(email => email.trim()) : [],
            subject: deliveryMethod === 'email' ? subject : undefined,
            message: ['email', 'notification'].includes(deliveryMethod) ? message : undefined,
          },
          user_id: userData.user.id,
        })
        .select('id')
        .single();

      if (error) {
        throw new Error(`Error creating schedule: ${error.message}`);
      }

      onSchedule(data.id);
      onClose();
    } catch (err) {
      console.error('Error scheduling report:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while scheduling the report');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Schedule Report: {reportName}</DialogTitle>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Schedule Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label>Frequency</Label>
            <Select value={frequency} onValueChange={(value) => setFrequency(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {frequency === 'weekly' && (
            <div className="grid gap-2">
              <Label>Day of Week</Label>
              <Select value={dayOfWeek.toString()} onValueChange={(value) => setDayOfWeek(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Sunday</SelectItem>
                  <SelectItem value="1">Monday</SelectItem>
                  <SelectItem value="2">Tuesday</SelectItem>
                  <SelectItem value="3">Wednesday</SelectItem>
                  <SelectItem value="4">Thursday</SelectItem>
                  <SelectItem value="5">Friday</SelectItem>
                  <SelectItem value="6">Saturday</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {['monthly', 'quarterly', 'yearly'].includes(frequency) && (
            <div className="grid gap-2">
              <Label>Day of Month</Label>
              <Select value={dayOfMonth.toString()} onValueChange={(value) => setDayOfMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 31 }, (_, i) => (
                    <SelectItem key={i} value={(i + 1).toString()}>
                      {i + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {['quarterly', 'yearly'].includes(frequency) && (
            <div className="grid gap-2">
              <Label>Month</Label>
              <Select value={month.toString()} onValueChange={(value) => setMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">January</SelectItem>
                  <SelectItem value="1">February</SelectItem>
                  <SelectItem value="2">March</SelectItem>
                  <SelectItem value="3">April</SelectItem>
                  <SelectItem value="4">May</SelectItem>
                  <SelectItem value="5">June</SelectItem>
                  <SelectItem value="6">July</SelectItem>
                  <SelectItem value="7">August</SelectItem>
                  <SelectItem value="8">September</SelectItem>
                  <SelectItem value="9">October</SelectItem>
                  <SelectItem value="10">November</SelectItem>
                  <SelectItem value="11">December</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="grid gap-2">
            <Label htmlFor="time">Time (24-hour format)</Label>
            <Input
              id="time"
              type="time"
              value={time}
              onChange={(e) => setTime(e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label>Delivery Method</Label>
            <RadioGroup value={deliveryMethod} onValueChange={(value) => setDeliveryMethod(value as any)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="email" id="email" />
                <Label htmlFor="email">Email</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="notification" id="notification" />
                <Label htmlFor="notification">In-app Notification</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="download" id="download" />
                <Label htmlFor="download">Download Link</Label>
              </div>
            </RadioGroup>
          </div>

          {deliveryMethod === 'email' && (
            <>
              <div className="grid gap-2">
                <Label htmlFor="recipients">Recipients (comma-separated)</Label>
                <Input
                  id="recipients"
                  value={recipients}
                  onChange={(e) => setRecipients(e.target.value)}
                  placeholder="<EMAIL>, <EMAIL>"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="subject">Email Subject</Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="message">Email Message</Label>
                <Input
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                />
              </div>
            </>
          )}

          {deliveryMethod === 'notification' && (
            <div className="grid gap-2">
              <Label htmlFor="notification-message">Notification Message</Label>
              <Input
                id="notification-message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Scheduling...' : 'Schedule Report'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
