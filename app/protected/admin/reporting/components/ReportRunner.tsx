'use client';

import { useState, useEffect, useCallback } from 'react';
import { ReportResultTable } from './ReportResultTable';
import { ReportResultChart } from './ReportResultChart';
import { ClientSelector } from './ClientSelector';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, FileSpreadsheet, FileText, Save, AlertTriangle, Filter, Edit } from 'lucide-react';
import { fetchReportData } from '../lib/data-fetching';
import { exportToExcel } from '../lib/export/excel';
import { exportToPDF } from '../lib/export/pdf';
import { saveReportConfig } from '../lib/report-management';
import { ReportParams, ReportData, ReportType } from '../lib/types';
import { Label } from '@/components/ui/label';

interface ReportRunnerProps {
  reportType: string;
  reportName: string;
  parameters: ReportParams;
  onSave?: (result: ReportData) => void;
  onClose?: () => void;
  onEdit?: () => void;
  customReportResult?: ReportData;
  isCustomReport?: boolean;
}

export function ReportRunner({
  reportType,
  reportName,
  parameters,
  onSave,
  onClose,
  onEdit,
  customReportResult,
  isCustomReport = false
}: ReportRunnerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ReportData | null>(customReportResult || null);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'chart' | 'card'>('table');
  const [selectedClientId, setSelectedClientId] = useState<string | undefined>(
    parameters.filters.clientId as string | undefined
  );
  const [currentParams, setCurrentParams] = useState<ReportParams>(parameters);

  // Check if this report requires a client selection
  const requiresClientSelection = parameters.filters.clientFilter === 'specific';

  const runReport = useCallback(async () => {
    // If we already have a custom report result, use it
    if (customReportResult) {
      setResult(customReportResult);

      // Set initial view mode based on recommended chart type
      if (customReportResult.recommendedChartType === 'card') {
        setViewMode('card');
      } else if (customReportResult.chartData) {
        setViewMode('chart');
      }

      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await fetchReportData(reportType, currentParams);
      setResult(data);

      // Set initial view mode based on recommended chart type
      if (data.recommendedChartType === 'card') {
        setViewMode('card');
      } else if (data.chartData) {
        setViewMode('chart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while generating the report');
    } finally {
      setIsLoading(false);
    }
  }, [customReportResult, reportType, currentParams]);

  // Handle client selection change
  function handleClientChange(clientId: string) {
    setSelectedClientId(clientId);

    // Update parameters with the new client ID
    const newParams = {
      ...currentParams,
      filters: {
        ...currentParams.filters,
        clientId: clientId || undefined
      }
    };

    setCurrentParams(newParams);
  }

  function handleExport(format: 'excel' | 'pdf') {
    if (!result) return;

    if (format === 'excel') {
      exportToExcel(result.data, `${reportType}-report`, result.columns);
    } else {
      const reportElement = document.getElementById('report-result');
      if (reportElement) {
        exportToPDF(reportElement, `${reportType}-report`);
      }
    }
  }

  function handleSave() {
    if (!result || !onSave) return;
    onSave(result);
  }

  // Run report when parameters change
  useEffect(() => {
    // Skip if we have a custom report result
    if (!customReportResult) {
      runReport();
    }
  }, [reportType, currentParams, customReportResult, runReport]);

  // Run report when client selection changes
  useEffect(() => {
    // Skip if we have a custom report result
    if (!customReportResult && requiresClientSelection) {
      runReport();
    }
  }, [selectedClientId, customReportResult, requiresClientSelection, runReport]);

  // Update result and view mode when custom report result changes
  useEffect(() => {
    if (customReportResult) {
      console.log('REPORT FLOW - ReportRunner received customReportResult:', customReportResult);
      console.log('REPORT FLOW - customReportResult fields:', customReportResult.columns);
      console.log('REPORT FLOW - customReportResult data:', customReportResult.data);

      // Update the result state with the new customReportResult
      setResult(customReportResult);

      // Set view mode based on recommended chart type
      if (customReportResult.recommendedChartType === 'card') {
        setViewMode('card');
      } else if (customReportResult.chartData) {
        setViewMode('chart');
      }
    }
  }, [customReportResult]);

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
          <p className="text-lg font-medium">Generating report...</p>
          <p className="text-sm text-muted-foreground mt-2">This may take a moment</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!result) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-10">
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full flex flex-col w-[calc(100vw-230px)]">
      <CardHeader className="flex-shrink-0">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{reportName}</CardTitle> {/* Use reportName prop */}
            <CardDescription>
              Generated on {result.runAt.toLocaleDateString()} at {result.runAt.toLocaleTimeString()}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('excel')}>
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Export Excel
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
              <FileText className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
            {isCustomReport && onEdit && (
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Report
              </Button>
            )}
            {onSave && (
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="mr-2 h-4 w-4" />
                Save Report
              </Button>
            )}
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-x-auto flex flex-col">
        {requiresClientSelection && (
          <div className="mb-6 flex-shrink-0">
            <div className="flex flex-col space-y-2 mb-4">
              <Label htmlFor="client-selector">Select Client</Label>
              <ClientSelector
                value={selectedClientId}
                onChange={handleClientChange}
              />
            </div>
            {!selectedClientId && (
              <Alert variant="default" className="mt-2 bg-yellow-50 text-yellow-800 border-yellow-200">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Client Required</AlertTitle>
                <AlertDescription>
                  Please select a client to view this report.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as 'table' | 'chart' | 'card')} className="flex-1 flex flex-col">
          <TabsList className="flex-shrink-0">
            <TabsTrigger value="table">Table</TabsTrigger>
            <TabsTrigger value="chart" disabled={!result.chartData}>Chart</TabsTrigger>
            <TabsTrigger value="card" disabled={!result.summary}>Summary</TabsTrigger>
          </TabsList>

          <div id="report-result" className="mt-4 flex-1 overflow-hidden">
            <TabsContent value="table" className="h-full">
              <ReportResultTable
                columns={result.columns}
                data={result.data}
              />
            </TabsContent>

            <TabsContent value="chart" className="h-full overflow-hidden">
              {result.chartData ? (
                <ReportResultChart
                  reportData={result}
                  type={result.recommendedChartType}
                />
              ) : (
                <p className="text-muted-foreground text-center py-10">No chart data available</p>
              )}
            </TabsContent>

            <TabsContent value="card" className="h-full overflow-hidden">
              {result.summary ? (
                <ReportResultChart
                  reportData={result}
                  type="card"
                />
              ) : (
                <p className="text-muted-foreground text-center py-10">No summary data available</p>
              )}
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>

      <CardFooter className="border-t pt-4 flex-shrink-0">
        <p className="text-sm text-muted-foreground">
          {result.data.length} records found
        </p>
      </CardFooter>
    </Card>
  );
}
