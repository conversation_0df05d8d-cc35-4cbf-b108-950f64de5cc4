'use client';

import { useState, useEffect } from 'react';
import { ReportSchedule } from '../lib/types';
import { getSchedules, setScheduleEnabled, deleteSchedule } from '../lib/scheduling';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Loader2, MoreHorizontal, Play, Calendar, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScheduledReportDetail } from './ScheduledReportDetail';
import { ScheduleReportModal } from './ScheduleReportModal';

export function ScheduledReportsList() {
  const [schedules, setSchedules] = useState<ReportSchedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSchedule, setSelectedSchedule] = useState<ReportSchedule | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isRunning, setIsRunning] = useState<Record<string, boolean>>({});

  // Fetch schedules on component mount
  useEffect(() => {
    fetchSchedules();
  }, []);

  async function fetchSchedules() {
    setIsLoading(true);
    setError(null);

    try {
      // Try to get schedules using the utility function
      const data = await getSchedules();
      setSchedules(data);
    } catch (err) {
      console.error('Error fetching schedules:', err);

      // If that fails, try to get schedules directly from the API
      try {
        const response = await fetch('/api/reports/schedules');

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        if (data.schedules) {
          setSchedules(data.schedules);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (apiErr) {
        console.error('Error fetching schedules from API:', apiErr);
        setError(
          err instanceof Error
            ? `${err.message}. Please try refreshing the page.`
            : 'Failed to fetch schedules. Please try refreshing the page.'
        );
      }
    } finally {
      setIsLoading(false);
    }
  }

  async function handleToggleEnabled(scheduleId: string, enabled: boolean) {
    try {
      await setScheduleEnabled(scheduleId, enabled);

      // Update the local state
      setSchedules(prev => prev.map(schedule =>
        schedule.id === scheduleId ? { ...schedule, enabled } : schedule
      ));
    } catch (err) {
      console.error('Error toggling schedule:', err);
      setError(err instanceof Error ? err.message : 'Failed to update schedule');
    }
  }

  async function handleDeleteSchedule(scheduleId: string) {
    if (!confirm('Are you sure you want to delete this schedule?')) {
      return;
    }

    try {
      await deleteSchedule(scheduleId);

      // Update the local state
      setSchedules(prev => prev.filter(schedule => schedule.id !== scheduleId));
    } catch (err) {
      console.error('Error deleting schedule:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete schedule');
    }
  }

  async function handleRunSchedule(scheduleId: string) {
    setIsRunning(prev => ({ ...prev, [scheduleId]: true }));

    try {
      const response = await fetch(`/api/reports/schedules/${scheduleId}/run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ deliver: true })
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to run schedule');
      }

      // Refresh the schedules to get the updated last run time
      fetchSchedules();
    } catch (err) {
      console.error('Error running schedule:', err);
      setError(err instanceof Error ? err.message : 'Failed to run schedule');
    } finally {
      setIsRunning(prev => ({ ...prev, [scheduleId]: false }));
    }
  }

  function handleViewSchedule(schedule: ReportSchedule) {
    setSelectedSchedule(schedule);
  }

  function handleEditSchedule(schedule: ReportSchedule) {
    setSelectedSchedule(schedule);
    setIsEditModalOpen(true);
  }

  function handleCloseDetail() {
    setSelectedSchedule(null);
  }

  function handleCloseEditModal() {
    setIsEditModalOpen(false);
  }

  function handleScheduleUpdated() {
    setIsEditModalOpen(false);
    fetchSchedules();
  }

  function formatFrequency(schedule: ReportSchedule): string {
    switch (schedule.frequency) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return `Weekly on ${days[schedule.dayOfWeek || 0]}`;
      case 'monthly':
        return `Monthly on day ${schedule.dayOfMonth}`;
      case 'quarterly':
        const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
        const quarterIndex = Math.floor((schedule.month || 0) / 3);
        return `Quarterly (${quarters[quarterIndex]})`;
      case 'yearly':
        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        return `Yearly in ${months[schedule.month || 0]}`;
      default:
        return schedule.frequency;
    }
  }

  function formatDeliveryMethod(method: string): string {
    switch (method) {
      case 'email':
        return 'Email';
      case 'notification':
        return 'In-app Notification';
      case 'download':
        return 'Download Link';
      default:
        return method;
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading schedules...</span>
      </div>
    );
  }

  if (selectedSchedule) {
    return (
      <ScheduledReportDetail
        schedule={selectedSchedule}
        onClose={handleCloseDetail}
        onEdit={() => handleEditSchedule(selectedSchedule)}
        onDelete={() => handleDeleteSchedule(selectedSchedule.id)}
        onRun={() => handleRunSchedule(selectedSchedule.id)}
        isRunning={isRunning[selectedSchedule.id] || false}
      />
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Scheduled Reports</CardTitle>
        <CardDescription>Manage your scheduled reports</CardDescription>
      </CardHeader>

      {error && (
        <CardContent className="pt-0 pb-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      )}

      <CardContent>
        {schedules.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No scheduled reports found.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Create a report and use the schedule option to set up automatic report generation.
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Report</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Next Run</TableHead>
                <TableHead>Delivery</TableHead>
                <TableHead>Enabled</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewSchedule(schedule)}
                    >
                      {schedule.name}
                    </Button>
                  </TableCell>
                  <TableCell>{schedule.reportName}</TableCell>
                  <TableCell>{formatFrequency(schedule)}</TableCell>
                  <TableCell>
                    {schedule.nextRunAt.toLocaleString(undefined, {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {formatDeliveryMethod(schedule.delivery.method)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={schedule.enabled}
                      onCheckedChange={(checked) => handleToggleEnabled(schedule.id, checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewSchedule(schedule)}>
                          <Calendar className="mr-2 h-4 w-4" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleRunSchedule(schedule.id)}>
                          <Play className="mr-2 h-4 w-4" />
                          <span>Run Now</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditSchedule(schedule)}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => handleDeleteSchedule(schedule.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {isEditModalOpen && selectedSchedule && (() => {
        // Create a properly typed variable to satisfy TypeScript
        const schedule: ReportSchedule = selectedSchedule;
        return (
          <ScheduleReportModal
            reportId={schedule.reportId}
            reportName={schedule.reportName ?? ''}
            isOpen={isEditModalOpen}
            onClose={handleCloseEditModal}
            onSchedule={handleScheduleUpdated}
          />
        );
      })()}
    </Card>
  );
}
