'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, X, ArrowUp, ArrowDown } from 'lucide-react';
import { ReportField, FilterCondition, SortOption } from '../../lib/types';

interface SortingAndFilteringProps {
  fields: ReportField[];
  filters: FilterCondition[];
  sortBy: SortOption[];
  onFiltersChange: (filters: FilterCondition[]) => void;
  onSortingChange: (sortBy: SortOption[]) => void;
}

export function SortingAndFiltering({
  fields,
  filters,
  sortBy,
  onFiltersChange,
  onSortingChange
}: SortingAndFilteringProps) {
  // Add a filter condition
  function addFilterCondition() {
    if (fields.length === 0) return;
    
    const newFilter: FilterCondition = {
      id: crypto.randomUUID(),
      field: fields[0].id,
      operator: 'equals',
      value: '',
      logic: 'and'
    };
    
    onFiltersChange([...filters, newFilter]);
  }

  // Remove a filter condition
  function removeFilterCondition(id: string) {
    onFiltersChange(filters.filter(filter => filter.id !== id));
  }

  // Update a filter condition
  function updateFilterCondition(id: string, property: string, value: any) {
    onFiltersChange(
      filters.map(filter => 
        filter.id === id 
          ? { ...filter, [property]: value } 
          : filter
      )
    );
  }

  // Add a sort option
  function addSortOption() {
    if (fields.length === 0) return;

    const newSort: SortOption = {
      field: fields[0].id,
      direction: 'asc'
    };

    onSortingChange([...sortBy, newSort]);
  }

  // Remove a sort option
  function removeSortOption(index: number) {
    onSortingChange(sortBy.filter((_, i) => i !== index));
  }

  // Update a sort option
  function updateSortOption(index: number, field: string, direction: 'asc' | 'desc') {
    const newSortBy = [...sortBy];
    newSortBy[index] = { field, direction };
    onSortingChange(newSortBy);
  }

  // Toggle sort direction
  function toggleSortDirection(index: number) {
    const newSortBy = [...sortBy];
    newSortBy[index] = { 
      ...newSortBy[index], 
      direction: newSortBy[index].direction === 'asc' ? 'desc' : 'asc' 
    };
    onSortingChange(newSortBy);
  }

  // Get field name by ID
  function getFieldName(fieldId: string): string {
    const field = fields.find(f => f.id === fieldId);
    return field ? field.displayName : fieldId;
  }

  // Get operators based on field type
  function getOperatorsForField(fieldId: string): { value: string, label: string }[] {
    const field = fields.find(f => f.id === fieldId);
    
    if (!field) return getOperatorsForType('string');
    
    return getOperatorsForType(field.dataType);
  }

  // Get operators based on field type
  function getOperatorsForType(type: string): { value: string, label: string }[] {
    switch (type) {
      case 'number':
        return [
          { value: 'equals', label: 'Equals' },
          { value: 'notEquals', label: 'Not Equals' },
          { value: 'greaterThan', label: 'Greater Than' },
          { value: 'lessThan', label: 'Less Than' }
        ];
      case 'date':
        return [
          { value: 'equals', label: 'Equals' },
          { value: 'notEquals', label: 'Not Equals' },
          { value: 'greaterThan', label: 'After' },
          { value: 'lessThan', label: 'Before' }
        ];
      case 'boolean':
        return [
          { value: 'equals', label: 'Equals' },
          { value: 'notEquals', label: 'Not Equals' }
        ];
      default: // string
        return [
          { value: 'equals', label: 'Equals' },
          { value: 'notEquals', label: 'Not Equals' },
          { value: 'contains', label: 'Contains' },
          { value: 'startsWith', label: 'Starts With' },
          { value: 'endsWith', label: 'Ends With' }
        ];
    }
  }

  return (
    <div className="space-y-6">
      {/* Sorting section */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Sort By</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addSortOption}
            className="flex items-center gap-1"
            disabled={fields.length === 0}
          >
            <Plus className="h-4 w-4" />
            Add Sort
          </Button>
        </CardHeader>
        <CardContent>
          {sortBy.length === 0 ? (
            <p className="text-sm text-muted-foreground">No sorting applied</p>
          ) : (
            <div className="space-y-2">
              {sortBy.map((sort, index) => (
                <div key={index} className="flex items-center gap-2 border rounded-md p-2">
                  <div className="flex-1">
                    <Select
                      value={sort.field}
                      onValueChange={(field) => updateSortOption(index, field, sort.direction)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        {fields.map(field => (
                          <SelectItem key={field.id} value={field.id}>
                            {field.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSortDirection(index)}
                    className="flex items-center gap-1 min-w-[100px]"
                  >
                    {sort.direction === 'asc' ? (
                      <>
                        <ArrowUp className="h-4 w-4" />
                        Ascending
                      </>
                    ) : (
                      <>
                        <ArrowDown className="h-4 w-4" />
                        Descending
                      </>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSortOption(index)}
                    className="p-0 h-9 w-9 text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filtering section */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Filter Conditions</CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={addFilterCondition}
            className="flex items-center gap-1"
            disabled={fields.length === 0}
          >
            <Plus className="h-4 w-4" />
            Add Filter
          </Button>
        </CardHeader>
        <CardContent>
          {filters.length === 0 ? (
            <p className="text-sm text-muted-foreground">No filter conditions applied</p>
          ) : (
            <div className="space-y-2">
              {filters.map((filter) => (
                <div key={filter.id} className="border rounded-md p-3 space-y-3">
                  <div className="grid grid-cols-12 gap-2">
                    {/* Field selector */}
                    <div className="col-span-5">
                      <Label htmlFor={`field-${filter.id}`} className="text-xs">Field</Label>
                      <Select
                        value={filter.field}
                        onValueChange={(value) => updateFilterCondition(filter.id, 'field', value)}
                      >
                        <SelectTrigger id={`field-${filter.id}`}>
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {fields.map(field => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.displayName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Operator selector */}
                    <div className="col-span-3">
                      <Label htmlFor={`operator-${filter.id}`} className="text-xs">Operator</Label>
                      <Select
                        value={filter.operator}
                        onValueChange={(value) => updateFilterCondition(filter.id, 'operator', value)}
                      >
                        <SelectTrigger id={`operator-${filter.id}`}>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {getOperatorsForField(filter.field).map(op => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Value input */}
                    <div className="col-span-3">
                      <Label htmlFor={`value-${filter.id}`} className="text-xs">Value</Label>
                      <Input
                        id={`value-${filter.id}`}
                        value={filter.value || ''}
                        onChange={(e) => updateFilterCondition(filter.id, 'value', e.target.value)}
                        placeholder="Enter value"
                      />
                    </div>
                    
                    {/* Remove button */}
                    <div className="col-span-1 flex items-end justify-end">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => removeFilterCondition(filter.id)}
                        className="h-10 w-10 p-0 text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
