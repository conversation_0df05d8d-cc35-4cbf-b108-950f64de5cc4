'use client';

import { useState } from 'react';
import { ReportField, SortOption } from '../../lib/types';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowUpDown, ArrowUp, ArrowDown, Plus, X } from 'lucide-react';

interface GroupingAndSortingProps {
  fields: ReportField[];
  groupBy: string[];
  sortBy: SortOption[];
  onGroupingChange: (groupBy: string[]) => void;
  onSortingChange: (sortBy: SortOption[]) => void;
}

export function GroupingAndSorting({
  fields,
  groupBy,
  sortBy,
  onGroupingChange,
  onSortingChange
}: GroupingAndSortingProps) {
  // Add a field to group by
  function addGroupBy(fieldId: string) {
    if (groupBy.includes(fieldId)) return;
    onGroupingChange([...groupBy, fieldId]);
  }

  // Remove a field from group by
  function removeGroupBy(fieldId: string) {
    onGroupingChange(groupBy.filter(id => id !== fieldId));
  }

  // Add a sort option
  function addSortOption() {
    if (fields.length === 0) return;

    const newSort: SortOption = {
      field: fields[0].id,
      direction: 'asc'
    };

    onSortingChange([...sortBy, newSort]);
  }

  // Remove a sort option
  function removeSortOption(index: number) {
    onSortingChange(sortBy.filter((_, i) => i !== index));
  }

  // Update a sort option
  function updateSortOption(index: number, field: string, direction: 'asc' | 'desc') {
    const newSortBy = [...sortBy];
    newSortBy[index] = { field, direction };
    onSortingChange(newSortBy);
  }

  // Get field name by ID
  function getFieldName(fieldId: string): string {
    const field = fields.find(f => f.id === fieldId);
    return field ? field.displayName : fieldId;
  }

  // Filter fields that can be used for grouping (typically non-numeric fields)
  const groupableFields = fields.filter(field =>
    field.dataType === 'string' || field.dataType === 'date' || field.dataType === 'boolean'
  );

  return (
    <div className="space-y-6">
      {/* Grouping section */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Group By</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {groupBy.length === 0 ? (
                <p className="text-sm text-muted-foreground">No grouping applied</p>
              ) : (
                groupBy.map(fieldId => (
                  <Badge key={fieldId} variant="secondary" className="flex items-center gap-1">
                    {getFieldName(fieldId)}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeGroupBy(fieldId)}
                      className="h-4 w-4 p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))
              )}
            </div>

            <div className="flex items-end gap-2">
              <div className="flex-1">
                <Select
                  value="select-field"
                  onValueChange={addGroupBy}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Add field to group by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="select-field" disabled>
                      Select a field to group by
                    </SelectItem>
                    {groupableFields.map(field => (
                      <SelectItem
                        key={field.id}
                        value={field.id}
                        disabled={groupBy.includes(field.id)}
                      >
                        {field.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sorting section */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Sort By</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={addSortOption}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Sort
          </Button>
        </CardHeader>
        <CardContent>
          {sortBy.length === 0 ? (
            <p className="text-sm text-muted-foreground">No sorting applied</p>
          ) : (
            <div className="space-y-2">
              {sortBy.map((sort, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="flex-1">
                    <Select
                      value={sort.field}
                      onValueChange={(field) => updateSortOption(index, field, sort.direction)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        {fields.map(field => (
                          <SelectItem key={field.id} value={field.id}>
                            {field.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateSortOption(
                      index,
                      sort.field,
                      sort.direction === 'asc' ? 'desc' : 'asc'
                    )}
                    className="flex items-center gap-1 min-w-[100px]"
                  >
                    {sort.direction === 'asc' ? (
                      <>
                        <ArrowUp className="h-4 w-4" />
                        Ascending
                      </>
                    ) : (
                      <>
                        <ArrowDown className="h-4 w-4" />
                        Descending
                      </>
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSortOption(index)}
                    className="p-0 h-9 w-9 text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
