'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Search, Check } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ReportField } from '../../lib/types';

interface TableSelectorProps {
  dataSource: string;
  selectedFields: ReportField[];
  onDataSourceChange: (dataSource: string) => void;
  onSelectedFieldsChange: (fields: ReportField[]) => void;
}

export function TableSelector({
  dataSource,
  selectedFields,
  onDataSourceChange,
  onSelectedFieldsChange
}: TableSelectorProps) {
  // Log initial props for debugging
  console.log('TABLE SELECTOR - INITIAL PROPS:', { dataSource, selectedFields });
  const [searchTerm, setSearchTerm] = useState('');
  const [availableFields, setAvailableFields] = useState<ReportField[]>([]);

  // Available tables in the database
  const availableTables = [
    'households',
    'assets',
    'income',
    'expenses',
    'liabilities',
    'goals',
    'tasks',
    'interactions',
    'recommendations'
  ];

  // Common fields for each table with their data types
  const tableFieldMap: Record<string, { name: string, type: 'string' | 'number' | 'date' | 'boolean' }[]> = {
    'households': [
      { name: 'id', type: 'string' },
      { name: 'householdName', type: 'string' },
      { name: 'members', type: 'string' },
      { name: 'user_id', type: 'string' },
      { name: 'address', type: 'string' },
      { name: 'phone', type: 'string' },
      { name: 'email', type: 'string' },
      { name: 'occupation', type: 'string' },
      { name: 'employer', type: 'string' },
      { name: 'marital_status', type: 'string' },
      { name: 'date_of_birth', type: 'date' },
      { name: 'tax_file_number', type: 'string' },
      { name: 'notes', type: 'string' },
      { name: 'street', type: 'string' },
      { name: 'city', type: 'string' },
      { name: 'state', type: 'string' },
      { name: 'zip_code', type: 'string' },
      { name: 'country', type: 'string' },
      { name: 'property_type', type: 'string' },
      { name: 'preferred_contact', type: 'string' },
      { name: 'best_time_to_call', type: 'string' },
      { name: 'alternative_contact', type: 'string' },
      { name: 'investment_strategy', type: 'string' },
      { name: 'risk_tolerance', type: 'string' },
      { name: 'primary_advisor', type: 'string' },
      { name: 'last_review', type: 'date' },
      { name: 'next_review', type: 'date' },
      { name: 'additional_info', type: 'string' },
      { name: 'org_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'assets': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'type', type: 'string' },
      { name: 'value', type: 'number' },
      { name: 'details', type: 'string' },
      { name: 'property_type', type: 'string' },
      { name: 'rental_income', type: 'number' },
      { name: 'provider', type: 'string' },
      { name: 'linked_income_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'income': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'source', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'frequency', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'income_type', type: 'string' },
      { name: 'linked_asset_id', type: 'string' },
      { name: 'member_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'expenses': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'frequency', type: 'string' },
      { name: 'category', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'linked_liability_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'liabilities': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'interest_rate', type: 'number' },
      { name: 'lender', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'linked_asset_id', type: 'string' },
      { name: 'linked_expense_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'goals': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'target_date', type: 'date' },
      { name: 'target_amount', type: 'number' },
      { name: 'current_amount', type: 'number' },
      { name: 'status', type: 'string' },
      { name: 'priority', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'tasks': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'due_date', type: 'date' },
      { name: 'status', type: 'string' },
      { name: 'priority', type: 'string' },
      { name: 'assigned_to', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'interactions': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'content', type: 'string' },
      { name: 'date', type: 'date' },
      { name: 'type', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'recommendations': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'status', type: 'string' },
      { name: 'financial_impact', type: 'number' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ]
  };

  // Track initialization
  const isInitialized = useRef(false);

  // First, handle the initial loading of fields
  useEffect(() => {
    // Only run this once on component mount
    if (isInitialized.current) return;

    console.log('TABLE SELECTOR - INITIALIZING WITH FIELDS:', selectedFields);
    isInitialized.current = true;

    // If we have selected fields on mount, make sure they're all available
    if (selectedFields.length > 0) {
      const fields: ReportField[] = [];

      // Add all selected fields to ensure they're preserved
      selectedFields.forEach(field => {
        console.log(`TABLE SELECTOR - PRESERVING INITIAL FIELD: ${field.name}`, field);
        fields.push({...field}); // Make a copy to avoid reference issues
      });

      // Add any fields from the current table that aren't already in the selected fields
      if (dataSource && tableFieldMap[dataSource]) {
        tableFieldMap[dataSource].forEach(field => {
          // Check if this field is already in fields array
          const existingField = fields.find(f =>
            f.sourceField === field.name ||
            f.name === field.name ||
            f.id === `${dataSource}_${field.name}`
          );

          if (!existingField) {
            // If the field doesn't exist in fields, create a new one
            const newField = {
              id: `${dataSource}_${field.name}`,
              name: field.name,
              sourceField: field.name,
              displayName: formatFieldName(field.name),
              dataType: field.type,
              isVisible: true,
              width: 150
            };
            console.log(`TABLE SELECTOR - ADDING INITIAL FIELD FROM TABLE: ${field.name}`, newField);
            fields.push(newField);
          }
        });
      }

      console.log('TABLE SELECTOR - INITIAL AVAILABLE FIELDS:', fields);
      setAvailableFields(fields);
    }
  }, []);

  // Then handle changes to dataSource
  useEffect(() => {
    if (!dataSource || !isInitialized.current) return;

    // Log for debugging
    console.log('TABLE SELECTOR - DATA SOURCE CHANGED:', dataSource);

    const fields: ReportField[] = [];

    // First, add all selected fields to ensure they're preserved
    selectedFields.forEach(field => {
      console.log(`TABLE SELECTOR - PRESERVING SELECTED FIELD: ${field.name}`, field);
      fields.push({...field}); // Make a copy to avoid reference issues
    });

    // Then add any fields from the current table that aren't already in the selected fields
    if (tableFieldMap[dataSource]) {
      tableFieldMap[dataSource].forEach(field => {
        // Check if this field is already in fields array
        const existingField = fields.find(f =>
          f.sourceField === field.name ||
          f.name === field.name ||
          f.id === `${dataSource}_${field.name}`
        );

        if (!existingField) {
          // If the field doesn't exist in fields, create a new one
          const newField = {
            id: `${dataSource}_${field.name}`,
            name: field.name,
            sourceField: field.name,
            displayName: formatFieldName(field.name),
            dataType: field.type,
            isVisible: true,
            width: 150
          };
          console.log(`TABLE SELECTOR - ADDING NEW FIELD FROM CURRENT TABLE: ${field.name}`, newField);
          fields.push(newField);
        }
      });
    }

    console.log('TABLE SELECTOR - FINAL AVAILABLE FIELDS:', fields);
    setAvailableFields(fields);
  }, [dataSource]);

  // Helper function to format field names for display
  function formatFieldName(name: string): string {
    return name
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/\b\w/g, c => c.toUpperCase());
  }

  // Toggle field selection
  function toggleFieldSelection(field: ReportField) {
    const isSelected = selectedFields.some(f => f.id === field.id);

    if (isSelected) {
      // Remove field
      onSelectedFieldsChange(selectedFields.filter(f => f.id !== field.id));
    } else {
      // Add field
      onSelectedFieldsChange([...selectedFields, field]);
    }
  }

  // Identify fields from other tables
  const otherTableFields = selectedFields.filter(field => {
    // Check if this field is from a different table
    if (!field.sourceField) return false;

    // If sourceField contains a dot, it's from another table
    if (field.sourceField.includes('.')) {
      const [tableName] = field.sourceField.split('.');
      return tableName !== dataSource;
    }

    // If the field ID has a prefix that doesn't match the current dataSource
    if (field.id && field.id.includes('_')) {
      const [tablePrefix] = field.id.split('_');
      return tablePrefix !== dataSource;
    }

    // Otherwise, check if the field exists in the current table
    return !tableFieldMap[dataSource]?.some(f => f.name === field.sourceField || f.name === field.name);
  });

  // Log other table fields for debugging
  useEffect(() => {
    console.log('TABLE SELECTOR - FIELDS FROM OTHER TABLES:', otherTableFields);
  }, [otherTableFields]);

  // Filter available fields based on search term
  const filteredFields = availableFields.filter(field =>
    field.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    field.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Table selection */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Select Table</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="dataSource">Data Source</Label>
              <Select
                value={dataSource}
                onValueChange={onDataSourceChange}
              >
                <SelectTrigger id="dataSource">
                  <SelectValue placeholder="Select a data source" />
                </SelectTrigger>
                <SelectContent>
                  {availableTables.map(table => (
                    <SelectItem key={table} value={table}>
                      {table.charAt(0).toUpperCase() + table.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                Select the table containing the data you want to report on.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Column selection */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Select Columns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search input */}
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search columns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            {/* Column list */}
            <div className="border rounded-md">
              <ScrollArea className="h-[300px]">
                <div className="p-2 space-y-1">
                  {filteredFields.length === 0 ? (
                    <p className="text-center py-4 text-muted-foreground">
                      {dataSource ? 'No columns found' : 'Select a table to view columns'}
                    </p>
                  ) : (
                    filteredFields.map(field => {
                      const isSelected = selectedFields.some(f => f.id === field.id);
                      return (
                        <div
                          key={field.id}
                          className={`flex items-center space-x-2 p-2 rounded-md hover:bg-muted cursor-pointer ${isSelected ? 'bg-muted' : ''}`}
                          onClick={() => toggleFieldSelection(field)}
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleFieldSelection(field)}
                            id={`field-${field.id}`}
                          />
                          <div className="flex-1">
                            <Label
                              htmlFor={`field-${field.id}`}
                              className="cursor-pointer flex items-center justify-between"
                            >
                              <span>{field.displayName}</span>
                              <span className="text-xs text-muted-foreground">({field.dataType})</span>
                            </Label>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Fields from other tables */}
            {otherTableFields.length > 0 && (
              <div className="mt-4 border-t pt-4">
                <p className="text-sm font-medium mb-2">Fields from Other Tables</p>
                <div className="border rounded-md p-2 bg-muted/30">
                  <p className="text-xs text-muted-foreground mb-2">
                    These fields are from tables other than the currently selected table.
                    They will be preserved in your report.
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {otherTableFields.map(field => (
                      <div key={field.id} className="bg-primary/10 border border-primary/20 text-xs px-2 py-1 rounded-md flex items-center gap-1">
                        <span>{field.displayName}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 rounded-full text-primary"
                          onClick={() => toggleFieldSelection(field)}
                        >
                          <span className="sr-only">Remove</span>
                          <span className="text-xs">×</span>
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Selected columns summary */}
            <div className="mt-4">
              <p className="text-sm font-medium mb-1">Selected Columns: {selectedFields.length}</p>
              <div className="flex flex-wrap gap-1">
                {selectedFields
                  .filter(field => !otherTableFields.some(f => f.id === field.id))
                  .map(field => (
                    <div key={field.id} className="bg-muted text-xs px-2 py-1 rounded-md flex items-center gap-1">
                      <span>{field.displayName}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 rounded-full"
                        onClick={() => toggleFieldSelection(field)}
                      >
                        <span className="sr-only">Remove</span>
                        <span className="text-xs">×</span>
                      </Button>
                    </div>
                  ))
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
