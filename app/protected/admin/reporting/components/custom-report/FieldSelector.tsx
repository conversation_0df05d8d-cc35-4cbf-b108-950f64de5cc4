'use client';

import { useState, useRef, useCallback } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { ReportField } from '../../lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, X, GripVertical, Search, ArrowUpDown } from 'lucide-react';

interface FieldSelectorProps {
  availableFields: ReportField[];
  selectedFields: ReportField[];
  onChange: (fields: ReportField[]) => void;
}

export function FieldSelector({
  availableFields,
  selectedFields,
  onChange
}: FieldSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');

  // Add a field to the report
  function addField(field: ReportField) {
    // Check if field is already selected
    if (selectedFields.some(f => f.id === field.id)) {
      return;
    }

    onChange([...selectedFields, { ...field }]);
  }

  // Remove a field from the report
  function removeField(fieldId: string) {
    onChange(selectedFields.filter(f => f.id !== fieldId));
  }

  // Update a field property
  function updateField(fieldId: string, property: string, value: any) {
    onChange(
      selectedFields.map(field =>
        field.id === fieldId
          ? { ...field, [property]: value }
          : field
      )
    );
  }

  // Move a field in the list
  function moveField(dragIndex: number, hoverIndex: number) {
    const newFields = [...selectedFields];
    const draggedField = newFields[dragIndex];

    // Remove the dragged item
    newFields.splice(dragIndex, 1);
    // Insert it at the new position
    newFields.splice(hoverIndex, 0, draggedField);

    onChange(newFields);
  }

  // Filter available fields based on search term
  const filteredAvailableFields = availableFields.filter(field =>
    field.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    field.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="border rounded p-4">
        <h3 className="font-medium mb-2">Available Fields</h3>
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search fields..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <div className="space-y-2 max-h-[400px] overflow-y-auto">
          {filteredAvailableFields.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No fields found
            </p>
          ) : (
            filteredAvailableFields.map(field => (
              <AvailableFieldItem
                key={field.id}
                field={field}
                onAdd={() => addField(field)}
                isSelected={selectedFields.some(f => f != null && f.id === field.id)}
              />
            ))
          )}
        </div>
      </div>
      <div className="border rounded p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Selected Fields</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-xs"
            onClick={() => onChange([])}
            disabled={selectedFields.length === 0}
          >
            Clear All
          </Button>
        </div>
        <div className="space-y-2 max-h-[400px] overflow-y-auto">
          {selectedFields.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No fields selected. Add fields from the left panel.
            </p>
          ) : (
            selectedFields
              .filter(field => field != null) // Filter out null or undefined fields
              .map((field, index) => (
                <SelectedFieldItem
                  key={field.id}
                  field={field}
                  index={index}
                  onRemove={() => removeField(field.id)}
                  onUpdate={(property, value) => updateField(field.id, property, value)}
                  moveField={moveField}
                />
              ))
          )}
        </div>
      </div>
    </div>
  );
}

// Available field item component
interface AvailableFieldItemProps {
  field: ReportField;
  onAdd: () => void;
  isSelected: boolean;
}

function AvailableFieldItem({ field, onAdd, isSelected }: AvailableFieldItemProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'FIELD',
    item: { field },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  const ref = useRef<HTMLDivElement>(null);
  const combinedRef = useCallback((node: HTMLDivElement | null) => {
    ref.current = node;
    drag(node);
  }, [drag]);

  return (
    <div
      ref={combinedRef}
      className={`p-2 border rounded cursor-move flex justify-between items-center ${
        isDragging ? 'opacity-50' : ''
      } ${isSelected ? 'bg-muted' : ''}`}
    >
      <div className="flex items-center">
        <span className="text-sm">{field.displayName}</span>
        <span className="text-xs text-muted-foreground ml-2">({field.dataType})</span>
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={onAdd}
        disabled={isSelected}
        className="h-6 w-6 p-0"
      >
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  );
}

// Selected field item component
interface SelectedFieldItemProps {
  field: ReportField;
  index: number;
  onRemove: () => void;
  onUpdate: (property: string, value: any) => void;
  moveField: (dragIndex: number, hoverIndex: number) => void;
}

function SelectedFieldItem({
  field,
  index,
  onRemove,
  onUpdate,
  moveField
}: SelectedFieldItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'SELECTED_FIELD',
    item: { id: field.id, originalIndex: index }, // Include id and originalIndex in the item
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult<{ index: number }>();
      if (dropResult) {
        // Perform the move only when the drag ends
        moveField(item.originalIndex, dropResult.index);
      }
    },
  }));

  const [, drop] = useDrop(() => ({
    accept: 'SELECTED_FIELD',
    // No state update on hover, only visual feedback if needed
    // hover: (item: { id: string, originalIndex: number }, monitor) => {
    //   // Optional: Add visual feedback on hover
    // },
    drop: (item: { id: string, originalIndex: number }, monitor) => {
      // Return the drop target index
      return { index: index };
    },
  }));

  const ref = useRef<HTMLDivElement>(null);
  const combinedRef = useCallback((node: HTMLDivElement | null) => {
    ref.current = node;
    drag(node);
    drop(node);
  }, [drag, drop]);

  return (
    <div
      ref={combinedRef}
      className={`border rounded ${isDragging ? 'opacity-50' : ''}`}
    >
      <div className="p-2 flex justify-between items-center">
        <div className="flex items-center">
          <GripVertical className="h-4 w-4 mr-2 text-muted-foreground cursor-move" />
          <div>
            <div className="flex items-center">
              <span className="text-sm font-medium">{field.displayName}</span>
              <span className="text-xs text-muted-foreground ml-2">({field.dataType})</span>
            </div>
            {field.aggregation && (
              <span className="text-xs text-muted-foreground">
                {field.aggregation.toUpperCase()}
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            <ArrowUpDown className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-6 w-6 p-0 text-destructive"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {isExpanded && (
        <Card className="m-2 border-dashed">
          <CardContent className="p-3 space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor={`display-name-${field.id}`} className="text-xs">Display Name</Label>
                <Input
                  id={`display-name-${field.id}`}
                  value={field.displayName}
                  onChange={(e) => onUpdate('displayName', e.target.value)}
                  className="h-8 text-sm"
                />
              </div>

              {field.dataType === 'number' && (
                <div>
                  <Label htmlFor={`aggregation-${field.id}`} className="text-xs">Aggregation</Label>
                  <Select
                    value={field.aggregation || 'none'}
                    onValueChange={(value) => onUpdate('aggregation', value === 'none' ? '' : value)}
                  >
                    <SelectTrigger id={`aggregation-${field.id}`} className="h-8 text-sm">
                      <SelectValue placeholder="None" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="sum">Sum</SelectItem>
                      <SelectItem value="avg">Average</SelectItem>
                      <SelectItem value="min">Minimum</SelectItem>
                      <SelectItem value="max">Maximum</SelectItem>
                      <SelectItem value="count">Count</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id={`visible-${field.id}`}
                checked={field.isVisible}
                onCheckedChange={(checked) => onUpdate('isVisible', checked)}
              />
              <Label htmlFor={`visible-${field.id}`} className="text-xs">Show in results</Label>
            </div>

            {field.dataType === 'number' && (
              <div>
                <Label htmlFor={`format-${field.id}`} className="text-xs">Format</Label>
                <Select
                  value={field.format || 'decimal'}
                  onValueChange={(value) => onUpdate('format', value)}
                >
                  <SelectTrigger id={`format-${field.id}`} className="h-8 text-sm">
                    <SelectValue placeholder="Decimal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="decimal">Decimal</SelectItem>
                    <SelectItem value="currency">Currency ($)</SelectItem>
                    <SelectItem value="percent">Percentage (%)</SelectItem>
                    <SelectItem value="integer">Integer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {field.dataType === 'date' && (
              <div>
                <Label htmlFor={`format-${field.id}`} className="text-xs">Format</Label>
                <Select
                  value={field.format || 'date'}
                  onValueChange={(value) => onUpdate('format', value)}
                >
                  <SelectTrigger id={`format-${field.id}`} className="h-8 text-sm">
                    <SelectValue placeholder="Date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date (MM/DD/YYYY)</SelectItem>
                    <SelectItem value="datetime">Date & Time</SelectItem>
                    <SelectItem value="year">Year</SelectItem>
                    <SelectItem value="month">Month</SelectItem>
                    <SelectItem value="quarter">Quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
