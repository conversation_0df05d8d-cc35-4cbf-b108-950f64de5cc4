'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, RefreshCw } from 'lucide-react';
import { CustomReport } from '../../lib/types';
import { validateSqlQuery } from '../../lib/custom-reports';
import { buildSqlQuery } from '../../lib/custom-reports-utils';

interface ReportPreviewProps {
  report: CustomReport;
}

export function ReportPreview({ report }: ReportPreviewProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [sqlQuery, setSqlQuery] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('data');

  // Generate the SQL query and fetch preview data
  async function fetchPreviewData() {
    setIsLoading(true);
    setError(null);

    try {
      // Generate the SQL query
      const query = buildSqlQuery(report);
      setSqlQuery(query);

      // Validate the SQL query
      const validation = await validateSqlQuery(query);

      if (validation.isValid) {
        setPreviewData(validation.data?.slice(0, 5) || []);
      } else {
        setError(validation.error || 'Failed to validate SQL query');
        setPreviewData([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      setPreviewData([]);
    } finally {
      setIsLoading(false);
    }
  }

  // Fetch preview data when the report changes
  useEffect(() => {
    console.log('REPORT PREVIEW - REPORT CHANGED:', report);

    if (report.fields.length > 0) {
      console.log('REPORT PREVIEW - FETCHING PREVIEW DATA');
      // Use a small delay to ensure all state updates have been processed
      const timer = setTimeout(() => {
        fetchPreviewData();
      }, 50);

      return () => clearTimeout(timer);
    } else {
      console.log('REPORT PREVIEW - NO FIELDS, CLEARING DATA');
      setSqlQuery('');
      setPreviewData([]);
    }
  }, [report, report.fields, report.dataSource, report.filters, report.sortBy, report.groupBy]);

  // Get column names from the preview data
  const columns = previewData.length > 0
    ? Object.keys(previewData[0])
    : report.fields.filter(f => f.isVisible).map(f => f.name);

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Preview</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchPreviewData}
          disabled={isLoading || report.fields.length === 0}
          className="flex items-center gap-1"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="data">Data Preview</TabsTrigger>
            <TabsTrigger value="sql">SQL Query</TabsTrigger>
          </TabsList>

          <TabsContent value="data" className="mt-0">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="p-4 border border-destructive/20 bg-destructive/10 rounded-md text-destructive">
                <p className="font-medium">Error</p>
                <p className="text-sm">{error}</p>
              </div>
            ) : previewData.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {report.fields.length === 0
                  ? 'Select fields to preview data'
                  : 'No data available for preview'}
              </div>
            ) : (
              <div className="border rounded-md overflow-hidden">
                <ScrollArea className="h-[300px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columns.map((column) => (
                          <TableHead key={column}>{column}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {previewData.map((row, rowIndex) => (
                        <TableRow key={rowIndex}>
                          {columns.map((column) => (
                            <TableCell key={`${rowIndex}-${column}`}>
                              {row[column] !== null && row[column] !== undefined
                                ? String(row[column])
                                : 'NULL'}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sql" className="mt-0">
            <div className="border rounded-md p-4 bg-muted/50 overflow-auto max-h-[300px]">
              <pre className="text-xs whitespace-pre-wrap">{sqlQuery || 'No SQL query generated yet'}</pre>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
