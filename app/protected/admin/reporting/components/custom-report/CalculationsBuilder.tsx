'use client';

import { useState } from 'react';
import { ReportField } from '../../lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, X, Calculator } from 'lucide-react';

interface Calculation {
  id: string;
  name: string;
  formula: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
  format?: string;
}

interface CalculationsBuilderProps {
  fields: ReportField[];
  calculations: Calculation[];
  onChange: (calculations: Calculation[]) => void;
}

export function CalculationsBuilder({
  fields,
  calculations,
  onChange
}: CalculationsBuilderProps) {
  const [formulaInput, setFormulaInput] = useState('');
  
  // Add a new calculation
  function addCalculation() {
    const newCalculation: Calculation = {
      id: crypto.randomUUID(),
      name: `Calculation ${calculations.length + 1}`,
      formula: '',
      dataType: 'number'
    };
    
    onChange([...calculations, newCalculation]);
  }
  
  // Remove a calculation
  function removeCalculation(id: string) {
    onChange(calculations.filter(calc => calc.id !== id));
  }
  
  // Update a calculation
  function updateCalculation(id: string, property: string, value: any) {
    onChange(
      calculations.map(calc => 
        calc.id === id 
          ? { ...calc, [property]: value } 
          : calc
      )
    );
  }
  
  // Insert a field reference into the formula
  function insertFieldIntoFormula(calculationId: string, fieldId: string) {
    const calculation = calculations.find(calc => calc.id === calculationId);
    if (!calculation) return;
    
    const field = fields.find(f => f.id === fieldId);
    if (!field) return;
    
    const fieldReference = `[${field.name}]`;
    const currentFormula = calculation.formula || '';
    const newFormula = currentFormula + fieldReference;
    
    updateCalculation(calculationId, 'formula', newFormula);
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="font-medium">Custom Calculations</h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={addCalculation}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Calculation
        </Button>
      </div>
      
      {calculations.length === 0 ? (
        <p className="text-sm text-muted-foreground text-center py-4 border rounded">
          No calculations defined. Click "Add Calculation" to create one.
        </p>
      ) : (
        <div className="space-y-4">
          {calculations.map(calculation => (
            <Card key={calculation.id}>
              <CardContent className="p-4 space-y-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <Label htmlFor={`calc-name-${calculation.id}`} className="text-xs">Name</Label>
                    <Input
                      id={`calc-name-${calculation.id}`}
                      value={calculation.name}
                      onChange={(e) => updateCalculation(calculation.id, 'name', e.target.value)}
                      placeholder="Calculation name"
                    />
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => removeCalculation(calculation.id)}
                    className="ml-2 h-8 w-8 p-0 text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div>
                  <Label htmlFor={`calc-formula-${calculation.id}`} className="text-xs">Formula</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id={`calc-formula-${calculation.id}`}
                      value={calculation.formula}
                      onChange={(e) => updateCalculation(calculation.id, 'formula', e.target.value)}
                      placeholder="e.g., [Revenue] - [Cost]"
                      className="font-mono"
                    />
                    <Select
                      value=""
                      onValueChange={(value) => insertFieldIntoFormula(calculation.id, value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Insert field" />
                      </SelectTrigger>
                      <SelectContent>
                        {fields.map(field => (
                          <SelectItem key={field.id} value={field.id}>
                            {field.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Use field names in square brackets, e.g., [Revenue] - [Cost]
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`calc-type-${calculation.id}`} className="text-xs">Result Type</Label>
                    <Select
                      value={calculation.dataType}
                      onValueChange={(value) => updateCalculation(
                        calculation.id, 
                        'dataType', 
                        value as 'string' | 'number' | 'date' | 'boolean'
                      )}
                    >
                      <SelectTrigger id={`calc-type-${calculation.id}`}>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="number">Number</SelectItem>
                        <SelectItem value="string">Text</SelectItem>
                        <SelectItem value="date">Date</SelectItem>
                        <SelectItem value="boolean">Boolean</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {calculation.dataType === 'number' && (
                    <div>
                      <Label htmlFor={`calc-format-${calculation.id}`} className="text-xs">Format</Label>
                      <Select
                        value={calculation.format || 'decimal'}
                        onValueChange={(value) => updateCalculation(calculation.id, 'format', value)}
                      >
                        <SelectTrigger id={`calc-format-${calculation.id}`}>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="decimal">Decimal</SelectItem>
                          <SelectItem value="currency">Currency ($)</SelectItem>
                          <SelectItem value="percent">Percentage (%)</SelectItem>
                          <SelectItem value="integer">Integer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
                
                <div className="bg-muted p-3 rounded-md">
                  <div className="flex items-center text-sm font-medium mb-1">
                    <Calculator className="h-4 w-4 mr-1" />
                    Formula Help
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Supported operations: + (add), - (subtract), * (multiply), / (divide), % (modulo)
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Functions: SUM(), AVG(), MIN(), MAX(), COUNT(), IF(condition, true_value, false_value)
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
