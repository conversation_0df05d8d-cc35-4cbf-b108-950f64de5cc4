'use client';

import { useState } from 'react';
import { FilterCondition, ReportField } from '../../lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { CalendarIcon, Plus, X, Save } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdvancedFilterBuilderProps {
  fields: ReportField[];
  filters: FilterCondition[];
  onChange: (filters: FilterCondition[]) => void;
}

export function AdvancedFilterBuilder({
  fields,
  filters,
  onChange
}: AdvancedFilterBuilderProps) {
  const [savedFilters, setSavedFilters] = useState<{ name: string, filters: FilterCondition[] }[]>([]);
  const [filterName, setFilterName] = useState('');

  // Add a new filter condition
  function addFilterCondition() {
    if (fields.length === 0) return;
    
    const newFilter: FilterCondition = {
      id: crypto.randomUUID(),
      field: fields[0].id,
      operator: 'equals',
      value: '',
      logic: 'and'
    };
    
    onChange([...filters, newFilter]);
  }

  // Remove a filter condition
  function removeFilterCondition(id: string) {
    onChange(filters.filter(filter => filter.id !== id));
  }

  // Update a filter condition
  function updateFilterCondition(id: string, property: string, value: any) {
    onChange(
      filters.map(filter => 
        filter.id === id 
          ? { ...filter, [property]: value } 
          : filter
      )
    );
  }

  // Save current filters
  function saveCurrentFilters() {
    if (!filterName.trim() || filters.length === 0) return;
    
    setSavedFilters([
      ...savedFilters,
      { name: filterName, filters: [...filters] }
    ]);
    
    setFilterName('');
  }

  // Load saved filters
  function loadSavedFilters(savedFilter: { name: string, filters: FilterCondition[] }) {
    onChange(savedFilter.filters);
  }

  // Delete saved filters
  function deleteSavedFilter(index: number) {
    setSavedFilters(savedFilters.filter((_, i) => i !== index));
  }

  return (
    <div className="space-y-4">
      {/* Filter conditions */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="font-medium">Filter Conditions</h3>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={addFilterCondition}
            className="flex items-center"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Condition
          </Button>
        </div>
        
        {filters.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4 border rounded">
            No filter conditions. Click "Add Condition" to create one.
          </p>
        ) : (
          <div className="space-y-2">
            {filters.map((filter, index) => (
              <FilterConditionItem
                key={filter.id}
                filter={filter}
                fields={fields}
                isLast={index === filters.length - 1}
                onUpdate={(property, value) => updateFilterCondition(filter.id, property, value)}
                onRemove={() => removeFilterCondition(filter.id)}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Save filters section */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-2">Save Filter Set</h3>
          <div className="flex space-x-2">
            <Input
              placeholder="Filter set name"
              value={filterName}
              onChange={(e) => setFilterName(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={saveCurrentFilters} 
              disabled={!filterName.trim() || filters.length === 0}
              className="flex items-center"
            >
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
          </div>
          
          {savedFilters.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Saved Filter Sets</h4>
              <div className="space-y-2">
                {savedFilters.map((savedFilter, index) => (
                  <div key={index} className="flex justify-between items-center p-2 border rounded">
                    <span>{savedFilter.name}</span>
                    <div className="flex space-x-1">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => loadSavedFilters(savedFilter)}
                        className="h-7 px-2"
                      >
                        Load
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => deleteSavedFilter(index)}
                        className="h-7 px-2 text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Filter condition item component
interface FilterConditionItemProps {
  filter: FilterCondition;
  fields: ReportField[];
  isLast: boolean;
  onUpdate: (property: string, value: any) => void;
  onRemove: () => void;
}

function FilterConditionItem({
  filter,
  fields,
  isLast,
  onUpdate,
  onRemove
}: FilterConditionItemProps) {
  // Find the selected field
  const selectedField = fields.find(f => f.id === filter.field);
  
  // Get operators based on field type
  const operators = getOperatorsForType(selectedField?.dataType || 'string');
  
  return (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-12 gap-2">
          {/* Field selector */}
          <div className="col-span-3">
            <Label htmlFor={`field-${filter.id}`} className="text-xs">Field</Label>
            <Select
              value={filter.field}
              onValueChange={(value) => onUpdate('field', value)}
            >
              <SelectTrigger id={`field-${filter.id}`}>
                <SelectValue placeholder="Select field" />
              </SelectTrigger>
              <SelectContent>
                {fields.map(field => (
                  <SelectItem key={field.id} value={field.id}>
                    {field.displayName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* Operator selector */}
          <div className="col-span-2">
            <Label htmlFor={`operator-${filter.id}`} className="text-xs">Operator</Label>
            <Select
              value={filter.operator}
              onValueChange={(value) => onUpdate('operator', value)}
            >
              <SelectTrigger id={`operator-${filter.id}`}>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                {operators.map(op => (
                  <SelectItem key={op.value} value={op.value}>
                    {op.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* Value input */}
          <div className={`col-span-${filter.operator === 'between' ? '4' : '5'}`}>
            <Label htmlFor={`value-${filter.id}`} className="text-xs">Value</Label>
            {renderValueInput(filter, selectedField, (value) => onUpdate('value', value))}
          </div>
          
          {/* End value input (for 'between' operator) */}
          {filter.operator === 'between' && (
            <div className="col-span-1">
              <Label htmlFor={`value-end-${filter.id}`} className="text-xs">And</Label>
              {renderValueInput(
                { ...filter, operator: 'equals' }, // Use 'equals' to get a simple input
                selectedField,
                (value) => onUpdate('valueEnd', value),
                filter.valueEnd
              )}
            </div>
          )}
          
          {/* Logic selector (AND/OR) */}
          {!isLast && (
            <div className="col-span-1">
              <Label className="text-xs">Logic</Label>
              <RadioGroup
                value={filter.logic || 'and'}
                onValueChange={(value) => onUpdate('logic', value)}
                className="flex space-x-2"
              >
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="and" id={`and-${filter.id}`} />
                  <Label htmlFor={`and-${filter.id}`} className="text-xs">AND</Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="or" id={`or-${filter.id}`} />
                  <Label htmlFor={`or-${filter.id}`} className="text-xs">OR</Label>
                </div>
              </RadioGroup>
            </div>
          )}
          
          {/* Remove button */}
          <div className="col-span-1 flex items-end justify-end">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onRemove}
              className="h-10 w-10 p-0 text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to get operators based on field type
function getOperatorsForType(type: string): { value: string, label: string }[] {
  switch (type) {
    case 'number':
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'notEquals', label: 'Not Equals' },
        { value: 'greaterThan', label: 'Greater Than' },
        { value: 'lessThan', label: 'Less Than' },
        { value: 'between', label: 'Between' },
        { value: 'in', label: 'In List' },
      ];
    case 'date':
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'notEquals', label: 'Not Equals' },
        { value: 'greaterThan', label: 'After' },
        { value: 'lessThan', label: 'Before' },
        { value: 'between', label: 'Between' },
      ];
    case 'boolean':
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'notEquals', label: 'Not Equals' },
      ];
    default: // string
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'notEquals', label: 'Not Equals' },
        { value: 'contains', label: 'Contains' },
        { value: 'startsWith', label: 'Starts With' },
        { value: 'endsWith', label: 'Ends With' },
        { value: 'in', label: 'In List' },
      ];
  }
}

// Helper function to render the appropriate value input based on field type
function renderValueInput(
  filter: FilterCondition, 
  field?: ReportField,
  onChange?: (value: any) => void,
  value?: any
) {
  const inputValue = value !== undefined ? value : filter.value;
  
  if (!field) {
    return (
      <Input
        id={`value-${filter.id}`}
        value={inputValue || ''}
        onChange={(e) => onChange && onChange(e.target.value)}
        placeholder="Enter value"
      />
    );
  }
  
  switch (field.dataType) {
    case 'number':
      return (
        <Input
          id={`value-${filter.id}`}
          type="number"
          value={inputValue || ''}
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder="Enter number"
        />
      );
    
    case 'date':
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !inputValue && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {inputValue ? format(new Date(inputValue), 'PPP') : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={inputValue ? new Date(inputValue) : undefined}
              onSelect={(date) => onChange && onChange(date ? date.toISOString() : '')}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      );
    
    case 'boolean':
      return (
        <Select
          value={inputValue === true ? 'true' : inputValue === false ? 'false' : ''}
          onValueChange={(value) => onChange && onChange(value === 'true' ? true : value === 'false' ? false : '')}
        >
          <SelectTrigger id={`value-${filter.id}`}>
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">True</SelectItem>
            <SelectItem value="false">False</SelectItem>
          </SelectContent>
        </Select>
      );
    
    case 'string':
      if (filter.operator === 'in') {
        return (
          <Input
            id={`value-${filter.id}`}
            value={inputValue || ''}
            onChange={(e) => onChange && onChange(e.target.value)}
            placeholder="Comma-separated values"
          />
        );
      }
      
      return (
        <Input
          id={`value-${filter.id}`}
          value={inputValue || ''}
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder="Enter text"
        />
      );
    
    default:
      return (
        <Input
          id={`value-${filter.id}`}
          value={inputValue || ''}
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder="Enter value"
        />
      );
  }
}
