'use client';

import { useState } from 'react';
import { ReportField } from '../../lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Plus, X, <PERSON><PERSON>, Line<PERSON>hart, Pie<PERSON>hart, AreaChart, ScatterChart, Grid3X3 } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface VisualizationSeries {
  field: string;
  name: string;
  color?: string;
  type?: 'bar' | 'line' | 'area';
}

interface Visualization {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'heatmap';
  title: string;
  xAxis?: string;
  yAxis?: string;
  series: VisualizationSeries[];
  options?: Record<string, any>;
}

interface VisualizationBuilderProps {
  fields: ReportField[];
  visualizations: Visualization[];
  onChange: (visualizations: Visualization[]) => void;
}

export function VisualizationBuilder({
  fields,
  visualizations,
  onChange
}: VisualizationBuilderProps) {
  const [activeTab, setActiveTab] = useState('chart');
  
  // Add a new visualization
  function addVisualization() {
    const newVisualization: Visualization = {
      id: crypto.randomUUID(),
      type: 'bar',
      title: `Chart ${visualizations.length + 1}`,
      series: []
    };
    
    onChange([...visualizations, newVisualization]);
  }
  
  // Remove a visualization
  function removeVisualization(id: string) {
    onChange(visualizations.filter(vis => vis.id !== id));
  }
  
  // Update a visualization
  function updateVisualization(id: string, property: string, value: any) {
    onChange(
      visualizations.map(vis => 
        vis.id === id 
          ? { ...vis, [property]: value } 
          : vis
      )
    );
  }
  
  // Add a series to a visualization
  function addSeries(visualizationId: string) {
    const visualization = visualizations.find(vis => vis.id === visualizationId);
    if (!visualization || fields.length === 0) return;
    
    // Find a numeric field for the series
    const numericField = fields.find(f => f.dataType === 'number');
    const fieldId = numericField ? numericField.id : fields[0].id;
    
    const newSeries: VisualizationSeries = {
      field: fieldId,
      name: getFieldName(fieldId),
      color: getRandomColor()
    };
    
    updateVisualization(
      visualizationId, 
      'series', 
      [...(visualization.series || []), newSeries]
    );
  }
  
  // Remove a series from a visualization
  function removeSeries(visualizationId: string, index: number) {
    const visualization = visualizations.find(vis => vis.id === visualizationId);
    if (!visualization) return;
    
    const newSeries = [...visualization.series];
    newSeries.splice(index, 1);
    
    updateVisualization(visualizationId, 'series', newSeries);
  }
  
  // Update a series
  function updateSeries(visualizationId: string, index: number, property: string, value: any) {
    const visualization = visualizations.find(vis => vis.id === visualizationId);
    if (!visualization) return;
    
    const newSeries = [...visualization.series];
    newSeries[index] = { ...newSeries[index], [property]: value };
    
    updateVisualization(visualizationId, 'series', newSeries);
  }
  
  // Update visualization options
  function updateOptions(visualizationId: string, property: string, value: any) {
    const visualization = visualizations.find(vis => vis.id === visualizationId);
    if (!visualization) return;
    
    const newOptions = { ...(visualization.options || {}), [property]: value };
    updateVisualization(visualizationId, 'options', newOptions);
  }
  
  // Get field name by ID
  function getFieldName(fieldId: string): string {
    const field = fields.find(f => f.id === fieldId);
    return field ? field.displayName : fieldId;
  }
  
  // Generate a random color
  function getRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
  
  // Get chart icon based on type
  function getChartIcon(type: string) {
    switch (type) {
      case 'bar':
        return <BarChart className="h-4 w-4" />;
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      case 'area':
        return <AreaChart className="h-4 w-4" />;
      case 'scatter':
        return <ScatterChart className="h-4 w-4" />;
      case 'heatmap':
        return <Grid3X3 className="h-4 w-4" />;
      default:
        return <BarChart className="h-4 w-4" />;
    }
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="font-medium">Visualizations</h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={addVisualization}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Visualization
        </Button>
      </div>
      
      {visualizations.length === 0 ? (
        <p className="text-sm text-muted-foreground text-center py-4 border rounded">
          No visualizations defined. Click "Add Visualization" to create one.
        </p>
      ) : (
        <div className="space-y-6">
          {visualizations.map(visualization => (
            <Card key={visualization.id}>
              <CardHeader className="pb-2 flex flex-row items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    {getChartIcon(visualization.type)}
                    <Input
                      value={visualization.title}
                      onChange={(e) => updateVisualization(visualization.id, 'title', e.target.value)}
                      className="ml-2 text-lg font-semibold border-none h-auto p-0 focus-visible:ring-0"
                      placeholder="Chart title"
                    />
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => removeVisualization(visualization.id)}
                  className="h-8 w-8 p-0 text-destructive"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent className="pt-0">
                <Tabs defaultValue="chart" className="w-full">
                  <TabsList className="mb-4 grid grid-cols-3">
                    <TabsTrigger value="chart">Chart Type</TabsTrigger>
                    <TabsTrigger value="data">Data Series</TabsTrigger>
                    <TabsTrigger value="options">Options</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="chart">
                    <div className="grid grid-cols-3 gap-4">
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'bar' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'bar')}
                      >
                        <BarChart className="h-12 w-12 mb-2" />
                        <span>Bar Chart</span>
                      </div>
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'line' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'line')}
                      >
                        <LineChart className="h-12 w-12 mb-2" />
                        <span>Line Chart</span>
                      </div>
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'pie' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'pie')}
                      >
                        <PieChart className="h-12 w-12 mb-2" />
                        <span>Pie Chart</span>
                      </div>
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'area' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'area')}
                      >
                        <AreaChart className="h-12 w-12 mb-2" />
                        <span>Area Chart</span>
                      </div>
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'scatter' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'scatter')}
                      >
                        <ScatterChart className="h-12 w-12 mb-2" />
                        <span>Scatter Plot</span>
                      </div>
                      <div 
                        className={`border rounded p-3 flex flex-col items-center cursor-pointer ${visualization.type === 'heatmap' ? 'bg-muted border-primary' : ''}`}
                        onClick={() => updateVisualization(visualization.id, 'type', 'heatmap')}
                      >
                        <Grid3X3 className="h-12 w-12 mb-2" />
                        <span>Heat Map</span>
                      </div>
                    </div>
                    
                    {visualization.type !== 'pie' && (
                      <div className="mt-4 grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`x-axis-${visualization.id}`} className="text-xs">X-Axis Field</Label>
                          <Select
                            value={visualization.xAxis || ''}
                            onValueChange={(value) => updateVisualization(visualization.id, 'xAxis', value)}
                          >
                            <SelectTrigger id={`x-axis-${visualization.id}`}>
                              <SelectValue placeholder="Select X-Axis field" />
                            </SelectTrigger>
                            <SelectContent>
                              {fields.map(field => (
                                <SelectItem key={field.id} value={field.id}>
                                  {field.displayName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label htmlFor={`y-axis-${visualization.id}`} className="text-xs">Y-Axis Label</Label>
                          <Input
                            id={`y-axis-${visualization.id}`}
                            value={visualization.yAxis || ''}
                            onChange={(e) => updateVisualization(visualization.id, 'yAxis', e.target.value)}
                            placeholder="e.g., Amount ($)"
                          />
                        </div>
                      </div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="data">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="text-sm font-medium">Data Series</h4>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => addSeries(visualization.id)}
                          className="flex items-center"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Series
                        </Button>
                      </div>
                      
                      {visualization.series.length === 0 ? (
                        <p className="text-sm text-muted-foreground text-center py-4 border rounded">
                          No data series defined. Click "Add Series" to add data to your chart.
                        </p>
                      ) : (
                        <div className="space-y-3">
                          {visualization.series.map((series, index) => (
                            <Card key={index} className="p-3">
                              <div className="flex justify-between items-start">
                                <div className="flex-1 grid grid-cols-2 gap-3">
                                  <div>
                                    <Label htmlFor={`series-field-${visualization.id}-${index}`} className="text-xs">Field</Label>
                                    <Select
                                      value={series.field}
                                      onValueChange={(value) => {
                                        updateSeries(visualization.id, index, 'field', value);
                                        updateSeries(visualization.id, index, 'name', getFieldName(value));
                                      }}
                                    >
                                      <SelectTrigger id={`series-field-${visualization.id}-${index}`}>
                                        <SelectValue placeholder="Select field" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {fields.map(field => (
                                          <SelectItem key={field.id} value={field.id}>
                                            {field.displayName}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                  
                                  <div>
                                    <Label htmlFor={`series-name-${visualization.id}-${index}`} className="text-xs">Display Name</Label>
                                    <Input
                                      id={`series-name-${visualization.id}-${index}`}
                                      value={series.name}
                                      onChange={(e) => updateSeries(visualization.id, index, 'name', e.target.value)}
                                      placeholder="Series name"
                                    />
                                  </div>
                                  
                                  <div>
                                    <Label htmlFor={`series-color-${visualization.id}-${index}`} className="text-xs">Color</Label>
                                    <div className="flex items-center space-x-2">
                                      <Popover>
                                        <PopoverTrigger asChild>
                                          <Button 
                                            variant="outline" 
                                            className="w-full h-9 flex justify-between items-center"
                                          >
                                            <div 
                                              className="h-5 w-5 rounded-sm" 
                                              style={{ backgroundColor: series.color || '#000000' }}
                                            />
                                            <span>{series.color || '#000000'}</span>
                                          </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-3">
                                          <HexColorPicker 
                                            color={series.color || '#000000'} 
                                            onChange={(color) => updateSeries(visualization.id, index, 'color', color)}
                                          />
                                        </PopoverContent>
                                      </Popover>
                                    </div>
                                  </div>
                                  
                                  {(visualization.type === 'line' || visualization.type === 'area') && (
                                    <div>
                                      <Label htmlFor={`series-type-${visualization.id}-${index}`} className="text-xs">Series Type</Label>
                                      <Select
                                        value={series.type || visualization.type}
                                        onValueChange={(value) => updateSeries(
                                          visualization.id, 
                                          index, 
                                          'type', 
                                          value as 'bar' | 'line' | 'area'
                                        )}
                                      >
                                        <SelectTrigger id={`series-type-${visualization.id}-${index}`}>
                                          <SelectValue placeholder="Select type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="bar">Bar</SelectItem>
                                          <SelectItem value="line">Line</SelectItem>
                                          <SelectItem value="area">Area</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  )}
                                </div>
                                
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  onClick={() => removeSeries(visualization.id, index)}
                                  className="ml-2 h-8 w-8 p-0 text-destructive"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </Card>
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="options">
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`show-legend-${visualization.id}`}
                              checked={visualization.options?.showLegend !== false}
                              onCheckedChange={(checked) => updateOptions(visualization.id, 'showLegend', checked)}
                            />
                            <Label htmlFor={`show-legend-${visualization.id}`}>Show Legend</Label>
                          </div>
                        </div>
                        
                        <div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`show-grid-${visualization.id}`}
                              checked={visualization.options?.showGrid !== false}
                              onCheckedChange={(checked) => updateOptions(visualization.id, 'showGrid', checked)}
                            />
                            <Label htmlFor={`show-grid-${visualization.id}`}>Show Grid</Label>
                          </div>
                        </div>
                        
                        <div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`stacked-${visualization.id}`}
                              checked={visualization.options?.stacked === true}
                              onCheckedChange={(checked) => updateOptions(visualization.id, 'stacked', checked)}
                            />
                            <Label htmlFor={`stacked-${visualization.id}`}>Stacked</Label>
                          </div>
                        </div>
                        
                        <div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`show-values-${visualization.id}`}
                              checked={visualization.options?.showValues === true}
                              onCheckedChange={(checked) => updateOptions(visualization.id, 'showValues', checked)}
                            />
                            <Label htmlFor={`show-values-${visualization.id}`}>Show Values</Label>
                          </div>
                        </div>
                      </div>
                      
                      {(visualization.type === 'line' || visualization.type === 'area') && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium">Trend Analysis</h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="flex items-center space-x-2">
                                <Switch
                                  id={`show-trend-line-${visualization.id}`}
                                  checked={visualization.options?.showTrendLine === true}
                                  onCheckedChange={(checked) => updateOptions(visualization.id, 'showTrendLine', checked)}
                                />
                                <Label htmlFor={`show-trend-line-${visualization.id}`}>Show Trend Line</Label>
                              </div>
                            </div>
                            
                            <div>
                              <div className="flex items-center space-x-2">
                                <Switch
                                  id={`show-forecast-${visualization.id}`}
                                  checked={visualization.options?.showForecast === true}
                                  onCheckedChange={(checked) => updateOptions(visualization.id, 'showForecast', checked)}
                                />
                                <Label htmlFor={`show-forecast-${visualization.id}`}>Show Forecast</Label>
                              </div>
                            </div>
                            
                            {visualization.options?.showForecast && (
                              <div>
                                <Label htmlFor={`forecast-periods-${visualization.id}`} className="text-xs">Forecast Periods</Label>
                                <Input
                                  id={`forecast-periods-${visualization.id}`}
                                  type="number"
                                  min="1"
                                  max="12"
                                  value={visualization.options?.forecastPeriods || 3}
                                  onChange={(e) => updateOptions(visualization.id, 'forecastPeriods', parseInt(e.target.value) || 3)}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {visualization.type === 'bar' && (
                        <div>
                          <Label htmlFor={`bar-orientation-${visualization.id}`} className="text-xs">Bar Orientation</Label>
                          <Select
                            value={visualization.options?.barOrientation || 'vertical'}
                            onValueChange={(value) => updateOptions(visualization.id, 'barOrientation', value)}
                          >
                            <SelectTrigger id={`bar-orientation-${visualization.id}`}>
                              <SelectValue placeholder="Select orientation" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="vertical">Vertical</SelectItem>
                              <SelectItem value="horizontal">Horizontal</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                      
                      {visualization.type === 'pie' && (
                        <div>
                          <Label htmlFor={`pie-type-${visualization.id}`} className="text-xs">Pie Chart Type</Label>
                          <Select
                            value={visualization.options?.pieType || 'pie'}
                            onValueChange={(value) => updateOptions(visualization.id, 'pieType', value)}
                          >
                            <SelectTrigger id={`pie-type-${visualization.id}`}>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pie">Pie</SelectItem>
                              <SelectItem value="doughnut">Doughnut</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
