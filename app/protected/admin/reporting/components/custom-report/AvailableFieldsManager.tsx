'use client';

import { useState, useEffect } from 'react';
import { ReportField } from '../../lib/types';

interface AvailableFieldsManagerProps {
  dataSource: string;
  joins: any[];
  onFieldsChange: (fields: ReportField[]) => void;
}

export function AvailableFieldsManager({
  dataSource,
  joins,
  onFieldsChange
}: AvailableFieldsManagerProps) {
  const [availableFields, setAvailableFields] = useState<ReportField[]>([]);

  // Table field definitions
  const tableFieldMap: Record<string, { name: string, type: 'string' | 'number' | 'date' | 'boolean' }[]> = {
    'households': [
      { name: 'id', type: 'string' },
      { name: 'householdName', type: 'string' },
      { name: 'members', type: 'string' },
      { name: 'user_id', type: 'string' },
      { name: 'address', type: 'string' },
      { name: 'phone', type: 'string' },
      { name: 'email', type: 'string' },
      { name: 'occupation', type: 'string' },
      { name: 'employer', type: 'string' },
      { name: 'marital_status', type: 'string' },
      { name: 'date_of_birth', type: 'date' },
      { name: 'tax_file_number', type: 'string' },
      { name: 'notes', type: 'string' },
      { name: 'street', type: 'string' },
      { name: 'city', type: 'string' },
      { name: 'state', type: 'string' },
      { name: 'zip_code', type: 'string' },
      { name: 'country', type: 'string' },
      { name: 'property_type', type: 'string' },
      { name: 'preferred_contact', type: 'string' },
      { name: 'best_time_to_call', type: 'string' },
      { name: 'alternative_contact', type: 'string' },
      { name: 'investment_strategy', type: 'string' },
      { name: 'risk_tolerance', type: 'string' },
      { name: 'primary_advisor', type: 'string' },
      { name: 'last_review', type: 'date' },
      { name: 'next_review', type: 'date' },
      { name: 'additional_info', type: 'string' },
      { name: 'org_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'assets': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'type', type: 'string' },
      { name: 'value', type: 'number' },
      { name: 'details', type: 'string' },
      { name: 'property_type', type: 'string' },
      { name: 'rental_income', type: 'number' },
      { name: 'provider', type: 'string' },
      { name: 'linked_income_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'income': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'source', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'frequency', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'income_type', type: 'string' },
      { name: 'linked_asset_id', type: 'string' },
      { name: 'member_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'expenses': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'frequency', type: 'string' },
      { name: 'category', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'linked_liability_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'liabilities': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'amount', type: 'number' },
      { name: 'interest_rate', type: 'number' },
      { name: 'lender', type: 'string' },
      { name: 'details', type: 'string' },
      { name: 'linked_asset_id', type: 'string' },
      { name: 'linked_expense_id', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'goals': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'target_date', type: 'date' },
      { name: 'target_amount', type: 'number' },
      { name: 'current_amount', type: 'number' },
      { name: 'status', type: 'string' },
      { name: 'priority', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'tasks': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'due_date', type: 'date' },
      { name: 'status', type: 'string' },
      { name: 'priority', type: 'string' },
      { name: 'assigned_to', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'interactions': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'content', type: 'string' },
      { name: 'date', type: 'date' },
      { name: 'type', type: 'string' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ],
    'recommendations': [
      { name: 'id', type: 'string' },
      { name: 'household_id', type: 'string' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'status', type: 'string' },
      { name: 'financial_impact', type: 'number' },
      { name: 'created_at', type: 'date' },
      { name: 'updated_at', type: 'date' }
    ]
  };

  // Update available fields when data source or joins change
  useEffect(() => {
    const fields: ReportField[] = [];

    // Add fields from the primary table
    if (tableFieldMap[dataSource]) {
      tableFieldMap[dataSource].forEach(field => {
        fields.push({
          id: `${dataSource}_${field.name}`,
          name: field.name,
          sourceField: field.name,
          displayName: formatFieldName(field.name),
          dataType: field.type,
          isVisible: true,
          width: 150
        });
      });
    }

    // Add fields from joined tables
    joins.forEach(join => {
      if (tableFieldMap[join.table]) {
        tableFieldMap[join.table].forEach(field => {
          fields.push({
            id: `${join.table}_${field.name}`,
            name: `${join.table}_${field.name}`,
            sourceField: `${join.table}.${field.name}`,
            displayName: `${formatTableName(join.table)} ${formatFieldName(field.name)}`,
            dataType: field.type,
            isVisible: true,
            width: 150
          });
        });
      }
    });

    setAvailableFields(fields);
    onFieldsChange(fields);
  }, [dataSource, joins]);

  // Helper function to format field names for display
  function formatFieldName(name: string): string {
    return name
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/\b\w/g, c => c.toUpperCase());
  }

  // Helper function to format table names for display
  function formatTableName(name: string): string {
    return name
      .replace(/_/g, ' ')
      .replace(/\b\w/g, c => c.toUpperCase());
  }

  return null; // This component just manages the available fields, it doesn't render anything
}
