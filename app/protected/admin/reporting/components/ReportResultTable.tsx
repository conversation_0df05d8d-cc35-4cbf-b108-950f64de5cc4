'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
  getPaginationRowModel,
  getFilteredRowModel,
  Header, // Import Header type
} from "@tanstack/react-table";
import { useState, CSSProperties, useMemo } from "react"; // Import CSSProperties and useMemo
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ArrowUpDown, GripVertical } from "lucide-react"; // Add GripVertical

interface ReportResultTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  onRowClick?: (row: TData) => void;
}

// Draggable Table Header Cell Component
const DraggableTableHeader = <TData, TValue>({ header }: { header: Header<TData, TValue> }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } =
    useSortable({ id: header.id });

  const style: CSSProperties = {
    opacity: isDragging ? 0.8 : 1,
    position: 'relative',
    transform: CSS.Translate.toString(transform), // Use translate instead of transform to avoid squishing
    transition,
    width: header.getSize(),
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <TableHead
      key={header.id}
      ref={setNodeRef}
      style={style}
      colSpan={header.colSpan}
      className="whitespace-nowrap" // Prevent text wrapping during drag
    >
      <div className="flex items-center gap-1"> {/* Reduced gap */}
        {/* Drag Handle */}
        <Button
          variant="ghost"
          size="sm"
          {...attributes}
          {...listeners}
          className="cursor-grab p-1 h-auto" // Make button smaller
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </Button>
        {/* Original Header Content */}
        {header.isPlaceholder ? null : (
          <div
            className={header.column.getCanSort() ? "cursor-pointer select-none flex items-center" : "select-none flex items-center"}
            onClick={header.column.getToggleSortingHandler()}
            title={header.column.getCanSort() ? 'Sort column' : undefined} // Add tooltip for sorting
          >
            {flexRender(
              header.column.columnDef.header,
              header.getContext()
            )}
            {header.column.getCanSort() && (
              <ArrowUpDown className="ml-2 h-4 w-4 opacity-50" /> // Dim sort icon slightly
            )}
          </div>
        )}
      </div>
    </TableHead>
  );
};

export function ReportResultTable<TData, TValue>({
  columns,
  data,
  onRowClick
}: ReportResultTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState('');

  // Helper function to extract column IDs safely
  const getColumnIds = (cols: ColumnDef<TData, TValue>[]) => {
    return cols.map((c, index) => {
      // Prefer accessorKey if available and is a string
      if (typeof (c as any).accessorKey === 'string') {
        return (c as any).accessorKey;
      }
      // Fallback to id if available and is a string
      if (typeof (c as any).id === 'string') {
        return (c as any).id;
      }
      // Fallback to header if it's a string
      if (typeof c.header === 'string') {
        return c.header;
      }
      // If no suitable string ID found, generate one based on index
      console.warn("Column definition missing usable ID for DnD, generating fallback:", c);
      return `col_${index}`;
    });
  };

  const initialColumnIds = useMemo(() => getColumnIds(columns), [columns]);
  const [columnOrder, setColumnOrder] = useState<string[]>(initialColumnIds);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      globalFilter,
      columnOrder, // Add columnOrder to state
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    onColumnOrderChange: setColumnOrder, // Add handler for column order changes
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // Prevent columns from resizing while dragging
    columnResizeMode: 'onChange',
  });

  // Sensors for dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  // Drag end handler for columns
  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;
    if (active.id !== over?.id) {
      setColumnOrder((items) => {
        const oldIndex = items.indexOf(active.id as string);
        const newIndex = items.indexOf(over!.id as string);
        // Ensure indices are valid before moving
        if (oldIndex !== -1 && newIndex !== -1) {
          return arrayMove(items, oldIndex, newIndex);
        }
        return items; // Return original items if indices are invalid
      });
    }
  }

  return (
    <div className="space-y-4 max-h-[calc(100vh-250px)] flex flex-col">
      <div className="flex items-center justify-between">
        <Input
          placeholder="Search all columns..."
          value={globalFilter ?? ''}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="rounded-md border overflow-auto flex-1">
        <div className="overflow-x-auto w-full">
          {/* Wrap table in DndContext */}
          <DndContext collisionDetection={closestCenter} modifiers={[restrictToHorizontalAxis]} onDragEnd={handleDragEnd} sensors={sensors}>
            <Table>
              <TableHeader className="sticky top-0 bg-background z-10">
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {/* Wrap headers in SortableContext */}
                    <SortableContext items={columnOrder} strategy={horizontalListSortingStrategy}>
                      {headerGroup.headers.map((header) => (
                        // Use the DraggableTableHeader component
                        <DraggableTableHeader key={header.id} header={header} />
                      ))}
                    </SortableContext>
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={() => onRowClick && onRowClick(row.original)}
                      className={onRowClick ? "cursor-pointer hover:bg-muted" : ""}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="max-w-[300px] truncate">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </DndContext> {/* Close DndContext */}
        </div>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} row(s) total
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Rows per page</span>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
            className="h-8 w-16 rounded-md border border-input bg-background px-2 py-1 text-sm"
          >
            {[10, 20, 30, 40, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
