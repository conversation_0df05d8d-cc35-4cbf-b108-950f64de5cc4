'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { CustomReport } from '../lib/types';
import {
  deleteCustomReport,
  toggleFavoriteReport,
  saveReportAsTemplate
} from '../lib/custom-reports';
import { useReports } from '../context/ReportContext';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Loader2,
  MoreHorizontal,
  Play,
  Edit,
  Trash2,
  Star,
  StarOff,
  Copy,
  FileSpreadsheet,
  FileText,
  Check,
  Square
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { Checkbox } from '@/components/ui/checkbox';

interface CustomReportsListProps {
  onCreateReport: () => void;
  onEditReport: (report: CustomReport) => void;
  onRunReport: (report: CustomReport) => void;
  searchTerm: string;
}

export function CustomReportsList({
  onCreateReport,
  onEditReport,
  onRunReport,
  searchTerm
}: CustomReportsListProps) {
  const { reports, isLoading, error: contextError, refreshReports } = useReports();
  const [deleteReportId, setDeleteReportId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(contextError);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [isDeleteMultipleOpen, setIsDeleteMultipleOpen] = useState(false);
  const [isDeletingMultiple, setIsDeletingMultiple] = useState(false);

  // Handle report deletion
  async function handleDeleteReport() {
    if (!deleteReportId) return;

    setIsDeleting(true);

    try {
      await deleteCustomReport(deleteReportId);
      await refreshReports(); // Refresh the reports list after deletion
      setDeleteReportId(null);
    } catch (err) {
      console.error('Error deleting report:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete report');
    } finally {
      setIsDeleting(false);
    }
  }

  // Handle multiple report deletion
  async function handleDeleteMultipleReports() {
    if (selectedReports.length === 0) return;

    setIsDeletingMultiple(true);

    try {
      // Delete each selected report one by one
      for (const reportId of selectedReports) {
        await deleteCustomReport(reportId);
      }

      // Clear selection and refresh the list
      setSelectedReports([]);
      await refreshReports();
      setIsDeleteMultipleOpen(false);
    } catch (err) {
      console.error('Error deleting multiple reports:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete selected reports');
    } finally {
      setIsDeletingMultiple(false);
    }
  }

  // Handle toggling favorite status
  async function handleToggleFavorite(report: CustomReport) {
    try {
      await toggleFavoriteReport(report.id, !report.isFavorite);
      await refreshReports(); // Refresh the reports list after toggling favorite
    } catch (err) {
      console.error('Error toggling favorite:', err);
      setError(err instanceof Error ? err.message : 'Failed to update favorite status');
    }
  }

  // Handle saving as template
  async function handleSaveAsTemplate(report: CustomReport) {
    try {
      await saveReportAsTemplate(report.id);
      await refreshReports(); // Refresh the reports list after saving as template
    } catch (err) {
      console.error('Error saving as template:', err);
      setError(err instanceof Error ? err.message : 'Failed to save as template');
    }
  }

  // Handle selecting/deselecting all reports
  function handleSelectAll(checked: boolean) {
    if (checked) {
      // Select all filtered reports
      setSelectedReports(filteredReports.map(report => report.id));
    } else {
      // Deselect all
      setSelectedReports([]);
    }
  }

  // Handle selecting/deselecting a single report
  function handleSelectReport(reportId: string, checked: boolean) {
    if (checked) {
      setSelectedReports(prev => [...prev, reportId]);
    } else {
      setSelectedReports(prev => prev.filter(id => id !== reportId));
    }
  }

  // Filter reports based on search term
  const filteredReports = reports.filter(report =>
    report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (report.description && report.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading custom reports...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-end items-center">
        {selectedReports.length > 0 && (
          <Button
            variant="destructive"
            onClick={() => setIsDeleteMultipleOpen(true)}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete Selected ({selectedReports.length})
          </Button>
        )}
      </div>

      {filteredReports.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground mb-4">No custom reports found.</p>
            <Button onClick={onCreateReport}>Create Your First Report</Button>
          </CardContent>
        </Card>
      ) : (
        <div>
          <div className="border-b">
            <Table className="table-fixed w-full">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[30px]">
                    <Checkbox
                      checked={filteredReports.length > 0 && selectedReports.length === filteredReports.length}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all reports"
                    />
                  </TableHead>
                  <TableHead className="w-[30px]"></TableHead>
                  <TableHead className="w-[150px]">Report Name</TableHead>
                  <TableHead className="w-auto">Description</TableHead>
                  <TableHead className="w-[120px]">Data Source</TableHead>
                  <TableHead className="w-[80px]">Template</TableHead>
                  <TableHead className="w-[100px]">Last Updated</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
            </Table>
          </div>
          <div className="overflow-y-auto max-h-[calc(100vh-350px)]">
            <Table className="table-fixed w-full">
              <TableBody>
                {filteredReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell className="w-[30px]">
                    <Checkbox
                      checked={selectedReports.includes(report.id)}
                      onCheckedChange={(checked) => handleSelectReport(report.id, checked as boolean)}
                      aria-label={`Select ${report.name}`}
                    />
                  </TableCell>
                  <TableCell className="w-[30px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => handleToggleFavorite(report)}
                    >
                      {report.isFavorite ? (
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      ) : (
                        <StarOff className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell className="font-medium w-[150px] truncate">{report.name}</TableCell>
                  <TableCell className="w-auto">{report.description}</TableCell>
                  <TableCell className="w-[120px] truncate">{report.dataSource}</TableCell>
                  <TableCell className="w-[80px]">
                    {report.isTemplate && (
                      <Badge variant="outline">Template</Badge>
                    )}
                  </TableCell>
                  <TableCell className="w-[100px]">{new Date(report.updatedAt).toLocaleDateString()}</TableCell>
                  <TableCell className="w-[80px]">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onRunReport(report)}>
                          <Play className="mr-2 h-4 w-4" />
                          Run Report
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditReport(report)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Report
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleFavorite(report)}>
                          {report.isFavorite ? (
                            <>
                              <StarOff className="mr-2 h-4 w-4" />
                              Remove from Favorites
                            </>
                          ) : (
                            <>
                              <Star className="mr-2 h-4 w-4" />
                              Add to Favorites
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleSaveAsTemplate(report)}>
                          <Copy className="mr-2 h-4 w-4" />
                          Save as Template
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setDeleteReportId(report.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Report
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        </div>
      )}

      {/* Delete Single Report Confirmation Dialog */}
      <AlertDialog open={!!deleteReportId} onOpenChange={(open) => !open && setDeleteReportId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              report and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteReport}
              className="bg-destructive text-destructive-foreground"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Multiple Reports Confirmation Dialog */}
      <AlertDialog open={isDeleteMultipleOpen} onOpenChange={setIsDeleteMultipleOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {selectedReports.length} reports?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              selected reports and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMultipleReports}
              className="bg-destructive text-destructive-foreground"
              disabled={isDeletingMultiple}
            >
              {isDeletingMultiple && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete {selectedReports.length} Reports
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
