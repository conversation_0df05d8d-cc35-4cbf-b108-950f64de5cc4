'use client';

import { useState } from 'react';
import { CustomReport } from '../lib/types';
import { saveCustomReport } from '../lib/custom-reports';
import { useReports } from '../context/ReportContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Copy, Play } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface TemplateReportsListProps {
  onRunReport: (report: CustomReport) => void;
  onEditReport: (report: CustomReport) => void;
}

export function TemplateReportsList({
  onRunReport,
  onEditReport
}: TemplateReportsListProps) {
  const { reports: allReports, isLoading, error: contextError, refreshReports } = useReports();
  const [error, setError] = useState<string | null>(contextError);
  const { toast } = useToast();

  // Filter only template reports
  const templates = allReports.filter(report => report.isTemplate);

  // Handle using a template
  async function handleUseTemplate(template: CustomReport) {
    try {
      // Create a new report based on the template
      const newReport: CustomReport = {
        ...template,
        id: '', // Clear ID to create a new report
        name: `${template.name.replace(' (Template)', '')} - Copy`,
        isTemplate: false,
        isFavorite: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save the new report
      const reportId = await saveCustomReport(newReport);

      // Update the new report with the generated ID
      const savedReport = {
        ...newReport,
        id: reportId
      };

      // Refresh the reports list
      await refreshReports();

      toast({
        title: "Report Created",
        description: "New report created from template",
      });

      // Open the report editor
      onEditReport(savedReport);
    } catch (err) {
      console.error('Error using template:', err);
      setError(err instanceof Error ? err.message : 'Failed to create report from template');
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading templates...</span>
      </div>
    );
  }

  if (templates.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <p className="text-muted-foreground">No report templates found.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Save reports as templates to reuse them later.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => (
          <Card key={template.id} className="flex flex-col">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{template.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {template.description || 'No description'}
              </p>
            </CardHeader>
            <CardContent className="flex-1">
              <div className="text-sm">
                <div className="flex justify-between mb-1">
                  <span className="text-muted-foreground">Data Source:</span>
                  <span>{template.dataSource}</span>
                </div>
                <div className="flex justify-between mb-1">
                  <span className="text-muted-foreground">Fields:</span>
                  <span>{template.fields.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Visualizations:</span>
                  <span>{template.visualizations?.length || 0}</span>
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUseTemplate(template)}
                  className="flex items-center"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Use Template
                </Button>
                <Button
                  size="sm"
                  onClick={() => onRunReport(template)}
                  className="flex items-center"
                >
                  <Play className="h-4 w-4 mr-1" />
                  Run
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
