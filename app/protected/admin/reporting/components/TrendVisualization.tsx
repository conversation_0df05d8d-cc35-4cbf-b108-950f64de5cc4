
'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Loader2, TrendingUp } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { ReportData } from '../lib/types';
import { reportTypes } from '../lib/report-types';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend as RechartsLegend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import regression from 'regression';

// Install regression package if needed:
// npm install regression

// Define interfaces for chart data
interface ChartDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
  fill: boolean;
  borderDash?: number[];
  pointRadius?: number;
}

interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export function TrendVisualization() {
  const [reportType, setReportType] = useState('');
  const [timeRange, setTimeRange] = useState('1-year');
  const [showTrendLine, setShowTrendLine] = useState(true);
  const [showMovingAverage, setShowMovingAverage] = useState(false);
  const [showForecast, setShowForecast] = useState(false);
  const [forecastPeriods, setForecastPeriods] = useState(3);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [trendData, setTrendData] = useState<any | null>(null);

  // Available report types for trend analysis
  const availableReportTypes = reportTypes.filter(report =>
    ['financial', 'performance'].includes(report.category) &&
    report.id.includes('trend') || report.id.includes('performance')
  );

  // Run the trend analysis
  async function runTrendAnalysis() {
    if (!reportType) return;

    setIsLoading(true);
    setError(null);

    try {
      // Find the selected report type
      const selectedReport = reportTypes.find(r => r.id === reportType);
      if (!selectedReport) {
        throw new Error('Report type not found');
      }

      // Determine date range based on time range
      const dateRange = getDateRange(timeRange);

      // Run report
      const result = await selectedReport.fetchData({
        dateRange,
        filters: {
          clientFilter: 'all'
        }
      });

      // Store the result
      setReportData(result);

      // Generate trend data
      const trend = generateTrendData(result);
      setTrendData(trend);
    } catch (err) {
      console.error('Error running trend analysis:', err);
      setError(err instanceof Error ? err.message : 'Failed to run trend analysis');
    } finally {
      setIsLoading(false);
    }
  }

  // Get date range based on time range selection
  function getDateRange(timeRange: string): { start: string, end: string } {
    const now = new Date();
    const end = now.toISOString().split('T')[0];

    let start: Date;

    switch (timeRange) {
      case '1-month':
        start = new Date(now);
        start.setMonth(now.getMonth() - 1);
        break;
      case '3-months':
        start = new Date(now);
        start.setMonth(now.getMonth() - 3);
        break;
      case '6-months':
        start = new Date(now);
        start.setMonth(now.getMonth() - 6);
        break;
      case '1-year':
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 1);
        break;
      case '3-years':
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 3);
        break;
      case '5-years':
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 5);
        break;
      default:
        start = new Date(now);
        start.setFullYear(now.getFullYear() - 1);
    }

    return {
      start: start.toISOString().split('T')[0],
      end
    };
  }

  // Generate trend data from report data
  function generateTrendData(data: ReportData): ChartData | null {
    if (!data.chartData) {
      return null;
    }

    const chartData = data.chartData;

    // Prepare the base chart data
    const trendData: ChartData = {
      labels: chartData.labels,
      datasets: [
        {
          label: 'Actual Data',
          data: chartData.datasets[0].data,
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          fill: false
        }
      ]
    };

    // Add trend line if enabled
    if (showTrendLine) {
      const trendLineData = calculateTrendLine(chartData);
      if (trendLineData) {
        trendData.datasets.push({
          label: 'Trend Line',
          data: trendLineData,
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0
        });
      }
    }

    // Add moving average if enabled
    if (showMovingAverage) {
      const movingAverageData = calculateMovingAverage(chartData.datasets[0].data, 3);
      if (movingAverageData) {
        trendData.datasets.push({
          label: '3-Period Moving Average',
          data: movingAverageData,
          borderColor: 'rgba(153, 102, 255, 1)',
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          fill: false,
          pointRadius: 0
        });
      }
    }

    // Add forecast if enabled
    if (showForecast) {
      const forecastData = calculateForecast(chartData, forecastPeriods);
      if (forecastData) {
        trendData.datasets.push({
          label: 'Forecast',
          data: forecastData.data,
          borderColor: 'rgba(255, 159, 64, 1)',
          backgroundColor: 'rgba(255, 159, 64, 0.2)',
          borderDash: [10, 5],
          fill: false,
          pointRadius: 0
        });

        // Add forecast labels
        trendData.labels = [...chartData.labels, ...forecastData.labels];
      }
    }

    return trendData;
  }

  // Calculate trend line using linear regression
  function calculateTrendLine(chartData: any): number[] | null {
    if (!chartData || !chartData.datasets || chartData.datasets.length === 0) {
      return null;
    }
    
    try {
      // Create data points for regression analysis
      const dataPoints = chartData.labels.map((label: string, index: number) => 
        [index, chartData.datasets[0].data[index] || 0]
      );
      
      // Perform linear regression
      const result = regression.linear(dataPoints);
      
      // Generate trend line points
      return chartData.labels.map((_: any, index: number) => result.predict(index)[1]);
    } catch (error) {
      console.error('Error calculating trend line:', error);
      return null;
    }
  }

  // Calculate moving average
  function calculateMovingAverage(data: number[], periods: number): number[] | null {
    if (!data || data.length < periods) {
      return null;
    }
    
    const result: number[] = [];
    
    // Fill initial values with null (or original data)
    for (let i = 0; i < periods - 1; i++) {
      result.push(data[i]);
    }
    
    // Calculate moving average
    for (let i = periods - 1; i < data.length; i++) {
      let sum = 0;
      for (let j = 0; j < periods; j++) {
        sum += data[i - j];
      }
      result.push(sum / periods);
    }
    
    return result;
  }

  // Calculate forecast
  function calculateForecast(chartData: any, periods: number): { data: number[], labels: string[] } | null {
    if (!chartData || !chartData.datasets || chartData.datasets.length === 0) {
      return null;
    }
    
    try {
      // Create data points for regression analysis
      const dataPoints = chartData.labels.map((label: string, index: number) => 
        [index, chartData.datasets[0].data[index] || 0]
      );
      
      // Perform linear regression
      const result = regression.linear(dataPoints);
      
      // Generate forecast data
      const forecastData: number[] = [];
      const forecastLabels: string[] = [];
      
      // Fill existing data points with null
      for (let i = 0; i < chartData.labels.length; i++) {
        forecastData.push(null as any);
      }
      
      // Generate forecast for future periods
      for (let i = 0; i < periods; i++) {
        const nextIndex = chartData.labels.length + i;
        forecastData.push(result.predict(nextIndex)[1]);
        
        // Generate label for forecast period
        // This is a simple implementation - you might want to format dates properly
        if (chartData.labels[0] && isNaN(Date.parse(chartData.labels[0]))) {
          // If labels are not dates, just increment
          forecastLabels.push(`F${i + 1}`);
        } else {
          // If labels are dates, try to increment the date
          try {
            const lastDate = new Date(chartData.labels[chartData.labels.length - 1]);
            const nextDate = new Date(lastDate);
            nextDate.setMonth(lastDate.getMonth() + (i + 1));
            forecastLabels.push(nextDate.toISOString().split('T')[0]);
          } catch (e) {
            forecastLabels.push(`F${i + 1}`);
          }
        }
      }
      
      return { data: forecastData, labels: forecastLabels };
    } catch (error) {
      console.error('Error calculating forecast:', error);
      return null;
    }
  }

  // Check if a string is a date
  function isDateString(str: string): boolean {
    return !isNaN(Date.parse(str));
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Trend Analysis & Forecasting</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Configuration Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="report-type">Report Type</Label>
              <Select
                value={reportType}
                onValueChange={setReportType}
              >
                <SelectTrigger id="report-type">
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  {availableReportTypes.map(report => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="time-range">Time Range</Label>
              <Select
                value={timeRange}
                onValueChange={setTimeRange}
              >
                <SelectTrigger id="time-range">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-month">Last Month</SelectItem>
                  <SelectItem value="3-months">Last 3 Months</SelectItem>
                  <SelectItem value="6-months">Last 6 Months</SelectItem>
                  <SelectItem value="1-year">Last Year</SelectItem>
                  <SelectItem value="3-years">Last 3 Years</SelectItem>
                  <SelectItem value="5-years">Last 5 Years</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={runTrendAnalysis}
                disabled={!reportType || isLoading}
                className="w-full"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Run Analysis
              </Button>
            </div>
          </div>

          {/* Analysis Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="trend-line"
                checked={showTrendLine}
                onCheckedChange={setShowTrendLine}
              />
              <Label htmlFor="trend-line">Show Trend Line</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="moving-average"
                checked={showMovingAverage}
                onCheckedChange={setShowMovingAverage}
              />
              <Label htmlFor="moving-average">Show Moving Average</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="forecast"
                checked={showForecast}
                onCheckedChange={setShowForecast}
              />
              <Label htmlFor="forecast">Show Forecast</Label>
            </div>
          </div>

          {/* Forecast Periods */}
          {showForecast && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="forecast-periods">Forecast Periods</Label>
                <Select
                  value={forecastPeriods.toString()}
                  onValueChange={(value) => setForecastPeriods(parseInt(value))}
                >
                  <SelectTrigger id="forecast-periods">
                    <SelectValue placeholder="Select periods" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Period</SelectItem>
                    <SelectItem value="3">3 Periods</SelectItem>
                    <SelectItem value="6">6 Periods</SelectItem>
                    <SelectItem value="12">12 Periods</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-destructive/10 text-destructive p-3 rounded-md">
              {error}
            </div>
          )}

          {/* Results Section */}
          {trendData && (
            <div className="space-y-4">
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={trendData.datasets ? trendData.labels.map((label: string, index: number) => {
                      const dataPoint: Record<string, any> = { name: label };
                      trendData.datasets.forEach((dataset: ChartDataset) => {
                        dataPoint[dataset.label] = dataset.data[index];
                      });
                      return dataPoint;
                    }) : []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      label={{ value: 'Period', position: 'insideBottomRight', offset: -10 }}
                    />
                    <YAxis
                      label={{ value: 'Value', angle: -90, position: 'insideLeft' }}
                      tickFormatter={(value: any) => {
                        if (reportType.includes('worth') ||
                            reportType.includes('asset') ||
                            reportType.includes('financial')) {
                          return new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                            maximumFractionDigits: 0
                          }).format(value);
                        }
                        return value.toString();
                      }}
                    />
                    <RechartsTooltip />
                    <RechartsLegend />

                    {trendData.datasets && trendData.datasets.map((dataset: ChartDataset, index: number) => (
                      <Line
                        key={index}
                        type="monotone"
                        dataKey={dataset.label}
                        stroke={dataset.borderColor}
                        fill={dataset.backgroundColor}
                        strokeDasharray={dataset.borderDash ? dataset.borderDash.join(' ') : undefined}
                        dot={dataset.pointRadius === 0 ? false : true}
                        activeDot={{ r: 8 }}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Analysis Summary */}
              {reportData && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Analysis Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Report Type</span>
                          <span>{reportData.reportId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Time Range</span>
                          <span>{getTimeRangeLabel(timeRange)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Data Points</span>
                          <span>{reportData.chartData?.labels.length || 0}</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {showForecast && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Forecast Periods</span>
                            <span>{forecastPeriods}</span>
                          </div>
                        )}
                        {reportData.summary && Object.entries(reportData.summary).slice(0, 3).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-muted-foreground">{formatKey(key)}</span>
                            <span>{formatValue(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to get a human-readable label for the time range
function getTimeRangeLabel(timeRange: string): string {
  switch (timeRange) {
    case '1-month':
      return 'Last Month';
    case '3-months':
      return 'Last 3 Months';
    case '6-months':
      return 'Last 6 Months';
    case '1-year':
      return 'Last Year';
    case '3-years':
      return 'Last 3 Years';
    case '5-years':
      return 'Last 5 Years';
    default:
      return timeRange;
  }
}

// Helper function to format keys for display
function formatKey(key: string): string {
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format values for display
function formatValue(value: any): string {
  if (typeof value === 'number') {
    // Check if it looks like a currency value
    if (value > 100 || value < -100) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    }

    return value.toFixed(2);
  }

  return String(value);
}
