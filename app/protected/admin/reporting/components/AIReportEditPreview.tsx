'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { JoinTable, Filter, Column, SortField, GroupByField } from '../lib/types';

interface AIReportEditPreviewProps {
  config: {
    title: string;
    description: string;
    tableDefinition?: string;
    type: string;
    dataSource: string;
    joinTables?: JoinTable[];
    filters: Filter[];
    columns: Column[] | string[];
    sortBy?: SortField | string;
    sortDirection?: 'asc' | 'desc';
    chartType?: string;
    groupBy?: GroupByField[] | string;
    limit?: number;
    sqlQuery?: string;
  };
  explanation?: string;
  previewData: any[];
}

export function AIReportEditPreview({ config, explanation, previewData }: AIReportEditPreviewProps) {
  // Check if columns is an array of strings or objects
  const isColumnsArray = Array.isArray(config.columns) &&
    (config.columns.length === 0 || typeof config.columns[0] === 'string');

  // Convert columns to a consistent format
  const formattedColumns = isColumnsArray
    ? (config.columns as string[]).map(col => ({ name: col, displayName: col }))
    : (config.columns as Column[]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{config.title}</CardTitle>
          <p className="text-sm text-muted-foreground">{config.description}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-1">Report Type</h4>
              <p className="text-sm">{config.type}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Data Source</h4>
              <p className="text-sm">{config.dataSource}</p>
            </div>
          </div>

          {/* Preview Table */}
          <div>
            <h4 className="text-sm font-medium mb-2">Preview (First 5 rows)</h4>
            {previewData.length > 0 ? (
              <div className="border rounded-md overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {Object.keys(previewData[0]).map((column) => (
                        <TableHead key={column}>{column}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {previewData.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {Object.values(row).map((value: any, cellIndex) => (
                          <TableCell key={cellIndex}>
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-4 border rounded-md">
                <p className="text-sm text-muted-foreground">No preview data available</p>
              </div>
            )}
          </div>

          <div>
            <h4 className="text-sm font-medium mb-1">Columns</h4>
            <div className="flex flex-wrap gap-1">
              {formattedColumns.map((col, index) => (
                <Badge key={index} variant="outline">
                  {typeof col === 'string' ? col : col.displayName || col.name}
                </Badge>
              ))}
            </div>
          </div>

          {config.joinTables && config.joinTables.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-1">Joins</h4>
              <div className="flex flex-wrap gap-1">
                {config.joinTables.map((join, index) => (
                  <Badge key={index} variant="outline">
                    {join.table} ({join.type || 'JOIN'})
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {config.filters && config.filters.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-1">Filters</h4>
              <div className="flex flex-wrap gap-1">
                {config.filters.map((filter, index) => (
                  <Badge key={index} variant="outline">
                    {filter.field} {filter.operator} {filter.value}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {config.groupBy && (
            <div>
              <h4 className="text-sm font-medium mb-1">Group By</h4>
              <div className="flex flex-wrap gap-1">
                {Array.isArray(config.groupBy) ? (
                  config.groupBy.map((group, index) => (
                    <Badge key={index} variant="outline">
                      {typeof group === 'string' ? group : group.field}
                    </Badge>
                  ))
                ) : (
                  <Badge variant="outline">{config.groupBy}</Badge>
                )}
              </div>
            </div>
          )}

          {config.sortBy && (
            <div>
              <h4 className="text-sm font-medium mb-1">Sort By</h4>
              <div className="flex flex-wrap gap-1">
                <Badge variant="outline">
                  {typeof config.sortBy === 'string' ? config.sortBy : config.sortBy.field}{' '}
                  {config.sortDirection || (typeof config.sortBy !== 'string' && config.sortBy.direction) || 'asc'}
                </Badge>
              </div>
            </div>
          )}

          {config.chartType && (
            <div>
              <h4 className="text-sm font-medium mb-1">Chart Type</h4>
              <Badge variant="outline">{config.chartType}</Badge>
            </div>
          )}

          {config.limit && (
            <div>
              <h4 className="text-sm font-medium mb-1">Limit</h4>
              <Badge variant="outline">{config.limit} rows</Badge>
            </div>
          )}

          {(config.sqlQuery || (config.tableDefinition && config.tableDefinition.includes('SQL Query:'))) && (
            <div>
              <h4 className="text-sm font-medium mb-1 flex items-center gap-2">
                SQL Query
                {explanation && explanation.includes('SQL query was validated and is valid') && (
                  <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
                    Validated
                  </Badge>
                )}
                {explanation && explanation.includes('SQL query was validated and could not be validated') && (
                  <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
                    Validation Failed
                  </Badge>
                )}
              </h4>
              <div className="bg-muted p-2 rounded-md text-xs overflow-x-auto">
                <pre>{config.sqlQuery || (config.tableDefinition && config.tableDefinition.includes('SQL Query:') ?
                  config.tableDefinition.match(/SQL Query: ([\s\S]*?)(?:\n\n|$)/)?.[1]?.trim() :
                  'No SQL query available')}</pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
