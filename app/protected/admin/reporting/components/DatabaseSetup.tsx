'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Database, CheckCircle2, XCircle } from 'lucide-react';
import { setupAdvancedReportingDatabase, isAdvancedReportingDatabaseSetup } from '../lib/setup-database';

interface DatabaseSetupProps {
  onSetupComplete?: () => void;
}

export function DatabaseSetup({ onSetupComplete }: DatabaseSetupProps = {}) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSetup, setIsSetup] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if the database is set up on component mount
  useEffect(() => {
    checkDatabaseSetup();
  }, []);

  // Check if the database is set up
  async function checkDatabaseSetup() {
    setIsLoading(true);
    setError(null);

    try {
      const isSetup = await isAdvancedReportingDatabaseSetup();
      setIsSetup(isSetup);
    } catch (err) {
      console.error('Error checking database setup:', err);
      setError(err instanceof Error ? err.message : 'Failed to check database setup');
    } finally {
      setIsLoading(false);
    }
  }

  // Set up the database
  async function handleSetupDatabase() {
    setIsSettingUp(true);
    setError(null);

    try {
      const success = await setupAdvancedReportingDatabase();
      setIsSetup(success);

      // Call the onSetupComplete callback if provided and setup was successful
      if (success && onSetupComplete) {
        onSetupComplete();
      }
    } catch (err) {
      console.error('Error setting up database:', err);
      setError(err instanceof Error ? err.message : 'Failed to set up database');
    } finally {
      setIsSettingUp(false);
    }
  }



  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Checking database setup...</p>
        </CardContent>
      </Card>
    );
  }

  if (isSetup) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <CheckCircle2 className="h-8 w-8 text-green-500 mb-4" />
          <p className="text-muted-foreground">Advanced reporting database is set up and ready to use.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Database Setup Required</CardTitle>
        <CardDescription>
          The advanced reporting features require additional database tables and functions.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center space-x-4">
          <Database className="h-12 w-12 text-primary" />
          <div>
            <h3 className="text-lg font-medium">Advanced Reporting Database</h3>
            <p className="text-sm text-muted-foreground">
              This will create the necessary tables and functions for custom reports,
              comparative analysis, and trend visualization.
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleSetupDatabase}
          disabled={isSettingUp}
          className="w-full"
        >
          {isSettingUp && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Set Up Database
        </Button>
      </CardFooter>
    </Card>
  );
}
