
'use client';

import { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ReportData } from '../lib/types';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend as Re<PERSON>rtsLegend,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface ReportResultChartProps {
  reportData: ReportData;
  type?: 'bar' | 'line' | 'pie' | 'area' | 'card';
  height?: number;
}

// Define colors for charts
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe',
  '#00c49f', '#ffbb28', '#ff8042', '#a4de6c', '#d0ed57'
];

export function ReportResultChart({
  reportData,
  type,
  height = 400
}: ReportResultChartProps) {
  // Use the recommended chart type if none is specified
  const chartType = type || reportData.recommendedChartType || 'bar';

  // Check if we have chart data
  if (!reportData.chartData) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center h-[400px]">
          <p className="text-muted-foreground">No chart data available</p>
        </CardContent>
      </Card>
    );
  }

  // Handle card type (summary cards)
  if (chartType === 'card') {
    // Extract summary data
    const summary = reportData.summary || {};

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(summary).map(([key, value]) => {
          // Skip complex objects or arrays
          if (typeof value === 'object') return null;

          // Format the key for display
          const displayKey = key
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/^./, str => str.toUpperCase()); // Capitalize first letter

          // Format the value
          let displayValue = value;
          if (typeof value === 'number') {
            // Format as currency if it looks like a dollar amount
            if (key.toLowerCase().includes('value') ||
                key.toLowerCase().includes('worth') ||
                key.toLowerCase().includes('asset') ||
                key.toLowerCase().includes('liability')) {
              displayValue = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                maximumFractionDigits: 0
              }).format(value as number);
            }
            // Format as percentage if it looks like a percentage
            else if (key.toLowerCase().includes('percentage') ||
                    key.toLowerCase().includes('rate')) {
              displayValue = `${(value as number).toFixed(1)}%`;
            }
            // Format as number with commas for other numbers
            else {
              displayValue = new Intl.NumberFormat('en-US').format(value as number);
            }
          }

          return (
            <Card key={key} className="overflow-hidden">
              <CardContent className="p-6">
                <h3 className="text-sm font-medium text-muted-foreground">{displayKey}</h3>
                <p className="text-2xl font-bold mt-2">{displayValue}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  }

  // Format value based on label and type
  const formatValue = (value: number, label: string): string => {
    // Format as currency if it looks like a dollar amount
    if (label.toLowerCase().includes('value') ||
        label.toLowerCase().includes('worth') ||
        label.toLowerCase().includes('asset') ||
        label.toLowerCase().includes('liability')) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0
      }).format(value);
    }
    // Format as percentage if it looks like a percentage
    else if (label.toLowerCase().includes('percentage') ||
            label.toLowerCase().includes('rate')) {
      return `${value.toFixed(1)}%`;
    }
    // Default formatting
    return new Intl.NumberFormat('en-US').format(value);
  };

  // Transform Chart.js data format to Recharts format
  const transformData = () => {
    if (!reportData.chartData) return [];

    const { labels, datasets } = reportData.chartData;

    return labels.map((label, index) => {
      const dataPoint: Record<string, any> = { name: label };

      datasets.forEach(dataset => {
        dataPoint[dataset.label] = dataset.data[index];
      });

      return dataPoint;
    });
  };

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-2 rounded shadow-md">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {formatValue(entry.value, entry.name)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Render the appropriate chart type
  return (
    <div style={{ height: `${height}px` }}>
      <ResponsiveContainer width="100%" height="100%">
        {(() => {
          switch (chartType) {
            case 'bar':
              return (
                <BarChart data={transformData()} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis
                    tickFormatter={(value) => {
                      const datasets = reportData.chartData?.datasets || [];
                      if (datasets.length > 0) {
                        const label = datasets[0].label || '';
                        return formatValue(value, label).replace('$', '');
                      }
                      return value.toString();
                    }}
                  />
                  <RechartsTooltip content={<CustomTooltip />} />
                  <RechartsLegend />
                  {reportData.chartData?.datasets.map((dataset, index) => (
                    <Bar
                      key={index}
                      dataKey={dataset.label}
                      fill={typeof dataset.backgroundColor === 'string' ? dataset.backgroundColor : COLORS[index % COLORS.length]}
                      stroke={typeof dataset.borderColor === 'string' ? dataset.borderColor : undefined}
                    />
                  ))}
                </BarChart>
              );
            case 'line':
              return (
                <LineChart data={transformData()} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis
                    tickFormatter={(value) => {
                      const datasets = reportData.chartData?.datasets || [];
                      if (datasets.length > 0) {
                        const label = datasets[0].label || '';
                        return formatValue(value, label).replace('$', '');
                      }
                      return value.toString();
                    }}
                  />
                  <RechartsTooltip content={<CustomTooltip />} />
                  <RechartsLegend />
                  {reportData.chartData?.datasets.map((dataset, index) => (
                    <Line
                      key={index}
                      type="monotone"
                      dataKey={dataset.label}
                      stroke={typeof dataset.borderColor === 'string' ? dataset.borderColor : COLORS[index % COLORS.length]}
                      fill={typeof dataset.backgroundColor === 'string' ? dataset.backgroundColor : undefined}
                      dot={true}
                      activeDot={{ r: 8 }}
                    />
                  ))}
                </LineChart>
              );
            case 'area':
              return (
                <AreaChart data={transformData()} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis
                    tickFormatter={(value) => {
                      const datasets = reportData.chartData?.datasets || [];
                      if (datasets.length > 0) {
                        const label = datasets[0].label || '';
                        return formatValue(value, label).replace('$', '');
                      }
                      return value.toString();
                    }}
                  />
                  <RechartsTooltip content={<CustomTooltip />} />
                  <RechartsLegend />
                  {reportData.chartData?.datasets.map((dataset, index) => (
                    <Area
                      key={index}
                      type="monotone"
                      dataKey={dataset.label}
                      stroke={typeof dataset.borderColor === 'string' ? dataset.borderColor : COLORS[index % COLORS.length]}
                      fill={typeof dataset.backgroundColor === 'string' ? dataset.backgroundColor : COLORS[index % COLORS.length]}
                    />
                  ))}
                </AreaChart>
              );
            case 'pie':
              return (
                <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                  <RechartsTooltip content={<CustomTooltip />} />
                  <RechartsLegend />
                  <Pie
                    data={reportData.chartData?.labels.map((label, index) => ({
                      name: label,
                      value: reportData.chartData?.datasets[0].data[index] || 0
                    }))}
                    cx="50%"
                    cy="50%"
                    outerRadius={height / 3}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {reportData.chartData?.labels.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              );
            default:
              // Fallback to bar chart if chartType is somehow invalid
              return (
                <BarChart data={transformData()} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip content={<CustomTooltip />} />
                  <RechartsLegend />
                  {reportData.chartData?.datasets.map((dataset, index) => (
                    <Bar
                      key={index}
                      dataKey={dataset.label}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </BarChart>
              );
          }
        })()}
      </ResponsiveContainer>
    </div>
  );
}
