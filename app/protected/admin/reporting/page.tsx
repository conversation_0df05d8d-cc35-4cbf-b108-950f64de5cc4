'use client';

import { Suspense, useState } from 'react';
import { MigrationRunner } from './components/MigrationRunner';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import { Toaster } from '@/components/ui/toaster';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  FileText,
  Loader2,
  Clock,
  Star,
  Copy,
  Sparkles
} from 'lucide-react';
import { ReportViewer } from './components/ReportViewer';
import { ScheduledReportsList } from './components/ScheduledReportsList';
import { ScheduleReportModal } from './components/ScheduleReportModal';
import { CustomReportBuilder } from './components/CustomReportBuilder';
import { CustomReportsList } from './components/CustomReportsList';
import { FavoriteReportsList } from './components/FavoriteReportsList';
import { TemplateReportsList } from './components/TemplateReportsList';
import { ComparativeAnalysis } from './components/ComparativeAnalysis';
import { TrendVisualization } from './components/TrendVisualization';
import { CreateCustomReportModal } from './components/CreateCustomReportModal';
import { EditReportModal } from './components/EditReportModal';
import { CustomReport } from './lib/types';
import { saveCustomReport, runCustomReport } from './lib/custom-reports';
import { ReportProvider, useReports } from './context/ReportContext'; // Import useReports

// No pre-configured report types - using custom reports instead

function ReportingContent() {
  const [activeTab, setActiveTab] = useState('custom');
  const { refreshReports } = useReports(); // Get refreshReports from context
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);
  const [isCustomReportBuilderOpen, setIsCustomReportBuilderOpen] = useState(false);
  const [isCreateCustomReportModalOpen, setIsCreateCustomReportModalOpen] = useState(false);
  const [isEditReportModalOpen, setIsEditReportModalOpen] = useState(false);
  const [selectedCustomReport, setSelectedCustomReport] = useState<CustomReport | null>(null);
  const [viewingReport, setViewingReport] = useState<any | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleCreateCustomReport = () => {
    setSelectedCustomReport(null);
    setIsCustomReportBuilderOpen(true);
  };

  const handleCreateAIAssistedReport = () => {
    // Open the AI-assisted custom report creation modal
    setIsCreateCustomReportModalOpen(true);
  };

  const handleEditCustomReport = (report: CustomReport) => {
    setSelectedCustomReport(report);
    setIsCustomReportBuilderOpen(true);
  };

  const handleRunCustomReport = async (report: CustomReport) => {
    try {
      const result = await runCustomReport(report);
      setViewingReport({
        ...report,
        type: 'custom',
        customReportResult: result
      });
    } catch (error) {
      console.error('Error running custom report:', error);
      toast.error(`Error running report: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleCloseReport = () => {
    setViewingReport(null);
  };

  const handleEditViewingReport = async () => {
    if (viewingReport && viewingReport.type === 'custom') {
      try {
        // Import the getCustomReportById function
        const { getCustomReportById } = await import('./lib/custom-reports');

        // Load the full report from the database to ensure we have all fields
        console.log('REPORT FLOW - Loading full report from database for editing:', viewingReport.id);
        const fullReport = await getCustomReportById(viewingReport.id);
        console.log('REPORT FLOW - Loaded full report:', fullReport);
        console.log('REPORT FLOW - Current viewing report:', viewingReport);

        // Set the full report for editing
        setSelectedCustomReport(fullReport);
        setIsEditReportModalOpen(true);
      } catch (error) {
        console.error('Error loading report for editing:', error);
        toast.error(`Error loading report: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  };

  const handleSaveEditedReport = async (updatedReport: CustomReport) => {
    try {
      console.log('REPORT FLOW - Saving edited report:', updatedReport);

      // Save the report to the database
      await saveCustomReport(updatedReport);
      setIsEditReportModalOpen(false);

      // Show success message
      toast.success(`Report "${updatedReport.name}" updated successfully`);

      // Fetch the report from the database to ensure we have the latest version
      console.log('REPORT FLOW - Fetching updated report from database');
      const { getCustomReportById } = await import('./lib/custom-reports');
      const freshReport = await getCustomReportById(updatedReport.id);
      console.log('REPORT FLOW - Fresh report from database:', freshReport);

      // Force a rebuild of the SQL query if this is an AI-generated report
      if (freshReport.tableDefinition && freshReport.tableDefinition.includes('SQL Query:')) {
        console.log('REPORT FLOW - Updating SQL query for AI-generated report');

        try {
          // Ensure groupBy is an array
          if (!freshReport.groupBy || !Array.isArray(freshReport.groupBy)) {
            freshReport.groupBy = [];
          }

          const { buildSqlQuery } = await import('./lib/custom-reports-utils');
          const newSql = buildSqlQuery(freshReport);

          // Update the tableDefinition with the new SQL query
          freshReport.tableDefinition = freshReport.tableDefinition.replace(
            /SQL Query: ([\s\S]*?)(?:\n\n|$)/,
            `SQL Query: ${newSql}\n\n`
          );

          // Save the updated report with the new SQL query
          await saveCustomReport(freshReport);
          console.log('REPORT FLOW - Updated SQL query in database:', newSql);
        } catch (error) {
          console.error('Error updating SQL query in page.tsx:', error);
          toast.error(`Error updating SQL query: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // Run the updated report to show the changes
      console.log('REPORT FLOW - Running updated report to show changes');
      const result = await runCustomReport(freshReport);
      console.log('REPORT FLOW - Updated report result:', result);
      console.log('REPORT FLOW - Updated report result columns:', result.columns);

      // Create a new object to ensure React detects the change
      const updatedViewingReport = {
        ...freshReport,
        type: 'custom',
        customReportResult: result
      };

      console.log('REPORT FLOW - Setting new viewing report:', updatedViewingReport);

      // Update the viewing report with the result
      setViewingReport(updatedViewingReport);

      // Refresh the reports list using the context hook
      await refreshReports();
    } catch (error) {
      console.error('Error saving edited report:', error);
      toast.error(`Error saving report: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleSaveCustomReport = async (report: CustomReport) => {
    try {
      await saveCustomReport(report);
      setIsCustomReportBuilderOpen(false);

      // Show success message
      toast.success(`Report "${report.name}" saved successfully`);

      // Refresh the reports list using the context hook
      await refreshReports();
    } catch (error) {
      console.error('Error saving custom report:', error);
      toast.error(`Error saving report: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Helper function to map AI-generated operator to FilterCondition operator
  const mapOperator = (operator: string): 'equals' | 'notEquals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in' => {
    switch (operator) {
      case 'equals':
        return 'equals';
      case 'notEquals':
        return 'notEquals';
      case 'greaterThan':
        return 'greaterThan';
      case 'lessThan':
        return 'lessThan';
      case 'contains':
        return 'contains';
      default:
        return 'equals'; // Default fallback
    }
  };

  const handleSaveAIGeneratedReport = async (config: any) => {
    try {
      // Extract column names and handle both string[] and object[] formats
      const extractColumns = () => {
        if (!config.columns || config.columns.length === 0) return [];

        // If columns is an array of strings
        if (typeof config.columns[0] === 'string') {
          return config.columns.map((column: string) => ({
            id: crypto.randomUUID(),
            name: column,
            sourceField: column,
            displayName: column,
            dataType: 'string',
            isVisible: true
          }));
        }

        // If columns is an array of objects
        return config.columns.map((column: any) => ({
          id: crypto.randomUUID(),
          name: column.name,
          sourceField: `${column.table}.${column.name}${column.aggregate ? ` ${column.aggregate}` : ''}`,
          displayName: column.alias || column.name,
          dataType: 'string', // Default to string
          isVisible: true
        }));
      };

      // Extract filters and handle table qualification
      const extractFilters = () => {
        if (!config.filters || config.filters.length === 0) return [];

        return config.filters.map((filter: any) => ({
          id: crypto.randomUUID(),
          field: filter.table ? `${filter.table}.${filter.field}` : filter.field,
          operator: mapOperator(filter.operator),
          value: filter.value
        }));
      };

      // Extract group by fields
      const extractGroupBy = () => {
        if (!config.groupBy) return [];

        // If groupBy is a string
        if (typeof config.groupBy === 'string') {
          return [config.groupBy];
        }

        // If groupBy is an array of objects
        return config.groupBy.map((group: any) =>
          group.table ? `${group.table}.${group.field}` : group.field
        );
      };

      // Extract sort by fields
      const extractSortBy = () => {
        if (!config.sortBy) return [];

        // If sortBy is a string
        if (typeof config.sortBy === 'string') {
          return [{
            field: config.sortBy,
            direction: config.sortDirection || 'asc'
          }];
        }

        // If sortBy is an object
        const sortField = config.sortBy.table
          ? `${config.sortBy.table}.${config.sortBy.field}${config.sortBy.aggregate ? ` ${config.sortBy.aggregate}` : ''}`
          : config.sortBy.field;

        return [{
          field: sortField,
          direction: config.sortDirection || 'asc'
        }];
      };

      // Extract visualization
      const extractVisualization = () => {
        if (!config.chartType) return [];

        // Determine x and y axes based on groupBy and columns
        let xAxis = '';
        if (config.groupBy) {
          if (typeof config.groupBy === 'string') {
            xAxis = config.groupBy;
          } else if (config.groupBy.length > 0) {
            xAxis = config.groupBy[0].table
              ? `${config.groupBy[0].table}.${config.groupBy[0].field}`
              : config.groupBy[0].field;
          }
        }

        let yAxis = '';
        let seriesField = '';
        if (config.columns && config.columns.length > 0) {
          if (typeof config.columns[0] === 'string') {
            yAxis = config.columns[0];
            seriesField = config.columns[0];
          } else {
            // Find a numeric column, preferably with an aggregate function
            const numericColumn = config.columns.find((col: any) => col.aggregate) || config.columns[0];
            yAxis = numericColumn.alias ||
              `${numericColumn.table}.${numericColumn.name}${numericColumn.aggregate ? ` ${numericColumn.aggregate}` : ''}`;
            seriesField = numericColumn.alias ||
              `${numericColumn.table}.${numericColumn.name}${numericColumn.aggregate ? ` ${numericColumn.aggregate}` : ''}`;
          }
        }

        return [{
          id: crypto.randomUUID(),
          type: config.chartType as 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'heatmap',
          title: `AI-Generated: ${config.title}`, // Mark as AI-generated for special handling
          xAxis: xAxis,
          yAxis: yAxis,
          series: [{
            field: seriesField,
            name: seriesField,
            color: '#4f46e5' // Default indigo color
          }]
        }];
      };

      // Convert the AI-generated config to a CustomReport format
      const customReport: CustomReport = {
        id: '',
        name: config.title,
        description: config.description || '',
        tableDefinition: `AI-Generated Report\n${config.sqlQuery ? `\nSQL Query: ${config.sqlQuery}` : ''}\n${config.joinTables && config.joinTables.length > 0 ? `\nJoins: ${JSON.stringify(config.joinTables)}` : ''}\n${config.limit ? `\nLimit: ${config.limit}` : ''}`,
        dataSource: config.dataSource,
        fields: extractColumns(),
        filters: extractFilters(),
        groupBy: extractGroupBy(),
        sortBy: extractSortBy(),
        visualizations: extractVisualization(),
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: '',
        isTemplate: false,
        isFavorite: false
      };

      await saveCustomReport(customReport);
      setIsCreateCustomReportModalOpen(false);

      // Switch to the custom reports tab to show the newly created report
      setActiveTab('custom');

      // Show success message
      toast.success(`AI-generated report "${config.title}" created successfully`);

      // Refresh the reports list using the context hook
      await refreshReports();

      // Track AI usage for analytics
      console.log('Report created with AI assistance', {
        reportName: config.title,
        reportType: config.type,
        dataSource: config.dataSource,
        chartType: config.chartType,
        hasJoins: config.joinTables && config.joinTables.length > 0
      });
    } catch (error) {
      console.error('Error saving AI-generated report:', error);
      toast.error('Failed to save AI-generated report');
    }
  };



  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px] overflow-hidden flex flex-col">
          {!viewingReport && (
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Reporting</CardTitle>
                  <CardDescription>
                    View and analyze data about your organization and clients
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  {activeTab === 'custom' && (
                    <Input
                      placeholder="Search reports..."
                      value={searchTerm}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                      className="w-64"
                    />
                  )}
                  <Button variant="outline" className="flex items-center gap-2" onClick={handleCreateCustomReport}>
                    <FileText className="h-4 w-4" />
                    Create Custom Report
                  </Button>
                  <Button className="flex items-center gap-2" onClick={handleCreateAIAssistedReport}>
                    <Sparkles className="h-4 w-4" />
                    AI-Assisted Report
                  </Button>
                </div>
              </div>
            </CardHeader>
          )}

          {/* Report Scheduling Modal */}
          <ScheduleReportModal
            reportId=""
            reportName=""
            isOpen={isScheduleModalOpen}
            onClose={() => setIsScheduleModalOpen(false)}
            onSchedule={(id) => {
              console.log('Schedule created with ID:', id);
              setIsScheduleModalOpen(false);
              setActiveTab('scheduled');
            }}
          />

          {/* Custom Report Builder Modal */}
          {isCustomReportBuilderOpen && (
            <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
              <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-5xl translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
                <CustomReportBuilder
                  initialReport={selectedCustomReport || undefined}
                  onSave={handleSaveCustomReport}
                  onClose={() => setIsCustomReportBuilderOpen(false)}
                />
              </div>
            </div>
          )}

          {/* AI-Assisted Custom Report Creation Modal */}
          {isCreateCustomReportModalOpen && (
            <CreateCustomReportModal
              onClose={() => setIsCreateCustomReportModalOpen(false)}
              onSave={handleSaveAIGeneratedReport}
            />
          )}

          {/* Edit Report Modal */}
          {isEditReportModalOpen && selectedCustomReport && (
            <EditReportModal
              report={selectedCustomReport}
              onClose={() => setIsEditReportModalOpen(false)}
              onSave={handleSaveEditedReport}
            />
          )}

          {/* Main Content - Show either the report list or the report viewer */}
          {viewingReport ? (
            <CardContent className="flex-1 overflow-x-auto p-0 h-full min-w-0">
              <ReportViewer
                report={viewingReport}
                onClose={handleCloseReport}
                onEdit={viewingReport.type === 'custom' ? handleEditViewingReport : undefined}
              />
            </CardContent>
          ) : (
          <CardContent className="flex-1 overflow-hidden flex flex-col">
            <Tabs defaultValue="custom" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="custom" className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  <span>Custom Reports</span>
                </TabsTrigger>
                <TabsTrigger value="favorites" className="flex items-center gap-1">
                  <Star className="h-4 w-4" />
                  <span>Favorites</span>
                </TabsTrigger>
                <TabsTrigger value="templates" className="flex items-center gap-1">
                  <Copy className="h-4 w-4" />
                  <span>Templates</span>
                </TabsTrigger>
                <TabsTrigger value="scheduled" className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>Scheduled</span>
                </TabsTrigger>
              </TabsList>

              {/* Custom Reports Tab */}
              <TabsContent value="custom" className="flex-1 overflow-auto">
                <CustomReportsList
                  onCreateReport={handleCreateCustomReport}
                  onEditReport={handleEditCustomReport}
                  onRunReport={handleRunCustomReport}
                  searchTerm={searchTerm}
                />
              </TabsContent>

              {/* Favorites Tab */}
              <TabsContent value="favorites" className="flex-1 overflow-auto">
                <FavoriteReportsList
                  onEditReport={handleEditCustomReport}
                  onRunReport={handleRunCustomReport}
                />
              </TabsContent>

              {/* Templates Tab */}
              <TabsContent value="templates" className="flex-1 overflow-auto">
                <TemplateReportsList
                  onEditReport={handleEditCustomReport}
                  onRunReport={handleRunCustomReport}
                />
              </TabsContent>

              {/* Scheduled Reports Tab */}
              <TabsContent value="scheduled" className="flex-1 overflow-auto">
                <ScheduledReportsList />
              </TabsContent>

              {/* Analysis Tab */}
              <TabsContent value="analysis" className="flex-1 overflow-auto">
                <Tabs defaultValue="comparative" className="w-full">
                  <TabsList className="grid grid-cols-2 mb-4">
                    <TabsTrigger value="comparative">Comparative Analysis</TabsTrigger>
                    <TabsTrigger value="trend">Trend Analysis</TabsTrigger>
                  </TabsList>

                  <TabsContent value="comparative">
                    <ComparativeAnalysis />
                  </TabsContent>

                  <TabsContent value="trend">
                    <TrendVisualization />
                  </TabsContent>
                </Tabs>
              </TabsContent>
            </Tabs>
          </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}

export default function ReportingPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading reporting...</span>
    </div>}>
      {/* Run database migrations */}
      <MigrationRunner />

      {/* Main content */}
      <ReportProvider>
        <ReportingContent />
      </ReportProvider>
      <Toaster />
    </Suspense>
  );
}
