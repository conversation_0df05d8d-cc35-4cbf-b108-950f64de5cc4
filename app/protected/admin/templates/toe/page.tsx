'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Loader2, Plus, Trash2, ArrowLeft, Edit, Save, GripVertical, FileSignature } from 'lucide-react';
import { useRouter } from 'next/navigation';
import SearchParamsWrapper from './SearchParamsWrapper';
import { createClient } from '@/utils/supabase/client';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Placeholder from '@tiptap/extension-placeholder';
import TOEEditorToolbar from '@/components/tiptap/TOEEditorToolbar';
import { toast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

// Section types
type SectionType =
  | 'text'
  | 'checkbox'
  | 'signature'
  | 'fee'
  | 'client_info'
  | 'adviser_info'
  | 'date'
  | 'terms'
  | 'disclaimer';

// Layout types
type LayoutType = 'full' | 'column';

// Section interface
interface TOESection {
  id: string;
  type: SectionType;
  title: string;
  content: any;
  layout?: LayoutType;
}

// Sortable section item component
interface SortableSectionProps {
  id: string;
  section: TOESection;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
}

const SortableSection = ({ id, section, isSelected, onSelect, onDelete }: SortableSectionProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center p-3 mb-2 rounded-md border ${isSelected ? 'bg-primary/10 border-primary' : 'bg-white hover:bg-gray-50'}`}
      onClick={onSelect}
    >
      <div {...attributes} {...listeners} className="cursor-grab p-1 mr-2">
        <GripVertical className="h-4 w-4 text-gray-400" />
      </div>
      <div className="flex-1">
        <p className="font-medium">{section.title}</p>
        <p className="text-xs text-gray-500 capitalize">{section.type.replace('_', ' ')} Section</p>
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="text-red-500 hover:bg-red-50"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

// Function to render a preview of a section
const renderSectionPreview = (section: TOESection) => {
  // Create a wrapper div with appropriate class based on layout
  const wrapperClass = section.layout === 'column' ? 'w-full' : 'w-full';

  const content = (() => {
    switch (section.type) {
      case 'text':
        return (
          <div className="prose max-w-none">
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: section.content?.html || '' }} className="whitespace-pre-wrap text-sm indent-2" />
          </div>
        );

      case 'checkbox':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div className="space-y-2">
              {section.content?.options?.map((option: any) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <input type="checkbox" disabled checked={option.checked} className="h-4 w-4 rounded border-gray-300" />
                  <span>{option.label}</span>
                </div>
              ))}
            </div>
          </div>
        );

      case 'signature':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <p className="text-sm text-gray-600">{section.content?.text || ''}</p>

            {section.content?.showPartner ? (
              // Show both signatures side by side
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm font-medium mb-1">Main Client</p>
                  <div className="border-b border-dashed border-gray-400 h-20 flex items-center justify-center bg-gray-50">
                    <span className="text-gray-400 text-sm">Signature will appear here</span>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium mb-1">Partner</p>
                  <div className="border-b border-dashed border-gray-400 h-20 flex items-center justify-center bg-gray-50">
                    <span className="text-gray-400 text-sm">Signature will appear here</span>
                  </div>
                </div>
              </div>
            ) : (
              // Show single signature
              <div className="border-b border-dashed border-gray-400 h-20 mt-2 flex items-center justify-center bg-gray-50">
                <span className="text-gray-400 text-sm">Signature will appear here</span>
              </div>
            )}
          </div>
        );

      case 'fee':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div className="border border-dashed border-gray-300 p-4 bg-gray-50 text-gray-500 text-sm italic">
              {section.content?.showOneOff && (
                <p className="mb-2">One-off advice fee will be entered when generating the document</p>
              )}
              {section.content?.showOngoing && (
                <p>
                  Ongoing advice fee ({section.content.ongoingType === 'percentage' ? '% of assets' : '$ per month'})
                  will be entered when generating the document
                </p>
              )}
              {!section.content?.showOneOff && !section.content?.showOngoing && (
                <p>No fee types selected</p>
              )}
            </div>
          </div>
        );

      case 'terms':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div className="border border-dashed border-gray-300 p-4 bg-gray-50 text-gray-500 text-sm italic">
              Terms will need to be entered here when generating the document
            </div>
          </div>
        );

      case 'disclaimer':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div className="border border-dashed border-gray-300 p-4 bg-gray-50 text-gray-600 text-sm whitespace-pre-wrap mb-2">
              {section.content?.text || 'Disclaimer text will appear here'}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <input type="checkbox" disabled className="h-4 w-4 rounded border-gray-300" />
              <span className="text-sm">I confirm I have read and agree to the above</span>
            </div>
            {section.content?.required && (
              <p className="text-xs text-red-500 mt-1">* Required</p>
            )}
            {section.content?.useAdviserInfo && (
              <p className="text-xs text-blue-500 mt-1">* Will use adviser and organization information</p>
            )}
          </div>
        );

      case 'client_info':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            {section.content?.showMain && (
              <p className="text-lg font-semibold text-gray-800">[Main Client Name]</p>
            )}
            {section.content?.showPartner && (
              <p className="text-lg font-semibold text-gray-800">[Partner Name]</p>
            )}
            {!section.content?.showMain && !section.content?.showPartner && (
              <p className="text-lg font-semibold text-gray-800">[Client Name]</p>
            )}
          </div>
        );

      case 'adviser_info':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <p className="text-lg font-semibold text-gray-800">[Adviser Name]</p>
            <p className="text-sm text-gray-600">[Organisation Name]</p>
          </div>
        );

      case 'date':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <p className="text-gray-800">[Current Date]</p>
          </div>
        );

      default:
        return null;
    }
  })();

  return <div className={wrapperClass}>{content}</div>;
};

// Main component
export default function TOETemplateEditor() {
  const router = useRouter();
  const [templateIdParam, setTemplateIdParam] = useState<string | null>(null);

  // Callback function to receive search params
  const handleSearchParams = (templateId: string | null) => {
    setTemplateIdParam(templateId);
  };

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [templateTitle, setTemplateTitle] = useState('New Terms of Engagement Template');
  const [templateId, setTemplateId] = useState<string | null>(null);
  const [sections, setSections] = useState<TOESection[]>([]);
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);
  const [selectedSection, setSelectedSection] = useState<TOESection | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // TipTap editor for text sections
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Placeholder.configure({
        placeholder: 'Start typing content...',
      }),
    ],
    editorProps: {
      attributes: {
        class: 'prose focus:outline-none',
        spellcheck: 'false',
      },
    },
    content: selectedSection?.type === 'text' ? selectedSection.content?.html || '' : '',
    onUpdate: ({ editor }) => {
      if (selectedSection?.type === 'text') {
        updateSectionContent({ html: editor.getHTML() });
      }
    },
  });

  // Update editor content when selected section changes
  useEffect(() => {
    if (editor && selectedSection?.type === 'text') {
      editor.commands.setContent(selectedSection.content?.html || '', false, { preserveWhitespace: 'full' });
    }
  }, [selectedSection, editor]);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load template data if templateId is provided
  useEffect(() => {
    if (templateIdParam) {
      setIsLoading(true);
      const loadTemplateData = async () => {
        try {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('templates')
            .select('*')
            .eq('id', templateIdParam)
            .single();

          if (error) throw error;

          if (data) {
            setTemplateId(data.id);
            setTemplateTitle(data.title);

            // Parse content if it exists
            if (data.content) {
              try {
                const parsedContent = JSON.parse(data.content);
                if (Array.isArray(parsedContent)) {
                  setSections(parsedContent);
                  if (parsedContent.length > 0) {
                    setSelectedSectionId(parsedContent[0].id);
                    setSelectedSection(parsedContent[0]);
                  }
                }
              } catch (e) {
                console.error('Error parsing template content:', e);
              }
            }
          }
        } catch (err) {
          console.error('Error loading template:', err);
        } finally {
          setIsLoading(false);
        }
      };

      loadTemplateData();
    } else {
      // Start with a default text section for new templates
      const initialSection: TOESection = {
        id: `section_${Date.now()}`,
        type: 'text',
        title: 'Introduction',
        content: {
          html: '<p>This Terms of Engagement document outlines the services provided by [Your Company Name] and the terms under which these services will be delivered.</p>'
        }
      };
      setSections([initialSection]);
      setSelectedSectionId(initialSection.id);
      setSelectedSection(initialSection);
    }
  }, [templateIdParam]);

  // Handle section selection
  const handleSelectSection = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (section) {
      setSelectedSectionId(sectionId);
      setSelectedSection(section);
    }
  };

  // Update section title
  const updateSectionTitle = (title: string) => {
    if (!selectedSectionId) return;

    setSelectedSection(prev => prev ? { ...prev, title } : null);

    setSections(prev => prev.map(section =>
      section.id === selectedSectionId ? { ...section, title } : section
    ));
  };

  // Update section content
  const updateSectionContent = (content: any) => {
    if (!selectedSectionId) return;

    setSelectedSection(prev => prev ? { ...prev, content: { ...prev.content, ...content } } : null);

    setSections(prev => prev.map(section =>
      section.id === selectedSectionId ? {
        ...section,
        content: { ...section.content, ...content }
      } : section
    ));
  };

  // Add new section
  const addSection = (type: SectionType) => {
    const newId = `section_${Date.now()}`;
    let newSection: TOESection;

    // Create different default content based on section type
    switch (type) {
      case 'text':
        newSection = {
          id: newId,
          type,
          title: 'New Text Section',
          content: { html: '<p>Enter your content here...</p>' },
          layout: 'full'
        };
        break;
      case 'checkbox':
        newSection = {
          id: newId,
          type,
          title: 'Scope of Advice',
          content: {
            options: [
              { id: 'investment', label: 'Investment Management', checked: false },
              { id: 'kiwisaver', label: 'KiwiSaver', checked: false },
              { id: 'financialPlanning', label: 'Financial Planning', checked: false },
              { id: 'estatePlanning', label: 'Estate Planning', checked: false },
              { id: 'insurance', label: 'Insurance', checked: false },
              { id: 'accountancy', label: 'Accountancy', checked: false },
              { id: 'budgeting', label: 'Budgeting', checked: false }
            ],
            showExcluded: true
          },
          layout: 'full'
        };
        break;
      case 'signature':
        newSection = {
          id: newId,
          type,
          title: 'Client Signature',
          content: {
            text: 'By signing below, I confirm that I have read, understood, and agree to the terms and conditions outlined in this document.',
            showPartner: false // Option to show partner signature
          },
          layout: 'full'
        };
        break;
      case 'fee':
        newSection = {
          id: newId,
          type,
          title: 'Fee Information',
          content: {
            showOneOff: true,
            showOngoing: false,
            ongoingType: 'percentage' // 'percentage' or 'fixed'
          },
          layout: 'full'
        };
        break;
      case 'terms':
        newSection = {
          id: newId,
          type,
          title: 'Terms and Conditions',
          content: {
            placeholder: 'Terms will be entered when generating the document'
          },
          layout: 'full'
        };
        break;
      case 'disclaimer':
        newSection = {
          id: newId,
          type,
          title: 'Disclaimer',
          content: {
            text: 'By signing below, I confirm that I have read, understood, and agree to the terms and conditions outlined in this document.\n\nI/we agree to the areas of advice noted above in this document, which is based on the full and accurate information I/we have provided.\nI/we understand the information we have been provided about {org_name} as a Financial Advice Provider, and {adviser_name} as my Financial Adviser.\nI/we confirm the agreed advice plan fee will be invoiced.\nI/we confirm that the email address noted below is correct for servicing communication.',
            required: true,
            useAdviserInfo: true
          },
          layout: 'full'
        };
        break;
      case 'client_info':
        newSection = {
          id: newId,
          type,
          title: 'Client Information',
          content: {
            showMain: true,
            showPartner: false
          },
          layout: 'full'
        };
        break;
      case 'adviser_info':
        newSection = {
          id: newId,
          type,
          title: 'Adviser Information',
          content: {},
          layout: 'full'
        };
        break;
      case 'date':
        newSection = {
          id: newId,
          type,
          title: 'Date',
          content: {},
          layout: 'full'
        };
        break;
      default:
        newSection = {
          id: newId,
          type: 'text',
          title: 'New Section',
          content: { html: '<p>Enter your content here...</p>' },
          layout: 'full'
        };
    }

    setSections(prev => [...prev, newSection]);
    setSelectedSectionId(newId);
    setSelectedSection(newSection);
  };

  // Delete section
  const deleteSection = (sectionId: string) => {
    setSections(prev => prev.filter(section => section.id !== sectionId));

    // Select another section if the current one is deleted
    if (selectedSectionId === sectionId) {
      const remainingSections = sections.filter(section => section.id !== sectionId);
      if (remainingSections.length > 0) {
        setSelectedSectionId(remainingSections[0].id);
        setSelectedSection(remainingSections[0]);
      } else {
        setSelectedSectionId(null);
        setSelectedSection(null);
      }
    }
  };

  // Handle drag end for reordering sections
  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setSections(prev => {
        const oldIndex = prev.findIndex(section => section.id === active.id);
        const newIndex = prev.findIndex(section => section.id === over.id);

        const newSections = [...prev];
        const [movedSection] = newSections.splice(oldIndex, 1);
        newSections.splice(newIndex, 0, movedSection);

        return newSections;
      });
    }
  };

  // Save template
  const saveTemplate = async () => {
    if (!templateTitle.trim()) {
      setSaveError('Template title is required');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const supabase = createClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get the user's profile to determine org_id and name
      const { data: profileData } = await supabase
        .from('profiles')
        .select('org_id, name')
        .eq('user_id', user.id)
        .single();

      // Convert sections to JSON string for storage
      const content = JSON.stringify(sections);

      let template_id = templateId;

      // If no template ID exists, create a new template
      if (!template_id) {
        const { data: templateData, error: templateError } = await supabase
          .from('templates')
          .insert({
            title: templateTitle,
            type: 'Terms of Engagement',
            content: content,
            created_by: profileData?.name || 'Unknown',
            user_id: user.id,
            org_id: profileData?.org_id || null
          })
          .select('id')
          .single();

        if (templateError) throw templateError;
        template_id = templateData.id;
        setTemplateId(template_id);
      } else {
        // Update existing template
        const { error: templateError } = await supabase
          .from('templates')
          .update({
            title: templateTitle,
            content: content,
            last_edited_at: new Date().toISOString()
          })
          .eq('id', template_id);

        if (templateError) throw templateError;
      }

      toast({
        title: "Success",
        description: "Template saved successfully",
      });

      // Redirect back to templates page after successful save
      router.push('/protected/admin/templates');
    } catch (error) {
      console.error('Error saving template:', error);
      setSaveError('Failed to save template');
      toast({
        title: "Error",
        description: "Failed to save template",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update section layout
  const updateSectionLayout = (layout: LayoutType) => {
    if (!selectedSection) return;

    setSections(prev => prev.map(section =>
      section.id === selectedSection.id ? { ...section, layout } : section
    ));

    setSelectedSection({ ...selectedSection, layout });
  };

  // Render section editor based on section type
  const renderSectionEditor = () => {
    if (!selectedSection) return null;

    // Common layout selector for all section types
    const layoutSelector = (
      <div className="mb-4 border-b pb-4">
        <Label>Section Layout</Label>
        <div className="flex space-x-2 mt-2">
          <Button
            variant={selectedSection.layout === 'full' ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateSectionLayout('full')}
            className="flex-1"
          >
            Full Width
          </Button>
          <Button
            variant={selectedSection.layout === 'column' ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateSectionLayout('column')}
            className="flex-1"
          >
            Column
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Column layout allows placing two sections side by side.
        </p>
      </div>
    );

    switch (selectedSection.type) {
      case 'text':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Content</Label>
              <div className="border rounded-md mt-1 overflow-hidden">
                {editor && (
                  <>
                    <TOEEditorToolbar editor={editor} />
                    <EditorContent editor={editor} className="min-h-[300px] p-4" />
                  </>
                )}
              </div>
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Checkbox Options</Label>
              <div className="border rounded-md p-4 mt-1 space-y-2">
                {selectedSection.content?.options?.map((option: any, index: number) => (
                  <div key={option.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`option-${option.id}`}
                        checked={option.checked}
                        onChange={(e) => {
                          const newOptions = [...selectedSection.content.options];
                          newOptions[index] = { ...option, checked: e.target.checked };
                          updateSectionContent({ options: newOptions });
                        }}
                        className="h-4 w-4 rounded border-gray-300"
                      />
                      <Input
                        value={option.label}
                        onChange={(e) => {
                          const newOptions = [...selectedSection.content.options];
                          newOptions[index] = { ...option, label: e.target.value };
                          updateSectionContent({ options: newOptions });
                        }}
                        className="flex-1"
                      />
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        const newOptions = selectedSection.content.options.filter((_: any, i: number) => i !== index);
                        updateSectionContent({ options: newOptions });
                      }}
                      className="text-red-500 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(selectedSection.content?.options || [])];
                    newOptions.push({
                      id: `option_${Date.now()}`,
                      label: `Option ${newOptions.length + 1}`,
                      checked: false
                    });
                    updateSectionContent({ options: newOptions });
                  }}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Option
                </Button>
              </div>
            </div>
          </div>
        );

      case 'signature':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="signature-text">Signature Text</Label>
              <Input
                id="signature-text"
                value={selectedSection.content?.text || ''}
                onChange={(e) => updateSectionContent({ ...selectedSection.content, text: e.target.value })}
                className="mt-1"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-partner-signature"
                checked={selectedSection.content?.showPartner || false}
                onCheckedChange={(checked) =>
                  updateSectionContent({ ...selectedSection.content, showPartner: checked === true })
                }
              />
              <Label htmlFor="show-partner-signature">Include Partner Signature</Label>
            </div>
            <div className="p-4 bg-blue-50 rounded-md">
              <p className="text-blue-700 text-sm">
                When partner signature is enabled, both main client and partner will have signature boxes.
              </p>
            </div>
          </div>
        );

      case 'fee':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="p-4 bg-blue-50 rounded-md mb-4">
              <p className="text-blue-700 text-sm">
                Fee amounts will be entered when generating the document. Select which fee types to include below.
              </p>
            </div>
            <div className="space-y-4 border p-4 rounded-md">
              <h3 className="font-medium">Fee Types</h3>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-one-off"
                  checked={selectedSection.content?.showOneOff || false}
                  onCheckedChange={(checked) =>
                    updateSectionContent({ ...selectedSection.content, showOneOff: checked === true })
                  }
                />
                <Label htmlFor="show-one-off">Include One-Off Advice Fee</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-ongoing"
                  checked={selectedSection.content?.showOngoing || false}
                  onCheckedChange={(checked) =>
                    updateSectionContent({ ...selectedSection.content, showOngoing: checked === true })
                  }
                />
                <Label htmlFor="show-ongoing">Include Ongoing Advice Fee</Label>
              </div>

              {selectedSection.content?.showOngoing && (
                <div className="ml-6 border-l-2 pl-4 border-gray-200">
                  <Label className="mb-2 block">Ongoing Fee Type</Label>
                  <RadioGroup
                    value={selectedSection.content?.ongoingType || 'percentage'}
                    onValueChange={(value) =>
                      updateSectionContent({ ...selectedSection.content, ongoingType: value })
                    }
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="percentage" id="percentage" />
                      <Label htmlFor="percentage">Percentage of Assets (% p.a.)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="fixed" id="fixed" />
                      <Label htmlFor="fixed">Fixed Monthly Fee ($ per month)</Label>
                    </div>
                  </RadioGroup>
                </div>
              )}
            </div>
          </div>
        );

      case 'terms':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="p-4 bg-blue-50 rounded-md">
              <p className="text-blue-700 text-sm">
                This section will allow users to enter terms when generating the document. The terms will be editable in the generate documents modal.
              </p>
            </div>
          </div>
        );

      case 'disclaimer':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="disclaimer-text">Disclaimer Text</Label>
              <p className="text-xs text-gray-500 mb-1">
                Use {'{adviser_name}'} and {'{org_name}'} as placeholders for the adviser and organization names.
              </p>
              <textarea
                id="disclaimer-text"
                value={selectedSection.content?.text || ''}
                onChange={(e) => updateSectionContent({ ...selectedSection.content, text: e.target.value })}
                className="min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Enter disclaimer text..."
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="required-checkbox"
                checked={selectedSection.content?.required || false}
                onCheckedChange={(checked) =>
                  updateSectionContent({ ...selectedSection.content, required: checked === true })
                }
              />
              <Label htmlFor="required-checkbox">Required</Label>
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Checkbox
                id="use-adviser-info-checkbox"
                checked={selectedSection.content?.useAdviserInfo || false}
                onCheckedChange={(checked) =>
                  updateSectionContent({ ...selectedSection.content, useAdviserInfo: checked === true })
                }
              />
              <Label htmlFor="use-adviser-info-checkbox">Use Adviser Information</Label>
            </div>
            <div className="p-4 bg-blue-50 rounded-md">
              <p className="text-blue-700 text-sm">
                This section will add a disclaimer checkbox that users must check before submitting the form.
                When "Use Adviser Information" is enabled, {'{adviser_name}'} and {'{org_name}'} placeholders will be replaced with actual values.
              </p>
            </div>
          </div>
        );

      case 'client_info':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="p-4 border rounded-md">
              <Label>Client Information to Display</Label>
              <div className="mt-2 space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-main"
                    checked={selectedSection.content?.showMain || false}
                    onCheckedChange={(checked) =>
                      updateSectionContent({
                        ...selectedSection.content,
                        showMain: checked === true
                      })
                    }
                  />
                  <Label htmlFor="show-main">Show Main Client</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-partner"
                    checked={selectedSection.content?.showPartner || false}
                    onCheckedChange={(checked) =>
                      updateSectionContent({
                        ...selectedSection.content,
                        showPartner: checked === true
                      })
                    }
                  />
                  <Label htmlFor="show-partner">Show Partner</Label>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Select which client information to display in this section.
              </p>
            </div>
          </div>
        );

      case 'adviser_info':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="p-4 bg-gray-50 rounded-md">
              <p className="text-gray-500 text-sm">
                This section will display the adviser's name and organization information automatically.
              </p>
            </div>
          </div>
        );

      case 'date':
        return (
          <div className="space-y-4">
            {layoutSelector}
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={selectedSection.title}
                onChange={(e) => updateSectionTitle(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="p-4 bg-gray-50 rounded-md">
              <p className="text-gray-500 text-sm">
                This section will display the current date automatically.
              </p>
            </div>
          </div>
        );

      // No default case needed as all SectionType values are handled
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      {/* Suspense boundary for useSearchParams */}
      <Suspense fallback={<div>Loading...</div>}>
        <SearchParamsWrapper onParamsReady={handleSearchParams} />
      </Suspense>
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent className="p-4 h-full">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => router.push('/protected/admin/templates')}
                  className="mr-2"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Templates
                </Button>
                <div className="flex flex-col">
                  <Input
                    value={templateTitle}
                    onChange={(e) => setTemplateTitle(e.target.value)}
                    className="text-2xl font-bold h-10 px-2 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    placeholder="Template Title"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? 'Hide Preview' : 'Show Preview'}
                </Button>
                <Button
                  onClick={saveTemplate}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>Save Template</>
                  )}
                </Button>
              </div>
            </div>

            {saveError && (
              <div className="bg-red-50 text-red-600 p-3 rounded-md text-sm mb-4">
                {saveError}
              </div>
            )}

            <div className="grid grid-cols-3 gap-6 h-[calc(100%-60px)]">
              {/* Left Column - Sections List */}
              <div className="col-span-1 border rounded-md p-4 overflow-auto">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Sections</h3>
                </div>

                <div className="space-y-2">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                    modifiers={[restrictToVerticalAxis]}
                  >
                    <SortableContext
                      items={sections.map(section => section.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      {sections.map(section => (
                        <SortableSection
                          key={section.id}
                          id={section.id}
                          section={section}
                          isSelected={selectedSectionId === section.id}
                          onSelect={() => handleSelectSection(section.id)}
                          onDelete={() => deleteSection(section.id)}
                        />
                      ))}
                    </SortableContext>
                  </DndContext>
                </div>

                <div className="pt-4 border-t mt-4">
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      onClick={() => addSection('text')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Text
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('checkbox')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Checkbox
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('signature')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Signature
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('fee')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Fee
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('terms')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Terms
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('disclaimer')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Disclaimer
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('client_info')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Client Info
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('adviser_info')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Adviser Info
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => addSection('date')}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Date
                    </Button>
                  </div>
                </div>
              </div>

              {/* Right Column - Section Editor or Preview */}
              <div className="col-span-2 border rounded-md p-4 overflow-auto">
                {showPreview ? (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold mb-4">Form Preview</h3>
                    <Card className="shadow-sm">
                      <CardHeader className="bg-primary/5 border-b border-gray-200">
                        <CardTitle className="text-xl font-bold text-primary">Terms of Engagement</CardTitle>
                      </CardHeader>
                      <CardContent className="pt-6 space-y-6">
                        {
                        // Group sections by layout type for rendering
                        (() => {
                          const renderedSections = [];
                          let columnSections = [];

                          for (let i = 0; i < sections.length; i++) {
                            const section = sections[i];

                            if (section.layout === 'column') {
                              // Add to column sections
                              columnSections.push(section);

                              // If we have 2 column sections or this is the last section, render the row
                              if (columnSections.length === 2 || i === sections.length - 1) {
                                renderedSections.push(
                                  <div key={`column-row-${i}`} className="grid grid-cols-2 gap-4">
                                    {columnSections.map(colSection => (
                                      <div key={colSection.id}>
                                        {renderSectionPreview(colSection)}
                                      </div>
                                    ))}
                                  </div>
                                );

                                // Clear column sections
                                columnSections = [];
                              }
                            } else {
                              // If we have any pending column sections, render them first
                              if (columnSections.length > 0) {
                                renderedSections.push(
                                  <div key={`column-row-${i}-pending`} className="grid grid-cols-2 gap-4">
                                    {columnSections.map(colSection => (
                                      <div key={colSection.id}>
                                        {renderSectionPreview(colSection)}
                                      </div>
                                    ))}
                                  </div>
                                );
                                columnSections = [];
                              }

                              // Render full-width section
                              renderedSections.push(
                                <div key={section.id}>
                                  {renderSectionPreview(section)}
                                </div>
                              );
                            }

                            // Add separator if not the last section and not in the middle of column sections
                            if (i < sections.length - 1 && !(section.layout === 'column' && columnSections.length === 0)) {
                              // Only add separator after a full row is complete or after a full-width section
                              renderedSections.push(
                                <Separator key={`separator-${i}`} className="my-4" />
                              );
                            }
                          }

                          return renderedSections;
                        })()
                      }
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Section Editor</h3>
                    {selectedSection ? (
                      renderSectionEditor()
                    ) : (
                      <div className="text-center py-12 text-gray-500 border rounded-md">
                        <p>Select a section to edit or add a new section</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
