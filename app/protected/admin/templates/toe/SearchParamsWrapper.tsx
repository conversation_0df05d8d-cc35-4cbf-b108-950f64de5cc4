'use client';

import { useSearchParams } from 'next/navigation';

interface SearchParamsWrapperProps {
  onParamsReady: (templateId: string | null) => void;
}

export default function SearchParamsWrapper({ onParamsReady }: SearchParamsWrapperProps) {
  const searchParams = useSearchParams();
  const templateIdParam = searchParams.get('templateId');
  
  // Call the callback with the params
  onParamsReady(templateIdParam);
  
  // This component doesn't render anything
  return null;
}
