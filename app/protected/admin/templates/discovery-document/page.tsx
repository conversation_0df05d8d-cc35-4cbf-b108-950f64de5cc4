'use client';

import { useState, useEffect, Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Loader2, Plus, Trash2, ArrowLeft, Edit, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import SearchParamsWrapper from './SearchParamsWrapper';
import { discoveryQuestions, DiscoverySection, DiscoveryQuestion } from '@/lib/discovery-questions';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { createClient } from '@/utils/supabase/client';
import { cloneDeep } from 'lodash';

// Function to add mappings to default discovery questions
const addMappingsToDefaultQuestions = (questions: DiscoverySection[]): DiscoverySection[] => {
  const mappedQuestions = cloneDeep(questions);

  // Loop through each section and add mappings to questions
  mappedQuestions.forEach(section => {
    switch (section.id) {
      case 'personal':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'client_name':
            case 'client_last_name':
            case 'partner_name':
            case 'partner_last_name':
            case 'date_of_birth':
            case 'partner_date_of_birth':
            case 'address':
            case 'postal_address':
              q.mapping = 'household_overview';
              break;
            case 'phone':
            case 'partner_phone':
            case 'email':
            case 'partner_email':
              q.mapping = 'personal.contact';
              break;
            case 'tax_residency':
            case 'partner_tax_residency':
            case 'tax_number':
            case 'partner_tax_number':
            case 'citizenship':
            case 'partner_citizenship':
              q.mapping = 'personal.tax';
              break;
          }
        });
        break;

      case 'relationships':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'marital_status':
              q.mapping = 'household_overview';
              break;
            case 'children':
              q.mapping = 'relationships.family';
              break;
            case 'dependents':
              q.mapping = 'relationships.family';
              break;
            case 'professional_advisors':
              q.mapping = 'relationships.professional';
              break;
          }
        });
        break;

      case 'employment':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'employment_status':
            case 'occupation':
            case 'employer':
            case 'years_with_employer':
              q.mapping = 'personal.employment';
              break;
            case 'partner_employment_status':
            case 'partner_occupation':
            case 'partner_employer':
            case 'partner_years_with_employer':
              q.mapping = 'personal.partner_employment';
              break;
          }
        });
        break;

      case 'goals':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'financial_priorities':
              q.mapping = 'goal';
              break;
            case 'short_term_goals':
              q.mapping = 'goal';
              break;
            case 'medium_term_goals':
              q.mapping = 'goal';
              break;
            case 'long_term_goals':
              q.mapping = 'goal';
              break;
            case 'retirement_age':
            case 'partner_retirement_age':
            case 'retirement_income':
              q.mapping = 'goal.retirement';
              break;
            case 'legacy_goals':
              q.mapping = 'goal.legacy';
              break;
          }
        });
        break;

      case 'income':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'employment_income':
            case 'employment_income_frequency':
              q.mapping = 'income.main';
              break;
            case 'partner_employment_income':
            case 'partner_employment_income_frequency':
              q.mapping = 'income.partner';
              break;
            case 'additional_income':
            case 'additional_income_frequency':
            case 'additional_income_source':
              q.mapping = 'income.additional';
              break;
            case 'rental_income':
            case 'rental_income_frequency':
              q.mapping = 'income.rental';
              break;
            case 'other_income':
            case 'other_income_frequency':
            case 'other_income_source':
              q.mapping = 'income.other';
              break;
          }
        });
        break;

      case 'expenses':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'housing_expenses':
            case 'utilities':
            case 'groceries':
            case 'transport':
            case 'insurance_premiums':
            case 'healthcare':
            case 'childcare':
            case 'education':
              q.mapping = 'expense.essential';
              break;
            case 'entertainment':
            case 'dining_out':
            case 'holidays':
            case 'subscriptions':
            case 'other_expenses':
              q.mapping = 'expense.discretionary';
              break;
            case 'living_expenses':
              q.mapping = 'expense';
              break;
          }
        });
        break;

      case 'property':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'primary_residence':
              q.mapping = 'asset.property';
              break;
            case 'investment_properties':
              q.mapping = 'asset.property';
              break;
            case 'property_intentions':
            case 'property_notes':
              q.mapping = 'asset.property';
              break;
          }
        });
        break;

      case 'assets':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'investment_assets':
              q.mapping = 'asset.investment';
              break;
            case 'savings_accounts':
              q.mapping = 'asset.savings';
              break;
            case 'superannuation':
              q.mapping = 'asset.superannuation';
              break;
            case 'other_assets':
              q.mapping = 'asset.other';
              break;
            case 'asset_notes':
              q.mapping = 'asset';
              break;
          }
        });
        break;

      case 'liabilities':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'mortgages':
              q.mapping = 'liability.mortgage';
              break;
            case 'personal_loans':
              q.mapping = 'liability.loan';
              break;
            case 'credit_cards':
              q.mapping = 'liability.credit';
              break;
            case 'other_debts':
              q.mapping = 'liability.other';
              break;
            case 'debt_strategy':
              q.mapping = 'liability';
              break;
          }
        });
        break;

      case 'kiwisaver':
        section.questions.forEach(q => {
          q.mapping = 'asset.superannuation';
        });
        break;

      case 'investments':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'investment_experience':
            case 'risk_profile':
              q.mapping = 'personal.investment';
              break;
            case 'shares':
            case 'managed_funds':
            case 'term_deposits':
            case 'other_investments':
              q.mapping = 'asset.investment';
              break;
            case 'investment_goals':
              q.mapping = 'goal.investment';
              break;
          }
        });
        break;

      case 'insurance':
        section.questions.forEach(q => {
          switch (q.id) {
            case 'life_insurance':
              q.mapping = 'insurances.life';
              break;
            case 'health_insurance':
              q.mapping = 'insurances.health';
              break;
            case 'income_protection':
              q.mapping = 'insurances.income';
              break;
            case 'trauma_insurance':
              q.mapping = 'insurances.trauma';
              break;
            case 'tpd_insurance':
              q.mapping = 'insurances.tpd';
              break;
            case 'general_insurance':
              q.mapping = 'insurances.general';
              break;
            case 'insurance_notes':
              q.mapping = 'insurances';
              break;
          }
        });
        break;

      case 'estate':
        section.questions.forEach(q => {
          q.mapping = 'estate_planning';
        });
        break;

      case 'risk_assessment':
        section.questions.forEach(q => {
          q.mapping = 'risk_profile';
        });
        break;

      case 'additional_info':
        section.questions.forEach(q => {
          q.mapping = 'client_summary';
        });
        break;
    }

    // For dynamic lists, set mapping based on subType
    section.questions.forEach(q => {
      if (q.type === 'dynamic-list' && q.subType && !q.mapping) {
        switch (q.subType) {
          case 'child':
          case 'dependent':
            q.mapping = 'relationships.family';
            break;
          case 'advisor':
            q.mapping = 'relationships.professional';
            break;
          case 'income':
            q.mapping = 'income';
            break;
          case 'expense':
            q.mapping = 'expense';
            break;
          case 'property':
            q.mapping = 'asset.property';
            break;
          case 'vehicle':
            q.mapping = 'asset.vehicle';
            break;
          case 'savings':
            q.mapping = 'asset.savings';
            break;
          case 'investment':
          case 'share':
          case 'managed_fund':
          case 'term_deposit':
            q.mapping = 'asset.investment';
            break;
          case 'superannuation':
            q.mapping = 'asset.superannuation';
            break;
          case 'other_asset':
            q.mapping = 'asset.other';
            break;
          case 'mortgage':
            q.mapping = 'liability.mortgage';
            break;
          case 'loan':
            q.mapping = 'liability.loan';
            break;
          case 'credit':
            q.mapping = 'liability.credit';
            break;
          case 'debt':
            q.mapping = 'liability.other';
            break;
          case 'life_insurance':
            q.mapping = 'insurances.life';
            break;
          case 'health_insurance':
            q.mapping = 'insurances.health';
            break;
          case 'income_protection':
            q.mapping = 'insurances.income';
            break;
          case 'trauma_insurance':
            q.mapping = 'insurances.trauma';
            break;
          case 'tpd_insurance':
            q.mapping = 'insurances.tpd';
            break;
          case 'general_insurance':
            q.mapping = 'insurances.general';
            break;
          case 'beneficiary':
            q.mapping = 'estate_planning';
            break;
          case 'goal':
            q.mapping = 'goal';
            break;
        }
      }
    });
  });

  return mappedQuestions;
};

interface QuestionTemplate {
  id: string;
  question: string;
  type: 'text' | 'number' | 'select' | 'date' | 'radio' | 'textarea' | 'currency' | 'dynamic-list';
  options?: string[];
  subType?: string;
  defaultValue?: any;
  mapping?: string;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
}

interface SectionTemplate {
  id: string;
  title: string;
  questions: QuestionTemplate[];
}

export default function DiscoveryDocumentTemplate() {
  const router = useRouter();
  const [useDefault, setUseDefault] = useState(false);
  const [templateIdParam, setTemplateIdParam] = useState<string | null>(null);

  // Callback function to receive search params
  const handleSearchParams = (useDefaultValue: boolean, templateId: string | null) => {
    setUseDefault(useDefaultValue);
    setTemplateIdParam(templateId);
  };

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [templateTitle, setTemplateTitle] = useState('New Discovery Document Template');
  const [templateId, setTemplateId] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [sections, setSections] = useState<SectionTemplate[]>([]);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<QuestionTemplate | null>(null);

  // Section management
  const [isSectionDialogOpen, setIsSectionDialogOpen] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState('');
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [isEditingSectionTitle, setIsEditingSectionTitle] = useState(false);
  const [editedSectionTitle, setEditedSectionTitle] = useState('');

  // Load template data if templateId is provided
  useEffect(() => {
    if (templateIdParam) {
      setIsLoading(true);
      const loadTemplateData = async () => {
        try {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('templates')
            .select('*')
            .eq('id', templateIdParam)
            .single();

          if (error) throw error;

          if (data) {
            setTemplateId(data.id);
            setTemplateTitle(data.title);

            // Parse content if it exists
            if (data.content) {
              try {
                const parsedContent = JSON.parse(data.content);
                if (Array.isArray(parsedContent)) {
                  setSections(parsedContent);
                  if (parsedContent.length > 0) {
                    setSelectedSection(parsedContent[0].id);
                    if (parsedContent[0].questions.length > 0) {
                      setSelectedQuestion(parsedContent[0].questions[0].id);
                      setEditingQuestion(parsedContent[0].questions[0]);
                    }
                  }
                }
              } catch (e) {
                console.error('Error parsing template content:', e);
              }
            }
          }
        } catch (err) {
          console.error('Error loading template:', err);
        } finally {
          setIsLoading(false);
        }
      };

      loadTemplateData();
    }
  }, [templateIdParam]);

  // Initialize with default discovery questions or empty array based on useDefault
  // Only if we're not loading an existing template
  useEffect(() => {
    if (!templateIdParam && useDefault) {
      // Add mappings to the default questions
      const questionsWithMappings = addMappingsToDefaultQuestions(discoveryQuestions);
      setSections(questionsWithMappings as SectionTemplate[]);
      if (questionsWithMappings.length > 0) {
        setSelectedSection(questionsWithMappings[0].id);
        if (questionsWithMappings[0].questions.length > 0) {
          setSelectedQuestion(questionsWithMappings[0].questions[0].id);
          setEditingQuestion(questionsWithMappings[0].questions[0]);
        }
      }
    } else if (!templateIdParam) {
      // Start with an empty template if useDefault is false and we're not loading an existing template
      const initialSection: SectionTemplate = {
        id: `section_${Date.now()}`,
        title: 'New Section',
        questions: []
      };
      setSections([initialSection]);
      setSelectedSection(initialSection.id);
    }
  }, [useDefault]);

  const handleQuestionChange = (field: string, value: any) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      if (field === 'type' && value !== prev.type) {
        // Reset options if changing from select/radio to another type
        if ((prev.type === 'select' || prev.type === 'radio') &&
            (value !== 'select' && value !== 'radio')) {
          return { ...prev, [field]: value, options: undefined };
        }
        // Initialize options array if changing to select/radio
        if ((value === 'select' || value === 'radio') &&
            (prev.type !== 'select' && prev.type !== 'radio')) {
          return { ...prev, [field]: value, options: ['Option 1'] };
        }
      }

      // Suggest mapping based on subType for dynamic lists
      if (field === 'subType' && prev.type === 'dynamic-list') {
        let suggestedMapping = '';
        switch (value) {
          case 'income':
            suggestedMapping = 'income';
            break;
          case 'expense':
            suggestedMapping = 'expense';
            break;
          case 'property':
            suggestedMapping = 'asset.property';
            break;
          case 'vehicle':
            suggestedMapping = 'asset.vehicle';
            break;
          case 'savings':
            suggestedMapping = 'asset.savings';
            break;
          case 'investment':
            suggestedMapping = 'asset.investment';
            break;
          case 'superannuation':
            suggestedMapping = 'asset.superannuation';
            break;
          case 'other_asset':
            suggestedMapping = 'asset.other';
            break;
          case 'mortgage':
            suggestedMapping = 'liability.mortgage';
            break;
          case 'loan':
            suggestedMapping = 'liability.loan';
            break;
          case 'credit':
            suggestedMapping = 'liability.credit';
            break;
          case 'goal':
            suggestedMapping = 'goal';
            break;
          default:
            suggestedMapping = 'none';
        }
        return { ...prev, [field]: value, mapping: suggestedMapping };
      }

      return { ...prev, [field]: value };
    });
  };

  const handleValidationChange = (field: string, value: any) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      const validation = prev.validation || {};
      return {
        ...prev,
        validation: {
          ...validation,
          [field]: value
        }
      };
    });
  };

  const handleOptionChange = (index: number, value: string) => {
    if (!editingQuestion || !editingQuestion.options) return;

    const newOptions = [...editingQuestion.options];
    newOptions[index] = value;

    setEditingQuestion({
      ...editingQuestion,
      options: newOptions
    });
  };

  const addOption = () => {
    if (!editingQuestion) return;

    setEditingQuestion({
      ...editingQuestion,
      options: [...(editingQuestion.options || []), `Option ${(editingQuestion.options?.length || 0) + 1}`]
    });
  };

  const removeOption = (index: number) => {
    if (!editingQuestion || !editingQuestion.options) return;

    setEditingQuestion({
      ...editingQuestion,
      options: editingQuestion.options.filter((_, i) => i !== index)
    });
  };

  const saveQuestion = () => {
    if (!editingQuestion || !selectedSection) return;

    setSections(prevSections => {
      return prevSections.map(section => {
        if (section.id === selectedSection) {
          const existingQuestionIndex = section.questions.findIndex(q => q.id === editingQuestion.id);

          if (existingQuestionIndex >= 0) {
            // Update existing question
            const updatedQuestions = [...section.questions];
            updatedQuestions[existingQuestionIndex] = editingQuestion;
            return { ...section, questions: updatedQuestions };
          } else {
            // Add new question
            return {
              ...section,
              questions: [...section.questions, editingQuestion]
            };
          }
        }
        return section;
      });
    });
  };

  const addNewQuestion = () => {
    if (!selectedSection) return;

    const newId = `question_${Date.now()}`;
    const newQuestion: QuestionTemplate = {
      id: newId,
      question: 'New Question',
      type: 'text',
      validation: {
        required: false
      }
    };

    setEditingQuestion(newQuestion);
    setSelectedQuestion(newId);

    setSections(prevSections => {
      return prevSections.map(section => {
        if (section.id === selectedSection) {
          return {
            ...section,
            questions: [...section.questions, newQuestion]
          };
        }
        return section;
      });
    });
  };

  const deleteQuestion = (questionId: string) => {
    if (!selectedSection) return;

    setSections(prevSections => {
      return prevSections.map(section => {
        if (section.id === selectedSection) {
          return {
            ...section,
            questions: section.questions.filter(q => q.id !== questionId)
          };
        }
        return section;
      });
    });

    // Select another question if the current one is deleted
    if (selectedQuestion === questionId) {
      const currentSection = sections.find(s => s.id === selectedSection);
      if (currentSection) {
        const remainingQuestions = currentSection.questions.filter(q => q.id !== questionId);
        if (remainingQuestions.length > 0) {
          setSelectedQuestion(remainingQuestions[0].id);
          setEditingQuestion(remainingQuestions[0]);
        } else {
          setSelectedQuestion(null);
          setEditingQuestion(null);
        }
      }
    }
  };

  // Section management functions
  const addNewSection = () => {
    if (!newSectionTitle.trim()) return;

    const newSection: SectionTemplate = {
      id: `section_${Date.now()}`,
      title: newSectionTitle,
      questions: []
    };

    setSections(prev => [...prev, newSection]);
    setSelectedSection(newSection.id);
    setSelectedQuestion(null);
    setEditingQuestion(null);
    setNewSectionTitle('');
    setIsSectionDialogOpen(false);
  };

  const deleteSection = (sectionId: string) => {
    // Don't allow deleting the last section
    if (sections.length <= 1) return;

    setSections(prev => prev.filter(s => s.id !== sectionId));

    // If the deleted section was selected, select another one
    if (selectedSection === sectionId) {
      const remainingSections = sections.filter(s => s.id !== sectionId);
      if (remainingSections.length > 0) {
        setSelectedSection(remainingSections[0].id);
        if (remainingSections[0].questions.length > 0) {
          setSelectedQuestion(remainingSections[0].questions[0].id);
          setEditingQuestion(remainingSections[0].questions[0]);
        } else {
          setSelectedQuestion(null);
          setEditingQuestion(null);
        }
      }
    }
  };

  const startEditingSectionTitle = (sectionId: string, currentTitle: string) => {
    setEditingSectionId(sectionId);
    setEditedSectionTitle(currentTitle);
    setIsEditingSectionTitle(true);
  };

  const saveSectionTitle = () => {
    if (!editingSectionId || !editedSectionTitle.trim()) {
      setIsEditingSectionTitle(false);
      setEditingSectionId(null);
      return;
    }

    setSections(prev => prev.map(section =>
      section.id === editingSectionId
        ? { ...section, title: editedSectionTitle }
        : section
    ));

    setIsEditingSectionTitle(false);
    setEditingSectionId(null);
  };

  // Save the entire template to Supabase
  const saveTemplate = async () => {
    if (!templateTitle.trim()) {
      setSaveError('Template title is required');
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const supabase = createClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get the user's profile to determine org_id and name
      const { data: profileData } = await supabase
        .from('profiles')
        .select('org_id, name')
        .eq('user_id', user.id)
        .single();

      // Convert sections to JSON string for storage
      const content = JSON.stringify(sections);

      let template_id = templateId;

      // If no template ID exists, create a new template
      if (!template_id) {
        const { data: templateData, error: templateError } = await supabase
          .from('templates')
          .insert({
            title: templateTitle,
            type: 'Discovery Document',
            content: content,
            created_by: profileData?.name || 'Unknown',
            user_id: user.id,
            org_id: profileData?.org_id || null,
            use_default: useDefault
          })
          .select('id')
          .single();

        if (templateError) throw templateError;
        template_id = templateData.id;
        setTemplateId(template_id);
      } else {
        // Update existing template
        const { error: templateError } = await supabase
          .from('templates')
          .update({
            title: templateTitle,
            content: content,
            last_edited_at: new Date().toISOString(),
            use_default: useDefault
          })
          .eq('id', template_id);

        if (templateError) throw templateError;
      }

      // First, delete any existing questions for this template
      const { error: deleteError } = await supabase
        .from('discovery_template_questions')
        .delete()
        .eq('template_id', template_id);

      if (deleteError) throw deleteError;

      // Prepare questions for insertion
      const questionsToInsert = [];
      let sortOrder = 0;

      for (const section of sections) {
        for (const question of section.questions) {
          questionsToInsert.push({
            template_id: template_id,
            section_id: section.id,
            section_title: section.title,
            question_id: question.id,
            question: question.question,
            type: question.type,
            options: question.options ? JSON.stringify(question.options) : null,
            sub_type: question.subType || null,
            validation: question.validation ? JSON.stringify(question.validation) : null,
            mapping: question.mapping && question.mapping !== 'none' ? question.mapping : null,
            sort_order: sortOrder++
          });
        }
      }

      // Insert questions if there are any
      if (questionsToInsert.length > 0) {
        const { error: questionsError } = await supabase
          .from('discovery_template_questions')
          .insert(questionsToInsert);

        if (questionsError) throw questionsError;
      }

      setSaveSuccess(true);

      // Redirect back to templates page after successful save
      setTimeout(() => {
        router.push('/protected/admin/templates');
      }, 1500);

    } catch (err: any) {
      console.error('Error saving template:', err);
      setSaveError(err.message || 'An error occurred while saving the template');
    } finally {
      setIsSaving(false);
    }
  };

  const renderQuestionTypeInput = () => {
    if (!editingQuestion) return null;

    switch (editingQuestion.type) {
      case 'text':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <Input
              placeholder="Text input (read-only)"
              disabled
              className="bg-white"
            />
          </div>
        );
      case 'textarea':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <Textarea
              placeholder="Textarea input (read-only)"
              disabled
              className="bg-white"
            />
          </div>
        );
      case 'number':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <Input
              type="number"
              placeholder="Number input (read-only)"
              disabled
              className="bg-white"
            />
          </div>
        );
      case 'currency':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="relative">
              <span className="absolute left-3 top-2">$</span>
              <Input
                type="number"
                placeholder="0.00"
                disabled
                className="pl-6 bg-white"
              />
            </div>
          </div>
        );
      case 'date':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <Input
              type="date"
              disabled
              className="bg-white"
            />
          </div>
        );
      case 'select':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <Select disabled>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                {editingQuestion.options?.map((option, index) => (
                  <SelectItem key={index} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="mt-4">
              <Label>Options</Label>
              {editingQuestion.options?.map((option, index) => (
                <div key={index} className="flex items-center mt-2 gap-2">
                  <Input
                    value={option}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeOption(index)}
                    className="text-red-500 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={addOption}
                className="mt-2 w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Option
              </Button>
            </div>
          </div>
        );
      case 'radio':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="space-y-2">
              {editingQuestion.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input type="radio" disabled />
                  <Label>{option}</Label>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <Label>Options</Label>
              {editingQuestion.options?.map((option, index) => (
                <div key={index} className="flex items-center mt-2 gap-2">
                  <Input
                    value={option}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeOption(index)}
                    className="text-red-500 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                variant="outline"
                onClick={addOption}
                className="mt-2 w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Option
              </Button>
            </div>
          </div>
        );
      case 'dynamic-list':
        return (
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex flex-col gap-2">
              <Label>Sub Type</Label>
              <Select
                value={editingQuestion.subType || ''}
                onValueChange={(value) => handleQuestionChange('subType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select sub type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="child">Child</SelectItem>
                  <SelectItem value="dependent">Dependent</SelectItem>
                  <SelectItem value="advisor">Advisor</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                  <SelectItem value="property">Property</SelectItem>
                  <SelectItem value="vehicle">Vehicle</SelectItem>
                  <SelectItem value="savings">Savings</SelectItem>
                  <SelectItem value="investment">Investment</SelectItem>
                  <SelectItem value="superannuation">Superannuation</SelectItem>
                  <SelectItem value="other_asset">Other Asset</SelectItem>
                  <SelectItem value="mortgage">Mortgage</SelectItem>
                  <SelectItem value="loan">Loan</SelectItem>
                  <SelectItem value="credit">Credit</SelectItem>
                  <SelectItem value="insurance">Insurance</SelectItem>
                  <SelectItem value="beneficiary">Beneficiary</SelectItem>
                  <SelectItem value="goal">Goal</SelectItem>
                </SelectContent>
              </Select>

              <div className="mt-2 p-3 bg-white rounded border">
                <p className="text-sm text-gray-500">
                  Dynamic lists allow users to add multiple items with predefined fields.
                  The fields will be determined by the selected sub type.
                </p>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen">
      {/* Suspense boundary for useSearchParams */}
      <Suspense fallback={<div>Loading...</div>}>
        <SearchParamsWrapper onParamsReady={handleSearchParams} />
      </Suspense>
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent className="p-4 h-full">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => router.push('/protected/admin/templates')}
                  className="mr-2"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Templates
                </Button>
                <div className="flex flex-col">
                  <Input
                    value={templateTitle}
                    onChange={(e) => setTemplateTitle(e.target.value)}
                    className="text-2xl font-bold h-10 px-2 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    placeholder="Template Title"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2">
                {saveError && (
                  <div className="text-red-500 text-sm mr-2">{saveError}</div>
                )}
                {saveSuccess && (
                  <div className="text-green-500 text-sm mr-2">Template saved successfully!</div>
                )}
                <Button
                  onClick={saveTemplate}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Template'
                  )}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6 h-[calc(100%-60px)]">
              {/* Left Column - Questions */}
              <div className="col-span-2 border rounded-md p-4 overflow-auto">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Questions</h3>
                  <div className="flex items-center gap-2">
                    {isEditingSectionTitle && editingSectionId === selectedSection ? (
                      <div className="flex items-center gap-2">
                        <Input
                          value={editedSectionTitle}
                          onChange={(e) => setEditedSectionTitle(e.target.value)}
                          className="w-[200px]"
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={saveSectionTitle}
                        >
                          <Save className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <Select
                          value={selectedSection || ''}
                          onValueChange={setSelectedSection}
                        >
                          <SelectTrigger className="w-[200px]">
                            <SelectValue placeholder="Select section" />
                          </SelectTrigger>
                          <SelectContent>
                            {sections.map(section => (
                              <SelectItem key={section.id} value={section.id}>
                                {section.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            const currentSection = sections.find(s => s.id === selectedSection);
                            if (currentSection) {
                              startEditingSectionTitle(currentSection.id, currentSection.title);
                            }
                          }}
                          title="Edit Section Title"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setIsSectionDialogOpen(true)}
                      title="Add New Section"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => selectedSection && deleteSection(selectedSection)}
                      disabled={sections.length <= 1}
                      className="text-red-500 hover:bg-red-50"
                      title="Delete Section"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {selectedSection && (
                  <div className="space-y-6">
                    {sections
                      .find(s => s.id === selectedSection)
                      ?.questions.map(question => (
                        <div
                          key={question.id}
                          className={`border rounded-md p-4 cursor-pointer ${
                            selectedQuestion === question.id ? 'border-blue-500 bg-blue-50' : ''
                          }`}
                          onClick={() => {
                            setSelectedQuestion(question.id);
                            setEditingQuestion(question);
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">{question.question}</h4>
                              <div className="text-sm text-gray-500 mt-1">
                                Type: {question.type}
                                {question.validation?.required && (
                                  <span className="ml-2 text-red-500">Required</span>
                                )}
                              </div>
                              <div className="text-xs text-gray-400 mt-0.5">
                                {question.mapping && question.mapping !== 'none' ? (
                                  <span>Mapped to: {question.mapping}</span>
                                ) : (
                                  <span className="italic">Not mapped</span>
                                )}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteQuestion(question.id);
                              }}
                              className="text-red-500 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="mt-2">
                            {question.type === 'select' || question.type === 'radio' ? (
                              <div className="text-sm text-gray-500">
                                Options: {question.options?.join(', ')}
                              </div>
                            ) : question.type === 'dynamic-list' ? (
                              <div className="text-sm text-gray-500">
                                Sub Type: {question.subType}
                              </div>
                            ) : null}
                          </div>
                        </div>
                      ))
                    }

                    <Button
                      variant="outline"
                      onClick={addNewQuestion}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add New Question
                    </Button>
                  </div>
                )}
              </div>

              {/* Right Column - Question Editor */}
              <div className="col-span-1 border rounded-md p-4 overflow-auto">
                <h3 className="text-lg font-semibold mb-4">Question Editor</h3>

                {editingQuestion ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="question">Question Text</Label>
                      <Input
                        id="question"
                        value={editingQuestion.question}
                        onChange={(e) => handleQuestionChange('question', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="type">Question Type</Label>
                      <Select
                        value={editingQuestion.type}
                        onValueChange={(value) => handleQuestionChange('type', value)}
                      >
                        <SelectTrigger id="type">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="textarea">Text Area</SelectItem>
                          <SelectItem value="number">Number</SelectItem>
                          <SelectItem value="currency">Currency</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="select">Select</SelectItem>
                          <SelectItem value="radio">Radio</SelectItem>
                          <SelectItem value="dynamic-list">Dynamic List</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {renderQuestionTypeInput()}

                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-2">Data Mapping</h4>
                      <div className="mb-4">
                        <Label htmlFor="mapping">Map to Household Data</Label>
                        <Select
                          value={editingQuestion.mapping || 'none'}
                          onValueChange={(value) => handleQuestionChange('mapping', value)}
                        >
                          <SelectTrigger id="mapping">
                            <SelectValue placeholder="Select mapping" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">No Mapping</SelectItem>

                            {/* Household Overview */}
                            <SelectItem value="household_overview">Household Overview</SelectItem>

                            {/* Income & Expenses */}
                            <SelectItem value="income">Income</SelectItem>
                            <SelectItem value="income.main">Income - Main</SelectItem>
                            <SelectItem value="income.partner">Income - Partner</SelectItem>
                            <SelectItem value="income.additional">Income - Additional</SelectItem>
                            <SelectItem value="income.rental">Income - Rental</SelectItem>
                            <SelectItem value="expense">Expense</SelectItem>
                            <SelectItem value="expense.essential">Expense - Essential</SelectItem>
                            <SelectItem value="expense.discretionary">Expense - Discretionary</SelectItem>

                            {/* Assets & Liabilities */}
                            <SelectItem value="asset">Asset</SelectItem>
                            <SelectItem value="asset.property">Asset - Property</SelectItem>
                            <SelectItem value="asset.vehicle">Asset - Vehicle</SelectItem>
                            <SelectItem value="asset.savings">Asset - Savings</SelectItem>
                            <SelectItem value="asset.investment">Asset - Investment</SelectItem>
                            <SelectItem value="asset.superannuation">Asset - Superannuation</SelectItem>
                            <SelectItem value="asset.other">Asset - Other</SelectItem>
                            <SelectItem value="liability">Liability</SelectItem>
                            <SelectItem value="liability.mortgage">Liability - Mortgage</SelectItem>
                            <SelectItem value="liability.loan">Liability - Loan</SelectItem>
                            <SelectItem value="liability.credit">Liability - Credit</SelectItem>

                            {/* Goals */}
                            <SelectItem value="goal">Goal</SelectItem>
                            <SelectItem value="goal.savings">Goal - Savings</SelectItem>
                            <SelectItem value="goal.debt_reduction">Goal - Debt Reduction</SelectItem>
                            <SelectItem value="goal.investment">Goal - Investment</SelectItem>
                            <SelectItem value="goal.property">Goal - Property</SelectItem>
                            <SelectItem value="goal.retirement">Goal - Retirement</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground mt-1">
                          This determines which household data section this question will be mapped to.
                        </p>
                      </div>
                    </div>

                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-2">Validation</h4>

                      <div className="flex items-center space-x-2 mb-4">
                        <Switch
                          id="required"
                          checked={editingQuestion.validation?.required || false}
                          onCheckedChange={(checked) => handleValidationChange('required', checked)}
                        />
                        <Label htmlFor="required">Required</Label>
                      </div>

                      {(editingQuestion.type === 'number' || editingQuestion.type === 'currency') && (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="min">Minimum</Label>
                            <Input
                              id="min"
                              type="number"
                              value={editingQuestion.validation?.min || ''}
                              onChange={(e) => handleValidationChange('min', e.target.value ? Number(e.target.value) : undefined)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="max">Maximum</Label>
                            <Input
                              id="max"
                              type="number"
                              value={editingQuestion.validation?.max || ''}
                              onChange={(e) => handleValidationChange('max', e.target.value ? Number(e.target.value) : undefined)}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="pt-4">
                      <Button onClick={saveQuestion}>Save Question</Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    Select a question to edit or add a new one
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dialog for adding new section */}
      <Dialog open={isSectionDialogOpen} onOpenChange={setIsSectionDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Section</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sectionTitle" className="text-right">
                Section Title
              </Label>
              <Input
                id="sectionTitle"
                value={newSectionTitle}
                onChange={(e) => setNewSectionTitle(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={addNewSection} disabled={!newSectionTitle.trim()}>
              Add Section
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
