'use client';

import { useSearchParams } from 'next/navigation';

interface SearchParamsWrapperProps {
  onParamsReady: (useDefault: boolean, templateId: string | null, profilerType: string) => void;
}

export default function SearchParamsWrapper({ onParamsReady }: SearchParamsWrapperProps) {
  const searchParams = useSearchParams();
  const useDefault = searchParams.get('useDefault') === 'true';
  const templateIdParam = searchParams.get('templateId');
  const profilerType = searchParams.get('profilerType') || '10q';
  
  // Call the callback with the params
  onParamsReady(useDefault, templateIdParam, profilerType);
  
  // This component doesn't render anything
  return null;
}
