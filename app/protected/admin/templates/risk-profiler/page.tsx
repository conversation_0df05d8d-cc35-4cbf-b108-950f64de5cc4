'use client';

import { useState, useEffect, Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Loader2, Plus, Trash2, ArrowLeft, Edit, Save, GripVertical } from 'lucide-react';
import { useRouter } from 'next/navigation';
import SearchParamsWrapper from './SearchParamsWrapper';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { createClient } from '@/utils/supabase/client';
import { cloneDeep } from 'lodash';
import { riskQuestions10Q, riskQuestions25Q } from '@/app/risk-profiler-form/questions';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';

interface QuestionOption {
  text: string;
  score: number;
}

interface QuestionTemplate {
  id: string;
  question: string;
  type: 'radio' | 'checkbox' | 'textarea';
  options: QuestionOption[];
  validation?: {
    required?: boolean;
  };
  includeInScore?: boolean;
}

interface SortableOptionProps {
  id: string;
  option: QuestionOption;
  index: number;
  onTextChange: (value: string) => void;
  onScoreChange: (value: number) => void;
  onRemove: () => void;
}

const SortableOption = ({ id, option, index, onTextChange, onScoreChange, onRemove }: SortableOptionProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  };

  return (
    <div ref={setNodeRef} style={style} className="flex items-center mt-2 gap-2">
      <div {...attributes} {...listeners} className="cursor-grab p-1">
        <GripVertical className="h-4 w-4 text-gray-400" />
      </div>
      <Input
        value={option.text}
        onChange={(e) => onTextChange(e.target.value)}
        className="flex-grow"
      />
      <Input
        type="number"
        value={option.score}
        onChange={(e) => onScoreChange(parseInt(e.target.value))}
        className="w-20"
      />
      <Button
        variant="ghost"
        size="icon"
        onClick={onRemove}
        className="text-red-500 hover:bg-red-50"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default function RiskProfilerTemplate() {
  const router = useRouter();
  const [useDefault, setUseDefault] = useState(false);
  const [profilerType, setProfilerType] = useState('10q');
  const [templateIdParam, setTemplateIdParam] = useState<string | null>(null);

  // Callback function to receive search params
  const handleSearchParams = (useDefaultValue: boolean, templateId: string | null, profilerTypeValue: string) => {
    setUseDefault(useDefaultValue);
    setTemplateIdParam(templateId);
    setProfilerType(profilerTypeValue);
  };

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [templateTitle, setTemplateTitle] = useState('New Risk Profiler Template');
  const [templateId, setTemplateId] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [questions, setQuestions] = useState<QuestionTemplate[]>([]);
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<QuestionTemplate | null>(null);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load template data if templateId is provided
  useEffect(() => {
    if (templateIdParam) {
      setIsLoading(true);
      const loadTemplateData = async () => {
        try {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('templates')
            .select('*')
            .eq('id', templateIdParam)
            .single();

          if (error) throw error;

          if (data) {
            setTemplateId(data.id);
            setTemplateTitle(data.title);

            // Parse content if it exists
            if (data.content) {
              try {
                const parsedContent = JSON.parse(data.content);
                if (Array.isArray(parsedContent)) {
                  setQuestions(parsedContent);
                  if (parsedContent.length > 0) {
                    setSelectedQuestion(parsedContent[0].id);
                    setEditingQuestion(parsedContent[0]);
                  }
                }
              } catch (e) {
                console.error('Error parsing template content:', e);
              }
            }
          }
        } catch (err) {
          console.error('Error loading template:', err);
        } finally {
          setIsLoading(false);
        }
      };

      loadTemplateData();
    }
  }, [templateIdParam]);

  // Initialize with default questions if useDefault is true
  useEffect(() => {
    if (!templateIdParam && useDefault) {
      // Get the appropriate default questions based on profilerType
      const defaultRiskQuestions = profilerType === '10q' ? riskQuestions10Q : riskQuestions25Q;

      // Convert the default questions to the format needed for the editor
      const defaultQuestions: QuestionTemplate[] = defaultRiskQuestions.map(q => ({
        id: q.id,
        question: q.question,
        type: 'radio', // Default to radio type
        options: q.options.map(opt => ({
          text: opt.text,
          score: opt.score
        })),
        validation: { required: true }
      }));

      setQuestions(defaultQuestions);
      if (defaultQuestions.length > 0) {
        setSelectedQuestion(defaultQuestions[0].id);
        setEditingQuestion(defaultQuestions[0]);
      }
    } else if (!templateIdParam) {
      // Start with an empty template if useDefault is false
      const initialQuestion: QuestionTemplate = {
        id: `question_${Date.now()}`,
        question: 'New Question',
        type: 'radio',
        options: [
          { text: 'Option 1', score: 1 },
          { text: 'Option 2', score: 2 }
        ],
        validation: { required: true }
      };
      setQuestions([initialQuestion]);
      setSelectedQuestion(initialQuestion.id);
      setEditingQuestion(initialQuestion);
    }
  }, [useDefault, templateIdParam, profilerType]);

  const handleQuestionChange = (field: string, value: any) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      if (field === 'type' && value !== prev.type) {
        // If changing to textarea, set includeInScore to false
        if (value === 'textarea') {
          return {
            ...prev,
            [field]: value,
            includeInScore: false,
            options: [] // No options needed for textarea
          };
        }
        // If changing from textarea to radio/checkbox, set includeInScore to true and add default options
        else if (prev.type === 'textarea') {
          return {
            ...prev,
            [field]: value,
            includeInScore: true,
            options: [
              { text: 'Option 1', score: 1 },
              { text: 'Option 2', score: 2 }
            ]
          };
        }
        // If changing between radio and checkbox, keep the options
        else {
          return { ...prev, [field]: value };
        }
      }

      return { ...prev, [field]: value };
    });
  };

  const handleValidationChange = (field: string, value: any) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      const validation = prev.validation || {};
      return {
        ...prev,
        validation: {
          ...validation,
          [field]: value
        }
      };
    });
  };

  const handleOptionTextChange = (index: number, value: string) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      const newOptions = [...prev.options];
      newOptions[index] = { ...newOptions[index], text: value };

      return {
        ...prev,
        options: newOptions
      };
    });
  };

  const handleOptionScoreChange = (index: number, value: number) => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      const newOptions = [...prev.options];
      newOptions[index] = { ...newOptions[index], score: value };

      return {
        ...prev,
        options: newOptions
      };
    });
  };

  const addOption = () => {
    if (!editingQuestion) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      const newScore = prev.options.length > 0
        ? Math.max(...prev.options.map(opt => opt.score)) + 1
        : 1;

      return {
        ...prev,
        options: [...prev.options, { text: `Option ${prev.options.length + 1}`, score: newScore }]
      };
    });
  };

  const removeOption = (index: number) => {
    if (!editingQuestion || editingQuestion.options.length <= 1) return;

    setEditingQuestion(prev => {
      if (!prev) return null;

      return {
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      };
    });
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setEditingQuestion(prev => {
        if (!prev) return null;

        const oldIndex = prev.options.findIndex((_, i) => `option-${i}` === active.id);
        const newIndex = prev.options.findIndex((_, i) => `option-${i}` === over.id);

        const newOptions = [...prev.options];
        const [movedOption] = newOptions.splice(oldIndex, 1);
        newOptions.splice(newIndex, 0, movedOption);

        return {
          ...prev,
          options: newOptions
        };
      });
    }
  };

  const saveQuestion = () => {
    if (!editingQuestion) return;

    setQuestions(prevQuestions => {
      const existingQuestionIndex = prevQuestions.findIndex(q => q.id === editingQuestion.id);

      if (existingQuestionIndex >= 0) {
        // Update existing question
        const updatedQuestions = [...prevQuestions];
        updatedQuestions[existingQuestionIndex] = editingQuestion;
        return updatedQuestions;
      } else {
        // Add new question
        return [...prevQuestions, editingQuestion];
      }
    });
  };

  const addNewQuestion = () => {
    const newId = `question_${Date.now()}`;
    const newQuestion: QuestionTemplate = {
      id: newId,
      question: 'New Question',
      type: 'radio',
      options: [
        { text: 'Option 1', score: 1 },
        { text: 'Option 2', score: 2 }
      ],
      validation: {
        required: true
      },
      includeInScore: true
    };

    setEditingQuestion(newQuestion);
    setSelectedQuestion(newId);

    setQuestions(prevQuestions => [...prevQuestions, newQuestion]);
  };

  const deleteQuestion = (questionId: string) => {
    setQuestions(prevQuestions => prevQuestions.filter(q => q.id !== questionId));

    // Select another question if the current one is deleted
    if (selectedQuestion === questionId) {
      const remainingQuestions = questions.filter(q => q.id !== questionId);
      if (remainingQuestions.length > 0) {
        setSelectedQuestion(remainingQuestions[0].id);
        setEditingQuestion(remainingQuestions[0]);
      } else {
        setSelectedQuestion(null);
        setEditingQuestion(null);
      }
    }
  };

  // Save the entire template to Supabase
  const saveTemplate = async () => {
    if (!templateTitle.trim()) {
      setSaveError('Template title is required');
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const supabase = createClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get the user's profile to determine org_id and name
      const { data: profileData } = await supabase
        .from('profiles')
        .select('org_id, name')
        .eq('user_id', user.id)
        .single();

      // Convert questions to JSON string for storage
      const content = JSON.stringify(questions);

      let template_id = templateId;

      // If no template ID exists, create a new template
      if (!template_id) {
        const { data: templateData, error: templateError } = await supabase
          .from('templates')
          .insert({
            title: templateTitle,
            type: 'Risk Profiler',
            content: content,
            created_by: profileData?.name || 'Unknown',
            user_id: user.id,
            org_id: profileData?.org_id || null,
            use_default: useDefault
          })
          .select('id')
          .single();

        if (templateError) throw templateError;
        template_id = templateData.id;
        setTemplateId(template_id);
      } else {
        // Update existing template
        const { error: templateError } = await supabase
          .from('templates')
          .update({
            title: templateTitle,
            content: content,
            last_edited_at: new Date().toISOString(),
            use_default: useDefault
          })
          .eq('id', template_id);

        if (templateError) throw templateError;
      }

      // First, delete any existing questions for this template
      const { error: deleteError } = await supabase
        .from('risk_profiler_template_questions')
        .delete()
        .eq('template_id', template_id);

      if (deleteError) throw deleteError;

      // Prepare questions for insertion
      const questionsToInsert = [];
      let sortOrder = 0;

      for (const question of questions) {
        questionsToInsert.push({
          template_id: template_id,
          question_id: question.id,
          question: question.question,
          type: question.type,
          options: JSON.stringify(question.options || []),
          validation: question.validation ? JSON.stringify(question.validation) : null,
          include_in_score: question.type === 'textarea' ? false : (question.includeInScore !== false),
          sort_order: sortOrder++
        });
      }

      // Insert questions if there are any
      if (questionsToInsert.length > 0) {
        const { error: questionsError } = await supabase
          .from('risk_profiler_template_questions')
          .insert(questionsToInsert);

        if (questionsError) throw questionsError;
      }

      setSaveSuccess(true);

      // Redirect back to templates page after successful save
      setTimeout(() => {
        router.push('/protected/admin/templates');
      }, 1500);

    } catch (err: any) {
      console.error('Error saving template:', err);
      setSaveError(err.message || 'An error occurred while saving the template');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex h-screen">
      {/* Suspense boundary for useSearchParams */}
      <Suspense fallback={<div>Loading...</div>}>
        <SearchParamsWrapper onParamsReady={handleSearchParams} />
      </Suspense>
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent className="p-4 h-full">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => router.push('/protected/admin/templates')}
                  className="mr-2"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Templates
                </Button>
                <div className="flex flex-col">
                  <Input
                    value={templateTitle}
                    onChange={(e) => setTemplateTitle(e.target.value)}
                    className="text-2xl font-bold h-10 px-2 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    placeholder="Template Title"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2">
                {saveError && (
                  <div className="text-red-500 text-sm mr-2">{saveError}</div>
                )}
                {saveSuccess && (
                  <div className="text-green-500 text-sm mr-2">Template saved successfully!</div>
                )}
                <Button
                  onClick={saveTemplate}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Template'
                  )}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6 h-[calc(100%-60px)]">
              {/* Left Column - Questions */}
              <div className="col-span-2 border rounded-md p-4 overflow-auto">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Questions</h3>
                </div>

                <div className="space-y-6">
                  {questions.map(question => (
                    <div
                      key={question.id}
                      className={`border rounded-md p-4 cursor-pointer ${
                        selectedQuestion === question.id ? 'border-blue-500 bg-blue-50' : ''
                      }`}
                      onClick={() => {
                        setSelectedQuestion(question.id);
                        setEditingQuestion(question);
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{question.question}</h4>
                          <div className="text-sm text-gray-500 mt-1">
                            Type: {question.type}
                            {question.validation?.required && (
                              <span className="ml-2 text-red-500">Required</span>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteQuestion(question.id);
                          }}
                          className="text-red-500 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="mt-2">
                        {question.type === 'textarea' ? (
                          <div className="text-sm text-gray-500">
                            <span className="italic">Text area response (not included in score calculation)</span>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">
                            Options: {question.options && question.options.length > 0 ?
                              question.options.map(opt => `${opt.text} (${opt.score})`).join(', ') :
                              'No options defined'}
                            {question.includeInScore === false && (
                              <span className="ml-2 italic">(not included in score)</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    onClick={addNewQuestion}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Question
                  </Button>
                </div>
              </div>

              {/* Right Column - Question Editor */}
              <div className="col-span-1 border rounded-md p-4 overflow-auto">
                <h3 className="text-lg font-semibold mb-4">Question Editor</h3>

                {editingQuestion ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="question">Question Text</Label>
                      <Input
                        id="question"
                        value={editingQuestion.question}
                        onChange={(e) => handleQuestionChange('question', e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="type">Question Type</Label>
                      <Select
                        value={editingQuestion.type}
                        onValueChange={(value) => handleQuestionChange('type', value)}
                      >
                        <SelectTrigger id="type">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="radio">Radio</SelectItem>
                          <SelectItem value="checkbox">Checkbox</SelectItem>
                          <SelectItem value="textarea">Text Area (Contextual)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {editingQuestion.type !== 'textarea' ? (
                      <div className="bg-gray-50 p-3 rounded-md">
                        <Label>Options</Label>
                        <div className="flex items-center justify-between mb-2 mt-2">
                          <div className="flex-grow pl-8">Option Text</div>
                          <div className="w-20 text-center">Score</div>
                          <div className="w-10"></div>
                        </div>

                        <DndContext
                          sensors={sensors}
                          collisionDetection={closestCenter}
                          onDragEnd={handleDragEnd}
                          modifiers={[restrictToVerticalAxis]}
                        >
                          <SortableContext
                            items={editingQuestion.options.map((_, i) => `option-${i}`)}
                            strategy={verticalListSortingStrategy}
                          >
                            {editingQuestion.options.map((option, index) => (
                              <SortableOption
                                key={`option-${index}`}
                                id={`option-${index}`}
                                option={option}
                                index={index}
                                onTextChange={(value) => handleOptionTextChange(index, value)}
                                onScoreChange={(value) => handleOptionScoreChange(index, value)}
                                onRemove={() => removeOption(index)}
                              />
                            ))}
                          </SortableContext>
                        </DndContext>

                        <Button
                          variant="outline"
                          onClick={addOption}
                          className="mt-4 w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Option
                        </Button>
                      </div>
                    ) : (
                      <div className="bg-gray-50 p-3 rounded-md">
                        <Label>Text Area Response</Label>
                        <div className="mt-2">
                          <Textarea
                            disabled
                            placeholder="User will enter free text here"
                            className="bg-gray-100 text-gray-500"
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          This is a preview of how the text area will appear to users. Their responses will be saved but not used in risk score calculations.
                        </p>
                      </div>
                    )}

                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-2">Validation</h4>

                      <div className="flex items-center space-x-2 mb-4">
                        <Switch
                          id="required"
                          checked={editingQuestion.validation?.required || false}
                          onCheckedChange={(checked) => handleValidationChange('required', checked)}
                        />
                        <Label htmlFor="required">Required</Label>
                      </div>

                      {editingQuestion.type !== 'textarea' && (
                        <div className="flex items-center space-x-2 mb-4">
                          <Switch
                            id="includeInScore"
                            checked={editingQuestion.includeInScore !== false} // Default to true if undefined
                            onCheckedChange={(checked) => handleQuestionChange('includeInScore', checked)}
                          />
                          <Label htmlFor="includeInScore">Include in risk score calculation</Label>
                        </div>
                      )}

                      {editingQuestion.type === 'textarea' && (
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mt-2">
                          <p className="text-sm text-amber-800">
                            Text area questions are for collecting additional context and will not be included in the risk score calculation.
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="pt-4">
                      <Button onClick={saveQuestion}>Save Question</Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    Select a question to edit or add a new one
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
