'use client';

import { useState, useEffect, Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { TemplatesTable } from '@/components/tables/TemplatesTable';
import TemplatesModal from '@/components/modals/TemplatesModal';

interface Template {
  id: string;
  title: string;
  type: string;
  content: string;
  created_at: string;
  last_edited_at: string;
  created_by: string;
  user_id: string;
  org_id: string;
}

function TemplatesContent() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get the user's profile to determine org_id
      const { data: profileData } = await supabase
        .from('profiles')
        .select('org_id')
        .eq('user_id', user.id)
        .single();

      // Fetch templates based on user_id or org_id
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .or(`user_id.eq.${user.id},org_id.eq.${profileData?.org_id || ''}`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setTemplates(data || []);
    } catch (err: any) {
      console.error('Error fetching templates:', err);
      setError(err.message || 'Failed to load templates');
      setTemplates([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setIsModalOpen(true);
  };

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center h-[calc(100vh-150px)]">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading templates...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-[calc(100vh-150px)]">
                <div className="text-red-500">{error}</div>
              </div>
            ) : (
              <TemplatesTable
                data={templates}
                onDataChange={fetchTemplates}
                onCreateTemplate={handleCreateTemplate}
                onEditTemplate={(partialTemplate) => {
                  // Find the full template object from the state using the ID
                  const fullTemplate = templates.find(t => t.id === partialTemplate.id);
                  if (fullTemplate) {
                    setSelectedTemplate(fullTemplate);
                    setIsModalOpen(true);
                  } else {
                    // Optional: Handle case where template is not found
                    console.error("Template not found in state:", partialTemplate);
                    setError("Could not find the selected template details.");
                  }
                }}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {isModalOpen && (
        <TemplatesModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          templateToEdit={selectedTemplate}
          onSuccess={fetchTemplates}
        />
      )}
    </div>
  );
}

export default function TemplatesPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading templates...</span>
      </div>
    }>
      <TemplatesContent />
    </Suspense>
  );
}
