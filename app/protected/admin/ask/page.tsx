'use client';

import { Suspense } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { SQLChatbot } from '@/components/reporting/SQLChatbot';

function AskContent() {
  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
          <CardContent className="h-full p-0">
            <SQLChatbot />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AskPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading SQL Assistant...</span>
    </div>}>
      <AskContent />
    </Suspense>
  );
}
