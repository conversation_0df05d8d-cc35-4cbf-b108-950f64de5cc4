'use client';

import { useState, useEffect, Suspense } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Loader2, User, Shield, Settings, Bell, Palette, Mail, Edit, Smartphone, Building2, BarChart2 } from 'lucide-react';
import { ScenarioMetricsTab } from '@/components/tabs/ScenarioMetricsTab';
import { useRouter, useSearchParams } from 'next/navigation';
import { EnrollMFA } from '@/components/mfa/EnrollMFA';
import { UnenrollMFA } from '@/components/mfa/UnenrollMFA';
import { unenrollMFAAction, updateMfaEnabledAction } from '@/app/actions';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import ChangePasswordModal from '@/components/modals/ChangePasswordModal';
import UpdateEmailModal from '@/components/modals/UpdateEmailModal';
import UpdateProfileModal from '@/components/modals/UpdateProfileModal';
// Device management feature temporarily disabled, but keeping import for future use
import ManageSessionsModal from '@/components/modals/ManageSessionsModal';
import DeviceRegistration from '@/components/DeviceRegistration';
import DatabaseSetup from '@/components/DatabaseSetup';
import LogoUpload from '@/components/LogoUpload';
import LogoAvatar from '@/components/LogoAvatar';
import NotificationPreferences from '@/components/NotificationPreferences';

// Create a wrapper component that uses searchParams
function ProfileContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createClient();
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [profileData, setProfileData] = useState<any>({
    name: "Riki Carston",
    email: "<EMAIL>",
    role: "Premium Member",
    joinDate: "December 2023",
    lastLogin: "December 7, 2024",
    preferences: {
      theme: "System Default",
      notifications: "Enabled",
      twoFactorAuth: "Enabled"
    }
  });
  const [hasMFA, setHasMFA] = useState(false);
  const [mfaEnabled, setMfaEnabled] = useState(false);
  const [showMFAEnrollment, setShowMFAEnrollment] = useState(false);
  const [updatingMfaSetting, setUpdatingMfaSetting] = useState(false);
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] = useState(false);
  const [isUpdateEmailModalOpen, setIsUpdateEmailModalOpen] = useState(false);
  const [isUpdateProfileModalOpen, setIsUpdateProfileModalOpen] = useState(false);
  // Device management feature temporarily disabled, but keeping state for future use
  const [isManageSessionsModalOpen, setIsManageSessionsModalOpen] = useState(false);

  // Check for success/error/warning messages from URL parameters
  useEffect(() => {
    const status = searchParams.get('status');
    const message = searchParams.get('message');

    if (status && message) {
      const decodedMessage = decodeURIComponent(message);

      if (status === 'success') {
        setSuccessMessage(decodedMessage);
        setError(''); // Clear any previous errors
        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 5000);
      } else if (status === 'error') {
        setError(decodedMessage);
        setSuccessMessage(''); // Clear any previous success messages
      } else if (status === 'warning') {
        // For warnings, we'll show them as success messages but with different styling
        setSuccessMessage(decodedMessage);
        setError(''); // Clear any previous errors
        // Clear warning message after 5 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 5000);
      }
    }
  }, [searchParams]);

  // Fetch user profile data
  const fetchUserProfile = async () => {
    try {
      setLoading(true);

      // Get user data
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.log('User not authenticated, redirecting to sign-in');
        router.push('/sign-in');
        return;
      }

      console.log('User authenticated:', user.id);

      // Get profile data
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching profile:', profileError);
        throw profileError;
      }

      // Set profile data
      setProfileData({
        id: user.id,
        email: user.email,
        ...profileData,
        // Keep some default values if not available from the database
        name: profileData?.name || "Riki Carston",
        role: profileData?.role || "Premium Member",
        joinDate: profileData?.created_at ? new Date(profileData.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'long' }) : "December 2023",
        lastLogin: profileData?.last_sign_in_at ? new Date(profileData.last_sign_in_at).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) : "December 7, 2024",
        logo_path: profileData?.logo_path || null,
        preferences: {
          theme: profileData?.preferences?.theme || "System Default",
          notifications: profileData?.preferences?.notifications || "Enabled",
        }
      });

      // Set MFA enabled state from profile
      setMfaEnabled(profileData?.mfa_enabled === true);

      // Check MFA status
      await checkMFAStatus();
    } catch (err: any) {
      console.error('Error fetching user profile:', err);
      setError(err.message || 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  // Check MFA status
  const checkMFAStatus = async () => {
    try {
      // Check MFA factors
      const { data, error } = await supabase.auth.mfa.listFactors();

      if (error) {
        console.error('Error listing factors:', error);
        throw error;
      }

      console.log('MFA factors:', data);

      // Check if any factors are verified
      const allFactors = [...(data.totp || []), ...(data.phone || [])];

      // Also check for unverified factors and clean them up
      const unverifiedFactors = allFactors.filter(factor => factor.status !== 'verified');
      if (unverifiedFactors.length > 0) {
        console.log('Found unverified factors, cleaning up:', unverifiedFactors);
        for (const factor of unverifiedFactors) {
          try {
            await supabase.auth.mfa.unenroll({ factorId: factor.id });
            console.log('Successfully unenrolled unverified factor:', factor.id);
          } catch (unenrollError) {
            console.error('Error unenrolling factor:', unenrollError);
          }
        }
      }

      const hasVerifiedFactors = allFactors.some(factor => factor.status === 'verified');
      console.log('Has verified MFA factors:', hasVerifiedFactors);

      setHasMFA(hasVerifiedFactors);

      // If the actual MFA status doesn't match the profile setting, update the profile
      if (hasVerifiedFactors !== mfaEnabled) {
        console.log('MFA status mismatch. Actual:', hasVerifiedFactors, 'Profile setting:', mfaEnabled);
        // Only auto-sync from false to true (if user has MFA but setting is disabled)
        // For the other direction, we want the user to explicitly disable it via the toggle
        if (hasVerifiedFactors && !mfaEnabled) {
          console.log('MFA status mismatch detected, but not auto-syncing to avoid circular updates');
          // We'll let the user manually enable MFA through the UI
        }
      }
    } catch (err: any) {
      console.error('Error checking MFA status:', err);
      setError(err.message || 'Failed to check MFA status');
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const handleMFAEnrollSuccess = async () => {
    setShowMFAEnrollment(false);
    await checkMFAStatus();

    // Show success message
    setError(''); // Clear any previous errors
    setSuccessMessage('Two-factor authentication set up successfully');

    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 5000);

    // If MFA is successfully enrolled but mfa_enabled is false in the profile,
    // we'll let the user manually enable it through the UI
    if (hasMFA && !mfaEnabled) {
      console.log('MFA enrolled but not enabled in profile. User should enable it manually.');
    }
  };

  const handleMFAUnenrollSuccess = async () => {
    await checkMFAStatus();

    // Show success message
    setError(''); // Clear any previous errors
    setSuccessMessage('Two-factor authentication method removed successfully');

    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 5000);

    // If all MFA factors are removed but mfa_enabled is still true in the profile,
    // we'll let the user manually disable it through the UI
    if (!hasMFA && mfaEnabled) {
      console.log('No MFA factors but still enabled in profile. User should disable it manually.');
    }
  };

  // Logout function for future use
  const handleLogout = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push('/sign-in');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <Card className="flex-0 h-[calc(100vh-62px)] mt-[50px]">
        <CardContent className="p-0"> {/* Moved CardContent here, removed padding */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-grow flex flex-col pt-4">
            <TabsList className="grid grid-cols-3 w-full border-b rounded-none px-2">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Profile</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>Security</span>
              </TabsTrigger>
              <TabsTrigger value="scenario-metrics" className="flex items-center gap-2">
                <BarChart2 className="h-4 w-4" />
                <span>Scenario Metrics</span>
              </TabsTrigger>
            </TabsList>

            {/* Removed CardContent from here */}
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {successMessage && (
              <Alert
                variant="default"
                className={`mb-6 ${successMessage.includes('issue') || successMessage.includes('could not')
                  ? 'bg-yellow-50 text-yellow-800 border-yellow-200' // Warning style
                  : 'bg-green-50 text-green-800 border-green-200'    // Success style
                  }`}
              >
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            {/* Profile Tab */}
            <TabsContent value="profile" className="p-6"> {/* Added padding */}
              <div className="flex flex-col md:flex-row items-center mb-6 border rounded-lg p-4 gap-4">
                {/* Left side - User Avatar and Name (50%) */}
                <div className="flex items-center space-x-4 w-full md:w-1/2">
                  <Avatar className="h-16 w-16 shrink-0">
                    <AvatarFallback className="text-xl">
                      {profileData.name?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-semibold">{profileData.name}</h3>
                    <p className="text-sm text-muted-foreground">{profileData.role}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <div className="space-y-4">

                  <h3 className="text-lg font-semibold mt-6">Account Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Email</p>
                        <p>{profileData.email}</p>
                        {profileData.pending_email && (
                          <p className="text-xs text-yellow-600 mt-1">
                            Pending verification: {profileData.pending_email}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">Full Name</p>
                        <p>{profileData.name}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setIsUpdateProfileModalOpen(true)}
                        className="h-8 w-8"
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit Name</span>
                      </Button>
                    </div>
                    {profileData.org_name && (
                      <div>
                        <p className="text-sm text-muted-foreground">Organisation</p>
                        <p>{profileData.org_name}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Update Email Modal */}
              <UpdateEmailModal
                isOpen={isUpdateEmailModalOpen}
                onClose={() => setIsUpdateEmailModalOpen(false)}
                currentEmail={profileData.email}
                onEmailUpdate={(newEmail) => {
                  // Update the local state immediately
                  setProfileData((prev: any) => ({
                    ...prev,
                    email: newEmail
                  }));
                  // Show success message
                  setSuccessMessage('Email update initiated. Please check your new email for a confirmation link.');
                  // Clear success message after 5 seconds
                  setTimeout(() => setSuccessMessage(''), 5000);
                }}
              />

              {/* Update Profile Modal */}
              <UpdateProfileModal
                isOpen={isUpdateProfileModalOpen}
                onClose={() => setIsUpdateProfileModalOpen(false)}
                currentName={profileData.name}
                onProfileUpdate={(newName) => {
                  // Update the local state immediately
                  setProfileData((prev: any) => ({
                    ...prev,
                    name: newName
                  }));
                  // Show success message
                  setSuccessMessage('Profile updated successfully');
                  // Clear success message after 5 seconds
                  setTimeout(() => setSuccessMessage(''), 5000);
                }}
              />
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security" className="p-6 overflow-y-auto max-h-[calc(100vh-200px)]"> {/* Added padding and scroll */}
              <div className="space-y-6">
                {/* MFA Toggle Section */}
                <div className="border rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-2">Two-Factor Authentication</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Enable or disable two-factor authentication for your account
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="mfa-toggle">Enable Two-Factor Authentication</Label>
                      <p className="text-sm text-muted-foreground">
                        When enabled, you'll need to set up and use an authenticator app
                      </p>
                    </div>
                    <Button
                      variant={mfaEnabled ? "destructive" : "default"}
                      size="sm"
                      disabled={updatingMfaSetting}
                      onClick={async () => {
                        try {
                          setUpdatingMfaSetting(true);

                          // Update UI immediately for responsiveness
                          const newEnabledState = !mfaEnabled;
                          setMfaEnabled(newEnabledState);

                          // Get user data
                          const { data: { user }, error: userError } = await supabase.auth.getUser();

                          if (userError || !user) {
                            throw new Error('User not authenticated');
                          }

                          // Update profile in database
                          const { error: updateError } = await supabase
                            .from('profiles')
                            .update({ mfa_enabled: newEnabledState })
                            .eq('user_id', user.id);

                          if (updateError) {
                            throw updateError;
                          }

                          // If disabling MFA, also unenroll all factors
                          if (!newEnabledState && hasMFA) {
                            // Get all factors
                            const { data, error: listError } = await supabase.auth.mfa.listFactors();

                            if (listError) {
                              throw listError;
                            }

                            // Combine all factor types
                            const allFactors = [...(data.totp || []), ...(data.phone || [])];
                            const verifiedFactors = allFactors.filter(factor => factor.status === 'verified');

                            // Unenroll each verified factor
                            for (const factor of verifiedFactors) {
                              await supabase.auth.mfa.unenroll({ factorId: factor.id });
                            }

                            // Refresh MFA status
                            await checkMFAStatus();
                          }

                          // Show success message
                          setSuccessMessage(`Two-factor authentication ${newEnabledState ? 'enabled' : 'disabled'} successfully`);

                          // If enabling MFA and no factors are set up, show enrollment UI
                          if (newEnabledState && !hasMFA) {
                            setShowMFAEnrollment(true);
                          }
                        } catch (err: any) {
                          console.error('Error updating MFA setting:', err);
                          setError(err.message || 'Failed to update MFA setting');
                          // Revert UI state on error
                          setMfaEnabled(!mfaEnabled);
                        } finally {
                          setUpdatingMfaSetting(false);
                        }
                      }}
                    >
                      {updatingMfaSetting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : mfaEnabled ? (
                        'Disable'
                      ) : (
                        'Enable'
                      )}
                    </Button>
                  </div>

                  {/* MFA Enrollment UI */}
                  {mfaEnabled && (
                    <div className="mt-4 border-t pt-4">
                      {hasMFA ? (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="text-sm font-medium">Authenticator App</h4>
                              <p className="text-sm text-muted-foreground">
                                You have set up an authenticator app
                              </p>
                            </div>
                          </div>
                          <UnenrollMFA
                            onUnenrolled={handleMFAUnenrollSuccess}
                          />
                        </div>
                      ) : showMFAEnrollment ? (
                        <EnrollMFA
                          onEnrolled={handleMFAEnrollSuccess}
                          onCancelled={() => {
                            setShowMFAEnrollment(false);
                            // If cancelling enrollment and no factors are set up, disable MFA
                            if (!hasMFA) {
                              setMfaEnabled(false);
                            }
                          }}
                        />
                      ) : (
                        <div className="space-y-4">
                          <p className="text-sm text-yellow-600">
                            You have enabled two-factor authentication but haven't set up any authentication methods yet.
                          </p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowMFAEnrollment(true)}
                          >
                            Set up authenticator app
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Password Section */}
                <div className="border rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-2">Password</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Update your password to keep your account secure
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setIsChangePasswordModalOpen(true)}
                  >
                    Change Password
                  </Button>

                  {/* Change Password Modal */}
                  <ChangePasswordModal
                    isOpen={isChangePasswordModalOpen}
                    onClose={() => {
                      setIsChangePasswordModalOpen(false);
                      // Show success message
                      setSuccessMessage('Password changed successfully');
                      // Clear success message after 5 seconds
                      setTimeout(() => setSuccessMessage(''), 5000);
                    }}
                  />
                </div>

                {/* Device Management Section - Temporarily Disabled
                <div className="border rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-2">Device Management</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Manage your registered devices and active sessions
                  </p>
                  <div>
                    <h3 className="text-sm font-medium mb-1">Registered Devices</h3>
                    <p className="text-sm text-muted-foreground">
                      You can have up to 3 registered devices. View and manage your devices below.
                    </p>
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => setIsManageSessionsModalOpen(true)}
                  >
                    Manage Devices
                  </Button>

                  Sessions/Devices Management Modal
                  <ManageSessionsModal
                    isOpen={isManageSessionsModalOpen}
                    onClose={() => setIsManageSessionsModalOpen(false)}
                  />
                </div>
                */}
              </div>
            </TabsContent>

            {/* Preferences Tab */}
            <TabsContent value="preferences" className="p-6"> {/* Added padding */}
              <div className="space-y-6">
                <div className="border rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-2">Appearance</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Customise how the application looks and feels
                  </p>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium mb-1">Theme</h3>
                      <p className="text-sm text-muted-foreground">
                        Choose between light and dark mode
                      </p>
                    </div>

                    <div className="flex space-x-2">
                      <Button variant="outline">Light</Button>
                      <Button variant="outline">Dark</Button>
                      <Button variant="outline">System</Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Notifications Tab */}
            <TabsContent value="notifications" className="p-6 overflow-y-auto">
              <div className="space-y-6">
                <div className=" max-h-[calc(100vh-200px)] overflow-y-auto pr-2">
                  <NotificationPreferences />
                </div>
              </div>
            </TabsContent>

            {/* Scenario Metrics Tab */}
            <TabsContent value="scenario-metrics" className="p-6 overflow-y-auto max-h-[calc(100vh-200px)]">
              <ScenarioMetricsTab
                user={{ id: profileData?.user_id || '' }}
                profile={{ org_id: profileData?.org_id || '' }}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}

// Main component that wraps ProfileContent with Suspense
export default function ProfilePage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading profile...</span>
    </div>}>
      <ProfileContent />
    </Suspense>
  );
}
