"use client";

import React, { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import SignaturePad from 'react-signature-canvas';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Download, Lock } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import * as bcrypt from 'bcryptjs';
import FormLogo from '@/components/FormLogo';

interface TOEData {
  household_members: any;
  advice_scope: {
    investment: boolean;
    kiwisaver: boolean;
    financialPlanning: boolean;
    estatePlanning: boolean;
    insurance: boolean;
    accountancy: boolean;
    budgeting: boolean;
    [key: string]: boolean; // For custom checkbox options
  };
  // One-off fee
  advice_fee: number;
  is_gst_inclusive: boolean;
  // Ongoing fee
  ongoing_fee?: number;
  ongoing_fee_type?: 'percentage' | 'fixed';
  is_ongoing_gst_inclusive?: boolean;
  // Other fields
  id: number;
  household_id: number;
  client_name: string;
  status: string;
  signature: string;
  partner_signature?: string;
  signature_date: string;
  ip_address: string;
  terms: string;
  adviser_name?: string;
  org_name?: string;
  disclaimer_checked?: boolean;
}

export default function TOEForm({ params }: { params: { token: string } }) {
  // Authentication and password protection states
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [passwordVerified, setPasswordVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // Form data states - only used after password verification
  const [toeData, setTOEData] = useState<TOEData | null>(null);
  const [signature, setSignature] = useState<string>('');
  const [signaturePad, setSignaturePad] = useState<any>(null);
  const [partnerSignaturePad, setPartnerSignaturePad] = useState<any>(null);
  const [disclaimerChecked, setDisclaimerChecked] = useState<{[key: string]: boolean}>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const [templateData, setTemplateData] = useState<any | null>(null);
  const router = useRouter();

  // First useEffect - only check for password protection
  useEffect(() => {
    const checkPasswordProtection = async () => {
      setInitialLoading(true);
      const supabase = createClient();

      // Check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      setIsLoggedIn(!!user);

      // Only check for password protection initially
      const { data, error } = await supabase
        .from('toe_tokens')
        .select('password_hash, status')
        .eq('token', params.token)
        .single();

      if (error) {
        setError('Invalid or expired link');
        toast.error('Invalid or expired link');
        setInitialLoading(false);
        return;
      }

      // Check if the form is password protected
      if (data.password_hash) {
        setIsPasswordProtected(true);
        // If user is logged in, bypass password protection
        if (user) {
          setPasswordVerified(true);
        }
      } else {
        // No password protection, consider it verified
        setPasswordVerified(true);
      }

      setInitialLoading(false);
    };

    checkPasswordProtection();
  }, [params.token]);

  // State for adviser's organization ID
  const [adviserOrgId, setAdviserOrgId] = useState<string | null>(null);

  // Second useEffect - only runs after password verification
  useEffect(() => {
    // Only fetch form data if password is verified or user is logged in
    if (!passwordVerified && !isLoggedIn) return;

    const fetchTOEData = async () => {
      setLoading(true);
      const supabase = createClient();

      // Get the TOE token data
      const { data, error } = await supabase
        .from('toe_tokens')
        .select(`
          id, household_id, client_name, status, signature, partner_signature,
          signature_date, ip_address, terms, advice_scope,
          advice_fee, is_gst_inclusive, adviser_id, created_by,
          template_id, ongoing_fee, ongoing_fee_type, is_ongoing_gst_inclusive, token
        `)
        .eq('token', params.token)
        .single();

      // If there's a template_id, fetch the template data
      if (data?.template_id) {
        const { data: template, error: templateError } = await supabase
          .from('templates')
          .select('*')
          .eq('id', data.template_id)
          .single();

        if (!templateError && template) {
          try {
            const parsedContent = JSON.parse(template.content);
            setTemplateData(parsedContent);
          } catch (e) {
            console.error('Error parsing template content:', e);
          }
        }
      }

      if (error || !data) {
        setError('Invalid or expired link');
        toast.error('Invalid or expired link');
        setLoading(false);
        return;
      }

      // If the form is completed, show it in read-only mode
      if (data.status === 'completed') {
        setIsReadOnly(true);
        if (!isLoggedIn) {
          toast('This form has already been submitted and cannot be modified');
        }
      } else if (data.status !== 'pending') {
        // If the form is not pending and the user is not logged in, show error
        if (!isLoggedIn) {
          setError('This form is no longer accessible');
          toast.error('This form is no longer accessible');
          setLoading(false);
          return;
        }
        // For logged in users, show in read-only mode
        setIsReadOnly(true);
      }

      // If there's an adviser_id, get their name and organization
      let adviserName = undefined;
      let adviserOrgName = undefined;
      if (data.adviser_id) {
        const { data: adviserData, error: adviserError } = await supabase
          .from('profiles')
          .select('name, org_name, org_id')
          .eq('id', data.adviser_id)
          .single();

        if (!adviserError && adviserData) {
          adviserName = adviserData.name;
          adviserOrgName = adviserData.org_name;
          setAdviserOrgId(adviserData.org_id);
        }
      }

      // If we couldn't get the org_id from adviser_id, try with created_by
      if (!adviserOrgId && data.created_by) {
        const { data: creatorProfile, error: creatorError } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', data.created_by)
          .single();

        if (!creatorError && creatorProfile) {
          setAdviserOrgId(creatorProfile.org_id);
        }
      }

      // Fetch household members data if we have a household_id
      let householdMembers = null;
      if (data.household_id) {
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('members')
          .eq('id', data.household_id)
          .single();

        if (!householdError && householdData?.members) {
          householdMembers = householdData.members;
        }
      }

      setTOEData({...data, adviser_name: adviserName, org_name: adviserOrgName, household_members: householdMembers});
      setLoading(false);
    };

    fetchTOEData();
  }, [params.token, passwordVerified, isLoggedIn]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!toeData) return;

    // Check if any disclaimer sections are required but not checked
    const hasUncheckedDisclaimers = templateData?.some((section: any) =>
      section.type === 'disclaimer' &&
      section.content?.required === true &&
      !disclaimerChecked[section.id]
    );

    if (hasUncheckedDisclaimers) {
      toast.error('Please check all required disclaimers');
      return;
    }

    // Check if main signature is provided
    if (!signaturePad || signaturePad.isEmpty()) {
      toast.error('Please provide your signature');
      return;
    }

    // Check if partner signature is required but not provided
    const hasPartnerSignature = templateData?.some((section: any) =>
      section.type === 'signature' &&
      section.content?.showPartner === true
    );

    if (hasPartnerSignature && (!partnerSignaturePad || partnerSignaturePad.isEmpty())) {
      toast.error('Please provide partner signature');
      return;
    }

    const supabase = createClient();
    const signatureData = signaturePad.toDataURL();
    const partnerSignatureData = hasPartnerSignature ? partnerSignaturePad.toDataURL() : null;

    try {
      // Get IP address
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      const ipData = await ipResponse.json();
      const ipAddress = ipData.ip;

      const { error } = await supabase
        .from('toe_tokens')
        .update({
          status: 'completed',
          signature: signatureData,
          partner_signature: partnerSignatureData, // Add partner signature if available
          signature_date: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          ip_address: ipAddress,
          disclaimer_checked: Object.keys(disclaimerChecked).filter(id => disclaimerChecked[id]).length > 0 ? true : null
        })
        .eq('id', toeData.id);

      if (error) {
        toast.error('Error submitting form');
        console.error(error);
        return;
      }

      // Fetch the token data to get the created_by user ID
      const { data: tokenData, error: tokenError } = await supabase
        .from('toe_tokens')
        .select('created_by, household_id')
        .eq('id', toeData.id)
        .single();

      if (tokenError) {
        console.error('Error fetching token data:', tokenError);
      } else if (tokenData) {
        // Fetch household name
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', tokenData.household_id)
          .single();

        const householdName = householdError ? 'Unknown Household' : householdData?.householdName;

        // Create a notification for the user who created the TOE form
        await supabase.from('notifications').insert({
          user_id: tokenData.created_by,
          content: `Terms of Engagement for ${householdName} has been completed`,
          type: 'toe_form_submission',
          link: `/protected/households/household/${tokenData.household_id}`,
          created_at: new Date().toISOString()
        });
      }

      toast.success('Form submitted successfully');
      router.push('/submission-success');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Error submitting form');
    }
  };

  const verifyPassword = async () => {
    setPasswordError(null);

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('toe_tokens')
        .select('password_hash')
        .eq('token', params.token)
        .single();

      if (error || !data || !data.password_hash) {
        setPasswordError('Could not verify password. Please try again.');
        return;
      }

      // Compare the entered password with the stored hash
      const isValid = await bcrypt.compare(password, data.password_hash);

      if (isValid) {
        setPasswordVerified(true);
      } else {
        setPasswordError('Incorrect password. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setPasswordError('An error occurred while verifying the password.');
    }
  };

  // Initial loading state - checking if password protection is needed
  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto mb-4"></div>
          <p className="text-xl text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  // Show password prompt if the form is password protected and not yet verified
  if (isPasswordProtected && !passwordVerified && !isLoggedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Password Protected Form
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                This form is password protected. Please enter the password to continue.
              </p>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
                {passwordError && (
                  <p className="text-sm text-red-500">{passwordError}</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={verifyPassword} className="w-full">
              Continue
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Loading state for form data - only shown after password verification
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto mb-4"></div>
          <p className="text-xl text-gray-700">Loading Terms of Engagement...</p>
        </div>
      </div>
    );
  }

  if (error || !toeData) {
    return (
      <div className="max-w-4xl mx-auto p-8 text-center min-h-screen flex flex-col justify-center bg-gray-50">
        <div className="bg-white shadow-md rounded-lg p-8">
          <h1 className="text-3xl font-bold text-red-600 mb-4">Form Unavailable</h1>
          <p className="text-lg text-gray-700 mb-4">{error || 'The Terms of Engagement form is not available.'}</p>
          <Button
            onClick={() => router.push('/')}
            className="mx-auto"
          >
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  const isCompleted = toeData.status === 'completed' || isReadOnly;

  // Format currency with 2 decimal places
  const formatCurrency = (amount: number | undefined | null) => {
    // Default to 0 if amount is undefined or null
    const safeAmount = amount ?? 0;
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
      minimumFractionDigits: 2
    }).format(safeAmount);
  };

  // Render advice scope section
  const renderAdviceScope = () => {
    if (!toeData?.advice_scope) return null;

    const scope = toeData.advice_scope;
    const includedItems: string[] = [];
    const excludedItems: string[] = [];

    // Map all possible scope items
    const allScopeItems = [
      { key: 'investment', label: 'Investment Management' },
      { key: 'kiwisaver', label: 'KiwiSaver' },
      { key: 'financialPlanning', label: 'Financial Planning' },
      { key: 'estatePlanning', label: 'Estate Planning' },
      { key: 'insurance', label: 'Insurance' },
      { key: 'accountancy', label: 'Accountancy' },
      { key: 'budgeting', label: 'Budgeting' }
    ];

    // Sort items into included and excluded
    allScopeItems.forEach(item => {
      // Use type assertion to tell TypeScript this is a valid key
      if (scope[item.key as keyof typeof scope]) {
        includedItems.push(item.label);
      } else {
        excludedItems.push(item.label);
      }
    });

    return (
      <div className="space-y-4 text-sm">
        <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Advice Scope</h3>
        <div>
          <h4 className="font-medium mb-2">Included in Scope:</h4>
          <ul className="list-disc pl-5 space-y-1">
            {includedItems.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>

        {excludedItems.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Excluded from Scope:</h4>
            <ul className="list-disc pl-5 space-y-1 text-gray-600">
              {excludedItems.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  // Render fee information section
  const renderFeeInformation = () => {
    // Check if we have any fee information to display
    const hasOneOffFee = toeData?.advice_fee !== null && toeData?.advice_fee !== undefined;
    const hasOngoingFee = toeData?.ongoing_fee !== null && toeData?.ongoing_fee !== undefined;

    if (!hasOneOffFee && !hasOngoingFee) return null;

    return (
      <div>
        <h3 className="font-medium text-gray-600 mb-3 text-sm uppercase tracking-wider">Fee Information</h3>

        <div className="space-y-4">
          {/* One-off fee */}
          {hasOneOffFee && (
            <div className="bg-white border rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-700">One-Off Advice Fee</h4>
                <div className="bg-primary/10 text-primary font-semibold px-3 py-1 rounded-full text-sm">
                  {formatCurrency(toeData.advice_fee)}
                </div>
              </div>
              <div className="mt-1 text-right">
                <span className="text-gray-600 text-sm">
                  {toeData.is_gst_inclusive ? "Including GST" : "Excluding GST"}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2 border-t pt-2">
                This is a one-time fee for the advice provided.
              </p>
            </div>
          )}

          {/* Ongoing fee */}
          {hasOngoingFee && (
            <div className="bg-white border rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-700">Ongoing Advice Fee</h4>
                <div className="bg-primary/10 text-primary font-semibold px-3 py-1 rounded-full text-sm">
                  {toeData.ongoing_fee_type === 'percentage' ?
                    `${toeData.ongoing_fee ?? 0}% p.a.` :
                    `${formatCurrency(toeData.ongoing_fee)} per month`}
                </div>
              </div>
              <div className="mt-1 text-right">
                <span className="text-gray-600 text-sm">
                  {toeData.is_ongoing_gst_inclusive ? "Including GST" : "Excluding GST"}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2 border-t pt-2">
                {toeData.ongoing_fee_type === 'percentage' ?
                  'This fee is calculated as a percentage of your assets under management and charged annually.' :
                  'This is a fixed monthly fee for ongoing financial advice and services.'}
              </p>
            </div>
          )}

          {/* No fees message */}
          {!hasOneOffFee && !hasOngoingFee && (
            <div className="bg-gray-50 border border-dashed rounded-lg p-4 text-center text-gray-500">
              <p>No fee information available</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render a section based on its type
  const renderSection = (section: any) => {
    if (!section || !toeData) return null;

    switch (section.type) {
      case 'text':
        return (
          <div className="prose max-w-none">
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: section.content?.html || '' }} className="whitespace-pre-wrap text-sm indent-2" />
          </div>
        );

      case 'checkbox':
        // For custom template checkbox section
        if (section.content?.options) {
          // Split options into included and excluded
          const includedOptions: any[] = [];
          const excludedOptions: any[] = [];

          section.content.options.forEach((option: any) => {
            // Check if this option is checked in the advice_scope
            const isChecked = toeData.advice_scope && toeData.advice_scope[option.id] === true;

            if (isChecked) {
              includedOptions.push(option);
            } else {
              excludedOptions.push(option);
            }
          });

          return (
            <div>
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <div className="grid grid-cols-2 gap-4">
                {/* Left column - Included items */}
                <div>
                  <h4 className="font-medium mb-2">Included in Scope:</h4>
                  {includedOptions.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1">
                      {includedOptions.map((option: any) => (
                        <li key={option.id}>{option.label}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-sm italic">No items included</p>
                  )}
                </div>

                {/* Right column - Excluded items */}
                <div>
                  <h4 className="font-medium mb-2">Excluded from Scope:</h4>
                  {excludedOptions.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1 text-gray-600">
                      {excludedOptions.map((option: any) => (
                        <li key={option.id}>{option.label}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-sm italic">No items excluded</p>
                  )}
                </div>
              </div>
            </div>
          );
        }
        // For default template advice scope
        return renderAdviceScope();

      case 'fee':
        // For custom template fee section
        // Check if we have the new fee structure with showOneOff and showOngoing
        if (section.content?.showOneOff !== undefined || section.content?.showOngoing !== undefined) {
          // Use the renderFeeInformation function which already handles both fee types
          return renderFeeInformation();
        }
        // For older templates with defaultAmount (backward compatibility)
        else if (section.content?.defaultAmount !== undefined) {
          return (
            <div>
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <p className="text-lg">
                Advice Fee: {formatCurrency(section.content.defaultAmount)}
                <span className="text-gray-600 text-sm ml-1">
                  {section.content.isGstInclusive ? "(including GST)" : "(excluding GST)"}
                </span>
              </p>
            </div>
          );
        }
        // For default template fee information
        return renderFeeInformation();

      case 'client_info':
        // Check if we should show main client, partner, or both
        const showMain = section.content?.showMain !== false;
        const showPartner = section.content?.showPartner === true;

        // Get client names from household data if available
        let mainName = '';
        let partnerName = '';

        if (toeData.household_members) {
          // Try to extract names from household members
          const members = toeData.household_members;
          // Combine first and last names
          const firstName1 = members.name1 || '';
          const lastName1 = members.last_name1 || '';
          const firstName2 = members.name2 || '';
          const lastName2 = members.last_name2 || '';

          mainName = firstName1 && lastName1 ? `${firstName1} ${lastName1}` : firstName1 || '';
          partnerName = firstName2 && lastName2 ? `${firstName2} ${lastName2}` : firstName2 || '';
        } else {
          // Fallback to client_name if no household members data
          mainName = toeData.client_name || '';
        }

        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title || 'Client Information'}</h3>

            {/* Show main client if enabled */}
            {showMain && mainName && (
              <p className="text-lg font-semibold text-gray-800">{mainName}</p>
            )}

            {/* Show partner if enabled and available */}
            {showPartner && partnerName && (
              <p className="text-lg font-semibold text-gray-800">{partnerName}</p>
            )}

            {/* Fallback if no specific names are shown */}
            {(!showMain && !showPartner) || (!mainName && !partnerName) ? (
              <p className="text-lg font-semibold text-gray-800">{toeData.client_name || 'Client'}</p>
            ) : null}
          </div>
        );

      case 'adviser_info':
        return toeData.adviser_name ? (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title || 'Adviser Information'}</h3>
            <p className="text-lg font-semibold text-gray-800">{toeData.adviser_name}</p>
            {toeData.org_name && (
              <p className="text-sm text-gray-600">{toeData.org_name}</p>
            )}
          </div>
        ) : null;

      case 'date':
        return (
          <div>
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Date</h3>
            <p className="text-gray-800">{new Date().toLocaleDateString('en-NZ', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}</p>
          </div>
        );

      case 'terms':
        return (
          <div className="prose max-w-none">
            <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
            <p className="whitespace-pre-wrap text-sm indent-2">{toeData.terms}</p>
          </div>
        );

      case 'disclaimer':
        // Process the disclaimer text to replace placeholders if useAdviserInfo is enabled
        let disclaimerText = section.content?.text || 'I agree to the terms and conditions';

        if (section.content?.useAdviserInfo && toeData) {
          // Replace placeholders with actual values
          disclaimerText = disclaimerText
            .replace(/{adviser_name}/g, toeData.adviser_name || 'your Financial Adviser')
            .replace(/{org_name}/g, toeData.org_name || 'your Financial Advice Provider');
        }

        if (isCompleted) {
          // Show completed disclaimer
          return (
            <div>
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <div className="bg-white border rounded-lg p-4 shadow-sm mb-3">
                <p className="whitespace-pre-wrap text-sm text-gray-700">{disclaimerText}</p>
              </div>
              <div className="flex items-center space-x-2 mt-2 bg-gray-50 p-3 rounded-md border">
                <input type="checkbox" checked disabled className="h-4 w-4 rounded border-gray-300" />
                <span className="text-sm font-medium">I confirm I have read and agree to the above</span>
              </div>
            </div>
          );
        } else {
          // Show active disclaimer with checkbox
          return (
            <div>
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <div className="bg-white border rounded-lg p-4 shadow-sm mb-3">
                <p className="whitespace-pre-wrap text-sm text-gray-700">{disclaimerText}</p>
              </div>
              <div className="flex items-center space-x-2 mt-2 bg-gray-50 p-3 rounded-md border">
                <input
                  type="checkbox"
                  id={`disclaimer-${section.id}`}
                  checked={disclaimerChecked[section.id] || false}
                  onChange={(e) => setDisclaimerChecked(prev => ({ ...prev, [section.id]: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor={`disclaimer-${section.id}`} className="text-sm font-medium">
                  I confirm I have read and agree to the above
                </label>
              </div>
              {section.content?.required && (
                <p className="text-xs text-red-500 mt-1">* Required</p>
              )}
            </div>
          );
        }

      case 'signature':
        // Check if this signature section includes partner signature
        const showPartnerSignature = section.content?.showPartner === true;

        // Get client names from household data
        // Use different variable names to avoid redefinition
        let signatureMainName = '';
        let signaturePartnerName = '';

        if (toeData.household_members) {
          const members = toeData.household_members;
          const firstName1 = members.name1 || '';
          const lastName1 = members.last_name1 || '';
          const firstName2 = members.name2 || '';
          const lastName2 = members.last_name2 || '';

          signatureMainName = firstName1 && lastName1 ? `${firstName1} ${lastName1}` : firstName1 || '';
          signaturePartnerName = firstName2 && lastName2 ? `${firstName2} ${lastName2}` : firstName2 || '';
        } else {
          signatureMainName = toeData.client_name || '';
        }

        if (isCompleted) {
          // Show completed signatures
          return (
            <div className="space-y-6">
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <p className="text-sm text-gray-600">{section.content?.text || ''}</p>

              {showPartnerSignature ? (
                // Show both signatures side by side
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {/* Main signature */}
                  <div>
                    <p className="text-sm font-medium mb-1">{signatureMainName || 'Main Client'}</p>
                    <div className="border rounded-lg p-4 bg-white">
                      {toeData.signature ? (
                        <img
                          src={toeData.signature}
                          alt="Digital Signature"
                          className="max-w-full h-40 object-contain"
                        />
                      ) : (
                        <div className="text-amber-600 p-4 bg-amber-50 rounded-md">
                          <p>No signature provided</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Partner signature */}
                  <div>
                    <p className="text-sm font-medium mb-1">{signaturePartnerName || 'Partner'}</p>
                    <div className="border rounded-lg p-4 bg-white">
                      {toeData.partner_signature ? (
                        <img
                          src={toeData.partner_signature}
                          alt="Partner Digital Signature"
                          className="max-w-full h-40 object-contain"
                        />
                      ) : (
                        <div className="text-amber-600 p-4 bg-amber-50 rounded-md">
                          <p>No signature provided</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                // Show single signature
                <div>
                  <p className="text-sm font-medium mb-1">{signatureMainName || 'Client'}</p>
                  <div className="border rounded-lg p-4 bg-white">
                    {toeData.signature ? (
                      <img
                        src={toeData.signature}
                        alt="Digital Signature"
                        className="max-w-full h-40 object-contain"
                      />
                    ) : (
                      <div className="text-amber-600 p-4 bg-amber-50 rounded-md">
                        <p>No signature provided</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="text-xs text-gray-500">
                <p>Signed on: {new Date(toeData.signature_date).toLocaleString()}</p>
                <p>IP Address: {toeData.ip_address}</p>
              </div>
            </div>
          );
        } else {
          // Show signature input fields
          return (
            <div>
              <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">{section.title}</h3>
              <p className="mb-4 text-sm">
                {section.content?.text || 'By signing below, I confirm that I have read, understood, and agree to the terms and conditions outlined in this document.'}
              </p>

              {showPartnerSignature ? (
                // Show both signature pads side by side
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {/* Main signature */}
                  <div>
                    <p className="text-sm font-medium mb-1">{signatureMainName || 'Main Client'}</p>
                    <div className="border rounded-lg p-4 bg-gray-100">
                      <SignaturePad
                        ref={(ref) => setSignaturePad(ref)}
                        canvasProps={{
                          className: 'signature-canvas w-full h-40 border rounded bg-white'
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="mt-2 text-sm"
                        onClick={() => signaturePad?.clear()}
                      >
                        Clear Signature
                      </Button>
                    </div>
                  </div>

                  {/* Partner signature */}
                  <div>
                    <p className="text-sm font-medium mb-1">{signaturePartnerName || 'Partner'}</p>
                    <div className="border rounded-lg p-4 bg-gray-100">
                      <SignaturePad
                        ref={(ref) => setPartnerSignaturePad(ref)}
                        canvasProps={{
                          className: 'signature-canvas w-full h-40 border rounded bg-white'
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="mt-2 text-sm"
                        onClick={() => partnerSignaturePad?.clear()}
                      >
                        Clear Signature
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                // Show single signature pad
                <div>
                  <p className="text-sm font-medium mb-1">{signatureMainName || 'Client'}</p>
                  <div className="border rounded-lg p-4 bg-gray-100">
                    <SignaturePad
                      ref={(ref) => setSignaturePad(ref)}
                      canvasProps={{
                        className: 'signature-canvas w-full h-40 border rounded bg-white'
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-2 text-sm"
                      onClick={() => signaturePad?.clear()}
                    >
                      Clear Signature
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );
        }

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4 md:p-8 bg-gray-50 min-h-screen">
        <div className="space-y-6 animate-fade-in">
          <Card className="shadow-md hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="bg-primary/5 border-b border-gray-200 flex flex-row items-center justify-between">
              <CardTitle className="text-2xl font-bold text-primary">Terms of Engagement</CardTitle>
              <FormLogo orgId={adviserOrgId || undefined} />
            </CardHeader>
          <CardContent className="pt-6 space-y-6">
            {templateData ? (
              // Render using custom template
              <>
                {
                  // Group sections by layout type for rendering
                  (() => {
                    const renderedSections = [];
                    let columnSections = [];

                    for (let i = 0; i < templateData.length; i++) {
                      const section = templateData[i];
                      console.log('Processing section:', section);

                      if (section.layout === 'column') {
                        // Add to column sections
                        columnSections.push(section);

                        // If we have 2 column sections or this is the last section, render the row
                        if (columnSections.length === 2 || i === templateData.length - 1) {
                          renderedSections.push(
                            <div key={`column-row-${i}`} className="grid grid-cols-2 gap-4">
                              {columnSections.map(colSection => (
                                <div key={colSection.id || `col-${i}`}>
                                  {renderSection(colSection)}
                                </div>
                              ))}
                            </div>
                          );

                          // Add separator if not the last section
                          if (i < templateData.length - 1) {
                            renderedSections.push(
                              <Separator key={`separator-${i}`} className="my-4" />
                            );
                          }

                          // Clear column sections
                          columnSections = [];
                        }
                      } else {
                        // If we have any pending column sections, render them first
                        if (columnSections.length > 0) {
                          renderedSections.push(
                            <div key={`column-row-${i}-pending`} className="grid grid-cols-2 gap-4">
                              {columnSections.map(colSection => (
                                <div key={colSection.id || `col-pending-${i}`}>
                                  {renderSection(colSection)}
                                </div>
                              ))}
                            </div>
                          );

                          // Add separator
                          renderedSections.push(
                            <Separator key={`separator-pending-${i}`} className="my-4" />
                          );

                          columnSections = [];
                        }

                        // Render full-width section
                        renderedSections.push(
                          <React.Fragment key={section.id || i}>
                            {renderSection(section)}
                            {i < templateData.length - 1 && (
                              <Separator className="my-4" />
                            )}
                          </React.Fragment>
                        );
                      }
                    }

                    return renderedSections;
                  })()
                }

                {!isCompleted && (
                  <div className="flex items-center space-x-4 mt-6">
                    <Button type="button" onClick={handleSubmit} className="w-full">
                      Submit Form
                    </Button>
                  </div>
                )}
              </>
            ) : (
              // Render using default layout
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Client</h3>
                    <p className="text-lg font-semibold text-gray-800">{toeData.client_name}</p>
                  </div>
                  {toeData.adviser_name && (
                    <div>
                      <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Adviser</h3>
                      <p className="text-lg font-semibold text-gray-800">{toeData.adviser_name}</p>
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                <div>
                  <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Date</h3>
                  <p className="text-gray-800">{new Date().toLocaleDateString('en-NZ', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</p>
                </div>

                <Separator className="my-4" />

                {renderAdviceScope()}

                <Separator className="my-4" />

                <div className="prose max-w-none">
                  <h3 className="font-medium text-gray-600 mb-1 text-sm uppercase tracking-wider">Terms and Conditions</h3>
                  <p className="whitespace-pre-wrap text-sm indent-2">{toeData.terms}</p>
                </div>

                <Separator className="my-4" />

                {renderFeeInformation()}

                <Separator className="my-4" />

                {isCompleted ? (
                  <div className="space-y-6">
                    {toeData.signature ? (
                      <>
                        <div>
                          <h3 className="font-medium mb-2 text-sm">Digital Signature</h3>
                          <div className="border rounded-lg p-4 bg-white">
                            <img
                              src={toeData.signature}
                              alt="Digital Signature"
                              className="max-w-full h-40 object-contain"
                            />
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Signed on: {new Date(toeData.signature_date).toLocaleString()}</p>
                          <p>IP Address: {toeData.ip_address}</p>
                        </div>
                      </>
                    ) : (
                      <div className="text-amber-600 p-4 bg-amber-50 rounded-md">
                        <p>This form has not been signed yet.</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <p className="mb-4 text-sm">
                        By signing below, I confirm that I have read, understood, and agree to the terms and conditions outlined in this document.
                      </p>
                      <ul className="list-disc pl-5 space-y-1 text-sm mb-4">
                        <li>I/we agree to the areas of advice noted above in this document, which is based on the full and accurate information I/we have provided.</li>
                        <li>I/we understand the information we have been provided about {toeData.org_name} as a Financial Advice Provider, and {toeData.adviser_name} as my Financial Adviser.</li>
                        <li>I/we confirm the agreed advice plan fee will be invoiced.</li>
                        <li>I/we confirm that the email address noted below is correct for servicing communication.</li>
                      </ul>

                      <h3 className="font-medium mb-2 text-sm">Digital Signature</h3>
                      <div className="border rounded-lg p-4 bg-gray-100">
                        <SignaturePad
                          ref={(ref) => setSignaturePad(ref)}
                          canvasProps={{
                            className: 'signature-canvas w-full h-40 border rounded bg-white'
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="mt-2 text-sm"
                          onClick={() => signaturePad?.clear()}
                        >
                          Clear Signature
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <Button type="submit" className="w-full">
                        Submit Form
                      </Button>
                    </div>
                  </form>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {isLoggedIn && (
          <Card className="mt-4 shadow-md hover:shadow-lg transition-shadow duration-300">
            <CardContent className="pt-6">
              <div className="text-sm text-gray-600">
                <p>You are viewing this form as a logged-in user. You can download this form as a PDF using the button below.</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => window.print()}
                className="flex items-center gap-2"
              >
                <Download size={16} />
                Download as PDF
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
