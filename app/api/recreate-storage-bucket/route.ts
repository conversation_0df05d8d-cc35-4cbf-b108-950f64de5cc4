import { NextRequest, NextResponse } from 'next/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create a Supabase admin client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createSupabaseClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(request: NextRequest) {
  try {
    console.log('Recreating storage bucket with new policies...');

    // Check if the media bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      return NextResponse.json(
        { error: listError.message },
        { status: 500 }
      );
    }

    const mediaBucketExists = buckets?.some(bucket => bucket.name === 'media');

    // If the bucket exists, delete it
    if (mediaBucketExists) {
      console.log('Media bucket exists, deleting it...');

      // First, list all objects in the bucket
      const { data: objects, error: listObjectsError } = await supabaseAdmin.storage
        .from('media')
        .list();

      if (listObjectsError) {
        console.error('Error listing objects in media bucket:', listObjectsError);
      } else if (objects && objects.length > 0) {
        // Delete all objects in the bucket
        console.log(`Found ${objects.length} objects in the bucket, deleting them...`);

        // Delete objects in batches of 100
        const batchSize = 100;
        for (let i = 0; i < objects.length; i += batchSize) {
          const batch = objects.slice(i, i + batchSize);
          const paths = batch.map(obj => obj.name);

          const { error: deleteObjectsError } = await supabaseAdmin.storage
            .from('media')
            .remove(paths);

          if (deleteObjectsError) {
            console.error('Error deleting objects:', deleteObjectsError);
          }
        }
      }

      // Delete the bucket
      const { error: deleteBucketError } = await supabaseAdmin.storage.deleteBucket('media');

      if (deleteBucketError) {
        console.error('Error deleting media bucket:', deleteBucketError);
        return NextResponse.json(
          { error: deleteBucketError.message },
          { status: 500 }
        );
      }

      console.log('Media bucket deleted successfully');
    }

    // Create the media bucket
    console.log('Creating new media bucket...');
    const { error: createError } = await supabaseAdmin.storage.createBucket('media', {
      public: false,
      fileSizeLimit: 5242880, // 5MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml']
    });

    if (createError) {
      console.error('Error creating media bucket:', createError);
      return NextResponse.json(
        { error: createError.message },
        { status: 500 }
      );
    }

    console.log('Media bucket created successfully');

    // Set up RLS policies for the media bucket
    try {
      console.log('Setting up RLS policies for media bucket...');

      // Use SQL to create policies directly
      // SELECT policy - Allow authenticated users to read any files
      await supabaseAdmin.rpc('create_storage_policy', {
        bucket_name: 'media',
        policy_name: 'Public Read Access',
        definition: "bucket_id = 'media'",
        operation: 'SELECT'
      });

      // INSERT policy - Allow authenticated users to upload files
      await supabaseAdmin.rpc('create_storage_policy', {
        bucket_name: 'media',
        policy_name: 'Allow authenticated uploads',
        definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
        operation: 'INSERT'
      });

      // UPDATE policy - Allow authenticated users to update any files
      await supabaseAdmin.rpc('create_storage_policy', {
        bucket_name: 'media',
        policy_name: 'Allow authenticated updates',
        definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
        operation: 'UPDATE'
      });

      // DELETE policy - Allow authenticated users to delete any files
      await supabaseAdmin.rpc('create_storage_policy', {
        bucket_name: 'media',
        policy_name: 'Allow authenticated deletes',
        definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
        operation: 'DELETE'
      });

      console.log('RLS policies set up successfully');
    } catch (policyError) {
      console.warn('Could not set RLS policies automatically:', policyError);
      // Continue anyway, as the bucket was created
    }

    return NextResponse.json({
      success: true,
      message: 'Storage bucket recreated successfully with new policies'
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
