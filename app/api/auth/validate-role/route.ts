/**
 * API Route: Role Validation
 *
 * Server-side role validation endpoint that cannot be bypassed by client manipulation.
 * Used by SecureClientWrapper to validate user roles.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthUser } from '@/utils/auth-guard';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Validate authentication first
    const user = await getAuthUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the allowed roles from request body
    const { allowedRoles } = await request.json();
    
    if (!allowedRoles || !Array.isArray(allowedRoles)) {
      return NextResponse.json(
        { error: 'Invalid roles specified' },
        { status: 400 }
      );
    }

    // Get user profile to check role
    const supabase = createClient();
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('org_role')
      .eq('user_id', user.id)
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }

    if (!profile || !allowedRoles.includes(profile.org_role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      role: profile.org_role
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Role validation failed' },
      { status: 500 }
    );
  }
}
