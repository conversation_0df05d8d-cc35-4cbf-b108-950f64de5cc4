/**
 * API Route: MFA Validation
 *
 * Server-side MFA validation endpoint that cannot be bypassed by client manipulation.
 * Used by SecureClientWrapper to validate MFA status.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthUser } from '@/utils/auth-guard';
import { getMFAStatusServer } from '@/utils/server-mfa-security';

export async function GET(_request: NextRequest) {
  try {
    // Validate authentication first
    const user = await getAuthUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check MFA status
    const mfaStatus = await getMFAStatusServer();

    if (mfaStatus.needsChallenge) {
      return NextResponse.json(
        {
          error: 'MFA verification required',
          needsChallenge: true
        },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      needsChallenge: mfaStatus.needsChallenge,
      isVerified: mfaStatus.isVerified
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'MFA validation failed' },
      { status: 500 }
    );
  }
}
