import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import fs from 'fs';
import { downloadFileFromStorage } from '@/utils/video-processing';

// Mark this route as dynamic
export const dynamic = 'force-dynamic';

// This would typically be stored in a database
// We're using a global variable to share state between API routes
declare global {
  var transcriptionJobs: Record<string, {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    noteId: string;
    filePath: string;
    fileType: string;
    isVideo?: boolean;      // Whether the original file is a video
  }>;
}

// Initialize global variable if it doesn't exist
if (!global.transcriptionJobs) {
  global.transcriptionJobs = {};
}

export async function POST(request: Request) {
  try {
    const {
      filePath,
      displayFilePath,
      fileName,
      fileType,
      displayFileType,
      userId,
      orgId,
      title,
      household_id
    } = await request.json();

    if (!filePath || !userId || !title || !household_id) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // We'll use the provided title for the transcription

    let noteId: string;

    try {
      // Prepare the note data
      const noteData = {
        user_id: userId,
        org_id: orgId,
        household_id: household_id,
        title,
        note_type: 'transcription',
        content: JSON.stringify({ type: 'doc', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Transcription in progress...' }] }] }),
        created_at: new Date().toISOString(),
        last_edited_at: new Date().toISOString(),
        summary: `Transcription of ${fileType.startsWith('video/') ? 'video' : 'audio'} file: ${fileName}`,
        metadata: {
          media_file_path: displayFilePath || filePath, // Use display file path for the player if available
          media_file_type: displayFileType || fileType, // Use display file type for the player if available
          transcription_file_path: filePath, // Path to the file used for transcription
          transcription_status: 'pending',
          original_filename: fileName
        }
      };

      // Log the metadata for debugging
      console.log('Setting up media metadata:', {
        media_file_path: displayFilePath || filePath,
        media_file_type: displayFileType || fileType,
        transcription_file_path: filePath,
        transcription_status: 'pending',
        original_filename: fileName
      });

      // Log the data we're trying to insert
      console.log('Attempting to insert note with data:', JSON.stringify(noteData));

      // Insert the note
      const { data: insertedNote, error: noteError } = await supabase
        .from('notes')
        .insert([noteData])
        .select()
        .single();

      if (noteError) {
        console.error('Error creating note:', noteError);
        return NextResponse.json(
          { error: `Failed to create note for transcription: ${noteError.message}` },
          { status: 500 }
        );
      }

      if (!insertedNote) {
        console.error('No note data returned after insert');
        return NextResponse.json(
          { error: 'Failed to create note: No data returned' },
          { status: 500 }
        );
      }

      // Store the note ID for further processing
      noteId = insertedNote.id;
      console.log('Successfully created note with ID:', noteId);
    } catch (error) {
      console.error('Exception during note creation:', error);
      return NextResponse.json(
        { error: `Exception creating note: ${error instanceof Error ? error.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

    // Generate a unique job ID
    const jobId = uuidv4();

    // Store job information
    global.transcriptionJobs[jobId] = {
      status: 'pending',
      progress: 0,
      noteId,
      filePath,
      fileType,
      isVideo: fileType.startsWith('video/')
    };

    // Start the transcription process in the background
    processTranscription(jobId, filePath, noteId, supabase);

    return NextResponse.json({
      success: true,
      jobId,
      noteId
    });

  } catch (error) {
    console.error('Error starting transcription:', error);
    return NextResponse.json(
      { error: 'Failed to start transcription process' },
      { status: 500 }
    );
  }
}

async function processTranscription(jobId: string, filePath: string, noteId: string, supabase: any) {
  // Get information from the job
  const isVideo = global.transcriptionJobs[jobId].isVideo;

  try {
    // Update job status
    global.transcriptionJobs[jobId].status = 'processing';
    global.transcriptionJobs[jobId].progress = 10;

    console.log(`Processing ${isVideo ? 'video' : 'audio'} file: ${filePath}`);

    // Download file from Supabase storage
    let localFilePath;
    try {
      console.log(`Downloading file from media/${filePath}`);

      // Download file from Supabase storage
      localFilePath = await downloadFileFromStorage(supabase, 'media', filePath);
      global.transcriptionJobs[jobId].progress = 30;
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }

    // Configure OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    global.transcriptionJobs[jobId].progress = 50;

    // Check file size
    const stats = await fs.promises.stat(localFilePath);
    console.log(`File size: ${stats.size} bytes`);

    // For large files, we'll use a different approach
    let fileStream;
    if (stats.size > 25 * 1024 * 1024) {
      console.log('File exceeds 25MB limit, using direct transcription approach');
      global.transcriptionJobs[jobId].progress = 45;

      // For large files, we'll use a different approach
      // We'll create a readable stream directly from the file
      fileStream = fs.createReadStream(localFilePath);
    } else {
      // For smaller files, we can use the standard approach
      fileStream = fs.createReadStream(localFilePath);
    }

    global.transcriptionJobs[jobId].progress = 60;

    // Perform transcription using OpenAI
    try {
      console.log('Starting OpenAI transcription...');
      const transcription = await openai.audio.transcriptions.create({
        model: "whisper-1",
        file: fileStream,
        response_format: "verbose_json",
        timestamp_granularities: ["segment"]
      });
      console.log('OpenAI transcription completed successfully');

      global.transcriptionJobs[jobId].progress = 80;

      // Process the response with improved segmentation
      const transcriptionData = transcription as any;
      let formattedContent;

      if (transcriptionData.segments) {
        // Group segments into larger paragraphs with less frequent timestamps
        const paragraphs = [];
        let currentParagraph = { text: '', startTime: 0 };
        let wordCount = 0;
        let timeSinceLastTimestamp = 0;

        transcriptionData.segments.forEach((segment: any) => {
          const segmentDuration = segment.end - segment.start;

          // Start a new paragraph if:
          // 1. This is the first segment
          // 2. Current paragraph has more than 100 words
          // 3. It's been more than 30 seconds since the last timestamp
          // 4. Current segment ends with period + long pause
          const endsWithPeriodAndPause = /[.!?]$/.test(segment.text.trim()) &&
                                        (segment.end - segment.start > 1.5);

          if (paragraphs.length === 0 ||
              wordCount > 200 ||
              timeSinceLastTimestamp > 60 ||
              endsWithPeriodAndPause) {
            if (currentParagraph.text) {
              paragraphs.push(currentParagraph);
            }
            currentParagraph = {
              text: segment.text.trim(),
              startTime: segment.start
            };
            wordCount = segment.text.split(/\s+/).length;
            timeSinceLastTimestamp = 0;
          } else {
            currentParagraph.text += ' ' + segment.text.trim();
            wordCount += segment.text.split(/\s+/).length;
            timeSinceLastTimestamp += segmentDuration;
          }
        });

        // Add the last paragraph if it exists
        if (currentParagraph.text) {
          paragraphs.push(currentParagraph);
        }

        // Format content with timestamps for each paragraph
        const contentNodes = paragraphs.map(paragraph => {
          const startTime = formatTimestamp(paragraph.startTime);
          return {
            type: 'paragraph',
            content: [
              { type: 'text', marks: [{ type: 'bold' }], text: `[${startTime}] ` },
              { type: 'text', text: paragraph.text }
            ]
          };
        });

        formattedContent = JSON.stringify({
          type: 'doc',
          content: contentNodes
        });
      } else {
        // Fallback if segments aren't available
        formattedContent = JSON.stringify({
          type: 'doc',
          content: [{
            type: 'paragraph',
            content: [{ type: 'text', text: transcriptionData.text || '' }]
          }]
        });
      }

      // First get the current note to preserve any metadata
      const { data: currentNote, error: fetchError } = await supabase
        .from('notes')
        .select('metadata')
        .eq('id', noteId)
        .single();

      if (fetchError) {
        console.error('Error fetching current note metadata:', fetchError);
      }

      // Prepare updated metadata
      // Get the current metadata
      const currentMetadata = (currentNote?.metadata as any) || {};

      // Update only the transcription status, preserving the display file info
      const updatedMetadata = {
        ...currentMetadata,
        // Keep the original media_file_path and media_file_type for display
        transcription_status: 'completed'
      };

      console.log('Updating note with metadata:', updatedMetadata);

      // Update the note with the timestamped transcription
      const { error: updateError } = await supabase
        .from('notes')
        .update({
          content: formattedContent,
          last_edited_at: new Date().toISOString(),
          metadata: updatedMetadata
        })
        .eq('id', noteId);

      if (updateError) {
        throw new Error(`Failed to update note: ${updateError.message}`);
      }

      // Mark job as completed
      global.transcriptionJobs[jobId].status = 'completed';

      // Clean up the temporary file
      try {
        await fs.promises.unlink(localFilePath);
        console.log(`Removed temporary file: ${localFilePath}`);
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file:', cleanupError);
      }

      // Clean up job after some time
      setTimeout(() => {
        delete global.transcriptionJobs[jobId];
      }, 3600000); // Remove after 1 hour

      return;
    } catch (transcriptionError) {
      console.error('Error during OpenAI transcription:', transcriptionError);
      throw transcriptionError;
    }

    // This code is unreachable due to the return statement in the try block above
    // Keeping this comment as a reminder

  } catch (error) {
    console.error('Transcription processing error:', error);

    // Mark job as failed
    if (global.transcriptionJobs[jobId]) {
      global.transcriptionJobs[jobId].status = 'failed';
      console.log(`Marked job ${jobId} as failed due to error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } else {
      console.error(`Cannot mark job as failed: Job ${jobId} not found in global.transcriptionJobs`);
    }

    // Update note with error
    try {
      await supabase
        .from('notes')
        .update({
          content: JSON.stringify({
            type: 'doc',
            content: [{
              type: 'paragraph',
              content: [{ type: 'text', text: 'Transcription failed. Please try again.' }]
            }]
          }),
          metadata: {
            // Get current metadata to preserve original file type
            ...(await supabase.from('notes').select('metadata').eq('id', noteId).single().then((res: any) => (res.data?.metadata as any) || {}).catch(() => ({}))),
            // Only update these fields, preserving the original media_file_type
            transcription_status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error'
          }
        })
        .eq('id', noteId);
    } catch (updateError) {
      console.error('Failed to update note with error:', updateError);
    }
  }
}

// Helper function to format timestamps as MM:SS
function formatTimestamp(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
