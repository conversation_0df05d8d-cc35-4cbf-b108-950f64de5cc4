import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

export async function POST(request: Request) {
  try {
    const { content, refactorType = 'format' } = await request.json();

    // Parse the content from JSON string to object
    const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;

    // Extract text content from the Tiptap JSON structure
    const extractedText = extractTextFromTiptapJSON(parsedContent);

    // Generate the refactored content using Gemini
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });

    // Select the appropriate prompt based on refactorType
    const prompt = getPromptForType(refactorType, extractedText);

    const result = await model.generateContent(prompt);
    const refactoredText = result.response.text();

    // Convert the markdown response to TipTap JSON
    const refactoredContent = convertMarkdownToTiptapJSON(refactoredText);

    return NextResponse.json({
      refactoredContent: refactoredContent // Return the object directly, not as a string
    });
  } catch (error) {
    console.error('Error refactoring note:', error);
    return NextResponse.json(
      { error: 'Failed to refactor note' },
      { status: 500 }
    );
  }
}

// Helper function to extract text from Tiptap JSON
function extractTextFromTiptapJSON(json: any): string {
  let text = '';

  if (!json || !json.content) return text;

  const extractFromNode = (node: any) => {
    if (node.text) {
      text += node.text + ' ';
    }

    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(extractFromNode);
    }

    // Add newlines for block elements
    if (['heading', 'paragraph', 'bulletList', 'orderedList'].includes(node.type)) {
      text += '\n';
    }
  };

  json.content.forEach(extractFromNode);
  return text;
}

// Add this helper function to properly convert markdown to TipTap JSON
const convertMarkdownToTiptapJSON = (markdownText: string) => {
  // Split the text into paragraphs - ensure we properly handle multiple newlines
  const paragraphs = markdownText.split(/\n\n+/);

  // Create the document structure
  const content = paragraphs.map(paragraph => {
    // Skip empty paragraphs
    if (!paragraph.trim()) return null;

    // Handle headings
    if (paragraph.startsWith('# ')) {
      return {
        type: 'heading',
        attrs: { level: 1 },
        content: [{ type: 'text', text: paragraph.substring(2).trim() }]
      };
    } else if (paragraph.startsWith('## ')) {
      return {
        type: 'heading',
        attrs: { level: 2 },
        content: [{ type: 'text', text: paragraph.substring(3).trim() }]
      };
    } else if (paragraph.startsWith('### ')) {
      return {
        type: 'heading',
        attrs: { level: 3 },
        content: [{ type: 'text', text: paragraph.substring(4).trim() }]
      };
    }
    // Handle bullet lists - check if paragraph contains multiple bullet points
    else if (/^[*-] /.test(paragraph)) {
      // Split into individual list items, preserving newlines within items
      const listItems = paragraph.split(/\n(?=[*-] )/);
      return {
        type: 'bulletList',
        content: listItems.map(item => ({
          type: 'listItem',
          content: [{
            type: 'paragraph',
            content: processInlineMarkdown(item.replace(/^[*-] /, '').trim())
          }]
        }))
      };
    }
    // Handle regular paragraphs with inline formatting
    else {
      return {
        type: 'paragraph',
        content: processInlineMarkdown(paragraph.trim())
      };
    }
  }).filter(Boolean); // Remove null items (empty paragraphs)

  return { type: 'doc', content };
};

// Process inline markdown like **bold** and *italic*
const processInlineMarkdown = (text: string) => {
  const content = [];

  // Process text for bold, italic, etc.
  let remainingText = text;
  let boldMatch;
  let italicMatch;

  // Extract bold text
  const boldRegex = /\*\*(.*?)\*\*/g;
  let lastIndex = 0;

  while ((boldMatch = boldRegex.exec(text)) !== null) {
    // Add text before the bold part
    if (boldMatch.index > lastIndex) {
      content.push({
        type: 'text',
        text: text.substring(lastIndex, boldMatch.index)
      });
    }

    // Add the bold text
    content.push({
      type: 'text',
      marks: [{ type: 'bold' }],
      text: boldMatch[1]
    });

    lastIndex = boldMatch.index + boldMatch[0].length;
  }

  // Add any remaining text
  if (lastIndex < text.length) {
    content.push({ type: 'text', text: text.substring(lastIndex) });
  }

  return content.length > 0 ? content : [{ type: 'text', text }];
};

// Helper function to get the appropriate prompt based on refactor type
function getPromptForType(type: string, text: string): string {
  switch (type) {
    case 'format':
      return `
      Please refactor the following notes to make them more organized, clear, and professional.
      Maintain ALL of the information but improve formatting, clarity, and structure.

      IMPORTANT: Ensure proper paragraph separation with blank lines between paragraphs and sections.
      Use markdown formatting with double newlines between paragraphs and sections.

      Return the result in a well-structured format with proper headings, bullet points, and paragraphs as needed.
      Fix any spelling or grammatical errors.

      Make sure to create a stand alone "Action Items" section at the end of the note.

      NOTES TO REFACTOR:
      ${text}
      `;
    case 'transcription-summary':
      return `
      This is a meeting transcription. Please analyze it and create a well-structured summary with the following sections:

      1. Meeting Overview - A brief introduction about what the meeting was about
      2. Key Discussion Points - The main topics that were discussed
      3. Important Decisions - Any decisions that were made during the meeting
      4. Action Items - A clear list of tasks that need to be done, who is responsible (if mentioned), and any deadlines

      IMPORTANT: Ensure proper paragraph separation with blank lines between paragraphs and sections.
      Use markdown formatting with double newlines between paragraphs and sections.
      Use headings (# for main headings, ## for subheadings) to clearly separate sections.

      Make sure to create a stand alone "Action Items" section at the end with a bullet list of all tasks or to-dos mentioned in the transcription.
      Format each action item as a clear, actionable task.

      MEETING TRANSCRIPTION:
      ${text}
      `;
    case 'summarize':
      return `
      Please create a clear, concise summary of the following notes.
      Capture the most important information and main ideas while reducing the length significantly.

      IMPORTANT: Ensure proper paragraph separation with blank lines between paragraphs and sections.
      Use markdown formatting with double newlines between paragraphs and sections.

      Organize the summary with appropriate headings and structure.

      Make sure to create a stand alone "Action Items" section at the end that captures all tasks or to-dos mentioned in the original notes.

      NOTES TO SUMMARIZE:
      ${text}
      `;
    case 'keypoints':
      return `
      Please extract and list the key points from the following notes.
      Create a bullet-point list of the most important ideas, concepts, and information.  You MUST create this using Bullet point format and ONLY produce the key points with very short, sharp and concise sentences. 

      IMPORTANT: Ensure proper paragraph separation with blank lines between paragraphs and sections.
      Use markdown formatting with double newlines between paragraphs and sections.

      Group related points under appropriate headings.

      Make sure to create a stand alone "Action Items" section at the end that captures all tasks or to-dos mentioned in the original notes.

      NOTES TO EXTRACT KEY POINTS FROM:
      ${text}
      `;
    case 'brief-summary':
      return `
      Please create a very concise summary of the following notes in 100 words or less.
      Capture only the most essential information and main ideas.

      The summary should be a single paragraph with no formatting or bullet points.

      NOTES TO SUMMARIZE:
      ${text}
      `;
    default:
      return `
      Please refactor the following notes to make them more organized, clear, and professional.
      Maintain ALL of the information but improve formatting, clarity, and structure.

      IMPORTANT: Ensure proper paragraph separation with blank lines between paragraphs and sections.
      Use markdown formatting with double newlines between paragraphs and sections.

      Return the result in a well-structured format with proper headings, bullet points, and paragraphs as needed.

      Make sure to create a stand alone "Action Items" section at the end of the note.

      NOTES TO REFACTOR:
      ${text}
      `;
  }
}
