import { google } from '@/lib/ai';
import { generateObject } from 'ai';
import { z } from 'zod';
import { NextRequest, NextResponse } from 'next/server';
import { validateAIGeneratedSQL, validateAIPrompt } from '@/utils/sql-injection-prevention';
import { withInputValidation } from '@/utils/api-input-validation';

// Define available tables and their columns
const tableDefinitions = {
  households: {
    name: 'households',
    description: 'Client household information',
    columns: [
      'id',
      'created_at',
      'householdName',
      'members',
      'user_id',
      'address',
      'phone',
      'email',
      'occupation',
      'employer',
      'marital_status',
      'date_of_birth',
      'tax_file_number',
      'notes',
      'street',
      'city',
      'state',
      'zip_code',
      'country',
      'property_type',
      'preferred_contact',
      'best_time_to_call',
      'alternative_contact',
      'investment_strategy',
      'risk_tolerance',
      'primary_advisor',
      'last_review',
      'next_review',
      'additional_info',
      'org_id',
      'updated_at'
    ]
  },
  assets: {
    name: 'assets',
    description: 'Client assets information',
    columns: [
      'id',
      'household_id',
      'name',
      'type',
      'value',
      'details',
      'created_at',
      'updated_at',
      'property_type',
      'rental_income',
      'provider',
      'linked_income_id'
    ]
  },
  expenses: {
    name: 'expenses',
    description: 'Client expenses information',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'frequency',
      'category',
      'details',
      'created_at',
      'updated_at',
      'linked_liability_id'
    ]
  },
  goals: {
    name: 'goals',
    description: 'Client financial goals',
    columns: [
      'id',
      'created_at',
      'household_id',
      'title',
      'type',
      'details',
      'start_date',
      'achieved_date',
      'status',
      'member',
      'target_amount',
      'priority',
      'user_id',
      'org_id'
    ]
  },
  income: {
    name: 'income',
    description: 'Client income sources',
    columns: [
      'id',
      'household_id',
      'source',
      'amount',
      'frequency',
      'details',
      'created_at',
      'updated_at',
      'income_type',
      'linked_asset_id',
      'member_id'
    ]
  },
  insurances: {
    name: 'insurances',
    description: 'Client insurance policies',
    columns: [
      'id',
      'household_id',
      'type',
      'provider',
      'policy_number',
      'premium',
      'frequency',
      'coverage_amount',
      'renewal_date',
      'details',
      'created_at',
      'updated_at',
      'policy_owner',
      'person_insured'
    ]
  },
  interactions: {
    name: 'interactions',
    description: 'Client interactions and communications',
    columns: [
      'id',
      'household_id',
      'title',
      'content',
      'date',
      'type',
      'created_at',
      'file_url'
    ]
  },
  liabilities: {
    name: 'liabilities',
    description: 'Client debts and liabilities',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'interest_rate',
      'lender',
      'payment_amount',
      'payment_frequency',
      'details',
      'created_at',
      'updated_at',
      'linked_asset_id',
      'loan_type',
      'type',
      'linked_expense_id'
    ]
  },
  recommendations: {
    name: 'recommendations',
    description: 'Financial recommendations for clients',
    columns: [
      'id',
      'household_id',
      'title',
      'type',
      'details',
      'created_date',
      'implementation_date',
      'status',
      'member',
      'financial_impact',
      'priority',
      'adviser_notes',
      'user_id',
      'org_id',
      'created_at',
      'updated_at'
    ]
  },
  tasks: {
    name: 'tasks',
    description: 'Tasks related to clients',
    columns: [
      'id',
      'title',
      'content',
      'due_date',
      'household_id',
      'importance',
      'created_at',
      'updated_at',
      'user_id',
      'status',
      'org_id',
      'assigned_to',
      'metadata'
    ]
  }
};

// Input validation schema
const requestSchema = {
  prompt: {
    type: 'string' as const,
    required: true,
    minLength: 1,
    maxLength: 1000,
    sanitize: true
  }
};

export const POST = withInputValidation(
  async (_req: NextRequest, _context: any, validatedData: any) => {
    try {
      const { prompt } = validatedData.body;

      // Additional AI prompt validation
      const promptValidation = validateAIPrompt(prompt);
      if (!promptValidation.isValid) {
        return NextResponse.json(
          {
            error: 'Invalid prompt',
            details: promptValidation.errors
          },
          { status: 400 }
        );
      }

      const result = await generateObject({
      model: google('gemini-2.0-flash-exp'),
      schema: z.object({
        reportConfig: z.object({
          title: z.string().describe('The title of the report'),
          description: z.string().describe('A clear, concise description of what the report shows in natural language, without technical details or SQL information'),
          tableDefinition: z.string().describe('A technical description of the report including SQL details, for internal use only'),
          type: z.enum(['client', 'organization', 'financial', 'custom']).describe('The type of report'),
          dataSource: z.enum(['households', 'assets', 'expenses', 'goals', 'income', 'insurances', 'interactions', 'liabilities', 'recommendations', 'tasks']).describe('The primary data source for the report'),
          joinTables: z.array(z.object({
            table: z.string().describe('The table to join with'),
            joinField: z.string().describe('The field in the primary table to join on'),
            foreignField: z.string().describe('The field in the joined table to join on'),
            joinType: z.enum(['inner', 'left', 'right']).describe('The type of join to use'),
          })).optional().describe('Tables to join with the primary data source'),
          filters: z.array(z.object({
            field: z.string().describe('The field to filter on (must be a valid column from the selected table or joined tables)'),
            operator: z.enum(['equals', 'notEquals', 'greaterThan', 'lessThan', 'contains']).describe('The filter operator'),
            value: z.string().describe('The value to filter by'),
            table: z.string().optional().describe('The table this field belongs to (if not the primary table)'),
          })).describe('Filters to apply to the data'),
          columns: z.array(z.object({
            name: z.string().describe('The name of the column to include'),
            table: z.string().describe('The table this column belongs to'),
            alias: z.string().optional().describe('An optional alias for the column'),
            aggregate: z.enum(['sum', 'avg', 'min', 'max', 'count']).optional().describe('Aggregate function to apply to the column'),
          })).describe('Columns to include in the report'),
          sortBy: z.object({
            field: z.string().describe('Field to sort the report by'),
            table: z.string().optional().describe('The table this field belongs to'),
            aggregate: z.enum(['sum', 'avg', 'min', 'max', 'count']).optional().describe('Aggregate function to apply to the sort field'),
          }).optional().describe('Field to sort the report by'),
          sortDirection: z.enum(['asc', 'desc']).optional().describe('Sort direction'),
          chartType: z.enum(['bar', 'line', 'pie', 'area', 'card']).optional().describe('Type of chart to display'),
          groupBy: z.array(z.object({
            field: z.string().describe('Field to group the data by'),
            table: z.string().optional().describe('The table this field belongs to'),
          })).optional().describe('Fields to group the data by'),
          limit: z.number().optional().describe('Maximum number of results to return'),
          sqlQuery: z.string().optional().describe('The SQL query that would generate this report (for reference only)'),
        }),
        explanation: z.string().describe('An explanation of how the AI interpreted the user\'s request'),
      }),
      prompt: `You are an expert financial reporting assistant and SQL expert. Create a report configuration based on this description: "${promptValidation.sanitized}".

IMPORTANT INSTRUCTIONS FOR DESCRIPTIONS:
1. For the "description" field, write a clear, concise explanation of what the report shows in natural language that a non-technical user would understand. This should NOT include any SQL or technical details.
2. For the "tableDefinition" field, include the technical details including the SQL query and any other technical information.

IMPORTANT: You must only use the following tables and their respective columns:

${Object.keys(tableDefinitions).map(tableName => {
  const table = tableDefinitions[tableName as keyof typeof tableDefinitions];
  return `Table: ${table.name} (${table.description})
Available columns: ${table.columns.join(', ')}`;
}).join('\n\n')}

Table Relationships:
- households is the primary table containing client information
- income has a foreign key 'household_id' that references households.id
- assets has a foreign key 'household_id' that references households.id
- expenses has a foreign key 'household_id' that references households.id
- liabilities has a foreign key 'household_id' that references households.id
- insurances has a foreign key 'household_id' that references households.id
- goals has a foreign key 'household_id' that references households.id
- tasks has a foreign key 'household_id' that references households.id
- interactions has a foreign key 'household_id' that references households.id
- recommendations has a foreign key 'household_id' that references households.id
- income can also have a foreign key 'linked_asset_id' that references assets.id
- assets can have a foreign key 'linked_income_id' that references income.id
- expenses can have a foreign key 'linked_liability_id' that references liabilities.id
- liabilities can have a foreign key 'linked_asset_id' that references assets.id and 'linked_expense_id' that references expenses.id

Your task:
1. Determine which table should be the primary data source based on the user's request
2. Identify if any joins are needed to fulfill the request (e.g., joining households with income to show household income)
3. For each join, specify:
   - The table to join with
   - The join field in the primary table
   - The foreign field in the joined table
   - The type of join (inner, left, right)
4. Specify the columns to include in the report, including:
   - The name of the column
   - The table it belongs to
   - An optional alias for clarity
   - Any aggregate functions needed (sum, avg, min, max, count)
5. If grouping is needed, specify which fields to group by
6. If sorting is needed, specify which field to sort by and the direction
7. Include appropriate filters based on the user's request
8. Specify a limit if the user requests a specific number of results
9. Include a sample SQL query that would generate this report (for reference only)
10. Provide a clear explanation of how you interpreted the request

IMPORTANT GUIDELINES:
- PostgreSQL is case-sensitive for quoted identifiers
- Pay special attention to column names with mixed case like "householdName" (with capital N)
- When aggregating data (using SUM, AVG, etc.), make sure to include all non-aggregated columns in the GROUP BY clause
- For "top N" queries, use appropriate aggregation and ORDER BY with LIMIT
- When joining tables, use the correct join type (INNER JOIN for required relationships, LEFT JOIN for optional ones)
- Always qualify column names with their table names to avoid ambiguity (e.g., h.id, i.amount)
- When using table aliases (like 'h' for households), use proper case for column names: h."householdName" (not h.householdName)
- For complex calculations, use appropriate SQL functions and aliases
- If the request involves totals or averages across households, use SUM or AVG with GROUP BY
- Always include the primary key of the main table in the result set for proper identification
- Double-check your SQL query for proper case sensitivity in column names
- IMPORTANT: The 'total_assets' column has been removed from the households table

Example: For "list the top 5 households by total income", you would:
1. Set dataSource to "income"
2. Join with households table
3. Include columns: h."householdName", SUM(i.amount) as totalIncome
4. Group by h.id, h."householdName"
5. Sort by totalIncome DESC
6. Set limit to 5
7. Sample SQL: SELECT h.id, h."householdName", SUM(i.amount) as totalIncome FROM income i INNER JOIN households h ON i.household_id = h.id GROUP BY h.id, h."householdName" ORDER BY totalIncome DESC LIMIT 5

Example: For "show all clients with their city and total income":
1. Set dataSource to "households"
2. Join with income table
3. Include columns: h."householdName", h.city, SUM(i.amount) as totalIncome
4. Group by h.id, h."householdName", h.city
5. Sort by totalIncome DESC
6. Sample SQL: SELECT h."householdName", h.city, SUM(i.amount) as totalIncome FROM households h INNER JOIN income i ON h.id = i.household_id GROUP BY h.id, h."householdName", h.city ORDER BY totalIncome DESC`,
      });

      // Validate any generated SQL query
      if (result.object.reportConfig.sqlQuery) {
        const sqlValidation = validateAIGeneratedSQL(result.object.reportConfig.sqlQuery);
        if (!sqlValidation.isValid) {
          return NextResponse.json(
            {
              error: 'Generated SQL query failed security validation',
              details: sqlValidation.errors,
              threats: sqlValidation.detectedThreats
            },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(result.object);
    } catch (error) {
      return NextResponse.json(
        { error: 'Failed to generate report configuration' },
        { status: 500 }
      );
    }
  },
  { bodySchema: requestSchema }
);
