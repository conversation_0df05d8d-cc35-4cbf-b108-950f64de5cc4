import { createClient } from "@/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const token = searchParams.get('token');

  if (!token) {
    return NextResponse.json({ valid: false, error: "Token is required" }, { status: 400 });
  }

  console.log(`Validating invitation token: ${token}`);

  const supabase = createClient();

  try {
    // Query the invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('organization_invitations')
      .select('*, organization:organization_id(id, name)')
      .eq('token', token)
      .eq('status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .single();

    if (invitationError) {
      console.error('Error querying invitation:', invitationError);
      return NextResponse.json({
        valid: false,
        error: "Invalid or expired invitation"
      }, { status: 400 });
    }

    if (!invitation) {
      console.error('No invitation found for token:', token);
      return NextResponse.json({
        valid: false,
        error: "Invalid or expired invitation"
      }, { status: 400 });
    }

    console.log('Invitation found:', {
      id: invitation.id,
      email: invitation.email,
      organization: invitation.organization?.name
    });

    return NextResponse.json({
      valid: true,
      organization: invitation.organization,
      email: invitation.email,
      role: invitation.role || 'readonly'
    });
  } catch (error) {
    console.error('Unexpected error validating invitation:', error);
    return NextResponse.json({
      valid: false,
      error: "Error validating invitation"
    }, { status: 500 });
  }
}
