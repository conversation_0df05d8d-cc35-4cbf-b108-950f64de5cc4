import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get request body
    const { logoPath } = await request.json();

    try {
      // Update profile in the profiles table
      const { error } = await supabase
        .from('profiles')
        .update({ logo_path: logoPath })
        .eq('user_id', user.id);

      if (error) {
        // Check if the error is related to missing column
        if (error.message.includes('column "logo_path" of relation "profiles" does not exist')) {
          console.warn('logo_path column does not exist in profiles table');
          
          // Return success anyway since we'll handle this gracefully on the client
          return NextResponse.json({ success: true, message: 'Logo path stored in local state only' });
        }
        
        throw error;
      }

      return NextResponse.json({ success: true, message: 'Logo updated successfully' });
    } catch (error: any) {
      console.error('Error updating logo:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to update logo' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
