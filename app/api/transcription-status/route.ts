import { NextResponse } from 'next/server';

// Mark this route as dynamic to allow using request.url
export const dynamic = 'force-dynamic';

// This would typically be stored in a database
// We're referencing the same object from the transcribe route
// In a real implementation, this would be stored in a database
declare global {
  var transcriptionJobs: Record<string, {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    noteId: string;
    filePath: string;
    fileType: string;
    isVideo?: boolean;
  }>;
}

// Initialize global variable if it doesn't exist
if (!global.transcriptionJobs) {
  global.transcriptionJobs = {};
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { error: 'Missing job ID' },
        { status: 400 }
      );
    }

    const job = global.transcriptionJobs[jobId];

    if (!job) {
      console.error(`Job not found: ${jobId}`);
      return NextResponse.json(
        { error: 'Job not found', jobId },
        { status: 404 }
      );
    }

    console.log(`Transcription status for job ${jobId}: ${job.status}, progress: ${job.progress}%`);

    return NextResponse.json({
      status: job.status,
      progress: job.progress,
      noteId: job.noteId
    });

  } catch (error) {
    console.error('Error checking transcription status:', error);
    return NextResponse.json(
      { error: 'Failed to check transcription status' },
      { status: 500 }
    );
  }
}
