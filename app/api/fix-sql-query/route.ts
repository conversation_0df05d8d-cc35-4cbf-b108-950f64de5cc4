import { google } from '@/lib/ai';
import { generateText } from 'ai';
import { NextResponse } from 'next/server';

// Define available tables and their columns
const tableDefinitions = {
  households: {
    name: 'households',
    description: 'Client household information',
    columns: [
      'id',
      'created_at',
      'householdName',
      'members',
      'user_id',
      'address',
      'phone',
      'email',
      'occupation',
      'employer',
      'marital_status',
      'date_of_birth',
      'tax_file_number',
      'notes',
      'street',
      'city',
      'state',
      'zip_code',
      'country',
      'property_type',
      'preferred_contact',
      'best_time_to_call',
      'alternative_contact',
      'investment_strategy',
      'risk_tolerance',
      'primary_advisor',
      'last_review',
      'next_review',
      'additional_info',
      'org_id',
      'updated_at'
    ]
  },
  assets: {
    name: 'assets',
    description: 'Client assets information',
    columns: [
      'id',
      'household_id',
      'name',
      'type',
      'value',
      'details',
      'created_at',
      'updated_at',
      'property_type',
      'rental_income',
      'provider',
      'linked_income_id'
    ]
  },
  expenses: {
    name: 'expenses',
    description: 'Client expenses information',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'frequency',
      'category',
      'details',
      'created_at',
      'updated_at',
      'linked_liability_id'
    ]
  },
  income: {
    name: 'income',
    description: 'Client income sources',
    columns: [
      'id',
      'household_id',
      'source',
      'amount',
      'frequency',
      'details',
      'created_at',
      'updated_at',
      'income_type',
      'linked_asset_id',
      'member_id'
    ]
  },
  liabilities: {
    name: 'liabilities',
    description: 'Client debts and liabilities',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'interest_rate',
      'lender',
      'payment_amount',
      'payment_frequency',
      'details',
      'created_at',
      'updated_at',
      'linked_asset_id',
      'loan_type',
      'type',
      'linked_expense_id'
    ]
  }
};

export async function POST(req: Request) {
  try {
    const { query, error } = await req.json();

    if (!query || !error) {
      return NextResponse.json(
        { error: 'Query and error are required' },
        { status: 400 }
      );
    }

    // Format table definitions for the prompt
    const tableDefinitionsText = Object.keys(tableDefinitions).map(tableName => {
      const table = tableDefinitions[tableName as keyof typeof tableDefinitions];
      return `Table: ${table.name} (${table.description})
Available columns: ${table.columns.join(', ')}`;
    }).join('\n\n');

    // Use Google AI to fix the SQL query
    const prompt = `
This is the SQL Query You need to Fix:
\`\`\`sql
${query}
\`\`\`

It has returned this error:
\`\`\`
${error}
\`\`\`

Here are the Table definitions that you must reference:
${tableDefinitionsText}

Your task is to understand what the SQL code is trying to Query and then understand why the error is occurring. You must then provide an appropriate and effective fix and rewrite the SQL code.

IMPORTANT NOTE! You must not change or alter any of the columns that are trying to be queried, unless it is a spelling error or fetching from an incorrect table. The SQL query must provide the results the original was trying to fetch.

Please provide only the fixed SQL query without any explanations or markdown formatting. The query should be valid PostgreSQL syntax.

Important notes:
1. PostgreSQL is case-sensitive for quoted identifiers
2. The "households" table has a column named "householdName" (with capital N)
3. When joining tables, make sure to use the correct case for column names
4. When using aliases like 'h' for households, use h."householdName" (not h.householdName)
5. When using GROUP BY with aggregate functions, all non-aggregated columns must be included in the GROUP BY clause
6. Make sure all column references in the SELECT list are either aggregated or included in the GROUP BY clause
7. The 'total_assets' column has been removed from the households table
`;

    // Use the generateText function from the AI SDK
    const { text: fixedQuery } = await generateText({
      model: google('gemini-2.0-flash-exp'),
      prompt: prompt
    });

    return NextResponse.json({ fixedQuery });
  } catch (error) {
    console.error('Error fixing SQL query:', error);
    return NextResponse.json(
      { error: 'Failed to fix SQL query' },
      { status: 500 }
    );
  }
}
