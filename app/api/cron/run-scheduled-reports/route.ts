import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { fetchReportData } from '@/app/protected/admin/reporting/lib/data-fetching';
import { calculateNextRunDate } from '@/app/protected/admin/reporting/lib/scheduling';

/**
 * GET /api/cron/run-scheduled-reports
 * Runs all scheduled reports that are due
 * This endpoint should be called by a cron job every minute
 */
export async function GET(request: NextRequest) {
  try {
    // Check for authorization header (in a real implementation, you would use a secret key)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    // In a real implementation, you would validate the token against a secret
    // For now, we'll just check if it's not empty
    if (!token) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const supabase = createClient();
    const now = new Date();

    // Get all schedules that are due to run
    const { data: schedules, error: schedulesError } = await supabase
      .from('report_schedules')
      .select('*, reports(name, type, category)')
      .eq('enabled', true)
      .lte('next_run_at', now.toISOString());

    if (schedulesError) {
      console.error('Error fetching schedules:', schedulesError);
      return NextResponse.json(
        { error: 'Failed to fetch schedules' },
        { status: 500 }
      );
    }

    if (!schedules || schedules.length === 0) {
      return NextResponse.json({ message: 'No schedules due to run' });
    }

    // Process each schedule
    const results = await Promise.allSettled(
      schedules.map(async (schedule) => {
        try {
          // Create an execution record
          const { data: execution, error: executionError } = await supabase
            .from('report_executions')
            .insert({
              schedule_id: schedule.id,
              report_id: String(schedule.report_id), // Ensure report_id is a string
              status: 'running'
            })
            .select('id')
            .single();

          if (executionError) {
            throw new Error(`Error creating execution record: ${executionError.message}`);
          }

          // Run the report
          const reportData = await fetchReportData(
            schedule.reports.type,
            schedule.parameters || {}
          );

          // Save the report result
          const { data: result, error: resultError } = await supabase
            .from('report_results')
            .insert({
              report_id: schedule.report_id,
              parameters: schedule.parameters,
              data: reportData.data,
              summary: reportData.summary,
              chart_data: reportData.chartData,
              user_id: schedule.user_id
            })
            .select('id')
            .single();

          if (resultError) {
            throw new Error(`Error saving report result: ${resultError.message}`);
          }

          // Update the execution record
          const { error: updateError } = await supabase
            .from('report_executions')
            .update({
              status: 'completed',
              completed_at: new Date().toISOString(),
              result_id: result.id,
              delivery_status: {
                method: schedule.delivery.method,
                status: 'sent',
                sentAt: new Date().toISOString()
              }
            })
            .eq('id', execution.id);

          if (updateError) {
            throw new Error(`Error updating execution record: ${updateError.message}`);
          }

          // Calculate the next run date
          const nextRunAt = calculateNextRunDate(
            schedule.frequency,
            schedule.day_of_week || 1,
            schedule.day_of_month || 1,
            schedule.month || 0,
            schedule.time
          );

          // Update the schedule with the last run time and next run time
          const { error: scheduleUpdateError } = await supabase
            .from('report_schedules')
            .update({
              last_run_at: now.toISOString(),
              next_run_at: nextRunAt.toISOString()
            })
            .eq('id', schedule.id);

          if (scheduleUpdateError) {
            throw new Error(`Error updating schedule: ${scheduleUpdateError.message}`);
          }

          return {
            scheduleId: schedule.id,
            reportId: schedule.report_id,
            executionId: execution.id,
            resultId: result.id,
            nextRunAt: nextRunAt.toISOString()
          };
        } catch (error) {
          // If there was an error, update the execution record
          const { data: execution } = await supabase
            .from('report_executions')
            .select('id')
            .eq('schedule_id', schedule.id)
            .order('started_at', { ascending: false })
            .limit(1)
            .single();

          if (execution) {
            await supabase
              .from('report_executions')
              .update({
                status: 'failed',
                completed_at: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'An unknown error occurred'
              })
              .eq('id', execution.id);
          }

          throw error;
        }
      })
    );

    // Count successful and failed runs
    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;

    return NextResponse.json({
      message: `Processed ${schedules.length} schedules`,
      successful,
      failed,
      results: results.map(result => {
        if (result.status === 'fulfilled') {
          return {
            status: 'success',
            data: result.value
          };
        } else {
          return {
            status: 'error',
            error: result.reason instanceof Error ? result.reason.message : 'An unknown error occurred'
          };
        }
      })
    });
  } catch (error) {
    console.error('Error in GET /api/cron/run-scheduled-reports:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
