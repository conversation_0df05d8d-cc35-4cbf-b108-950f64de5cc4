import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { processNaturalLanguageQuery } from '@/app/actions/reporting';
import { generateChartConfig } from '@/app/actions/chart';
import { validateAIPrompt } from '@/utils/sql-injection-prevention';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Validate messages array
    if (!Array.isArray(messages)) {
      return new Response(
        JSON.stringify({ error: 'Messages must be an array' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Validate and sanitize each message
    for (const message of messages) {
      if (message.content && typeof message.content === 'string') {
        const promptValidation = validateAIPrompt(message.content);
        if (!promptValidation.isValid) {
          return new Response(
            JSON.stringify({
              error: 'Invalid message content',
              details: promptValidation.errors
            }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          );
        }
        // Use sanitized content
        message.content = promptValidation.sanitized;
      }
    }

  const result = streamText({
    model: openai('gpt-4o-mini'),
    system: `You are a helpful SQL assistant that helps users query their database.
    You can convert natural language questions into SQL queries, execute them, and visualize the results.

    Always follow these guidelines:
    1. Always use the queryDatabase tool to answer questions about the data.
    2. If the query fails, analyze the error message carefully.
    3. After an error, ALWAYS try again with a modified query that addresses the issue.
    4. If the query returns no results, try alternative approaches (e.g., different column names, different filters).
    5. If the query succeeds, explain the results in a clear and concise way.
    6. For follow-up questions, refer to previous context to understand what the user is asking about.
    7. When generating SQL queries, make sure they are compatible with PostgreSQL.
    8. If a user is looking for a specific person or entity that doesn't exist with the exact spelling, try using ILIKE with wildcards.

    The database contains information about households and their members. The members column is a JSONB array.`,
    messages,
    maxSteps: 4, // Enable multiple tool calls for retries
    toolCallStreaming: true, // Enable streaming of tool calls for real-time UI updates
    tools: {
      queryDatabase: tool({
        description: 'Query the database using natural language and visualize the results',
        parameters: z.object({
          question: z.string().describe('The natural language question to convert to SQL and execute'),
        }),
        execute: async ({ question }) => {
          // Validate the question before processing
          const questionValidation = validateAIPrompt(question);
          if (!questionValidation.isValid) {
            return {
              status: 'error',
              error: 'Invalid question: ' + questionValidation.errors.join(', '),
              query: null,
            };
          }

          // Show thinking process in real-time
          const result = await processNaturalLanguageQuery(questionValidation.sanitized);

          if (result.error) {
            return {
              status: 'error',
              error: result.error,
              query: result.query,
            };
          }

          // Generate chart configuration if query was successful
          let chartConfig = null;
          if (result.data && result.data.length > 0) {
            try {
              chartConfig = await generateChartConfig(result.data, question);
            } catch (error) {
              console.error('Error generating chart config:', error);
              // Continue even if chart generation fails
            }
          }

          return {
            status: 'success',
            data: result.data,
            query: result.query,
            explanation: result.explanation,
            rowCount: result.data?.length || 0,
            columns: result.data && result.data.length > 0 ? Object.keys(result.data[0]) : [],
            chartConfig,
          };
        },
      }),
    },
  });

    return result.toDataStreamResponse({
      // Provide better error messages for debugging
      getErrorMessage: (error) => {
        if (error instanceof Error) return error.message;
        return String(error);
      }
    });
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Failed to process request' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
