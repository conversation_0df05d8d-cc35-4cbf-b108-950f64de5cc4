import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create a Supabase admin client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createSupabaseClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(request: NextRequest) {
  try {
    console.log('Server client using origin:', request.headers.get('origin'));

    // Use the admin client to bypass RLS policies
    const supabase = supabaseAdmin;

    // Check if the media bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      return NextResponse.json(
        { error: listError.message },
        { status: 500 }
      );
    }

    const mediaBucketExists = buckets?.some(bucket => bucket.name === 'media');

    if (!mediaBucketExists) {
      console.log('Media bucket does not exist, creating it now...');

      // Create the media bucket with admin privileges
      const { error: createError } = await supabase.storage.createBucket('media', {
        public: false,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml']
      });

      if (createError) {
        console.error('Error creating media bucket:', createError);
        return NextResponse.json(
          { error: createError.message },
          { status: 500 }
        );
      }

      console.log('Media bucket created successfully');

      // Set up RLS policies for the media bucket
      try {
        console.log('Setting up RLS policies for media bucket...');

        // Use SQL to create policies directly
        // SELECT policy - Allow authenticated users to read any files
        await supabase.rpc('create_storage_policy', {
          bucket_name: 'media',
          policy_name: 'Public Read Access',
          definition: "bucket_id = 'media'",
          operation: 'SELECT'
        });

        // INSERT policy - Allow authenticated users to upload files
        await supabase.rpc('create_storage_policy', {
          bucket_name: 'media',
          policy_name: 'Allow authenticated uploads',
          definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
          operation: 'INSERT'
        });

        // UPDATE policy - Allow authenticated users to update any files
        // This is more permissive to allow organization members to update logos
        await supabase.rpc('create_storage_policy', {
          bucket_name: 'media',
          policy_name: 'Allow authenticated updates',
          definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
          operation: 'UPDATE'
        });

        // DELETE policy - Allow authenticated users to delete any files
        // This is more permissive to allow organization members to delete logos
        await supabase.rpc('create_storage_policy', {
          bucket_name: 'media',
          policy_name: 'Allow authenticated deletes',
          definition: "bucket_id = 'media' AND auth.role() = 'authenticated'",
          operation: 'DELETE'
        });

        console.log('RLS policies set up successfully');
      } catch (policyError) {
        console.warn('Could not set RLS policies automatically:', policyError);
        // Continue anyway, as the bucket was created
      }
    } else {
      console.log('Media bucket already exists');
    }

    return NextResponse.json({ success: true, bucketExists: mediaBucketExists });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
