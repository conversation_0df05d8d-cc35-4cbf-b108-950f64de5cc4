import { NextResponse } from 'next/server';
import { generateText } from 'ai';
import { google } from '@/lib/ai';
import { z } from 'zod';

// Define available tables and their columns
const tableDefinitions = {
  households: {
    name: 'households',
    description: 'Client household information',
    columns: [
      'id',
      'created_at',
      'householdName',
      'members',
      'user_id',
      'address',
      'phone',
      'email',
      'occupation',
      'employer',
      'marital_status',
      'date_of_birth',
      'tax_file_number',
      'notes',
      'street',
      'city',
      'state',
      'zip_code',
      'country',
      'property_type',
      'preferred_contact',
      'best_time_to_call',
      'alternative_contact',
      'investment_strategy',
      'risk_tolerance',
      'primary_advisor',
      'last_review',
      'next_review',
      'additional_info',
      'org_id',
      'updated_at'
    ]
  },
  assets: {
    name: 'assets',
    description: 'Client assets information',
    columns: [
      'id',
      'household_id',
      'name',
      'type',
      'value',
      'details',
      'created_at',
      'updated_at',
      'property_type',
      'rental_income',
      'provider',
      'linked_income_id'
    ]
  },
  expenses: {
    name: 'expenses',
    description: 'Client expenses information',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'frequency',
      'category',
      'details',
      'created_at',
      'updated_at',
      'linked_liability_id'
    ]
  },
  income: {
    name: 'income',
    description: 'Client income sources',
    columns: [
      'id',
      'household_id',
      'source',
      'amount',
      'frequency',
      'details',
      'created_at',
      'updated_at',
      'income_type',
      'linked_asset_id',
      'member_id'
    ]
  },
  liabilities: {
    name: 'liabilities',
    description: 'Client debts and liabilities',
    columns: [
      'id',
      'household_id',
      'name',
      'amount',
      'interest_rate',
      'lender',
      'payment_amount',
      'payment_frequency',
      'details',
      'created_at',
      'updated_at',
      'linked_asset_id',
      'loan_type',
      'type',
      'linked_expense_id'
    ]
  }
};

export async function POST(req: Request) {
  try {
    const { prompt, currentReport } = await req.json();

    if (!prompt || !currentReport) {
      return NextResponse.json(
        { error: 'Prompt and current report are required' },
        { status: 400 }
      );
    }

    // Format table definitions for the prompt
    const tableDefinitionsText = Object.keys(tableDefinitions).map(tableName => {
      const table = tableDefinitions[tableName as keyof typeof tableDefinitions];
      return `Table: ${table.name} (${table.description})
Available columns: ${table.columns.join(', ')}`;
    }).join('\n\n');

    // Define the schema for the updated report
    const reportConfigSchema = z.object({
      title: z.string().describe('The title of the report'),
      description: z.string().describe('A clear, concise description of what the report shows in natural language, without technical details or SQL information'),
      tableDefinition: z.string().describe('A technical description of the report including SQL details, for internal use only'),
      type: z.enum(['client', 'organization', 'financial', 'custom']).describe('The type of report'),
      dataSource: z.enum(['households', 'assets', 'expenses', 'goals', 'income', 'insurances', 'interactions', 'liabilities', 'recommendations', 'tasks']).describe('The primary data source for the report'),
      joinTables: z.array(z.object({
        table: z.string().describe('The table to join'),
        type: z.enum(['INNER', 'LEFT', 'RIGHT', 'FULL']).optional().describe('The type of join'),
        joinField: z.string().describe('The field in the primary table to join on'),
        foreignField: z.string().describe('The field in the joined table to join on')
      })).optional().describe('Tables to join with the primary data source'),
      filters: z.array(z.object({
        field: z.string().describe('The field to filter on'),
        operator: z.enum(['=', '!=', '>', '<', '>=', '<=', 'LIKE', 'IN', 'NOT IN', 'IS NULL', 'IS NOT NULL']).describe('The filter operator'),
        value: z.any().describe('The filter value')
      })).describe('Filters to apply to the data'),
      columns: z.array(z.union([
        z.string(),
        z.object({
          name: z.string().describe('The column name in the database'),
          displayName: z.string().optional().describe('The display name for the column'),
          dataType: z.enum(['string', 'number', 'date', 'boolean']).optional().describe('The data type of the column'),
          format: z.enum(['none', 'currency', 'percent', 'date', 'datetime', 'integer']).optional().describe('The format to apply to the column')
        })
      ])).describe('Columns to include in the report'),
      sortBy: z.union([
        z.string(),
        z.object({
          field: z.string().describe('The field to sort by'),
          direction: z.enum(['asc', 'desc']).describe('The sort direction')
        })
      ]).optional().describe('Field to sort the data by'),
      sortDirection: z.enum(['asc', 'desc']).optional().describe('The sort direction'),
      chartType: z.enum(['bar', 'line', 'pie', 'area', 'card']).optional().describe('The type of chart to use for visualization'),
      groupBy: z.union([
        z.string(),
        z.array(z.object({
          field: z.string().describe('The field to group by'),
          function: z.enum(['COUNT', 'SUM', 'AVG', 'MIN', 'MAX']).optional().describe('The aggregate function to apply')
        }))
      ]).optional().describe('Fields to group the data by'),
      limit: z.number().optional().describe('Maximum number of rows to return'),
      sqlQuery: z.string().optional().describe('Custom SQL query to use instead of building one')
    });

    // Use Google AI to generate the updated report configuration
    const { text: aiResponse } = await generateText({
      model: google('gemini-2.0-flash'),
      prompt: `You are an expert financial reporting assistant and SQL expert. Update the existing report configuration based on this request: "${prompt}".

CURRENT REPORT CONFIGURATION:
\`\`\`json
${JSON.stringify(currentReport, null, 2)}
\`\`\`

IMPORTANT INSTRUCTIONS FOR DESCRIPTIONS:
1. For the "description" field, write a clear, concise explanation of what the report shows in natural language that a non-technical user would understand. This should NOT include any SQL or technical details.
2. For the "tableDefinition" field, include the technical details including the SQL query and any other technical information.

IMPORTANT: You must only use the following tables and their respective columns:
${tableDefinitionsText}

IMPORTANT GUIDELINES:
- PostgreSQL is case-sensitive for quoted identifiers
- Pay special attention to column names with mixed case like "householdName" (with capital N)
- When aggregating data (using SUM, AVG, etc.), make sure to include all non-aggregated columns in the GROUP BY clause
- For "top N" queries, use appropriate aggregation and ORDER BY with LIMIT
- When joining tables, use the correct join type (INNER JOIN for required relationships, LEFT JOIN for optional ones)
- Always qualify column names with their table names to avoid ambiguity (e.g., h.id, i.amount)
- When using table aliases (like 'h' for households), use proper case for column names: h."householdName" (not h.householdName)
- For complex calculations, use appropriate SQL functions and aliases
- If the request involves totals or averages across households, use SUM or AVG with GROUP BY
- Always include the primary key of the main table in the result set for proper identification
- Double-check your SQL query for proper case sensitivity in column names
- IMPORTANT: The 'total_assets' column has been removed from the households table

Please provide a complete updated report configuration that includes all necessary fields. Ensure that the SQL query is valid PostgreSQL syntax.

Your response should be in JSON format with two main objects:
1. "updatedReport" - The complete updated report configuration
2. "explanation" - A brief explanation of the changes you made to the report
`
    });

    // Parse the AI response
    try {
      // Extract the JSON part from the response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in the response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);

      // Ensure we have the required fields from the current report as fallbacks
      const updatedReportWithDefaults = {
        // Use current report values as defaults
        title: currentReport.name || 'Updated Report',
        description: currentReport.description || 'Updated report description',
        tableDefinition: currentReport.tableDefinition || 'AI-Generated Report',
        type: currentReport.type || 'custom',
        dataSource: currentReport.dataSource || 'households',
        columns: currentReport.fields?.map((f: any) => f.name) || ['id'],
        filters: currentReport.filters || [],

        // Now merge with the AI-generated updates
        ...parsedResponse.updatedReport,
      };

      // Handle special cases for sortBy and groupBy that might cause validation issues
      if (updatedReportWithDefaults.sortBy && Array.isArray(updatedReportWithDefaults.sortBy)) {
        // Convert array to object format if it's an array
        if (updatedReportWithDefaults.sortBy.length > 0) {
          updatedReportWithDefaults.sortBy = {
            field: typeof updatedReportWithDefaults.sortBy[0] === 'string'
              ? updatedReportWithDefaults.sortBy[0]
              : updatedReportWithDefaults.sortBy[0].field || 'id',
            direction: 'asc'
          };
        } else {
          // If empty array, set to undefined
          updatedReportWithDefaults.sortBy = undefined;
        }
      }

      // Handle groupBy if it's an array of strings (convert to proper format)
      if (updatedReportWithDefaults.groupBy && Array.isArray(updatedReportWithDefaults.groupBy)) {
        const firstItem = updatedReportWithDefaults.groupBy[0];
        if (typeof firstItem === 'string') {
          // If it's an array of strings, use the first one
          updatedReportWithDefaults.groupBy = firstItem;
        } else if (updatedReportWithDefaults.groupBy.length === 0) {
          // If empty array, set to undefined
          updatedReportWithDefaults.groupBy = undefined;
        }
      }

      console.log('Prepared report for validation:', JSON.stringify(updatedReportWithDefaults, null, 2));

      // Validate the updated report against the schema
      const validatedReport = reportConfigSchema.parse(updatedReportWithDefaults);

      return NextResponse.json({
        updatedReport: validatedReport,
        explanation: parsedResponse.explanation || 'Report has been updated based on your request.'
      });
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);

      // Return a more user-friendly error with the current report as fallback
      return NextResponse.json({
        updatedReport: {
          title: currentReport.name,
          description: currentReport.description || 'Report description',
          tableDefinition: currentReport.tableDefinition || 'AI-Generated Report',
          type: currentReport.type || 'custom',
          dataSource: currentReport.dataSource,
          columns: currentReport.fields?.map((f: any) => f.name) || ['id'],
          filters: currentReport.filters || [],
          sqlQuery: currentReport.sqlQuery || ''
        },
        explanation: 'There was an error updating the report. The original report has been preserved.'
      });
    }
  } catch (error) {
    console.error('Error in AI report edit:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
