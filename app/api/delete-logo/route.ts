import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create a Supabase admin client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createSupabaseClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(request: NextRequest) {
  try {
    // Use regular client for auth
    const supabase = createClient();

    // Get user data
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the logo path from the request body
    const { logoPath } = await request.json();

    if (!logoPath) {
      return NextResponse.json(
        { error: 'No logo path provided' },
        { status: 400 }
      );
    }

    // Verify that the logo belongs to the user (basic security check)
    // This is a simple check that can be bypassed, but it's better than nothing
    // The real security is handled by RLS policies
    if (logoPath.includes('/') && !logoPath.includes(`/${user.id}/`) && !logoPath.includes(`${user.id}/`)) {
      return NextResponse.json(
        { error: 'Unauthorized to delete this logo' },
        { status: 403 }
      );
    }

    // Delete the file from storage using admin client
    console.log(`Attempting to delete logo from path: ${logoPath}`);
    const { error: deleteError } = await supabaseAdmin.storage
      .from('media')
      .remove([logoPath]);

    if (deleteError) {
      console.warn('Error deleting logo from storage:', deleteError);
      // Continue anyway to update the profile
    } else {
      console.log('Logo deleted successfully from storage');
    }

    // Get the user's profile to check if it's for an organization
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      console.warn('Error fetching profile:', profileError);
      // Continue anyway to try updating
    }

    try {
      if (profile?.org_id) {
        // If user has an organization, update the organization's logo_path
        console.log(`Updating organization ${profile.org_id} to remove logo path`);

        // Use admin client to update organization
        const { error: updateOrgError } = await supabaseAdmin
          .from('organizations')
          .update({ logo_path: null })
          .eq('id', profile.org_id);

        if (updateOrgError) {
          console.warn('Error updating organization to remove logo path:', updateOrgError);
        } else {
          console.log('Organization logo path removed successfully');
        }
      } else {
        // If no organization, update the user's profile
        console.log('Updating user profile to remove logo path');

        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({ logo_path: null })
          .eq('user_id', user.id);

        if (updateError && !updateError.message.includes('column "logo_path" of relation "profiles" does not exist')) {
          console.warn('Error updating profile to remove logo path:', updateError);
        } else {
          console.log('Profile logo path removed successfully');
        }
      }
    } catch (updateErr) {
      console.error('Error updating profile or organization:', updateErr);
      // Continue anyway, as we attempted to delete the file
    }

    return NextResponse.json({
      success: true,
      message: 'Logo deleted successfully'
    });

  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
