import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, client_summary, response, income_expenses, risk_profile')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }

    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }

    if (!tokenData.client_summary) {
      return NextResponse.json({ error: 'No client summary data available' }, { status: 400 });
    }

    // Get current household data to merge with
    const { data: currentHousehold, error: householdError } = await supabase
      .from('households')
      .select('members')
      .eq('id', tokenData.household_id)
      .single();

    if (householdError) {
      return NextResponse.json({ error: 'Failed to fetch current household data' }, { status: 500 });
    }

    const clientSummary = tokenData.client_summary;
    const fullResponse = tokenData.response || {};

    // Check if we have the enhanced member mappings
    const mainMember = fullResponse._mainMember || {};
    const partnerMember = fullResponse._partnerMember || {};

    // Extract household fields
    const householdFields: Record<string, any> = {};

    // Map household name
    if (clientSummary.household_name) {
      householdFields.householdName = clientSummary.household_name;
    } else if (clientSummary.client_name) {
      householdFields.householdName = `${clientSummary.client_name}'s Household`;
    }

    // Map address fields - these are household level
    if (clientSummary.address) householdFields.address = clientSummary.address;
    if (clientSummary.postal_address) householdFields.notes = `Postal Address: ${clientSummary.postal_address}`;
    if (clientSummary.street) householdFields.street = clientSummary.street;
    if (clientSummary.city) householdFields.city = clientSummary.city;
    if (clientSummary.state) householdFields.state = clientSummary.state;
    if (clientSummary.zip_code) householdFields.zip_code = clientSummary.zip_code;
    if (clientSummary.country) householdFields.country = clientSummary.country;

    // Map household level fields
    if (clientSummary.marital_status) householdFields.marital_status = clientSummary.marital_status;
    if (clientSummary.property_type) householdFields.property_type = clientSummary.property_type;
    if (clientSummary.investment_experience) householdFields.investment_strategy = clientSummary.investment_experience;
    if (clientSummary.risk_profile) householdFields.risk_tolerance = clientSummary.risk_profile;

    // Extract member fields
    const members = { ...(currentHousehold?.members || {}) };

    // Map main client (member1) data
    // First name and last name
    if (clientSummary.client_name) members.name1 = clientSummary.client_name;
    if (clientSummary.client_last_name) {
      members.name1 = `${members.name1 || ''} ${clientSummary.client_last_name}`.trim();
    }

    // Contact information
    if (clientSummary.phone) members.phone1 = clientSummary.phone;
    if (clientSummary.email) members.email1 = clientSummary.email;

    // Employment information
    if (clientSummary.occupation) members.occupation1 = clientSummary.occupation;
    if (clientSummary.employer) members.employer1 = clientSummary.employer;
    if (clientSummary.employment_status) members.employment_status1 = clientSummary.employment_status;

    // Personal information
    if (clientSummary.date_of_birth) {
      // Convert to Date object if it's a string
      if (typeof clientSummary.date_of_birth === 'string') {
        members.date_of_birth1 = clientSummary.date_of_birth;
      }
    }
    if (clientSummary.tax_number) members.tax_file_number1 = clientSummary.tax_number;
    if (clientSummary.citizenship) members.citizenship1 = clientSummary.citizenship;
    if (clientSummary.tax_residency) members.tax_residency1 = clientSummary.tax_residency;

    // Map partner (member2) data
    // First name and last name
    if (clientSummary.partner_name) members.name2 = clientSummary.partner_name;
    if (clientSummary.partner_last_name) {
      members.name2 = `${members.name2 || ''} ${clientSummary.partner_last_name}`.trim();
    }

    // Contact information
    if (clientSummary.partner_phone) members.phone2 = clientSummary.partner_phone;
    if (clientSummary.partner_email) members.email2 = clientSummary.partner_email;

    // Employment information
    if (clientSummary.partner_occupation) members.occupation2 = clientSummary.partner_occupation;
    if (clientSummary.partner_employer) members.employer2 = clientSummary.partner_employer;
    if (clientSummary.partner_employment_status) members.employment_status2 = clientSummary.partner_employment_status;

    // Personal information
    if (clientSummary.partner_date_of_birth) {
      // Convert to Date object if it's a string
      if (typeof clientSummary.partner_date_of_birth === 'string') {
        members.date_of_birth2 = clientSummary.partner_date_of_birth;
      }
    }
    if (clientSummary.partner_tax_number) members.tax_file_number2 = clientSummary.partner_tax_number;
    if (clientSummary.partner_citizenship) members.citizenship2 = clientSummary.partner_citizenship;
    if (clientSummary.partner_tax_residency) members.tax_residency2 = clientSummary.partner_tax_residency;

    // Use the enhanced member data if available
    if (Object.keys(mainMember).length > 0) {
      console.log('Using enhanced main member data');
      // Map any additional fields from mainMember
      if (mainMember.client_name) members.name1 = mainMember.client_name;
      if (mainMember.occupation) members.occupation1 = mainMember.occupation;
      if (mainMember.employer) members.employer1 = mainMember.employer;
      if (mainMember.tax_number) members.tax_file_number1 = mainMember.tax_number;
    }

    if (Object.keys(partnerMember).length > 0) {
      console.log('Using enhanced partner member data');
      // Map any additional fields from partnerMember
      if (partnerMember.partner_name) members.name2 = partnerMember.partner_name;
      if (partnerMember.partner_occupation) members.occupation2 = partnerMember.partner_occupation;
      if (partnerMember.partner_employer) members.employer2 = partnerMember.partner_employer;
      if (partnerMember.partner_tax_number) members.tax_file_number2 = partnerMember.partner_tax_number;
    }

    // Add additional fields from income_expenses section if needed
    if (tokenData.income_expenses) {
      const incomeExpenses = tokenData.income_expenses;

      // Map income fields
      if (incomeExpenses.employment_income) members.income1 = incomeExpenses.employment_income;
      if (incomeExpenses.partner_employment_income) members.income2 = incomeExpenses.partner_employment_income;
    }

    // Add additional fields from risk_profile section if needed
    if (tokenData.risk_profile) {
      const riskProfile = tokenData.risk_profile;

      // Map investment strategy and risk tolerance
      if (riskProfile.investment_experience) householdFields.investment_strategy = riskProfile.investment_experience;
      if (riskProfile.risk_profile) householdFields.risk_tolerance = riskProfile.risk_profile;
    }

    // Log the data we're about to save
    console.log('Updating household with:', {
      householdId: tokenData.household_id,
      householdFields,
      memberFields: members
    });

    // Update the household record
    const { error: updateError } = await supabase
      .from('households')
      .update({
        ...householdFields,
        members,
        updated_at: new Date().toISOString() // Add updated timestamp
      })
      .eq('id', tokenData.household_id);

    if (updateError) {
      console.error('Error updating household:', updateError);
      return NextResponse.json({ error: 'Failed to update household data' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Household data updated successfully',
      householdId: tokenData.household_id
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
