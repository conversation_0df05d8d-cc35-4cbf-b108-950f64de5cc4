import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Function to process goals data using Google AI
async function processDataWithAI(formData: any, mainMemberName: string, partnerMemberName: string) {
  try {
    // Initialize Google AI
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables');
      return null;
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite",
    });

    const generationConfig = {
      temperature: 0.2, // Lower temperature for more deterministic results
      topP: 0.95,
      topK: 40,
      maxOutputTokens: 8192,
      responseMimeType: "application/json",
    };

    // Create a prompt that explains the task to the AI
    const prompt = `
    You are a financial data processing assistant for a financial advisory firm. Your task is to analyze the following financial goals data from a discovery form
    filled out by a prospective client of a financial adviser, and prepare it for saving into a database.

    The data includes information about the client's financial goals and objectives. Please analyze this data and create structured goal entries according to the following guidelines:

    1. Identify all explicit and implicit goals from the client's responses
    2. For each goal:
       - Create a clear, descriptive title
       - Determine the appropriate goal type (Savings, Debt Reduction, Investment, Property, Education, Retirement, Travel, Business, Career, Health, Other)
       - Extract or infer relevant details about the goal
       - Determine logical start and achievement dates based on context and common sense
       - Determine if the goal has a target amount
       - Determine the priority of the goal (Low, Medium, High)
       - Determine if the goal is for the household, main client (${mainMemberName}), or partner (${partnerMemberName})
       - Set an appropriate status (Not Started, In Progress, Completed, On Hold)

    Client Information:
    ${JSON.stringify(formData, null, 2)}

    Please return a JSON object with the following structure:
    {
      "goals": [
        {
          "title": "string",
          "type": "Savings|Debt Reduction|Investment|Property|Education|Retirement|Travel|Business|Career|Health|Other",
          "details": "string",
          "start_date": "YYYY-MM-DD" or null,
          "achieved_date": "YYYY-MM-DD" or null,
          "status": "Not Started|In Progress|Completed|On Hold",
          "member": "Household|${mainMemberName}|${partnerMemberName}",
          "target_amount": number or null,
          "priority": "Low|Medium|High"
        }
      ]
    }

    IMPORTANT NOTES:
    - Create at least 3-5 goals based on the client's responses
    - Be specific and realistic with goal titles and details
    - For start_date: Use today's date for goals that should start immediately, future dates for goals that should start later, or null if unclear
    - For achieved_date: Use realistic future dates based on the goal type and context (e.g., retirement goals might be 20-30 years in the future)
    - For short-term goals (0-2 years), set achieved_date within 2 years from now
    - For medium-term goals (2-5 years), set achieved_date 2-5 years from now
    - For long-term goals (5+ years), set achieved_date at least 5 years from now
    - For retirement goals, calculate the achieved_date based on the client's current age and target retirement age
    - If a target_amount is not specified, set it to null
    - Ensure all goals have a priority level
    - Ensure all goals have a status (default to "Not Started" if unclear)
    - Ensure all goals have a member assignment (default to "Household" if it applies to both clients)
    - Format dates as YYYY-MM-DD
    - Today's date is ${new Date().toISOString().split('T')[0]}
    `;

    // Send the prompt to the AI
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig
    });

    const response = result.response;
    const text = response.text();

    // Parse the JSON response
    try {
      // Clean the response text to handle markdown code blocks
      let cleanedText = text;

      // Remove markdown code blocks if present
      if (cleanedText.includes('```')) {
        cleanedText = cleanedText.replace(/```json\s*|\s*```/g, '');
      }

      // Try to find JSON object in the text
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedText = jsonMatch[0];
      }

      const parsedResponse = JSON.parse(cleanedText);
      return parsedResponse;
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError, 'Raw text:', text);
      return null;
    }
  } catch (error) {
    console.error('Error processing data with AI:', error);
    return null;
  }
}

// Helper function to get household members from the households table
async function getHouseholdMembers(supabase: any, householdId: number) {
  try {
    const { data: household, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
      return { mainMemberId: null, partnerMemberId: null, mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
    }

    let mainMemberId = null;
    let partnerMemberId = null;
    let mainMemberName = 'Main Client';
    let partnerMemberName = 'Partner';

    if (household && household.members) {
      // Extract member names from the members JSON object
      mainMemberName = household.members.name1 || 'Main Client';
      partnerMemberName = household.members.name2 || 'Partner';

      // For compatibility with existing code, we'll set IDs to 1 and 2
      mainMemberId = 1;
      partnerMemberId = 2;
    }

    return { mainMemberId, partnerMemberId, mainMemberName, partnerMemberName };
  } catch (error) {
    console.error('Error in getHouseholdMembers:', error);
    return { mainMemberId: null, partnerMemberId: null, mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
  }
}

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, goals, response')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }

    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }

    const householdId = tokenData.household_id;
    const goalsData = tokenData.goals || {};
    const fullResponse = tokenData.response || {};

    // Get household member IDs and names
    const { mainMemberId, partnerMemberId, mainMemberName, partnerMemberName } = await getHouseholdMembers(supabase, householdId);

    console.log('Household members:', { mainMemberId, partnerMemberId, mainMemberName, partnerMemberName });

    // First, check if we already have goal entries for this household
    // to avoid duplicates
    const { data: existingGoals } = await supabase
      .from('goals')
      .select('title')
      .eq('household_id', householdId);

    if (existingGoals && existingGoals.length > 0) {
      console.log(`Found ${existingGoals.length} existing goals for household ${householdId}`);
    }

    // Get the current user for RLS
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's organization ID
    const { data: userData } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user.id)
      .single();

    const orgId = userData?.org_id;

    // Process the data with AI
    const aiProcessedData = await processDataWithAI(
      { ...goalsData, ...fullResponse },
      mainMemberName,
      partnerMemberName
    );

    console.log('AI processed data:', aiProcessedData ? 'Success' : 'Failed');

    // If AI processing failed, fall back to manual processing
    if (!aiProcessedData) {
      console.log('AI processing failed, falling back to manual processing');

      // Extract goals data from the discovery form
      const shortTermGoals = fullResponse.short_term_goals || [];
      const mediumTermGoals = fullResponse.medium_term_goals || [];
      const longTermGoals = fullResponse.long_term_goals || [];
      const financialPriorities = fullResponse.financial_priorities || [];
      const retirementAge = fullResponse.retirement_age;
      const partnerRetirementAge = fullResponse.partner_retirement_age;
      const retirementIncome = fullResponse.retirement_income;
      const legacyGoals = fullResponse.legacy_goals;

      console.log('Processing goals data manually:', {
        shortTermGoals: shortTermGoals.length,
        mediumTermGoals: mediumTermGoals.length,
        longTermGoals: longTermGoals.length,
        financialPriorities: Array.isArray(financialPriorities) ? financialPriorities.length : 'single value',
        retirementAge,
        partnerRetirementAge,
        retirementIncome,
        legacyGoals: legacyGoals ? 'present' : 'absent'
      });

      // Process short-term goals
      for (const goal of shortTermGoals) {
        if (!goal.title) continue;

        // Process member information
        let memberValue = 'Household';
        if (goal.member) {
          memberValue = goal.member.replace('{MainName}', mainMemberName).replace('{PartnerName}', partnerMemberName);
        }

        const goalData = {
          household_id: householdId,
          title: goal.title,
          type: goal.type || 'Other',
          details: goal.details || 'Short-term goal (0-2 years)',
          start_date: goal.start_date || null,
          achieved_date: goal.target_date || null,
          status: goal.status || 'Not Started',
          member: memberValue,
          target_amount: goal.target_amount ? parseFloat(goal.target_amount) : null,
          priority: goal.priority || 'Medium',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(goalData);
          console.log(`Successfully saved short-term goal: ${goal.title}`);
        } catch (insertError) {
          console.error(`Error saving short-term goal ${goal.title}:`, insertError);
        }
      }

      // Process medium-term goals
      for (const goal of mediumTermGoals) {
        if (!goal.title) continue;

        // Process member information
        let memberValue = 'Household';
        if (goal.member) {
          memberValue = goal.member.replace('{MainName}', mainMemberName).replace('{PartnerName}', partnerMemberName);
        }

        const goalData = {
          household_id: householdId,
          title: goal.title,
          type: goal.type || 'Other',
          details: goal.details || 'Medium-term goal (2-5 years)',
          start_date: goal.start_date || null,
          achieved_date: goal.target_date || null,
          status: goal.status || 'Not Started',
          member: memberValue,
          target_amount: goal.target_amount ? parseFloat(goal.target_amount) : null,
          priority: goal.priority || 'Medium',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(goalData);
          console.log(`Successfully saved medium-term goal: ${goal.title}`);
        } catch (insertError) {
          console.error(`Error saving medium-term goal ${goal.title}:`, insertError);
        }
      }

      // Process long-term goals
      for (const goal of longTermGoals) {
        if (!goal.title) continue;

        // Process member information
        let memberValue = 'Household';
        if (goal.member) {
          memberValue = goal.member.replace('{MainName}', mainMemberName).replace('{PartnerName}', partnerMemberName);
        }

        const goalData = {
          household_id: householdId,
          title: goal.title,
          type: goal.type || 'Other',
          details: goal.details || 'Long-term goal (5+ years)',
          start_date: goal.start_date || null,
          achieved_date: goal.target_date || null,
          status: goal.status || 'Not Started',
          member: memberValue,
          target_amount: goal.target_amount ? parseFloat(goal.target_amount) : null,
          priority: goal.priority || 'Medium',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(goalData);
          console.log(`Successfully saved long-term goal: ${goal.title}`);
        } catch (insertError) {
          console.error(`Error saving long-term goal ${goal.title}:`, insertError);
        }
      }

      // Create retirement goal if retirement age is provided
      if (retirementAge) {
        const retirementGoalData = {
          household_id: householdId,
          title: `${mainMemberName}'s Retirement`,
          type: 'Retirement',
          details: `${mainMemberName} plans to retire at age ${retirementAge}${retirementIncome ? ` with an annual income of $${retirementIncome}` : ''}`,
          start_date: null,
          achieved_date: null,
          status: 'Not Started',
          member: mainMemberName,
          target_amount: retirementIncome ? parseFloat(retirementIncome.toString()) : null,
          priority: 'High',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(retirementGoalData);
          console.log(`Successfully saved retirement goal for ${mainMemberName}`);
        } catch (insertError) {
          console.error(`Error saving retirement goal for ${mainMemberName}:`, insertError);
        }
      }

      // Create partner retirement goal if partner retirement age is provided
      if (partnerRetirementAge && partnerMemberName !== 'Partner') {
        const partnerRetirementGoalData = {
          household_id: householdId,
          title: `${partnerMemberName}'s Retirement`,
          type: 'Retirement',
          details: `${partnerMemberName} plans to retire at age ${partnerRetirementAge}${retirementIncome ? ` with an annual income of $${retirementIncome}` : ''}`,
          start_date: null,
          achieved_date: null,
          status: 'Not Started',
          member: partnerMemberName,
          target_amount: retirementIncome ? parseFloat(retirementIncome.toString()) : null,
          priority: 'High',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(partnerRetirementGoalData);
          console.log(`Successfully saved retirement goal for ${partnerMemberName}`);
        } catch (insertError) {
          console.error(`Error saving retirement goal for ${partnerMemberName}:`, insertError);
        }
      }

      // Create legacy goal if legacy goals are provided
      if (legacyGoals) {
        const legacyGoalData = {
          household_id: householdId,
          title: 'Legacy Planning',
          type: 'Other',
          details: legacyGoals,
          start_date: null,
          achieved_date: null,
          status: 'Not Started',
          member: 'Household',
          target_amount: null,
          priority: 'Medium',
          user_id: user.id,
          org_id: orgId
        };

        try {
          await supabase
            .from('goals')
            .insert(legacyGoalData);
          console.log('Successfully saved legacy goal');
        } catch (insertError) {
          console.error('Error saving legacy goal:', insertError);
        }
      }

      // Create goals for financial priorities if provided
      if (financialPriorities) {
        const priorities = Array.isArray(financialPriorities) ? financialPriorities : [financialPriorities];

        for (const priority of priorities) {
          const priorityGoalData = {
            household_id: householdId,
            title: priority,
            type: mapPriorityToType(priority),
            details: `Financial priority: ${priority}`,
            start_date: null,
            achieved_date: null,
            status: 'Not Started',
            member: 'Household',
            target_amount: null,
            priority: 'High',
            user_id: user.id,
            org_id: orgId
          };

          try {
            await supabase
              .from('goals')
              .insert(priorityGoalData);
            console.log(`Successfully saved financial priority goal: ${priority}`);
          } catch (insertError) {
            console.error(`Error saving financial priority goal ${priority}:`, insertError);
          }
        }
      }
    } else {
      // Use AI processed data
      console.log('Using AI processed data');
      console.log(`AI identified ${aiProcessedData.goals?.length || 0} goals`);

      // Save the goals to the database
      if (aiProcessedData.goals && aiProcessedData.goals.length > 0) {
        for (const goal of aiProcessedData.goals) {
          // Map member name to the correct format
          let memberValue = goal.member;
          if (memberValue === mainMemberName || memberValue === partnerMemberName) {
            // Keep as is
          } else if (memberValue === 'Main' || memberValue === 'Main Client') {
            memberValue = mainMemberName;
          } else if (memberValue === 'Partner') {
            memberValue = partnerMemberName;
          } else {
            memberValue = 'Household';
          }

          const goalData = {
            household_id: householdId,
            title: goal.title,
            type: goal.type,
            details: goal.details || null,
            start_date: goal.start_date || null,
            achieved_date: goal.achieved_date || null,
            status: goal.status || 'Not Started',
            member: memberValue,
            target_amount: goal.target_amount ? parseFloat(goal.target_amount.toString()) : null,
            priority: goal.priority || 'Medium',
            user_id: user.id,
            org_id: orgId
          };

          // Debug log the goal data
          console.log(`Saving goal: ${JSON.stringify(goalData)}`);

          try {
            const { data, error: insertError } = await supabase
              .from('goals')
              .insert(goalData)
              .select();

            if (insertError) {
              console.error(`Error saving goal ${goal.title}:`, insertError);
              console.error('Failed goal data:', JSON.stringify(goalData));
            } else {
              console.log(`Successfully saved goal: ${goal.title} with ID ${data[0]?.id}`);
            }
          } catch (error) {
            console.error(`Exception saving goal ${goal.title}:`, error);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Goals data updated successfully',
      householdId: householdId,
      dataAdded: {
        aiProcessed: aiProcessedData ? true : false
      }
    });
  } catch (error) {
    console.error('Error processing goals request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to map financial priorities to goal types
function mapPriorityToType(priority: string): string {
  const priorityMap: Record<string, string> = {
    'Retirement Planning': 'Retirement',
    'Debt Reduction': 'Debt Reduction',
    'Wealth Building': 'Investment',
    'Education Funding': 'Education',
    'Estate Planning': 'Other',
    'Tax Planning': 'Other',
    'Insurance Coverage': 'Other',
    'Property Purchase': 'Property',
    'Travel': 'Travel',
    'Business Goals': 'Business',
    'Other': 'Other'
  };

  return priorityMap[priority] || 'Other';
}
