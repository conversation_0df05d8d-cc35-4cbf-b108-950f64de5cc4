import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Helper function to convert frequency string to frequency_type
const mapFrequency = (frequency: string): string => {
  // Convert to lowercase for case-insensitive matching
  const freqLower = frequency?.toLowerCase() || '';

  // Map to the exact enum values expected by the database
  switch (freqLower) {
    case 'annually':
    case 'annual':
    case 'yearly':
    case 'year':
      return 'annually';

    case 'monthly':
    case 'month':
      return 'monthly';

    case 'fortnightly':
    case 'fortnight':
    case 'biweekly':
      return 'fortnightly';

    case 'weekly':
    case 'week':
      return 'weekly';

    case 'quarterly':
    case 'quarter':
      return 'quarterly';

    default:
      console.log(`Unknown frequency: ${frequency}, defaulting to annual`);
      return 'annual'; // Default to annual for discovery form data
  }
};

// Helper function to infer expense category based on name
const inferExpenseCategory = (name: string): string => {
  const nameLower = name.toLowerCase();

  // Housing related
  if (nameLower.includes('rent') || nameLower.includes('mortgage') || nameLower.includes('housing') ||
      nameLower.includes('property') || nameLower.includes('accommodation')) {
    return 'fixed';
  }

  // Utilities
  if (nameLower.includes('utility') || nameLower.includes('power') || nameLower.includes('electricity') ||
      nameLower.includes('water') || nameLower.includes('gas') || nameLower.includes('internet') ||
      nameLower.includes('phone') || nameLower.includes('broadband')) {
    return 'fixed';
  }

  // Food and groceries
  if (nameLower.includes('grocery') || nameLower.includes('food') || nameLower.includes('supermarket')) {
    return 'variable';
  }

  // Transport
  if (nameLower.includes('transport') || nameLower.includes('car') || nameLower.includes('fuel') ||
      nameLower.includes('petrol') || nameLower.includes('vehicle') || nameLower.includes('bus') ||
      nameLower.includes('train') || nameLower.includes('travel')) {
    return 'variable';
  }

  // Insurance
  if (nameLower.includes('insurance') || nameLower.includes('premium')) {
    return 'fixed';
  }

  // Debt
  if (nameLower.includes('debt') || nameLower.includes('loan') || nameLower.includes('repayment') ||
      nameLower.includes('credit card')) {
    return 'fixed';
  }

  // Entertainment and discretionary
  if (nameLower.includes('entertainment') || nameLower.includes('dining') || nameLower.includes('restaurant') ||
      nameLower.includes('subscription') || nameLower.includes('streaming') || nameLower.includes('hobby') ||
      nameLower.includes('leisure') || nameLower.includes('vacation') || nameLower.includes('holiday')) {
    return 'lifestyle';
  }

  // Default to variable if we can't determine
  return 'variable';
};

// Function to process income and expenses data using Google AI
async function processDataWithAI(formData: any) {
  try {
    // Initialize Google AI
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables');
      return null;
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite",
    });

    const generationConfig = {
      temperature: 0.2, // Lower temperature for more deterministic results
      topP: 0.95,
      topK: 40,
      maxOutputTokens: 8192,
      responseMimeType: "application/json",
    };

    // Create a prompt that explains the task to the AI
    const prompt = `
    You are a financial data processing assistant for a financial advisory firm. Your task is to analyze the following financial data from a discovery form
    filled out by a prospective client of a financial adviser, and prepare it for saving into a database.

    The data includes income and expenses information. Please categorize and structure this data according to the following guidelines:

    For incomes:
    1. Identify all income sources
    2. Determine if each income belongs to the main client (member_id: 1) or partner (member_id: 2) or household (no member_id)
    3. Determine the appropriate frequency (annual, monthly, fortnightly, weekly, quarterly) - IMPORTANT: use 'annual' not 'annually'
    4. Determine if the income is gross or net
    5. Add appropriate details for each income

    For expenses:
    1. Identify all expenses
    2. Categorize each expense as 'fixed', 'variable', or 'lifestyle'
    3. Determine the appropriate frequency (annual, monthly, fortnightly, weekly, quarterly) - IMPORTANT: use 'annual' not 'annually'
    4. Add appropriate details for each expense

    IMPORTANT INFORMATION ABOUT THE CLIENTS:
    - Main Client Name: ${formData.mainMemberName}
    - Partner Name: ${formData.partnerMemberName}
    - Main Client ID: 1
    - Partner Client ID: 2

    IMPORTANT NOTES:
    - Most values in the discovery form are ANNUAL amounts, so default to "annual" for frequency unless specified otherwise
    - Employment income should be assigned to the correct member (main client or partner)
    - For expenses, use common sense to determine the appropriate frequency (e.g., rent is usually monthly, groceries could be weekly)
    - Make sure to include ALL income sources and expenses found in the data
    - If you're unsure about a member assignment, use the name to determine ownership

    Here is the discovery form data:
    ${JSON.stringify(formData, null, 2)}

    Please return a JSON object with the following structure:
    {
      "incomes": [
        {
          "source": "string",
          "amount": number,
          "frequency": "annual|monthly|fortnightly|weekly|quarterly",
          "income_type": "gross|net",
          "details": "string",
          "member_id": number|null
        }
      ],
      "expenses": [
        {
          "name": "string",
          "amount": number,
          "frequency": "annual|monthly|fortnightly|weekly|quarterly",
          "category": "fixed|variable|lifestyle",
          "details": "string"
        }
      ]
    }
    `;

    // Send the prompt to the AI
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig
    });

    const responseText = result.response.text();

    // Parse the JSON response
    try {
      const processedData = JSON.parse(responseText);
      console.log('AI processed data successfully');
      console.log('AI processed incomes:', JSON.stringify(processedData.incomes, null, 2));
      return processedData;
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.log('AI response text:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error processing data with AI:', error);
    return null;
  }
}

// Helper function to get household member IDs
async function getHouseholdMembers(supabase: any, householdId: number) {
  const { data: householdData, error } = await supabase
    .from('households')
    .select('members')
    .eq('id', householdId)
    .single();

  if (error || !householdData?.members) {
    console.warn('Error fetching household members:', error);
    return { mainMemberId: null, partnerMemberId: null, mainMemberName: null, partnerMemberName: null };
  }

  // Extract member IDs based on the structure
  const members = householdData.members;

  // In the household table, members are stored with keys like name1, name2
  // We need to return the actual IDs that can be used in the income table
  // For this application, we'll use 1 for main client and 2 for partner

  // Check if we have a main client (member1)
  const hasMainMember = members.name1 ? true : false;
  // Check if we have a partner (member2)
  const hasPartnerMember = members.name2 ? true : false;

  console.log('Household members found:', {
    hasMainMember,
    mainMemberName: members.name1,
    hasPartnerMember,
    partnerMemberName: members.name2
  });

  return {
    mainMemberId: hasMainMember ? 1 : null,
    partnerMemberId: hasPartnerMember ? 2 : null,
    mainMemberName: members.name1,
    partnerMemberName: members.name2
  };
}

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, income_expenses, response')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }

    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }

    const householdId = tokenData.household_id;
    const incomeExpensesData = tokenData.income_expenses || {};
    const fullResponse = tokenData.response || {};

    // Get household member IDs and names
    const { mainMemberId, partnerMemberId, mainMemberName, partnerMemberName } = await getHouseholdMembers(supabase, householdId);

    console.log('Household members:', { mainMemberId, partnerMemberId, mainMemberName, partnerMemberName });

    // First, check if we already have income/expense entries for this household
    // to avoid duplicates
    const { data: existingIncomes } = await supabase
      .from('income')
      .select('source')
      .eq('household_id', householdId);

    const { data: existingExpenses } = await supabase
      .from('expenses')
      .select('name')
      .eq('household_id', householdId);

    const existingIncomeSources = new Set(existingIncomes?.map(i => i.source.toLowerCase()) || []);
    const existingExpenseNames = new Set(existingExpenses?.map(e => e.name.toLowerCase()) || []);

    // Prepare data for AI processing
    const dataForAI = {
      incomeExpensesData,
      fullResponse,
      mainMemberId,
      partnerMemberId,
      // Include member names from household data
      mainMemberName: mainMemberName || fullResponse.client_name || 'Main Client',
      partnerMemberName: partnerMemberName || fullResponse.partner_name || 'Partner',
      // Include other relevant data
      otherIncome: fullResponse.other_income || [],
      livingExpenses: fullResponse.living_expenses || [],
      discretionaryExpenses: fullResponse.discretionary_expenses || []
    };

    console.log('Member names for AI processing:', {
      mainMemberName: dataForAI.mainMemberName,
      partnerMemberName: dataForAI.partnerMemberName
    });

    console.log('Processing data with AI...');

    // Process data with AI
    const aiProcessedData = await processDataWithAI(dataForAI);

    // If AI processing failed, fall back to manual processing
    if (!aiProcessedData) {
      console.log('AI processing failed, falling back to manual processing');

      // Extract income and expenses data from the discovery form
      const otherIncome = fullResponse.other_income || [];
      const livingExpenses = fullResponse.living_expenses || [];
      const discretionaryExpenses = fullResponse.discretionary_expenses || [];

      console.log('Processing income and expenses data manually:', {
        otherIncome: otherIncome.length,
        livingExpenses: livingExpenses.length,
        discretionaryExpenses: discretionaryExpenses.length
      });

      // Process standard income fields with more detailed information
      const standardIncomeFields = [
        {
          source: 'Employment Income (Main)',
          amount: incomeExpensesData.employment_income,
          frequency: incomeExpensesData.employment_income_frequency || 'annual',
          income_type: 'gross',
          details: 'Main client employment income',
          memberId: mainMemberId
        },
        {
          source: 'Employment Income (Partner)',
          amount: incomeExpensesData.partner_employment_income,
          frequency: incomeExpensesData.partner_employment_income_frequency || 'annual',
          income_type: 'gross',
          details: 'Partner employment income',
          memberId: partnerMemberId
        },
        {
          source: 'Business Income',
          amount: incomeExpensesData.business_income,
          frequency: 'annual',
          income_type: 'gross',
          details: 'Income from business activities',
          memberId: null // Household level
        },
        {
          source: 'Rental Income',
          amount: incomeExpensesData.rental_income,
          frequency: 'annual',
          income_type: 'gross',
          details: 'Income from rental properties',
          memberId: null // Household level
        },
        {
          source: 'Investment Income',
          amount: incomeExpensesData.investment_income,
          frequency: 'annual',
          income_type: 'gross',
          details: 'Income from investments (dividends, interest, etc.)',
          memberId: null // Household level
        },
        {
          source: 'Government Benefits',
          amount: incomeExpensesData.government_benefits,
          frequency: 'fortnightly',
          income_type: 'gross',
          details: 'Income from government benefits or support',
          memberId: null // Household level
        }
      ];

      // Add standard income entries
      for (const income of standardIncomeFields) {
        if (income.amount && parseFloat(income.amount) > 0) {
          // Check if this income source already exists
          if (existingIncomeSources.has(income.source.toLowerCase())) {
            console.log(`Skipping existing income source: ${income.source}`);
            continue;
          }

          // Create the income object
          const incomeData: any = {
            household_id: householdId,
            source: income.source,
            amount: parseFloat(income.amount),
            frequency: mapFrequency(income.frequency),
            income_type: income.income_type,
            details: income.details,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Add member_id if available
          if (income.memberId) {
            // Ensure member_id is properly formatted for the database
            // If your database expects a number, convert it to a number
            incomeData.member_id = Number(income.memberId);
            console.log(`Assigning income ${income.source} to member ${incomeData.member_id} (type: ${typeof incomeData.member_id})`);
          }

          try {
            await supabase
              .from('income')
              .insert(incomeData);
            console.log(`Successfully saved income: ${income.source}`);
          } catch (insertError) {
            console.error(`Error saving income ${income.source}:`, insertError);
          }
        }
      }

      // Process other income entries with more intelligence
      for (const income of otherIncome) {
        if (!income.source || !income.amount) continue; // Skip entries without source or amount

        // Check if this income source already exists
        if (existingIncomeSources.has(income.source.toLowerCase())) {
          console.log(`Skipping existing income source: ${income.source}`);
          continue;
        }

        // Determine member ID based on the member field if available
        let memberId = null;
        if (income.member === '{MainName}' && mainMemberId) {
          memberId = mainMemberId;
        } else if (income.member === '{PartnerName}' && partnerMemberId) {
          memberId = partnerMemberId;
        }

        // Infer income type based on source
        const sourceLower = income.source.toLowerCase();
        let incomeType = 'gross';
        if (sourceLower.includes('net') || sourceLower.includes('after tax')) {
          incomeType = 'net';
        }

        // Create the income object
        const incomeData: any = {
          household_id: householdId,
          source: income.source,
          amount: parseFloat(income.amount),
          frequency: mapFrequency(income.frequency || 'annual'),
          income_type: incomeType,
          details: income.details || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add member_id if available
        if (memberId) {
          incomeData.member_id = memberId;
          console.log(`Assigning other income ${income.source} to member ${memberId}`);
        }

        try {
          await supabase
            .from('income')
            .insert(incomeData);
          console.log(`Successfully saved other income: ${income.source}`);
        } catch (insertError) {
          console.error(`Error saving other income ${income.source}:`, insertError);
        }
      }

      // Process standard expense fields with more detailed mapping
      // For discovery form data, most expenses are entered as annual amounts
      const standardExpenseFields = [
        {
          name: 'Housing',
          amount: incomeExpensesData.housing_expenses,
          frequency: 'annual',
          category: 'fixed',
          details: 'Mortgage or rent payments'
        },
        {
          name: 'Utilities',
          amount: incomeExpensesData.utilities,
          frequency: 'annual',
          category: 'fixed',
          details: 'Power, water, internet, etc.'
        },
        {
          name: 'Groceries',
          amount: incomeExpensesData.groceries,
          frequency: 'annual',
          category: 'variable',
          details: 'Food and household supplies'
        },
        {
          name: 'Transport',
          amount: incomeExpensesData.transport,
          frequency: 'annual',
          category: 'variable',
          details: 'Fuel, public transport, vehicle maintenance'
        },
        {
          name: 'Insurance Premiums',
          amount: incomeExpensesData.insurance_premiums,
          frequency: 'annual',
          category: 'fixed',
          details: 'All insurance premiums combined'
        },
        {
          name: 'Debt Repayments',
          amount: incomeExpensesData.debt_repayments,
          frequency: 'annual',
          category: 'fixed',
          details: 'Loan repayments, credit card payments, etc.'
        }
      ];

      // Add standard expense entries
      for (const expense of standardExpenseFields) {
        if (expense.amount && parseFloat(expense.amount) > 0) {
          // Check if this expense name already exists
          if (existingExpenseNames.has(expense.name.toLowerCase())) {
            console.log(`Skipping existing expense name: ${expense.name}`);
            continue;
          }

          const expenseData = {
            household_id: householdId,
            name: expense.name,
            amount: parseFloat(expense.amount),
            frequency: mapFrequency(expense.frequency),
            category: expense.category,
            details: expense.details,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          try {
            await supabase
              .from('expenses')
              .insert(expenseData);
            console.log(`Successfully saved standard expense: ${expense.name}`);
          } catch (insertError) {
            console.error(`Error saving standard expense ${expense.name}:`, insertError);
          }
        }
      }

      // Process living expenses with better category mapping
      for (const expense of livingExpenses) {
        if (!expense.category || !expense.amount) continue; // Skip entries without category or amount

        // Check if this expense name already exists
        if (existingExpenseNames.has(expense.category.toLowerCase())) {
          console.log(`Skipping existing expense name: ${expense.category}`);
          continue;
        }

        // Infer the appropriate category based on the expense name
        const category = inferExpenseCategory(expense.category);

        const expenseData = {
          household_id: householdId,
          name: expense.category,
          amount: parseFloat(expense.amount),
          frequency: mapFrequency(expense.frequency || 'annual'),
          category: category,
          details: expense.details || 'Living expense',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          await supabase
            .from('expenses')
            .insert(expenseData);
          console.log(`Successfully saved living expense: ${expense.category}`);
        } catch (insertError) {
          console.error(`Error saving living expense ${expense.category}:`, insertError);
        }
      }

      // Process discretionary expenses
      for (const expense of discretionaryExpenses) {
        if (!expense.category || !expense.amount) continue; // Skip entries without category or amount

        // Check if this expense name already exists
        if (existingExpenseNames.has(expense.category.toLowerCase())) {
          console.log(`Skipping existing expense name: ${expense.category}`);
          continue;
        }

        const expenseData = {
          household_id: householdId,
          name: expense.category,
          amount: parseFloat(expense.amount),
          frequency: mapFrequency(expense.frequency || 'annual'),
          category: 'lifestyle', // Discretionary expenses are lifestyle by definition
          details: expense.details || 'Discretionary expense',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          await supabase
            .from('expenses')
            .insert(expenseData);
          console.log(`Successfully saved discretionary expense: ${expense.category}`);
        } catch (insertError) {
          console.error(`Error saving discretionary expense ${expense.category}:`, insertError);
        }
      }
    } else {
      // Use AI processed data
      console.log('Using AI processed data');
      console.log(`AI identified ${aiProcessedData.incomes?.length || 0} incomes and ${aiProcessedData.expenses?.length || 0} expenses`);

      // Process incomes from AI
      if (aiProcessedData.incomes && aiProcessedData.incomes.length > 0) {
        for (const income of aiProcessedData.incomes) {
          // Skip if no source or amount
          if (!income.source || !income.amount) continue;

          // Check if this income source already exists
          if (existingIncomeSources.has(income.source.toLowerCase())) {
            console.log(`Skipping existing income source: ${income.source}`);
            continue;
          }

          // Create the income object
          const incomeData: any = {
            household_id: householdId,
            source: income.source,
            amount: parseFloat(income.amount.toString()),
            frequency: mapFrequency(income.frequency || 'annual'),
            income_type: income.income_type || 'gross',
            details: income.details || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Add member_id if available
          if (income.member_id !== undefined && income.member_id !== null) {
            // Ensure member_id is a number
            incomeData.member_id = Number(income.member_id);
            console.log(`Assigning AI-processed income ${income.source} to member ${incomeData.member_id}`);
          } else {
            // Try to infer member_id from the source name
            const sourceLower = income.source.toLowerCase();
            if (sourceLower.includes('main') || sourceLower.includes('client')) {
              incomeData.member_id = mainMemberId;
              console.log(`Inferred main member for income ${income.source}, assigning to member ${mainMemberId}`);
            } else if (sourceLower.includes('partner') || sourceLower.includes('spouse')) {
              incomeData.member_id = partnerMemberId;
              console.log(`Inferred partner for income ${income.source}, assigning to member ${partnerMemberId}`);
            } else {
              // Default to household (0) if no specific member is identified
              incomeData.member_id = 0;
              console.log(`No specific member identified for income ${income.source}, assigning to household (0)`);
            }
          }

          // Debug log the final income data
          console.log(`Saving income: ${JSON.stringify(incomeData)}`);

          try {
            const { data, error: insertError } = await supabase
              .from('income')
              .insert(incomeData)
              .select();

            if (insertError) {
              console.error(`Error saving income ${income.source}:`, insertError);
              console.error('Failed income data:', JSON.stringify(incomeData));
            } else {
              console.log(`Successfully saved income: ${income.source} with ID ${data[0]?.id}`);
            }
          } catch (error) {
            console.error(`Exception saving income ${income.source}:`, error);
          }
        }
      }

      // Process expenses from AI
      if (aiProcessedData.expenses && aiProcessedData.expenses.length > 0) {
        for (const expense of aiProcessedData.expenses) {
          // Skip if no name or amount
          if (!expense.name || !expense.amount) continue;

          // Check if this expense name already exists
          if (existingExpenseNames.has(expense.name.toLowerCase())) {
            console.log(`Skipping existing expense name: ${expense.name}`);
            continue;
          }

          const expenseData = {
            household_id: householdId,
            name: expense.name,
            amount: parseFloat(expense.amount.toString()),
            frequency: mapFrequency(expense.frequency || 'annual'),
            category: expense.category || 'variable',
            details: expense.details || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Debug log the expense data
          console.log(`Saving expense: ${JSON.stringify(expenseData)}`);

          try {
            await supabase
              .from('expenses')
              .insert(expenseData);
            console.log(`Successfully saved expense: ${expense.name}`);
          } catch (insertError) {
            console.error(`Error saving expense ${expense.name}:`, insertError);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Income and expenses data updated successfully',
      householdId: householdId,
      dataAdded: {
        aiProcessed: aiProcessedData ? true : false
      }
    });
  } catch (error) {
    console.error('Error processing income and expenses request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to get member UUID from numeric ID
async function getMemberUuid(supabase: { from: (arg0: string) => { (): any; new(): any; select: { (arg0: string): { (): any; new(): any; eq: { (arg0: string, arg1: any): { (): any; new(): any; eq: { (arg0: string, arg1: any): { (): any; new(): any; single: { (): PromiseLike<{ data: any; error: any; }> | { data: any; error: any; }; new(): any; }; }; new(): any; }; }; new(): any; }; }; new(): any; }; }; }, memberId: any, householdId: any) {
  // Query the members table to get the UUID for this member
  const { data, error } = await supabase
    .from('household_members')
    .select('id')
    .eq('household_id', householdId)
    .eq('member_number', memberId)
    .single();

  if (error || !data) {
    console.error('Error finding member UUID:', error);
    return null;
  }

  return data.id;
}
