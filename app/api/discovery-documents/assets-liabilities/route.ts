import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Function to process assets and liabilities data using Google AI
async function processDataWithAI(formData: any, mainMemberName: string, partnerMemberName: string) {
  try {
    // Initialize Google AI
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables');
      return null;
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite",
    });

    const generationConfig = {
      temperature: 0.2, // Lower temperature for more deterministic results
      topP: 0.95,
      topK: 40,
      maxOutputTokens: 8192,
      responseMimeType: "application/json",
    };

    // Create a prompt that explains the task to the AI
    const prompt = `
    You are a financial data processing assistant for a financial advisory firm. Your task is to analyze the following financial data from a discovery form
    filled out by a prospective client of a financial adviser, and prepare it for saving into a database.

    The data includes information about the client's assets and liabilities. Please analyze this data and create structured entries according to the following guidelines:

    For assets:
    1. Identify all assets from the client's responses
    2. For each asset:
       - Create a clear, descriptive name
       - Determine the appropriate asset type (property, vehicle, savings, investment, superannuation, other)
       - Extract or infer the value of the asset
       - For property assets, determine if it's owner_occupied or investment
       - For investment properties, determine rental income if available
       - For financial assets (savings, investment, superannuation), determine the provider if available
       - Extract any additional details about the asset

    For liabilities:
    1. Identify all liabilities from the client's responses
    2. For each liability:
       - Create a clear, descriptive name
       - Determine the appropriate liability type (mortgage, car_loan, personal_loan, credit_card, student_loan, business_loan, tax_debt, other)
       - Extract or infer the amount of the liability
       - Determine the interest rate if available
       - Determine the lender if available
       - For loans, determine if it's principal_and_interest or interest_only
       - Determine payment amount and frequency if available
       - Try to link liabilities to assets where appropriate (e.g., mortgage to property)
       - Extract any additional details about the liability

    Client Information:
    ${JSON.stringify(formData, null, 2)}

    Please return a JSON object with the following structure:
    {
      "assets": [
        {
          "name": "string",
          "type": "property|vehicle|savings|investment|superannuation|other",
          "value": number,
          "details": "string" or null,
          "property_type": "owner_occupied|investment" or null,
          "rental_income": number or null,
          "provider": "string" or null
        }
      ],
      "liabilities": [
        {
          "name": "string",
          "type": "mortgage|car_loan|personal_loan|credit_card|student_loan|business_loan|tax_debt|other",
          "amount": number,
          "interest_rate": number or null,
          "lender": "string" or null,
          "payment_amount": number or null,
          "payment_frequency": "annually|monthly|fortnightly|weekly|quarterly" or null,
          "loan_type": "principal_and_interest|interest_only" or null,
          "details": "string" or null,
          "linked_asset_name": "string" or null
        }
      ]
    }

    IMPORTANT NOTES:
    - Be specific and realistic with asset and liability names and details
    - Ensure all assets have a type and value
    - Ensure all liabilities have a type and amount
    - For property assets, ALWAYS include property_type (either 'owner_occupied' or 'investment')
    - For investment properties, ALWAYS include rental_income (use a reasonable estimate if not specified)
    - For financial assets (savings, investment, superannuation), ALWAYS include provider (extract from name if necessary)
    - For loans, ALWAYS include loan_type ('principal_and_interest' or 'interest_only') for mortgage, car_loan, personal_loan, and business_loan types
    - ALWAYS include interest_rate for loans (use a reasonable estimate if not specified)
    - Format currency values as numbers without currency symbols or commas
    - For linked_asset_name, use the exact name of the corresponding asset if there's a clear relationship
    - Today's date is ${new Date().toISOString().split('T')[0]}
    - DO NOT leave optional fields as null if you can reasonably infer or estimate their values
    `;

    // Send the prompt to the AI
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig
    });

    const response = result.response;
    const text = response.text();

    // Parse the JSON response
    try {
      // Clean the response text to handle markdown code blocks
      let cleanedText = text;

      // Remove markdown code blocks if present
      if (cleanedText.includes('```')) {
        cleanedText = cleanedText.replace(/```json\s*|\s*```/g, '');
      }

      // Try to find JSON object in the text
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedText = jsonMatch[0];
      }

      const parsedResponse = JSON.parse(cleanedText);
      return parsedResponse;
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError, 'Raw text:', text);
      return null;
    }
  } catch (error) {
    console.error('Error processing data with AI:', error);
    return null;
  }
}

// Helper function to get household members from the households table
async function getHouseholdMembers(supabase: any, householdId: number) {
  try {
    const { data: household, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
      return { mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
    }

    let mainMemberName = 'Main Client';
    let partnerMemberName = 'Partner';

    if (household && household.members) {
      // Extract member names from the members JSON object
      mainMemberName = household.members.name1 || 'Main Client';
      partnerMemberName = household.members.name2 || 'Partner';
    }

    return { mainMemberName, partnerMemberName };
  } catch (error) {
    console.error('Error in getHouseholdMembers:', error);
    return { mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
  }
}

// Helper function to map frequency string to frequency_type
function mapFrequency(frequency: string): string {
  // Convert to lowercase for case-insensitive matching
  const freqLower = frequency?.toLowerCase() || '';

  // Map to the exact enum values expected by the database
  switch (freqLower) {
    case 'annually':
    case 'annual':
    case 'yearly':
    case 'year':
      return 'annually';

    case 'monthly':
    case 'month':
      return 'monthly';

    case 'fortnightly':
    case 'fortnight':
    case 'biweekly':
      return 'fortnightly';

    case 'weekly':
    case 'week':
      return 'weekly';

    case 'quarterly':
    case 'quarter':
      return 'quarterly';

    default:
      console.log(`Unknown frequency: ${frequency}, defaulting to monthly`);
      return 'monthly'; // Default to monthly for liabilities
  }
}

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, assets_liabilities, response')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }

    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }

    const householdId = tokenData.household_id;
    const assetsLiabilitiesData = tokenData.assets_liabilities || {};
    const fullResponse = tokenData.response || {};

    // Get household member names
    const { mainMemberName, partnerMemberName } = await getHouseholdMembers(supabase, householdId);

    console.log('Household members:', { mainMemberName, partnerMemberName });

    // First, check if we already have asset and liability entries for this household
    // to avoid duplicates
    const { data: existingAssets } = await supabase
      .from('assets')
      .select('name')
      .eq('household_id', householdId);

    const { data: existingLiabilities } = await supabase
      .from('liabilities')
      .select('name')
      .eq('household_id', householdId);

    if (existingAssets && existingAssets.length > 0) {
      console.log(`Found ${existingAssets.length} existing assets for household ${householdId}`);
    }

    if (existingLiabilities && existingLiabilities.length > 0) {
      console.log(`Found ${existingLiabilities.length} existing liabilities for household ${householdId}`);
    }

    // Get the current user for RLS
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's organization ID
    const { data: userData } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user.id)
      .single();

    const orgId = userData?.org_id;

    // Process the data with AI
    const aiProcessedData = await processDataWithAI(
      { ...assetsLiabilitiesData, ...fullResponse },
      mainMemberName,
      partnerMemberName
    );

    console.log('AI processed data:', aiProcessedData ? 'Success' : 'Failed');

    // If AI processing failed, fall back to manual processing
    if (!aiProcessedData) {
      console.log('AI processing failed, falling back to manual processing');

      // Extract assets data from the discovery form
      const properties = fullResponse.investment_assets || [];
      const vehicles = fullResponse.other_assets?.filter((asset: any) => asset.type === 'vehicle') || [];
      const savingsAccounts = fullResponse.savings_accounts || [];
      const superannuationAccounts = fullResponse.superannuation || [];
      const otherAssets = fullResponse.other_assets?.filter((asset: any) => asset.type !== 'vehicle') || [];

      console.log('Processing assets data manually:', {
        properties: properties.length,
        vehicles: vehicles.length,
        savingsAccounts: savingsAccounts.length,
        superannuationAccounts: superannuationAccounts.length,
        otherAssets: otherAssets.length
      });

      // Process properties
      for (const property of properties) {
        if (!property.name || !property.value) continue;

        const assetData = {
          household_id: householdId,
          name: property.name,
          type: 'property',
          value: parseFloat(property.value),
          details: property.details || null,
          property_type: property.type || 'investment',
          rental_income: property.rental_income ? parseFloat(property.rental_income) : null,
          provider: null
        };

        try {
          await supabase
            .from('assets')
            .insert(assetData);
          console.log(`Successfully saved property asset: ${property.name}`);
        } catch (insertError) {
          console.error(`Error saving property asset ${property.name}:`, insertError);
        }
      }

      // Process vehicles
      for (const vehicle of vehicles) {
        if (!vehicle.type || !vehicle.value) continue;

        const assetData = {
          household_id: householdId,
          name: vehicle.type,
          type: 'vehicle',
          value: parseFloat(vehicle.value),
          details: null,
          property_type: null,
          rental_income: null,
          provider: null
        };

        try {
          await supabase
            .from('assets')
            .insert(assetData);
          console.log(`Successfully saved vehicle asset: ${vehicle.type}`);
        } catch (insertError) {
          console.error(`Error saving vehicle asset ${vehicle.type}:`, insertError);
        }
      }

      // Process savings accounts
      for (const account of savingsAccounts) {
        if (!account.name || !account.value) continue;

        // Extract provider from name if not explicitly provided
        let provider = account.provider;
        if (!provider && account.name) {
          // Try to extract provider from the name
          const nameParts = account.name.split(' ');
          if (nameParts.length > 1) {
            // Assume first part might be the provider
            provider = nameParts[0];
          } else {
            provider = account.name; // Use the full name as provider if no spaces
          }
        }

        const assetData = {
          household_id: householdId,
          name: account.name,
          type: 'savings',
          value: parseFloat(account.value),
          details: account.details || null,
          property_type: null,
          rental_income: null,
          provider: provider
        };

        try {
          await supabase
            .from('assets')
            .insert(assetData);
          console.log(`Successfully saved savings asset: ${account.name}`);
        } catch (insertError) {
          console.error(`Error saving savings asset ${account.name}:`, insertError);
        }
      }

      // Process superannuation accounts
      for (const account of superannuationAccounts) {
        if (!account.name || !account.value) continue;

        // Extract provider from name if not explicitly provided
        let provider = account.provider;
        if (!provider && account.name) {
          // Try to extract provider from the name
          const nameParts = account.name.split(' ');
          if (nameParts.length > 1) {
            // Assume first part might be the provider
            provider = nameParts[0];
          } else {
            provider = account.name; // Use the full name as provider if no spaces
          }
        }

        const assetData = {
          household_id: householdId,
          name: account.name,
          type: 'superannuation',
          value: parseFloat(account.value),
          details: account.details || null,
          property_type: null,
          rental_income: null,
          provider: provider
        };

        try {
          await supabase
            .from('assets')
            .insert(assetData);
          console.log(`Successfully saved superannuation asset: ${account.name}`);
        } catch (insertError) {
          console.error(`Error saving superannuation asset ${account.name}:`, insertError);
        }
      }

      // Process other assets
      for (const asset of otherAssets) {
        if (!asset.name || !asset.value) continue;

        const assetData = {
          household_id: householdId,
          name: asset.name,
          type: 'other',
          value: parseFloat(asset.value),
          details: asset.details || null,
          property_type: null,
          rental_income: null,
          provider: null
        };

        try {
          await supabase
            .from('assets')
            .insert(assetData);
          console.log(`Successfully saved other asset: ${asset.name}`);
        } catch (insertError) {
          console.error(`Error saving other asset ${asset.name}:`, insertError);
        }
      }

      // Extract liabilities data from the discovery form
      const mortgages = fullResponse.mortgages || [];
      const personalLoans = fullResponse.personal_loans || [];
      const creditCards = fullResponse.credit_cards || [];
      const otherDebts = fullResponse.other_debts || [];

      console.log('Processing liabilities data manually:', {
        mortgages: mortgages.length,
        personalLoans: personalLoans.length,
        creditCards: creditCards.length,
        otherDebts: otherDebts.length
      });

      // Process mortgages
      for (const mortgage of mortgages) {
        if (!mortgage.property || !mortgage.amount) continue;

        // Set default interest rate if not provided
        const interestRate = mortgage.rate ? parseFloat(mortgage.rate) : 5.5; // Default mortgage rate

        // Try to find a matching property asset to link
        let linkedAssetId = null;
        try {
          const { data: propertyAssets } = await supabase
            .from('assets')
            .select('id, name')
            .eq('household_id', householdId)
            .eq('type', 'property');

          if (propertyAssets && propertyAssets.length > 0) {
            // Try to find a property with a matching name
            const matchingProperty = propertyAssets.find(asset =>
              asset.name.toLowerCase().includes(mortgage.property.toLowerCase()) ||
              mortgage.property.toLowerCase().includes(asset.name.toLowerCase())
            );

            if (matchingProperty) {
              linkedAssetId = matchingProperty.id;
              console.log(`Linked mortgage to property asset: ${matchingProperty.name}`);
            }
          }
        } catch (error) {
          console.error('Error finding matching property asset:', error);
        }

        const liabilityData = {
          household_id: householdId,
          name: `Mortgage on ${mortgage.property}`,
          type: 'mortgage',
          amount: parseFloat(mortgage.amount),
          interest_rate: interestRate,
          lender: 'Unknown Bank', // Default lender
          payment_amount: null,
          payment_frequency: 'monthly',
          loan_type: 'principal_and_interest',
          details: `Term: ${mortgage.term || 'Unknown'} years`,
          linked_asset_id: linkedAssetId
        };

        try {
          await supabase
            .from('liabilities')
            .insert(liabilityData);
          console.log(`Successfully saved mortgage liability: ${liabilityData.name}`);
        } catch (insertError) {
          console.error(`Error saving mortgage liability ${liabilityData.name}:`, insertError);
        }
      }

      // Process personal loans
      for (const loan of personalLoans) {
        if (!loan.type || !loan.amount) continue;

        // Set default interest rate if not provided
        const interestRate = loan.rate ? parseFloat(loan.rate) : 10.0; // Default personal loan rate

        const liabilityData = {
          household_id: householdId,
          name: loan.type,
          type: 'personal_loan',
          amount: parseFloat(loan.amount),
          interest_rate: interestRate,
          lender: 'Unknown Lender', // Default lender
          payment_amount: null,
          payment_frequency: 'monthly',
          loan_type: 'principal_and_interest',
          details: null,
          linked_asset_id: null
        };

        try {
          await supabase
            .from('liabilities')
            .insert(liabilityData);
          console.log(`Successfully saved personal loan liability: ${loan.type}`);
        } catch (insertError) {
          console.error(`Error saving personal loan liability ${loan.type}:`, insertError);
        }
      }

      // Process credit cards
      for (const card of creditCards) {
        if (!card.provider || !card.balance) continue;

        // Set default interest rate for credit cards
        const interestRate = 19.99; // Default credit card interest rate

        const liabilityData = {
          household_id: householdId,
          name: `${card.provider} Credit Card`,
          type: 'credit_card',
          amount: parseFloat(card.balance),
          interest_rate: interestRate,
          lender: card.provider,
          payment_amount: null,
          payment_frequency: 'monthly', // Most credit cards have monthly payments
          loan_type: null,
          details: card.limit ? `Credit limit: $${card.limit}` : null,
          linked_asset_id: null
        };

        try {
          await supabase
            .from('liabilities')
            .insert(liabilityData);
          console.log(`Successfully saved credit card liability: ${liabilityData.name}`);
        } catch (insertError) {
          console.error(`Error saving credit card liability ${liabilityData.name}:`, insertError);
        }
      }

      // Process other debts
      for (const debt of otherDebts) {
        if (!debt.type || !debt.amount) continue;

        // Set default interest rate for other debts
        const interestRate = 8.0; // Default interest rate for other debts

        const liabilityData = {
          household_id: householdId,
          name: debt.type,
          type: 'other',
          amount: parseFloat(debt.amount),
          interest_rate: interestRate,
          lender: 'Unknown', // Default lender
          payment_amount: null,
          payment_frequency: 'monthly', // Default to monthly payments
          loan_type: null,
          details: 'Other debt',
          linked_asset_id: null
        };

        try {
          await supabase
            .from('liabilities')
            .insert(liabilityData);
          console.log(`Successfully saved other debt liability: ${debt.type}`);
        } catch (insertError) {
          console.error(`Error saving other debt liability ${debt.type}:`, insertError);
        }
      }
    } else {
      // Use AI processed data
      console.log('Using AI processed data');
      console.log(`AI identified ${aiProcessedData.assets?.length || 0} assets and ${aiProcessedData.liabilities?.length || 0} liabilities`);

      // Create a map to store asset IDs by name for linking liabilities to assets
      const assetIdsByName: Record<string, string> = {};

      // Save the assets to the database
      if (aiProcessedData.assets && aiProcessedData.assets.length > 0) {
        for (const asset of aiProcessedData.assets) {
          // Ensure all required fields have values
          let provider = asset.provider;
          if (!provider && ['savings', 'investment', 'superannuation'].includes(asset.type)) {
            // Try to extract provider from the name
            const nameParts = asset.name.split(' ');
            if (nameParts.length > 1) {
              // Assume first part might be the provider
              provider = nameParts[0];
            } else {
              provider = asset.name; // Use the full name as provider if no spaces
            }
          }

          // Ensure property_type is set for property assets
          let propertyType = asset.property_type;
          if (asset.type === 'property' && !propertyType) {
            // Default to owner_occupied if not specified
            propertyType = 'owner_occupied';

            // Try to infer from name or details
            const nameAndDetails = (asset.name + ' ' + (asset.details || '')).toLowerCase();
            if (nameAndDetails.includes('investment') ||
                nameAndDetails.includes('rental') ||
                nameAndDetails.includes('income')) {
              propertyType = 'investment';
            }
          }

          const assetData = {
            household_id: householdId,
            name: asset.name,
            type: asset.type,
            value: asset.value,
            details: asset.details || null,
            property_type: asset.type === 'property' ? propertyType : null,
            rental_income: asset.type === 'property' && propertyType === 'investment' ? asset.rental_income || 0 : null,
            provider: ['savings', 'investment', 'superannuation'].includes(asset.type) ? provider : null
          };

          // Debug log the asset data
          console.log(`Saving asset: ${JSON.stringify(assetData)}`);

          try {
            const { data, error: insertError } = await supabase
              .from('assets')
              .insert(assetData)
              .select();

            if (insertError) {
              console.error(`Error saving asset ${asset.name}:`, insertError);
              console.error('Failed asset data:', JSON.stringify(assetData));
            } else {
              console.log(`Successfully saved asset: ${asset.name} with ID ${data[0]?.id}`);
              // Store the asset ID for linking with liabilities
              assetIdsByName[asset.name] = data[0]?.id;
            }
          } catch (error) {
            console.error(`Exception saving asset ${asset.name}:`, error);
          }
        }
      }

      // Save the liabilities to the database
      if (aiProcessedData.liabilities && aiProcessedData.liabilities.length > 0) {
        for (const liability of aiProcessedData.liabilities) {
          // Find linked asset ID if available
          let linkedAssetId = null;
          if (liability.linked_asset_name && assetIdsByName[liability.linked_asset_name]) {
            linkedAssetId = assetIdsByName[liability.linked_asset_name];
          }

          // Ensure all required fields have values
          let interestRate = liability.interest_rate;
          if (!interestRate && ['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(liability.type)) {
            // Set default interest rates based on liability type
            switch(liability.type) {
              case 'mortgage':
                interestRate = 5.5; // Default mortgage rate
                break;
              case 'car_loan':
                interestRate = 7.0; // Default car loan rate
                break;
              case 'personal_loan':
                interestRate = 10.0; // Default personal loan rate
                break;
              case 'business_loan':
                interestRate = 8.0; // Default business loan rate
                break;
              default:
                interestRate = 6.0; // Default rate
            }
          }

          // Ensure loan type is set for appropriate liability types
          let loanType = liability.loan_type;
          if (!loanType && ['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(liability.type)) {
            loanType = 'principal_and_interest'; // Default to principal and interest
          }

          // Set default payment frequency if not provided
          let paymentFrequency = liability.payment_frequency ? mapFrequency(liability.payment_frequency) : null;
          if (!paymentFrequency && ['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(liability.type)) {
            paymentFrequency = 'monthly'; // Default to monthly payments
          }

          // Set default lender if not provided
          let lender = liability.lender;
          if (!lender) {
            // Try to extract from name
            const nameParts = liability.name.split(' ');
            if (nameParts.length > 1 && !['loan', 'mortgage', 'debt', 'card'].includes(nameParts[0].toLowerCase())) {
              lender = nameParts[0];
            } else {
              lender = 'Unknown';
            }
          }

          const liabilityData = {
            household_id: householdId,
            name: liability.name,
            type: liability.type,
            amount: liability.amount,
            interest_rate: interestRate,
            lender: lender,
            payment_amount: liability.payment_amount || null,
            payment_frequency: paymentFrequency,
            loan_type: ['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(liability.type) ? loanType : null,
            details: liability.details || null,
            linked_asset_id: linkedAssetId
          };

          // Debug log the liability data
          console.log(`Saving liability: ${JSON.stringify(liabilityData)}`);

          try {
            const { data, error: insertError } = await supabase
              .from('liabilities')
              .insert(liabilityData)
              .select();

            if (insertError) {
              console.error(`Error saving liability ${liability.name}:`, insertError);
              console.error('Failed liability data:', JSON.stringify(liabilityData));
            } else {
              console.log(`Successfully saved liability: ${liability.name} with ID ${data[0]?.id}`);
            }
          } catch (error) {
            console.error(`Exception saving liability ${liability.name}:`, error);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Assets and liabilities data updated successfully',
      householdId: householdId,
      dataAdded: {
        aiProcessed: aiProcessedData ? true : false
      }
    });
  } catch (error) {
    console.error('Error processing assets and liabilities request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
