import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { token } = await request.json();
    
    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }
    
    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, relationships, response')
      .eq('token', token)
      .single();
    
    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }
    
    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }
    
    const householdId = tokenData.household_id;
    const relationshipsData = tokenData.relationships || {};
    const fullResponse = tokenData.response || {};
    
    // Extract relationship data from the discovery form
    const children = fullResponse.children || [];
    const dependents = fullResponse.dependents || [];
    const professionalAdvisors = fullResponse.professional_advisors || [];
    
    console.log('Processing relationships data:', {
      children: children.length,
      dependents: dependents.length,
      professionalAdvisors: professionalAdvisors.length
    });
    
    // Process children - they are family relationships
    for (const child of children) {
      if (!child.name) continue; // Skip entries without a name
      
      await supabase
        .from('relationships')
        .insert({
          household_id: householdId,
          name: child.name,
          relationship_type: 'family',
          family_relationship: child.relationship || 'Child',
          notes: `Age: ${child.age || 'Unknown'}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
    
    // Process dependents - they are also family relationships
    for (const dependent of dependents) {
      if (!dependent.name) continue; // Skip entries without a name
      
      await supabase
        .from('relationships')
        .insert({
          household_id: householdId,
          name: dependent.name,
          relationship_type: 'family',
          family_relationship: dependent.relationship || 'Dependent',
          notes: `Age: ${dependent.age || 'Unknown'}, Support: ${dependent.support || 'Not specified'}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
    
    // Process professional advisors
    for (const advisor of professionalAdvisors) {
      if (!advisor.name) continue; // Skip entries without a name
      
      await supabase
        .from('relationships')
        .insert({
          household_id: householdId,
          name: advisor.name,
          relationship_type: 'professional',
          profession: advisor.profession || 'Advisor',
          phone: advisor.contact?.split(',')[0] || '',
          email: advisor.contact?.includes('@') ? advisor.contact : '',
          notes: `Company: ${advisor.company || 'Not specified'}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Relationships data updated successfully',
      householdId: householdId,
      relationshipsAdded: {
        children: children.length,
        dependents: dependents.length,
        professionalAdvisors: professionalAdvisors.length
      }
    });
  } catch (error) {
    console.error('Error processing relationships request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
