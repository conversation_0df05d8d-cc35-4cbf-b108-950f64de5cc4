import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Helper function to map frequency string to frequency_type enum
function mapFrequency(frequency: string): string {
  // Convert to lowercase for case-insensitive matching
  const freqLower = frequency?.toLowerCase() || '';

  // Map to the exact enum values expected by the database
  switch (freqLower) {
    case 'annually':
    case 'annual':
    case 'yearly':
    case 'year':
      return 'annually';

    case 'monthly':
    case 'month':
      return 'monthly';

    case 'fortnightly':
    case 'fortnight':
    case 'biweekly':
      return 'fortnightly';

    case 'weekly':
    case 'week':
      return 'weekly';

    case 'quarterly':
    case 'quarter':
      return 'quarterly';

    default:
      console.log(`Unknown frequency: ${frequency}, defaulting to annually`);
      return 'annually'; // Default to annually
  }
}

// Helper function to map insurance type to valid enum value
function mapInsuranceType(type: string): string {
  // Convert to lowercase for case-insensitive matching
  const typeLower = type?.toLowerCase() || '';

  // Map to the exact enum values expected by the database
  switch (typeLower) {
    case 'life':
      return 'life';
    case 'health':
      return 'health';
    case 'income_protection':
    case 'income protection':
      return 'income_protection';
    case 'tpd':
    case 'total permanent disability':
    case 'total & permanent disability':
      return 'tpd';
    case 'trauma':
      return 'trauma';
    case 'home':
      return 'home';
    case 'contents':
      return 'contents';
    case 'vehicle':
    case 'car':
    case 'auto':
      return 'vehicle';
    case 'business':
      return 'business';
    case 'travel':
      return 'travel';
    default:
      console.log(`Unknown insurance type: ${type}, defaulting to other`);
      return 'other';
  }
}

// Helper function to get household members from the households table
async function getHouseholdMembers(supabase: any, householdId: number) {
  try {
    const { data: household, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
      return { mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
    }

    let mainMemberName = 'Main Client';
    let partnerMemberName = 'Partner';

    if (household && household.members) {
      // Extract member names from the members JSON object
      mainMemberName = household.members.name1 || 'Main Client';
      partnerMemberName = household.members.name2 || 'Partner';
    }

    return { mainMemberName, partnerMemberName };
  } catch (error) {
    console.error('Error in getHouseholdMembers:', error);
    return { mainMemberName: 'Main Client', partnerMemberName: 'Partner' };
  }
}

// Function to process insurance data using Google AI
async function processDataWithAI(formData: any, mainMemberName: string, partnerMemberName: string) {
  try {
    // Initialize Google AI
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables');
      return null;
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite",
    });

    const generationConfig = {
      temperature: 0.2, // Lower temperature for more deterministic results
      topP: 0.95,
      topK: 40,
      maxOutputTokens: 8192,
      responseMimeType: "application/json",
    };

    // Create a prompt that explains the task to the AI
    const prompt = `
    You are a financial data processing assistant for a financial advisory firm. Your task is to analyze the following insurance data from a discovery form
    filled out by a prospective client of a financial adviser, and prepare it for saving into a database.

    The data includes various types of insurance information. Please structure this data according to the following guidelines:

    1. Extract all insurance policies from the data
    2. For each insurance policy, identify:
       - type (life, health, income_protection, tpd, trauma, home, contents, vehicle, business, travel, other)
       - provider (insurance company name)
       - policy_number (if available)
       - premium (annual cost)
       - frequency (annually, monthly, fortnightly, weekly)
       - coverage_amount (total coverage amount)
       - renewal_date (if available, in YYYY-MM-DD format)
       - details (any additional information)
       - person_insured (who is covered by this policy - main client, partner, or household)
       - policy_owner (who owns this policy - main client, partner, or household)

    3. Use the following member names for person_insured and policy_owner:
       - Main client: "${mainMemberName}"
       - Partner: "${partnerMemberName}"
       - Household: "Household"

    4. If any field is not explicitly provided, make a reasonable inference based on the available data.

    Here is the data from the discovery form:
    ${JSON.stringify(formData, null, 2)}

    Please return a JSON object with the following structure:
    {
      "insurances": [
        {
          "type": "life",
          "provider": "Insurance Company Name",
          "policy_number": "Policy123",
          "premium": 1000,
          "frequency": "annually",
          "coverage_amount": 500000,
          "renewal_date": "2023-12-31",
          "details": "Additional details about the policy",
          "person_insured": "${mainMemberName}",
          "policy_owner": "${mainMemberName}"
        },
        // Additional insurance policies...
      ]
    }
    `;

    // Generate content with the AI model
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig,
    });

    const responseText = result.response.text();

    // Parse the JSON response
    try {
      // Extract JSON from the response (in case there's any additional text)
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.warn('No valid JSON found in AI response');
        return null;
      }

      const jsonResponse = JSON.parse(jsonMatch[0]);
      console.log('Successfully processed insurance data with AI');
      return jsonResponse;
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.log('AI response:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error processing data with AI:', error);
    return null;
  }
}

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the discovery token data
    const supabase = createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('discovery_tokens')
      .select('household_id, insurances, response')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to fetch discovery token data' }, { status: 404 });
    }

    if (!tokenData.household_id) {
      return NextResponse.json({ error: 'No household ID associated with this token' }, { status: 400 });
    }

    const householdId = tokenData.household_id;
    const insurancesData = tokenData.insurances || {};
    const fullResponse = tokenData.response || {};

    // Get household member names
    const { mainMemberName, partnerMemberName } = await getHouseholdMembers(supabase, householdId);

    console.log('Household members:', { mainMemberName, partnerMemberName });

    // First, check if we already have insurance entries for this household
    // to avoid duplicates
    const { data: existingInsurances } = await supabase
      .from('insurances')
      .select('provider, type')
      .eq('household_id', householdId);

    // Get the current user for RLS
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's organization ID
    const { data: userData } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user.id)
      .single();

    const orgId = userData?.org_id;

    // Process the data with AI
    const aiProcessedData = await processDataWithAI(
      { ...insurancesData, ...fullResponse },
      mainMemberName,
      partnerMemberName
    );

    console.log('AI processed data:', aiProcessedData ? 'Success' : 'Failed');

    // If AI processing failed, fall back to manual processing
    if (!aiProcessedData) {
      console.log('AI processing failed, falling back to manual processing');

      // Extract insurance data from the discovery form
      const lifeInsurance = fullResponse.life_insurance || [];
      const healthInsurance = fullResponse.health_insurance || [];
      const incomeProtection = fullResponse.income_protection || [];
      const traumaInsurance = fullResponse.trauma_insurance || [];
      const tpdInsurance = fullResponse.tpd_insurance || [];
      const generalInsurance = fullResponse.general_insurance || [];

      console.log('Processing insurance data manually:', {
        lifeInsurance: lifeInsurance.length,
        healthInsurance: healthInsurance.length,
        incomeProtection: incomeProtection.length,
        traumaInsurance: traumaInsurance.length,
        tpdInsurance: tpdInsurance.length,
        generalInsurance: generalInsurance.length
      });

      // Process life insurance
      for (const insurance of lifeInsurance) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          i.type === 'life'
        );

        if (existingInsurance) {
          console.log(`Skipping existing life insurance from ${insurance.provider}`);
          continue;
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType('life'),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || mainMemberName,
          policy_owner: insurance.policy_owner || insurance.member || mainMemberName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving life insurance from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved life insurance from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving life insurance from ${insurance.provider}:`, error);
        }
      }

      // Process health insurance
      for (const insurance of healthInsurance) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          i.type === 'health'
        );

        if (existingInsurance) {
          console.log(`Skipping existing health insurance from ${insurance.provider}`);
          continue;
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType('health'),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || mainMemberName,
          policy_owner: insurance.policy_owner || insurance.member || mainMemberName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving health insurance from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved health insurance from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving health insurance from ${insurance.provider}:`, error);
        }
      }

      // Process income protection
      for (const insurance of incomeProtection) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          i.type === 'income_protection'
        );

        if (existingInsurance) {
          console.log(`Skipping existing income protection from ${insurance.provider}`);
          continue;
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType('income_protection'),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || mainMemberName,
          policy_owner: insurance.policy_owner || insurance.member || mainMemberName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving income protection from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved income protection from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving income protection from ${insurance.provider}:`, error);
        }
      }

      // Process trauma insurance
      for (const insurance of traumaInsurance) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          i.type === 'trauma'
        );

        if (existingInsurance) {
          console.log(`Skipping existing trauma insurance from ${insurance.provider}`);
          continue;
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType('trauma'),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || mainMemberName,
          policy_owner: insurance.policy_owner || insurance.member || mainMemberName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving trauma insurance from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved trauma insurance from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving trauma insurance from ${insurance.provider}:`, error);
        }
      }

      // Process TPD insurance
      for (const insurance of tpdInsurance) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          i.type === 'tpd'
        );

        if (existingInsurance) {
          console.log(`Skipping existing TPD insurance from ${insurance.provider}`);
          continue;
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType('tpd'),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || mainMemberName,
          policy_owner: insurance.policy_owner || insurance.member || mainMemberName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving TPD insurance from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved TPD insurance from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving TPD insurance from ${insurance.provider}:`, error);
        }
      }

      // Process general insurance
      for (const insurance of generalInsurance) {
        // Skip if this insurance already exists
        const existingInsurance = existingInsurances?.find(i =>
          i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
          (i.type === 'home' || i.type === 'contents' || i.type === 'vehicle')
        );

        if (existingInsurance) {
          console.log(`Skipping existing general insurance from ${insurance.provider}`);
          continue;
        }

        // Determine the type based on details or default to 'other'
        let type = 'other';
        const details = (insurance.details || '').toLowerCase();
        if (details.includes('home') || details.includes('house')) {
          type = 'home';
        } else if (details.includes('content') || details.includes('belonging')) {
          type = 'contents';
        } else if (details.includes('car') || details.includes('vehicle') || details.includes('auto')) {
          type = 'vehicle';
        }

        const insuranceData = {
          household_id: householdId,
          type: mapInsuranceType(insurance.type || type),
          provider: insurance.provider || '',
          policy_number: insurance.policy_number || '',
          premium: parseFloat(insurance.premium) || 0,
          frequency: mapFrequency(insurance.frequency || 'annually'),
          coverage_amount: parseFloat(insurance.coverage_amount) || 0,
          renewal_date: insurance.renewal_date || null,
          details: insurance.details || '',
          person_insured: insurance.person_insured || insurance.member || 'Household',
          policy_owner: insurance.policy_owner || insurance.member || 'Household',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          const { error: insertError } = await supabase
            .from('insurances')
            .insert(insuranceData);

          if (insertError) {
            console.error(`Error saving general insurance from ${insurance.provider}:`, insertError);
          } else {
            console.log(`Successfully saved general insurance from ${insurance.provider}`);
          }
        } catch (error) {
          console.error(`Exception saving general insurance from ${insurance.provider}:`, error);
        }
      }
    } else {
      // Use AI processed data
      console.log('Using AI processed data');
      console.log(`AI identified ${aiProcessedData.insurances?.length || 0} insurance policies`);

      if (aiProcessedData.insurances && aiProcessedData.insurances.length > 0) {
        for (const insurance of aiProcessedData.insurances) {
          // Skip if this insurance already exists
          const existingInsurance = existingInsurances?.find(i =>
            i.provider?.toLowerCase() === insurance.provider?.toLowerCase() &&
            i.type === insurance.type
          );

          if (existingInsurance) {
            console.log(`Skipping existing ${insurance.type} insurance from ${insurance.provider}`);
            continue;
          }

          const insuranceData = {
            household_id: householdId,
            type: mapInsuranceType(insurance.type || 'other'),
            provider: insurance.provider || '',
            policy_number: insurance.policy_number || '',
            premium: parseFloat(insurance.premium?.toString()) || 0,
            frequency: mapFrequency(insurance.frequency || 'annually'),
            coverage_amount: parseFloat(insurance.coverage_amount?.toString()) || 0,
            renewal_date: insurance.renewal_date || null,
            details: insurance.details || '',
            person_insured: insurance.person_insured || mainMemberName,
            policy_owner: insurance.policy_owner || mainMemberName,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Debug log the insurance data
          console.log(`Saving insurance: ${JSON.stringify(insuranceData)}`);

          try {
            const { error: insertError } = await supabase
              .from('insurances')
              .insert(insuranceData);

            if (insertError) {
              console.error(`Error saving ${insurance.type} insurance from ${insurance.provider}:`, insertError);
            } else {
              console.log(`Successfully saved ${insurance.type} insurance from ${insurance.provider}`);
            }
          } catch (error) {
            console.error(`Exception saving ${insurance.type} insurance from ${insurance.provider}:`, error);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Insurance data updated successfully',
      householdId: householdId,
      dataAdded: {
        aiProcessed: aiProcessedData ? true : false
      }
    });
  } catch (error) {
    console.error('Error processing insurance request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
