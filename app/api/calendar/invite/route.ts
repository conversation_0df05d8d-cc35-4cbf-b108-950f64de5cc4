import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { createClient } from '@/utils/supabase/server';
import { CalendarInviteEmailNew } from '@/components/emails/CalendarInviteEmailNew';
import { generateICSFile, generateGoogleCalendarUrl, generateOutlookCalendarUrl } from '@/utils/icsGenerator';

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('name, email')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Parse the request body
    const {
      eventId,
      title,
      description,
      location,
      startDate,
      endDate,
      startTime,
      endTime,
      isAllDay,
      requiredMembers,
      optionalMembers,
      householdMembers,
      externalEmails,
    } = await request.json();

    if (!eventId || !title || !startDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get the event details from the database
    const { data: event, error: eventError } = await supabase
      .from('calendar_events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError || !event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Fetch required organization members
    const orgMemberIds = [...(requiredMembers || []), ...(optionalMembers || [])];
    let orgMembers: { user_id: string; name: string; email: string }[] = [];

    if (orgMemberIds.length > 0) {
      const { data: members, error: membersError } = await supabase
        .from('profiles')
        .select('user_id, name, email')
        .in('user_id', orgMemberIds);

      if (!membersError && members) {
        orgMembers = members;
      }
    }

    // Fetch household members
    let householdMemberDetails: { id: string; name: string; email: string }[] = [];

    if (householdMembers && householdMembers.length > 0) {
      // Extract household IDs from the member IDs (format: household-{id}-{memberIndex})
      const householdIds = householdMembers
        .map((id: string) => {
          const match = id.match(/household-(\d+)-\d+/);
          return match ? parseInt(match[1]) : null;
        })
        .filter((id: null) => id !== null);

      if (householdIds.length > 0) {
        const { data: households, error: householdsError } = await supabase
          .from('households')
          .select('id, members')
          .in('id', householdIds);

        if (!householdsError && households) {
          // Extract member details from households
          householdMemberDetails = householdMembers.map((memberId: string) => {
            const match = memberId.match(/household-(\d+)-(\d+)/);
            if (!match) return null;

            const householdId = parseInt(match[1]);
            const memberIndex = parseInt(match[2]);

            const household = households.find(h => h.id === householdId);
            if (!household || !household.members) return null;

            const memberKey = `name${memberIndex}`;
            const emailKey = `email${memberIndex}`;

            if (household.members[memberKey] && household.members[emailKey]) {
              return {
                id: memberId,
                name: household.members[memberKey],
                email: household.members[emailKey],
              };
            }

            return null;
          }).filter((member: null) => member !== null) as { id: string; name: string; email: string }[];
        }
      }
    }

    // Prepare attendees for the ICS file and email
    const attendees = [
      // Required organization members
      ...(requiredMembers || []).map((id: string) => {
        const member = orgMembers.find(m => m.user_id === id);
        return member ? {
          name: member.name,
          email: member.email,
          role: 'REQ-PARTICIPANT' as const,
        } : null;
      }).filter(Boolean),

      // Optional organization members
      ...(optionalMembers || []).map((id: string) => {
        const member = orgMembers.find(m => m.user_id === id);
        return member ? {
          name: member.name,
          email: member.email,
          role: 'OPT-PARTICIPANT' as const,
        } : null;
      }).filter(Boolean),

      // Household members
      ...householdMemberDetails.map(member => ({
        name: member.name,
        email: member.email,
        role: 'REQ-PARTICIPANT' as const,
      })),

      // External emails
      ...(externalEmails || []).map((email: any) => ({
        email,
        role: 'REQ-PARTICIPANT' as const,
      })),
    ].filter(Boolean) as Array<{
      name?: string;
      email: string;
      role: 'REQ-PARTICIPANT' | 'OPT-PARTICIPANT';
    }>;

    // Parse dates
    const parsedStartDate = new Date(`${startDate}T${isAllDay ? '00:00:00' : startTime}:00`);
    const parsedEndDate = new Date(`${endDate || startDate}T${isAllDay ? '23:59:59' : endTime}:00`);

    // Generate ICS file
    const icsContent = generateICSFile({
      title,
      description,
      location,
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      isAllDay,
      organizerName: profile.name,
      organizerEmail: profile.email,
      attendees,
    });

    // Generate calendar URLs
    const googleCalendarUrl = generateGoogleCalendarUrl({
      title,
      description,
      location,
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      isAllDay,
      organizerName: profile.name,
      organizerEmail: profile.email,
      attendees,
    });

    const outlookCalendarUrl = generateOutlookCalendarUrl({
      title,
      description,
      location,
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      isAllDay,
      organizerName: profile.name,
      organizerEmail: profile.email,
      attendees,
    });

    // Send emails to all attendees with rate limiting (max 2 per second)
    console.log(`Sending emails to ${attendees.length} recipients with rate limiting`);

    // Function to send email with rate limiting
    const sendEmailWithRateLimit = async (attendee: any, index: number) => {
      // Add delay to respect rate limits (2 per second)
      // Wait (index / 2) seconds, rounded down to the nearest second
      const delaySeconds = Math.floor(index / 2);
      if (delaySeconds > 0) {
        console.log(`Rate limiting: Waiting ${delaySeconds} seconds before sending to ${attendee.email}`);
        await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000 + 100)); // Add 100ms buffer
      }
      try {
        // Create a personalized version of the attendees list for this recipient
        const personalizedAttendees = attendees.map(a => {
          // Check if this is the current recipient
          const isCurrentRecipient = a.email === attendee.email;

          return {
            ...a,
            // Mark the current recipient for display purposes
            isCurrentRecipient,
            // Ensure name is correct
            name: a.name || a.email
          };
        });

        // Generate direct RSVP URLs with the actual recipient email
        const baseUrl = new URL(request.url).origin;
        const rsvpBaseUrl = `${baseUrl}/api/calendar/rsvp`;

        // Create RSVP URLs with the actual recipient email (no placeholders)
        const rsvpYesUrl = `${rsvpBaseUrl}?eventId=${eventId}&response=yes&email=${encodeURIComponent(attendee.email)}`;
        const rsvpNoUrl = `${rsvpBaseUrl}?eventId=${eventId}&response=no&email=${encodeURIComponent(attendee.email)}`;
        const rsvpMaybeUrl = `${rsvpBaseUrl}?eventId=${eventId}&response=maybe&email=${encodeURIComponent(attendee.email)}`;

        // Prepare the email content with personalized RSVP links
        const emailContent = CalendarInviteEmailNew({
          title,
          date: startDate,
          startTime,
          endTime,
          isAllDay,
          description,
          location,
          organizerName: profile.name,
          googleCalendarUrl,
          outlookCalendarUrl,
          eventId: parseInt(eventId),
          attendees: personalizedAttendees,
          // Pass the personalized RSVP URLs
          rsvpYesUrl,
          rsvpNoUrl,
          rsvpMaybeUrl,
          recipientEmail: attendee.email
        });

        // Send the email with the appropriate content
        const { data, error } = await resend.emails.send({
          from: `Wealthie Calendar <<EMAIL>>`,
          to: [attendee.email],
          subject: `Calendar Invitation: ${title}`,
          // Always use the React component directly
          // This ensures the email placeholder is properly handled
          react: emailContent,
          attachments: [
            {
              filename: 'invitation.ics',
              content: Buffer.from(icsContent),
            },
          ],
          headers: {
            // Add headers to improve deliverability
            'X-Entity-Ref-ID': `event-${eventId}-${Date.now()}`,
            'List-Unsubscribe': '<mailto:<EMAIL>>',
          },
        });

        if (error) {
          console.error(`Error sending email to ${attendee.email}:`, error);
          return { email: attendee.email, success: false, error };
        }

        return { email: attendee.email, success: true, messageId: data?.id };
      } catch (error) {
        console.error(`Error sending email to ${attendee.email}:`, error);
        return { email: attendee.email, success: false, error };
      }
    };

    // Process emails sequentially with rate limiting
    const emailPromises = [];
    for (let i = 0; i < attendees.length; i++) {
      emailPromises.push(sendEmailWithRateLimit(attendees[i], i));
    }

    // Add notifications for organization members
    const notificationPromises = [...(requiredMembers || []), ...(optionalMembers || [])].map(async (userId) => {
      try {
        const { error } = await supabase
          .from('notifications')
          .insert({
            user_id: userId,
            type: 'calendar_invite',
            content: `You have been invited to "${title}" on ${new Date(startDate).toLocaleDateString()}`,
            link: `/protected/calendar?event=${eventId}`,
            read: false,
          });

        if (error) {
          console.error(`Error creating notification for user ${userId}:`, error);
          return { userId, success: false, error };
        }

        return { userId, success: true };
      } catch (error) {
        console.error(`Error creating notification for user ${userId}:`, error);
        return { userId, success: false, error };
      }
    });

    // Wait for all promises to resolve
    const [emailResults, notificationResults] = await Promise.all([
      Promise.all(emailPromises),
      Promise.all(notificationPromises),
    ]);

    // Log results for debugging
    console.log(`Email results: ${emailResults.filter(r => r.success).length} successful, ${emailResults.filter(r => !r.success).length} failed`);
    console.log(`Notification results: ${notificationResults.filter(r => r.success).length} successful, ${notificationResults.filter(r => !r.success).length} failed`);

    // Update the event with invites_sent_at timestamp
    await supabase
      .from('calendar_events')
      .update({ invites_sent_at: new Date().toISOString() })
      .eq('id', eventId);

    return NextResponse.json({
      success: true,
      emailResults,
      notificationResults,
    });
  } catch (error) {
    console.error('Error sending calendar invites:', error);
    return NextResponse.json(
      { error: 'Failed to send calendar invites' },
      { status: 500 }
    );
  }
}
