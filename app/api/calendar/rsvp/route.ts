import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Mark this route as dynamic to prevent static generation errors
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const eventId = searchParams.get('eventId');
    const response = searchParams.get('response');
    let email = searchParams.get('email');

    // Log the email parameter for debugging
    console.log('RSVP request with email:', email);

    // Handle the {{EMAIL}} placeholder (it might be URL-encoded)
    if (email === '{{EMAIL}}' || email === '%7B%7BEMAIL%7D%7D') {
      console.log('Detected {{EMAIL}} placeholder in RSVP request');
      // This is a direct click from the email without proper substitution
      // Redirect to an error page
      const baseUrl = new URL(request.url).origin;
      return NextResponse.redirect(new URL('/calendar/rsvp?error=invalid_email', baseUrl));
    }

    if (!eventId || !response || !email) {
      const baseUrl = new URL(request.url).origin;
      return NextResponse.redirect(new URL('/calendar/rsvp?error=invalid_rsvp', baseUrl));
    }

    // Validate response value
    if (!['yes', 'no', 'maybe'].includes(response)) {
      const baseUrl = new URL(request.url).origin;
      return NextResponse.redirect(new URL('/calendar/rsvp?error=invalid_response', baseUrl));
    }

    // Get the event
    const { data: event, error: eventError } = await supabase
      .from('calendar_events')
      .select('id, responses')
      .eq('id', eventId)
      .single();

    if (eventError || !event) {
      const baseUrl = new URL(request.url).origin;
      return NextResponse.redirect(new URL('/calendar/rsvp?error=event_not_found', baseUrl));
    }

    // Update the responses
    const currentResponses = event.responses || {};
    currentResponses[email] = response;

    const { error: updateError } = await supabase
      .from('calendar_events')
      .update({ responses: currentResponses })
      .eq('id', eventId);

    if (updateError) {
      console.error('Error updating event responses:', updateError);
      const baseUrl = new URL(request.url).origin;
      return NextResponse.redirect(new URL('/calendar/rsvp?error=update_failed', baseUrl));
    }

    // Redirect to the RSVP response page (use the same domain as the request)
    // This ensures it works on both localhost and production
    const baseUrl = new URL(request.url).origin;
    return NextResponse.redirect(new URL(`/calendar/rsvp?eventId=${eventId}&response=${response}&email=${encodeURIComponent(email)}`, baseUrl));
  } catch (error) {
    console.error('Error handling RSVP:', error);
    const baseUrl = new URL(request.url).origin;
    return NextResponse.redirect(new URL('/calendar/rsvp?error=server_error', baseUrl));
  }
}
