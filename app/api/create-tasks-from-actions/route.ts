import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
// Use a different model - you'll need to update this to the correct model name
const MODEL_NAME = 'gemini-2.0-flash-lite'; // Update this to the correct model name

export async function POST(request: Request) {
  try {
    const { actionItems, householdId, noteId } = await request.json();
    
    console.log("Received request:", { 
      actionItemsCount: actionItems?.length, 
      householdId, 
      noteId 
    });
    
    // Ensure householdId is a number
    const numericHouseholdId = householdId ? Number(householdId) : undefined;
    
    if (!actionItems || !Array.isArray(actionItems) || actionItems.length === 0) {
      console.log("Invalid action items provided");
      return NextResponse.json(
        { error: 'Invalid action items provided' },
        { status: 400 }
      );
    }

    const supabase = createClient();
    
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.log("No authenticated user found");
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log("Processing for user:", user.id);

    // Get the user's organization ID
    const { data: userData } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', user.id)
      .single();

    const orgId = userData?.org_id;

    // Fetch the note to get context
    let noteTitle = '';
    let noteContent = '';
    
    if (noteId) {
      const { data: noteData } = await supabase
        .from('notes')
        .select('title, content')
        .eq('id', noteId)
        .single();
      
      if (noteData) {
        noteTitle = noteData.title || '';
        noteContent = typeof noteData.content === 'string' 
          ? noteData.content 
          : JSON.stringify(noteData.content || '');
      }
    }

    // Calculate tomorrow's date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Process action items to create tasks
    const taskIds = [];
    
    console.log(`Starting to process ${actionItems.length} action items`);
    
    for (const item of actionItems) {
      try {
        console.log(`Processing action item: "${item.substring(0, 30)}..."`);
        
        let title = '';
        let content = '';
        
        try {
          // Try to use AI to generate title and content
          const model = genAI.getGenerativeModel({ model: MODEL_NAME });

          const prompt = `
          Create a task from this action item: "${item}"

          Context: ${noteTitle ? `This comes from a note titled "${noteTitle}".` : 'No additional context available.'}

          Return ONLY a JSON object with:
          1. "title": A clear, concise title (STRICTLY MAX 50 CHARS, shorter is better)
          2. "content": HTML content (not Markdown) with the full action item and structured steps if possible

          IMPORTANT: Return ONLY the raw JSON object without any markdown code blocks, backticks, or formatting.
          Format: { "title": "...", "content": "..." }
          `;

          const result = await model.generateContent(prompt);
          const response = await result.response;
          const text = response.text();

          // Parse the JSON response
          try {
            // Clean the response text to handle markdown code blocks
            let cleanedText = text;
            
            // Remove markdown code blocks if present
            if (cleanedText.includes('```')) {
              cleanedText = cleanedText.replace(/```json\s*|\s*```/g, '');
            }
            
            // Try to find JSON object in the text
            const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              cleanedText = jsonMatch[0];
            }
            
            const parsedResponse = JSON.parse(cleanedText);
            title = parsedResponse.title || '';
            
            // Ensure title is no more than 50 characters
            if (title.length > 50) {
              title = title.substring(0, 47) + '...';
            }
            
            // Ensure content is HTML, not Markdown
            content = parsedResponse.content || '';
            if (!content.includes('<')) {
              // If content doesn't have HTML tags, wrap it in paragraph tags
              content = `<p>${content}</p>`;
            }
          } catch (parseError) {
            console.error('Error parsing AI response:', parseError, 'Raw text:', text);
            throw new Error('Failed to parse AI response');
          }
        } catch (aiError) {
          console.error('Error generating task with AI:', aiError);
          
          // Fallback to manual generation if AI fails
          title = item.length > 50 ? item.substring(0, 47) + '...' : item;

          // Ensure content is HTML for TipTap
          content = `<p>${item}</p>`;

          if (item.includes('\n') || item.includes(';')) {
            const steps = item.split(/[\n;]/).filter((step: string) => step.trim());
            if (steps.length > 1) {
              content += '<p>Steps:</p><ul>';
              steps.forEach((step: string) => {
                if (step.trim()) {
                  content += `<li>${step.trim()}</li>`;
                }
              });
              content += '</ul>';
            }
          }
        }
        
        // Always add the source note reference
        if (noteTitle) {
          content += `<p><em>Source note: "${noteTitle}"</em></p>`;
        }
        
        // Create the task
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .insert({
            title: title,
            content: content,
            importance: "medium",
            status: 'not started',
            household_id: numericHouseholdId,
            due_date: tomorrow.toISOString(),
            user_id: user.id,
            org_id: orgId,
            assigned_to: user.id, // Always assign to the user who created it
            metadata: { actionItem: item }
          })
          .select('id')
          .single();
        
        if (taskError) {
          console.error('Error creating task:', taskError);
        } else if (taskData) {
          console.log(`Successfully created task with ID: ${taskData.id}`);
          taskIds.push(taskData.id);
        }
      }
      catch (error) {
        console.error('Error processing action item:', error);
      }
    }

    // If we have a noteId, create links between the tasks and the note
    if (noteId && taskIds.length > 0) {
      console.log(`Creating links for ${taskIds.length} tasks to note ${noteId}`);
      
      const taskNoteLinks = taskIds.map(taskId => ({
        task_id: taskId,
        note_id: noteId
      }));
      
      try {
        const { error: linkError } = await supabase
          .from('task_note_links')
          .insert(taskNoteLinks);
        
        if (linkError) {
          console.error('Error creating task-note links:', linkError);
        } else {
          console.log(`Successfully created ${taskNoteLinks.length} task-note links`);
        }
      } catch (linkException) {
        console.error('Exception creating task-note links:', linkException);
      }
    }

    return NextResponse.json({
      success: true,
      createdTasks: taskIds.length,
      taskIds: taskIds
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
