import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create a Supabase admin client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createSupabaseClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(request: NextRequest) {
  try {
    // Get the regular client for authentication
    const supabase = createClient();
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    // Parse the request body
    const body = await request.json();
    const { logoPath } = body;
    
    if (!logoPath) {
      return NextResponse.json(
        { error: 'Logo path is required' },
        { status: 400 }
      );
    }
    
    console.log(`Fetching signed URL for logo: ${logoPath}`);
    
    // Use the admin client to create a signed URL (bypassing RLS)
    const { data: urlData, error: urlError } = await supabaseAdmin.storage
      .from('media')
      .createSignedUrl(logoPath, 3600); // 1 hour expiry
    
    if (urlError) {
      console.error('Error creating signed URL:', urlError);
      return NextResponse.json(
        { error: urlError.message },
        { status: 500 }
      );
    }
    
    if (!urlData?.signedUrl) {
      console.error('No signed URL returned from Supabase');
      return NextResponse.json(
        { error: 'Failed to generate signed URL' },
        { status: 500 }
      );
    }
    
    console.log(`Successfully generated signed URL for ${logoPath}`);
    
    return NextResponse.json({
      success: true,
      signedUrl: urlData.signedUrl
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
