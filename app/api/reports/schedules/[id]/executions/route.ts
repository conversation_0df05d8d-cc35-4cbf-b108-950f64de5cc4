import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET /api/reports/schedules/[id]/executions
 * Get execution history for a schedule
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the execution history with specific column selection
    const { data, error } = await supabase
      .from('report_executions')
      .select('id, schedule_id, report_id, status, started_at, completed_at, duration, record_count, error_message, result_id, delivery_status')
      .eq('schedule_id', params.id)
      .order('started_at', { ascending: false });

    if (error) {
      console.error('Error fetching execution history:', error);
      return NextResponse.json(
        { error: 'Failed to fetch execution history' },
        { status: 500 }
      );
    }

    // Transform the data to match our interface
    const executions = data.map(item => ({
      id: item.id,
      scheduleId: item.schedule_id,
      reportId: item.report_id,
      startedAt: item.started_at,
      completedAt: item.completed_at,
      status: item.status,
      error: item.error_message,
      resultId: item.result_id,
      deliveryStatus: item.delivery_status
    }));

    return NextResponse.json({ executions });
  } catch (error) {
    console.error(`Error in GET /api/reports/schedules/${params.id}/executions:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
