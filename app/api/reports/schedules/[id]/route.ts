import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { calculateNextRunDate } from '@/app/protected/admin/reporting/lib/scheduling';

/**
 * GET /api/reports/schedules/[id]
 * Get a specific report schedule
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the schedule
    let data: any;
    try {
      const { data: scheduleData, error } = await supabase
        .from('report_schedules')
        .select('*, reports(name, description, type, category)')
        .eq('id', params.id)
        .single();

      if (error) {
        throw error;
      }

      data = scheduleData;
    } catch (error) {
      console.error('Error with joined query, falling back to basic query:', error);

      // If the join fails, fall back to just getting the schedule with specific columns
      const { data: scheduleData, error: fallbackError } = await supabase
        .from('report_schedules')
        .select('id, report_id, name, description, frequency, parameters, next_run, last_run, is_active, created_at, updated_at')
        .eq('id', params.id)
        .single();

      if (fallbackError) {
        console.error('Error fetching schedule:', fallbackError);
        return NextResponse.json(
          { error: 'Failed to fetch schedule' },
          { status: 500 }
        );
      }

      data = scheduleData;

      // Get report details separately if needed
      try {
        const { data: reportData } = await supabase
          .from('reports')
          .select('name, description, type, category')
          .eq('id', data.report_id)
          .maybeSingle();

        if (reportData) {
          data.reports = reportData;
        }
      } catch (reportError) {
        console.error('Error fetching report details:', reportError);
      }
    }

    // Transform the data to match our interface
    const schedule = {
      id: data.id,
      reportId: data.report_id,
      reportName: data.reports?.name || 'Unknown Report',
      reportType: data.reports?.type || 'unknown',
      reportCategory: data.reports?.category || 'unknown',
      name: data.name,
      frequency: data.frequency,
      dayOfWeek: data.day_of_week,
      dayOfMonth: data.day_of_month,
      month: data.month,
      time: data.time,
      nextRunAt: data.next_run_at,
      lastRunAt: data.last_run_at,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      enabled: data.enabled,
      delivery: data.delivery,
      userId: data.user_id,
      parameters: data.parameters
    };

    return NextResponse.json({ schedule });
  } catch (error) {
    console.error(`Error in GET /api/reports/schedules/${params.id}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/reports/schedules/[id]
 * Update a report schedule
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Get the current schedule to fill in missing parameters with specific columns
    const { data: currentSchedule, error: fetchError } = await supabase
      .from('report_schedules')
      .select('id, report_id, name, description, frequency, parameters, next_run, last_run, is_active, created_at, updated_at, day_of_week, day_of_month, time, month')
      .eq('id', params.id)
      .single();

    if (fetchError) {
      console.error('Error fetching current schedule:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch current schedule' },
        { status: 500 }
      );
    }

    // Calculate next run date if frequency or time parameters are updated
    let nextRunAt = currentSchedule.next_run;
    if (
      body.frequency ||
      body.dayOfWeek !== undefined ||
      body.dayOfMonth !== undefined ||
      body.month !== undefined ||
      body.time
    ) {
      const frequency = body.frequency || currentSchedule.frequency;
      const dayOfWeek = body.dayOfWeek !== undefined ? body.dayOfWeek : currentSchedule.day_of_week || 1;
      const dayOfMonth = body.dayOfMonth !== undefined ? body.dayOfMonth : currentSchedule.day_of_month || 1;
      const month = body.month !== undefined ? body.month : currentSchedule.month || 0;
      const time = body.time || currentSchedule.time;

      nextRunAt = calculateNextRunDate(frequency, dayOfWeek, dayOfMonth, month, time).toISOString();
    }

    // Prepare the update object
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (body.name) updateData.name = body.name;
    if (body.frequency) updateData.frequency = body.frequency;
    if (body.dayOfWeek !== undefined) updateData.day_of_week = body.frequency === 'weekly' ? body.dayOfWeek : null;
    if (body.dayOfMonth !== undefined) updateData.day_of_month = ['monthly', 'quarterly', 'yearly'].includes(body.frequency || currentSchedule.frequency) ? body.dayOfMonth : null;
    if (body.month !== undefined) updateData.month = ['quarterly', 'yearly'].includes(body.frequency || currentSchedule.frequency) ? body.month : null;
    if (body.time) updateData.time = body.time;
    if (nextRunAt !== currentSchedule.next_run) updateData.next_run = nextRunAt;
    if (body.enabled !== undefined) updateData.enabled = body.enabled;
    if (body.delivery) updateData.delivery = body.delivery;
    if (body.parameters) updateData.parameters = body.parameters;

    // Update the schedule
    const { data, error } = await supabase
      .from('report_schedules')
      .update(updateData)
      .eq('id', params.id)
      .select('id')
      .single();

    if (error) {
      console.error('Error updating schedule:', error);
      return NextResponse.json(
        { error: 'Failed to update schedule' },
        { status: 500 }
      );
    }

    return NextResponse.json({ id: data.id });
  } catch (error) {
    console.error(`Error in PUT /api/reports/schedules/${params.id}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reports/schedules/[id]
 * Delete a report schedule
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Delete the schedule
    const { error } = await supabase
      .from('report_schedules')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting schedule:', error);
      return NextResponse.json(
        { error: 'Failed to delete schedule' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error in DELETE /api/reports/schedules/${params.id}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
