import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { fetchReportData } from '@/app/protected/admin/reporting/lib/data-fetching';
import { getReportById } from '@/app/protected/admin/reporting/lib/report-management';
import { calculateNextRunDate } from '@/app/protected/admin/reporting/lib/scheduling';

/**
 * POST /api/reports/schedules/[id]/run
 * Manually run a scheduled report
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the schedule
    const { data: schedule, error: scheduleError } = await supabase
      .from('report_schedules')
      .select('*, reports(name, type, category)')
      .eq('id', params.id)
      .single();

    if (scheduleError) {
      console.error('Error fetching schedule:', scheduleError);
      return NextResponse.json(
        { error: 'Failed to fetch schedule' },
        { status: 500 }
      );
    }

    // Create an execution record
    const { data: execution, error: executionError } = await supabase
      .from('report_executions')
      .insert({
        schedule_id: params.id,
        report_id: String(schedule.report_id), // Ensure report_id is a string
        status: 'running'
      })
      .select('id')
      .single();

    if (executionError) {
      console.error('Error creating execution record:', executionError);
      return NextResponse.json(
        { error: 'Failed to create execution record' },
        { status: 500 }
      );
    }

    try {
      // Get the report configuration
      const reportConfig = await getReportById(schedule.report_id);

      // Run the report
      const reportData = await fetchReportData(
        reportConfig.type,
        schedule.parameters || {}
      );

      // Save the report result
      const { data: result, error: resultError } = await supabase
        .from('report_results')
        .insert({
          report_id: schedule.report_id,
          parameters: schedule.parameters,
          data: reportData.data,
          summary: reportData.summary,
          chart_data: reportData.chartData,
          user_id: user.id
        })
        .select('id')
        .single();

      if (resultError) {
        throw new Error(`Error saving report result: ${resultError.message}`);
      }

      // Update the execution record
      const { error: updateError } = await supabase
        .from('report_executions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          result_id: result.id
        })
        .eq('id', execution.id);

      if (updateError) {
        throw new Error(`Error updating execution record: ${updateError.message}`);
      }

      // Update the schedule with the last run time and next run time
      const now = new Date();
      const nextRunAt = calculateNextRunDate(
        schedule.frequency,
        schedule.day_of_week || 1,
        schedule.day_of_month || 1,
        schedule.month || 0,
        schedule.time
      );

      const { error: scheduleUpdateError } = await supabase
        .from('report_schedules')
        .update({
          last_run_at: now.toISOString(),
          next_run_at: nextRunAt.toISOString()
        })
        .eq('id', params.id);

      if (scheduleUpdateError) {
        throw new Error(`Error updating schedule: ${scheduleUpdateError.message}`);
      }

      // Handle delivery if specified in the request
      const requestBody = await request.json().catch(() => ({}));
      if (requestBody.deliver) {
        // In a real implementation, this would call the delivery functions
        // For now, we'll just update the execution record with a delivery status
        const { error: deliveryError } = await supabase
          .from('report_executions')
          .update({
            delivery_status: {
              method: schedule.delivery.method,
              status: 'sent',
              sentAt: new Date().toISOString()
            }
          })
          .eq('id', execution.id);

        if (deliveryError) {
          throw new Error(`Error updating delivery status: ${deliveryError.message}`);
        }
      }

      return NextResponse.json({
        success: true,
        executionId: execution.id,
        resultId: result.id
      });
    } catch (error) {
      // Update the execution record with the error
      await supabase
        .from('report_executions')
        .update({
          status: 'failed',
          completed_at: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        })
        .eq('id', execution.id);

      throw error;
    }
  } catch (error) {
    console.error(`Error in POST /api/reports/schedules/${params.id}/run:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
