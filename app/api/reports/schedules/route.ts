import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { calculateNextRunDate } from '@/app/protected/admin/reporting/lib/scheduling';

/**
 * GET /api/reports/schedules
 * Get all report schedules for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all schedules for the user
    let data;
    try {
      const { data: schedulesData, error } = await supabase
        .from('report_schedules')
        .select('*, reports(name, description, type, category)')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      data = schedulesData;
    } catch (error) {
      console.error('Error with joined query, falling back to basic query:', error);

      // If the join fails, fall back to just getting the schedules with specific columns
      const { data: schedulesData, error: fallbackError } = await supabase
        .from('report_schedules')
        .select('id, report_id, name, description, frequency, parameters, next_run, last_run, is_active, created_at, updated_at')
        .order('created_at', { ascending: false });

      if (fallbackError) {
        console.error('Error fetching schedules:', fallbackError);
        return NextResponse.json(
          { error: 'Failed to fetch schedules' },
          { status: 500 }
        );
      }

      data = schedulesData;
    }

    // Transform the data to match our interface
    const schedules = data.map(item => ({
      id: item.id,
      reportId: item.report_id,
      reportName: item.reports?.name || 'Unknown Report',
      reportType: item.reports?.type || 'unknown',
      reportCategory: item.reports?.category || 'unknown',
      name: item.name,
      frequency: item.frequency,
      dayOfWeek: item.day_of_week,
      dayOfMonth: item.day_of_month,
      month: item.month,
      time: item.time,
      nextRunAt: item.next_run_at,
      lastRunAt: item.last_run_at,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
      enabled: item.enabled,
      delivery: item.delivery,
      userId: item.user_id,
      parameters: item.parameters
    }));

    return NextResponse.json({ schedules });
  } catch (error) {
    console.error('Error in GET /api/reports/schedules:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports/schedules
 * Create a new report schedule
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.reportId || !body.name || !body.frequency || !body.time || !body.delivery?.method) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Calculate next run date
    const nextRunAt = calculateNextRunDate(
      body.frequency,
      body.dayOfWeek || 1,
      body.dayOfMonth || 1,
      body.month || 0,
      body.time
    );

    // Create the schedule
    const { data, error } = await supabase
      .from('report_schedules')
      .insert({
        report_id: String(body.reportId), // Ensure reportId is a string
        name: body.name,
        frequency: body.frequency,
        day_of_week: body.frequency === 'weekly' ? body.dayOfWeek : null,
        day_of_month: ['monthly', 'quarterly', 'yearly'].includes(body.frequency) ? body.dayOfMonth : null,
        month: ['quarterly', 'yearly'].includes(body.frequency) ? body.month : null,
        time: body.time,
        next_run_at: nextRunAt.toISOString(),
        enabled: body.enabled !== false, // Default to true if not specified
        delivery: body.delivery,
        user_id: user.id,
        parameters: body.parameters || {}
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating schedule:', error);
      return NextResponse.json(
        { error: 'Failed to create schedule' },
        { status: 500 }
      );
    }

    return NextResponse.json({ id: data.id });
  } catch (error) {
    console.error('Error in POST /api/reports/schedules:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
