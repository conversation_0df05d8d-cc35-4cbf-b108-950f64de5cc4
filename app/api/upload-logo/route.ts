import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create a Supabase admin client with the service role key
// This client bypasses RLS policies and has admin privileges
const supabaseAdmin = createSupabaseClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Route segment config
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    // Use regular client for auth
    const supabase = createClient();

    // Get user data
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the form data with the file
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml'].includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPG, PNG, and SVG files are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 5MB' },
        { status: 400 }
      );
    }

    // Generate a unique filename
    const timestamp = Date.now();
    const safeFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `${timestamp}-${safeFileName}`;

    // Use a consistent path format for organization logos
    const filePath = `organizations/${user.id}/${fileName}`;
    let uploadError: any = null;

    console.log(`Attempting to upload logo to path: ${filePath}`);

    try {
      // Use admin client for storage operations to bypass RLS
      const { error } = await supabaseAdmin.storage
        .from('media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true // Use upsert to overwrite existing files
        });

      if (error) {
        uploadError = error;
        console.error(`Upload failed for path ${filePath}:`, error.message);
      } else {
        console.log(`Successfully uploaded logo to ${filePath}`);

        // Get the user's profile to check if it's for an organization
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', user.id)
          .single();

        if (profileError) {
          console.warn('Error fetching profile:', profileError);
        } else if (profile?.org_id) {
          // If user has an organization, update the organization's logo_path
          console.log(`Updating organization ${profile.org_id} with logo path: ${filePath}`);

          // Use admin client to update organization
          const { error: updateOrgError } = await supabaseAdmin
            .from('organizations')
            .update({ logo_path: filePath })
            .eq('id', profile.org_id);

          if (updateOrgError) {
            console.warn('Error updating organization with logo path:', updateOrgError);
          } else {
            console.log('Organization logo path updated successfully');
          }
        } else {
          // If no organization, update the user's profile
          console.log(`Updating user profile with logo path: ${filePath}`);

          const { error: updateError } = await supabaseAdmin
            .from('profiles')
            .update({ logo_path: filePath })
            .eq('user_id', user.id);

          if (updateError) {
            console.warn('Error updating profile with logo path:', updateError);
          } else {
            console.log('Profile logo path updated successfully');
          }
        }
      }
    } catch (err) {
      uploadError = err;
      console.error(`Error uploading to path ${filePath}:`, err);
    }

    if (uploadError) {
      return NextResponse.json(
        { error: `Failed to upload file: ${uploadError.message}` },
        { status: 500 }
      );
    }

    // Generate a signed URL for the uploaded file
    const { data: urlData } = await supabaseAdmin.storage
      .from('media')
      .createSignedUrl(filePath, 3600); // 1 hour expiry

    return NextResponse.json({
      success: true,
      filePath,
      url: urlData?.signedUrl || null,
      message: 'Logo uploaded successfully'
    });

  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
