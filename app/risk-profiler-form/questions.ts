export interface RiskQuestion {
  type: string;
  includeInScore: boolean;
  id: string;
  question: string;
  options: {
    text: string;
    score: number;
  }[];
}

// Define the 10-question questionnaire
export const riskQuestions10Q: RiskQuestion[] = [
  {
    id: 'risk_willingness_comparison',
    question: 'Compared to others, how do you rate your willingness to take financial risks?',
    options: [
      { text: 'Extremely low risk taker', score: 1 },
      { text: 'Very low risk taker', score: 2 },
      { text: 'Low risk taker', score: 3 },
      { text: 'Average risk taker', score: 4 },
      { text: 'High risk taker', score: 5 },
      { text: 'Very high risk taker', score: 6 },
      { text: 'Extremely high risk taker', score: 7 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'financial_adaptability',
    question: 'How easily do you adapt when things go wrong financially?',
    options: [
      { text: 'Very uneasily', score: 1 },
      { text: 'Somewhat uneasily', score: 2 },
      { text: 'Somewhat easily', score: 3 },
      { text: 'Very easily', score: 4 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'risk_word_association',
    question: 'When you think of the word "risk" in a financial context, which of the following words comes to mind first?',
    options: [
      { text: 'Danger', score: 1 },
      { text: 'Uncertainty', score: 2 },
      { text: 'Opportunity', score: 3 },
      { text: 'Thrill', score: 4 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'loss_gain_perspective',
    question: 'When faced with a major financial decision, are you more concerned about the possible losses or the possible gains?',
    options: [
      { text: 'Always the possible losses', score: 1 },
      { text: 'Usually the possible losses', score: 2 },
      { text: 'Usually the possible gains', score: 3 },
      { text: 'Always the possible gains', score: 4 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'current_risk_appetite',
    question: 'What degree of risk are you currently prepared to take with your financial decisions?',
    options: [
      { text: 'Very small', score: 1 },
      { text: 'Small', score: 2 },
      { text: 'Medium', score: 3 },
      { text: 'Large', score: 4 },
      { text: 'Very large', score: 5 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'investment_loss_reaction',
    question: 'Suppose that 5 years ago you bought shares in a highly regarded company. That same year the company experienced a severe decline in sales due to poor management. The price of the shares dropped drastically and you sold at a substantial loss. Thinking back, how would you describe your reaction at the time?',
    options: [
      { text: 'Very upset and worried', score: 1 },
      { text: 'Somewhat upset and worried', score: 2 },
      { text: 'Slightly disappointed but accepted it as part of investing', score: 3 },
      { text: 'Not at all concerned', score: 4 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'value_stability_vs_inflation',
    question: 'With some types of investment, such as cash and term deposits, the value of the investment is fixed. However, inflation will cause the purchasing power of this value to decrease. With other types of investment, the value will vary over time, but over the long term, their value is expected to increase by more than the rate of inflation. Thinking about your investments, which of the following statements best describes how you feel?',
    options: [
      { text: 'It is much more important that the value of my investments does not fall than that it keeps pace with inflation', score: 1 },
      { text: 'It is somewhat more important that the value of my investments does not fall than that it keeps pace with inflation', score: 2 },
      { text: 'It is equally important that the value of my investments does not fall and that it keeps pace with inflation', score: 3 },
      { text: 'It is somewhat more important that the value of my investments keeps pace with inflation than that it does not fall', score: 4 },
      { text: 'It is much more important that the value of my investments keeps pace with inflation than that it does not fall', score: 5 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'portfolio_preference',
    question: 'Which mix of investments do you find most appealing? Would you prefer all low-risk/low-return, all high-risk/high-return, or somewhere in between?',
    options: [
      { text: 'Portfolio 1 (100% low risk/return, 0% medium, 0% high)', score: 1 },
      { text: 'Portfolio 2 (70% low, 30% medium, 0% high)', score: 2 },
      { text: 'Portfolio 3 (50% low, 40% medium, 10% high)', score: 3 },
      { text: 'Portfolio 4 (30% low, 40% medium, 30% high)', score: 4 },
      { text: 'Portfolio 5 (10% low, 40% medium, 50% high)', score: 5 },
      { text: 'Portfolio 6 (0% low, 30% medium, 70% high)', score: 6 },
      { text: 'Portfolio 7 (0% low, 0% medium, 100% high)', score: 7 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'market_downturn_reaction',
    question: 'Imagine that you have a portfolio of investments that has fallen in value by 10% over the last three months. Assuming that there has been no specific news about the types of investments you hold, what would be your most likely reaction?',
    options: [
      { text: 'Sell all of the investments', score: 1 },
      { text: 'Sell some of the investments', score: 2 },
      { text: 'Sit tight and do nothing', score: 3 },
      { text: 'Buy more of the investments', score: 4 }
    ],
    type: "",
    includeInScore: false
  },
  {
    id: 'return_expectations',
    question: 'Think of the average rate of return you would expect to earn on an investment portfolio over the next ten years. How does this compare with what you think you would earn if you invested the money in term deposits?',
    options: [
      { text: 'About the same rate as from term deposits', score: 1 },
      { text: 'About one and a half times the rate from term deposits', score: 2 },
      { text: 'About twice the rate from term deposits', score: 3 },
      { text: 'About two and a half times the rate from term deposits', score: 4 },
      { text: 'At least three times the rate from term deposits', score: 5 }
    ],
    type: "",
    includeInScore: false
  }
];



export const riskQuestions25Q: RiskQuestion[] = [
{
  id: 'risk_willingness',
  question: 'Compared to others, how do you rate your willingness to take financial risks?',
  options: [
    { text: 'Extremely low risk taker', score: 1 },
    { text: 'Very low risk taker', score: 2 },
    { text: 'Low risk taker', score: 3 },
    { text: 'Average risk taker', score: 4 },
    { text: 'High risk taker', score: 5 },
    { text: 'Very high risk taker', score: 6 },
    { text: 'Extremely high risk taker', score: 7 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'adaptability',
  question: 'How easily do you adapt when things go wrong financially?',
  options: [
    { text: 'Very uneasily', score: 1 },
    { text: 'Somewhat uneasily', score: 2 },
    { text: 'Somewhat easily', score: 3 },
    { text: 'Very easily', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'risk_association',
  question: 'When you think of the word "risk" in a financial context, which of the following words comes to mind first?',
  options: [
    { text: 'Danger', score: 1 },
    { text: 'Uncertainty', score: 2 },
    { text: 'Opportunity', score: 3 },
    { text: 'Thrill', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'thrill_investing',
  question: 'Have you ever invested a large sum in a risky investment mainly for the "thrill" of seeing whether it went up or down in value?',
  options: [
    { text: 'No', score: 1 },
    { text: 'Yes, very rarely', score: 2 },
    { text: 'Yes, somewhat rarely', score: 3 },
    { text: 'Yes, somewhat frequently', score: 4 },
    { text: 'Yes, very frequently', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'job_security_tradeoff',
  question: 'If you had to choose between more job security with a small pay increase and less job security with a big pay increase, which would you pick?',
  options: [
    { text: 'Definitely more job security with a small pay increase', score: 1 },
    { text: 'Probably more job security with a small pay increase', score: 2 },
    { text: 'Not sure', score: 3 },
    { text: 'Probably less job security with a big pay increase', score: 4 },
    { text: 'Definitely less job security with a big pay increase', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'loss_gain_focus',
  question: 'When faced with a major financial decision, are you more concerned about the possible losses or the possible gains?',
  options: [
    { text: 'Always the possible losses', score: 1 },
    { text: 'Usually the possible losses', score: 2 },
    { text: 'Usually the possible gains', score: 3 },
    { text: 'Always the possible gains', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'decision_feelings',
  question: 'How do you usually feel about your major financial decisions after you make them?',
  options: [
    { text: 'Very pessimistic', score: 1 },
    { text: 'Somewhat pessimistic', score: 2 },
    { text: 'Somewhat optimistic', score: 3 },
    { text: 'Very optimistic', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'compensation_preference',
  question: 'Imagine you were in a job where you could choose whether to be paid salary, commission or a mix of both. Which would you pick?',
  options: [
    { text: 'All salary', score: 1 },
    { text: 'Mainly salary', score: 2 },
    { text: 'Equal mix of salary and commission', score: 3 },
    { text: 'Mainly commission', score: 4 },
    { text: 'All commission', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'past_risk_taking',
  question: 'What degree of risk have you taken with your financial decisions in the past?',
  options: [
    { text: 'Very small', score: 1 },
    { text: 'Small', score: 2 },
    { text: 'Medium', score: 3 },
    { text: 'Large', score: 4 },
    { text: 'Very Large', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'current_risk_appetite',
  question: 'What degree of risk are you currently prepared to take with your financial decisions?',
  options: [
    { text: 'Very small', score: 1 },
    { text: 'Small', score: 2 },
    { text: 'Medium', score: 3 },
    { text: 'Large', score: 4 },
    { text: 'Very large', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'borrowing_for_investment',
  question: 'You have an opportunity to make an investment that appears to be almost certain to produce a sizeable return. However, you have no funds to put towards this investment. One option is to borrow money for this purpose. How likely is it that you would do this?',
  options: [
    { text: 'Very unlikely', score: 1 },
    { text: 'Somewhat unlikely', score: 2 },
    { text: 'Somewhat likely', score: 3 },
    { text: 'Very likely', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'financial_confidence',
  question: 'How much confidence do you have in your ability to make good financial decisions?',
  options: [
    { text: 'None', score: 1 },
    { text: 'A little', score: 2 },
    { text: 'A reasonable amount', score: 3 },
    { text: 'A great deal', score: 4 },
    { text: 'Complete', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'bad_experience_reinvestment',
  question: 'Suppose that 5 years ago you bought shares in a highly regarded company. That same year the company experienced a severe decline in sales due to poor management. The price of the shares dropped drastically and you sold at a substantial loss. The company has been restructured under new management and most experts now expect it to produce better than average returns. Given your bad past experience with this company, would you buy shares now?',
  options: [
    { text: 'Definitely not', score: 1 },
    { text: 'Probably not', score: 2 },
    { text: 'Not sure', score: 3 },
    { text: 'Probably', score: 4 },
    { text: 'Definitely', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'tolerance_for_loss',
  question: 'Investments can go up or down in value and experts often say you should be prepared to weather a downturn. By how much could the total value of all your investments go down before you would begin to feel uncomfortable?',
  options: [
    { text: 'Any fall in value would make me feel uncomfortable', score: 1 },
    { text: '10%', score: 2 },
    { text: '20%', score: 3 },
    { text: '33%', score: 4 },
    { text: '50%', score: 5 },
    { text: 'More than 50%', score: 6 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'inherited_property_decision',
  question: 'Assume that a long-lost relative dies and leaves you a house which is in poor condition but is located in a suburb that\'s becoming popular. As is, the house would probably sell for $300,000, but if you were to spend about $100,000 on renovations, the selling price would be around $600,000. However, there is some talk of constructing a major highway next to the house, and this would lower its value considerably. Which of the following options would you take?',
  options: [
    { text: 'Sell it as is', score: 1 },
    { text: 'Keep it as is, but rent it out', score: 2 },
    { text: 'Take out a $100,000 mortgage and do the renovations', score: 3 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'portfolio_preference',
  question: 'Most investment portfolios have a mix of investments - some may have high expected returns with high risk, some medium expected returns with medium risk, and some low-risk/low-return. Which mix of investments do you find most appealing?',
  options: [
    { text: '100% low-risk/low-return', score: 1 },
    { text: '80% low-risk, 20% medium-risk', score: 2 },
    { text: '60% low-risk, 30% medium-risk, 10% high-risk', score: 3 },
    { text: '40% low-risk, 40% medium-risk, 20% high-risk', score: 4 },
    { text: '20% low-risk, 50% medium-risk, 30% high-risk', score: 5 },
    { text: '10% low-risk, 40% medium-risk, 50% high-risk', score: 6 },
    { text: '100% high-risk/high-return', score: 7 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'loss_tolerance_threshold',
  question: 'You are considering placing one-quarter of your investment funds into a single investment. This investment is expected to earn about twice the term deposit rate. However, unlike a term deposit, this investment is not protected against loss of the money invested. How low would the chance of a loss have to be for you to make the investment?',
  options: [
    { text: 'Zero, i.e. no chance of any loss', score: 1 },
    { text: 'Very low chance of loss', score: 2 },
    { text: 'Moderately low chance of loss', score: 3 },
    { text: '50% chance of loss', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'value_stability_vs_purchasing_power',
  question: 'Which is more important to you - that the value of your investments does not fall or that it retains its purchasing power?',
  options: [
    { text: 'Much more important that the value does not fall', score: 1 },
    { text: 'Somewhat more important that the value does not fall', score: 2 },
    { text: 'Somewhat more important that the value retains its purchasing power', score: 3 },
    { text: 'Much more important that the value retains its purchasing power', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'risk_trend',
  question: 'In recent years, how have your personal investments changed?',
  options: [
    { text: 'Always toward lower risk', score: 1 },
    { text: 'Mostly toward lower risk', score: 2 },
    { text: 'No changes or changes with no clear direction', score: 3 },
    { text: 'Mostly toward higher risk', score: 4 },
    { text: 'Always toward higher risk', score: 5 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'high_risk_allocation',
  question: 'How much of your available investment funds would you be willing to place in investments where both returns and risks are expected to be above average?',
  options: [
    { text: 'None', score: 1 },
    { text: '10%', score: 2 },
    { text: '20%', score: 3 },
    { text: '30%', score: 4 },
    { text: '40%', score: 5 },
    { text: '50%', score: 6 },
    { text: '60%', score: 7 },
    { text: '70%', score: 8 },
    { text: '80%', score: 9 },
    { text: '90%', score: 10 },
    { text: '100%', score: 11 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'return_expectations',
  question: 'Think of the average rate of return you would expect to earn on an investment portfolio over the next ten years. How does this compare with what you think you would earn if you invested the money in term deposits?',
  options: [
    { text: 'About the same rate as from term deposits', score: 1 },
    { text: 'About one and a half times the rate from term deposits', score: 2 },
    { text: 'About twice the rate from term deposits', score: 3 },
    { text: 'About two and a half times the rate from term deposits', score: 4 },
    { text: 'About three times the rate from term deposits', score: 5 },
    { text: 'More than three times the rate from term deposits', score: 6 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'tax_risk_tolerance',
  question: 'Would you take a risk in arranging your financial affairs to qualify for a government benefit or obtain a tax advantage, knowing a change in legislation could leave you worse off?',
  options: [
    { text: 'I would not take a risk if there was any chance I could finish up worse off', score: 1 },
    { text: 'I would take a risk if there was only a small chance I could finish up worse off', score: 2 },
    { text: 'I would take a risk as long as there was more than a 50% chance that I would finish up better off', score: 3 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'loan_preference',
  question: 'When borrowing a large sum with uncertain future interest rates, would you prefer a variable rate or a fixed rate that is 1% higher?',
  options: [
    { text: 'Definitely the variable rate', score: 1 },
    { text: 'Probably the variable rate', score: 2 },
    { text: 'Probably the fixed rate', score: 3 },
    { text: 'Definitely the fixed rate', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'insurance_deductible',
  question: 'What sort of insurance deductibles or excess do you typically take?',
  options: [
    { text: 'Very small deductible/excess – highest cost for insurance', score: 1 },
    { text: 'Small deductible/excess – high cost for insurance', score: 2 },
    { text: 'Large deductible/excess – low cost for insurance', score: 3 },
    { text: 'Very large deductible/excess – lowest cost for insurance', score: 4 }
  ],
  type: "",
  includeInScore: false
},
{
  id: 'score_prediction',
  question: 'This questionnaire is scored on a scale of 0 to 100. When the scores are graphed they follow the familiar bell-curve of the Normal distribution shown below. The average score is 50. Two-thirds of all scores are within 10 points of the average. Only 1 in 1000 is less than 20 or more than 80. What do you think your score will be?',
  options: [
    { text: 'Below 20', score: 1 },
    { text: '20-30', score: 2 },
    { text: '30-40', score: 3 },
    { text: '40-50', score: 4 },
    { text: '50-60', score: 5 },
    { text: '60-70', score: 6 },
    { text: '70-80', score: 7 },
    { text: 'Above 80', score: 8 }
  ],
  type: "",
  includeInScore: false
}
];

export function calculateNormalizedRiskScore(responses: Record<string, string>, profilerType: '10q' | '25q'): number {
  const questions = profilerType === '10q' ? riskQuestions10Q : riskQuestions25Q;
  
  let totalScore = 0;
  let maxPossibleScore = 0;
  
  questions.forEach(question => {
    const selectedOption = question.options.find(
      opt => opt.text === responses[question.id]
    );
    
    if (selectedOption) {
      totalScore += selectedOption.score;
    }
    
    // Calculate the maximum possible score for this question
    const maxQuestionScore = Math.max(...question.options.map(opt => opt.score));
    maxPossibleScore += maxQuestionScore;
  });
  
  // Normalize the score to a 0-100 scale and round to an integer
  return Math.round((totalScore / maxPossibleScore) * 100);
}
