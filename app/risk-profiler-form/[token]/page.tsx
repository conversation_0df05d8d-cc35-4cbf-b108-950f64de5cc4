'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { riskQuestions10Q, riskQuestions25Q } from '../questions';
import { Lock } from 'lucide-react';
import * as bcrypt from 'bcryptjs';
import FormLogo from '@/components/FormLogo';

interface RiskQuestion {
  id: string;
  question: string;
  type?: 'radio' | 'checkbox' | 'textarea';
  options: {
    text: string;
    score: number;
  }[];
  includeInScore?: boolean;
}

interface DemographicQuestion {
  id: string;
  question: string;
  options?: {
    text: string;
    value: string;
  }[];
  type: 'select' | 'text' | 'number';
}



export default function RiskProfilerForm() {
  const params = useParams();

  // Authentication and password protection states
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [passwordVerified, setPasswordVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // Form data states - only used after password verification
  const [isValid, setIsValid] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [responses, setResponses] = useState<Record<string, string | string[]>>({});
  const [demographicResponses, setDemographicResponses] = useState<Record<string, string>>({});
  const [isReadOnly, setIsReadOnly] = useState(false);
  const [profilerType, setProfilerType] = useState<'10q' | '25q'>('10q');
  const [customQuestions, setCustomQuestions] = useState<RiskQuestion[] | null>(null);
  const [memberName, setMemberName] = useState<string | null>(null);

  // Get the appropriate questions based on profiler type or custom template
  const riskQuestions = customQuestions || (profilerType === '10q' ? riskQuestions10Q : riskQuestions25Q);

  // First useEffect - only check for password protection
  useEffect(() => {
    const checkPasswordProtection = async () => {
      setInitialLoading(true);
      const supabase = createClient();

      // Check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      setIsLoggedIn(!!user);

      // Only check for password protection initially
      const { data, error } = await supabase
        .from('risk_profiler_tokens')
        .select('password_hash, status')
        .eq('token', params.token)
        .single();

      if (error) {
        setIsValid(false);
        setInitialLoading(false);
        return;
      }

      // Check if the form is password protected
      if (data.password_hash) {
        setIsPasswordProtected(true);
        // If user is logged in, bypass password protection
        if (user) {
          setPasswordVerified(true);
        }
      } else {
        // No password protection, consider it verified
        setPasswordVerified(true);
      }

      setInitialLoading(false);
    };

    checkPasswordProtection();
  }, [params.token]);

  // State for adviser's organization ID
  const [adviserOrgId, setAdviserOrgId] = useState<string | null>(null);

  // Second useEffect - only runs after password verification
  useEffect(() => {
    // Only fetch form data if password is verified or user is logged in
    if (!passwordVerified && !isLoggedIn || !isValid) return;

    const fetchRiskProfilerData = async () => {
      const supabase = createClient();

      const { data, error } = await supabase
        .from('risk_profiler_tokens')
        .select('*, households:household_id(householdName, members), created_by, template_id')
        .eq('token', params.token)
        .single();

      if (error || !data) {
        setIsValid(false);
        return;
      }

      // Set the profiler type from the token data
      if (data.profiler_type) {
        setProfilerType(data.profiler_type);
      }

      // Check if a custom template is specified
      if (data.template_id) {
        try {
          // Fetch the template questions
          const { data: templateQuestions, error: templateError } = await supabase
            .from('risk_profiler_template_questions')
            .select('*')
            .eq('template_id', data.template_id)
            .order('sort_order', { ascending: true });

          if (!templateError && templateQuestions && templateQuestions.length > 0) {
            // Convert the template questions to the expected format
            const formattedQuestions: RiskQuestion[] = templateQuestions.map(q => {
              // Parse options from JSON string if needed
              let parsedOptions = q.options;

              // Check if options is a string (JSON) and parse it
              if (typeof parsedOptions === 'string') {
                try {
                  parsedOptions = JSON.parse(parsedOptions);
                } catch (e) {
                  console.error('Error parsing options JSON:', e);
                  parsedOptions = [];
                }
              }

              // If options is not an array, make it an empty array
              if (!Array.isArray(parsedOptions)) {
                console.warn('Options is not an array for question:', q.question_id);
                parsedOptions = [];
              }

              return {
                id: q.question_id,
                question: q.question,
                type: q.type || 'radio', // Default to radio if not specified
                options: parsedOptions,
                includeInScore: q.include_in_score !== false // Default to true if not specified
              };
            });

            setCustomQuestions(formattedQuestions);
            console.log('Using custom template questions:', formattedQuestions);
          } else {
            console.error('Error fetching template questions or no questions found:', templateError);
            // Fall back to default questions based on profiler_type
          }
        } catch (e) {
          console.error('Error processing template questions:', e);
          // Fall back to default questions based on profiler_type
        }
      }

      // Get member name if member_id is present
      if (data.member_id && data.households?.members) {
        const memberKey = `name${data.member_id}`;
        const memberName = data.households.members[memberKey];
        if (memberName) {
          setMemberName(memberName);
        }
      }

      // If the document is completed, load the responses and set to read-only
      if (data.status === 'completed' && data.responses) {
        setResponses(data.responses.riskResponses || {});
        setDemographicResponses(data.responses.demographicResponses || {});
        setIsReadOnly(true);
        setIsCompleted(true);
      }

      // Get the creator's organization ID
      if (data.created_by) {
        const { data: creatorProfile, error: creatorError } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', data.created_by)
          .single();

        if (!creatorError && creatorProfile) {
          setAdviserOrgId(creatorProfile.org_id);
        }
      }
    };

    fetchRiskProfilerData();
  }, [params.token, passwordVerified, isLoggedIn, isValid]);

  const handleSubmit = async () => {
    // Check if all risk questions are answered
    if (Object.keys(responses).length < riskQuestions.length) {
      toast({
        title: "Error",
        description: "Please answer all risk assessment questions before submitting.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    const supabase = createClient();

    try {
      // Calculate risk score
      let totalScore = 0;
      let maxPossibleScore = 0;

      // Use the current questions (either custom or default)
      const currentQuestions = customQuestions || (profilerType === '10q' ? riskQuestions10Q : riskQuestions25Q);

      currentQuestions.forEach(question => {
        // Skip questions that should not be included in the score calculation
        if (question.type === 'textarea' || question.includeInScore === false) {
          console.log('Skipping question not included in score:', question.id);
          return;
        }

        // Skip questions with no options or invalid options
        if (!Array.isArray(question.options) || question.options.length === 0) {
          console.warn('Skipping question with invalid options:', question.id);
          return;
        }

        // Handle checkbox questions (multiple selections)
        if (question.type === 'checkbox') {
          // Get the selected options
          const selectedOptions = Array.isArray(responses[question.id])
            ? (responses[question.id] as string[])
            : [];

          if (selectedOptions.length === 0) {
            // No selections made for this question
            return;
          }

          // Calculate the average score of selected options
          let optionSum = 0;
          let validOptionsCount = 0;

          selectedOptions.forEach(selectedText => {
            const option = question.options.find(opt => opt.text === selectedText);
            if (option) {
              // Ensure score is a number
              const score = typeof option.score === 'number' ? option.score : parseInt(option.score);
              if (!isNaN(score)) {
                optionSum += score;
                validOptionsCount++;
              }
            }
          });

          // Add the average score to the total
          if (validOptionsCount > 0) {
            const averageScore = optionSum / validOptionsCount;
            totalScore += averageScore;
            console.log(`Checkbox question ${question.id} average score:`, averageScore);
          }
        }
        // Handle radio questions (single selection)
        else {
          const selectedOption = question.options.find(
            opt => opt.text === responses[question.id]
          );
          if (selectedOption) {
            totalScore += selectedOption.score;
          }
        }

        // Calculate the maximum possible score for this question
        try {
          const maxQuestionScore = Math.max(...question.options.map(opt => {
            // Ensure score is a number
            const score = typeof opt.score === 'number' ? opt.score : parseInt(opt.score);
            return isNaN(score) ? 0 : score;
          }));
          maxPossibleScore += maxQuestionScore;
        } catch (e) {
          console.error('Error calculating max score for question:', question.id, e);
        }
      });

      // Normalize the score to a 0-100 scale and round to an integer
      const riskScore = Math.round((totalScore / maxPossibleScore) * 100);

      // Combine risk and demographic responses
      const allResponses: {
        riskResponses: Record<string, string | string[]>;
        demographicResponses: Record<string, string>;
        customQuestions?: RiskQuestion[]; // Explicitly allow optional customQuestions
      } = {
        riskResponses: responses,
        demographicResponses: demographicResponses
      };

      // If using custom questions, include them in the response
      if (customQuestions) {
        console.log('Saving custom questions to Supabase:', customQuestions);

        // Make sure each question has the necessary properties
        const enhancedCustomQuestions = customQuestions.map(q => ({
          ...q,
          // Ensure type is set
          type: q.type || 'radio',
          // Ensure includeInScore is set correctly
          includeInScore: q.type === 'textarea' ? false : (q.includeInScore !== false)
        }));

        allResponses.customQuestions = enhancedCustomQuestions;
      }

      // Update token status and save responses
      const { error: tokenError } = await supabase
        .from('risk_profiler_tokens')
        .update({
          status: 'completed',
          responses: allResponses,
          risk_score: riskScore,
          completed_at: new Date().toISOString()
        })
        .eq('token', params.token);

      if (tokenError) throw tokenError;

      // Fetch the token data to get the created_by user ID and household information
      const { data: tokenData, error: fetchError } = await supabase
        .from('risk_profiler_tokens')
        .select('created_by, household_id, member_id')
        .eq('token', params.token)
        .single();

      if (fetchError) {
        console.error('Error fetching token data:', fetchError);
      } else if (tokenData) {
        // Fetch household name
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName, members')
          .eq('id', tokenData.household_id)
          .single();

        let householdName = householdError ? 'Unknown Household' : householdData?.householdName;
        let memberInfo = '';

        // If this is for a specific member, include their name
        if (tokenData.member_id && householdData?.members) {
          const memberKey = `name${tokenData.member_id}`;
          const memberName = householdData.members[memberKey];
          if (memberName) {
            memberInfo = ` for ${memberName}`;
          }
        }

        // Create a notification for the user who created the risk profiler
        await supabase.from('notifications').insert({
          user_id: tokenData.created_by,
          content: `Risk Profile${memberInfo} in ${householdName} has been completed with a score of ${riskScore}`,
          type: 'risk_profiler_submission',
          link: `/protected/households/household/${tokenData.household_id}`,
          created_at: new Date().toISOString()
        });
      }

      setIsCompleted(true);
      toast({
        title: "Success",
        description: "Your risk profile has been submitted successfully.",
      });
    } catch (error) {
      console.error('Error submitting risk profile:', error);
      toast({
        title: "Error",
        description: "Failed to submit your risk profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const verifyPassword = async () => {
    setPasswordError(null);

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('risk_profiler_tokens')
        .select('password_hash')
        .eq('token', params.token)
        .single();

      if (error || !data || !data.password_hash) {
        setPasswordError('Could not verify password. Please try again.');
        return;
      }

      // Compare the entered password with the stored hash
      const isValid = await bcrypt.compare(password, data.password_hash);

      if (isValid) {
        setPasswordVerified(true);
      } else {
        setPasswordError('Incorrect password. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setPasswordError('An error occurred while verifying the password.');
    }
  };

  // Initial loading state - checking if password protection is needed
  if (initialLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto mb-4"></div>
          <p className="text-xl text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  // Show password prompt if the form is password protected and not yet verified
  if (isPasswordProtected && !passwordVerified && !isLoggedIn) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Password Protected Form
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                This form is password protected. Please enter the password to continue.
              </p>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
                {passwordError && (
                  <p className="text-sm text-destructive">{passwordError}</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={verifyPassword} className="w-full">
              Continue
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!isValid) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardHeader>
            <CardTitle className="text-center text-red-500">Invalid or Expired Link</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center">
              This risk profiler link is either invalid or has already been completed.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="min-h-screen bg-background p-4">
        <div className="max-w-2xl mx-auto">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Risk Profile Results</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Below are your submitted risk profile responses.
              </p>
            </CardContent>
          </Card>

          {riskQuestions.map((question, index) => (
            <Card key={question.id} className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">
                  {index + 1}. {question.question}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {question.type === 'textarea' ? (
                  <div>
                    <div className="p-3 border rounded-md bg-gray-50">
                      {responses[question.id] ? (
                        <p className="whitespace-pre-wrap">{responses[question.id]}</p>
                      ) : (
                        <p className="text-gray-400 italic">No response provided</p>
                      )}
                    </div>
                    {question.includeInScore === false && (
                      <p className="text-xs text-muted-foreground mt-2">
                        This response was not included in your risk profile score calculation.
                      </p>
                    )}
                  </div>
                ) : question.type === 'checkbox' ? (
                  <div>
                    <p className="text-sm text-muted-foreground italic mb-2">Selected options:</p>
                    <div className="p-3 border rounded-md bg-gray-50">
                      {Array.isArray(responses[question.id]) && (responses[question.id] as string[]).length > 0 ? (
                        <div className="space-y-2">
                          {Array.isArray(question.options) && question.options.map(option => {
                            const isSelected = Array.isArray(responses[question.id]) &&
                              (responses[question.id] as string[]).includes(option.text);

                            return isSelected ? (
                              <div key={option.text} className="flex items-center space-x-2">
                                <div className="h-4 w-4 border border-primary bg-primary rounded flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3 text-white">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                  </svg>
                                </div>
                                <span className="text-sm">{option.text}</span>
                              </div>
                            ) : null;
                          })}
                        </div>
                      ) : (
                        <p className="text-gray-400 italic">No options selected</p>
                      )}
                    </div>
                    {question.includeInScore === false && (
                      <p className="text-xs text-muted-foreground mt-2">
                        This response was not included in your risk profile score calculation.
                      </p>
                    )}
                  </div>
                ) : (
                  <RadioGroup
                    value={responses[question.id] as string}
                    disabled={true}
                  >
                    {Array.isArray(question.options) ? question.options.map((option) => (
                      <div key={option.text} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.text} id={`${question.id}-${option.text}`} />
                        <Label htmlFor={`${question.id}-${option.text}`}>{option.text}</Label>
                      </div>
                    )) : <div className="text-red-500">No options available for this question</div>}
                    {question.includeInScore === false && (
                      <p className="text-xs text-muted-foreground mt-2">
                        This response was not included in your risk profile score calculation.
                      </p>
                    )}
                  </RadioGroup>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto">
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Risk Profiler Questionnaire</CardTitle>
              {memberName && (
                <CardDescription>
                  For: {memberName}
                </CardDescription>
              )}
            </div>
            <FormLogo orgId={adviserOrgId || undefined} />
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Please answer all questions honestly to help us understand your risk tolerance and investment preferences.
            </p>
          </CardContent>
        </Card>

        {riskQuestions.map((question, index) => (
          <Card key={question.id} className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">
                {index + 1}. {question.question}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {question.type === 'textarea' ? (
                <div>
                  <Textarea
                    placeholder="Enter your response here"
                    value={responses[question.id] || ''}
                    onChange={(e) => {
                      if (!isReadOnly) {
                        setResponses(prev => ({ ...prev, [question.id]: e.target.value }))
                      }
                    }}
                    disabled={isReadOnly}
                    className="min-h-[100px]"
                  />
                  {question.includeInScore === false && (
                    <p className="text-xs text-muted-foreground mt-2">
                      This response will not be included in your risk profile score calculation.
                    </p>
                  )}
                </div>
              ) : question.type === 'checkbox' ? (
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground italic mb-2">Select all that apply:</p>
                  {Array.isArray(question.options) ? question.options.map((option) => {
                    // Check if this option is selected
                    const isChecked = Array.isArray(responses[question.id])
                      ? (responses[question.id] as string[]).includes(option.text)
                      : false;

                    return (
                      <div key={option.text} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${question.id}-${option.text}`}
                          checked={isChecked}
                          onCheckedChange={(checked) => {
                            if (isReadOnly) return;

                            setResponses(prev => {
                              // Get current selections as array
                              const currentSelections = Array.isArray(prev[question.id])
                                ? [...prev[question.id] as string[]]
                                : [];

                              // Add or remove the option based on checked state
                              if (checked) {
                                if (!currentSelections.includes(option.text)) {
                                  currentSelections.push(option.text);
                                }
                              } else {
                                const index = currentSelections.indexOf(option.text);
                                if (index !== -1) {
                                  currentSelections.splice(index, 1);
                                }
                              }

                              return { ...prev, [question.id]: currentSelections };
                            });
                          }}
                          disabled={isReadOnly}
                        />
                        <Label
                          htmlFor={`${question.id}-${option.text}`}
                          className="text-sm font-normal"
                        >
                          {option.text}
                        </Label>
                      </div>
                    );
                  }) : <div className="text-red-500">No options available for this question</div>}

                  {question.includeInScore === false && (
                    <p className="text-xs text-muted-foreground mt-2">
                      This response will not be included in your risk profile score calculation.
                    </p>
                  )}
                </div>
              ) : (
                <RadioGroup
                  value={responses[question.id] as string}
                  onValueChange={(value) => {
                    if (!isReadOnly) {
                      setResponses(prev => ({ ...prev, [question.id]: value }))
                    }
                  }}
                  disabled={isReadOnly}
                >
                  {Array.isArray(question.options) ? question.options.map((option) => (
                    <div key={option.text} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.text} id={`${question.id}-${option.text}`} />
                      <Label htmlFor={`${question.id}-${option.text}`}>{option.text}</Label>
                    </div>
                  )) : <div className="text-red-500">No options available for this question</div>}
                  {question.includeInScore === false && (
                    <p className="text-xs text-muted-foreground mt-2">
                      This response will not be included in your risk profile score calculation.
                    </p>
                  )}
                </RadioGroup>
              )}
            </CardContent>
          </Card>
        ))}

        <div className="flex justify-end mt-6">
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || isReadOnly}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Risk Profile'}
          </Button>
        </div>
      </div>
    </div>
  );
}
