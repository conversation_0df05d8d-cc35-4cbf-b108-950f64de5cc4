"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { useParams } from 'next/navigation';
import { discoveryQuestions as defaultDiscoveryQuestions, DiscoverySection } from '@/lib/discovery-questions';
import { Progress } from "@/components/ui/progress";
import { format } from 'date-fns';
import { toast } from 'sonner';
import { ChevronLeft, ChevronRight, Save, Trash2, Plus, Refresh<PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import * as bcrypt from 'bcryptjs';
import FormLogo from '@/components/FormLogo';

interface FormResponse {
  [key: string]: any;
  _memberMappings?: Record<string, number>;
  _mainMember?: Record<string, any>;
  _partnerMember?: Record<string, any>;
}

interface DiscoveryToken {
  id: number;
  token: string;
  household_id: number;
  status: 'pending' | 'completed' | 'expired';
  response: FormResponse | null;
  // New section-based fields
  client_summary: FormResponse | null;
  goals: FormResponse | null;
  recommendations: FormResponse | null;
  income_expenses: FormResponse | null;
  assets_liabilities: FormResponse | null;
  insurances: FormResponse | null;
  risk_profile: FormResponse | null;
  estate_planning: FormResponse | null;
  relationships: FormResponse | null;
  password_hash?: string | null;
  template_id?: string | null;
}

interface TokenBasicData {
  password_hash: string | null;
  status: string;
  template_id?: string | null;
}

export default function DiscoveryForm() {
  const [currentSection, setCurrentSection] = useState(0);
  const [responses, setResponses] = useState<FormResponse>({});
  const [submitted, setSubmitted] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [passwordVerified, setPasswordVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isUpdatingHousehold, setIsUpdatingHousehold] = useState(false);
  const [mainName, setMainName] = useState('');
  const [partnerName, setPartnerName] = useState('');
  const [discoveryQuestions, setDiscoveryQuestions] = useState<DiscoverySection[]>(defaultDiscoveryQuestions);
  const [templateId, setTemplateId] = useState<string | null>(null);
  const params = useParams();
  const token = decodeURIComponent(params.token as string);

  // First useEffect - only check for password protection
  useEffect(() => {
    const checkPasswordProtection = async () => {
      setLoading(true);
      const supabase = createClient();

      // Check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      setIsLoggedIn(!!user);

      // Only check for password protection initially
      // Use a try-catch to handle potential missing columns
      let data: TokenBasicData;
      try {
        const { data: tokenData, error } = await supabase
          .from('discovery_tokens')
          .select('password_hash, status, template_id')
          .eq('token', token)
          .single();

        if (error) {
          // If there's an error, try without template_id (in case it doesn't exist yet)
          const { data: basicData, error: basicError } = await supabase
            .from('discovery_tokens')
            .select('password_hash, status')
            .eq('token', token)
            .single();

          if (basicError) {
            toast.error('Error fetching responses');
            setLoading(false);
            return;
          }

          data = basicData;
        } else {
          data = tokenData;
        }
      } catch (err) {
        toast.error('Error fetching responses');
        setLoading(false);
        return;
      }

      // Check if the form is password protected
      if (data.password_hash) {
        setIsPasswordProtected(true);
        // If user is logged in, bypass password protection
        if (user) {
          setPasswordVerified(true);
        }
      } else {
        // No password protection, consider it verified
        setPasswordVerified(true);
      }

      // Check if a template is specified
      if (data.template_id) {
        try {
          setTemplateId(data.template_id);

          // Fetch the template questions
          const { data: templateData, error: templateError } = await supabase
            .from('templates')
            .select('content')
            .eq('id', data.template_id)
            .single();

          if (!templateError && templateData?.content) {
            try {
              // Parse the template content
              const parsedContent = JSON.parse(templateData.content);
              if (Array.isArray(parsedContent)) {
                setDiscoveryQuestions(parsedContent);
              }
            } catch (e) {
              console.error('Error parsing template content:', e);
              // Fall back to default questions if parsing fails
              setDiscoveryQuestions(defaultDiscoveryQuestions);
            }
          }
        } catch (e) {
          console.error('Error fetching template:', e);
          // Fall back to default questions if there's any error
          setDiscoveryQuestions(defaultDiscoveryQuestions);
        }
      }

      setLoading(false);
    };

    checkPasswordProtection();
  }, [token]);

  // State for adviser's organization ID
  const [adviserOrgId, setAdviserOrgId] = useState<string | null>(null);

  // Second useEffect - only runs after password verification
  useEffect(() => {
    // Only fetch form data if password is verified or user is logged in
    if (!passwordVerified && !isLoggedIn) return;

    const fetchFormData = async () => {
      setLoading(true);
      const supabase = createClient();

      const { data, error } = await supabase
        .from('discovery_tokens')
        .select('response, status, client_summary, goals, recommendations, income_expenses, assets_liabilities, insurances, risk_profile, estate_planning, relationships, household_id, created_by')
        .eq('token', token)
        .single();

      if (error) {
        toast.error('Error fetching responses');
        setLoading(false);
        return;
      }

      // Fetch household member names
      if (data?.household_id) {
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('members')
          .eq('id', data.household_id)
          .single();

        if (!householdError && householdData?.members) {
          const main = householdData.members.name1 || 'Main Client';
          const partner = householdData.members.name2 || 'Partner';
          setMainName(main);
          setPartnerName(partner);

          // Prefill names in responses if not already set
          if (!data.client_summary?.client_name) {
            setResponses(prev => ({
              ...prev,
              client_name: main,
              partner_name: partner
            }));
          }
        }
      }

      if (data?.status === 'completed') {
        // Combine all section responses into a single responses object
        const combinedResponses = {
          ...(data.response || {}),
          ...(data.client_summary || {}),
          ...(data.goals || {}),
          ...(data.recommendations || {}),
          ...(data.income_expenses || {}),
          ...(data.assets_liabilities || {}),
          ...(data.insurances || {}),
          ...(data.risk_profile || {}),
          ...(data.estate_planning || {}),
          ...(data.relationships || {})
        };

        // Extract names from responses if available
        if (combinedResponses.client_name) setMainName(combinedResponses.client_name);
        if (combinedResponses.partner_name) setPartnerName(combinedResponses.partner_name);

        setResponses(combinedResponses);
        setIsReadOnly(true);
        setSubmitted(true);
      }

      // Get the creator's organization ID
      if (data?.created_by) {
        const { data: creatorProfile, error: creatorError } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', data.created_by)
          .single();

        if (!creatorError && creatorProfile) {
          setAdviserOrgId(creatorProfile.org_id);
        }
      }

      setLoading(false);
    };

    fetchFormData();
  }, [token, passwordVerified, isLoggedIn]);

  const handleInputChange = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const handleDynamicListChange = (questionId: string, index: number, field: string, value: any) => {
    setResponses(prev => {
      const list = [...(prev[questionId] || [])];
      if (!list[index]) {
        list[index] = {};
      }
      list[index][field] = value;
      return {
        ...prev,
        [questionId]: list
      };
    });
  };

  const addDynamicListItem = (questionId: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: [...(prev[questionId] || []), {}]
    }));
  };

  const removeDynamicListItem = (questionId: string, index: number) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: (prev[questionId] || []).filter((_: any, i: number) => i !== index)
    }));
  };

  // Move getFieldsBySubType outside of renderDynamicList to avoid duplication
  const getFieldsBySubType = (subType: string) => {
    switch (subType) {
      case 'child':
        return [
          { id: 'name', label: 'Name', type: 'text' },
          { id: 'age', label: 'Age', type: 'number' },
          { id: 'relationship', label: 'Relationship', type: 'select', options: ['Biological', 'Step-child', 'Adopted', 'Foster', 'Other'] }
        ];
      case 'dependent':
        return [
          { id: 'name', label: 'Name', type: 'text' },
          { id: 'relationship', label: 'Relationship', type: 'select', options: ['Parent', 'Sibling', 'Grandparent', 'Other Family', 'Non-Family'] },
          { id: 'age', label: 'Age', type: 'number' },
          { id: 'support', label: 'Support Provided', type: 'text' }
        ];
        case 'advisor':
          return [
            { id: 'name', label: 'Name', type: 'text' },
            { id: 'profession', label: 'Profession', type: 'select', options: ['Accountant', 'Lawyer', 'Financial Advisor', 'Insurance Broker', 'Mortgage Broker', 'Other'] },
            { id: 'company', label: 'Company', type: 'text' },
            { id: 'contact', label: 'Contact Details', type: 'text' }
          ];
        case 'income':
          return [
            { id: 'source', label: 'Source', type: 'text' },
            { id: 'amount', label: 'Amount', type: 'currency' },
            { id: 'frequency', label: 'Frequency', type: 'select', options: ['Annually', 'Monthly', 'Fortnightly', 'Weekly'] }
          ];
        case 'expense':
          return [
            { id: 'category', label: 'Category', type: 'text' },
            { id: 'amount', label: 'Amount', type: 'currency' },
            { id: 'frequency', label: 'Frequency', type: 'select', options: ['Annually', 'Monthly', 'Fortnightly', 'Weekly'] }
          ];
        case 'property':
          return [
            { id: 'name', label: 'Property Name/Address', type: 'text' },
            { id: 'type', label: 'Property Type', type: 'select', options: ['owner_occupied', 'investment'] },
            { id: 'value', label: 'Value', type: 'currency' },
            { id: 'rental_income', label: 'Rental Income (if investment)', type: 'currency' },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'vehicle':
          return [
            { id: 'type', label: 'Type', type: 'text' },
            { id: 'value', label: 'Value', type: 'currency' }
          ];
        case 'savings':
          return [
            { id: 'name', label: 'Account Name', type: 'text' },
            { id: 'value', label: 'Balance', type: 'currency' },
            { id: 'provider', label: 'Bank/Institution', type: 'text' },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'investment':
          return [
            { id: 'name', label: 'Investment Name', type: 'text' },
            { id: 'type', label: 'Type', type: 'select', options: ['shares', 'managed_fund', 'term_deposit', 'bonds', 'other'] },
            { id: 'value', label: 'Value', type: 'currency' },
            { id: 'provider', label: 'Provider/Institution', type: 'text' },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'superannuation':
          return [
            { id: 'name', label: 'Fund Name', type: 'text' },
            { id: 'value', label: 'Balance', type: 'currency' },
            { id: 'provider', label: 'Provider', type: 'text' },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'other_asset':
          return [
            { id: 'name', label: 'Asset Name', type: 'text' },
            { id: 'type', label: 'Type', type: 'select', options: ['vehicle', 'jewelry', 'art', 'collectibles', 'business', 'other'] },
            { id: 'value', label: 'Value', type: 'currency' },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'mortgage':
          return [
            { id: 'property', label: 'Property', type: 'text' },
            { id: 'amount', label: 'Amount', type: 'currency' },
            { id: 'rate', label: 'Interest Rate', type: 'number' },
            { id: 'term', label: 'Term (Years)', type: 'number' }
          ];
        case 'loan':
          return [
            { id: 'type', label: 'Type', type: 'text' },
            { id: 'amount', label: 'Amount', type: 'currency' },
            { id: 'rate', label: 'Interest Rate', type: 'number' }
          ];
        case 'credit':
          return [
            { id: 'provider', label: 'Provider', type: 'text' },
            { id: 'limit', label: 'Credit Limit', type: 'currency' },
            { id: 'balance', label: 'Current Balance', type: 'currency' }
          ];
        case 'insurance':
        case 'life_insurance':
        case 'health_insurance':
        case 'income_protection':
        case 'trauma_insurance':
        case 'tpd_insurance':
        case 'general_insurance':
          return [
            { id: 'type', label: 'Insurance Type', type: 'select', options: ['life', 'health', 'income_protection', 'tpd', 'trauma', 'home', 'contents', 'vehicle', 'business', 'travel', 'other'] },
            { id: 'provider', label: 'Provider', type: 'text' },
            { id: 'policy_number', label: 'Policy Number', type: 'text' },
            { id: 'premium', label: 'Premium', type: 'currency' },
            { id: 'frequency', label: 'Premium Frequency', type: 'select', options: ['Annually', 'Monthly', 'Fortnightly', 'Weekly'] },
            { id: 'coverage_amount', label: 'Coverage Amount', type: 'currency' },
            { id: 'renewal_date', label: 'Renewal Date', type: 'date' },
            { id: 'person_insured', label: 'Person Insured', type: 'select', options: ['Household', '{MainName}', '{PartnerName}', 'Other'] },
            { id: 'policy_owner', label: 'Policy Owner', type: 'select', options: ['Household', '{MainName}', '{PartnerName}', 'Other'] },
            { id: 'details', label: 'Details', type: 'textarea' }
          ];
        case 'beneficiary':
          return [
            { id: 'name', label: 'Name', type: 'text' },
            { id: 'relationship', label: 'Relationship', type: 'text' },
            { id: 'allocation', label: 'Allocation %', type: 'number' }
          ];
        case 'goal':
          return [
            { id: 'title', label: 'Goal Title', type: 'text' },
            { id: 'type', label: 'Goal Type', type: 'select', options: [
              'Savings', 'Debt Reduction', 'Investment', 'Property', 'Education',
              'Retirement', 'Travel', 'Business', 'Career', 'Health', 'Other'
            ]},
            { id: 'details', label: 'Details', type: 'textarea' },
            { id: 'start_date', label: 'Start Date', type: 'date' },
            { id: 'target_date', label: 'Target Date', type: 'date' },
            { id: 'target_amount', label: 'Target Amount ($)', type: 'currency' },
            { id: 'priority', label: 'Priority', type: 'select', options: ['Low', 'Medium', 'High'] },
            { id: 'member', label: 'For Member', type: 'select', options: ['Household', '{MainName}', '{PartnerName}'] },
            { id: 'status', label: 'Status', type: 'select', options: ['Not Started', 'In Progress', 'Completed', 'On Hold'] }
          ];
      default:
        return [{ id: 'value', label: 'Value', type: 'text' }];
    }
  };

  const renderDynamicList = (question: any) => {
    // Ensure we're working with an array for the dynamic list
    const items = Array.isArray(responses[question.id]) ? responses[question.id] : [];

    // Replace placeholders in question text with actual names
    const questionText = question.question
      .replace(/{MainName}/g, mainName || 'Main')
      .replace(/{PartnerName}/g, partnerName || 'Partner');

    // Add member field to appropriate dynamic list types
    const shouldIncludeMemberField = ['goal', 'income', 'expense', 'asset', 'liability', 'insurance', 'share', 'managed_fund', 'term_deposit', 'investment', 'life_insurance', 'health_insurance', 'income_protection', 'trauma_insurance', 'tpd_insurance', 'general_insurance'].includes(question.subType);

    // Get fields for this dynamic list type
    let fieldDefinitions = getFieldsBySubType(question.subType);

    // Add member field if not already included
    if (shouldIncludeMemberField && !fieldDefinitions.some(f => f.id === 'member')) {
      // Add member field at the beginning of the array for prominence
      fieldDefinitions = [
        {
          id: 'member',
          label: 'For Member',
          type: 'select',
          options: ['Household', '{MainName}', '{PartnerName}']
        },
        ...fieldDefinitions
      ];
    }

    return (
      <div className="space-y-4">
        {items.map((item: any, index: number) => (
          <Card key={index} className="p-4">
            <div className="grid gap-4">
              {fieldDefinitions.map(field => {
                // Skip rental_income field if property type is not investment
                if (field.id === 'rental_income' && item.type !== 'investment') {
                  return null;
                }

                return (
                  <div key={field.id} className="grid gap-2">
                    <Label>{field.label}</Label>
                    {field.type === 'select' ? (
                      <Select
                        value={item[field.id] || ''}
                        onValueChange={(value) => handleDynamicListChange(question.id, index, field.id, value)}
                        disabled={isReadOnly}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={`Select ${field.label}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {field.options?.map(option => (
                            <SelectItem key={option} value={option}>
                              {option.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : field.type === 'currency' ? (
                      <div className="relative">
                        <span className="absolute left-3 top-2">$</span>
                        <Input
                          type="number"
                          value={item[field.id] || ''}
                          onChange={(e) => handleDynamicListChange(question.id, index, field.id, e.target.value)}
                          className="pl-6"
                          disabled={isReadOnly}
                        />
                      </div>
                    ) : field.type === 'textarea' ? (
                      <Textarea
                        value={item[field.id] || ''}
                        onChange={(e) => handleDynamicListChange(question.id, index, field.id, e.target.value)}
                        disabled={isReadOnly}
                      />
                    ) : (
                      <Input
                        type={field.type}
                        value={item[field.id] || ''}
                        onChange={(e) => handleDynamicListChange(question.id, index, field.id, e.target.value)}
                        disabled={isReadOnly}
                      />
                    )}
                  </div>
                );
              })}
              {!isReadOnly && (
                <div className="flex items-center justify-end">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-red-500 hover:bg-red-50"
                    onClick={() => removeDynamicListItem(question.id, index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </Card>
        ))}
        {!isReadOnly && (
          <Button
            variant="outline"
            onClick={() => addDynamicListItem(question.id)}
            className="w-full mt-4 flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add {questionText}
          </Button>
        )}
      </div>
    );
  };

  const renderQuestionInput = (question: any) => {
    switch (question.type) {
      case 'text':
        return (
          <Input
            id={question.id}
            type="text"
            value={responses[question.id] || ''}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            disabled={isReadOnly}
          />
        );
      case 'textarea':
        return (
          <Textarea
            id={question.id}
            value={responses[question.id] || ''}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            disabled={isReadOnly}
          />
        );
      case 'number':
        return (
          <Input
            id={question.id}
            type="number"
            value={responses[question.id] || ''}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            disabled={isReadOnly}
          />
        );
      case 'currency':
        return (
          <Input
            id={question.id}
            type="number"
            value={responses[question.id] || ''}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            disabled={isReadOnly}
            prefix="$"
            step="0.01"
          />
        );
      case 'date':
        return (
          <Input
            id={question.id}
            type="date"
            value={responses[question.id] || ''}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            disabled={isReadOnly}
          />
        );
      case 'select':
        return (
          <Select
            value={responses[question.id] || ''}
            onValueChange={(value) => handleInputChange(question.id, value)}
            disabled={isReadOnly}
          >
            <SelectTrigger id={question.id}>
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent>
              {question.options.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case 'radio':
        return (
          <RadioGroup
            value={responses[question.id] || ''}
            onValueChange={(value) => handleInputChange(question.id, value)}
            disabled={isReadOnly}
          >
            {question.options.map((option: string) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      case 'dynamic-list':
        return renderDynamicList(question);
      default:
        return null;
    }
  };

  const renderQuestion = (question: any) => {
    // Replace placeholders in question text with actual names
    const questionText = question.question
      .replace(/{MainName}/g, mainName || 'Main Client')
      .replace(/{PartnerName}/g, partnerName || 'Partner');

    return (
      <>
        <Label htmlFor={question.id}>
          {questionText}
          {question.validation?.required && <span className="text-red-500">*</span>}
        </Label>
        {renderQuestionInput(question)}
      </>
    );
  };

  const handleSubmit = async () => {
    if (isReadOnly) return;
    setLoading(true);

    try {
      const supabase = createClient();

      // Get section-specific responses
      const sectionResponses = getSectionResponses(responses, discoveryQuestions);

      // Map responses to specific members
      const { memberMappings, mainMember, partnerMember } = mapResponsesToMembers(responses);

      // Add member mappings to the saved data
      const enhancedResponses = {
        ...responses,
        _memberMappings: memberMappings,
        _mainMember: mainMember,
        _partnerMember: partnerMember
      };

            // Log for debugging
            console.log('Saving responses:', {
              response: responses,
              sections: sectionResponses
            });

      // Update the discovery token with sectioned responses
      const { error } = await supabase
        .from('discovery_tokens')
        .update({
          // Keep the original response field with member mappings
          response: enhancedResponses,
          // Add section-specific fields with all data
          client_summary: sectionResponses.client_summary || {},
          goals: sectionResponses.goals || {},
          recommendations: sectionResponses.recommendations || {},
          income_expenses: sectionResponses.income_expenses || {},
          assets_liabilities: sectionResponses.assets_liabilities || {},
          insurances: sectionResponses.insurances || {},
          risk_profile: sectionResponses.risk_profile || {},
          estate_planning: sectionResponses.estate_planning || {},
          relationships: sectionResponses.relationships || {},
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('token', token);

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      // Fetch the token data to get the created_by user ID and household information
      const { data: tokenData, error: tokenError } = await supabase
        .from('discovery_tokens')
        .select('created_by, household_id')
        .eq('token', token)
        .single();

      if (tokenError) {
        console.error('Error fetching token data:', tokenError);
      } else if (tokenData) {
        // Fetch household name
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', tokenData.household_id)
          .single();

        const householdName = householdError ? 'Unknown Household' : householdData?.householdName;

        // Create a notification for the user who created the discovery form
        await supabase.from('notifications').insert({
          user_id: tokenData.created_by,
          content: `Discovery form for ${householdName} has been completed`,
          type: 'discovery_form_submission',
          link: `/protected/households/household/${tokenData.household_id}`,
          created_at: new Date().toISOString()
        });
      }

      setSubmitted(true);
      setIsReadOnly(true);
      toast.success('Discovery form submitted successfully');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Error submitting form');
    } finally {
      setLoading(false);
    }
  };

  const currentSectionData = discoveryQuestions[currentSection];
  const progress = ((currentSection + 1) / discoveryQuestions.length) * 100;

  const renderCurrentSection = () => {
    const currentSectionData = discoveryQuestions[currentSection];

    // Only show tabs for the Personal Information section
    if (currentSectionData.id === 'personal') {
      return (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">{currentSectionData.title}</h2>
          <Tabs defaultValue="main" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="main">{mainName || 'Main Client'}</TabsTrigger>
              <TabsTrigger value="partner">{partnerName || 'Partner'}</TabsTrigger>
            </TabsList>
            <TabsContent value="main" className="space-y-4 pt-4">
              {currentSectionData.questions
                .filter(q => !q.id.startsWith('partner_'))
                .map((question) => (
                  <div key={question.id} className="space-y-2">
                    <Label htmlFor={question.id}>
                      {question.question
                        .replace(/{MainName}/g, mainName || 'Main Client')
                        .replace(/{PartnerName}/g, partnerName || 'Partner')}
                      {question.validation?.required && <span className="text-red-500">*</span>}
                    </Label>
                    {renderQuestionInput(question)}
                  </div>
                ))}
            </TabsContent>
            <TabsContent value="partner" className="space-y-4 pt-4">
              {currentSectionData.questions
                .filter(q => q.id.startsWith('partner_') ||
                  (!q.id.includes('client_') && !currentSectionData.questions.some(pq =>
                    pq.id === `partner_${q.id}`)))
                .map((question) => {
                  // For questions that don't have a partner_ prefix but should be shown in partner tab
                  const displayQuestion = question.id.startsWith('partner_')
                    ? question
                    : {
                        ...question,
                        id: `partner_${question.id}`,
                        question: question.question.replace('{MainName}', '{PartnerName}')
                      };

                  return (
                    <div key={displayQuestion.id} className="space-y-2">
                      <Label htmlFor={displayQuestion.id}>
                        {displayQuestion.question
                          .replace(/{MainName}/g, mainName || 'Main Client')
                          .replace(/{PartnerName}/g, partnerName || 'Partner')}
                        {displayQuestion.validation?.required && <span className="text-red-500">*</span>}
                      </Label>
                      {renderQuestionInput(displayQuestion)}
                    </div>
                  );
                })}
            </TabsContent>
          </Tabs>
        </div>
      );
    }

    // For other sections, keep the existing rendering logic
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">{currentSectionData.title}</h2>
        {currentSectionData.questions.map((question) => (
          <div key={question.id} className="space-y-2">
            <Label htmlFor={question.id}>
              {question.question
                .replace(/{MainName}/g, mainName || 'Main Client')
                .replace(/{PartnerName}/g, partnerName || 'Partner')}
              {question.validation?.required && <span className="text-red-500">*</span>}
            </Label>
            {renderQuestionInput(question)}
          </div>
        ))}
      </div>
    );
  };

  // Add this useEffect to handle client_name and partner_name initialization
  useEffect(() => {
    // Only run this if we're on the first section (assuming that's where these fields are)
    if (currentSection === 0) {
      const currentQuestions = discoveryQuestions[currentSection].questions;

      // Initialize client_name and partner_name if they're undefined
      currentQuestions.forEach(question => {
        if (question.id === 'client_name' && responses.client_name === undefined) {
          handleInputChange('client_name', mainName);
        }
        if (question.id === 'partner_name' && responses.partner_name === undefined) {
          handleInputChange('partner_name', partnerName);
        }
      });
    }
  }, [currentSection, mainName, partnerName, responses.client_name, responses.partner_name]);

  // Add this useEffect to initialize empty arrays for dynamic lists
  useEffect(() => {
    if (isReadOnly) return;

    const currentSectionData = discoveryQuestions[currentSection];
    currentSectionData.questions.forEach(question => {
      if (question.type === 'dynamic-list') {
        // Only initialize if not already set
        if (responses[question.id] === undefined) {
          // Use default value if available, otherwise empty array
          const defaultValue = question.defaultValue !== undefined ? question.defaultValue : [];
          setResponses(prev => ({
            ...prev,
            [question.id]: defaultValue
          }));
        }
      }
    });
  }, [currentSection, isReadOnly, responses]);

  const updateHouseholdData = async () => {
    setIsUpdatingHousehold(true);
    try {
      // Update personal data
      const personalResponse = await fetch('/api/discovery-documents/personal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!personalResponse.ok) {
        const errorData = await personalResponse.json();
        throw new Error(errorData.error || 'Failed to update household data');
      }

      // Update relationships data
      const relationshipsResponse = await fetch('/api/discovery-documents/relationships', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!relationshipsResponse.ok) {
        const errorData = await relationshipsResponse.json();
        console.warn('Warning: Failed to update relationships data:', errorData.error);
        // Continue even if relationships update fails
      }

      // Update income and expenses data
      const incomeExpensesResponse = await fetch('/api/discovery-documents/income-expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!incomeExpensesResponse.ok) {
        const errorData = await incomeExpensesResponse.json();
        console.warn('Warning: Failed to update income and expenses data:', errorData.error);
        // Continue even if income-expenses update fails
      }

      // Update goals data
      const goalsResponse = await fetch('/api/discovery-documents/goals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!goalsResponse.ok) {
        const errorData = await goalsResponse.json();
        console.warn('Warning: Failed to update goals data:', errorData.error);
        // Continue even if goals update fails
      } else {
        console.log('Successfully updated goals data');
      }

      // Update assets and liabilities data
      const assetsLiabilitiesResponse = await fetch('/api/discovery-documents/assets-liabilities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!assetsLiabilitiesResponse.ok) {
        const errorData = await assetsLiabilitiesResponse.json();
        console.warn('Warning: Failed to update assets and liabilities data:', errorData.error);
        // Continue even if assets-liabilities update fails
      } else {
        console.log('Successfully updated assets and liabilities data');
      }

      // Update insurance data
      const insurancesResponse = await fetch('/api/discovery-documents/insurances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!insurancesResponse.ok) {
        const errorData = await insurancesResponse.json();
        console.warn('Warning: Failed to update insurance data:', errorData.error);
        // Continue even if insurance update fails
      } else {
        console.log('Successfully updated insurance data');
      }

      toast.success('Successfully updated Household Data');
    } catch (error) {
      console.error('Error updating household data:', error);
      toast.error('Error Updating Household Data');
    } finally {
      setIsUpdatingHousehold(false);
    }
  };

  const verifyPassword = async () => {
    if (!isPasswordProtected) return;

    setPasswordError(null);

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('discovery_tokens')
        .select('password_hash')
        .eq('token', token)
        .single();

      if (error || !data || !data.password_hash) {
        setPasswordError('Could not verify password. Please try again.');
        return;
      }

      // Compare the entered password with the stored hash
      const isValid = await bcrypt.compare(password, data.password_hash);

      if (isValid) {
        setPasswordVerified(true);
      } else {
        setPasswordError('Incorrect password. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setPasswordError('An error occurred while verifying the password.');
    }
  };

  // Show password prompt if the form is password protected and not yet verified
  if (isPasswordProtected && !passwordVerified && !isLoggedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Password Protected Form
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                This form is password protected. Please enter the password to continue.
              </p>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
                {passwordError && (
                  <p className="text-sm text-red-500">{passwordError}</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={verifyPassword} className="w-full">
              Continue
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-4">
              <CardTitle>Discovery Document</CardTitle>
              {isReadOnly && submitted && (
                <Button
                  variant="outline"
                  onClick={updateHouseholdData}
                  className="flex items-center gap-2"
                  disabled={isUpdatingHousehold}
                >
                  <RefreshCw className={`h-4 w-4 ${isUpdatingHousehold ? 'animate-spin' : ''}`} />
                  {isUpdatingHousehold ? 'Updating Household' : 'Update Household'}
                </Button>
              )}
            </div>
            <FormLogo orgId={adviserOrgId || undefined} />
          </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>

          {renderCurrentSection()}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentSection(prev => prev - 1)}
            disabled={currentSection === 0}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <div className="space-x-2">
            {currentSection === discoveryQuestions.length - 1 ? (
              <Button
                onClick={handleSubmit}
                disabled={isReadOnly || loading}
              >
                <Save className="mr-2 h-4 w-4" />
                {loading ? 'Submitting...' : 'Submit'}
              </Button>
            ) : (
              <Button
                onClick={() => setCurrentSection(prev => prev + 1)}
              >
                Next
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

// Add a helper function to categorize responses by section
const categorizeResponsesBySections = (allResponses: FormResponse) => {
  type SectionKey = 'client_summary' | 'goals' | 'recommendations' | 'income_expenses' |
    'assets_liabilities' | 'insurances' | 'risk_profile' | 'estate_planning' | 'relationships';

  // Initialize section responses
  const sectionResponses: Record<SectionKey, FormResponse> = {
    client_summary: {},
    goals: {},
    recommendations: {},
    income_expenses: {},
    assets_liabilities: {},
    insurances: {},
    risk_profile: {},
    estate_planning: {},
    relationships: {}
  };

  // Map dynamic list types to their respective sections
  const dynamicListMappings: Record<string, SectionKey> = {
    'child': 'relationships',
    'dependent': 'relationships',
    'advisor': 'relationships',
    'income': 'income_expenses',
    'expense': 'income_expenses',
    'property': 'assets_liabilities',
    'vehicle': 'assets_liabilities',
    'savings': 'assets_liabilities',
    'investment': 'assets_liabilities',
    'superannuation': 'assets_liabilities',
    'other_asset': 'assets_liabilities',
    'mortgage': 'assets_liabilities',
    'loan': 'assets_liabilities',
    'credit': 'assets_liabilities',
    'insurance': 'insurances',
    'life_insurance': 'insurances',
    'health_insurance': 'insurances',
    'income_protection': 'insurances',
    'trauma_insurance': 'insurances',
    'tpd_insurance': 'insurances',
    'general_insurance': 'insurances',
    'beneficiary': 'estate_planning',
    'goal': 'goals'
  };

  // We can't access the discoveryQuestions state here, so this function is deprecated
  // Use getSectionResponses instead which takes the questions as a parameter
  defaultDiscoveryQuestions.forEach((section: DiscoverySection) => {
    const sectionKey = getSectionKey(section.title);

    section.questions.forEach(question => {
      if (allResponses[question.id] !== undefined) {
        // For dynamic lists, ensure they're mapped to the correct section
        if (question.type === 'dynamic-list' && question.subType) {
          const targetSection = dynamicListMappings[question.subType] || sectionKey;
          sectionResponses[targetSection as SectionKey][question.id] = allResponses[question.id];
        } else {
          // Regular questions
          sectionResponses[sectionKey as SectionKey][question.id] = allResponses[question.id];
        }
      }
    });
  });

  return sectionResponses;
};

// Helper function to convert section titles to section keys
const getSectionKey = (title: string): string => {
  const titleMap: Record<string, string> = {
    'Personal Information': 'client_summary',
    'Household Details': 'client_summary',
    'Client Information': 'client_summary',
    'Goals & Objectives': 'goals',
    'Goals': 'goals',
    'Financial Goals': 'goals',
    'Recommendations': 'recommendations',
    'Advice Recommendations': 'recommendations',
    'Income': 'income_expenses',
    'Expenses': 'income_expenses',
    'Income & Expenses': 'income_expenses',
    'Assets': 'assets_liabilities',
    'Liabilities': 'assets_liabilities',
    'Assets & Liabilities': 'assets_liabilities',
    'Insurance': 'insurances',
    'Insurances': 'insurances',
    'Insurance Coverage': 'insurances',
    'Investment': 'risk_profile',
    'Investments': 'risk_profile',
    'KiwiSaver': 'risk_profile',
    'Risk Profile': 'risk_profile',
    'Investment Risk': 'risk_profile',
    'Estate Planning': 'estate_planning',
    'Wills & Trusts': 'estate_planning',
    'Relationships': 'relationships',
    'Professional Relationships': 'relationships'
  };

  return titleMap[title] || 'client_summary'; // Default to client_summary if not found
};

// Enhanced function to properly map responses to members
const mapResponsesToMembers = (allResponses: FormResponse) => {
  const memberMappings: Record<string, number> = {};
  const mainMember: Record<string, any> = {};
  const partnerMember: Record<string, any> = {};

  // Map fields that are explicitly for main client or partner based on naming convention
  Object.entries(allResponses).forEach(([key, value]) => {
    // Fields with 'client_' prefix are for main client (member 1)
    if (key.startsWith('client_') && !key.includes('summary')) {
      memberMappings[key] = 1;
      mainMember[key] = value;
    }
    // Fields with 'partner_' prefix are for partner (member 2)
    else if (key.startsWith('partner_')) {
      memberMappings[key] = 2;
      partnerMember[key] = value;
    }
    // Fields that don't have a prefix but are known to be for main client
    else if (['date_of_birth', 'occupation', 'employment_status', 'employer',
              'tax_number', 'employment_income', 'employment_income_frequency',
              'kiwisaver_member', 'kiwisaver_provider', 'kiwisaver_fund_type',
              'kiwisaver_balance', 'kiwisaver_contribution_rate', 'retirement_age'].includes(key)) {
      memberMappings[key] = 1;
      mainMember[key] = value;
    }
  });

  // Scan through responses to find dynamic list items with member indicators
  Object.entries(allResponses).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (typeof item === 'object' && item !== null && 'member' in item) {
          const memberValue = item.member;
          if (memberValue === '{MainName}') {
            // Create a key that includes the array index
            memberMappings[`${key}[${index}]`] = 1;
          } else if (memberValue === '{PartnerName}') {
            memberMappings[`${key}[${index}]`] = 2;
          }
        }
      });
    }
  });

  return { memberMappings, mainMember, partnerMember };
};

// Update getSectionResponses to include member information
const getSectionResponses = (allResponses: FormResponse, questions: DiscoverySection[]) => {
  // Get the main and partner names from the responses
  const mainName = allResponses.client_name || 'Main Client';
  const partnerName = allResponses.partner_name || 'Partner';
  type SectionKey = 'client_summary' | 'goals' | 'recommendations' | 'income_expenses' |
    'assets_liabilities' | 'insurances' | 'risk_profile' | 'estate_planning' | 'relationships';

  const sectionResponses: Record<SectionKey, FormResponse> = {
    client_summary: {},
    goals: {},
    recommendations: {},
    income_expenses: {},
    assets_liabilities: {},
    insurances: {},
    risk_profile: {},
    estate_planning: {},
    relationships: {}
  };

  // Map dynamic list types to their respective sections
  const dynamicListMappings: Record<string, SectionKey> = {
    'child': 'relationships',
    'dependent': 'relationships',
    'advisor': 'relationships',
    'income': 'income_expenses',
    'expense': 'income_expenses',
    'property': 'assets_liabilities',
    'vehicle': 'assets_liabilities',
    'savings': 'assets_liabilities',
    'investment': 'assets_liabilities',
    'superannuation': 'assets_liabilities',
    'other_asset': 'assets_liabilities',
    'mortgage': 'assets_liabilities',
    'loan': 'assets_liabilities',
    'credit': 'assets_liabilities',
    'insurance': 'insurances',
    'life_insurance': 'insurances',
    'health_insurance': 'insurances',
    'income_protection': 'insurances',
    'trauma_insurance': 'insurances',
    'tpd_insurance': 'insurances',
    'general_insurance': 'insurances',
    'beneficiary': 'estate_planning',
    'goal': 'goals'
  };

  // Map specific fields directly to members
  const directMemberMappings: Record<string, {section: SectionKey, member: number}> = {
    // Personal Information
    'client_name': {section: 'client_summary', member: 1},
    'client_last_name': {section: 'client_summary', member: 1},
    'partner_name': {section: 'client_summary', member: 2},
    'partner_last_name': {section: 'client_summary', member: 2},
    'date_of_birth': {section: 'client_summary', member: 1},
    'partner_date_of_birth': {section: 'client_summary', member: 2},
    'address': {section: 'client_summary', member: 0}, // Household
    'postal_address': {section: 'client_summary', member: 0}, // Household
    'phone': {section: 'client_summary', member: 1}, // Typically main client
    'email': {section: 'client_summary', member: 1}, // Typically main client
    'tax_residency': {section: 'client_summary', member: 1},
    'partner_tax_residency': {section: 'client_summary', member: 2},
    'tax_number': {section: 'client_summary', member: 1},
    'partner_tax_number': {section: 'client_summary', member: 2},
    'citizenship': {section: 'client_summary', member: 1},
    'partner_citizenship': {section: 'client_summary', member: 2},

    // Employment
    'occupation': {section: 'client_summary', member: 1},
    'partner_occupation': {section: 'client_summary', member: 2},
    'employment_status': {section: 'client_summary', member: 1},
    'partner_employment_status': {section: 'client_summary', member: 2},
    'employer': {section: 'client_summary', member: 1},
    'partner_employer': {section: 'client_summary', member: 2},
    'years_with_employer': {section: 'client_summary', member: 1},
    'partner_years_with_employer': {section: 'client_summary', member: 2},

    // Income
    'employment_income': {section: 'income_expenses', member: 1},
    'employment_income_frequency': {section: 'income_expenses', member: 1},
    'partner_employment_income': {section: 'income_expenses', member: 2},
    'partner_employment_income_frequency': {section: 'income_expenses', member: 2},
    'business_income': {section: 'income_expenses', member: 0}, // Household
    'rental_income': {section: 'income_expenses', member: 0}, // Household
    'investment_income': {section: 'income_expenses', member: 0}, // Household
    'government_benefits': {section: 'income_expenses', member: 0}, // Household

    // KiwiSaver
    'kiwisaver_member': {section: 'risk_profile', member: 1},
    'kiwisaver_provider': {section: 'risk_profile', member: 1},
    'kiwisaver_fund_type': {section: 'risk_profile', member: 1},
    'kiwisaver_balance': {section: 'risk_profile', member: 1},
    'kiwisaver_contribution_rate': {section: 'risk_profile', member: 1},
    'employer_contribution_rate': {section: 'risk_profile', member: 1},
    'partner_kiwisaver_member': {section: 'risk_profile', member: 2},
    'partner_kiwisaver_provider': {section: 'risk_profile', member: 2},
    'partner_kiwisaver_fund_type': {section: 'risk_profile', member: 2},
    'partner_kiwisaver_balance': {section: 'risk_profile', member: 2},
    'partner_kiwisaver_contribution_rate': {section: 'risk_profile', member: 2},
    'kiwisaver_first_home': {section: 'risk_profile', member: 0}, // Household

    // Goals
    'retirement_age': {section: 'goals', member: 1},
    'partner_retirement_age': {section: 'goals', member: 2},
    'retirement_income': {section: 'goals', member: 0}, // Household
    'legacy_goals': {section: 'goals', member: 0}, // Household
    'financial_priorities': {section: 'goals', member: 0} // Household
  };

  // Helper function to determine section key from title
  const getSectionKey = (title: string): SectionKey => {
    const titleMap: Record<string, SectionKey> = {
      'Personal Information': 'client_summary',
      'Goals and Objectives': 'goals',
      'Family and Professional Relationships': 'relationships',
      'Income': 'income_expenses',
      'Expenses': 'income_expenses',
      'Assets': 'assets_liabilities',
      'Liabilities': 'assets_liabilities',
      'Insurance': 'insurances',
      'Investments': 'risk_profile',
      'Estate Planning': 'estate_planning'
    };
    return titleMap[title] || 'client_summary';
  };

  // Process direct member mappings
  Object.entries(directMemberMappings).forEach(([field, mapping]) => {
    if (allResponses[field] !== undefined) {
      if (!sectionResponses[mapping.section].members) {
        sectionResponses[mapping.section].members = {};
      }

      // Store in both the original location and in the members object
      sectionResponses[mapping.section][field] = allResponses[field];
      sectionResponses[mapping.section].members[`member${mapping.member}_${field}`] = allResponses[field];
    }
  });

  // Process all questions from the provided questions parameter
  questions.forEach((section: DiscoverySection) => {
    const sectionKey = getSectionKey(section.title);

    section.questions.forEach(question => {
      if (allResponses[question.id] !== undefined) {
        // For dynamic lists, ensure they're mapped to the correct section and member
        if (question.type === 'dynamic-list' && question.subType) {
          const targetSection = dynamicListMappings[question.subType] || sectionKey;

          // For dynamic lists, check if items have member information
          if (Array.isArray(allResponses[question.id])) {
            const items = allResponses[question.id];

            // Process each item in the dynamic list
            const processedItems = items.map((item: { member: string; }) => {
              // If item has member information, add it to the processed item
              if (item.member) {
                let memberId = 0; // Default to household

                if (item.member === '{MainName}') memberId = 1;
                else if (item.member === '{PartnerName}') memberId = 2;

                return {
                  ...item,
                  _memberId: memberId,
                  member: item.member.replace(/{MainName}/g, mainName).replace(/{PartnerName}/g, partnerName)
                };
              }
              return item;
            });

            sectionResponses[targetSection as SectionKey][question.id] = processedItems;
          } else {
            sectionResponses[targetSection as SectionKey][question.id] = allResponses[question.id];
          }
        } else {
          // Regular questions
          sectionResponses[sectionKey as SectionKey][question.id] = allResponses[question.id];
        }
      }
    });
  });

  return sectionResponses;
};
