@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 280 80% 60%;
    --chart-7: 340 80% 60%;
    --chart-8: 50 80% 60%;
    --chart-9: 150 80% 60%;
    --chart-15: 200 70% 60%;
    --color-netWealth: hsl(var(--chart-2));
    --color-income: hsl(var(--chart-2));
    --color-expenses: hsl(var(--chart-1));
    --color-savings: hsl(var(--chart-3));
    --color-investment: hsl(var(--chart-4));
    --color-kiwisaver: hsl(var(--chart-5));
    --color-worst-scenario: hsl(0, 100%, 50%);
    --color-best-scenario: hsl(120, 100%, 25%);
    --chart-10: 0 70% 60%;    /* Red for Living Expenses */
    --chart-11: 20 70% 60%;   /* Orange for Additional Expenses */
    --chart-12: 40 70% 60%;   /* Yellow-Orange for Debt Repayments */
    --chart-13: 200 70% 60%;  /* Blue for Investment Contributions */
    --chart-14: 160 70% 60%;  /* Teal for KiwiSaver Contributions */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 215.3 25% 26.7%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 215.3 5% 90%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 215.3 5% 90%;
    --primary: 215.3 25% 26.7%;
    --primary-foreground: 0 0% 100%;
    --secondary: 215.3 18% 10%;
    --secondary-foreground: 0 0% 100%;
    --primary-hover: 215.3 25% 35%;
    --secondary-hover: 215.3 18% 20%;
    --muted: 177.3 18% 15%;
    --muted-foreground: 215.3 5% 60%;
    --accent: 177.3 18% 15%;
    --accent-foreground: 215.3 5% 90%;
    --destructive: 0 50% 30%;
    --destructive-foreground: 215.3 5% 90%;
    --border: 215.3 20% 18%;
    --input: 215.3 20% 18%;
    --ring: 215.3 25% 26.7%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 280 80% 60%;
    --chart-7: 340 80% 60%;
    --chart-8: 50 80% 60%;
    --chart-9: 150 80% 60%;
    --color-netWealth: hsl(var(--chart-2));
    --color-income: hsl(var(--chart-2));
    --color-expenses: hsl(var(--chart-1));
    --color-savings: hsl(var(--chart-3));
    --color-investment: hsl(var(--chart-4));
    --color-kiwisaver: hsl(var(--chart-5));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}


/* Add this at the end of the file */
.novel-editor-no-border {
  border: none !important;
}


.react-grid-item {
  transition: all 0.2s ease-in-out;
}

.react-grid-item:hover {
  background-color: #f0f0f0; /* Light grey */
}

/* Responsive styles for tabs in resizable containers */
.compact-tabs .tabs-content {
  padding: 0.5rem;
}

.compact-tabs .tabs-content h3 {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.compact-tabs .tabs-content .space-y-6 {
  margin-top: 0.5rem;
}

.compact-tabs .tabs-content .card {
  margin-bottom: 0.5rem;
}

.compact-tabs .tabs-content input:not([type="checkbox"]):not([type="radio"]),
.compact-tabs .tabs-content select,
.compact-tabs .tabs-content button:not([role="checkbox"]) {
  height: 2rem;
  font-size: 0.75rem;
}

/* Ensure checkboxes maintain their normal size */
.compact-tabs .tabs-content .peer {
  height: auto !important;
  width: auto !important;
}

/* Specifically target Radix UI checkbox */
.compact-tabs .tabs-content [role="checkbox"],
.compact-tabs .tabs-content [type="checkbox"] {
  height: 1rem !important;
  width: 1rem !important;
  min-height: 1rem !important;
  min-width: 1rem !important;
}

/* Fix spacing around checkboxes */
.compact-tabs .tabs-content .flex.items-center.space-x-2 {
  margin-bottom: 0.5rem;
}

.compact-tabs .tabs-content label {
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
}

.compact-tabs .tabs-content .grid {
  gap: 0.5rem;
}

/* Markdown styling */
.markdown-content {
  @apply text-sm leading-relaxed;
}

.markdown-content p {
  @apply mb-4;
}

.markdown-content h1 {
  @apply text-2xl font-bold mb-4 mt-6;
}

.markdown-content h2 {
  @apply text-xl font-bold mb-3 mt-5;
}

.markdown-content h3 {
  @apply text-lg font-bold mb-3 mt-4;
}

.markdown-content h4 {
  @apply text-base font-bold mb-2 mt-4;
}

.markdown-content ul {
  @apply list-disc pl-5 mb-4;
}

.markdown-content ol {
  @apply list-decimal pl-5 mb-4;
}

.markdown-content li {
  @apply mb-1;
}

.markdown-content a {
  @apply text-blue-600 hover:underline;
}

.markdown-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono;
}

.markdown-content pre {
  @apply bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-x-auto mb-4;
}

.markdown-content pre code {
  @apply bg-transparent p-0;
}

.markdown-content blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-700 pl-4 italic my-4;
}

.markdown-content table {
  @apply w-full border-collapse mb-4;
}

.markdown-content th,
.markdown-content td {
  @apply border border-gray-300 dark:border-gray-700 px-3 py-2;
}

.markdown-content th {
  @apply bg-gray-100 dark:bg-gray-800;
}

