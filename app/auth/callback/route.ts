import { createClient } from '@/utils/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { createProfileAdmin } from '@/app/actions/admin-profile';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') || '/protected';

  if (code) {
    const supabase = await createClient();
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      // Get the authenticated user
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        // Check if user has a profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();

        // If no profile exists, create one with user metadata using admin privileges
        if (profileError && profileError.code === 'PGRST116') {
          const userData = {
            ...user.user_metadata,
            email: user.email
          };

          // Create profile with admin privileges
          const profileResult = await createProfileAdmin(user.id, userData);

          if (!profileResult.success) {
            console.error('Error creating profile in callback with admin privileges:', profileResult.error);
          } else {
            console.log('Profile created successfully in callback with admin privileges, ID:', profileResult.profileId);
          }
        }
      }
    }
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(new URL(next, requestUrl.origin));
}
