'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

export default function DatabaseSetup() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [tableExists, setTableExists] = useState(true);
  const supabase = createClient();

  // Check if the user_devices table exists
  const checkTable = async () => {
    try {
      const { error } = await supabase
        .from('user_devices')
        .select('id')
        .limit(1);

      if (error && error.code === '42P01') {
        // Table doesn't exist
        setTableExists(false);
      } else {
        setTableExists(true);
      }
    } catch (err) {
      console.error('Error checking table:', err);
      setTableExists(false);
    }
  };

  useEffect(() => {
    checkTable();
  }, []);

  const createTable = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Create the user_devices table
      const { error: createTableError } = await supabase.rpc('create_user_devices_table');

      if (createTableError) {
        throw createTableError;
      }

      setSuccess(true);
      setTableExists(true);
      
      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err: any) {
      console.error('Error creating table:', err);
      setError(err.message || 'Failed to create table');
    } finally {
      setLoading(false);
    }
  };

  if (tableExists) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-semibold mb-4">Database Setup Required</h2>
        
        <p className="mb-4">
          The device management feature requires a database table that doesn't exist yet. 
          Click the button below to create it.
        </p>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success ? (
          <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
            <AlertDescription>
              Table created successfully! Reloading page...
            </AlertDescription>
          </Alert>
        ) : (
          <Button 
            onClick={createTable} 
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Table...
              </>
            ) : (
              'Create Table'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
