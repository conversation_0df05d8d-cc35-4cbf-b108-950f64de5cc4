import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from '@/utils/supabase/client'

interface Household {
  id: number;
  householdName: string;
}

interface HouseholdSelectorProps {
  onHouseholdSelect: (householdId: number) => void;
}

export default function HouseholdSelector({ onHouseholdSelect }: HouseholdSelectorProps) {
  const [households, setHouseholds] = useState<Household[]>([]);

  useEffect(() => {
    const fetchHouseholds = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName');
      
      if (error) {
        console.error('Error fetching households:', error);
      } else {
        setHouseholds(data || []);
      }
    };

    fetchHouseholds();
  }, []);

  return (
    <Select onValueChange={(value) => onHouseholdSelect(Number(value))}>
      <SelectTrigger>
        <SelectValue placeholder="Select Household" />
      </SelectTrigger>
      <SelectContent>
        {households.map((household) => (
          <SelectItem key={household.id} value={household.id.toString()}>
            {household.householdName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}