"use client";

import { useState, useEffect, SetStateAction } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Sidebar from '@/components/sidebars/Sidebar';
import RecentScenarios from '@/components/RecentScenariosHome';
import RecentNotes from '@/components/RecentNotesHome';
import RecentScribbles from '@/components/RecentScribblesHome';
import Overview from '@/components/Overview';
import '@/app/globals.css'; 

const ResponsiveGridLayout = WidthProvider(Responsive);

const HomePage = () => {
  const [layout, setLayout] = useState([]);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const savedLayout = localStorage.getItem('homePageLayout');
    if (savedLayout) {
      setLayout(JSON.parse(savedLayout));
    } else {
      setLayout([
        { i: 'scenarios', x: 0, y: 0, w: 6, h: 2 } as never,
        { i: 'notes', x: 6, y: 0, w: 6, h: 2 } as never,
        { i: 'scribbles', x: 0, y: 2, w: 6, h: 2 } as never,
        { i: 'overview', x: 6, y: 2, w: 6, h: 2 } as never,
      ]);
    }
  }, []);

  const onLayoutChange = (newLayout: SetStateAction<never[]>) => {
    setLayout(newLayout);
    localStorage.setItem('homePageLayout', JSON.stringify(newLayout));
  };

  if (!mounted) return null;

  return (
    <div className="flex h-screen">
      <div className="flex-grow p-4 overflow-auto">
        <h1 className="text-2xl font-bold mb-4">Welcome to Wealthie</h1>
        <ResponsiveGridLayout
          className="layout"
          layouts={{ lg: layout }}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
          cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
          rowHeight={150}
          onLayoutChange={(layout: any) => onLayoutChange(layout)}
          isDraggable={true}
          isResizable={true}
        >
          <div key="scenarios" className="grid-item">
            <RecentScenarios />
          </div>
          <div key="notes" className="grid-item">
            <RecentNotes />
          </div>
          <div key="scribbles" className="grid-item">
            <RecentScribbles />
          </div>
          <div key="overview" className="grid-item">
            <Overview />
          </div>
        </ResponsiveGridLayout>
      </div>
    </div>
  );
};

export default HomePage;
