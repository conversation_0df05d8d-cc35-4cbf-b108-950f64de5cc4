'use client';

import { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';


interface SidebarHeaderProps {
  isCollapsed: boolean;
  toggleCollapse: () => void; // Changed from setIsCollapsed
  theme?: string;
}

export const ToggleIcon = ({ isCollapsed }: { isCollapsed: boolean }) => (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`transform transition-transform duration-500 ${
        isCollapsed ? 'rotate-180' : ''
      }`}
    >
      <path
        d="M11.25 4.5L6.75 9L11.25 13.5"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  export const Logo = ({ isCollapsed }: { isCollapsed?: boolean }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 40 40"
      width={isCollapsed ? "30" : "40"}
      height={isCollapsed ? "30" : "40"}
      className="transition-all duration-300 ease-in-out"
    >
      <style>
        {`
          .circle { fill: #f0f0f0; }
          .tree-left { fill: #1a5f38; }
          .tree-middle { fill: #2e8b57; }
          .tree-right { fill: #3cb371; }
        `}
      </style>

      {/* Background Circle */}
      <circle className="circle" cx="20" cy="20" r="19.6" />

      {/* Clipping Path for Trees */}
      <clipPath id="circle-clip">
        <circle cx="20" cy="20" r="19.6" />
      </clipPath>

      <g clipPath="url(#circle-clip)">
        {/* Left Tree */}
        <polygon className="tree-left" points="6,40 16,12 26,40" />
        <polygon className="tree-left" points="8,40 16,16 24,40" />
        <polygon className="tree-left" points="10,36 16,14 22,36" />

        {/* Middle Tree */}
        <polygon className="tree-middle" points="13,40 22,10 31,40" />
        <polygon className="tree-middle" points="15,40 22,14 29,40" />
        <polygon className="tree-middle" points="17,36 22,12 27,36" />

        {/* Right Tree */}
        <polygon className="tree-right" points="20,40 30,12 40,40" />
        <polygon className="tree-right" points="22,40 30,16 38,40" />
        <polygon className="tree-right" points="24,36 30,14 36,36" />
      </g>

      {/* Circle Border */}
      <circle fill="none" stroke="#333" strokeWidth="0.8" cx="20" cy="20" r="19.6" />
    </svg>
  );

  // Use toggleCollapse prop
  export const SidebarHeader = ({ isCollapsed, toggleCollapse, theme }: SidebarHeaderProps) => {
    return (
      <div className="flex justify-between items-center p-4">
        <Link href="/protected/dashboard" className="hover:opacity-80 transition-opacity">
          <Logo isCollapsed={isCollapsed} />
        </Link>
      <div className="w-4" />
      <h1
        className={`text-xl font-bold whitespace-nowrap overflow-hidden transition-[opacity,width] duration-300 ease-in-out ${
          isCollapsed ? 'w-0 opacity-0' : 'w-full opacity-100'
        }`}
      >
        Wealthie
      </h1>
      {/* Use toggleCollapse passed via props */}
      <button
        onClick={toggleCollapse}
        className="p-2 rounded-full hover:bg-accent focus:outline-none"
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
      </button>
    </div>
  );
};
