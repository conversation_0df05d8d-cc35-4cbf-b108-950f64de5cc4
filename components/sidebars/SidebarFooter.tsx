'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { FooterMenu } from './FooterMenu';
import { useEffect, useState } from 'react';

interface SidebarFooterProps {
  isCollapsed: boolean;
  handleLogout: () => Promise<void>;
}

export const SidebarFooter = ({ isCollapsed, handleLogout }: SidebarFooterProps) => {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');

  // Load the view mode from localStorage on component mount
  useEffect(() => {
    const savedViewMode = localStorage.getItem('scenariosViewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode);
    }
  }, []);

  // Save the view mode to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('scenariosViewMode', viewMode);
    // Dispatch a custom event so other components can react to this change
    window.dispatchEvent(new Event('viewModeChange'));
  }, [viewMode]);

  return (
    <div className="p-4 mt-auto">
      <FooterMenu 
        isCollapsed={isCollapsed} 
        handleLogout={handleLogout} 
        viewMode={viewMode}
        setViewMode={setViewMode}
      />
    </div>
  );
};
