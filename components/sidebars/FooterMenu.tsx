import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { ChevronDown, LogOut, User, Cog, Building, ChevronUp, Check } from 'lucide-react';
import Link from 'next/link';
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { getUserProfile } from '@/utils/profile-utils';


interface FooterMenuProps {
  isCollapsed: boolean;
  handleLogout: () => Promise<void>;
  viewMode: 'user' | 'organization';
  setViewMode: (mode: 'user' | 'organization') => void;
}


export const FooterMenu = ({ isCollapsed, handleLogout, viewMode, setViewMode }: FooterMenuProps) => {
  const [profileData, setProfileData] = useState<{name?: string, org_name?: string, user_id?: string, org_id?: string}>({});

  const currentName = viewMode === 'user'
    ? profileData.name || 'User'
    : profileData.org_name || 'Organization';

  useEffect(() => {
    const fetchUser = async () => {
      try {
        // Use the new profile utility that ensures profile exists
        const profile = await getUserProfile();

        if (profile) {
          setProfileData({
            name: profile.name ?? undefined,
            org_name: profile.org_name ?? undefined,
            user_id: profile.user_id ?? undefined,
            org_id: profile.org_id ?? undefined
          });

          // Set default view mode to 'user' if not already set in localStorage
          const savedViewMode = localStorage.getItem('viewMode');
          if (!savedViewMode) {
            setViewMode('user');
            localStorage.setItem('viewMode', 'user');
            localStorage.setItem('scenariosViewMode', 'user');
            window.dispatchEvent(new Event('viewModeChange'));
          }
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };
    fetchUser();
  }, [setViewMode]);

  // Update the setViewMode handler to store in localStorage and dispatch event
  const handleViewModeChange = (mode: 'user' | 'organization') => {
    setViewMode(mode);
    // Store in localStorage
    localStorage.setItem('viewMode', mode);
    // Also store as scenariosViewMode for backward compatibility
    localStorage.setItem('scenariosViewMode', mode);
    // Dispatch event for components to listen
    window.dispatchEvent(new Event('viewModeChange'));
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
      <Button variant="outline" className="w-full flex items-left justify-start gap-1 pl-1 pr-2">
          <Avatar className={`h-6 w-6 ${isCollapsed ? 'mx-0' : ''}`}>
            <AvatarFallback>
              {viewMode === 'user' ? (
                <User className="h-4 w-4" />
              ) : (
                <Building className="h-4 w-4" />
              )}
            </AvatarFallback>
          </Avatar>
          {!isCollapsed && (
            <>
              <span className="flex-grow text-left">{currentName}</span>
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[200px]">
        <DropdownMenuItem onClick={() => handleViewModeChange('user')}>
          <User className="mr-2 h-4 w-4" />
          <span className={viewMode === 'user' ? 'font-bold' : ''}>
            {profileData.name || 'User'}
          </span>
          {viewMode === 'user' && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        {/*
        {profileData.org_name && (
          <DropdownMenuItem onClick={() => handleViewModeChange('organization')}>
            <Building className="mr-2 h-4 w-4" />
            <span className={viewMode === 'organization' ? 'font-bold' : ''}>
              {profileData.org_name}
            </span>
            {viewMode === 'organization' && <Check className="ml-auto h-4 w-4" />}
          </DropdownMenuItem>
        )}  */}
        <Separator />
      <DropdownMenuItem asChild>
       <Link href="/protected/admin/profile">
        <User className="mr-2 h-4 w-4" />
         Admin
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleLogout}>
        <LogOut className="mr-2 h-4 w-4" />
        Log out
      </DropdownMenuItem>
    </DropdownMenuContent>
    </DropdownMenu>
  );
};
