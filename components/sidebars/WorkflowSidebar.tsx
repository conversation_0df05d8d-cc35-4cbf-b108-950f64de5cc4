'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronLeft,
  Plus,
  User,
  BarChart2,
  LogOut,
  CheckSquare,
  GitBranch,
  UserCheck,
  Calendar,
  Bell,
  Flag,
  Maximize,
  Minimize,
  Copy,
  Save,
  Play,
  FileText,
  BookmarkPlus,
  Mail
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { useRouter } from 'next/navigation';
import { signOutAction } from '@/app/actions';
import { LoadingModal } from '@/components/modals/LoadingModal';
import SaveWorkflowTemplateModal from '@/components/modals/SaveWorkflowTemplateModal';
import Link from 'next/link';
import { SidebarFooter } from './SidebarFooter';
import { SidebarHeader } from './SidebarHeader';

// Extend the Window interface to include custom workflow functions
declare global {
  interface Window {
    addWorkflowNode?: (nodeType: string, data: any) => void;
    duplicateWorkflowNodes?: () => void;
    saveWorkflow?: (returnData?: boolean) => any;
    expandAllWorkflowNodes?: (excludeNodeTypes?: string[]) => void;
    minimizeAllWorkflowNodes?: (excludeNodeTypes?: string[]) => void;
  }
}

const ToggleIcon = ({ isCollapsed }: { isCollapsed: boolean }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`transform transition-transform duration-500 ${
      isCollapsed ? 'rotate-180' : ''
    }`}
  >
    <path
      d="M11.25 4.5L6.75 9L11.25 13.5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const Logo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
    <style>
      {`
        .circle { fill: #f0f0f0; }
        .tree-left { fill: #1a5f38; }
        .tree-middle { fill: #2e8b57; }
        .tree-right { fill: #3cb371; }
      `}
    </style>

    <circle className="circle" cx="20" cy="20" r="19.6" />

    <clipPath id="circle-clip">
      <circle cx="20" cy="20" r="19.6" />
    </clipPath>

    <g clipPath="url(#circle-clip)">
      <polygon className="tree-left" points="6,40 16,12 26,40" />
      <polygon className="tree-left" points="8,40 16,16 24,40" />
      <polygon className="tree-left" points="10,36 16,14 22,36" />

      <polygon className="tree-middle" points="13,40 22,10 31,40" />
      <polygon className="tree-middle" points="15,40 22,14 29,40" />
      <polygon className="tree-middle" points="17,36 22,12 27,36" />

      <polygon className="tree-right" points="20,40 30,12 40,40" />
      <polygon className="tree-right" points="22,40 30,16 38,40" />
      <polygon className="tree-right" points="24,36 30,14 36,36" />
    </g>

    <circle fill="none" stroke="#333" strokeWidth="0.8" cx="20" cy="20" r="19.6" />
  </svg>
);

interface WorkflowSidebarProps {
  className?: string;
  isCollapsed?: boolean;
  setIsCollapsed?: (isCollapsed: boolean) => void;
}

// Create a custom event for expanding/minimizing all nodes
export const EXPAND_ALL_NODES_EVENT = 'wealthie-workflow-expand-all-nodes';
export const MINIMIZE_ALL_NODES_EVENT = 'wealthie-workflow-minimize-all-nodes';

export default function WorkflowSidebar({
  className,
  isCollapsed: propIsCollapsed,
  setIsCollapsed: propSetIsCollapsed
}: WorkflowSidebarProps) {
  const [localIsCollapsed, setLocalIsCollapsed] = useState(() => {
    // Persist collapsed state using localStorage
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('workflowSidebarCollapsed');
      return savedState ? JSON.parse(savedState) : false;
    }
    return false;
  });

  // Use props if provided, otherwise use local state
  const isCollapsed = propIsCollapsed !== undefined ? propIsCollapsed : localIsCollapsed;
  const setIsCollapsed = propSetIsCollapsed || setLocalIsCollapsed;
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [areNodesExpanded, setAreNodesExpanded] = useState(true);
  const [saveTemplateModalOpen, setSaveTemplateModalOpen] = useState(false);
  const router = useRouter();

  const onDragStart = (event: React.DragEvent, nodeType: string, data: any = {}) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/json', JSON.stringify(data));
    event.dataTransfer.effectAllowed = 'move';
  };

  // Node types that can be dragged onto the canvas
  const nodeTypes = [
    {
      id: 'start',
      type: 'startNode',
      label: 'Start',
      icon: Play,
      data: {
        title: 'Start Workflow',
        triggerType: 'manual',
        description: 'Starting point for the workflow'
      }
    },
    {
      id: 'task',
      type: 'taskNode',
      label: 'Task',
      icon: CheckSquare,
      data: {
        title: 'New Task',
        importance: 'medium',
        status: 'not started',
        nextAction: 'complete',
        outcome: 'pending'
      }
    },
    {
      id: 'decision',
      type: 'decisionNode',
      label: 'Decision',
      icon: GitBranch,
      data: {
        title: 'Decision Point',
        condition: 'status',
        completedPath: 'Path for Completed',
        inProgressPath: 'Path for In Progress',
        notStartedPath: 'Path for Not Started'
      }
    },
    {
      id: 'event',
      type: 'eventNode',
      label: 'Event',
      icon: Bell,
      data: {
        title: 'External Event',
        eventType: 'discovery_form',
        details: 'Event details'
      }
    },
    {
      id: 'calendarEvent',
      type: 'calendarEventNode',
      label: 'Calendar Event',
      icon: Calendar,
      data: {
        title: 'Calendar Event',
        eventType: 'meeting',
        eventDate: new Date().toISOString(),
        startTime: '09:00',
        endTime: '10:00',
        isAllDay: false
      }
    },
    {
      id: 'documentEvent',
      type: 'documentEventNode',
      label: 'Document',
      icon: FileText,
      data: {
        title: 'Send Document',
        documentType: 'discovery',
        selectedTemplate: 'default',
        emailSubject: 'Please complete this document',
        emailBody: 'Please review and complete the attached document.',
        passwordProtected: false
      }
    },
    {
      id: 'notification',
      type: 'notificationNode',
      label: 'Notification',
      icon: Bell,
      data: {
        title: 'Send Notification',
        notificationType: 'task_update',
        content: 'Notification content',
        recipients: 'all_members'
      }
    },
    {
      id: 'email',
      type: 'emailNode',
      label: 'Email',
      icon: Mail,
      data: {
        title: 'Send Email',
        subject: 'Email Subject',
        recipients: '',
        cc: '',
        bcc: '',
        content: 'Email content',
        priority: 'medium',
        status: 'draft'
      }
    },
    {
      id: 'end',
      type: 'endNode',
      label: 'End',
      icon: Flag,
      data: {
        title: 'Workflow Complete',
        summary: 'Workflow has been completed successfully.'
      }
    },
    {
      id: 'approval',
      type: 'taskNode',
      label: 'Approval',
      icon: UserCheck,
      data: {
        title: 'Approval Required',
        importance: 'high',
        status: 'not started',
        nextAction: 'approve',
        outcome: 'pending'
      }
    }
  ];

  const toggleCollapse = () => {
    const newIsCollapsed = !isCollapsed;
    setIsCollapsed(newIsCollapsed);
    localStorage.setItem('workflowSidebarCollapsed', JSON.stringify(newIsCollapsed));
  };

  // Toggle expand/minimize all nodes (except DecisionNodes which are always expanded)
  const toggleExpandAllNodes = () => {
    const newExpandState = !areNodesExpanded;
    setAreNodesExpanded(newExpandState);

    // Dispatch a custom event that the workflow page will listen for
    if (typeof window !== 'undefined') {
      const eventName = newExpandState ? EXPAND_ALL_NODES_EVENT : MINIMIZE_ALL_NODES_EVENT;
      console.log(`Dispatching event: ${eventName}`);
      window.dispatchEvent(new CustomEvent(eventName, {
        detail: {
          // Exclude decision nodes from being minimized
          excludeNodeTypes: ['decisionNode']
        }
      }));

      // Also try to call the direct methods if they exist
      if (newExpandState && window.expandAllWorkflowNodes) {
        console.log('Calling direct expand method');
        window.expandAllWorkflowNodes(['decisionNode']); // Pass excluded node types
      } else if (!(newExpandState) && window.minimizeAllWorkflowNodes) {
        console.log('Calling direct minimize method');
        window.minimizeAllWorkflowNodes(['decisionNode']); // Pass excluded node types
      }
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await signOutAction();
      router.push('/sign-in');
    } catch (error) {
      console.error('Error logging out:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <aside
        className={cn(
          'h-screen fixed left-0 top-0 z-50 flex flex-col transition-all duration-300 ease-in-out',
          theme === 'dark'
            ? 'bg-gray-900 text-white'
            : 'bg-background text-foreground',
          isCollapsed ? 'w-16' : 'w-[220px]',
          className
        )}
      >
        <SidebarHeader isCollapsed={isCollapsed} toggleCollapse={toggleCollapse} theme={theme} />

        <div className="flex-1 overflow-y-auto">
          {!isCollapsed && (
            <div className="p-4">
              {/* Actions Section */}
              <h2 className="text-sm font-semibold text-muted-foreground uppercase mb-4">Actions</h2>
              <div className="space-y-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-between"
                    >
                      <span>Add Node</span>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding start node from sidebar");
                        const newNodeId = window.addWorkflowNode('startNode', { title: 'Start Workflow' });
                        console.log("Added start node with ID:", newNodeId);
                      }
                    }}>
                      Start Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding task node from sidebar");
                        const newNodeId = window.addWorkflowNode('taskNode', { title: 'New Task' });
                        console.log("Added task node with ID:", newNodeId);
                      }
                    }}>
                      Task Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding decision node from sidebar");
                        const newNodeId = window.addWorkflowNode('decisionNode', { title: 'Decision Point' });
                        console.log("Added decision node with ID:", newNodeId);
                      }
                    }}>
                      Decision Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding event node from sidebar");
                        const newNodeId = window.addWorkflowNode('eventNode', { title: 'External Event' });
                        console.log("Added event node with ID:", newNodeId);
                      }
                    }}>
                      Event Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding calendar event node from sidebar");
                        const newNodeId = window.addWorkflowNode('calendarEventNode', { title: 'Calendar Event' });
                        console.log("Added calendar event node with ID:", newNodeId);
                      }
                    }}>
                      Calendar Event Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding document event node from sidebar");
                        const documentNodeData = {
                          title: 'Send Document',
                          documentType: 'discovery',
                          selectedTemplate: 'default',
                          emailSubject: 'Please complete this document',
                          emailBody: 'Please review and complete the attached document.',
                          passwordProtected: false
                        };
                        const newNodeId = window.addWorkflowNode('documentEventNode', documentNodeData);
                        console.log("Added document event node with ID:", newNodeId);
                      }
                    }}>
                      Document Node
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding email node from sidebar");
                        const emailNodeData = {
                          title: 'Send Email',
                          subject: 'Email Subject',
                          recipients: '',
                          cc: '',
                          bcc: '',
                          content: 'Email content',
                          priority: 'medium',
                          status: 'draft'
                        };
                        const newNodeId = window.addWorkflowNode('emailNode', emailNodeData);
                        console.log("Added email node with ID:", newNodeId);
                      }
                    }}>
                      Email Node
                    </DropdownMenuItem>
                    {/* Notification nodes are now added via PlaceholderNode when hovering over other nodes */}
                    <DropdownMenuItem onClick={() => {
                      if (window.addWorkflowNode) {
                        console.log("Adding end node from sidebar");
                        const newNodeId = window.addWorkflowNode('endNode', { title: 'Workflow Complete' });
                        console.log("Added end node with ID:", newNodeId);
                      }
                    }}>
                      End Node
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-between"
                  onClick={() => {
                    if (window.duplicateWorkflowNodes) window.duplicateWorkflowNodes();
                  }}
                >
                  <span>Duplicate Selected</span>
                  <Copy className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-between"
                  onClick={toggleExpandAllNodes}
                >
                  <span>{areNodesExpanded ? 'Minimize All' : 'Expand All'}</span>
                  {areNodesExpanded ? (
                    <Minimize className="h-4 w-4" />
                  ) : (
                    <Maximize className="h-4 w-4" />
                  )}
                </Button>

                <Button
                  variant="default"
                  className="w-full flex items-center justify-between mt-4"
                  onClick={() => {
                    if (window.saveWorkflow) window.saveWorkflow();
                  }}
                >
                  <span>Save Workflow</span>
                  <Save className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-between mt-2"
                  onClick={() => setSaveTemplateModalOpen(true)}
                >
                  <span>Save As Template</span>
                  <BookmarkPlus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
        <SidebarFooter isCollapsed={isCollapsed} handleLogout={handleLogout} />
      </aside>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out',
          isCollapsed ? 'w-16' : 'w-[220px]'
        )}
        aria-hidden="true"
      />

      <LoadingModal isOpen={isLoading} message="Logging out..." />

      {/* Save as Template Modal */}
      <SaveWorkflowTemplateModal
        isOpen={saveTemplateModalOpen}
        onClose={() => setSaveTemplateModalOpen(false)}
        workflowData={null} // We fetch the data directly in the modal using window.saveWorkflow
      />
    </>
  );
}
