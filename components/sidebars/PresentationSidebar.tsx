'use client';

import { useState, useEffect } from 'react';
import { Line<PERSON><PERSON>, Layers, Download } from 'lucide-react';
import { useTheme } from 'next-themes';
import { PresentationSidebarHeader } from './PresentationSidebarHeader';
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import PresentationDownloadModal from '@/components/modals/PresentationDownloadModal';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Scenario {
  id: number;
  scenario_name: string;
  household_id: number;
  household_name: string;
  allMetrics?: any[];
  color?: string;
  chanceOfSuccess?: number;
  successfulScenarios?: number;
  failedScenarios?: number;
  inputData?: {
    income_period?: [number, number];
    starting_age?: number;
    ending_age?: number;
    [key: string]: any;
  };
}

interface PresentationSidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
  scenarios: Scenario[];
  activeScenarioId: number | null;
  onScenarioChange: (scenarioId: number) => void;
  isOverlayMode: boolean;
  setIsOverlayMode: (value: boolean) => void;
  onConfigureScenarios: () => void;
}

const PresentationSidebar = ({
  isCollapsed,
  setIsCollapsed,
  scenarios,
  activeScenarioId,
  onScenarioChange,
  isOverlayMode,
  setIsOverlayMode,
  onConfigureScenarios
}: PresentationSidebarProps) => {
  const [isClient, setIsClient] = useState(false);
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);
  const { theme } = useTheme();

  useEffect(() => {
    setIsClient(true);
    const savedCollapsed = localStorage.getItem('sidebarCollapsed');
    // Set initial state from localStorage only if setIsCollapsed is a function
    if (typeof setIsCollapsed === 'function') {
      setIsCollapsed(savedCollapsed ? JSON.parse(savedCollapsed) : false);
    }
  }, [setIsCollapsed]); // Add setIsCollapsed to dependency array

  // Function to toggle collapse state and save to localStorage
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState); // Call the function passed via props
    localStorage.setItem('sidebarCollapsed', JSON.stringify(newState));
  };



  return (
    <>
      {isClient && (
        <aside
          className={`h-screen fixed left-0 top-0 ${
            theme === 'dark'
              ? 'bg-gray-900 text-white'
              : 'bg-background text-foreground'
          } flex flex-col transition-all duration-300 ease-in-out ${
            isCollapsed ? 'w-16' : 'w-[220px]'
          }`}
        >
          {/* Pass the toggle function to SidebarHeader */}
          <PresentationSidebarHeader
            isCollapsed={isCollapsed}
            toggleCollapse={toggleCollapse}
            theme={theme}
          />
          <nav className="mt-2 flex-grow overflow-y-auto">
            <div className={`px-3 py-2 ${isCollapsed ? 'hidden' : 'block'}`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold">Scenarios</h3>
                {scenarios.length > 1 && (
                  <div className="flex items-center space-x-2 p-1 rounded-md bg-muted/50">
                    <Switch
                      id="overlay-mode"
                      checked={isOverlayMode}
                      onCheckedChange={setIsOverlayMode}
                      className={isOverlayMode ? "bg-amber-500" : "data-[state=checked]:bg-primary"}
                    />
                    <div className="flex flex-col">
                      <Label htmlFor="overlay-mode" className={`text-xs cursor-pointer ${isOverlayMode ? "text-amber-600 font-medium" : ""}`}>
                        Overlay {isOverlayMode ? "(Active)" : ""}
                      </Label>
                    </div>
                  </div>
                )}
              </div>

              {scenarios.length > 0 ? (
                <div className="space-y-2">
                  {scenarios.map((scenario) => (
                    <div
                      key={scenario.id}
                      className={`p-2 rounded-md ${
                        isOverlayMode
                          ? 'cursor-default'
                          : 'cursor-pointer hover:bg-muted'
                      } transition-colors ${
                        activeScenarioId === scenario.id
                          ? 'bg-accent text-accent-foreground'
                          : isOverlayMode
                            ? 'bg-muted/40' // Light background for all scenarios in overlay mode
                            : ''
                      }`}
                      onClick={() => !isOverlayMode && onScenarioChange(scenario.id)}
                      title={isOverlayMode ? "All scenarios are shown in overlay mode" : "Click to view this scenario"}
                    >
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-sm">{scenario.scenario_name}</div>
                        {isOverlayMode && (
                          <div className="w-3 h-3 rounded-full"
                            style={{
                              backgroundColor: ['#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800'][
                                scenarios.findIndex(s => s.id === scenario.id) % 5
                              ]
                            }}
                          />
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">{scenario.household_name}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No scenarios selected
                </div>
              )}
            </div>

            {isCollapsed && (
              <div className="flex flex-col items-center space-y-2 mt-4">
                {scenarios.length > 0 && (
                  <>
                    {isOverlayMode && (
                      <div className="text-xs text-amber-600 mb-2 font-medium text-center px-1">
                        Overlay mode
                      </div>
                    )}
                    {scenarios.map((scenario) => (
                      <TooltipProvider key={scenario.id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`w-10 h-10 flex items-center justify-center rounded-full ${
                                isOverlayMode
                                  ? 'cursor-default'
                                  : 'cursor-pointer hover:bg-muted/80'
                              } ${
                                activeScenarioId === scenario.id
                                  ? 'bg-accent text-accent-foreground'
                                  : isOverlayMode
                                    ? 'bg-muted/40' // Light background for all scenarios in overlay mode
                                    : 'bg-muted'
                              }`}
                              onClick={() => !isOverlayMode && onScenarioChange(scenario.id)}
                              style={isOverlayMode ? {
                                borderLeft: `3px solid ${['#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800'][
                                  scenarios.findIndex(s => s.id === scenario.id) % 5
                                ]}`
                              } : {}}
                            >
                              <LineChart size={16} />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="z-[9999]">
                            <div className="space-y-1">
                              <p className="font-medium">{scenario.scenario_name}</p>
                              <p className="text-xs text-muted-foreground">{scenario.household_name}</p>
                              {isOverlayMode && (
                                <p className="text-xs text-amber-600">All scenarios shown in overlay mode</p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </>
                )}

                {/* Export button in collapsed view 
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer hover:bg-muted/80 bg-muted mt-4"
                        onClick={() => setIsDownloadModalOpen(true)}
                      >
                        <Download size={16} />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="z-[9999]">
                      <p className="font-medium">Export Key Metrics</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                */}
              </div>
            )}
          </nav>
          <div className={`mt-auto p-4 ${isCollapsed ? 'hidden' : 'block'}`}>
            <div className="space-y-2">
              {/* Export button in expanded view 
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => setIsDownloadModalOpen(true)}
              >
                <Download size={16} />
                Export Key Metrics
              </Button>
              */}
              <Button
                variant="outline"
                className="w-full"
                onClick={onConfigureScenarios}
              >
                Configure Scenarios
              </Button>
            </div>
          </div>
        </aside>
      )}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isCollapsed ? 'w-16' : 'w-[220px]'
        }`}
        aria-hidden="true"
      ></div>

      {/* Presentation Download Modal */}
      {isClient && (
        <PresentationDownloadModal
          isOpen={isDownloadModalOpen}
          onClose={() => setIsDownloadModalOpen(false)}
          scenarios={scenarios}
        />
      )}
    </>
  );
};

export default PresentationSidebar;
