import { createClient } from '@/utils/supabase/client'; // Use client helper

export interface Household { // Add export
  id: number;
  householdName: string;
  address?: string;
  email?: string;
  // Add other household fields as needed
}

export interface Scenario {
  id: number;
  scenario_name: string;
  household_id: number;
  householdName?: string | null; // Add optional householdName
  // Add other scenario fields as needed
}

export const searchHouseholds = async (term: string): Promise<{ households: Household[], scenarios: Scenario[] }> => {
  if (term.length < 2) {
    return { households: [], scenarios: [] };
  }

  const supabase = createClient(); // Use client helper instance

  try {
    // Search households with specific column selection to prevent data exposure
    const { data: householdsData, error: householdsError } = await supabase
      .from('households')
      .select('id, householdName, city, state, created_at')
      .or(`householdName.ilike.%${term}%,city.ilike.%${term}%,state.ilike.%${term}%`)
      .limit(10);

    if (householdsError) throw householdsError;

    // Search scenarios and join with households to get householdName
    const { data: scenariosData, error: scenariosError } = await supabase
      .from('scenarios_data1')
      .select('id, scenario_name, household_id, households ( householdName )') // Fetch householdName via relationship
      .ilike('scenario_name', `%${term}%`)
      .limit(10);

    if (scenariosError) throw scenariosError;

    // Map scenarios to flatten the household name
    const mappedScenarios = (scenariosData || []).map(s => {
      // Adjust type assertion and access logic for potential array structure
      const householdRelation = s.households as { householdName: string }[] | { householdName: string } | null;
      let hhName: string | null = null;

      if (Array.isArray(householdRelation) && householdRelation.length > 0) {
        hhName = householdRelation[0]?.householdName ?? null; // Access first element if array
      } else if (householdRelation && !Array.isArray(householdRelation)) {
        hhName = householdRelation.householdName ?? null; // Access directly if object
      }

      return {
        ...s,
        householdName: hhName
      };
    });

    return {
      households: householdsData || [],
      scenarios: mappedScenarios
    };
  } catch (error) {
    console.error('Error searching:', error);
    return { households: [], scenarios: [] };
  }
};

export const debouncedSearch = (callback: (term: string) => void, delay: number) => {
  let timeoutId: NodeJS.Timeout;

  return (term: string) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback(term), delay);
  };
};
