'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Building,
  User,
} from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { signOutAction } from '@/app/actions';
import { LoadingModal } from '@/components/modals/LoadingModal';
import { useTheme } from 'next-themes';
import { SidebarHeader } from './SidebarHeader';
import { SidebarFooter } from './SidebarFooter';

interface AdminSidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: (value: boolean) => void;
}

const AdminSidebar = ({ isCollapsed, setIsCollapsed }: AdminSidebarProps) => {
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { theme } = useTheme();

  useEffect(() => {
    setIsClient(true);
    const savedCollapsed = localStorage.getItem('adminSidebarCollapsed');
    // Set initial state from localStorage only if setIsCollapsed is a function
    if (typeof setIsCollapsed === 'function') {
      setIsCollapsed(savedCollapsed ? JSON.parse(savedCollapsed) : false);
    }

    // Add event listener for window resize to handle sidebar collapse on small screens
    const handleResize = () => {
      if (window.innerWidth < 768 && !isCollapsed) {
        setIsCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    // Initial check
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, [setIsCollapsed]); // Remove isCollapsed from dependencies

  // Function to toggle collapse state and save to localStorage
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    if (isClient) {
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(newState));
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await signOutAction();
      router.push('/sign-in');
    } catch (error) {
      console.error('Error logging out:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Only include Profile tab
  const menuItems = [
    { href: '/protected/admin/profile', icon: User, label: 'Profile' },
    { href: '/protected/admin/organisation', icon: Building, label: 'Organisation' },
  ];

  return (
    <>
      {isClient && (
        <aside
          className={`h-screen fixed left-0 top-0 ${
            theme === 'dark'
              ? 'bg-gray-900 text-white'
              : 'bg-background text-foreground'
          } flex flex-col transition-all duration-300 ease-in-out ${
            isCollapsed ? 'w-16' : 'w-[220px]'
          }`}
        >
          <SidebarHeader
            isCollapsed={isCollapsed}
            toggleCollapse={toggleCollapse}
            theme={theme}
          />
          <nav className="mt-2 flex-grow">
            <ul className="space-y-1">
              {menuItems.map((item, index) => (
                <li key={index} className="relative group">
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 transition-all duration-500 ${pathname === item.href ? 'bg-accent' : ''}`}
                  >
                    {pathname === item.href && (
                      <div className="absolute left-0 top-0 bottom-0 w-[5px] bg-[hsl(173,58%,39%)] rounded-r" />
                    )}
                    <item.icon
                      className="flex-shrink-0 text-muted-foreground"
                      size={18}
                    />
                    <span
                      className={`ml-3 whitespace-nowrap overflow-hidden transition-[opacity,width] duration-500 ease-in-out ${
                        isCollapsed ? 'w-0 opacity-0' : 'w-full opacity-100'
                      }`}
                    >
                      {item.label}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          <SidebarFooter
            isCollapsed={isCollapsed}
            handleLogout={handleLogout}
          />
        </aside>
      )}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isCollapsed ? 'w-16' : 'w-[220px]'
        }`}
        aria-hidden="true"
      ></div>
      <LoadingModal isOpen={isLoading} message="Logging out..." />
    </>
  );
};

export default AdminSidebar;
