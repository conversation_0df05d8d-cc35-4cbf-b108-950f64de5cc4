'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  Home,
  DollarSign,
  Briefcase,
  LineChart,
  ChevronLeft,
  ChevronDown,
} from 'lucide-react';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { signOutAction } from '@/app/actions';
import { LoadingModal } from '@/components/modals/LoadingModal';
import { useTheme } from 'next-themes';
import { FooterMenu } from './FooterMenu';
import { SidebarFooter } from './SidebarFooter';
import { SidebarHeader } from './SidebarHeader';

interface SidebarProps {
  householdId: string;
  isCollapsed: boolean;
  setIsCollapsed: (isCollapsed: boolean) => void;
}

const ToggleIcon = ({ isCollapsed }: { isCollapsed: boolean }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`transform transition-transform duration-500 ${
      isCollapsed ? 'rotate-180' : ''
    }`}
  >
    <path
      d="M11.25 4.5L6.75 9L11.25 13.5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const Logo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
    <style>
      {`
        .circle { fill: #f0f0f0; }
        .tree-left { fill: #1a5f38; }
        .tree-middle { fill: #2e8b57; }
        .tree-right { fill: #3cb371; }
      `}
    </style>
    
    {/* Background Circle */}
    <circle className="circle" cx="20" cy="20" r="19.6" />
    
    {/* Clipping Path for Trees */}
    <clipPath id="circle-clip">
      <circle cx="20" cy="20" r="19.6" />
    </clipPath>
    
    <g clipPath="url(#circle-clip)">
      {/* Left Tree */}
      <polygon className="tree-left" points="6,40 16,12 26,40" />
      <polygon className="tree-left" points="8,40 16,16 24,40" />
      <polygon className="tree-left" points="10,36 16,14 22,36" />
      
      {/* Middle Tree */}
      <polygon className="tree-middle" points="13,40 22,10 31,40" />
      <polygon className="tree-middle" points="15,40 22,14 29,40" />
      <polygon className="tree-middle" points="17,36 22,12 27,36" />
      
      {/* Right Tree */}
      <polygon className="tree-right" points="20,40 30,12 40,40" />
      <polygon className="tree-right" points="22,40 30,16 38,40" />
      <polygon className="tree-right" points="24,36 30,14 36,36" />
    </g>
    
    {/* Circle Border */}
    <circle fill="none" stroke="#333" strokeWidth="0.8" cx="20" cy="20" r="19.6" />
  </svg>
);

// Use the props directly instead of local state for isCollapsed
const HouseholdsSidebar = ({ householdId, isCollapsed, setIsCollapsed }: SidebarProps) => {
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { theme } = useTheme();

  useEffect(() => {
    setIsClient(true);
    // Remove localStorage logic for isCollapsed - rely on parent state
  }, []);

  // Function to toggle collapse state and save to localStorage
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState); // Call the function passed via props
    localStorage.setItem('sidebarCollapsed', JSON.stringify(newState));
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await signOutAction();
      router.push('/sign-in');
    } catch (error) {
      console.error('Error logging out:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const menuItems = [
    {
      title: 'Overview',
      icon: Home,
      href: `/protected/households/household/${householdId}/household_overview`,
      label: 'Overview'
    },
    {
      title: 'Income & Expenses',
      icon: DollarSign,
      href: `/protected/households/household/${householdId}/income_expenses`,
      label: 'Income & Expenses'
    },
    {
      title: 'Assets and Liabilities',
      icon: Briefcase,
      href: `/protected/households/household/${householdId}/assets_liabilities`,
      label: 'Assets & Liabilities'
    },
    {
      title: 'Scenarios',
      icon: LineChart,
      href: `/protected/households/household/${householdId}/scenarios`,
      label: 'Scenarios'
    },
  ];

  return (
    <>
      {isClient && (
        <aside
          className={`h-screen fixed left-0 top-0 ${
            theme === 'dark'
              ? 'bg-gray-900 text-white'
              : 'bg-background text-foreground'
          } flex flex-col transition-all duration-300 ease-in-out ${
            isCollapsed ? 'w-16' : 'w-[220px]'
          }`}
        >
          {/* Pass the toggle function to SidebarHeader */}
          <SidebarHeader isCollapsed={isCollapsed} toggleCollapse={toggleCollapse} theme={theme} />
          <nav className="mt-2 flex-grow">
            <ul className="space-y-1">
              {menuItems.map((item, index) => (
                <li key={index} className="relative group">
                  <button
                    onClick={() => router.push(item.href)}
                    className={`w-full flex items-center px-4 py-3 text-left transition-all duration-500 ${
                      pathname === item.href ? 'bg-accent' : 'hover:bg-accent/50'
                    }`}
                  >
                    {pathname === item.href && (
                      <div className="absolute left-0 top-0 bottom-0 w-[5px] bg-[hsl(173,58%,39%)] rounded-r" />
                    )}
                    <item.icon
                      className={`flex-shrink-0 ${pathname === item.href ? 'text-foreground' : 'text-muted-foreground'}`}
                      size={18}
                    />
                    <span
                      className={`ml-3 whitespace-nowrap overflow-hidden transition-[opacity,width] duration-500 ease-in-out ${
                        isCollapsed ? 'w-0 opacity-0' : 'w-full opacity-100'
                      }`}
                    >
                      {item.label}
                    </span>
                  </button>
                </li>
              ))}
            </ul>
          </nav>
          <SidebarFooter isCollapsed={isCollapsed} handleLogout={handleLogout} />
        </aside>
      )}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isCollapsed ? 'w-16' : 'w-[220px]'
        }`}
        aria-hidden="true"
      ></div>
      <LoadingModal isOpen={isLoading} message="Logging out..." />
    </>
  );
};

export default HouseholdsSidebar;
