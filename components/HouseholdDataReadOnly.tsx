'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface HouseholdDataProps {
  householdData: {
    id: string;
    householdData: {
      householdName: string;
      name1: string;
      age1: string;
      income1: string;
      kiwisaverValue1: string;
      kiwisaverContribution1: string;
      employerContribution1: string;
      kiwisaverProfile1: string;
      name2: string;
      age2: string;
      income2: string;
      kiwisaverValue2: string;
      kiwisaverContribution2: string;
      employerContribution2: string;
      kiwisaverProfile2: string;
      annualExpenses: number;
      propertyValue: number;
      debtValue: number;
      investmentValue: number;
      riskProfile: string;
      savingsValue: number;
    };
  };
}

export default function HouseholdDataReadOnly({ householdData }: HouseholdDataProps) {
  const formatCurrency = (value: number | string) => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
    }).format(numValue || 0);
  };

  const data = householdData.householdData;

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>{data.householdName}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Member 1 */}
            {data.name1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Member 1</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Name:</div>
                  <div className="text-sm">{data.name1}</div>
                  <div className="text-sm font-medium">Age:</div>
                  <div className="text-sm">{data.age1}</div>
                  <div className="text-sm font-medium">Income:</div>
                  <div className="text-sm">{formatCurrency(data.income1)}</div>
                  <div className="text-sm font-medium">KiwiSaver Value:</div>
                  <div className="text-sm">{formatCurrency(data.kiwisaverValue1)}</div>
                  <div className="text-sm font-medium">KiwiSaver Contribution:</div>
                  <div className="text-sm">{data.kiwisaverContribution1}%</div>
                  <div className="text-sm font-medium">Employer Contribution:</div>
                  <div className="text-sm">{data.employerContribution1}%</div>
                  <div className="text-sm font-medium">KiwiSaver Profile:</div>
                  <div className="text-sm">{data.kiwisaverProfile1}</div>
                </div>
              </div>
            )}

            {/* Member 2 */}
            {data.name2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Member 2</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div className="text-sm font-medium">Name:</div>
                  <div className="text-sm">{data.name2}</div>
                  <div className="text-sm font-medium">Age:</div>
                  <div className="text-sm">{data.age2}</div>
                  <div className="text-sm font-medium">Income:</div>
                  <div className="text-sm">{formatCurrency(data.income2)}</div>
                  <div className="text-sm font-medium">KiwiSaver Value:</div>
                  <div className="text-sm">{formatCurrency(data.kiwisaverValue2)}</div>
                  <div className="text-sm font-medium">KiwiSaver Contribution:</div>
                  <div className="text-sm">{data.kiwisaverContribution2}%</div>
                  <div className="text-sm font-medium">Employer Contribution:</div>
                  <div className="text-sm">{data.employerContribution2}%</div>
                  <div className="text-sm font-medium">KiwiSaver Profile:</div>
                  <div className="text-sm">{data.kiwisaverProfile2}</div>
                </div>
              </div>
            )}
          </div>

          {/* Household Financial Information */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-4">Household Financial Information</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm font-medium">Annual Expenses</div>
                <div className="text-sm">{formatCurrency(data.annualExpenses)}</div>
              </div>
              <div>
                <div className="text-sm font-medium">Property Value</div>
                <div className="text-sm">{formatCurrency(data.propertyValue)}</div>
              </div>
              <div>
                <div className="text-sm font-medium">Debt Value</div>
                <div className="text-sm">{formatCurrency(data.debtValue)}</div>
              </div>
              <div>
                <div className="text-sm font-medium">Investment Value</div>
                <div className="text-sm">{formatCurrency(data.investmentValue)}</div>
              </div>
              <div>
                <div className="text-sm font-medium">Savings Value</div>
                <div className="text-sm">{formatCurrency(data.savingsValue)}</div>
              </div>
              <div>
                <div className="text-sm font-medium">Risk Profile</div>
                <div className="text-sm">{data.riskProfile}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
