'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { format } from 'date-fns';
import { 
  ChevronRight, 
  Shield 
} from 'lucide-react';

interface RiskProfilerHistoryProps {
  profiles: Array<{
    id: string;
    profiler_type: '10q' | '25q';
    risk_score: number;
    completed_at: string;
  }>;
  getRiskCategory: (score: number) => { category: string; color: string };
}

export function RiskProfilerHistory({ 
  profiles, 
  getRiskCategory 
}: RiskProfilerHistoryProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-muted/50 py-4 px-6">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-primary" />
            <span>Risk Profile Journey</span>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {profiles.map((profile, index) => {
          const riskCategory = getRiskCategory(profile.risk_score);
          const isLatest = index === 0;

          return (
            <div 
              key={profile.id} 
              className={`
                relative 
                px-6 py-4 
                border-b last:border-b-0
                group
                transition-colors 
                ${isLatest ? 'bg-primary/5' : 'hover:bg-muted/30'}
              `}
            >
              <div className="flex items-center justify-between">
                <div className="flex-grow">
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-10 h-10 rounded-full 
                      flex items-center justify-center
                      ${riskCategory.color} 
                      bg-opacity-20
                    `}>
                      <span className="text-sm font-bold text-foreground">
                        {profile.profiler_type === '10q' ? '10Q' : '25Q'}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">
                        {riskCategory.category} Risk Profile
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(profile.completed_at), 'MMMM dd, yyyy')}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className={`
                      text-2xl font-bold 
                      ${riskCategory.color.replace('bg-', 'text-')}
                    `}>
                      {Math.round(profile.risk_score)}%
                    </div>
                    <Progress 
                      value={profile.risk_score} 
                      className={`h-1.5 mt-2 ${riskCategory.color}`} 
                    />
                  </div>
                  
                  {isLatest && (
                    <div className="text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                      <ChevronRight className="w-5 h-5" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
