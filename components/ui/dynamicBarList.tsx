// Dynamic BarList with adjustable row height

import React, { useMemo } from "react"
import { cx, focusRing } from "@/lib/utils"

type Bar<T> = T & {
  key?: string
  href?: string
  value: number
  name: string
  color?: string
}

interface DynamicBarListProps<T = unknown>
  extends React.HTMLAttributes<HTMLDivElement> {
  data: Bar<T>[]
  valueFormatter?: (value: number) => string
  showAnimation?: boolean
  onValueChange?: (payload: Bar<T>) => void
  sortOrder?: "ascending" | "descending" | "none"
  maxHeight?: number // Maximum height for the component
  minRowHeight?: number // Minimum height for each row
}

function DynamicBarListInner<T>(
  {
    data = [],
    valueFormatter = (value) => value.toString(),
    showAnimation = false,
    onValueChange,
    sortOrder = "descending",
    className,
    maxHeight = 300, // Default max height
    minRowHeight = 20, // Minimum row height in pixels
    ...props
  }: DynamicBarListProps<T>,
  forwardedRef: React.ForwardedRef<HTMLDivElement>,
) {
  const Component = onValueChange ? "button" : "div"
  const sortedData = useMemo(() => {
    if (sortOrder === "none") {
      return data
    }
    return [...data].sort((a, b) => {
      return sortOrder === "ascending" ? a.value - b.value : b.value - a.value
    })
  }, [data, sortOrder])

  const widths = useMemo(() => {
    const maxValue = Math.max(...sortedData.map((item) => item.value), 0)
    return sortedData.map((item) =>
      item.value === 0 ? 0 : Math.max((item.value / maxValue) * 100, 2),
    )
  }, [sortedData])

  // Calculate dynamic row height based on number of items
  const rowHeight = useMemo(() => {
    // Default height for 8 items or fewer
    if (sortedData.length <= 8) {
      return "h-8"; // Standard height (32px)
    }

    // Calculate a proportional height based on number of items
    // Ensure it doesn't go below the minimum row height
    const calculatedHeight = Math.max(
      Math.floor(maxHeight / sortedData.length),
      minRowHeight
    );

    // Use Tailwind classes when possible, otherwise use inline style
    if (calculatedHeight === 24) return "h-6";
    if (calculatedHeight === 20) return "h-5";
    if (calculatedHeight === 16) return "h-4";
    if (calculatedHeight === 12) return "h-3";
    if (calculatedHeight === 8) return "h-2";
    if (calculatedHeight === 4) return "h-1";

    // For other values, use a style (will be applied inline)
    return calculatedHeight;
  }, [sortedData.length, maxHeight, minRowHeight]);

  // Calculate margin between rows based on number of items
  const rowMargin = useMemo(() => {
    if (sortedData.length <= 8) {
      return "mb-1.5"; // Standard margin (6px)
    }

    // Reduce margin as number of items increases
    if (sortedData.length <= 12) {
      return "mb-1"; // 4px
    }

    if (sortedData.length <= 16) {
      return "mb-0.5"; // 2px
    }

    return "mb-0"; // No margin for many items
  }, [sortedData.length]);

  // Calculate font size based on row height
  const fontSize = useMemo(() => {
    if (sortedData.length <= 8) {
      return "text-sm"; // Standard size
    }

    if (sortedData.length <= 12) {
      return "text-xs"; // Smaller text
    }

    return "text-xs"; // Smallest text for many items
  }, [sortedData.length]);

  return (
    <div
      ref={forwardedRef}
      className={cx("flex justify-between space-x-6", className)}
      aria-sort={sortOrder}
      {...props}
    >
      <div className="relative w-full space-y-1">
        {sortedData.map((item, index) => (
          <Component
            key={item.key ?? item.name}
            onClick={() => {
              onValueChange?.(item)
            }}
            className={cx(
              // base
              "group w-full rounded",
              // focus
              focusRing,
              onValueChange
                ? [
                    "!-m-0 cursor-pointer",
                    // hover
                    "hover:bg-gray-50 hover:dark:bg-gray-900",
                  ]
                : "",
            )}
          >
            <div
              className={cx(
                // base
                "flex items-center rounded transition-all",
                typeof rowHeight === 'string' ? rowHeight : '',
                // margin and duration
                {
                  "mb-0": index === sortedData.length - 1,
                  "duration-800": showAnimation,
                },
              )}
              style={{
                width: `${widths[index]}%`,
                backgroundColor: item.color || "#3b82f6", // Use custom color or default blue
                ...(typeof rowHeight === 'number' ? { height: `${rowHeight}px` } : {})
              }}
            >
              <div className={cx("absolute left-2 flex max-w-full pr-2")}>
                {item.href ? (
                  <a
                    href={item.href}
                    className={cx(
                      // base
                      "truncate whitespace-nowrap rounded",
                      fontSize,
                      // text color
                      "text-gray-900 dark:text-gray-50",
                      // hover
                      "hover:underline hover:underline-offset-2",
                      // focus
                      focusRing,
                    )}
                    target="_blank"
                    rel="noreferrer"
                    onClick={(event) => event.stopPropagation()}
                  >
                    {item.name}
                  </a>
                ) : (
                  <p
                    className={cx(
                      // base
                      "truncate whitespace-nowrap",
                      fontSize,
                      // text color
                      "text-gray-900 dark:text-gray-50",
                    )}
                  >
                    {item.name}
                  </p>
                )}
              </div>
            </div>
          </Component>
        ))}
      </div>
      <div>
        {sortedData.map((item, index) => (
          <div
            key={item.key ?? item.name}
            className={cx(
              "flex items-center justify-end",
              typeof rowHeight === 'string' ? rowHeight : '',
              index === sortedData.length - 1 ? "mb-0" : rowMargin,
            )}
            style={typeof rowHeight === 'number' ? { height: `${rowHeight}px` } : {}}
          >
            <p
              className={cx(
                // base
                "truncate whitespace-nowrap leading-none",
                fontSize,
                // text color
                "text-gray-900 dark:text-gray-50",
              )}
            >
              {valueFormatter(item.value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}

DynamicBarListInner.displayName = "DynamicBarList"

const DynamicBarList = React.forwardRef(DynamicBarListInner) as <T>(
  p: DynamicBarListProps<T> & { ref?: React.ForwardedRef<HTMLDivElement> },
) => ReturnType<typeof DynamicBarListInner>

export { DynamicBarList, type DynamicBarListProps }
