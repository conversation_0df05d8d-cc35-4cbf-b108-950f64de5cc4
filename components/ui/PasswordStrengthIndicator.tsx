'use client';

import { useState, useEffect } from 'react';
import { validatePassword, getPasswordRequirements, type PasswordValidationResult } from '@/utils/password-validation';
import { Check, X, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface PasswordStrengthIndicatorProps {
  password: string;
  showRequirements?: boolean;
  className?: string;
}

export function PasswordStrengthIndicator({
  password,
  showRequirements = true,
  className = ''
}: PasswordStrengthIndicatorProps) {
  const [validation, setValidation] = useState<PasswordValidationResult | null>(null);

  useEffect(() => {
    if (password) {
      setValidation(validatePassword(password));
    } else {
      setValidation(null);
    }
  }, [password]);

  if (!password || !validation) {
    return showRequirements ? (
      <div className={`mt-2 ${className}`}>
        <p className="text-sm font-medium text-gray-700 mb-2">Password Requirements:</p>
        <ul className="text-xs text-gray-600 space-y-1">
          {getPasswordRequirements().map((req, index) => (
            <li key={index} className="flex items-center">
              <div className="w-3 h-3 rounded-full border border-gray-300 mr-2 flex-shrink-0" />
              {req}
            </li>
          ))}
        </ul>
      </div>
    ) : null;
  }

  const { strength, errors, isValid } = validation;

  return (
    <div className={`mt-2 ${className}`}>
      {/* Strength Bar */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs font-medium text-gray-700">Password Strength</span>
          <span
            className="text-xs font-medium"
            style={{ color: strength.color }}
          >
            {strength.label}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{
              width: `${strength.percentage}%`,
              backgroundColor: strength.color
            }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      {showRequirements && (
        <div>
          <p className="text-sm font-medium text-gray-700 mb-2">Requirements:</p>
          <ul className="text-xs space-y-1">
            {getPasswordRequirements().map((req, index) => {
              const isRequirementMet = getRequirementStatus(req, password);
              return (
                <li key={index} className="flex items-center">
                  <div className="mr-2 flex-shrink-0">
                    {isRequirementMet ? (
                      <Check className="w-3 h-3 text-green-500" />
                    ) : (
                      <X className="w-3 h-3 text-gray-400" />
                    )}
                  </div>
                  <span className={isRequirementMet ? 'text-green-700' : 'text-gray-600'}>
                    {req}
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="mt-2">
          <ul className="text-xs text-red-600 space-y-1">
            {errors.map((error, index) => (
              <li key={index} className="flex items-center">
                <X className="w-3 h-3 mr-1 flex-shrink-0" />
                {error}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Success Message */}
      {isValid && strength.level === 'strong' && (
        <div className="mt-2 text-xs text-green-600 flex items-center">
          <Check className="w-3 h-3 mr-1" />
          Excellent! Your password is strong and secure.
        </div>
      )}
    </div>
  );
}

/**
 * Enhanced password input with built-in strength indicator
 */
interface PasswordInputWithStrengthProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  name?: string;
  required?: boolean;
  showRequirements?: boolean;
  showGenerator?: boolean;
  className?: string;
  onConfirmPasswordChange?: (value: string) => void; // New prop to update confirm password field
}

export function PasswordInputWithStrength({
  value,
  onChange,
  placeholder = "Enter password",
  name = "password",
  required = false,
  showRequirements = true,
  showGenerator = false,
  className = '',
  onConfirmPasswordChange
}: PasswordInputWithStrengthProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [generatedPassword, setGeneratedPassword] = useState('');

  const handleGeneratePassword = () => {
    // Import the generate function dynamically to avoid SSR issues
    import('@/utils/password-validation').then(({ generateSecurePassword }) => {
      const newPassword = generateSecurePassword(16);
      setGeneratedPassword(newPassword);
      setShowGenerateDialog(true);
    });
  };

  const handleConfirmGeneration = () => {
    onChange(generatedPassword);
    // Also update the confirm password field if the callback is provided
    if (onConfirmPasswordChange) {
      onConfirmPasswordChange(generatedPassword);
    }
    setShowGenerateDialog(false);
    setGeneratedPassword('');
  };

  const handleCancelGeneration = () => {
    setShowGenerateDialog(false);
    setGeneratedPassword('');
  };

  const handleRegeneratePassword = () => {
    // Generate a new password
    import('@/utils/password-validation').then(({ generateSecurePassword }) => {
      const newPassword = generateSecurePassword(16);
      setGeneratedPassword(newPassword);
    });
  };

  return (
    <div className={className}>
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          name={name}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          minLength={8}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-20"
        />
        <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
          {showGenerator && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleGeneratePassword}
              className="h-6 w-6 p-0 hover:bg-gray-100"
              title="Generate secure password"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setShowPassword(!showPassword)}
            className="h-6 w-6 p-0 hover:bg-gray-100"
            title={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <EyeOff className="h-3 w-3" />
            ) : (
              <Eye className="h-3 w-3" />
            )}
          </Button>
        </div>
      </div>

      <PasswordStrengthIndicator
        password={value}
        showRequirements={showRequirements}
        className="mt-2"
      />

      {/* Password Generation Confirmation Dialog */}
      <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Generate Secure Password</DialogTitle>
            <DialogDescription>
              A strong password has been generated for you. This will replace both your password and confirm password fields.
              You can regenerate a new password if you'd like a different one.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Generated Password:</span>
                <div className="flex items-center gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRegeneratePassword}
                    className="h-6 w-6 p-0"
                    title="Generate new password"
                  >
                    <RefreshCw className="h-3 w-3" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                    className="h-6 w-6 p-0"
                    title={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                </div>
              </div>
              <div className="mt-2 font-mono text-sm break-all">
                {showPassword ? generatedPassword : '•'.repeat(generatedPassword.length)}
              </div>
            </div>

            {/* Show strength indicator for generated password */}
            <PasswordStrengthIndicator
              password={generatedPassword}
              showRequirements={false}
            />
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancelGeneration}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleConfirmGeneration}
              className="bg-green-600 hover:bg-green-700"
            >
              Use This Password
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

/**
 * Helper function to check if a specific requirement is met
 */
function getRequirementStatus(requirement: string, password: string): boolean {
  if (requirement.includes('8 characters')) {
    return password.length >= 8;
  }
  if (requirement.includes('uppercase')) {
    return /[A-Z]/.test(password);
  }
  if (requirement.includes('lowercase')) {
    return /[a-z]/.test(password);
  }
  if (requirement.includes('numbers')) {
    return /\d/.test(password);
  }
  if (requirement.includes('special characters')) {
    return /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/.test(password);
  }
  if (requirement.includes('common patterns')) {
    return !/(.)\1{2,}/.test(password) &&
           !/123456|password|qwerty|abc123|admin|letmein/i.test(password);
  }
  return false;
}
