import React from 'react';
import { EdgeProps, getBezierPath } from '@xyflow/react';

// A simpler floating edge that doesn't rely on the store
export default function FloatingEdge({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  markerEnd,
  style = {},
}: EdgeProps) {
  // Create a bezier path between the nodes
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <path
      id={id}
      className="react-flow__edge-path"
      d={edgePath}
      markerEnd={markerEnd}
      style={{
        ...style,
        strokeWidth: 3,
        stroke: '#9333ea', // Purple color to match the notification button
        strokeDasharray: '5,5', // Dashed line for visual distinction
      }}
    />
  );
}
