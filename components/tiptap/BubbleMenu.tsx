import { BubbleMenu as TiptapBubbleMenu, BubbleMenuProps as TiptapBubbleMenuProps } from '@tiptap/react';
import { Bold, Italic, Underline, Strikethrough, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BubbleMenuProps extends Omit<TiptapBubbleMenuProps, 'children'> {}

const BubbleMenu = (props: BubbleMenuProps) => {
  const { editor } = props;

  if (!editor) {
    return null;
  }

  return (
    <TiptapBubbleMenu
      {...props}
      tippyOptions={{ duration: 100 }}
      className="flex bg-white shadow-md rounded-md border p-1"
    >
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
        className={editor.isActive('bold') ? 'bg-muted' : ''}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        className={editor.isActive('italic') ? 'bg-muted' : ''}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        className={editor.isActive('underline') ? 'bg-muted' : ''}
      >
        <Underline className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleStrike().run()}
        className={editor.isActive('strike') ? 'bg-muted' : ''}
      >
        <Strikethrough className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => editor.chain().focus().toggleCode().run()}
        className={editor.isActive('code') ? 'bg-muted' : ''}
      >
        <Code className="h-4 w-4" />
      </Button>
    </TiptapBubbleMenu>
  );
};

export default BubbleMenu;