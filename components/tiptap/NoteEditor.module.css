.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Add spacing between paragraphs */
.tiptap p {
  margin-bottom: 1em;
}

/* Add spacing after headings */
.tiptap h1, .tiptap h2, .tiptap h3 {
  margin-bottom: 0.75em;
  margin-top: 1em;
}

/* Add spacing between list items */
.tiptap ul, .tiptap ol {
  margin-bottom: 1em;
}

/* Ensure the editor has proper spacing */
.tiptap {
  line-height: 1.5;
}
