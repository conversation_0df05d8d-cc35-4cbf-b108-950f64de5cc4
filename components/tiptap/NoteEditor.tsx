import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import ListKeymap from '@tiptap/extension-list-keymap';
import Placeholder from '@tiptap/extension-placeholder';
import { Typography } from '@tiptap/extension-typography';
import './NoteEditor.module.css';
import styles from './NoteEditor.module.css';
import { useEffect } from 'react';
import MenuBar from './MenuBar';
import BubbleMenu from './BubbleMenu';
import { Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import Paragraph from '@tiptap/extension-paragraph';

interface NoteEditorProps {
  content: string | null;
  onChange: (content: string) => void;
  editable?: boolean;
  onMaximize?: () => void;
}

const NoteEditor = ({ content, onChange, editable = true, onMaximize }: NoteEditorProps) => {
  // Parse content only if it's a string, otherwise use empty string
  const parseContent = (content: any) => {
    if (!content) return '';
    if (typeof content === 'string') {
      try {
        return JSON.parse(content);
      } catch (e) {
        console.error('Invalid JSON content:', e);
        return '';
      }
    }
    return content; // Already an object
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
        },
        blockquote: {}, // Fix: Use empty object instead of true
      }),
      Underline,
      Typography,
      Paragraph.configure({
        HTMLAttributes: {
          class: 'mb-4', // Add margin-bottom to paragraphs
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      ListKeymap,
      Placeholder.configure({
        placeholder: 'Start typing your note...',
        emptyEditorClass: 'is-editor-empty',
      }),
    ],
    content: parseContent(content),
    immediatelyRender: false,
    editable,
    onUpdate: ({ editor }) => {
      onChange(JSON.stringify(editor.getJSON()));
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl focus:outline-none p-4 min-h-[200px]',
      },
    },
  });

  useEffect(() => {
    if (editor && content && !editor.isDestroyed) {
      const newContent = parseContent(content);
      const currentContent = editor.getJSON();
      
      // Only update if content has changed to avoid cursor jumping
      if (JSON.stringify(newContent) !== JSON.stringify(currentContent)) {
        editor.commands.setContent(newContent);
      }
    }
  }, [content, editor]);

  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <div className="border rounded-md relative h-full flex flex-col">
      <MenuBar editor={editor} />
      {editor && <BubbleMenu editor={editor} />}
      <ScrollArea className="flex-1">
        <EditorContent 
          editor={editor} 
          className={`min-h-[300px] ${styles.tiptap}`} 
        />
      </ScrollArea>
      
      {onMaximize && (
        <Button 
          variant="ghost" 
          size="sm" 
          className="absolute top-2 right-2 p-1" 
          onClick={onMaximize}
        >
          <Maximize2 className="h-4 w-4" />
          <span className="sr-only">Maximise editor</span>
        </Button>
      )}
    </div>
  );
};

export default NoteEditor;
