.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.ProseMirror {
  min-height: 200px;
  padding: 1rem;
}

.ProseMirror:focus {
  outline: none;
}

.ProseMirror h1 {
  font-size: 2em;
  font-weight: bold;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
}

.ProseMirror h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 1em;
}

.ProseMirror blockquote {
  border-left: 4px solid #ccc;
  margin-left: 0;
  margin-right: 0;
  padding-left: 1em;
}

.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5em;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5em;
}