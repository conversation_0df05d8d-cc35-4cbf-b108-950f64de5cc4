'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface NotificationPreference {
  type: string;
  enabled: boolean;
}

export default function NotificationPreferences() {
  const [preferences, setPreferences] = useState<NotificationPreference[]>([
    // Mention notifications
    { type: 'task_mention', enabled: true },
    { type: 'interaction_mention', enabled: true },

    // Form submission notifications
    { type: 'discovery_form_submission', enabled: true },
    { type: 'tpa_form_submission', enabled: true },
    { type: 'toe_form_submission', enabled: true },
    { type: 'risk_profiler_submission', enabled: true },

    // Calendar notifications
    { type: 'calendar_meeting_invite', enabled: true },
    { type: 'calendar_meeting_response', enabled: true },
    { type: 'calendar_meeting_reminder', enabled: true },
    { type: 'calendar_event_update', enabled: true },
    { type: 'calendar_event_cancellation', enabled: true }
  ]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    fetchPreferences();
  }, []);

  const fetchPreferences = async () => {
    try {
      setLoading(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get user profile to check for notification preferences
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('notification_preferences')
        .eq('user_id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError;
      }

      if (profileData?.notification_preferences) {
        // If preferences exist, use them
        const savedPreferences = profileData.notification_preferences;

        // Merge saved preferences with default preferences
        const mergedPreferences = preferences.map(defaultPref => {
          const savedPref = savedPreferences.find((p: NotificationPreference) => p.type === defaultPref.type);
          return savedPref || defaultPref;
        });

        setPreferences(mergedPreferences);
      }
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      toast.error('Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async (updatedPreferences: NotificationPreference[]) => {
    try {
      setSaving(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // First, get the user's profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (profileError) {
        throw profileError;
      }

      if (!profileData) {
        throw new Error('Profile not found');
      }

      // Update user profile with notification preferences
      const { error } = await supabase
        .from('profiles')
        .update({ notification_preferences: updatedPreferences })
        .eq('id', profileData.id);

      if (error) {
        throw error;
      }

      toast.success('Notification preferences saved');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (type: string, enabled: boolean) => {
    const updatedPreferences = preferences.map(pref =>
      pref.type === type ? { ...pref, enabled } : pref
    );
    setPreferences(updatedPreferences);
    savePreferences(updatedPreferences);
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      // Mention notifications
      case 'task_mention':
        return 'Task Mentions';
      case 'interaction_mention':
        return 'Interaction Mentions';

      // Form submission notifications
      case 'discovery_form_submission':
        return 'Discovery Form Submissions';
      case 'tpa_form_submission':
        return 'TPA Form Submissions';
      case 'toe_form_submission':
        return 'TOE Form Submissions';
      case 'risk_profiler_submission':
        return 'Risk Profiler Submissions';

      // Calendar notifications
      case 'calendar_meeting_invite':
        return 'Calendar Meeting Invites';
      case 'calendar_meeting_response':
        return 'Calendar Meeting Responses';
      case 'calendar_meeting_reminder':
        return 'Calendar Meeting Reminders';
      case 'calendar_event_update':
        return 'Calendar Event Updates';
      case 'calendar_event_cancellation':
        return 'Calendar Event Cancellations';

      default:
        return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Group preferences by category
  const mentionPrefs = preferences.filter(pref => pref.type.includes('mention'));
  const formPrefs = preferences.filter(pref => pref.type.includes('form') || pref.type.includes('profiler'));
  const calendarPrefs = preferences.filter(pref => pref.type.includes('calendar'));

  // Render a preference card
  const renderPreferenceCard = (pref: NotificationPreference) => (
    <Card key={pref.type}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor={`${pref.type}-toggle`}>{getNotificationTypeLabel(pref.type)}</Label>
            <p className="text-sm text-muted-foreground">
              {pref.type.includes('mention')
                ? 'Receive notifications when you are mentioned'
                : pref.type.includes('calendar')
                  ? `Receive notifications for ${getNotificationTypeLabel(pref.type).toLowerCase()}`
                  : `Receive notifications about ${getNotificationTypeLabel(pref.type).toLowerCase()}`}
            </p>
          </div>
          <Switch
            id={`${pref.type}-toggle`}
            checked={pref.enabled}
            onCheckedChange={(checked) => handleToggle(pref.type, checked)}
            disabled={saving}
          />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      {/* Mention Notifications */}
      <div>
        <h3 className="text-lg font-medium mb-4">Mention Notifications</h3>
        <div className="space-y-4">
          {mentionPrefs.map(renderPreferenceCard)}
        </div>
      </div>

      {/* Calendar Notifications */}
      <div>
        <h3 className="text-lg font-medium mb-4">Calendar Notifications</h3>
        <div className="space-y-4">
          {calendarPrefs.map(renderPreferenceCard)}
        </div>
      </div>

      {/* Form Submission Notifications */}
      <div>
        <h3 className="text-lg font-medium mb-4">Form Submission Notifications</h3>
        <div className="space-y-4">
          {formPrefs.map(renderPreferenceCard)}
        </div>
      </div>
    </div>
  );
}
