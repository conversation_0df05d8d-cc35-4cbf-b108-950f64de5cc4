'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Progress } from '@/components/ui/progress';
import { format } from 'date-fns';
import { riskQuestions10Q, riskQuestions25Q } from '@/app/risk-profiler-form/questions';
import {
  AlertTriangle,
  BarChart2,
  CheckCircle2,
  ChevronRight,
  ShieldAlert,
  ShieldCheck
} from 'lucide-react';

interface CurrentRiskProfileProps {
  profile: any;
  getRiskCategory: (score: number) => { category: string; color: string };
  getQuestionById: (questionId: string, profilerType: string) => any;
  getScoreForAnswer: (questionId: string, answerText: string, profilerType: string) => number | null;
  riskScore?: number; // Add this new optional property
}

export const RiskMeter = ({ score, getRiskCategory }: { score: number; getRiskCategory: (score: number) => { category: string; color: string } }) => {
  const { category, color } = getRiskCategory(score);

  const riskLevels = [
    { label: 'Very Conservative', min: 0, max: 20, color: 'bg-blue-500' },
    { label: 'Conservative', min: 20, max: 40, color: 'bg-cyan-500' },
    { label: 'Moderate', min: 40, max: 60, color: 'bg-green-500' },
    { label: 'Growth', min: 60, max: 80, color: 'bg-amber-500' },
    { label: 'Aggressive', min: 80, max: 100, color: 'bg-red-500' }
  ];

  // Find the current risk level based on score
  const currentLevel = riskLevels.find(level =>
    score >= level.min && score < level.max
  ) || riskLevels[riskLevels.length - 1];

  // Calculate the progress to the end of the current level
  const progressToEnd = currentLevel.max;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <BarChart2 className="w-5 h-5 text-muted-foreground" />
          <span className="text-sm font-medium">Risk Score: {Math.round(score)}</span>
        </div>
        <div className="flex items-center space-x-2">
          {category === 'Very Conservative' && <ShieldCheck className="w-5 h-5 text-blue-500" />}
          {category === 'Conservative' && <ShieldCheck className="w-5 h-5 text-cyan-500" />}
          {category === 'Moderate' && <CheckCircle2 className="w-5 h-5 text-green-500" />}
          {category === 'Growth' && <AlertTriangle className="w-5 h-5 text-amber-500" />}
          {category === 'Aggressive' && <ShieldAlert className="w-5 h-5 text-red-500" />}
          <span className="text-sm font-medium">{category}</span>
        </div>
      </div>

      <div className="relative h-5 w-full rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800">
        <div
          className={`h-full ${currentLevel.color} transition-all duration-500 ease-in-out`}
          style={{ width: `${progressToEnd}%` }}
        />

        {/* Risk level markers */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          {riskLevels.map((level, index) => (
            <div
              key={level.label}
              className="absolute top-0 h-full border-l border-white/70"
              style={{ left: `${level.min}%` }}
            />
          ))}
        </div>
      </div>

      {/* Centered labels */}
      <div className="relative w-full text-xs text-muted-foreground">
        {riskLevels.map((level, index) => {
          const width = level.max - level.min;
          const center = level.min + (width / 2);

          return (
            <div
              key={level.label}
              className="absolute transform -translate-x-1/2 text-center"
              style={{ left: `${center}%`, width: `${Math.min(width, 20)}%` }}
            >
              {level.label}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export function CurrentRiskProfile({ profile, getRiskCategory, getQuestionById, getScoreForAnswer, riskScore }: CurrentRiskProfileProps) {
  // Use the provided riskScore if available, otherwise use the original score
  const scoreToUse = riskScore !== undefined ? riskScore : profile.risk_score;
  const { category, color } = getRiskCategory(scoreToUse);
  const riskCategoryColor = color.replace('bg-', 'text-');

  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="border-b">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <span>Current Risk Profile</span>
              <span className={`text-xs px-2 py-1 rounded-full ${riskCategoryColor} bg-opacity-20 bg-current`}>
                {category}
              </span>
            </CardTitle>
            <CardDescription className="mt-1">
              {profile.template_id ? 'Custom' : (profile.profiler_type === '10q' ? '10-Question' : '25-Question')} Assessment •
              Completed on {format(new Date(profile.completed_at), 'PPP')}
            </CardDescription>
          </div>
          <div className={`p-2 rounded-full ${color} bg-opacity-20`}>
            <BarChart2 className={`w-6 h-6 ${riskCategoryColor}`} />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 pt-4">
        <RiskMeter score={scoreToUse} getRiskCategory={getRiskCategory} />

        {profile.responses && (
          <Accordion type="single" collapsible className="w-full pt-10 mt-10">
            <AccordionItem value="responses" className="border-none">
              <AccordionTrigger className="text-md font-semibold hover:no-underline group">
                <div className="flex items-center space-x-2">
                  <ChevronRight className="w-4 h-4 transition-transform group-data-[state=open]:rotate-90" />
                  <span>Response Summary</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="bg-muted/50 p-4 rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">
                      Total Questions Answered
                    </p>
                      {Object.keys(profile.responses.riskResponses || {}).length}
                  </div>

                  {profile.responses?.riskResponses && (
                    <>
                      {console.log('Profile responses:', profile.responses)}
                    <div className="space-y-3 max-h-[600px] overflow-y-auto">
                      {(() => {
                        // Get the questions in the correct order
                        let orderedQuestions: any[] = [];

                        // For custom templates, use the customQuestions array
                        if (profile.template_id && profile.responses.customQuestions) {
                          console.log('Using custom questions:', profile.responses.customQuestions);

                          // Check if customQuestions is an array
                          if (Array.isArray(profile.responses.customQuestions)) {
                            // Make sure each question has the necessary properties
                            orderedQuestions = profile.responses.customQuestions.map((q: { type: string; includeInScore: boolean; }) => ({
                              ...q,
                              // Ensure type is set
                              type: q.type || 'radio',
                              // Ensure includeInScore is set correctly
                              includeInScore: q.type === 'textarea' ? false : (q.includeInScore !== false)
                            }));
                          } else {
                            console.error('customQuestions is not an array:', profile.responses.customQuestions);
                          }
                        }
                        // For standard templates, use the default questions
                        else {
                          const defaultQuestions = profile.profiler_type === '10q' ? riskQuestions10Q : riskQuestions25Q;
                          // Only include questions that have responses
                          orderedQuestions = defaultQuestions.filter(q =>
                            profile.responses.riskResponses[q.id] !== undefined
                          );
                        }

                        // Special case for textarea questions
                        // Look for any question IDs in the responses that start with 'question_'
                        // These are likely to be textarea questions
                        const textareaQuestionIds = Object.keys(profile.responses.riskResponses || {})
                          .filter(id => id.startsWith('question_'));

                        // For each textarea question ID, check if it's already in orderedQuestions
                        // If not, create a new question object for it
                        textareaQuestionIds.forEach(id => {
                          const exists = orderedQuestions.some(q => q.id === id);
                          if (!exists) {
                            console.log('Adding missing textarea question:', id);
                            orderedQuestions.push({
                              id: id,
                              question: 'Text Area Question',
                              type: 'textarea',
                              options: [],
                              includeInScore: false
                            });
                          }
                        });

                        return orderedQuestions.map((question, index) => {
                          const questionId = question.id;
                          const answerText = profile.responses.riskResponses[questionId];
                          const score = getScoreForAnswer(questionId, answerText, profile.profiler_type);

                          if (answerText === undefined) return null;

                          console.log('Rendering question:', questionId, question.type, answerText);

                          return (
                            <div
                              key={questionId}
                              className="bg-background p-3 rounded-md border hover:bg-muted/50 transition-colors"
                            >
                              <div className="flex justify-between items-start">
                                <p className="font-medium text-sm flex-grow pr-2">
                                  {index + 1}. {question.question}
                                </p>
                                <p className="text-sm font-normal min-w-16">
                                  {question.type === 'textarea' || question.includeInScore === false ? (
                                    <span className="text-muted-foreground italic">Not scored</span>
                                  ) : (
                                    <>Score: {score}</>
                                  )}
                                </p>
                              </div>
                              <div className="text-sm text-muted-foreground mt-1 ml-4">
                                {question.type === 'textarea' ? (
                                  <span className="whitespace-pre-wrap">Answer: {answerText}</span>
                                ) : question.type === 'checkbox' ? (
                                  <div>
                                    <span className="font-medium">Selected options:</span>
                                    {Array.isArray(answerText) ? (
                                      <ul className="list-disc pl-5 mt-1 space-y-1">
                                        {answerText.map((text, i) => (
                                          <li key={i}>{text}</li>
                                        ))}
                                      </ul>
                                    ) : (
                                      <span> {answerText}</span>
                                    )}
                                  </div>
                                ) : (
                                  <>Answer: {answerText}</>
                                )}
                              </div>
                              {(question.type === 'textarea' || question.includeInScore === false) && (
                                <p className="text-xs text-muted-foreground mt-1 ml-4 italic">
                                  This response was not included in the risk score calculation.
                                </p>
                              )}
                            </div>
                          );
                        });
                      })()}
                    </div>
                    </>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}
      </CardContent>
    </Card>
  );
}
