'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function VerifyMFA() {
  const [factors, setFactors] = useState<any[]>([]);
  const [selectedFactorId, setSelectedFactorId] = useState('');
  const [challengeId, setChallengeId] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    const fetchFactors = async () => {
      try {
        // Get the authenticated user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          console.error('Error getting user:', userError);
          throw userError;
        }

        // Check if MFA is required
        const { data: aalData, error: aalError } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

        if (aalError) {
          console.error('Error checking AAL:', aalError);
          throw aalError;
        }

        console.log('AAL data:', aalData);

        // If MFA is not required or already verified, redirect to protected page
        if (aalData.currentLevel === 'aal2' || aalData.nextLevel !== 'aal2') {
          console.log('MFA not required or already verified, redirecting to protected page');
          router.push('/protected');
          return;
        }

        // Get available factors
        const { data, error } = await supabase.auth.mfa.listFactors();

        if (error) {
          console.error('Error listing factors:', error);
          throw error;
        }

        console.log('Factors data:', data);

        // Combine all factor types
        const allFactors = [...(data.totp || []), ...(data.phone || [])];
        setFactors(allFactors);

        // If there's only one factor, select it automatically
        if (allFactors.length === 1) {
          console.log('Auto-selecting the only factor:', allFactors[0]);
          setSelectedFactorId(allFactors[0].id);
          createChallenge(allFactors[0].id);
        } else if (allFactors.length === 0) {
          console.log('No factors found, redirecting to protected page');
          // If no factors are found, just redirect to protected page
          router.push('/protected');
          return;
        }
      } catch (err: any) {
        console.error('Error in fetchFactors:', err);
        setError(err.message || 'Failed to load MFA factors');
        // If there's an error, wait a bit and redirect to protected page
        setTimeout(() => router.push('/protected'), 3000);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchFactors();
  }, [router]);

  const createChallenge = async (factorId: string): Promise<string | null> => {
    try {
      setLoading(true);
      setError('');

      console.log('Creating challenge for factor:', factorId);

      const { data, error } = await supabase.auth.mfa.challenge({
        factorId
      });

      if (error) {
        console.error('Error creating challenge:', error);
        throw error;
      }

      console.log('Challenge created:', data);
      setChallengeId(data.id);
      return data.id;
    } catch (err: any) {
      console.error('Error in createChallenge:', err);
      setError(err.message || 'Failed to create challenge');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const onVerifyClicked = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('Verifying factor:', selectedFactorId, 'with challenge:', challengeId);

      // If we don't have a challenge ID yet, create one
      let currentChallengeId = challengeId;
      if (!currentChallengeId) {
        console.log('No challenge ID, creating a new challenge');
        const challenge = await createChallenge(selectedFactorId);
        currentChallengeId = challenge || '';
      }

      if (!currentChallengeId) {
        throw new Error('Failed to create challenge');
      }

      console.log('Using challenge ID:', currentChallengeId);

      // Verify the code
      const { data, error } = await supabase.auth.mfa.verify({
        factorId: selectedFactorId,
        challengeId: currentChallengeId,
        code: verifyCode,
      });

      if (error) {
        console.error('Error verifying code:', error);
        throw error;
      }

      console.log('Verification successful:', data);

      // Successfully verified, redirect to protected page
      router.push('/protected');
    } catch (err: any) {
      console.error('Error in onVerifyClicked:', err);
      setError(err.message || 'Failed to verify code');
      setLoading(false);
    }
  };

  const getFactorName = (factor: any) => {
    if (factor.factor_type === 'totp') {
      return `Authenticator (${factor.friendly_name || 'Default'})`;
    } else if (factor.factor_type === 'phone') {
      return `Phone (${factor.phone})`;
    }
    return factor.friendly_name || 'Unknown';
  };

  if (initialLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Two-Factor Authentication</CardTitle>
        <CardDescription>
          Enter the verification code to continue
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          {factors.length > 1 && (
            <div>
              <Label htmlFor="factorSelect">Select authentication method</Label>
              <select
                id="factorSelect"
                value={selectedFactorId}
                onChange={(e) => {
                  setSelectedFactorId(e.target.value);
                  setChallengeId('');
                  createChallenge(e.target.value);
                }}
                className="w-full p-2 border rounded mt-1"
              >
                <option value="" disabled>Select a method</option>
                {factors.map((factor) => (
                  <option key={factor.id} value={factor.id}>
                    {getFactorName(factor)}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div>
            <Label htmlFor="verifyCode">Verification code</Label>
            <Input
              id="verifyCode"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={6}
              placeholder="Enter 6-digit code"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value.replace(/[^0-9]/g, ''))}
              className="mt-1"
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          onClick={onVerifyClicked}
          disabled={loading || !selectedFactorId || verifyCode.length !== 6}
        >
          {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Verify
        </Button>
      </CardFooter>
    </Card>
  );
}
