'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ShieldCheck, Shield, AlertTriangle, Loader2 } from 'lucide-react';
import { getMFAStatus, getMFAStatusMessage, type MFAStatus } from '@/utils/mfa-security';

interface MFAStatusIndicatorProps {
  showDetails?: boolean;
  className?: string;
}

export function MFAStatusIndicator({ showDetails = false, className = '' }: MFAStatusIndicatorProps) {
  const [mfaStatus, setMfaStatus] = useState<MFAStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchMFAStatus = async () => {
      try {
        setLoading(true);
        setError('');
        const status = await getMFAStatus();
        setMfaStatus(status);
      } catch (err: any) {
        console.error('Error fetching MFA status:', err);
        setError(err.message || 'Failed to check MFA status');
      } finally {
        setLoading(false);
      }
    };

    fetchMFAStatus();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span className="text-sm text-muted-foreground">Checking security status...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>Unable to check MFA status: {error}</AlertDescription>
      </Alert>
    );
  }

  if (!mfaStatus) {
    return null;
  }

  const getStatusBadge = () => {
    if (mfaStatus.isVerified) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <ShieldCheck className="h-3 w-3 mr-1" />
          MFA Active
        </Badge>
      );
    }

    if (mfaStatus.isEnrolled && mfaStatus.needsChallenge) {
      return (
        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          MFA Required
        </Badge>
      );
    }

    if (mfaStatus.isEnrolled) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
          <Shield className="h-3 w-3 mr-1" />
          MFA Enrolled
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-200">
        <Shield className="h-3 w-3 mr-1" />
        MFA Not Set Up
      </Badge>
    );
  };

  const getStatusIcon = () => {
    if (mfaStatus.isVerified) {
      return <ShieldCheck className="h-4 w-4 text-green-500" />;
    }

    if (mfaStatus.needsChallenge) {
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }

    if (mfaStatus.isEnrolled) {
      return <Shield className="h-4 w-4 text-blue-500" />;
    }

    return <Shield className="h-4 w-4 text-gray-400" />;
  };

  if (!showDetails) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon()}
        {getStatusBadge()}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-sm">
          {getStatusIcon()}
          <span className="ml-2">Security Status</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Two-Factor Authentication</span>
            {getStatusBadge()}
          </div>
          
          <p className="text-xs text-muted-foreground">
            {getMFAStatusMessage(mfaStatus)}
          </p>

          {mfaStatus.isEnrolled && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Current Level:</span>
                <Badge variant="outline" className="text-xs">
                  {mfaStatus.currentLevel?.toUpperCase() || 'Unknown'}
                </Badge>
              </div>
              
              {mfaStatus.nextLevel && mfaStatus.nextLevel !== mfaStatus.currentLevel && (
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Required Level:</span>
                  <Badge variant="outline" className="text-xs">
                    {mfaStatus.nextLevel.toUpperCase()}
                  </Badge>
                </div>
              )}

              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Enrolled Factors:</span>
                <span className="font-medium">{mfaStatus.factors.length}</span>
              </div>
            </div>
          )}

          {mfaStatus.needsChallenge && (
            <Alert className="border-yellow-200 bg-yellow-50">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-xs text-yellow-800">
                MFA verification is required to access sensitive areas of your account.
              </AlertDescription>
            </Alert>
          )}

          {!mfaStatus.isEnrolled && (
            <Alert className="border-blue-200 bg-blue-50">
              <Shield className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-xs text-blue-800">
                Set up two-factor authentication to better protect your financial data.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Simple inline MFA status indicator for headers/navigation
 */
export function MFAStatusBadge({ className = '' }: { className?: string }) {
  return <MFAStatusIndicator showDetails={false} className={className} />;
}

/**
 * Detailed MFA status card for settings/security pages
 */
export function MFAStatusCard({ className = '' }: { className?: string }) {
  return <MFAStatusIndicator showDetails={true} className={className} />;
}
