'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield } from 'lucide-react';
import { logMFAEvent } from '@/utils/mfa-security';

interface EnrollMFAProps {
  onEnrolled: () => void;
  onCancelled: () => void;
}

export function EnrollMFA({ onEnrolled, onCancelled }: EnrollMFAProps) {
  const [factorId, setFactorId] = useState('');
  const [qrCode, setQrCode] = useState('');
  const [secret, setSecret] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [enrolling, setEnrolling] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const enrollFactor = async () => {
      try {
        setLoading(true);

        // First, check if there are any existing factors
        const { data: factorsData, error: factorsError } = await supabase.auth.mfa.listFactors();

        if (factorsError) {
          console.error('Error listing factors:', factorsError);
          throw new Error('Could not check existing MFA factors');
        }

        console.log('Existing factors:', factorsData);

        // Check for existing factors that might conflict
        const existingTotpFactors = factorsData.totp || [];
        const verifiedFactors = existingTotpFactors.filter(f => f.status === 'verified');

        // If there are verified factors, show a message and cancel
        if (verifiedFactors.length > 0) {
          setError('You already have MFA enabled. Please disable it first before setting up a new one.');
          setTimeout(() => onCancelled(), 3000);
          return;
        }

        // Unenroll ALL existing factors to avoid conflicts
        for (const factor of existingTotpFactors) {
          console.log('Unenrolling existing factor:', factor.id, factor.friendly_name);
          try {
            await supabase.auth.mfa.unenroll({ factorId: factor.id });
            console.log('Successfully unenrolled factor:', factor.id);
          } catch (unenrollError) {
            console.error('Error unenrolling factor:', unenrollError);
            // Continue with other factors even if one fails
          }
        }

        // Generate a unique friendly name with timestamp
        const timestamp = new Date().getTime();
        const friendlyName = `Authenticator_${timestamp}`;

        console.log('Enrolling with friendly name:', friendlyName);

        // Now try to enroll with the unique friendly name
        const { data, error } = await supabase.auth.mfa.enroll({
          factorType: 'totp',
          friendlyName: friendlyName,
        });

        if (error) {
          console.error('MFA enrollment error:', error);
          throw error;
        }

        logMFAEvent('MFA Enrollment Started', { factorId: data.id, factorType: 'totp' });
        setFactorId(data.id);
        setQrCode(data.totp.qr_code);
        setSecret(data.totp.secret);
        setEnrolling(false);
      } catch (err: any) {
        console.error('MFA enrollment error:', err);
        setError(err.message || 'Failed to start MFA enrollment');
      } finally {
        setLoading(false);
      }
    };

    enrollFactor();
  }, [onCancelled]);

  const onVerifyClicked = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('Verifying factor:', factorId);

      // Create a challenge
      const { data: challengeData, error: challengeError } = await supabase.auth.mfa.challenge({
        factorId
      });

      if (challengeError) {
        console.error('Error creating challenge:', challengeError);
        throw challengeError;
      }

      console.log('Challenge created:', challengeData);
      const challengeId = challengeData.id;

      // Verify the code
      const { data, error } = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code: verifyCode,
      });

      if (error) {
        console.error('Error verifying code:', error);
        throw error;
      }

      logMFAEvent('MFA Enrollment Completed', { factorId, success: true });

      // Update the profile to set mfa_enabled to true
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ mfa_enabled: true })
            .eq('user_id', user.id);

          if (updateError) {
            console.error('Error updating profile:', updateError);
          } else {
            console.log('Updated profile: mfa_enabled set to true');
          }
        }
      } catch (profileError) {
        console.error('Error updating profile:', profileError);
        // Continue even if profile update fails
      }

      // Successfully enrolled
      onEnrolled();
    } catch (err: any) {
      console.error('Error in onVerifyClicked:', err);
      setError(err.message || 'Failed to verify code');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="h-5 w-5 mr-2 text-blue-500" />
          Set up Two-Factor Authentication
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account with an authenticator app.
          This helps protect your financial data from unauthorized access.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {enrolling ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="space-y-4">
              <div className="flex justify-center mb-4">
                {qrCode && (
                  <div
                    dangerouslySetInnerHTML={{ __html: qrCode }}
                    className="border border-border p-4 rounded-md bg-white"
                  />
                )}
              </div>

              <div className="text-sm text-muted-foreground mb-4">
                <p>1. Scan the QR code with your authenticator app (Google Authenticator, Microsoft Authenticator, Authy, etc.)</p>
                <p>2. Enter the 6-digit code from your app below</p>
              </div>

              {secret && (
                <div className="mb-4">
                  <Label htmlFor="secret">Manual entry code:</Label>
                  <Input
                    id="secret"
                    value={secret}
                    readOnly
                    onClick={(e) => e.currentTarget.select()}
                    className="font-mono mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    If you can't scan the QR code, enter this code manually in your app.
                  </p>
                </div>
              )}

              <div>
                <Label htmlFor="verifyCode">Verification code</Label>
                <Input
                  id="verifyCode"
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={6}
                  placeholder="Enter 6-digit code"
                  value={verifyCode}
                  onChange={(e) => setVerifyCode(e.target.value.replace(/[^0-9]/g, ''))}
                  className="mt-1"
                />
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancelled} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={onVerifyClicked}
          disabled={loading || verifyCode.length !== 6 || enrolling}
        >
          {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Verify and Enable
        </Button>
      </CardFooter>
    </Card>
  );
}
