'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface UnenrollMFAProps {
  onUnenrolled?: () => void;
}

export function UnenrollMFA({ onUnenrolled }: UnenrollMFAProps) {
  const [factors, setFactors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedFactorId, setSelectedFactorId] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUnenrolling, setIsUnenrolling] = useState(false);
  const supabase = createClient();

  const fetchFactors = async () => {
    try {
      setLoading(true);

      // Get the authenticated user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('Error getting user:', userError);
        throw userError;
      }

      const { data, error } = await supabase.auth.mfa.listFactors();

      if (error) {
        console.error('Error listing factors:', error);
        throw error;
      }

      console.log('Factors data:', data);

      // Combine all factor types
      const allFactors = [...(data.totp || []), ...(data.phone || [])];
      setFactors(allFactors.filter(factor => factor.status === 'verified'));
    } catch (err: any) {
      console.error('Error fetching factors:', err);
      setError(err.message || 'Failed to load MFA factors');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFactors();
  }, []);

  const handleUnenroll = async () => {
    try {
      setIsUnenrolling(true);
      setError('');

      // Get the authenticated user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('Error getting user:', userError);
        throw userError;
      }

      console.log('Unenrolling factor:', selectedFactorId);

      const { error } = await supabase.auth.mfa.unenroll({
        factorId: selectedFactorId,
      });

      if (error) {
        console.error('Error unenrolling factor:', error);
        throw error;
      }

      console.log('Successfully unenrolled factor');

      // Close dialog and refresh factors
      setIsDialogOpen(false);
      await fetchFactors();

      // Check if this was the last factor
      const { data: updatedData } = await supabase.auth.mfa.listFactors();
      const remainingFactors = [...(updatedData?.totp || []), ...(updatedData?.phone || [])];
      const hasVerifiedFactors = remainingFactors.some(factor => factor.status === 'verified');

      // If no more verified factors, update the profile to set mfa_enabled to false
      if (!hasVerifiedFactors) {
        try {
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            await supabase
              .from('profiles')
              .update({ mfa_enabled: false })
              .eq('user_id', user.id);

            console.log('Updated profile: mfa_enabled set to false');
          }
        } catch (profileError) {
          console.error('Error updating profile:', profileError);
          // Continue even if profile update fails
        }
      }

      // Call callback if provided
      if (onUnenrolled) {
        onUnenrolled();
      }
    } catch (err: any) {
      console.error('Error in handleUnenroll:', err);
      setError(err.message || 'Failed to remove MFA factor');
    } finally {
      setIsUnenrolling(false);
    }
  };

  const getFactorName = (factor: any) => {
    if (factor.factor_type === 'totp') {
      return `Authenticator (${factor.friendly_name || 'Default'})`;
    } else if (factor.factor_type === 'phone') {
      return `Phone (${factor.phone})`;
    }
    return factor.friendly_name || 'Unknown';
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Two-Factor Authentication</CardTitle>
          <CardDescription>
            Manage your two-factor authentication methods
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          ) : factors.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              <Shield className="mx-auto h-12 w-12 mb-2 opacity-20" />
              <p>No two-factor authentication methods enabled</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground mb-2">
                Your account is protected with the following methods:
              </p>

              <div className="space-y-2">
                {factors.map((factor) => (
                  <div
                    key={factor.id}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div>
                      <p className="font-medium">{getFactorName(factor)}</p>
                      <p className="text-xs text-muted-foreground">
                        Added on {new Date(factor.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setSelectedFactorId(factor.id);
                        setIsDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Two-Factor Authentication</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this two-factor authentication method?
              This will make your account less secure.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              disabled={isUnenrolling}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleUnenroll}
              disabled={isUnenrolling}
            >
              {isUnenrolling ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
