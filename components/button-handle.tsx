import React, { ReactNode } from 'react';
import { Handle, HandleProps, Position } from '@xyflow/react';

interface ButtonHandleProps extends Omit<HandleProps, 'position'> {
  position: Position;
  showButton?: boolean;
  children: ReactNode;
}

export const ButtonHandle = ({
  position,
  showButton = true,
  children,
  ...handleProps
}: ButtonHandleProps) => {
  // Calculate the positioning styles based on the position
  const getPositionStyles = () => {
    switch (position) {
      case Position.Top:
        return {
          top: '-20px',
          left: '50%',
          transform: 'translate(-50%, 0)',
        };
      case Position.Right:
        return {
          top: '50%',
          right: '-20px',
          transform: 'translate(0, -50%)',
        };
      case Position.Bottom:
        return {
          bottom: '-20px',
          left: '50%',
          transform: 'translate(-50%, 0)',
        };
      case Position.Left:
        return {
          top: '50%',
          left: '-20px',
          transform: 'translate(0, -50%)',
        };
      default:
        return {};
    }
  };

  return (
    <div
      className="absolute z-10"
      style={{
        ...getPositionStyles(),
        pointerEvents: 'all',
      }}
    >
      {/* The actual handle is hidden but still functional for connections */}
      <Handle
        position={position}
        style={{ opacity: 0, width: '1px', height: '1px' }}
        {...handleProps}
      />
      
      {/* Only show the button if showButton is true */}
      {showButton && (
        <div className="pointer-events-auto">
          {children}
        </div>
      )}
    </div>
  );
};
