'use client';

import { useMemo, useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';
import { InputData } from '@/app/protected/planner/types';
import { ForwardedRef, forwardRef } from 'react';

const ReactApexChartDynamic = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface PropertyProps {
  allMetrics: any[];
  inputData: InputData | null;
  isExpanded?: boolean;
  chartConfig?: ChartConfig;
  onShowRepaymentsChange?: (showRepayments: boolean) => void;
  ref?: ForwardedRef<any>;
  dataReady?: boolean;
}

export const Property = forwardRef(({
  allMetrics,
  inputData,
  isExpanded,
  chartConfig,
  onShowRepaymentsChange,
  dataReady = true
}: PropertyProps, ref) => {
  const [separateCharts, setSeparateCharts] = useState<boolean>(false);
  const [showRepayments, setShowRepayments] = useState<boolean>(inputData?.show_repayments || false);

  useEffect(() => {
    if (inputData && inputData.show_repayments !== undefined) {
      setShowRepayments(inputData.show_repayments);
    }
  }, [inputData?.show_repayments]);

  const handleShowRepaymentsChange = (value: boolean) => {
    setShowRepayments(value);
    if (onShowRepaymentsChange) {
      onShowRepaymentsChange(value);
    }
  };

  const propertyValueSeries = useMemo(() => {
    // Start with main property (Property 1)
    const series = [
      {
        name: inputData?.property_title || 'Property 1',
        type: 'area',
        data: allMetrics.map(item => item['Property Value'])
      }
    ];

    // Add Property 2-5 only if they exist (have non-zero values)
    if (inputData?.property_value2) {
      series.push({
        name: inputData?.property_title2 || 'Property 2',
        type: 'area',
        data: allMetrics.map(item => item['Property Value 2'] || 0)
      });
    }

    if (inputData?.property_value3) {
      series.push({
        name: inputData?.property_title3 || 'Property 3',
        type: 'area',
        data: allMetrics.map(item => item['Property Value 3'] || 0)
      });
    }

    if (inputData?.property_value4) {
      series.push({
        name: inputData?.property_title4 || 'Property 4',
        type: 'area',
        data: allMetrics.map(item => item['Property Value 4'] || 0)
      });
    }

    if (inputData?.property_value5) {
      series.push({
        name: inputData?.property_title5 || 'Property 5',
        type: 'area',
        data: allMetrics.map(item => item['Property Value 5'] || 0)
      });
    }

    return series;
  }, [allMetrics, inputData]);

  const debtValueSeries = useMemo(() => {
    // Start with main property (Property 1)
    const series = [
      {
        name: `${inputData?.property_title || 'Property 1'} (Debt)`,
        type: 'area',
        data: allMetrics.map(item => item['Debt Value'])
      }
    ];

    // Add Property 2-5 debt only if the properties exist
    if (inputData?.property_value2) {
      series.push({
        name: `${inputData?.property_title2 || 'Property 2'} (Debt)`,
        type: 'area',
        data: allMetrics.map(item => item['Debt Value 2'] || 0)
      });
    }

    if (inputData?.property_value3) {
      series.push({
        name: `${inputData?.property_title3 || 'Property 3'} (Debt)`,
        type: 'area',
        data: allMetrics.map(item => item['Debt Value 3'] || 0)
      });
    }

    if (inputData?.property_value4) {
      series.push({
        name: `${inputData?.property_title4 || 'Property 4'} (Debt)`,
        type: 'area',
        data: allMetrics.map(item => item['Debt Value 4'] || 0)
      });
    }

    if (inputData?.property_value5) {
      series.push({
        name: `${inputData?.property_title5 || 'Property 5'} (Debt)`,
        type: 'area',
        data: allMetrics.map(item => item['Debt Value 5'] || 0)
      });
    }

    return series;
  }, [allMetrics, inputData]);

  const repaymentSeries = useMemo(() => {
    const series = [];

    // Property 1 - Combine principal and interest into a single stacked column
    series.push({
      name: `${inputData?.property_title || 'Property 1'} (Interest)`,
      type: 'column',
      data: allMetrics.map(item => item['Annual Interest Payments'])
    });

    series.push({
      name: `${inputData?.property_title || 'Property 1'} (Principal)`,
      type: 'column',
      data: allMetrics.map(item => item['Annual Principal Repayments'])
    });

    // Property 2 - Only add if it exists
    if (inputData?.property_value2) {
      series.push({
        name: `${inputData?.property_title2 || 'Property 2'} (Interest)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Interest Payments 2'] || 0)
      });

      series.push({
        name: `${inputData?.property_title2 || 'Property 2'} (Principal)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Principal Repayments 2'] || 0)
      });
    }

    // Property 3 - Only add if it exists
    if (inputData?.property_value3) {
      series.push({
        name: `${inputData?.property_title3 || 'Property 3'} (Interest)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Interest Payments 3'] || 0)
      });

      series.push({
        name: `${inputData?.property_title3 || 'Property 3'} (Principal)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Principal Repayments 3'] || 0)
      });
    }

    // Property 4 - Only add if it exists
    if (inputData?.property_value4) {
      series.push({
        name: `${inputData?.property_title4 || 'Property 4'} (Interest)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Interest Payments 4'] || 0)
      });

      series.push({
        name: `${inputData?.property_title4 || 'Property 4'} (Principal)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Principal Repayments 4'] || 0)
      });
    }

    // Property 5 - Only add if it exists
    if (inputData?.property_value5) {
      series.push({
        name: `${inputData?.property_title5 || 'Property 5'} (Interest)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Interest Payments 5'] || 0)
      });

      series.push({
        name: `${inputData?.property_title5 || 'Property 5'} (Principal)`,
        type: 'column',
        data: allMetrics.map(item => item['Annual Principal Repayments 5'] || 0)
      });
    }

    return series;
  }, [allMetrics, inputData]);

  const series = useMemo(() => {
    if (showRepayments) {
      return repaymentSeries;
    }

    // When not showing repayments, return property value series
    return propertyValueSeries;
  }, [showRepayments, propertyValueSeries, repaymentSeries]);

  const chartColors = useMemo(() => {
    const colors = ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0']; // Base colors for properties
    const repaymentColors = ['#99C4E6', '#99E6C4', '#E6D499', '#E69999', '#C499E6', '#D9E699', '#99E6E6', '#E699C4', '#99B3E6', '#E6B399']; // Colors for repayments

    const result: string[] = [];

    if (showRepayments) {
      // Add colors for repayments
      result.push(repaymentColors[0], repaymentColors[1]); // Property 1 repayments always exist
      if (inputData?.property_value2) result.push(repaymentColors[2], repaymentColors[3]);
      if (inputData?.property_value3) result.push(repaymentColors[4], repaymentColors[5]);
      if (inputData?.property_value4) result.push(repaymentColors[6], repaymentColors[7]);
      if (inputData?.property_value5) result.push(repaymentColors[8], repaymentColors[9]);
    } else {
      // Add colors for property values
      result.push(colors[0]); // Property 1 value always exists
      if (inputData?.property_value2) result.push(colors[1]);
      if (inputData?.property_value3) result.push(colors[2]);
      if (inputData?.property_value4) result.push(colors[3]);
      if (inputData?.property_value5) result.push(colors[4]);
    }

    return result;
  }, [inputData, showRepayments]);

  const debtColors = useMemo(() => {
    const colors = ['#0063B1', '#00A06A', '#B17800', '#B13046', '#5A4499']; // Darker colors for debts

    const result: string[] = [];

    // Add colors for debt values
    result.push(colors[0]); // Property 1 debt always exists
    if (inputData?.property_value2) result.push(colors[1]);
    if (inputData?.property_value3) result.push(colors[2]);
    if (inputData?.property_value4) result.push(colors[3]);
    if (inputData?.property_value5) result.push(colors[4]);

    return result;
  }, [inputData]);

  const chartOptions = useMemo(() => {
    return {
      chart: {
        type: 'area' as const,
        stacked: false,
        height: 350,
        zoom: {
          enabled: true
        },
        toolbar: {
          show: true,
          offsetX: 10,
          offsetY: 0,
          tools: {
            download: true,
            selection: false,
            zoom: true,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false,
            customIcons: []
          }
        },
        animations: {
          enabled: true,
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      colors: chartColors,
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'monotoneCubic' as const,
        width: 2,
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 0.45,
          opacityTo: 0.05,
          stops: [20, 100, 100, 100]
        }
      },
      title: {
        text: showRepayments ? 'Property Repayments' : '',
        align: 'center' as 'center',
        style: {
          fontSize: '14px',
          fontWeight: 'bold'
        }
      },
      grid: {
        borderColor: '#e0e0e0',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: 0.5
        }
      },
      markers: {
        size: 0
      },
      xaxis: {
        categories: allMetrics.map(item => item.Age),
        title: {
          text: 'Age'
        }
      },
      yaxis: {
        title: {
          text: showRepayments ? 'Repayment Amount ($)' : 'Property Value ($)'
        },
        labels: {
          formatter: (value: number) => {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value);
          }
        }
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: (value: number) => {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value);
          }
        }
      },
      legend: {
        position: 'top' as const,
        horizontalAlign: 'center' as const,
        offsetY: 0,
        fontSize: '12px',
        fontFamily: 'Helvetica, Arial',
        itemMargin: {
          horizontal: 10,
          vertical: 5
        },
        onItemClick: {
          toggleDataSeries: true
        },
        onItemHover: {
          highlightDataSeries: true
        }
      }
    };
  }, [allMetrics, showRepayments, chartColors]);

  const debtOptions = useMemo(() => {
    return {
      ...chartOptions,
      colors: debtColors,
      title: {
        text: '',
        align: 'center' as 'center',
        style: {
          fontSize: '14px',
          fontWeight: 'bold'
        }
      },
      yaxis: {
        title: {
          text: 'Debt Value ($)'
        },
        labels: {
          formatter: (value: number) => {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value);
          }
        }
      }
    };
  }, [chartOptions, debtColors]);

  const repaymentOptions = useMemo(() => {
    return {
      ...chartOptions,
      chart: {
        ...chartOptions.chart,
        type: 'bar' as const,
        stacked: true
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          endingShape: 'rounded',
          borderRadius: 2,
          borderRadiusApplication: 'end' as const, // Fix type by using 'as const'
          borderWidth: 0, // Remove border by setting width to 0
        },
      },
      fill: {
        opacity: 0.7, // Reduce opacity but not too much (0.5 → 0.7)
        type: 'solid'
      },
      title: {
        text: 'Property Repayments',
        align: 'center' as 'center',
        style: {
          fontSize: '14px',
          fontWeight: 'bold'
        }
      }
    };
  }, [chartOptions]);

  return (
    <>
      <div className="w-full h-full relative">
        <div className="absolute right-12 top-2 z-10 flex items-center gap-4 text-xs text-gray-500">
          <label className="flex items-center gap-1 cursor-pointer">
            <input
              type="checkbox"
              checked={showRepayments}
              onChange={() => handleShowRepaymentsChange(!showRepayments)}
              className="h-3 w-3"
            />
            Show Repayments
          </label>
          {!showRepayments && (
            <label className="flex items-center gap-1 cursor-pointer">
              <input
                type="checkbox"
                checked={separateCharts}
                onChange={() => setSeparateCharts(!separateCharts)}
                className="h-3 w-3"
              />
              Show Debt
            </label>
          )}
        </div>

        {!separateCharts || showRepayments ? (
          // Single chart view
          <div className="absolute inset-[5px]">
            <ReactApexChartDynamic
              key={`${isExpanded ? 'expanded' : 'collapsed'}-combined-${Date.now()}`}
              options={showRepayments ? repaymentOptions : chartOptions}
              series={series}
              type={!showRepayments ? 'area' : 'bar'}
              width="100%"
              height="100%"
              ref={ref as ForwardedRef<any>}
            />
          </div>
        ) : (
          // Split view with property values on top and debt values below
          <div className="absolute inset-[5px] grid grid-rows-2 gap-4">
            <div>
              <ReactApexChartDynamic
                key={`${isExpanded ? 'expanded' : 'collapsed'}-property-${Date.now()}`}
                options={chartOptions}
                series={propertyValueSeries}
                type="area"
                width="100%"
                height="100%"
              />
            </div>
            <div>
              <ReactApexChartDynamic
                key={`${isExpanded ? 'expanded' : 'collapsed'}-debt-${Date.now()}`}
                options={debtOptions}
                series={debtValueSeries}
                type="area"
                width="100%"
                height="100%"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
});

Property.displayName = 'Property';
