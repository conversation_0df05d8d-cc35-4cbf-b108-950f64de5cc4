'use client';

import { useMemo } from 'react';
import dynamic from 'next/dynamic';
import { ChartConfig } from '@/components/ui/chart';
import { InputData } from '@/app/protected/planner/types';
import { ForwardedRef, forwardRef } from 'react';

const ReactApexChartDynamic = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface RepaymentsProps {
  allMetrics: any[];
  inputData: InputData | null;
  isExpanded: boolean;
  chartConfig: ChartConfig;
  ref?: ForwardedRef<any>;
}

export const Repayments = forwardRef(({
  allMetrics,
  inputData,
  isExpanded,
  chartConfig
}: RepaymentsProps, ref) => {
  const series = useMemo(() => [
    {
      name: 'Principal Repayment',
      type: 'column',
      data: allMetrics.map(item => item['Annual Principal Repayments'])
    },
    {
      name: 'Interest Payment',
      type: 'column',
      data: allMetrics.map(item => item['Annual Interest Payments'])
    }
  ], [allMetrics]);

  const options = useMemo(() => ({
    chart: {
      type: 'bar',
      stacked: true,
      toolbar: {
        show: true,
        offsetX: 10,
        offsetY: 0,
        tools: {
          download: true,
          selection: false,
          zoom: true,
          zoomin: false,
          zoomout: false,
          pan: false,
          reset: false,
          customIcons: []
        }
      }
    },
    colors: ['#22C55E', '#EF4444'], // Principal (green), Interest (red)
    dataLabels: {
      enabled: false
    },
    stroke: {
      width: 0
    },
    fill: {
      type: 'solid'
    },
    plotOptions: {
      bar: {
        columnWidth: '70%'
      }
    },
    xaxis: {
      categories: allMetrics.map(item => item.Age),
      labels: {
        show: true,
        rotate: -45,
        style: {
          fontSize: '12px'
        }
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    yaxis: {
      labels: {
        formatter: (value: number) => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }).format(value);
        }
      },
      title: {
        text: 'Annual Payments ($)'
      }
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: (value: number) => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }).format(value);
        }
      }
    },
    legend: {
      position: 'bottom',
      horizontalAlign: 'center'
    }
  }), [allMetrics]);

  return (
    <>
      <div className="w-full h-full relative">
        <div className="absolute inset-[5px]">
          <ReactApexChartDynamic
            key={isExpanded ? 'expanded' : 'collapsed'}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            options={{
              ...options,
              // @ts-ignore - ApexCharts types are incorrect for string coordinates
              chart: {
                ...options.chart,
                parentHeightOffset: 0,
                toolbar: {
                  // @ts-ignore - ApexCharts types are incorrect for string coordinates
                  ...options.chart.toolbar,
                  offsetY: 0,
                },
                animations: {
                  enabled: true
                }
              }
            }}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            series={series}
            type="bar"
            width="100%"
            height="100%"
            ref={ref}
          />
        </div>
      </div>
    </>
  );
});