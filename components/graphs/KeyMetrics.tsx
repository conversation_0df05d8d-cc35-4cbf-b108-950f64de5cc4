'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Wallet, Calendar, PiggyBank } from 'lucide-react';
import { PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';

interface ScenarioData {
  id: number;
  scenario_name: string;
  household_name: string;
  allMetrics: any[];
  color?: string;
  chanceOfSuccess?: number;
  successfulScenarios?: number;
  failedScenarios?: number;
  inputData?: {
    income_period?: [number, number];
    starting_age?: number;
    ending_age?: number;
    [key: string]: any;
  };
}

interface KeyMetricsProps {
  scenarios: ScenarioData[];
}

export default function KeyMetrics({ scenarios }: KeyMetricsProps) {
  // Function to find the retirement age based on when main income ceases
  const findRetirementAge = (metrics: any[], scenario: ScenarioData) => {
    // Get the main income end age from the scenario data if available
    const mainIncomeEndAge = scenario.inputData?.income_period?.[1];

    if (mainIncomeEndAge && metrics.length > 0) {
      // Find the metric closest to the main income end age
      return metrics.reduce((closest, metric) => {
        return Math.abs(metric.Age - mainIncomeEndAge) < Math.abs(closest.Age - mainIncomeEndAge) ? metric : closest;
      }, metrics[0]);
    }

    // Fallback to age 65 if main income end age is not available
    return metrics.find(metric => Math.round(metric.Age) === 65) || metrics[0];
  };

  // Function to find the final age (last entry in metrics)
  const findFinalAge = (metrics: any[]) => {
    return metrics[metrics.length - 1];
  };

  // Function to find the peak net wealth and its age
  const findPeakNetWealth = (metrics: any[]) => {
    let peak = metrics[0];
    for (const metric of metrics) {
      if (metric["Net Wealth"] > peak["Net Wealth"]) {
        peak = metric;
      }
    }
    return peak;
  };

  // Function to find the net wealth in 10 years
  const findTenYearWealth = (metrics: any[]) => {
    const startAge = metrics[0].Age;
    const targetAge = startAge + 10;

    // Find the metric closest to the target age
    return metrics.reduce((closest, metric) => {
      return Math.abs(metric.Age - targetAge) < Math.abs(closest.Age - targetAge) ? metric : closest;
    }, metrics[0]);
  };

  // Function to format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Function to format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Function to calculate growth percentage between two values
  const calculateGrowth = (startValue: number, endValue: number) => {
    if (startValue === 0) return 100;
    return ((endValue - startValue) / startValue) * 100;
  };



  // Function to get badge variant based on scenario index
  const getBadgeVariant = (index: number): "blue" | "green" | "purple" | "yellow" | "default" | "secondary" | "destructive" | "outline" => {
    const variants: ("blue" | "green" | "purple" | "yellow" | "default")[] = ['blue', 'green', 'purple', 'yellow', 'default'];
    return variants[index % variants.length];
  };



  // Calculate retirement income for each scenario
  const getRetirementIncome = (metrics: any[], scenario: ScenarioData) => {
    const retirementMetric = findRetirementAge(metrics, scenario);
    return retirementMetric["Net Income"] || 0;
  };

  return (
    <div className="h-full overflow-auto p-1">
      <div className="grid grid-cols-1 gap-6">
        {/* Scenario Headers with Chance of Success */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {scenarios.map((scenario, index) => {
            const chanceOfSuccess = scenario.chanceOfSuccess || 75; // Default value if not provided

            return (
              <Card key={scenario.id} className="overflow-hidden transition-all duration-300 hover:shadow-md">
                <div
                  className="h-1.5"
                  style={{ backgroundColor: scenario.color || `var(--chart-${index + 1})` }}
                />
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <Badge variant={getBadgeVariant(index)} className="mb-2">
                        Scenario {index + 1}
                      </Badge>
                      <CardTitle className="text-lg">{scenario.scenario_name}</CardTitle>
                      <CardDescription>{scenario.household_name}</CardDescription>
                    </div>
                    <div>
                      {/* Success Rate Pie Chart */}
                      <div className="flex items-center">
                        <div className="w-16 h-16">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Pie
                                data={[
                                  { name: 'Success', value: scenario.successfulScenarios || 0 },
                                  { name: 'Failed', value: scenario.failedScenarios || 0 }
                                ]}
                                cx="50%"
                                cy="50%"
                                innerRadius={15}
                                outerRadius={30}
                                paddingAngle={2}
                                dataKey="value"
                                cornerRadius={3}
                              >
                                {[0, 1].map((entry) => (
                                  <Cell
                                    key={`cell-${entry}`}
                                    fill={entry === 0 ? 'hsl(173,58%,39%)' : 'hsl(12,76%,61%)'}
                                  />
                                ))}
                              </Pie>
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                        <div className="ml-2 text-right">
                          <div className="text-xs text-muted-foreground">Success Rate</div>
                          <div className="text-2xl font-bold" style={{ color: chanceOfSuccess >= 80 ? 'hsl(173,58%,39%)' : 'hsl(12,76%,61%)' }}>
                            {formatPercentage(chanceOfSuccess)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {scenario.successfulScenarios || 0} of {(scenario.successfulScenarios || 0) + (scenario.failedScenarios || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">

                  <div className="grid grid-cols-2 gap-3 mt-2">
                    <div className="flex items-center gap-2">
                      <Wallet className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="text-xs text-muted-foreground">Current</div>
                        <div className="font-medium">{formatCurrency(scenario.allMetrics[0]["Net Wealth"])}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="text-xs text-muted-foreground">Final Age</div>
                        <div className="font-medium">{Math.round(findFinalAge(scenario.allMetrics).Age)}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Wealth Metrics */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Wealth Progression
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {scenarios.map((scenario, index) => {
                const currentWealth = scenario.allMetrics[0]["Net Wealth"];
                const tenYearMetric = findTenYearWealth(scenario.allMetrics);
                const tenYearWealth = tenYearMetric["Net Wealth"];
                const tenYearGrowth = calculateGrowth(currentWealth, tenYearWealth);
                const retirementMetric = findRetirementAge(scenario.allMetrics, scenario);
                const retirementWealth = retirementMetric["Net Wealth"];
                const finalMetric = findFinalAge(scenario.allMetrics);
                const finalWealth = finalMetric["Net Wealth"];
                const peakMetric = findPeakNetWealth(scenario.allMetrics);
                const peakWealth = peakMetric["Net Wealth"];
                const growthToRetirement = calculateGrowth(currentWealth, retirementWealth);

                return (
                  <div
                    key={scenario.id}
                    className="p-4 rounded-lg border bg-card/50"
                    style={{ borderLeftColor: scenario.color || `var(--chart-${index + 1})`, borderLeftWidth: '4px' }}
                  >
                    <div className="font-medium mb-3">{scenario.scenario_name}</div>

                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Current</span>
                          <span className="font-medium">{formatCurrency(currentWealth)}</span>
                        </div>
                        <Progress value={0} className="h-1.5" />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">
                            In 10 Years ({Math.round(tenYearMetric.Age)})
                          </span>
                          <div className="flex items-center">
                            <span className="font-medium">{formatCurrency(tenYearWealth)}</span>
                            <Badge
                              variant={tenYearGrowth >= 0 ? "green" : "destructive"}
                              className="ml-2 text-xs"
                            >
                              {tenYearGrowth >= 0 ? '+' : ''}{formatPercentage(tenYearGrowth)}
                            </Badge>
                          </div>
                        </div>
                        <Progress
                          value={(tenYearWealth / peakWealth) * 100}
                          className="h-1.5"
                        />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">
                            At Retirement ({Math.round(retirementMetric.Age)})
                          </span>
                          <div className="flex items-center">
                            <span className="font-medium">{formatCurrency(retirementWealth)}</span>
                            <Badge
                              variant={growthToRetirement >= 0 ? "green" : "destructive"}
                              className="ml-2 text-xs"
                            >
                              {growthToRetirement >= 0 ? '+' : ''}{formatPercentage(growthToRetirement)}
                            </Badge>
                          </div>
                        </div>
                        <Progress
                          value={(retirementWealth / peakWealth) * 100}
                          className="h-1.5"
                        />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Peak ({Math.round(peakMetric.Age)})</span>
                          <span className="font-medium">{formatCurrency(peakWealth)}</span>
                        </div>
                        <Progress value={100} className="h-1.5" />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Final ({Math.round(finalMetric.Age)})</span>
                          <span className="font-medium">{formatCurrency(finalWealth)}</span>
                        </div>
                        <Progress
                          value={(finalWealth / peakWealth) * 100}
                          className="h-1.5"
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Retirement Assets */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <PiggyBank className="h-5 w-5" />
              Retirement Assets
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Assets at the age when main income ceases for each scenario
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {scenarios.map((scenario, index) => {
                const retirementMetric = findRetirementAge(scenario.allMetrics, scenario);
                const savingsFund = retirementMetric["Savings Fund"] || 0;
                const totalKiwiSaver = (retirementMetric["Main KiwiSaver"] || 0) +
                                      (retirementMetric["Partner KiwiSaver"] || 0);
                const totalInvestments = (retirementMetric["Investment Fund 1"] || 0) +
                                        (retirementMetric["Investment Fund 2"] || 0) +
                                        (retirementMetric["Investment Fund 3"] || 0) +
                                        (retirementMetric["Investment Fund 4"] || 0) +
                                        (retirementMetric["Investment Fund 5"] || 0);
                const totalAssets = savingsFund + totalKiwiSaver + totalInvestments;
                const savingsFundPercentage = totalAssets > 0 ? (savingsFund / totalAssets) * 100 : 0;
                const kiwiSaverPercentage = totalAssets > 0 ? (totalKiwiSaver / totalAssets) * 100 : 0;
                const investmentsPercentage = totalAssets > 0 ? (totalInvestments / totalAssets) * 100 : 0;
                const retirementIncome = getRetirementIncome(scenario.allMetrics, scenario);
                const retirementExpenses = retirementMetric["Total Expenditure"] || 0;

                return (
                  <div
                    key={scenario.id}
                    className="p-4 rounded-lg border bg-card/50"
                    style={{ borderLeftColor: scenario.color || `var(--chart-${index + 1})`, borderLeftWidth: '4px' }}
                  >
                    <div className="font-medium mb-1">{scenario.scenario_name}</div>
                    <div className="text-xs text-muted-foreground mb-2">
                      Retirement Age: {Math.round(retirementMetric.Age)}
                    </div>

                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Total Assets</span>
                          <span className="font-medium">{formatCurrency(totalAssets)}</span>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Savings Fund</span>
                          <div className="flex items-center">
                            <span className="font-medium">{formatCurrency(savingsFund)}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                              ({formatPercentage(savingsFundPercentage)})
                            </span>
                          </div>
                        </div>
                        <Progress
                          value={savingsFundPercentage}
                          className="h-1.5"
                          indicatorClassName="bg-yellow-500"
                        />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">KiwiSaver</span>
                          <div className="flex items-center">
                            <span className="font-medium">{formatCurrency(totalKiwiSaver)}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                              ({formatPercentage(kiwiSaverPercentage)})
                            </span>
                          </div>
                        </div>
                        <Progress
                          value={kiwiSaverPercentage}
                          className="h-1.5"
                          indicatorClassName="bg-blue-500"
                        />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Investments</span>
                          <div className="flex items-center">
                            <span className="font-medium">{formatCurrency(totalInvestments)}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                              ({formatPercentage(investmentsPercentage)})
                            </span>
                          </div>
                        </div>
                        <Progress
                          value={investmentsPercentage}
                          className="h-1.5"
                          indicatorClassName="bg-emerald-500"
                        />
                      </div>

                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  );
}
