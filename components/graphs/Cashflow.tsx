'use client';

import dynamic from 'next/dynamic';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';
import { ForwardedRef, forwardRef, useMemo, useState, useEffect } from 'react';
import { ApexOptions } from 'apexcharts';
import { InputData } from '@/app/protected/planner/types';

const ReactApexChartDynamic = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface CashflowProps {
  allMetrics: any[];
  isExpanded: boolean;
  chartConfig: ChartConfig;
  ref?: ForwardedRef<any>;
  onAgeHover?: (age: number | null) => void;
  inputData?: InputData | null;
  onShowCashflowBreakdownChange?: (showCashflowBreakdown: boolean) => void;
}

export const Cashflow = forwardRef(({
  allMetrics,
  isExpanded,
  chartConfig,
  onAgeHover,
  inputData,
  onShowCashflowBreakdownChange
}: CashflowProps, ref) => {
  const [showCashflowBreakdown, setShowCashflowBreakdown] = useState<boolean>(inputData?.show_cashflow_breakdown || false);

  useEffect(() => {
    if (inputData && inputData.show_cashflow_breakdown !== undefined) {
      setShowCashflowBreakdown(inputData.show_cashflow_breakdown);
    }
  }, [inputData?.show_cashflow_breakdown]);

  const handleShowCashflowBreakdownChange = (value: boolean) => {
    setShowCashflowBreakdown(value);
    if (onShowCashflowBreakdownChange) {
      onShowCashflowBreakdownChange(value);
    }
  };
  const chartOptions: ApexOptions = useMemo(() => {
    const series = [
      {
        name: "Net Income",
        data: allMetrics.map((metric) => ({
          x: metric.Age,
          y: metric['Net Income']
        }))
      },
      {
        name: String(chartConfig.expenses.label),
        data: allMetrics.map((metric) => ({
          x: metric.Age,
          y: metric['Total Expenditure']
        }))
      }
    ];

    return {
      series,
      chart: {
        type: 'bar' as const,
        height: '100%',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false,
            customIcons: []
          }
        },
        animations: {
          enabled: true
        },
        events: {
          mouseMove: function(event, chartContext, config) {
            if (config.dataPointIndex >= 0 && config.seriesIndex >= 0) {
              const age = allMetrics[config.dataPointIndex]?.Age;
              onAgeHover && onAgeHover(age || null);
            }
          },
          mouseLeave: function() {
            onAgeHover && onAgeHover(null);
          }
        }
      },
      dataLabels: {
        enabled: false,
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '80%',
          borderRadius: 4,
          borderRadiusApplication: 'end',
          dataLabels: {
            position: 'top'
          }
        }
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        type: 'numeric',
        title: {
          text: 'Age',
          style: {
            fontSize: '14px'
          }
        },
        tickAmount: Math.min(allMetrics.length),
        labels: {
          formatter: (value: string) => Math.round(Number(value)).toString()
        }
      },
      yaxis: {
        labels: {
          formatter: function(value: number) {
            return new Intl.NumberFormat('en-NZ', {
              style: 'currency',
              currency: 'NZD',
              maximumFractionDigits: 0
            }).format(value);
          }
        }
      },
      colors: [chartConfig.income.color, chartConfig.expenses.color],
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: function(value: number) {
            return new Intl.NumberFormat('en-NZ', {
              style: 'currency',
              currency: 'NZD',
              maximumFractionDigits: 0
            }).format(value);
          }
        },
        marker: {
          show: true
        }
      },
      legend: {
        position: 'bottom',
        fontSize: '14px',
        markers: {
          size: 4,
          shape: 'circle'
        }
      }
    };
  }, [allMetrics, isExpanded, chartConfig, onAgeHover]);

  return (
    <>
      <div className="w-full h-full relative">
        <div className="absolute right-12 top-2 z-10 flex items-center gap-4 text-xs text-gray-500">
          <label className="flex items-center gap-1 cursor-pointer mr-4">
            <input
              type="checkbox"
              checked={showCashflowBreakdown}
              onChange={() => handleShowCashflowBreakdownChange(!showCashflowBreakdown)}
              className="h-3 w-3"
            />
            Show Cashflow Breakdown
          </label>
        </div>
        <div className="absolute inset-[5px]">
          <ReactApexChartDynamic
            key={isExpanded ? 'expanded' : 'collapsed'}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            options={{
              ...chartOptions,
              // @ts-ignore - ApexCharts types are incorrect for string coordinates
              chart: {
                ...chartOptions.chart,
                parentHeightOffset: 0,
                toolbar: {
                  // @ts-ignore - ApexCharts types are incorrect for string coordinates
                  ...chartOptions.chart.toolbar,
                  offsetY: 0,
                },
                animations: {
                  enabled: true
                }
              }
            }}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            series={chartOptions.series}
            type="bar"
            width="100%"
            height="100%"
            ref={ref}
          />
        </div>
      </div>
    </>
  );
});

Cashflow.displayName = 'Cashflow';