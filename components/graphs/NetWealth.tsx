'use client';

import { InputData } from '@/app/protected/planner/types';
import dynamic from 'next/dynamic';
import { useMemo, useRef, useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FUND_COLORS } from '@/app/constants/fundColors';

const ReactApexChartDynamic = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface NetWealthProps {
  allMetrics: any[];
  showAdditionalData: {
    show_savings: boolean;
    show_investment: boolean;
    show_individual_investments?: boolean;
    show_kiwisaver: boolean;
    show_individual_kiwisavers?: boolean;
    show_cashflow: boolean;
    show_monte_carlo: boolean;
    show_realistic_netwealth?: boolean;
  };
  inputData: InputData | null;
  minNetWealthAtAge: number[];
  maxNetWealthAtAge: number[];
  averageNetWealthAtAge: number[];
  realisticInvestmentsFundAtAge?: number[];
  realisticTotalKiwiSaverAtAge?: number[];
  isExpanded: boolean;
  chartConfig: any;
  isAnnotationMode: boolean;
  showAnnotations: boolean;
  onAnnotationComplete: () => void;
  annotations: Array<{
    x: string;
    y: string;
    text: string;
    color: string;
    series: string;
  }>;
  onAnnotationsChange: (annotations: Array<{
    x: string;
    y: string;
    text: string;
    color: string;
    series: string;
  }>) => void;
  mainName?: string;
  dataReady?: boolean;
}

export default function NetWealth({
  allMetrics,
  showAdditionalData,
  inputData,
  minNetWealthAtAge,
  maxNetWealthAtAge,
  averageNetWealthAtAge,
  realisticInvestmentsFundAtAge = [],
  realisticTotalKiwiSaverAtAge = [],
  isExpanded,
  chartConfig,
  isAnnotationMode,
  showAnnotations,
  onAnnotationComplete,
  annotations = [],
  onAnnotationsChange,
  mainName = '',
  dataReady = true
}: NetWealthProps) {
  const [tempAnnotation, setTempAnnotation] = useState<{ x: string; y: string; series?: string } | null>(null);
  const [annotationText, setAnnotationText] = useState('');
  const [annotationColor, setAnnotationColor] = useState('#000000');
  const [selectedSeries, setSelectedSeries] = useState('Net Wealth');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [annotationToDelete, setAnnotationToDelete] = useState<{ x: string; y: string; text: string; color: string; series: string } | null>(null);

  const series = useMemo(() => [
    {
      name: "Net Wealth",
      type: "area",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: showAdditionalData.show_realistic_netwealth ? metric["Realistic Net Wealth"] || 0 : metric["Net Wealth"],
      })),
      color: FUND_COLORS['Net Wealth']
    },
    showAdditionalData.show_monte_carlo && {
      name: "Monte Carlo Max",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: maxNetWealthAtAge[index],
      })),
      color: FUND_COLORS['Monte Carlo Max'],
      opacity: 0.5,
      dashArray: 5,
    },
    showAdditionalData.show_monte_carlo && {
      name: "Monte Carlo Min",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: minNetWealthAtAge[index],
      })),
      color: FUND_COLORS['Monte Carlo Min'],
      opacity: 0.5,
      dashArray: 5,
    },
    showAdditionalData.show_kiwisaver && !showAdditionalData.show_individual_kiwisavers && {
      name: "KiwiSaver",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        // Calculate total KiwiSaver as the sum of individual KiwiSavers to ensure consistency
        y: showAdditionalData.show_realistic_netwealth
          ? metric["Realistic Total KiwiSaver"] || 0
          : (metric["Main KiwiSaver"] || 0) + (metric["Partner KiwiSaver"] || 0)
      })),
      color: FUND_COLORS['KiwiSaver']
    },
    // Show Main KiwiSaver when individual KiwiSavers toggle is enabled
    showAdditionalData.show_kiwisaver && showAdditionalData.show_individual_kiwisavers && {
      name: "Main KiwiSaver",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Main KiwiSaver"] || 0
      })),
      color: "#4F46E5" // Indigo-600
    },
    // Show Partner KiwiSaver when individual KiwiSavers toggle is enabled
    showAdditionalData.show_kiwisaver && showAdditionalData.show_individual_kiwisavers && {
      name: "Partner KiwiSaver",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Partner KiwiSaver"] || 0
      })),
      color: "#8B5CF6" // Violet-500
    },
    // Show total investments when individual investments toggle is off
    showAdditionalData.show_investment && !showAdditionalData.show_individual_investments && {
      name: "Total Investments",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        // Calculate total investments as the sum of individual investment funds to ensure consistency
        y: (metric["Investment Fund 1"] || 0) +
           (metric["Investment Fund 2"] || 0) +
           (metric["Investment Fund 3"] || 0) +
           (metric["Investment Fund 4"] || 0) +
           (metric["Investment Fund 5"] || 0)
      })),
      color: "#0891B2" // Cyan-600
    },
    // Show individual investment funds when the toggle is enabled
    showAdditionalData.show_investment && showAdditionalData.show_individual_investments && inputData?.withdrawal_priorities?.includes(1) && {
      name: (inputData as any)?.investment_description1 || "Investment Fund 1",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Investment Fund 1"] || 0
      })),
      color: FUND_COLORS['Investment Fund 1']
    },
    showAdditionalData.show_investment && showAdditionalData.show_individual_investments && inputData?.withdrawal_priorities?.includes(2) && {
      name: (inputData as any)?.investment_description2 || "Investment Fund 2",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Investment Fund 2"] || 0
      })),
      color: FUND_COLORS['Investment Fund 2']
    },
    showAdditionalData.show_investment && showAdditionalData.show_individual_investments && inputData?.withdrawal_priorities?.includes(3) && {
      name: (inputData as any)?.investment_description3 || "Investment Fund 3",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Investment Fund 3"] || 0
      })),
      color: FUND_COLORS['Investment Fund 3']
    },
    showAdditionalData.show_investment && showAdditionalData.show_individual_investments && inputData?.withdrawal_priorities?.includes(4) && {
      name: (inputData as any)?.investment_description4 || "Investment Fund 4",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Investment Fund 4"] || 0
      })),
      color: FUND_COLORS['Investment Fund 4']
    },
    showAdditionalData.show_investment && showAdditionalData.show_individual_investments && inputData?.withdrawal_priorities?.includes(5) && {
      name: (inputData as any)?.investment_description5 || "Investment Fund 5",
      type: "line",
      data: allMetrics.map((metric: any, index) => ({
        x: Math.round(metric.Age),
        y: metric["Investment Fund 5"] || 0
      })),
      color: FUND_COLORS['Investment Fund 5']
    },
    showAdditionalData.show_savings && {
      name: "Savings",
      type: "line",
      data: allMetrics.map((metric: any) => ({
        x: Math.round(metric.Age),
        y: metric["Savings Fund"]
      })),
      color: FUND_COLORS['Savings']
    }
  ].filter(Boolean), [allMetrics, showAdditionalData, maxNetWealthAtAge, minNetWealthAtAge, inputData]);

  const handleAddAnnotation = () => {
    if (tempAnnotation && annotationText) {
      const age = parseInt(tempAnnotation.x);
      const metric = allMetrics.find(m => m.Age === age);

      if (metric) {
        let yValue;
        // Get the fund number from the selected series if it's a dynamic investment fund title
        const fund1Title = (inputData as any)?.investment_description1 || 'Investment Fund 1';
        const fund2Title = (inputData as any)?.investment_description2 || 'Investment Fund 2';
        const fund3Title = (inputData as any)?.investment_description3 || 'Investment Fund 3';
        const fund4Title = (inputData as any)?.investment_description4 || 'Investment Fund 4';
        const fund5Title = (inputData as any)?.investment_description5 || 'Investment Fund 5';

        switch (selectedSeries) {
          case 'KiwiSaver':
            // Calculate total KiwiSaver as the sum of individual KiwiSavers to ensure consistency
            yValue = showAdditionalData.show_realistic_netwealth ?
              metric['Realistic Total KiwiSaver'] || 0 :
              (metric['Main KiwiSaver'] || 0) + (metric['Partner KiwiSaver'] || 0);
            break;
          case 'Main KiwiSaver':
            yValue = metric['Main KiwiSaver'] || 0;
            break;
          case 'Partner KiwiSaver':
            yValue = metric['Partner KiwiSaver'] || 0;
            break;
          case 'Total Investments':
            // Calculate total investments as the sum of individual investment funds to ensure consistency
            yValue = (metric['Investment Fund 1'] || 0) +
                     (metric['Investment Fund 2'] || 0) +
                     (metric['Investment Fund 3'] || 0) +
                     (metric['Investment Fund 4'] || 0) +
                     (metric['Investment Fund 5'] || 0);
            break;
          // Handle dynamic investment fund titles
          case fund1Title:
            yValue = metric['Investment Fund 1'] || 0;
            break;
          case fund2Title:
            yValue = metric['Investment Fund 2'] || 0;
            break;
          case fund3Title:
            yValue = metric['Investment Fund 3'] || 0;
            break;
          case fund4Title:
            yValue = metric['Investment Fund 4'] || 0;
            break;
          case fund5Title:
            yValue = metric['Investment Fund 5'] || 0;
            break;
          case 'Savings':
            yValue = metric['Savings Fund'];
            break;
          default:
            yValue = showAdditionalData.show_realistic_netwealth ?
              metric['Realistic Net Wealth'] :
              metric['Net Wealth'];
        }

        const newAnnotation = {
          x: tempAnnotation.x,
          y: yValue.toString(),
          text: annotationText,
          color: annotationColor,
          series: selectedSeries
        };
        onAnnotationsChange([...annotations, newAnnotation]);
        setTempAnnotation(null);
        setAnnotationText('');
        setAnnotationColor('#000000');
        setSelectedSeries('Net Wealth');
        setIsDialogOpen(false);
        onAnnotationComplete();
      }
    }
  };

  const handleDeleteAnnotation = () => {
    if (annotationToDelete) {
      const newAnnotations = annotations.filter(
        ann => !(ann.x === annotationToDelete.x && ann.y === annotationToDelete.y && ann.text === annotationToDelete.text)
      );
      onAnnotationsChange(newAnnotations);
      setAnnotationToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const options = useMemo(() => ({
    chart: {
      height: isExpanded ? '100%' : '100%',
      type: "line",
      stacked: false,
      animations: {
        enabled: true,
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350
        }
      },
      events: {
        click: function(event: any, chartContext: any, config: any) {
          if (isAnnotationMode && config.dataPointIndex >= 0) {
            const metric = allMetrics[config.dataPointIndex];
            setTempAnnotation({
              x: Math.round(metric.Age).toString(),
              y: metric["Net Wealth"].toString()
            });
            setIsDialogOpen(true);
          }
        },
        markerClick: function(event: any, chartContext: any, config: any) {
          const target = event.target;
          if (!target) return;

          // Check if we clicked on an annotation marker
          const classes = target.getAttribute('class')?.split(' ') || [];
          const markerClass = classes.find((c: string) => c.startsWith('annotation-marker-'));

          if (markerClass) {
            const index = parseInt(markerClass.split('-')[2]);
            const annotation = annotations[index];

            if (annotation) {
              setAnnotationToDelete(annotation);
              setIsDeleteDialogOpen(true);
            }
          }
        }
      },
      toolbar: {
        show: true,
        offsetX: 10,
        offsetY: 0,
        tools: {
          download: true,
          selection: false,
          zoom: true,
          zoomin: false,
          zoomout: false,
          pan: false,
          reset: false,
          customIcons: []
        }
      }
    },
    // We're now setting colors directly in the series objects
    stroke: {
      width: [2, 2, 2, 2, 2, 2, 2, 2, 2, 2,],
      curve: 'monotoneCubic',
      dashArray: [0, 0, 0, 0]
    },
    fill: {
      type: ['gradient', 'solid', 'solid', 'solid'],
      gradient: {
        shade: 'light',
        type: "vertical",
        shadeIntensity: 0.5,
        opacityFrom: 0.7,
        opacityTo: 0.2,
        stops: [0, 100]
      }
    },
    dataLabels: {
      enabled: false
    },
    tooltip: {
      shared: true,
      intersect: false,
      x: {
        formatter: function(value: number) {
          return `Age: ${Math.round(value)}`;
        }
      }
    },
    xaxis: {
      type: 'numeric',
      title: {
        text: mainName ? `Age - ${mainName}` : 'Age'
      },
      labels: {
        formatter: function(value: any) {
          return Math.round(value).toString();
        }
      },
      tickAmount: allMetrics.length > 20 ? 20 : allMetrics.length,
      tickPlacement: 'on',
      axisTicks: {
        show: true
      },
      axisBorder: {
        show: true
      },
      crosshairs: {
        show: true,
        position: 'back',
        stroke: {
          color: '#b6b6b6',
          width: 1,
          dashArray: 0
        }
      }
    },
    yaxis: {
      title: {
        text: ''
      },
      labels: {
        formatter: function(value: any) {
          return new Intl.NumberFormat('en-NZ', {
            style: 'currency',
            currency: 'NZD',
            maximumFractionDigits: 0
          }).format(value);
        }
      },
      min: 0
    },
    annotations: {
      points: showAnnotations ? annotations.map((ann, index) => ({
        x: ann.x,
        y: ann.y,
        marker: {
          size: 8,
          fillColor: ann.color,
          strokeColor: '#fff',
          radius: 2,
          cssClass: `annotation-marker-${index} cursor-pointer`
        },
        label: {
          borderColor: ann.color,
          style: {
            color: '#000', // Changed to black text
            background: '#e5e5e5', // Grey interior background
            fontSize: '10px', // xs text size
            padding: {
              left: 5,
              right: 5,
              top: 2,
              bottom: 2
            }
          },
          text: ann.text,
          textAnchor: 'start',
          offsetX: 8,
          offsetY: 0,
          minWidth: 30, // Minimum width
          minHeight: 20 // Minimum height
        }
      })) : []
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          height: 200
        },
        xaxis: {
          labels: {
            show: true
          }
        }
      }
    }]
  }), [allMetrics, isExpanded, annotations, isAnnotationMode, showAnnotations, showAdditionalData, mainName]);

  const availableSeries = ['Net Wealth'];
  if (showAdditionalData.show_kiwisaver) {
    if (showAdditionalData.show_individual_kiwisavers) {
      availableSeries.push('Main KiwiSaver');
      availableSeries.push('Partner KiwiSaver');
    } else {
      availableSeries.push('KiwiSaver');
    }
  }
  if (showAdditionalData.show_investment) {
    if (showAdditionalData.show_individual_investments) {
      // Only include funds that are in the withdrawal priorities list
      const withdrawalPriorities = inputData?.withdrawal_priorities || [];

      // Check if we have any investment funds with values that are in the priority list
      const hasInvestmentFund1 = withdrawalPriorities.includes(1) && allMetrics.some((metric: any) => (metric["Investment Fund 1"] || 0) > 0);
      const hasInvestmentFund2 = withdrawalPriorities.includes(2) && allMetrics.some((metric: any) => (metric["Investment Fund 2"] || 0) > 0);
      const hasInvestmentFund3 = withdrawalPriorities.includes(3) && allMetrics.some((metric: any) => (metric["Investment Fund 3"] || 0) > 0);
      const hasInvestmentFund4 = withdrawalPriorities.includes(4) && allMetrics.some((metric: any) => (metric["Investment Fund 4"] || 0) > 0);
      const hasInvestmentFund5 = withdrawalPriorities.includes(5) && allMetrics.some((metric: any) => (metric["Investment Fund 5"] || 0) > 0);

      // Add available funds to the series list with dynamic titles
      if (hasInvestmentFund1) availableSeries.push((inputData as any)?.investment_description1 || 'Investment Fund 1');
      if (hasInvestmentFund2) availableSeries.push((inputData as any)?.investment_description2 || 'Investment Fund 2');
      if (hasInvestmentFund3) availableSeries.push((inputData as any)?.investment_description3 || 'Investment Fund 3');
      if (hasInvestmentFund4) availableSeries.push((inputData as any)?.investment_description4 || 'Investment Fund 4');
      if (hasInvestmentFund5) availableSeries.push((inputData as any)?.investment_description5 || 'Investment Fund 5');
    } else {
      // Add total investments as a single series
      availableSeries.push('Total Investments');
    }
  }
  if (showAdditionalData.show_savings) availableSeries.push('Savings');

  // Use our shared color constants for annotation colors
  const colorPresets = {
    Blue: FUND_COLORS['Investment Fund 2'],
    Green: FUND_COLORS['Investment Fund 3'],
    Purple: FUND_COLORS['Investment Fund 4'],
    Red: FUND_COLORS['Investment Fund 5'],
    Orange: FUND_COLORS['Investment Fund 1']
  };

  useEffect(() => {
    const event = new CustomEvent('annotationsUpdated', {
      detail: annotations
    });
    window.dispatchEvent(event);
  }, [annotations]);

  // Add this effect to update annotation y-values when data changes
  useEffect(() => {
    if (annotations.length > 0) {
      const updatedAnnotations = annotations.map(ann => {
        const age = parseInt(ann.x);
        const metric = allMetrics.find(m => Math.round(m.Age) === age);

        if (metric) {
          let yValue;
          // Get the dynamic fund titles
          const fund1Title = (inputData as any)?.investment_description1 || 'Investment Fund 1';
          const fund2Title = (inputData as any)?.investment_description2 || 'Investment Fund 2';
          const fund3Title = (inputData as any)?.investment_description3 || 'Investment Fund 3';
          const fund4Title = (inputData as any)?.investment_description4 || 'Investment Fund 4';
          const fund5Title = (inputData as any)?.investment_description5 || 'Investment Fund 5';

          switch (ann.series) {
            case 'KiwiSaver':
              // Calculate total KiwiSaver as the sum of individual KiwiSavers to ensure consistency
              yValue = showAdditionalData.show_realistic_netwealth ?
                metric['Realistic Total KiwiSaver'] || 0 :
                (metric['Main KiwiSaver'] || 0) + (metric['Partner KiwiSaver'] || 0);
              break;
            case 'Main KiwiSaver':
              yValue = metric['Main KiwiSaver'] || 0;
              break;
            case 'Partner KiwiSaver':
              yValue = metric['Partner KiwiSaver'] || 0;
              break;
            case 'Total Investments':
              // Calculate total investments as the sum of individual investment funds to ensure consistency
              yValue = (metric['Investment Fund 1'] || 0) +
                       (metric['Investment Fund 2'] || 0) +
                       (metric['Investment Fund 3'] || 0) +
                       (metric['Investment Fund 4'] || 0) +
                       (metric['Investment Fund 5'] || 0);
              break;
            // Handle both static and dynamic investment fund titles for backward compatibility
            case 'Investment Fund 1':
            case fund1Title:
              yValue = metric['Investment Fund 1'] || 0;
              break;
            case 'Investment Fund 2':
            case fund2Title:
              yValue = metric['Investment Fund 2'] || 0;
              break;
            case 'Investment Fund 3':
            case fund3Title:
              yValue = metric['Investment Fund 3'] || 0;
              break;
            case 'Investment Fund 4':
            case fund4Title:
              yValue = metric['Investment Fund 4'] || 0;
              break;
            case 'Investment Fund 5':
            case fund5Title:
              yValue = metric['Investment Fund 5'] || 0;
              break;
            case 'Savings':
              yValue = metric['Savings Fund'];
              break;
            default:
              yValue = showAdditionalData.show_realistic_netwealth ?
                metric['Realistic Net Wealth'] :
                metric['Net Wealth'];
          }

          return {
            ...ann,
            y: yValue.toString()
          };
        }
        return ann;
      });

      // Only update if there are actual changes
      const hasChanges = JSON.stringify(updatedAnnotations) !== JSON.stringify(annotations);
      if (hasChanges) {
        // Use setTimeout to allow animations to complete
        setTimeout(() => {
          onAnnotationsChange(updatedAnnotations);
        }, 300);
      }
    }
  }, [allMetrics, showAdditionalData.show_realistic_netwealth, inputData]);

  return (
    <>
      <div className="w-full h-full relative">
        <div className="absolute inset-[5px]">
          <ReactApexChartDynamic
            key={`${isExpanded ? 'expanded' : 'collapsed'}-${annotations.length}-${Date.now()}`}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            options={{
              ...options,
              // @ts-ignore - ApexCharts types are incorrect for string coordinates
              chart: {
                ...options.chart,
                parentHeightOffset: 0,
                toolbar: {
                  ...options.chart.toolbar,
                  offsetY: 0,
                },
                animations: {
                  enabled: true,
                  speed: 800,
                  animateGradually: {
                    enabled: true,
                    delay: 150
                  },
                  dynamicAnimation: {
                    enabled: true,
                    speed: 350
                  }
                }
              }
            }}
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            series={series}
            type="line"
            width="100%"
            height="100%"
          />
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Annotation</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label htmlFor="annotation-text" className="block text-sm font-medium text-gray-700">
                Annotation Text
              </label>
              <Input
                id="annotation-text"
                value={annotationText}
                onChange={(e) => setAnnotationText(e.target.value)}
                placeholder="Enter your annotation"
                className="mt-1"
              />
            </div>
            <div>
              <label htmlFor="series" className="block text-sm font-medium text-gray-700">
                Select Series
              </label>
              <Select
                value={selectedSeries}
                onValueChange={setSelectedSeries}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select series" />
                </SelectTrigger>
                <SelectContent>
                  {availableSeries.map(series => (
                    <SelectItem key={series} value={series}>
                      {series}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="annotation-color" className="block text-sm font-medium text-gray-700 mb-2">
                Annotation Color
              </label>
              <div className="flex items-center space-x-2">
                {Object.entries(colorPresets).map(([name, color]) => (
                  <button
                    key={name}
                    onClick={() => setAnnotationColor(color)}
                    className={`w-8 h-8 rounded-full border-2 ${
                      annotationColor === color ? 'border-gray-400' : 'border-transparent'
                    }`}
                    style={{ backgroundColor: color }}
                    title={name}
                  />
                ))}
                <Input
                  id="annotation-color"
                  type="color"
                  value={annotationColor}
                  onChange={(e) => setAnnotationColor(e.target.value)}
                  className="h-8 w-8 p-0.5"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => {
                setIsDialogOpen(false);
                setAnnotationText('');
                setAnnotationColor('#000000');
                setSelectedSeries('Net Wealth');
              }}>
                Cancel
              </Button>
              <Button onClick={handleAddAnnotation}>
                Add
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Annotation</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this annotation?
            </DialogDescription>
          </DialogHeader>
          <div className="pt-4">
            <div className="text-sm text-gray-700">
              <p><strong>Text:</strong> {annotationToDelete?.text}</p>
              <p><strong>Series:</strong> {annotationToDelete?.series}</p>
            </div>
          </div>
          <DialogFooter className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAnnotation}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
