'use client';

import { ForwardedRef, forwardRef, useMemo } from 'react';
import { DynamicBarList } from '@/components/ui/dynamicBarList';
import { FinancialMetrics } from '@/app/utils/financialTypes'; // Import FinancialMetrics
import { InputData } from '@/app/protected/planner/types'; // Import InputData
import { Badge } from '@/components/ui/badge';

interface ExpenseBreakdownProps {
  allMetrics: any[];
  selectedAge: number | null;
  inputData?: InputData; // Use InputData type
  mainName?: string;
  partnerName?: string;
}

export const ExpenseBreakdown = forwardRef(({
  allMetrics,
  selectedAge,
  inputData,
  mainName = '',
  partnerName = ''
}: ExpenseBreakdownProps, ref: ForwardedRef<any>) => {
  const selectedYearData: FinancialMetrics | null = useMemo(() => { // Add type annotation
    if (!selectedAge || !allMetrics.length) return null;
    // Assuming allMetrics contains objects conforming to FinancialMetrics
    const foundData = allMetrics.find(metric => metric.Age === selectedAge) || null;
    return foundData;
  }, [allMetrics, selectedAge]);

  const expenseBreakdown = useMemo(() => {
    if (!selectedYearData) return []; // selectedYearData is now FinancialMetrics | null

    if (!selectedYearData) return []; // selectedYearData is now FinancialMetrics | null

    // Define colors for different expense types with an index signature
    const expenseColors: { [key: string]: string } = {
      'Pre Retirement Expenses': '#4B5563', // Gray
      'Post Retirement Expenses': '#6B7280', // Lighter Gray
      'Additional Expenses': '#9CA3AF', // Even Lighter Gray
      'Lump Sum Mortgage Payment': '#DC2626', // Bright Red
      'Lump Sum Mortgage Payment 2': '#F87171', // Lighter Red
      'Lump Sum Mortgage Payment 3': '#FCA5A5', // Even Lighter Red
      'Lump Sum Mortgage Payment 4': '#FEE2E2', // Very Light Red
      'Lump Sum Mortgage Payment 5': '#FECACA', // Pink-ish Red
      'Monthly Mortgage Payment': '#EF4444', // Red
      'Monthly Mortgage Payment 2': '#F87171', // Lighter Red
      'Monthly Mortgage Payment 3': '#FCA5A5', // Even Lighter Red
      'Monthly Mortgage Payment 4': '#FEE2E2', // Very Light Red
      'Monthly Mortgage Payment 5': '#FECACA', // Pink-ish Red
      'KiwiSaver Contributions': '#3B82F6', // Blue
      'Investment Contributions': '#10B981', // Green
      'Income Tax': '#F59E0B', // Amber
      // Default color for additional expenses
      'default': '#8B5CF6' // Purple
    };

    // Create expense items array
    const expenses = [];

    // Pre Retirement Expenses (formerly Basic Expenses 1)
    // Check if the property exists, is a number > 0, and is actually present in the current year
    if (selectedYearData['Basic Expenses 1'] !== undefined &&
        selectedYearData['Basic Expenses 1'] > 0 &&
        inputData?.expense_period1 &&
        selectedAge !== null &&
        selectedAge >= inputData.expense_period1[0] &&
        selectedAge <= inputData.expense_period1[1]) {
      // Create a more descriptive name based on the period
      const periodDescription = inputData.expense_period1[1] <= 65
        ? 'Pre-Retirement Living Expenses'
        : 'Living Expenses';

      expenses.push({
        name: periodDescription,
        value: selectedYearData['Basic Expenses 1'] || 0,
        color: expenseColors['Pre Retirement Expenses']
      });
    }

    // Post Retirement Expenses (formerly Basic Expenses 2)
    // Check if the property exists, is a number > 0, and is actually present in the current year
    if (selectedYearData['Basic Expenses 2'] !== undefined &&
        selectedYearData['Basic Expenses 2'] > 0 &&
        inputData?.second_expense &&
        inputData?.expense_period2 &&
        selectedAge !== null &&
        selectedAge >= inputData.expense_period2[0] &&
        selectedAge <= inputData.expense_period2[1]) {
      // Create a more descriptive name based on the period
      const periodDescription = inputData.expense_period2[0] >= 65
        ? 'Retirement Living Expenses'
        : 'Secondary Living Expenses';

      expenses.push({
        name: periodDescription,
        value: selectedYearData['Basic Expenses 2'] || 0,
        color: expenseColors['Post Retirement Expenses']
      });
    }

    // Additional Expenses
    if (selectedYearData['Additional Expenditure'] > 0) {
      // If there are additional expenses in the inputData, we can try to match them to the current age
      if (inputData?.additional_expenses && inputData.additional_expenses.length > 0) {
        // Group additional expenses by title
        const additionalExpensesByTitle: { [key: string]: number } = {};
        let totalTrackedExpenses = 0;

        // First, identify which expenses are active in the current year
        const activeExpenses = inputData.additional_expenses.filter(expense =>
          selectedAge !== null &&
          expense.period &&
          selectedAge >= expense.period[0] &&
          selectedAge <= expense.period[1]
        );

        // If we have active expenses, distribute the actual inflation-adjusted amount proportionally
        if (activeExpenses.length > 0) {
          // Calculate the total of input values for active expenses
          const totalInputValue = activeExpenses.reduce((sum, expense) => sum + (expense.value || 0), 0);

          // Get the actual inflation-adjusted total from the metrics
          const actualTotal = selectedYearData['Additional Expenditure'] || 0;

          // Distribute the actual total proportionally based on input values
          activeExpenses.forEach(expense => {
            const title = expense.title || 'Additional Expense';
            if (!additionalExpensesByTitle[title]) {
              additionalExpensesByTitle[title] = 0;
            }

            // Calculate this expense's proportion of the actual total
            if (totalInputValue > 0) {
              const proportion = (expense.value || 0) / totalInputValue;
              const inflatedValue = actualTotal * proportion;
              additionalExpensesByTitle[title] += inflatedValue;
              totalTrackedExpenses += inflatedValue;
            }
          });
        }

        // Add each additional expense to the breakdown
        Object.entries(additionalExpensesByTitle).forEach(([title, value]) => {
          if (value > 0) {
            expenses.push({
              name: title,
              value: value,
              color: expenseColors['Additional Expenses']
            });
          }
        });

        // If there's any remaining untracked amount, add it as a separate item
        const untrackedAmount = (selectedYearData['Additional Expenditure'] || 0) - totalTrackedExpenses;
        if (untrackedAmount > 0.01) { // Add a small threshold to account for rounding errors
          expenses.push({
            name: 'Other Additional Expenses',
            value: untrackedAmount,
            color: expenseColors['Additional Expenses']
          });
        }
      } else {
        // If we can't match specific additional expenses, just show the total
        expenses.push({
          name: 'Additional Expenses',
          value: selectedYearData['Additional Expenditure'] || 0,
          color: expenseColors['Additional Expenses']
        });
      }
    }

    // Mortgage Payments (1-5)
    for (let i = 1; i <= 5; i++) {
      const repaymentKey = i === 1 ? 'Annual Debt Repayments' : `Annual Debt Repayments ${i}`;
      // Use optional chaining and nullish coalescing for safe access
      const repaymentValue = selectedYearData?.[repaymentKey as keyof FinancialMetrics] ?? 0;
      if (repaymentValue > 0) {
        // Get property title if available
        const propertyTitleKey = i === 1 ? 'property_title' : `property_title${i}`;
        const propertyTitle = inputData?.[propertyTitleKey as keyof InputData] as string | undefined;

        expenses.push({
          name: propertyTitle
            ? `Mortgage Payment (${propertyTitle})`
            : (i === 1 ? 'Monthly Mortgage Payment' : `Monthly Mortgage Payment ${i}`),
          value: repaymentValue,
          color: expenseColors[i === 1 ? 'Monthly Mortgage Payment' : `Monthly Mortgage Payment ${i}`]
        });
      }
    }

    // Lump Sum Mortgage Payments (if available)
    // Check for lump sum payments in the current year
    const lumpSumValue = selectedYearData['Lump Sum Payment Amount'];
    if (lumpSumValue !== undefined && lumpSumValue > 0) {
      // Get property title if available
      const propertyTitle = inputData?.property_title as string | undefined;

      expenses.push({
        name: propertyTitle
          ? `Lump Sum Payment (${propertyTitle})`
          : 'Lump Sum Mortgage Payment',
        value: lumpSumValue,
        color: expenseColors['Lump Sum Mortgage Payment']
      });
    }

    // Check for property 2-5 lump sum payments
    for (let i = 2; i <= 5; i++) {
      const lumpSumKey = `Lump Sum Payment Amount ${i}` as keyof FinancialMetrics;
      const lumpSumValue = selectedYearData[lumpSumKey];
      if (lumpSumValue !== undefined && lumpSumValue > 0) {
        // Get property title if available
        const propertyTitleKey = `property_title${i}`;
        const propertyTitle = inputData?.[propertyTitleKey as keyof InputData] as string | undefined;

        expenses.push({
          name: propertyTitle
            ? `Lump Sum Payment (${propertyTitle})`
            : `Lump Sum Mortgage Payment ${i}`,
          value: lumpSumValue,
          color: expenseColors['Lump Sum Mortgage Payment']
        });
      }
    }

    // KiwiSaver Contributions
    const mainKiwiSaverContributions = selectedYearData['KiwiSaver Contributions'] || 0;
    const partnerKiwiSaverContributions = selectedYearData['Partner KiwiSaver Contributions'] || 0;

    // Add main KiwiSaver contributions if present
    if (mainKiwiSaverContributions > 0) {
      expenses.push({
        name: mainName
          ? `${mainName}'s KiwiSaver Contributions`
          : (inputData?.savings_owner
              ? `${inputData.savings_owner}'s KiwiSaver Contributions`
              : 'KiwiSaver Contributions'),
        value: mainKiwiSaverContributions,
        color: expenseColors['KiwiSaver Contributions']
      });
    }

    // Add partner KiwiSaver contributions if present
    if (partnerKiwiSaverContributions > 0) {
      expenses.push({
        name: partnerName
          ? `${partnerName}'s KiwiSaver Contributions`
          : (inputData?.partner_name
              ? `${inputData.partner_name}'s KiwiSaver Contributions`
              : 'Partner KiwiSaver Contributions'),
        value: partnerKiwiSaverContributions,
        color: expenseColors['KiwiSaver Contributions']
      });
    }

    // Investment Contributions
    if (selectedYearData['Annual Investment Contribution'] > 0) {
      expenses.push({
        name: inputData?.investment_description
          ? `${inputData.investment_description} Contributions`
          : 'Investment Contributions',
        value: selectedYearData['Annual Investment Contribution'] || 0,
        color: expenseColors['Investment Contributions']
      });
    }

    // Income Tax
    const mainIncomeTax = selectedYearData['Main Income Tax'] || 0;
    const partnerIncomeTax = selectedYearData['Partner Income Tax'] || 0;

    // Rental Income Tax
    const rentalIncomeTax = selectedYearData['Rental Income Tax'] || 0;
    const rentalIncomeTax2 = selectedYearData['Rental Income Tax 2'] || 0;
    const rentalIncomeTax3 = selectedYearData['Rental Income Tax 3'] || 0;
    const rentalIncomeTax4 = selectedYearData['Rental Income Tax 4'] || 0;
    const rentalIncomeTax5 = selectedYearData['Rental Income Tax 5'] || 0;


    // Calculate other taxes (investment, PIE, KiwiSaver tax, etc.)
    // Make sure to subtract all known tax components from the total
    const totalTax = selectedYearData['Income Tax'] || 0;
    const knownTaxes = mainIncomeTax + partnerIncomeTax +
                      rentalIncomeTax + rentalIncomeTax2 + rentalIncomeTax3 +
                      rentalIncomeTax4 + rentalIncomeTax5;

    // Ensure otherTaxes is never negative due to rounding errors
    const otherTaxes = Math.max(0, totalTax - knownTaxes);


    // Main Income Tax
    if (mainIncomeTax > 0) {
      expenses.push({
        name: mainName
          ? `${mainName}'s Income Tax`
          : (inputData?.savings_owner
              ? `${inputData.savings_owner}'s Income Tax`
              : 'Main Income Tax'),
        value: mainIncomeTax,
        color: expenseColors['Income Tax']
      });
    }

    // Partner Income Tax
    if (partnerIncomeTax > 0) {
      expenses.push({
        name: partnerName
          ? `${partnerName}'s Income Tax`
          : (inputData?.partner_name
              ? `${inputData.partner_name}'s Income Tax`
              : 'Partner Income Tax'),
        value: partnerIncomeTax,
        color: expenseColors['Income Tax']
      });
    }

    // Rental Income Tax - Always add rental tax items if they exist in the data
    // This ensures they're displayed even if the value is very small

    // Property 1 Rental Tax
    if ('Rental Income Tax' in selectedYearData) {
      const rentalTax = selectedYearData['Rental Income Tax'];
      const propertyTitle = inputData?.property_title || 'Property 1';
      if (rentalTax > 0) {
        expenses.push({
          name: `${propertyTitle} Rental Tax`,
          value: rentalTax,
          color: expenseColors['Income Tax']
        });
      }
    }

    // Property 2 Rental Tax
    if ('Rental Income Tax 2' in selectedYearData) {
      const rentalTax = selectedYearData['Rental Income Tax 2'];
      const propertyTitle = inputData?.property_title2 || 'Property 2';
      if (rentalTax > 0) {
        expenses.push({
          name: `${propertyTitle} Rental Tax`,
          value: rentalTax,
          color: expenseColors['Income Tax']
        });
      }
    }

    // Property 3 Rental Tax
    if ('Rental Income Tax 3' in selectedYearData) {
      const rentalTax = selectedYearData['Rental Income Tax 3'];
      const propertyTitle = inputData?.property_title3 || 'Property 3';
      if (rentalTax > 0) {
        expenses.push({
          name: `${propertyTitle} Rental Tax`,
          value: rentalTax,
          color: expenseColors['Income Tax']
        });
      }
    }

    // Property 4 Rental Tax
    if ('Rental Income Tax 4' in selectedYearData) {
      const rentalTax = selectedYearData['Rental Income Tax 4'];
      const propertyTitle = inputData?.property_title4 || 'Property 4';
      if (rentalTax > 0) {
        expenses.push({
          name: `${propertyTitle} Rental Tax`,
          value: rentalTax,
          color: expenseColors['Income Tax']
        });
      }
    }

    // Property 5 Rental Tax
    if ('Rental Income Tax 5' in selectedYearData) {
      const rentalTax = selectedYearData['Rental Income Tax 5'];
      const propertyTitle = inputData?.property_title5 || 'Property 5';
      if (rentalTax > 0) {
        expenses.push({
          name: `${propertyTitle} Rental Tax`,
          value: rentalTax,
          color: expenseColors['Income Tax']
        });
      }
    }

    // Other taxes (investment, etc.)
    if (otherTaxes > 0) {
      expenses.push({
        name: 'Other Taxes',
        value: otherTaxes,
        color: expenseColors['Income Tax']
      });
    }

    // Filter out zero values
    const filteredExpenses = expenses.filter(item => item.value > 0);

    // Group expenses by type for better organization
    const livingExpenses = filteredExpenses.filter(item =>
      item.name.includes('Living Expenses') ||
      item.name.includes('Retirement') ||
      item.name.includes('Additional Expenses'));

    const mortgageExpenses = filteredExpenses.filter(item =>
      item.name.includes('Mortgage'));

    const taxExpenses = filteredExpenses.filter(item =>
      item.name.includes('Income Tax') ||
      item.name.includes('Rental Tax') ||
      item.name === 'Other Taxes');

    const contributionExpenses = filteredExpenses.filter(item =>
      item.name.includes('Contributions'));

    const otherExpenses = filteredExpenses.filter(item =>
      !livingExpenses.includes(item) &&
      !mortgageExpenses.includes(item) &&
      !taxExpenses.includes(item) &&
      !contributionExpenses.includes(item));

    // Sort each group by value (descending)
    const sortByValue = (a: any, b: any) => b.value - a.value;
    livingExpenses.sort(sortByValue);
    mortgageExpenses.sort(sortByValue);
    taxExpenses.sort(sortByValue);
    contributionExpenses.sort(sortByValue);
    otherExpenses.sort(sortByValue);

    // Combine all groups in a logical order
    return [
      ...livingExpenses,
      ...mortgageExpenses,
      ...taxExpenses,
      ...contributionExpenses,
      ...otherExpenses
    ];
  }, [selectedYearData]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Calculate total expenses
  const totalExpenses = useMemo(() => {
    return expenseBreakdown.reduce((sum, item) => sum + item.value, 0);
  }, [expenseBreakdown]);

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-medium">
          {selectedAge ? `Expenses at Age ${selectedAge}` : 'Select an age to see expenses'}
        </h3>
        {selectedYearData && expenseBreakdown.length > 0 && (
          <Badge variant="destructive" className="ml-auto">
            {formatCurrency(totalExpenses)}
          </Badge>
        )}
      </div>
      {selectedYearData ? (
        <div className="flex-grow overflow-auto h-[calc(100%-2rem)]">
          <DynamicBarList
            data={expenseBreakdown}
            valueFormatter={formatCurrency}
            maxHeight={expenseBreakdown.length > 8 ? 200 : undefined} // Adjust based on the number of items
            minRowHeight={16} // Minimum row height
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">
          Hover over the chart to see expense breakdown
        </div>
      )}
    </div>
  );
});

ExpenseBreakdown.displayName = 'ExpenseBreakdown';
