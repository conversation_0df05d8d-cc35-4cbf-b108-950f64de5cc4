'use client';

import { InputData } from '@/app/protected/planner/types';
import dynamic from 'next/dynamic';
import { useMemo } from 'react';
import { FUND_COLORS } from '@/app/constants/fundColors';

const ReactApexChartDynamic = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

interface ScenarioData {
  id: number;
  scenario_name: string;
  household_name: string;
  allMetrics: any[];
  color?: string;
  ending_age?: number;
}

interface PresentationNetWealthProps {
  scenarios: ScenarioData[];
  isExpanded: boolean;
  chartConfig: any;
}

// Define a set of colors for the scenarios
const SCENARIO_COLORS = [
  '#2E93fA', // Blue
  '#66DA26', // Green
  '#546E7A', // Gray
  '#E91E63', // Pink
  '#FF9800'  // Orange
];

export default function PresentationNetWealth({
  scenarios,
  isExpanded,
  chartConfig,
}: PresentationNetWealthProps) {

  // Find the earliest ending age among all scenarios
  const earliestEndingAge = useMemo(() => {
    if (scenarios.length <= 1) return undefined;

    // If ending_age is provided for all scenarios, use the minimum
    if (scenarios.every(s => s.ending_age !== undefined)) {
      return Math.min(...scenarios.map(s => s.ending_age!));
    }

    // Otherwise, find the minimum ending age based on the metrics data
    return Math.min(...scenarios.map(s => {
      const ages = s.allMetrics.map(metric => Math.round(metric.Age));
      return Math.max(...ages); // Get the maximum age in this scenario (which is the ending age)
    }));
  }, [scenarios]);

  // Create series data for each scenario
  const series = useMemo(() => {
    return scenarios.map((scenario, index) => {
      // Filter metrics to only include data points up to the earliest ending age
      const filteredMetrics = earliestEndingAge
        ? scenario.allMetrics.filter(metric => Math.round(metric.Age) <= earliestEndingAge)
        : scenario.allMetrics;

      return {
        name: scenario.scenario_name,
        type: "area",
        data: filteredMetrics.map((metric: any) => ({
          x: Math.round(metric.Age),
          y: metric["Net Wealth"],
        })),
        color: scenario.color || SCENARIO_COLORS[index % SCENARIO_COLORS.length]
      };
    });
  }, [scenarios, earliestEndingAge]);

  const options = useMemo(() => ({
    chart: {
      height: isExpanded ? '100%' : '100%',
      type: "area",
      stacked: false,
      animations: {
        enabled: true
      },
      toolbar: {
        show: true,
        offsetX: 10,
        offsetY: 0,
        tools: {
          download: true,
          selection: false,
          zoom: true,
          zoomin: false,
          zoomout: false,
          pan: false,
          reset: false,
          customIcons: []
        }
      }
    },
    stroke: {
      width: 2,
      curve: 'monotoneCubic'
    },
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'light',
        type: "vertical",
        shadeIntensity: 0.5,
        opacityFrom: 0.7,
        opacityTo: 0.2,
        stops: [0, 100]
      }
    },
    dataLabels: {
      enabled: false
    },
    tooltip: {
      shared: true,
      intersect: false,
      x: {
        formatter: function(value: number) {
          return `Age: ${Math.round(value)}`;
        }
      },
      y: {
        formatter: function(value: number) {
          return new Intl.NumberFormat('en-NZ', {
            style: 'currency',
            currency: 'NZD',
            maximumFractionDigits: 0
          }).format(value);
        }
      }
    },
    xaxis: {
      type: 'numeric',
      title: {
        text: 'Age'
      },
      labels: {
        formatter: function(value: any) {
          return Math.round(value).toString();
        }
      },
      tickAmount: 20,
      tickPlacement: 'on',
      axisTicks: {
        show: true
      },
      axisBorder: {
        show: true
      },
      crosshairs: {
        show: true,
        position: 'back',
        stroke: {
          color: '#b6b6b6',
          width: 1,
          dashArray: 0
        }
      }
    },
    yaxis: {
      title: {
        text: 'Net Wealth'
      },
      labels: {
        formatter: function(value: any) {
          return new Intl.NumberFormat('en-NZ', {
            style: 'currency',
            currency: 'NZD',
            maximumFractionDigits: 0
          }).format(value);
        }
      },
      min: 0
    },
    legend: {
      position: 'top',
      horizontalAlign: 'center',
      offsetY: 0
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          height: 200
        },
        xaxis: {
          labels: {
            show: true
          }
        }
      }
    }]
  }), [isExpanded]);

  return (
    <div className="w-full h-full relative">
      {scenarios.length > 1 && (
        <div className="absolute top-0 right-25 z-10 bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-bl-md flex items-center gap-1">
          <div className="w-2 h-2 rounded-full bg-amber-500"></div>
          <span className="font-medium">Overlay Mode</span>
          {earliestEndingAge && (
            <span>- Chart limited to earliest common age: {earliestEndingAge}</span>
          )}
        </div>
      )}
      <div className="absolute inset-[5px]">
        <ReactApexChartDynamic
          key={`presentation-netwealth-${isExpanded ? 'expanded' : 'collapsed'}`}
          // @ts-ignore - ApexCharts types are incorrect for string coordinates
          options={{
            ...options,
            // @ts-ignore - ApexCharts types are incorrect for string coordinates
            chart: {
              ...options.chart,
              parentHeightOffset: 0,
              toolbar: {
                ...options.chart.toolbar,
                offsetY: 0,
              },
              animations: {
                enabled: true
              }
            }
          }}
          // @ts-ignore - ApexCharts types are incorrect for string coordinates
          series={series}
          type="area"
          width="100%"
          height="100%"
        />
      </div>
    </div>
  );
}
