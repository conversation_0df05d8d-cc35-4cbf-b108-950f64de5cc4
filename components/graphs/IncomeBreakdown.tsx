'use client';

import { ForwardedRef, forwardRef, useMemo } from 'react';
import { DynamicBarList } from '@/components/ui/dynamicBarList';
import { FinancialMetrics } from '@/app/utils/financialTypes';
import { InputData } from '@/app/protected/planner/types';
import { Badge } from '@/components/ui/badge';

interface IncomeBreakdownProps {
  allMetrics: any[];
  selectedAge: number | null;
  inputData?: InputData;
  mainName?: string;
  partnerName?: string;
}

export const IncomeBreakdown = forwardRef(({
  allMetrics,
  selectedAge,
  inputData,
  mainName = '',
  partnerName = ''
}: IncomeBreakdownProps, ref: ForwardedRef<any>) => {
  const selectedYearData: FinancialMetrics | null = useMemo(() => {
    if (!selectedAge || !allMetrics.length) return null;
    const foundData = allMetrics.find(metric => metric.Age === selectedAge) || null;

    return foundData;
  }, [allMetrics, selectedAge]);

  const incomeBreakdown = useMemo(() => {

    if (!selectedYearData) return [];

    // Define colors for different income types
    const incomeColors: { [key: string]: string } = {
      'Main Income': '#10B981', // Green
      'Partner Income': '#3B82F6', // Blue
      'Superannuation': '#F59E0B', // Amber
      'Rental Income': '#8B5CF6', // Purple
      'Rental Income 2': '#A78BFA', // Lighter Purple
      'Rental Income 3': '#C4B5FD', // Even Lighter Purple
      'Rental Income 4': '#DDD6FE', // Very Light Purple
      'Rental Income 5': '#EDE9FE', // Extremely Light Purple
      'Board Income': '#EC4899', // Pink
      'Board Income 2': '#F472B6', // Lighter Pink
      'Board Income 3': '#FBCFE8', // Even Lighter Pink
      'Board Income 4': '#FCE7F3', // Very Light Pink
      'Board Income 5': '#FBCFE8', // Extremely Light Pink
      'Additional Income': '#6366F1', // Indigo
      // Default color
      'default': '#6366F1' // Indigo
    };

    // Create income items array
    const incomes = [];

    // Main Income
    if (selectedYearData['Main Income'] > 0) {
      incomes.push({
        name: mainName
          ? `${mainName}'s Income`
          : (inputData?.savings_owner
              ? `${inputData.savings_owner}'s Income`
              : 'Main Income'),
        value: selectedYearData['Main Income'] || 0,
        color: incomeColors['Main Income']
      });
    }

    // Partner Income
    if (selectedYearData['Partner Income'] > 0) {
      incomes.push({
        name: partnerName
          ? `${partnerName}'s Income`
          : (inputData?.partner_name
              ? `${inputData.partner_name}'s Income`
              : 'Partner Income'),
        value: selectedYearData['Partner Income'] || 0,
        color: incomeColors['Partner Income']
      });
    }

    // Superannuation
    if (selectedYearData['Superannuation'] > 0) {
      // If we have both main and partner, we can create a more descriptive name
      let superName = 'Superannuation';

      if (mainName && partnerName && inputData?.includePartner) {
        superName = `${mainName} & ${partnerName}'s Superannuation`;
      } else if (mainName) {
        superName = `${mainName}'s Superannuation`;
      } else if (partnerName && inputData?.includePartner) {
        superName = `${partnerName}'s Superannuation`;
      } else if (inputData?.savings_owner && inputData?.partner_name && inputData?.includePartner) {
        superName = `${inputData.savings_owner} & ${inputData.partner_name}'s Superannuation`;
      } else if (inputData?.savings_owner) {
        superName = `${inputData.savings_owner}'s Superannuation`;
      } else if (inputData?.partner_name && inputData?.includePartner) {
        superName = `${inputData.partner_name}'s Superannuation`;
      }

      incomes.push({
        name: superName,
        value: selectedYearData['Superannuation'] || 0,
        color: incomeColors['Superannuation']
      });
    }

    // Rental Income for each property
    // Property 1
    if (selectedYearData['Rental Income'] > 0) {
      incomes.push({
        name: inputData?.property_title ? `Rental Income (${inputData.property_title})` : 'Rental Income',
        value: selectedYearData['Rental Income'] || 0,
        color: incomeColors['Rental Income']
      });
    }

    // Check for property 2-5 rental incomes
    for (let i = 2; i <= 5; i++) {
      const propertyTitle = inputData?.[`property_title${i}` as keyof InputData] as string | undefined;
      const rentalKey = `Rental Income ${i}` as keyof FinancialMetrics;

      // Make sure we're accessing the correct property in the metrics
      const rentalValue = selectedYearData[rentalKey];

      if (rentalValue && rentalValue > 0) {
        incomes.push({
          name: propertyTitle ? `Rental Income (${propertyTitle})` : `Rental Income ${i}`,
          value: rentalValue,
          color: incomeColors[`Rental Income ${i}`]
        });
      }
    }

    // Board Income for each property
    // Property 1
    if (selectedYearData['Board Income'] > 0) {
      incomes.push({
        name: inputData?.property_title ? `Board Income (${inputData.property_title})` : 'Board Income',
        value: selectedYearData['Board Income'] || 0,
        color: incomeColors['Board Income']
      });
    }

    // Check for property 2-5 board incomes
    for (let i = 2; i <= 5; i++) {
      const propertyTitle = inputData?.[`property_title${i}` as keyof InputData] as string | undefined;
      const boardKey = `Board Income ${i}` as keyof FinancialMetrics;

      // Make sure we're accessing the correct property in the metrics
      const boardValue = selectedYearData[boardKey];

      if (boardValue && boardValue > 0) {
        incomes.push({
          name: propertyTitle ? `Board Income (${propertyTitle})` : `Board Income ${i}`,
          value: boardValue,
          color: incomeColors[`Board Income ${i}`]
        });
      }
    }

    // Additional Incomes
    // We'll directly use the additional incomes from inputData and calculate them individually
    let additionalIncomesTotal = 0;

    // Process additional incomes if they exist in the input data
    if (inputData?.additional_incomes && inputData.additional_incomes.length > 0) {
      // Group additional incomes by title
      const additionalIncomesByTitle: { [key: string]: number } = {};

      // Identify which incomes are active in the current year
      const activeIncomes = inputData.additional_incomes.filter(income =>
        selectedAge !== null &&
        income.period &&
        selectedAge >= income.period[0] &&
        selectedAge <= income.period[1]
      );

      // If we have active incomes, add each one individually with its own inflation
      if (activeIncomes.length > 0) {
        activeIncomes.forEach(income => {
          const title = income.title || 'Additional Income';
          if (!additionalIncomesByTitle[title]) {
            additionalIncomesByTitle[title] = 0;
          }

          // Calculate the inflated value for this specific income
          const inflationRate = income.inflation_rate !== undefined ? income.inflation_rate : (inputData?.inflation_rate || 2.0);
          const yearsFromStart = selectedAge! - inputData!.starting_age;
          const inflatedValue = (income.value || 0) * Math.pow(1 + inflationRate / 100, yearsFromStart);

          additionalIncomesByTitle[title] += inflatedValue;
          additionalIncomesTotal += inflatedValue;
        });

        // Add each additional income to the breakdown
        Object.entries(additionalIncomesByTitle).forEach(([title, value]) => {
          if (value > 0) {
            incomes.push({
              name: title,
              value: value,
              color: incomeColors['Additional Income']
            });
          }
        });
      }
    }

    // Filter out zero values and sort by value (descending)
    return incomes.filter(item => item.value > 0)
                 .sort((a, b) => b.value - a.value);
  }, [selectedYearData, inputData]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-NZ', {
      style: 'currency',
      currency: 'NZD',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Calculate total income
  const totalIncome = useMemo(() => {
    return incomeBreakdown.reduce((sum, item) => sum + item.value, 0);
  }, [incomeBreakdown]);

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-medium">
          {selectedAge ? `Income at Age ${selectedAge}` : 'Select an age to see income'}
        </h3>
        {selectedYearData && incomeBreakdown.length > 0 && (
          <Badge variant="green" className="ml-auto">
            {formatCurrency(totalIncome)}
          </Badge>
        )}
      </div>
      {selectedYearData ? (
        <div className="flex-grow overflow-auto h-[calc(100%-2rem)]">
          <DynamicBarList
            data={incomeBreakdown}
            valueFormatter={formatCurrency}
            maxHeight={incomeBreakdown.length > 8 ? 200 : undefined} // Adjust based on the number of items
            minRowHeight={16} // Minimum row height
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">
          Hover over the chart to see income breakdown
        </div>
      )}
    </div>
  );
});

IncomeBreakdown.displayName = 'IncomeBreakdown';
