import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { useEffect, useState, useRef } from 'react';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';

interface Note {
  id: number;
  title: string;
}

const RecentNotes = () => {
  const [notes, setNotes] = useState<Note[]>([]);
  const containerRef = useRef(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const fetchNotes = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('notes')
        .select('id, title')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Error fetching notes:', error);
      } else {
        setNotes(data || []);
      }
    };

    fetchNotes();
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver(entries => {
      for (let entry of entries) {
        setContainerSize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        });
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Determine layout based on container width
  const isGridLayout = containerSize.width > 400;

  return (
    <Card ref={containerRef} className="h-full">
      <CardHeader>
        <CardTitle>Recent Notes</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <ul
          className={`grid gap-2 h-full ${
            isGridLayout ? 'grid-cols-2' : 'grid-cols-1'
          }`}
        >
          {notes.map((note) => (
            <li key={note.id}>
              <Link
                href={`/protected/notes/${note.id}`}
                className="block p-2 bg-gray-100 rounded hover:bg-gray-200"
              >
                {note.title}
              </Link>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default RecentNotes;
