'use client';

import { HouseholdSearch } from "./HouseholdSearch";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbItem,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbLink
} from "./ui/breadcrumb";
import { PanelRight, ChevronRight, Plus } from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";
import React from "react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import TaskModal from "./modals/TaskModal";
import CreateScenarioModal from "./modals/CreateScenarioModal";
import CreateHouseholdModal from "./modals/CreateHouseholdModal";
import InteractionsModal from "./modals/InteractionsModal";
import Notifications from "./Notifications";
import { SessionSecurityBadge } from "./security/SessionSecurityIndicator";

interface PageHeaderProps {
  isCollapsed: boolean;
  setIsCollapsed: (isCollapsed: boolean) => void;
}

const PageHeader = ({ isCollapsed, setIsCollapsed }: PageHeaderProps) => {
  const pathname = usePathname();
  const pathSegments = pathname?.split('/').filter(Boolean) || [];
  const [householdName, setHouseholdName] = useState<string>("");
  const [noteName, setNoteName] = useState<string>("");
  const [workflowTitle, setWorkflowTitle] = useState<string>("");

  // Modal states
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [isScenarioModalOpen, setIsScenarioModalOpen] = useState(false);
  const [isHouseholdModalOpen, setIsHouseholdModalOpen] = useState(false);
  const [isInteractionsModalOpen, setIsInteractionsModalOpen] = useState(false);

  // Remove 'protected' prefix if present
  const segments = pathSegments[0] === 'protected'
    ? pathSegments.slice(1)
    : pathSegments;

  const capitalize = (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1);

  // Check if we're in a section that needs entity name fetching
  useEffect(() => {
    const fetchEntityName = async () => {
      const supabase = createClient();

      if (segments[0] === 'households' && segments[1] === 'household' && segments[2]) {
        const householdId = segments[2];
        const { data, error } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', householdId)
          .single();

        if (error) {
          console.error('Error fetching household name:', error);
        } else if (data) {
          setHouseholdName(data.householdName);
        }
      } else if (segments[0] === 'notes' && segments[1] === 'note' && segments[2]) {
        const noteId = segments[2];
        const { data, error } = await supabase
          .from('notes')
          .select('title')
          .eq('id', noteId)
          .single();

        if (error) {
          console.error('Error fetching note title:', error);
        } else if (data) {
          setNoteName(data.title);
        }
      } else if (segments[0] === 'workflows' && segments.length === 2) {
        // We're on a workflow detail page
        const workflowId = segments[1];
        const { data, error } = await supabase
          .from('workflows')
          .select('title')
          .eq('id', workflowId)
          .single();

        if (error) {
          console.error('Error fetching workflow title:', error);
        } else if (data) {
          setWorkflowTitle(data.title);
        }
      }
    };

    fetchEntityName();
  }, [pathname, segments]);

  // Generate breadcrumb segments based on path
  const getBreadcrumbSegments = () => {
    if (segments[0] === 'households') {
      if (segments[1] === 'household' && segments[2]) {
        // For household-specific pages
        const pageName = segments[3] ? capitalize(segments[3].replace(/_/g, ' ')) : 'Overview';
        const householdId = segments[2];
        return [
          { label: 'Households', href: '/protected/households' },
          { label: householdName || 'Loading...', href: `/protected/households/household/${householdId}` },
          { label: pageName, href: `/protected/households/household/${householdId}/${segments[3] || ''}` }
        ];
      } else {
        // For the main households page
        return [{ label: 'Households', href: '/protected/households' }];
      }
    } else if (segments[0] === 'notes') {
      if (segments[1] === 'note' && segments[2]) {
        // For note-specific pages
        const noteId = segments[2];
        return [
          { label: 'Notes', href: '/protected/notes' },
          { label: noteName || 'Loading...', href: `/protected/notes/note/${noteId}` }
        ];
      } else {
        // For the main notes page
        return [{ label: 'Notes', href: '/protected/notes' }];
      }
    } else if (segments[0] === 'workflows') {
      if (segments.length === 2) {
        // For workflow detail pages
        const workflowId = segments[1];
        return [
          { label: 'Workflows', href: '/protected/workflows' },
          { label: workflowTitle || 'Loading...', href: `/protected/workflows/${workflowId}` }
        ];
      } else {
        // For the main workflows page
        return [{ label: 'Workflows', href: '/protected/workflows' }];
      }
    }

    // Default breadcrumb behavior for other pages
    let path = '/protected';
    return segments.map((segment, index) => {
      path += `/${segment}`;
      return {
        label: capitalize(segment.replace(/_/g, ' ')),
        href: path
      };
    });
  };

  const breadcrumbSegments = getBreadcrumbSegments();

  return (
    <div className={`fixed top-0 ${isCollapsed ? 'left-[70px]' : 'left-[220px]'} right-0 h-[50px] bg-background flex items-center justify-between z-10 transition-all duration-300 ease-in-out`}>
      <div className="flex items-center pt-3">
        <PanelRight
          className="h-5 w-5 mr-4 cursor-pointer hover:text-primary"
          onClick={() => setIsCollapsed(!isCollapsed)}
        />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbSegments.map((segment, index) => (
              <React.Fragment key={index}>
                <BreadcrumbItem>
                  {index === breadcrumbSegments.length - 1 ? (
                    // Current page (last item) - not clickable
                    <BreadcrumbPage>
                      {segment.label}
                    </BreadcrumbPage>
                  ) : (
                    // Previous pages - clickable
                    <BreadcrumbLink asChild>
                      <Link href={segment.href}>
                        {segment.label}
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < breadcrumbSegments.length - 1 && (
                  <BreadcrumbSeparator>
                    /
                  </BreadcrumbSeparator>
                )}
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="flex items-center gap-4 p-4 mt-3 mr-4">
        {/* Session Security Indicator */}
        <SessionSecurityBadge className="mr-2" />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="h-8 w-8 flex items-center justify-center rounded-md border border-input hover:bg-accent hover:text-accent-foreground">
              <Plus className="h-4 w-4" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsScenarioModalOpen(true)}>
              Create Scenario
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsHouseholdModalOpen(true)}>
              Create Household
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <div className="w-[300px]">
          <HouseholdSearch />
        </div>
      </div>

      {/* Modals */}
      <TaskModal
        isOpen={isTaskModalOpen}
        onClose={() => setIsTaskModalOpen(false)}
        onSave={async () => {
          // Refresh data if needed
        }}
      />

      <CreateScenarioModal
        isOpen={isScenarioModalOpen}
        onClose={() => setIsScenarioModalOpen(false)}
      />

      <CreateHouseholdModal
        isOpen={isHouseholdModalOpen}
        onClose={() => setIsHouseholdModalOpen(false)}
      />

      {isInteractionsModalOpen && (
        <InteractionsModal
          isOpen={isInteractionsModalOpen}
          onClose={() => setIsInteractionsModalOpen(false)}
          mode="new"
          onInteractionSaved={() => {
            // Refresh data if needed
            setIsInteractionsModalOpen(false);
          }}
          onInteractionDeleted={() => {
            // Not needed for new mode, but required by the component
          }}
        />
      )}
    </div>
  );
};

export default PageHeader;
