import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

interface MonteCarloTabProps {
  inputData: any;
  onDataChange: (name: string, value: any) => void;
}

export function MonteCarloTab({ inputData, onDataChange }: MonteCarloTabProps) {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newValue = name === 'show_monte_carlo' ? e.target.checked : Math.min(parseFloat(value), 10000);
    onDataChange(name, newValue);
  };

  const handleCheckboxChange = (checked: boolean) => {
    onDataChange('show_monte_carlo', checked);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <Label htmlFor="num_simulations" className="font-semibold">Number of Simulations</Label>
        <Input 
          id="num_simulations" 
          name="num_simulations"
          type="number" 
          value={inputData.num_simulations} 
          onChange={handleInputChange}
          max={10000}
          className="w-full"
        />
      </div>
      
      <div className="flex flex-col space-y-2">
        <Label htmlFor="confidence_interval" className="font-semibold">Confidence Interval (%)</Label>
        <Input 
          id="confidence_interval" 
          name="confidence_interval"
          type="number" 
          value={inputData.confidence_interval} 
          onChange={handleInputChange}
          min={1}
          max={99}
          className="w-full"
        />
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox 
          id="show_monte_carlo" 
          checked={inputData.show_monte_carlo} 
          onCheckedChange={handleCheckboxChange}
        />
        <Label htmlFor="show_monte_carlo" className="w-full">Show Monte Carlo Analysis</Label>
      </div>
    </div>
  );
}
