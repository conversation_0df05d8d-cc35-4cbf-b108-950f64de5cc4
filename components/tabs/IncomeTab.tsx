import { useState, useEffect, useCallback } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus, X, ChevronDown, ChevronUp } from "lucide-react";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Card } from "@/components/ui/card";
import { InputData } from '@/app/protected/planner/types';

interface IncomeData {
  id: number;
  description: string;
  annual_income: number;
  income_start_age: number;
  income_end_age: number;
  tax_type: 'main' | 'partner' | 'tax_free';
  is_additional?: boolean;
  inflation_rate: number;
  isCollapsed?: boolean;
}

interface IncomeTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  hasPartner: boolean;
  readOnly?: boolean;
  mainName?: string;
  partnerName?: string;
}

export function IncomeTab({ inputData, setInputData, hasPartner, readOnly = false, mainName = 'Main', partnerName = 'Partner' }: IncomeTabProps) {
  const [incomes, setIncomes] = useState<IncomeData[]>([]);
  const [isAddingIncome, setIsAddingIncome] = useState(false);
  const [incomesUpdated, setIncomesUpdated] = useState(false);
  // Generate a unique storage key for this component
  const storageKey = 'incomeTabAccordionState';

  // Initialize accordion states with default values or from localStorage
  const [showPrimaryIncomes, setShowPrimaryIncomes] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(storageKey);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        return parsedState.showPrimaryIncomes !== undefined ? parsedState.showPrimaryIncomes : true;
      }
    }
    return true; // Default to open
  });

  // Store collapsed state of individual additional incomes
  const [collapsedIncomeIds, setCollapsedIncomeIds] = useState<Record<number, boolean>>(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('incomeItemsCollapseState');
      if (savedState) {
        return JSON.parse(savedState);
      }
    }
    return {}; // Default empty object
  });

  useEffect(() => {
    // Initialize main incomes from inputData
    const initialIncomes: IncomeData[] = [
      {
        id: 1,
        description: `${mainName} Income`,
        annual_income: inputData.annual_income,
        income_start_age: inputData.income_period[0],
        income_end_age: inputData.income_period[1],
        tax_type: 'main',
        is_additional: false,
        inflation_rate: inputData.main_income_inflation_rate !== undefined ? inputData.main_income_inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
      }
    ];

    // Add partner income if hasPartner is true
    if (hasPartner) {
      initialIncomes.push({
        id: 2,
        description: `${partnerName} Income`,
        annual_income: inputData.partner_annual_income || 0,
        income_start_age: inputData.partner_income_period ? inputData.partner_income_period[0] : inputData.income_period[0],
        income_end_age: inputData.partner_income_period ? inputData.partner_income_period[1] : inputData.income_period[1],
        tax_type: 'partner',
        is_additional: false,
        inflation_rate: inputData.partner_income_inflation_rate !== undefined ? inputData.partner_income_inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
      });
    }

    // Add any existing additional incomes
    if (inputData.additional_incomes && inputData.additional_incomes.length > 0) {
      const additionalIncomes: IncomeData[] = inputData.additional_incomes.map((income, index) => {
        const id = initialIncomes.length + index + 1;

        return {
          id, // Store the id for later use
          description: income.title,
          annual_income: income.value,
          income_start_age: income.period[0],
          income_end_age: income.period[1],
          tax_type: income.tax_type || 'tax_free',
          is_additional: true,
          inflation_rate: income.inflation_rate !== undefined ? income.inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
        };
      });
      initialIncomes.push(...additionalIncomes);

      // Set collapsed state for all additional incomes at once
      const initialCollapsedState = additionalIncomes.reduce((acc, income) => {
        acc[income.id] = true; // Collapse by default
        return acc;
      }, {} as Record<number, boolean>);

      // We don't need to set the collapsed state here anymore,
      // as the useState initializer handles loading from localStorage,
      // and user interactions handle toggling.
      // setCollapsedIncomeIds(prev => ({ ...prev, ...initialCollapsedState }));
    }

    setIncomes(initialIncomes);
  }, [hasPartner, inputData, mainName, partnerName]);

  useEffect(() => {
    if (incomesUpdated) {
      updateInputData(incomes);
      setIncomesUpdated(false);
    }
  }, [incomes, incomesUpdated]);

  const handleInputChange = (id: number, name: string, value: string | number) => {
    if (!readOnly) {
      setIncomes(prevIncomes => {
        const updatedIncomes = prevIncomes.map(income =>
          income.id === id ? { ...income, [name]: value } : income
        );
        setIncomesUpdated(true);
        return updatedIncomes;
      });
    }
  };

  const updateInputData = (updatedIncomes: IncomeData[]) => {
    if (!readOnly) {
      setInputData(prevData => {
        const newData = { ...prevData };

        // Reset main incomes
        newData.annual_income = 0;
        newData.partner_annual_income = 0;

        // Reset additional incomes
        newData.additional_incomes = [];

        updatedIncomes.forEach(income => {
          if (!income.is_additional) {
            if (income.tax_type === 'main') {
              newData.annual_income = income.annual_income;
              newData.income_period = [income.income_start_age, income.income_end_age];
              newData.main_income_inflation_rate = income.inflation_rate;
            } else if (income.tax_type === 'partner') {
              newData.partner_annual_income = income.annual_income;
              newData.partner_income_period = [income.income_start_age, income.income_end_age];
              newData.partner_income_inflation_rate = income.inflation_rate;
            }
          } else {
            // Add to additional incomes array
            if (!newData.additional_incomes) {
              newData.additional_incomes = [];
            }
            newData.additional_incomes.push({
              title: income.description,
              value: income.annual_income,
              period: [income.income_start_age, income.income_end_age],
              tax_type: income.tax_type,
              inflation_rate: income.inflation_rate,
              frequency: false
            });
          }
        });
        return newData;
      });
    }
  };

  const addIncome = () => {
    if (!readOnly) {
      setIsAddingIncome(true);
      const newId = Math.max(...incomes.map(i => i.id), 0) + 1;
      const newIncome = {
        id: newId,
        description: `Additional Income ${newId}`,
        annual_income: 0,
        income_start_age: Math.max(inputData.starting_age, inputData.starting_age),
        income_end_age: Math.min(inputData.ending_age, inputData.ending_age),
        tax_type: 'tax_free' as const,
        is_additional: true,
        inflation_rate: inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0
      };
      
      // Ensure the new income's ages are within the scenario bounds
      if (newIncome.income_start_age < inputData.starting_age) {
        newIncome.income_start_age = inputData.starting_age;
      }
      if (newIncome.income_end_age > inputData.ending_age) {
        newIncome.income_end_age = inputData.ending_age;
      }

      // Set the new income to be collapsed by default
      setCollapsedIncomeIds(prev => ({
        ...prev,
        [newId]: true
      }));
      setIncomes([...incomes, newIncome]);
      setIncomesUpdated(true);

      // Reset button state after 1 second
      setTimeout(() => {
        setIsAddingIncome(false);
      }, 1000);
    }
  };

  const deleteIncome = (id: number) => {
    if (!readOnly) {
      const incomeToDelete = incomes.find(income => income.id === id);
      if (incomeToDelete?.is_additional) {
        const updatedIncomes = incomes.filter(income => income.id !== id);
        setIncomes(updatedIncomes);
        setIncomesUpdated(true);
      }
    }
  };

  // Toggle collapse state for an income
  // Save accordion states to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, JSON.stringify({
        showPrimaryIncomes
      }));
    }
  }, [showPrimaryIncomes, storageKey]);

  // Save collapsed state of individual additional incomes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('incomeItemsCollapseState', JSON.stringify(collapsedIncomeIds));
    }
  }, [collapsedIncomeIds]);

  // Toggle accordion state for primary incomes
  const togglePrimaryIncomes = useCallback(() => {
    setShowPrimaryIncomes((prev: any) => !prev);
  }, []);

  const toggleIncomeCollapse = (id: number) => {
    // Update the collapsed state in our state object
    setCollapsedIncomeIds(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Get color scheme based on tax type
  const getColorScheme = (taxType: string) => {
    switch (taxType) {
      case 'main':
        return {
          border: 'border-l-blue-500',
          bg: 'bg-blue-50 dark:bg-blue-950/30',
          text: 'text-blue-700 dark:text-blue-300',
          badge: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
        };
      case 'partner':
        return {
          border: 'border-l-purple-500',
          bg: 'bg-purple-50 dark:bg-purple-950/30',
          text: 'text-purple-700 dark:text-purple-300',
          badge: 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
        };
      case 'tax_free':
        return {
          border: 'border-l-green-500',
          bg: 'bg-green-50 dark:bg-green-950/30',
          text: 'text-green-700 dark:text-green-300',
          badge: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
        };
      default:
        return {
          border: 'border-l-gray-500',
          bg: 'bg-gray-50 dark:bg-gray-800/30',
          text: 'text-gray-700 dark:text-gray-300',
          badge: 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200'
        };
    }
  };

  // Get human-readable tax type label
  const getTaxTypeLabel = (taxType: string) => {
    switch (taxType) {
      case 'main': return `${mainName} Income`;
      case 'partner': return `${partnerName} Income`;
      case 'tax_free': return 'Tax Free';
      default: return taxType;
    }
  };

  // Generate Additional Income Cards before the return statement
  const additionalIncomeCards = incomes.filter(income => income.is_additional).map(income => {
    const colors = getColorScheme(income.tax_type);
    return (
      <Card key={income.id} className={`overflow-hidden border-l-4 ${colors.border}`}>
        <div className={`${colors.bg} px-4 py-2 flex justify-between items-center`}>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto"
              onClick={(e) => {
                e.stopPropagation();
                toggleIncomeCollapse(income.id);
              }}
            >
              {collapsedIncomeIds[income.id] ?
                <ChevronDown className="h-4 w-4 text-gray-500" /> :
                <ChevronUp className="h-4 w-4 text-gray-500" />
              }
            </Button>
            <div className="flex-grow">
              <Input
                id={`description_${income.id}`}
                value={income.description}
                onChange={(e) => handleInputChange(income.id, 'description', e.target.value)}
                placeholder="Income description"
                readOnly={readOnly}
                className="border-0 bg-transparent p-0 h-auto font-medium focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className={`text-sm font-semibold ${colors.badge} px-3 py-1 rounded-full`}>
              ${income.annual_income.toLocaleString()}
            </div>
            <span className="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300">
              {getTaxTypeLabel(income.tax_type)}
            </span>
            {collapsedIncomeIds[income.id] && (
              <span className="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300">
                Age: {income.income_start_age} - {income.income_end_age}
              </span>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                deleteIncome(income.id);
              }}
              className="text-gray-500 hover:text-red-500 p-1 h-auto"
              disabled={readOnly}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {!collapsedIncomeIds[income.id] && (
          <div className="p-4 space-y-4 bg-white dark:bg-gray-800">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <LabelWithTooltip
                  htmlFor={`annual_income_${income.id}`}
                  label="Annual Income"
                  tooltipText="Total yearly income before tax"
                />
                <Input
                  id={`annual_income_${income.id}`}
                  name="annual_income"
                  type="number"
                  value={income.annual_income}
                  onChange={(e) => handleInputChange(income.id, 'annual_income', Number(e.target.value) || 0)}
                  disabled={readOnly}
                />
              </div>
              <div>
                <LabelWithTooltip
                  htmlFor={`tax_type_${income.id}`}
                  label="Tax Type"
                  tooltipText="How this income should be taxed"
                />
                <Select
                  value={income.tax_type}
                  onValueChange={(value) => handleInputChange(income.id, 'tax_type', value)}
                  disabled={readOnly}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tax type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName} Income</SelectItem>
                    {hasPartner && <SelectItem value="partner">{partnerName} Income</SelectItem>}
                    <SelectItem value="tax_free">Tax Free</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <LabelWithTooltip
                  htmlFor={`income_start_age_${income.id}`}
                  label="Start Age"
                  tooltipText={`Age when this income stream begins (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                />
                <Input
                  id={`income_start_age_${income.id}`}
                  name="income_start_age"
                  type="number"
                  value={income.income_start_age}
                  onChange={(e) => handleInputChange(income.id, 'income_start_age', e.target.value)}
                  onBlur={(e) => {
                    let newAge = Number(e.target.value) || 0;
                    newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                    handleInputChange(income.id, 'income_start_age', newAge);
                  }}
                  min={0}
                  max={110}
                  disabled={readOnly}
                />
              </div>
              <div>
                <LabelWithTooltip
                  htmlFor={`income_end_age_${income.id}`}
                  label="End Age"
                  tooltipText={`Age when this income stream ends (must be between 0 and 110, and not less than Start Age)`}
                />
                <Input
                  id={`income_end_age_${income.id}`}
                  name="income_end_age"
                  type="number"
                  value={income.income_end_age}
                  onChange={(e) => handleInputChange(income.id, 'income_end_age', e.target.value)}
                  onBlur={(e) => {
                    let newAge = Number(e.target.value) || 0;
                    newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                    // Ensure end age is not less than start age
                    if (newAge < income.income_start_age) {
                      newAge = income.income_start_age;
                    }
                    handleInputChange(income.id, 'income_end_age', newAge);
                  }}
                  min={0}
                  max={110}
                  disabled={readOnly}
                />
              </div>
              <div>
                <LabelWithTooltip
                  htmlFor={`inflation_rate_${income.id}`}
                  label="Inflation Rate (%)"
                  tooltipText="Individual inflation rate for this income stream. Overrides the default rate."
                />
                <Input
                  id={`inflation_rate_${income.id}`}
                  name="inflation_rate"
                  type="number"
                  max={20}
                  step={0.5}
                  value={income.inflation_rate}
                  onChange={(e) => {
                    const value = e.target.value === '' ? 0 : Number(e.target.value);
                    handleInputChange(income.id, 'inflation_rate', value);
                  }}
                  disabled={readOnly}
                />
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  }); // End of map

  return ( // Start of return
    <div className="space-y-6">
      {/* Primary Incomes Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div
          className="flex justify-between items-center cursor-pointer mb-4"
          onClick={togglePrimaryIncomes}
        >
          <h3 className="text-lg font-medium">Primary Incomes</h3>
          <Button variant="ghost" size="sm">
            {showPrimaryIncomes ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>
        </div>

        {showPrimaryIncomes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Main Income Card */}
            {incomes.filter(income => income.tax_type === 'main' && !income.is_additional).map(income => {
              const colors = getColorScheme(income.tax_type);
              return (
                <Card key={income.id} className={`overflow-hidden border-l-4 ${colors.border}`}>
                  <div className={`${colors.bg} px-4 py-2 flex justify-between items-center`}>
                    <h4 className={`font-medium ${colors.text}`}>{income.description}</h4>
                    <div className={`text-sm font-semibold ${colors.badge} px-3 py-1 rounded-full`}>
                      ${income.annual_income.toLocaleString()}
                    </div>
                  </div>

                  <div className="p-4 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor={`annual_income_${income.id}`}
                          label="Annual Income"
                          tooltipText="Total yearly income before tax"
                        />
                        <Input
                          id={`annual_income_${income.id}`}
                          name="annual_income"
                          type="number"
                          value={income.annual_income}
                          onChange={(e) => handleInputChange(income.id, 'annual_income', Number(e.target.value) || 0)}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor={`inflation_rate_${income.id}`}
                          label="Inflation Rate (%)"
                          tooltipText="Individual inflation rate for this income stream. Overrides the default rate."
                        />
                        <Input
                          id={`inflation_rate_${income.id}`}
                          name="inflation_rate"
                          type="number"
                          max={20}
                          step={0.5}
                          value={income.inflation_rate}
                          onChange={(e) => {
                            const value = e.target.value === '' ? 0 : Number(e.target.value);
                            handleInputChange(income.id, 'inflation_rate', value);
                          }}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor={`income_start_age_${income.id}`}
                          label="Start Age"
                          tooltipText={`Age when this income stream begins (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                        />
                        <Input
                          id={`income_start_age_${income.id}`}
                          name="income_start_age"
                          type="number"
                          value={income.income_start_age}
                          onChange={(e) => handleInputChange(income.id, 'income_start_age', e.target.value)}
                          onBlur={(e) => {
                            let newAge = Number(e.target.value) || 0;
                            newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                            handleInputChange(income.id, 'income_start_age', newAge);
                          }}
                          min={0}
                          max={110}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor={`income_end_age_${income.id}`}
                          label="End Age"
                          tooltipText={`Age when this income stream ends (must be between 0 and 110, and not less than Start Age)`}
                        />
                        <Input
                          id={`income_end_age_${income.id}`}
                          name="income_end_age"
                          type="number"
                          value={income.income_end_age}
                          onChange={(e) => handleInputChange(income.id, 'income_end_age', e.target.value)}
                          onBlur={(e) => {
                            let newAge = Number(e.target.value) || 0;
                            newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                            // Ensure end age is not less than start age
                            if (newAge < income.income_start_age) {
                              newAge = income.income_start_age;
                            }
                            handleInputChange(income.id, 'income_end_age', newAge);
                          }}
                          min={0}
                          max={110}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}

            {/* Partner Income Card */}
            {hasPartner && incomes.filter(income => income.tax_type === 'partner' && !income.is_additional).map(income => {
              const colors = getColorScheme(income.tax_type);
              return (
                <Card key={income.id} className={`overflow-hidden border-l-4 ${colors.border}`}>
                  <div className={`${colors.bg} px-4 py-2 flex justify-between items-center`}>
                    <h4 className={`font-medium ${colors.text}`}>{income.description}</h4>
                    <div className={`text-sm font-semibold ${colors.badge} px-3 py-1 rounded-full`}>
                      ${income.annual_income.toLocaleString()}
                    </div>
                  </div>

                  <div className="p-4 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor={`annual_income_${income.id}`}
                          label="Annual Income"
                          tooltipText="Total yearly income before tax"
                        />
                        <Input
                          id={`annual_income_${income.id}`}
                          name="annual_income"
                          type="number"
                          value={income.annual_income}
                          onChange={(e) => handleInputChange(income.id, 'annual_income', Number(e.target.value) || 0)}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor={`inflation_rate_${income.id}`}
                          label="Inflation Rate (%)"
                          tooltipText="Individual inflation rate for this income stream. Overrides the default rate."
                        />
                        <Input
                          id={`inflation_rate_${income.id}`}
                          name="inflation_rate"
                          type="number"
                          max={20}
                          step={0.5}
                          value={income.inflation_rate}
                          onChange={(e) => {
                            const value = e.target.value === '' ? 0 : Number(e.target.value);
                            handleInputChange(income.id, 'inflation_rate', value);
                          }}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor={`income_start_age_${income.id}`}
                          label="Start Age"
                          tooltipText={`Age when this income stream begins (must be between 0 and 110)`}
                        />
                        <Input
                          id={`income_start_age_${income.id}`}
                          name="income_start_age"
                          type="number"
                          value={income.income_start_age}
                          onChange={(e) => handleInputChange(income.id, 'income_start_age', e.target.value)}
                          onBlur={(e) => {
                            let newAge = Number(e.target.value) || 0;
                            newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                            handleInputChange(income.id, 'income_start_age', newAge);
                          }}
                          min={0}
                          max={110}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor={`income_end_age_${income.id}`}
                          label="End Age"
                          tooltipText={`Age when this income stream ends (must be between 0 and 110, and not less than Start Age)`}
                        />
                        <Input
                          id={`income_end_age_${income.id}`}
                          name="income_end_age"
                          type="number"
                          value={income.income_end_age}
                          onChange={(e) => handleInputChange(income.id, 'income_end_age', e.target.value)}
                          onBlur={(e) => {
                            let newAge = Number(e.target.value) || 0;
                            newAge = Math.max(0, Math.min(110, newAge)); // Clamp between 0 and 110
                            // Ensure end age is not less than start age
                            if (newAge < income.income_start_age) {
                              newAge = income.income_start_age;
                            }
                            handleInputChange(income.id, 'income_end_age', newAge);
                          }}
                          min={0}
                          max={110}
                          disabled={readOnly}
                          className="bg-white dark:bg-gray-800"
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}
      </div> {/* End Primary Incomes Section */}

      {/* Additional Incomes Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Additional Incomes</h3>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {incomes.filter(income => income.is_additional).length} items
            </span>
          </div>
        </div>
        <div className="space-y-4">
          {/* Render the pre-generated cards */}
          {additionalIncomeCards}
          <Button
              onClick={addIncome}
              className="w-full mt-4"
              variant={isAddingIncome ? "secondary" : "default"}
              disabled={isAddingIncome || readOnly}
            >
              {isAddingIncome ? (
                "Income Added"
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Income
                </>
              )}
            </Button>
        </div>
      </div>
    </div>
  );
}
