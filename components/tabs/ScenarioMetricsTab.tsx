import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Info, Plus, Trash2, Edit, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";

// Define fund types
type FundType = 'investment' | 'kiwisaver';

// Define fund interface
interface Fund {
  id: string;
  name: string;
  type: FundType;
  return: number;
  stdDev: number;
  incomePortion: number; // Percentage of return that is taxable income (0-100)
  color?: string;
  isEditing?: boolean;
}

interface DefaultValue {
  value: number | string | boolean;
}

interface MetricDefaults {
  [key: string]: DefaultValue;
}

interface ScenarioMetricsTabProps {
  user?: {
    id: string;
  };
  profile?: {
    org_id: string;
  };
}

export function ScenarioMetricsTab({ user, profile }: ScenarioMetricsTabProps) {
  const supabase = createClient();
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  // State for funds
  const [funds, setFunds] = useState<Fund[]>([
    // Investment funds
    {
      id: 'inv-1',
      name: 'Conservative',
      type: 'investment',
      return: 3.5,
      stdDev: 4.0,
      incomePortion: 90, // 90% of return is taxable income
      color: '#E2F2FF',
    },
    {
      id: 'inv-2',
      name: 'Moderate',
      type: 'investment',
      return: 4.5,
      stdDev: 6.0,
      incomePortion: 80, // 80% of return is taxable income
      color: '#C2E0FF',
    },
    {
      id: 'inv-3',
      name: 'Balanced',
      type: 'investment',
      return: 5.5,
      stdDev: 8.0,
      incomePortion: 60, // 60% of return is taxable income
      color: '#99CCF3',
    },
    {
      id: 'inv-4',
      name: 'Growth',
      type: 'investment',
      return: 6.5,
      stdDev: 10.0,
      incomePortion: 40, // 40% of return is taxable income
      color: '#66B2FF',
    },
    {
      id: 'inv-5',
      name: 'High Growth',
      type: 'investment',
      return: 7.5,
      stdDev: 12.0,
      incomePortion: 20, // 20% of return is taxable income
      color: '#3399FF',
    },
    // KiwiSaver funds
    {
      id: 'ks-1',
      name: 'Conservative',
      type: 'kiwisaver',
      return: 3.5,
      stdDev: 4.0,
      incomePortion: 90, // 90% of return is taxable income
      color: '#E5F5E0',
    },
    {
      id: 'ks-2',
      name: 'Moderate',
      type: 'kiwisaver',
      return: 4.5,
      stdDev: 6.0,
      incomePortion: 80, // 80% of return is taxable income
      color: '#C7E9C0',
    },
    {
      id: 'ks-3',
      name: 'Balanced',
      type: 'kiwisaver',
      return: 5.5,
      stdDev: 8.0,
      incomePortion: 60, // 60% of return is taxable income
      color: '#A1D99B',
    },
    {
      id: 'ks-4',
      name: 'Growth',
      type: 'kiwisaver',
      return: 6.5,
      stdDev: 10.0,
      incomePortion: 40, // 40% of return is taxable income
      color: '#74C476',
    },
    {
      id: 'ks-5',
      name: 'High Growth',
      type: 'kiwisaver',
      return: 7.5,
      stdDev: 12.0,
      incomePortion: 20, // 20% of return is taxable income
      color: '#41AB5D',
    }
  ]);

  // State for default values
  const [defaults, setDefaults] = useState<{
    personal: MetricDefaults;
    income: MetricDefaults;
    expense: MetricDefaults;
    savings: MetricDefaults;
    investment: MetricDefaults;
    kiwisaver: MetricDefaults;
    property: MetricDefaults;
    misc: MetricDefaults;
  }>({
    personal: {
      include_partner: { value: true },
    },
    income: {
      include_superannuation: { value: true },
      income_inflation_rate: { value: 2.0 },
      partner_income_inflation_rate: { value: 2.0 },
      additional_income_inflation_rate: { value: 2.0 },
      enable_inflation: { value: true },
    },
    expense: {
      second_expense: { value: true },
      expense1_inflation_rate: { value: 2.0 },
      expense2_inflation_rate: { value: 2.0 },
      additional_expense_inflation_rate: { value: 2.0 },
      enable_inflation: { value: true },
    },
    savings: {
      cash_reserve: { value: 10000 },
      saving_percentage: { value: 10 },
    },
    investment: {
      utilise_excess_cashflow: { value: true },
      allocate_to_investment: { value: true },
      investment_income_portion: { value: 60 }, // Default to 60% (balanced fund)
    },
    kiwisaver: {
      kiwisaver_contribution: { value: 3 },
      employer_contribution: { value: 3 },
      consolidate_kiwisaver: { value: false },
      kiwisaver_income_portion: { value: 60 }, // Default to 60% (balanced fund)
    },
    property: {
      include_property_debt: { value: true },
      rental_income: { value: 0 },
      board_income: { value: 0 },
      interest_only_period: { value: false },
      property_inflation_rate: { value: 2.0 },
    },
    misc: {
      show_savings: { value: true },
      show_investment: { value: true },
      show_kiwisaver: { value: true },
      show_monte_carlo: { value: true },
      show_annotations: { value: false },
      num_simulations: { value: 100 },
      confidence_interval: { value: 80 },
      inflation_rate: { value: 2.0 },
    },
  });

  // State for toggle switches - these would be connected to actual data in the future
  const [toggles, setToggles] = useState({
    // Essential inputs that can't be toggled off
    essential: {
      starting_age: true,
      ending_age: true,
      annual_income: true,
      annual_expenses: true,
    },

    // Personal tab toggles
    personal: {
      include_partner: true,
    },

    // Income tab toggles
    income: {
      include_superannuation: true,
      income_inflation_rate_toggle: true,
      partner_income_inflation_rate_toggle: true,
      additional_income_inflation_rate_toggle: true,
      enable_inflation: true,
    },

    // Expense tab toggles
    expense: {
      second_expense: true,
      expense1_inflation_rate_toggle: true,
      expense2_inflation_rate_toggle: true,
      additional_expense_inflation_rate_toggle: true,
      enable_inflation: true,
    },

    // Savings tab toggles
    savings: {
      cash_reserve: true,
      saving_percentage: true,
    },

    // Investment tab toggles
    investment: {
      utilise_excess_cashflow: true,
      allocate_to_investment: true,
    },

    // KiwiSaver tab toggles
    kiwisaver: {
      kiwisaver_contribution: true,
      employer_contribution: true,
      consolidate_kiwisaver: true,
    },

    // Property tab toggles
    property: {
      include_property_debt: true,
      rental_income: true,
      board_income: true,
      interest_only_period: true,
      property_inflation_rate_toggle: true,
    },

    // Misc tab toggles
    misc: {
      show_savings: true,
      show_investment: true,
      show_kiwisaver: true,
      show_monte_carlo: true,
      show_annotations: true,
      num_simulations_toggle: true,
      confidence_interval_toggle: true,
      inflation_rate_toggle: true,
    },
  });

  // Handle toggle change
  const handleToggleChange = (category: string, setting: string, checked: boolean) => {
    setToggles(prev => {
      // Get the current category toggles or an empty object if it doesn't exist
      const categoryToggles = prev[category as keyof typeof prev] || {};

      return {
        ...prev,
        [category]: {
          ...categoryToggles,
          [setting]: checked
        }
      };
    });
  };

  // Handle default value change
  const handleDefaultValueChange = (category: string, setting: string, value: any) => {
    setDefaults(prev => {
      // Get the current category defaults or an empty object if it doesn't exist
      const categoryDefaults = prev[category as keyof typeof prev] || {};
      // Get the current setting default or an empty object if it doesn't exist
      const settingDefault = (categoryDefaults as Record<string, any>)[setting] || { value: null };

      return {
        ...prev,
        [category]: {
          ...categoryDefaults,
          [setting]: {
            ...settingDefault,
            value
          }
        }
      };
    });
  };



  // Render a toggle with label, optional tooltip, and default value
  const renderToggle = (
    category: string,
    setting: string,
    label: string,
    tooltipText?: string,
    disabled: boolean = false
  ) => {
    // Safely access the toggle state with proper type checking
    const categoryToggles = toggles[category as keyof typeof toggles] || {};
    const isChecked = (categoryToggles as Record<string, boolean>)[setting] ?? true;

    // Check if inflation is enabled for this category
    const isInflationEnabled = (categoryToggles as Record<string, boolean>)['enable_inflation'] ?? true;

    // Determine if the input should be disabled
    const isDisabled = setting.includes('inflation_rate') && !isInflationEnabled;

    const defaultValue = defaults[category as keyof typeof defaults]?.[setting];
    const showDefaultValue = defaultValue !== undefined;

    return (
      <div className="flex items-center justify-between py-2">
        <div className="flex items-center gap-2">
          <Label htmlFor={`${category}-${setting}`} className="cursor-pointer">
            {label}
          </Label>
          {tooltipText && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{tooltipText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        <div className="flex items-center gap-3">
          {showDefaultValue && (
            <div className="flex items-center gap-2">
              {typeof defaultValue.value === 'boolean' ? (
                <div className="flex items-center gap-1">
                  <Label htmlFor={`default-${category}-${setting}`} className="text-xs text-muted-foreground">
                    Value:
                  </Label>
                  <Switch
                    id={`default-${category}-${setting}`}
                    checked={defaultValue.value as boolean}
                    onCheckedChange={(checked) => handleDefaultValueChange(category, setting, checked)}
                    disabled={disabled}
                    className="scale-75"
                  />
                </div>
              ) : typeof defaultValue.value === 'number' ? (
                <div className="flex items-center gap-1">
                  <Label htmlFor={`default-${category}-${setting}`} className="text-xs text-muted-foreground">
                    Value:
                  </Label>
                  <Input
                    id={`default-${category}-${setting}`}
                    type="number"
                    value={defaultValue.value as number}
                    onChange={(e) => handleDefaultValueChange(category, setting, parseFloat(e.target.value) || 0)}
                    className="w-16 h-7 text-sm"
                    disabled={disabled}
                  />
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Label htmlFor={`default-${category}-${setting}`} className="text-xs text-muted-foreground">
                    Value:
                  </Label>
                  <Input
                    id={`default-${category}-${setting}`}
                    type="text"
                    value={defaultValue.value as string}
                    onChange={(e) => handleDefaultValueChange(category, setting, e.target.value)}
                    className="w-24 h-7 text-sm"
                    disabled={disabled}
                  />
                </div>
              )}
            </div>
          )}
          {/* Toggle switch removed as requested */}
        </div>
      </div>
    );
  };

  // Load settings from Supabase
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);

      // Check if the table exists first
      const { error: tableCheckError } = await supabase
        .from('scenario_metrics')
        .select('count')
        .limit(1);

      if (tableCheckError) {

        setIsLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('scenario_metrics')
        .select('*')
        .eq('user_id', user?.id)
        .maybeSingle();

      if (error) {

        setIsLoading(false);
        return;
      }

      if (data) {
        // Load toggle states
        if (data.toggle_states) {
          setToggles(prev => ({
            ...prev,
            ...data.toggle_states
          }));
        }

        // Load default values
        if (data.default_values) {
          setDefaults(prev => ({
            ...prev,
            ...data.default_values
          }));
        }

        // Load funds
        if (data.funds && Array.isArray(data.funds)) {
          setFunds(data.funds);
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // Don't show error toast, just use defaults
    } finally {
      setIsLoading(false);
    }
  }, [supabase, user?.id]);

  // Load saved settings from Supabase
  useEffect(() => {
    if (user?.id) {
      loadSettings();
    } else {
      setIsLoading(false);
    }
  }, [user, loadSettings]);

  // Render a numeric input with label and tooltip
  const renderNumericInput = (
    category: string,
    setting: string,
    label: string,
    tooltipText?: string,
    min?: number,
    max?: number,
    step?: number
  ) => {
    const defaultValue = defaults[category as keyof typeof defaults]?.[setting];
    if (!defaultValue || typeof defaultValue.value !== 'number') return null;

    // Create a toggle key based on the setting name
    const toggleKey = `${setting}_toggle`;

    // Safely access the toggle state with proper type checking
    const categoryToggles = toggles[category as keyof typeof toggles] || {};
    const isChecked = (categoryToggles as Record<string, boolean>)[toggleKey] ?? true;

    // Check if inflation is enabled for this category
    const isInflationEnabled = (categoryToggles as Record<string, boolean>)['enable_inflation'] ?? true;

    // Determine if the input should be disabled
    const isDisabled = setting.includes('inflation_rate') && !isInflationEnabled;

    return (
      <div className="flex items-center justify-between py-2">
        <div className="flex items-center gap-2">
          <Label htmlFor={`${category}-${setting}`} className="cursor-pointer">
            {label}
          </Label>
          {tooltipText && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{tooltipText}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            <Label htmlFor={`default-${category}-${setting}`} className="text-xs text-muted-foreground">
              Value:
            </Label>
            <Input
              id={`default-${category}-${setting}`}
              type="number"
              value={defaultValue.value as number}
              onChange={(e) => handleDefaultValueChange(category, setting, parseFloat(e.target.value) || 0)}
              className="w-16 h-7 text-sm"
              min={min}
              max={max}
              step={step}
              disabled={isDisabled}
            />
          </div>
          {/* Toggle switch removed as requested */}
        </div>
      </div>
    );
  };

  // Save settings to Supabase
  const handleSaveSettings = async () => {
    if (!user?.id) {
      toast.error('You must be logged in to save settings');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare data for saving
      const settingsData = {
        user_id: user.id,
        org_id: profile?.org_id || null,
        toggle_states: toggles,
        default_values: defaults,
        funds: funds
      };

      // Check if the table exists first
      const { error: tableCheckError } = await supabase
        .from('scenario_metrics')
        .select('count')
        .limit(1);

      if (tableCheckError) {
        toast.error(`Table check error: ${tableCheckError.message}`);
        console.error('Table check error:', tableCheckError);
        return;
      }

      // First check if record exists
      const { data: existingData, error: queryError } = await supabase
        .from('scenario_metrics')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (queryError) {
        console.error('Error checking for existing record:', queryError);
        toast.error(`Query error: ${queryError.message}`);
        return;
      }


      // Now save the data
      let result;

      if (existingData?.id) {
        // Update existing record
        result = await supabase
          .from('scenario_metrics')
          .update(settingsData)
          .eq('id', existingData.id);
      } else {
        // Insert new record
        result = await supabase
          .from('scenario_metrics')
          .insert(settingsData);
      }

      if (result.error) {
        console.error('Save operation error:', result.error);
        toast.error(`Save error: ${result.error.message}`);
        return;
      }

      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Exception during save operation:', error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to default values
  const handleResetDefaults = () => {
    // Confirm before resetting
    if (window.confirm('Are you sure you want to reset all settings to defaults? This will not affect your saved settings until you click Save.')) {
      // Reset toggles to initial state
      setToggles({
        // Essential inputs that can't be toggled off
        essential: {
          starting_age: true,
          ending_age: true,
          annual_income: true,
          annual_expenses: true,
        },

        // Personal tab toggles
        personal: {
          include_partner: true,
        },

        // Income tab toggles
        income: {
          include_superannuation: true,
          income_inflation_rate_toggle: true,
          partner_income_inflation_rate_toggle: true,
          additional_income_inflation_rate_toggle: true,
          enable_inflation: true,
        },

        // Expense tab toggles
        expense: {
          second_expense: true,
          expense1_inflation_rate_toggle: true,
          expense2_inflation_rate_toggle: true,
          additional_expense_inflation_rate_toggle: true,
          enable_inflation: true,
        },

        // Savings tab toggles
        savings: {
          cash_reserve: true,
          saving_percentage: true,
        },

        // Investment tab toggles
        investment: {
          utilise_excess_cashflow: true,
          allocate_to_investment: true,
        },

        // KiwiSaver tab toggles
        kiwisaver: {
          kiwisaver_contribution: true,
          employer_contribution: true,
          consolidate_kiwisaver: true,
        },

        // Property tab toggles
        property: {
          include_property_debt: true,
          rental_income: true,
          board_income: true,
          interest_only_period: true,
          property_inflation_rate_toggle: true,
        },

        // Misc tab toggles
        misc: {
          show_savings: true,
          show_investment: true,
          show_kiwisaver: true,
          show_monte_carlo: true,
          show_annotations: true,
          num_simulations_toggle: true,
          confidence_interval_toggle: true,
          inflation_rate_toggle: true,
        },
      });

      // Reset default values to initial state
      setDefaults({
        personal: {
          include_partner: { value: true },
        },
        income: {
          include_superannuation: { value: true },
          income_inflation_rate: { value: 2.0 },
          partner_income_inflation_rate: { value: 2.0 },
          additional_income_inflation_rate: { value: 2.0 },
          enable_inflation: { value: true },
        },
        expense: {
          second_expense: { value: true },
          expense1_inflation_rate: { value: 2.0 },
          expense2_inflation_rate: { value: 2.0 },
          additional_expense_inflation_rate: { value: 2.0 },
          enable_inflation: { value: true },
        },
        savings: {
          cash_reserve: { value: 10000 },
          saving_percentage: { value: 10 },
        },
        investment: {
          utilise_excess_cashflow: { value: true },
          allocate_to_investment: { value: true },
          investment_income_portion: { value: 60 }, // Default to 60% (balanced fund)
        },
        kiwisaver: {
          kiwisaver_contribution: { value: 3 },
          employer_contribution: { value: 3 },
          consolidate_kiwisaver: { value: false },
        },
        property: {
          include_property_debt: { value: true },
          rental_income: { value: 0 },
          board_income: { value: 0 },
          interest_only_period: { value: false },
          property_inflation_rate: { value: 2.0 },
        },
        misc: {
          show_savings: { value: true },
          show_investment: { value: true },
          show_kiwisaver: { value: true },
          show_monte_carlo: { value: true },
          show_annotations: { value: false },
          num_simulations: { value: 100 },
          confidence_interval: { value: 80 },
          inflation_rate: { value: 2.0 },
        },
      });

      // Reset funds to default state
      setFunds([
        // Investment funds
        {
          id: 'inv-1',
          name: 'Conservative',
          type: 'investment',
          return: 3.5,
          stdDev: 4.0,
          incomePortion: 90, // 90% of return is taxable income
          color: '#E2F2FF',
        },
        {
          id: 'inv-2',
          name: 'Moderate',
          type: 'investment',
          return: 4.5,
          stdDev: 6.0,
          incomePortion: 80, // 80% of return is taxable income
          color: '#C2E0FF',
        },
        {
          id: 'inv-3',
          name: 'Balanced',
          type: 'investment',
          return: 5.5,
          stdDev: 8.0,
          incomePortion: 60, // 60% of return is taxable income
          color: '#99CCF3',
        },
        {
          id: 'inv-4',
          name: 'Growth',
          type: 'investment',
          return: 6.5,
          stdDev: 10.0,
          incomePortion: 40, // 40% of return is taxable income
          color: '#66B2FF',
        },
        {
          id: 'inv-5',
          name: 'High Growth',
          type: 'investment',
          return: 7.5,
          stdDev: 12.0,
          incomePortion: 20, // 20% of return is taxable income
          color: '#3399FF',
        },
        // KiwiSaver funds
        {
          id: 'ks-1',
          name: 'Conservative',
          type: 'kiwisaver',
          return: 3.5,
          stdDev: 4.0,
          incomePortion: 90, // 90% of return is taxable income
          color: '#E5F5E0',
        },
        {
          id: 'ks-2',
          name: 'Moderate',
          type: 'kiwisaver',
          return: 4.5,
          stdDev: 6.0,
          incomePortion: 80, // 80% of return is taxable income
          color: '#C7E9C0',
        },
        {
          id: 'ks-3',
          name: 'Balanced',
          type: 'kiwisaver',
          return: 5.5,
          stdDev: 8.0,
          incomePortion: 60, // 60% of return is taxable income
          color: '#A1D99B',
        },
        {
          id: 'ks-4',
          name: 'Growth',
          type: 'kiwisaver',
          return: 6.5,
          stdDev: 10.0,
          incomePortion: 40, // 40% of return is taxable income
          color: '#74C476',
        },
        {
          id: 'ks-5',
          name: 'High Growth',
          type: 'kiwisaver',
          return: 7.5,
          stdDev: 12.0,
          incomePortion: 20, // 20% of return is taxable income
          color: '#41AB5D',
        }
      ]);

      toast.success('Settings reset to defaults');
    }
  };

  // Define preset colors
  const presetColors = [
    '#E2F2FF', // Light Blue
    '#E5F5E0', // Light Green
    '#FFF3E0', // Light Orange
    '#FCE4EC', // Light Pink
    '#E8EAF6', // Light Indigo
    '#EFEBE9', // Light Brown
  ];

  // State for color picker
  const [colorPickerOpen, setColorPickerOpen] = useState<string | null>(null);

  // Handle adding a new fund
  const handleAddFund = (type: FundType) => {
    const newId = type === 'investment' ? `inv-${Date.now()}` : `ks-${Date.now()}`;
    const defaultColor = type === 'investment' ? '#E2F2FF' : '#E5F5E0';

    const newFund: Fund = {
      id: newId,
      name: 'New Fund',
      type: type,
      return: 5.0,
      stdDev: 8.0, // This is explicitly set to 8.0, not undefined or null
      incomePortion: 60, // Default to 60% (balanced fund)
      color: defaultColor,
      isEditing: true
    };

    setFunds([...funds, newFund]);
  };

  // Handle deleting a fund
  const handleDeleteFund = (id: string) => {
    setFunds(funds.filter(fund => fund.id !== id));
  };

  // Toggle edit mode for a fund
  const toggleEditMode = (id: string) => {
    setFunds(funds.map(fund =>
      fund.id === id
        ? { ...fund, isEditing: !fund.isEditing }
        : fund
    ));
  };

  // Update fund property
  const updateFundProperty = (id: string, property: keyof Fund, value: any) => {
    // Ensure that 0 values are properly preserved
    // This is especially important for stdDev where 0 is a valid value
    const processedValue = value === 0 ? 0 : value || 0;

    setFunds(funds.map(fund =>
      fund.id === id
        ? { ...fund, [property]: processedValue }
        : fund
    ));
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Scenario Metrics Settings</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleResetDefaults}
            disabled={isSaving || isLoading}
          >
            Reset to Defaults
          </Button>
          <Button
            onClick={handleSaveSettings}
            disabled={isSaving || isLoading}
          >
            {isSaving ? (
              <>
                <span className="mr-2">Saving...</span>
                <Loader2 className="h-4 w-4 animate-spin" />
              </>
            ) : 'Save Settings'}
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground">
        Configure default values for your scenarios. Essential inputs are always enabled.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Essential Inputs Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              Essential Inputs
              <Badge variant="secondary">Always Enabled</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('essential', 'starting_age', 'Starting Age', 'The age at which the scenario begins', true)}
              {renderToggle('essential', 'ending_age', 'Ending Age', 'The age at which the scenario ends', true)}
              {renderToggle('essential', 'annual_income', 'Annual Income', 'Your primary annual income', true)}
              {renderToggle('essential', 'annual_expenses', 'Annual Expenses', 'Your primary annual expenses', true)}
            </div>
          </CardContent>
        </Card>

        {/* Personal Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Personal Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('personal', 'include_partner', 'Include Partner', 'Enable partner-related inputs and calculations')}
            </div>
          </CardContent>
        </Card>

        {/* Income Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Income Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('income', 'include_superannuation', 'Include Superannuation', 'Include superannuation in income calculations')}
              {renderToggle('income', 'enable_inflation', 'Enable Income Inflation', 'Apply inflation to income values')}
              {renderNumericInput('income', 'income_inflation_rate', 'Main Income Inflation Rate (%)', 'Annual inflation rate for main income', 0, 20, 0.1)}
              {renderNumericInput('income', 'partner_income_inflation_rate', 'Partner Income Inflation Rate (%)', 'Annual inflation rate for partner income', 0, 20, 0.1)}
              {renderNumericInput('income', 'additional_income_inflation_rate', 'Additional Income Inflation Rate (%)', 'Annual inflation rate for additional income sources', 0, 20, 0.1)}
            </div>
          </CardContent>
        </Card>

        {/* Expense Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Expense Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('expense', 'second_expense', 'Second Expense', 'Enable secondary expense inputs (e.g., retirement expenses)')}
              {renderToggle('expense', 'enable_inflation', 'Enable Expense Inflation', 'Apply inflation to expense values')}
              {renderNumericInput('expense', 'expense1_inflation_rate', 'Primary Expense Inflation Rate (%)', 'Annual inflation rate for primary expenses', 0, 20, 0.1)}
              {renderNumericInput('expense', 'expense2_inflation_rate', 'Secondary Expense Inflation Rate (%)', 'Annual inflation rate for secondary expenses', 0, 20, 0.1)}
              {renderNumericInput('expense', 'additional_expense_inflation_rate', 'Additional Expense Inflation Rate (%)', 'Annual inflation rate for additional expense items', 0, 20, 0.1)}
            </div>
          </CardContent>
        </Card>

        {/* Savings Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Savings Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('savings', 'cash_reserve', 'Cash Reserve', 'Enable cash reserve inputs')}
              {renderToggle('savings', 'saving_percentage', 'Saving Percentage', 'Enable saving percentage inputs')}
            </div>
          </CardContent>
        </Card>

        {/* Investment Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Investment Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('investment', 'utilise_excess_cashflow', 'Utilise Excess Cashflow', 'Automatically use excess cashflow for investments')}
              {renderToggle('investment', 'allocate_to_investment', 'Allocate to Investment', 'Allocate funds to investments')}
              {renderNumericInput('investment', 'investment_income_portion', 'Default Income Portion (%)', 'Default percentage of investment return that is taxable income', 0, 100, 5)}
            </div>
          </CardContent>
        </Card>

        {/* KiwiSaver Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>KiwiSaver Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('kiwisaver', 'kiwisaver_contribution', 'KiwiSaver Contribution', 'Enable KiwiSaver contribution inputs')}
              {renderToggle('kiwisaver', 'employer_contribution', 'Employer Contribution', 'Enable employer contribution inputs')}
              {renderToggle('kiwisaver', 'consolidate_kiwisaver', 'Consolidate KiwiSaver', 'Enable KiwiSaver consolidation options')}
              {renderNumericInput('kiwisaver', 'kiwisaver_income_portion', 'Default Income Portion (%)', 'Default percentage of KiwiSaver return that is taxable income', 0, 100, 5)}
            </div>
          </CardContent>
        </Card>

        {/* Property Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Property Tab</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('property', 'include_property_debt', 'Include Property Debt', 'Include property debt in net wealth calculations')}
              {renderToggle('property', 'rental_income', 'Rental Income', 'Enable rental income inputs for properties')}
              {renderToggle('property', 'board_income', 'Board Income', 'Enable board income inputs for properties')}
              {renderToggle('property', 'interest_only_period', 'Interest Only Period', 'Enable interest-only period options for mortgages')}
              {renderNumericInput('property', 'property_inflation_rate', 'Property Inflation Rate (%)', 'Annual inflation rate for property values', 0, 20, 0.1)}
            </div>
          </CardContent>
        </Card>

        {/* Misc Tab Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Display Options</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderToggle('misc', 'show_savings', 'Show Savings Fund', 'Display savings fund in results')}
              {renderToggle('misc', 'show_investment', 'Show Investment', 'Display investment portfolio in results')}
              {renderToggle('misc', 'show_kiwisaver', 'Show KiwiSaver', 'Display KiwiSaver balance in results')}
              {renderToggle('misc', 'show_monte_carlo', 'Show Monte Carlo', 'Display Monte Carlo simulation lines')}
            </div>
          </CardContent>
        </Card>

        {/* Simulation Settings Card */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Simulation Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {renderNumericInput('misc', 'num_simulations', 'Number of Simulations', 'Number of scenarios to simulate (max 10,000). More simulations provide more accurate results but take longer', 100, 10000, 100)}
              {renderNumericInput('misc', 'confidence_interval', 'Confidence Interval (%)', 'Percentage of simulations that should fall within the displayed range (e.g., 95% shows the range excluding the top and bottom 2.5%)', 1, 99, 1)}
              {renderNumericInput('misc', 'inflation_rate', 'Inflation Rate (%)', 'Annual inflation rate used in calculations', 0, 20, 0.1)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Funds Section */}
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">Funds</h2>
        <p className="text-muted-foreground mb-6">
          Configure investment and KiwiSaver funds with custom return rates and standard deviations.
        </p>

        {/* Two-column layout for funds */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Investment Funds Column */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                Investment Funds
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAddFund('investment')}
                  className="h-8"
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  Add Fund
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-2 pl-3 text-xs font-medium text-muted-foreground">Name</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Return (%)</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Std Dev (%)</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Income (%)</th>
                      <th className="text-right p-2 pr-3 text-xs font-medium text-muted-foreground">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {funds.filter(fund => fund.type === 'investment').map(fund => (
                      <tr
                        key={fund.id}
                        className="hover:bg-muted/30"
                        style={{ backgroundColor: fund.color ? `${fund.color}40` : 'transparent' }}
                      >
                        <td className="p-2 pl-3">
                          {fund.isEditing ? (
                            <Input
                              value={fund.name}
                              onChange={(e) => updateFundProperty(fund.id, 'name', e.target.value)}
                              className="h-8 w-full"
                            />
                          ) : (
                            <span>{fund.name}</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.return}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'return', value);
                              }}
                              step="0.1"
                              min="0"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.return}%</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.stdDev}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'stdDev', value);
                              }}
                              step="0.1"
                              min="0"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.stdDev}%</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.incomePortion}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'incomePortion', value);
                              }}
                              step="5"
                              min="0"
                              max="100"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.incomePortion}%</span>
                          )}
                        </td>
                        <td className="p-2 pr-3 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <div className="relative">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-7 w-7 overflow-hidden"
                                onClick={() => setColorPickerOpen(colorPickerOpen === fund.id ? null : fund.id)}
                              >
                                <div
                                  className="absolute inset-0.5 rounded-sm"
                                  style={{ backgroundColor: fund.color || 'transparent' }}
                                ></div>
                                <span className="sr-only">Choose color</span>
                              </Button>

                              {colorPickerOpen === fund.id && (
                                <div className="absolute right-0 top-full mt-1 p-2 bg-white border rounded-md shadow-md z-10 w-48" onClick={(e) => e.stopPropagation()}>
                                  <div className="text-xs font-medium mb-1">Preset Colors</div>
                                  <div className="grid grid-cols-3 gap-1 mb-2">
                                    {presetColors.map((color) => (
                                      <button
                                        key={color}
                                        className="w-6 h-6 rounded-md border"
                                        style={{ backgroundColor: color }}
                                        onClick={() => {
                                          updateFundProperty(fund.id, 'color', color);
                                          setColorPickerOpen(null);
                                        }}
                                      />
                                    ))}
                                  </div>
                                  <div className="text-xs font-medium mb-1">Custom Color</div>
                                  <div className="flex items-center gap-2">
                                    <input
                                      type="color"
                                      value={fund.color || '#ffffff'}
                                      onChange={(e) => updateFundProperty(fund.id, 'color', e.target.value)}
                                      className="w-full h-6"
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => toggleEditMode(fund.id)}
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => handleDeleteFund(fund.id)}
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* KiwiSaver Funds Column */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                KiwiSaver Funds
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAddFund('kiwisaver')}
                  className="h-8"
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  Add Fund
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-2 pl-3 text-xs font-medium text-muted-foreground">Name</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Return (%)</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Std Dev (%)</th>
                      <th className="text-center p-2 text-xs font-medium text-muted-foreground">Income (%)</th>
                      <th className="text-right p-2 pr-3 text-xs font-medium text-muted-foreground">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {funds.filter(fund => fund.type === 'kiwisaver').map(fund => (
                      <tr
                        key={fund.id}
                        className="hover:bg-muted/30"
                        style={{ backgroundColor: fund.color ? `${fund.color}40` : 'transparent' }}
                      >
                        <td className="p-2 pl-3">
                          {fund.isEditing ? (
                            <Input
                              value={fund.name}
                              onChange={(e) => updateFundProperty(fund.id, 'name', e.target.value)}
                              className="h-8 w-full"
                            />
                          ) : (
                            <span>{fund.name}</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.return}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'return', value);
                              }}
                              step="0.1"
                              min="0"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.return}%</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.stdDev}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'stdDev', value);
                              }}
                              step="0.1"
                              min="0"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.stdDev}%</span>
                          )}
                        </td>
                        <td className="p-2 text-center">
                          {fund.isEditing ? (
                            <Input
                              type="number"
                              value={fund.incomePortion}
                              onChange={(e) => {
                                const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                                updateFundProperty(fund.id, 'incomePortion', value);
                              }}
                              step="5"
                              min="0"
                              max="100"
                              className="h-8 w-20 mx-auto text-center"
                            />
                          ) : (
                            <span>{fund.incomePortion}%</span>
                          )}
                        </td>
                        <td className="p-2 pr-3 text-right">
                          <div className="flex items-center justify-end gap-1">
                            <div className="relative">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-7 w-7 overflow-hidden"
                                onClick={() => setColorPickerOpen(colorPickerOpen === fund.id ? null : fund.id)}
                              >
                                <div
                                  className="absolute inset-0.5 rounded-sm"
                                  style={{ backgroundColor: fund.color || 'transparent' }}
                                ></div>
                                <span className="sr-only">Choose color</span>
                              </Button>

                              {colorPickerOpen === fund.id && (
                                <div className="absolute right-0 top-full mt-1 p-2 bg-white border rounded-md shadow-md z-10 w-48" onClick={(e) => e.stopPropagation()}>
                                  <div className="text-xs font-medium mb-1">Preset Colors</div>
                                  <div className="grid grid-cols-3 gap-1 mb-2">
                                    {presetColors.map((color) => (
                                      <button
                                        key={color}
                                        className="w-6 h-6 rounded-md border"
                                        style={{ backgroundColor: color }}
                                        onClick={() => {
                                          updateFundProperty(fund.id, 'color', color);
                                          setColorPickerOpen(null);
                                        }}
                                      />
                                    ))}
                                  </div>
                                  <div className="text-xs font-medium mb-1">Custom Color</div>
                                  <div className="flex items-center gap-2">
                                    <input
                                      type="color"
                                      value={fund.color || '#ffffff'}
                                      onChange={(e) => updateFundProperty(fund.id, 'color', e.target.value)}
                                      className="w-full h-6"
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => toggleEditMode(fund.id)}
                            >
                              <Edit className="h-3.5 w-3.5" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                              onClick={() => handleDeleteFund(fund.id)}
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
