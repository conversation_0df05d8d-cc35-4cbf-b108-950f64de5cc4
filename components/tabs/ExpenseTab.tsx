import { useState, useEffect, useCallback } from 'react';
import { Input } from "@/components/ui/input";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Button } from "@/components/ui/button";
import { Plus, X, ChevronDown, ChevronUp } from "lucide-react";
import { InputData } from '@/app/protected/planner/types';
import { Card } from "@/components/ui/card";

interface ExpenseData {
  id: number;
  annual_expense: number;
  expense_start_age: number;
  expense_end_age: number;
  expense_description: string;
  frequency?: number;
  is_additional?: boolean;
  inflation_rate?: number;
  isCollapsed?: boolean;
}

interface ExpenseTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
}

export function ExpenseTab({ inputData, setInputData, readOnly }: ExpenseTabProps) {
  const [expenses, setExpenses] = useState<ExpenseData[]>([]);
  // Generate a unique storage key for this component
  const storageKey = 'expenseTabAccordionState';

  // Initialize accordion states with default values or from localStorage
  const [showMainExpenses, setShowMainExpenses] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem(storageKey);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        return parsedState.showMainExpenses !== undefined ? parsedState.showMainExpenses : true;
      }
    }
    return true; // Default to open
  });

  // Store collapsed state of individual additional expenses
  const [collapsedExpenseIds, setCollapsedExpenseIds] = useState<Record<number, boolean>>(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('expenseItemsCollapseState');
      if (savedState) {
        return JSON.parse(savedState);
      }
    }
    return {}; // Default empty object
  });

  useEffect(() => {
    // Initialize main expenses from inputData
    const initialExpenses = [
      {
        id: 1,
        annual_expense: inputData.annual_expenses1,
        expense_start_age: inputData.expense_period1[0],
        expense_end_age: inputData.expense_period1[1],
        expense_description: 'Pre Retirement Expenses',
        frequency: 1,
        is_additional: false,
        inflation_rate: inputData.expense1_inflation_rate !== undefined ? inputData.expense1_inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
      }
    ];

    if (inputData.second_expense) {
      initialExpenses.push({
        id: 2,
        annual_expense: inputData.annual_expenses2,
        expense_start_age: inputData.expense_period2[0],
        expense_end_age: inputData.expense_period2[1],
        expense_description: 'Post Retirement Expenses',
        frequency: 1,
        is_additional: false,
        inflation_rate: inputData.expense2_inflation_rate !== undefined ? inputData.expense2_inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
      });
    }
    // Add any existing additional expenses
    if (inputData.additional_expenses && inputData.additional_expenses.length > 0) {
      const additionalExpenses = inputData.additional_expenses.map((expense, index) => {
        const id = initialExpenses.length + index + 1;

        // We don't need to set the collapsed state here anymore,
        // as the useState initializer handles loading from localStorage,
        // and user interactions handle toggling.
        // setCollapsedExpenseIds(prev => ({
        //   ...prev,
        //   [id]: true
        // }));

        return {
          id,
          annual_expense: expense.value,
          expense_start_age: expense.period[0],
          expense_end_age: expense.period[1],
          expense_description: expense.title || 'Additional Expense',
          frequency: expense.frequency || 1,
          is_additional: true,
          inflation_rate: expense.inflation_rate !== undefined ? expense.inflation_rate : (inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0)
      };
      });
      initialExpenses.push(...additionalExpenses);
    }

    setExpenses(initialExpenses);
  }, [inputData]);

  const handleInputChange = (id: number, name: string, value: string) => {
    if (!readOnly) {
      setExpenses(prevExpenses => {
        const updatedExpenses = prevExpenses.map(expense =>
          expense.id === id ? {
            ...expense,
            [name]: name === 'inflation_rate' ? (value === '' ? 0 : parseFloat(value)) : parseInt(value)
          } : expense
        );
        updateInputData(updatedExpenses);
        return updatedExpenses;
      });
    }
  };

  const handleDescriptionChange = (id: number, value: string) => {
    if (!readOnly) {
      setExpenses(prevExpenses => {
        const updatedExpenses = prevExpenses.map(expense =>
          expense.id === id ? { ...expense, expense_description: value } : expense
        );
        updateInputData(updatedExpenses);
        return updatedExpenses;
      });
    }
  };

  const updateInputData = (updatedExpenses: ExpenseData[]) => {
    setInputData(prevData => {
      const newData = { ...prevData };

      // Reset main expenses
      newData.annual_expenses1 = 0;
      newData.annual_expenses2 = 0;
      newData.second_expense = false;

      // Reset additional expenses
      newData.additional_expenses = [];

      updatedExpenses.forEach(expense => {
        if (!expense.is_additional) {
          if (expense.expense_description === 'Pre Retirement Expenses') {
            newData.annual_expenses1 = expense.annual_expense;
            newData.expense_period1 = [expense.expense_start_age, expense.expense_end_age];
            newData.expense1_inflation_rate = expense.inflation_rate ?? inputData.inflation_rate ?? 2.0;
          } else if (expense.expense_description === 'Post Retirement Expenses') {
            newData.second_expense = true;
            newData.annual_expenses2 = expense.annual_expense;
            newData.expense_period2 = [expense.expense_start_age, expense.expense_end_age];
            newData.expense2_inflation_rate = expense.inflation_rate ?? inputData.inflation_rate ?? 2.0;
          }
        } else {
          // Add to additional expenses array
          newData.additional_expenses?.push({
            title: expense.expense_description,
            value: expense.annual_expense,
            period: [expense.expense_start_age, expense.expense_end_age] as [number, number],
            frequency: expense.frequency || 1,
            inflation_rate: expense.inflation_rate
          });
        }
      });
      return newData;
    });
  };

  const addExpense = () => {
    if (!readOnly) {
      const newId = Math.max(...expenses.map(i => i.id), 0) + 1;
      const newExpense = {
        id: newId,
        annual_expense: 0,
        expense_start_age: Math.max(inputData.starting_age, inputData.starting_age),
        expense_end_age: Math.min(inputData.ending_age, inputData.ending_age),
        expense_description: 'Additional Expense',
        frequency: 1,
        is_additional: true,
        inflation_rate: inputData.inflation_rate !== undefined ? inputData.inflation_rate : 2.0
      };
      
      // Ensure the new expense's ages are within the scenario bounds
      if (newExpense.expense_start_age < inputData.starting_age) {
        newExpense.expense_start_age = inputData.starting_age;
      }
      if (newExpense.expense_end_age > inputData.ending_age) {
        newExpense.expense_end_age = inputData.ending_age;
      }

      // Set the new expense to be collapsed by default
      setCollapsedExpenseIds(prev => ({
        ...prev,
        [newId]: true
      }));
      setExpenses([...expenses, newExpense]);
      updateInputData([...expenses, newExpense]);
    }
  };

  const deleteExpense = (id: number) => {
    if (!readOnly) {
      const expenseToDelete = expenses.find(expense => expense.id === id);
      if (expenseToDelete?.is_additional) {
        const updatedExpenses = expenses.filter(expense => expense.id !== id);
        setExpenses(updatedExpenses);
        updateInputData(updatedExpenses);
      }
    }
  };

  // Save accordion states to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, JSON.stringify({
        showMainExpenses
      }));
    }
  }, [showMainExpenses, storageKey]);

  // Save collapsed state of individual additional expenses
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('expenseItemsCollapseState', JSON.stringify(collapsedExpenseIds));
    }
  }, [collapsedExpenseIds]);

  // Toggle accordion state for primary expenses
  const toggleMainExpenses = useCallback(() => {
    setShowMainExpenses((prev: any) => !prev);
  }, []);

  // Toggle collapse state for an expense
  const toggleExpenseCollapse = (id: number) => {
    // Update the collapsed state in our state object
    setCollapsedExpenseIds(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  return (
    <div className="space-y-6">
      {/* Main Expenses Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div
          className="flex justify-between items-center cursor-pointer mb-4"
          onClick={toggleMainExpenses}
        >
          <h3 className="text-lg font-medium">Primary Expenses</h3>
          <Button variant="ghost" size="sm">
            {showMainExpenses ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>
        </div>

        {showMainExpenses && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Pre Retirement Expenses Card */}
            {expenses.filter(expense => expense.expense_description === 'Pre Retirement Expenses').map(expense => (
              <Card key={expense.id} className="overflow-hidden border-l-4 border-l-blue-500">
                <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-2 flex justify-between items-center">
                  <h4 className="font-medium text-blue-700 dark:text-blue-300">{expense.expense_description}</h4>
                  <div className="text-sm font-semibold bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full">
                    ${expense.annual_expense.toLocaleString()}
                  </div>
                </div>

                <div className="p-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`annual_expense_${expense.id}`}
                        label="Annual Expense"
                        tooltipText="Total yearly spending for this expense category"
                      />
                      <Input
                        id={`annual_expense_${expense.id}`}
                        name="annual_expense"
                        type="number"
                        value={expense.annual_expense}
                        onChange={(e) => handleInputChange(expense.id, 'annual_expense', e.target.value)}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`inflation_rate_${expense.id}`}
                        label="Inflation Rate (%)"
                        tooltipText="Individual inflation rate for this expense. Overrides the default rate."
                      />
                      <Input
                        id={`inflation_rate_${expense.id}`}
                        name="inflation_rate"
                        type="number"
                        max={20}
                        step={0.5}
                        value={expense.inflation_rate}
                        onChange={(e) => {
                          const value = e.target.value === '' ? '0' : e.target.value;
                          handleInputChange(expense.id, 'inflation_rate', value);
                        }}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_start_age_${expense.id}`}
                        label="Start Age"
                        tooltipText={`Age when these expenses begin (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                      />
                      <Input
                        id={`expense_start_age_${expense.id}`}
                        name="expense_start_age"
                        type="number"
                        value={expense.expense_start_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_start_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure start age is not greater than end age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge > currentExpense.expense_end_age) {
                            handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                        }}
                        min={inputData.starting_age}
                        max={inputData.ending_age}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_end_age_${expense.id}`}
                        label="End Age"
                        tooltipText={`Age when these expenses stop (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                      />
                      <Input
                        id={`expense_end_age_${expense.id}`}
                        name="expense_end_age"
                        type="number"
                        value={expense.expense_end_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_end_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure end age is not less than start age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge < currentExpense.expense_start_age) {
                            handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                        }}
                        min={inputData.starting_age}
                        max={inputData.ending_age}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            {/* Post Retirement Expenses Card */}
            {inputData.second_expense && expenses.filter(expense => expense.expense_description === 'Post Retirement Expenses').map(expense => (
              <Card key={expense.id} className="overflow-hidden border-l-4 border-l-purple-500">
                <div className="bg-purple-50 dark:bg-purple-950/30 px-4 py-2 flex justify-between items-center">
                  <h4 className="font-medium text-purple-700 dark:text-purple-300">{expense.expense_description}</h4>
                  <div className="text-sm font-semibold bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full">
                    ${expense.annual_expense.toLocaleString()}
                  </div>
                </div>

                <div className="p-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`annual_expense_${expense.id}`}
                        label="Annual Expense"
                        tooltipText="Total yearly spending for this expense category"
                      />
                      <Input
                        id={`annual_expense_${expense.id}`}
                        name="annual_expense"
                        type="number"
                        value={expense.annual_expense}
                        onChange={(e) => handleInputChange(expense.id, 'annual_expense', e.target.value)}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`inflation_rate_${expense.id}`}
                        label="Inflation Rate (%)"
                        tooltipText="Individual inflation rate for this expense. Overrides the default rate."
                      />
                      <Input
                        id={`inflation_rate_${expense.id}`}
                        name="inflation_rate"
                        type="number"
                        max={20}
                        step={0.5}
                        value={expense.inflation_rate}
                        onChange={(e) => {
                          const value = e.target.value === '' ? '0' : e.target.value;
                          handleInputChange(expense.id, 'inflation_rate', value);
                        }}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_start_age_${expense.id}`}
                        label="Start Age"
                        tooltipText="Age when these expenses begin"
                      />
                      <Input
                        id={`expense_start_age_${expense.id}`}
                        name="expense_start_age"
                        type="number"
                        value={expense.expense_start_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_start_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure start age is not greater than end age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge > currentExpense.expense_end_age) {
                            handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                        }}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_end_age_${expense.id}`}
                        label="End Age"
                        tooltipText="Age when these expenses stop"
                      />
                      <Input
                        id={`expense_end_age_${expense.id}`}
                        name="expense_end_age"
                        type="number"
                        value={expense.expense_end_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_end_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure end age is not less than start age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge < currentExpense.expense_start_age) {
                            handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                        }}
                        readOnly={readOnly}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Additional Expenses Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Additional Expenses</h3>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {expenses.filter(expense => expense.is_additional).length} items
            </span>
          </div>
        </div>
        <div className="space-y-4">
          {expenses.filter(expense => expense.is_additional).map(expense => (
            <Card key={expense.id} className="overflow-hidden border-l-4 border-l-green-500">
              <div className="bg-green-50 dark:bg-green-950/30 px-4 py-2 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleExpenseCollapse(expense.id);
                    }}
                  >
                    {collapsedExpenseIds[expense.id] ?
                      <ChevronDown className="h-4 w-4 text-gray-500" /> :
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    }
                  </Button>
                  <div className="flex-grow">
                    <Input
                      id={`expense_description_${expense.id}`}
                      value={expense.expense_description}
                      onChange={(e) => handleDescriptionChange(expense.id, e.target.value)}
                      placeholder="Expense description"
                      readOnly={readOnly}
                      className="border-0 bg-transparent p-0 h-auto text-green-700 dark:text-green-300 font-medium focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-sm font-semibold bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full">
                    ${expense.annual_expense.toLocaleString()}
                  </div>
                  {collapsedExpenseIds[expense.id] && (
                    <span className="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300">
                      Age: {expense.expense_start_age} - {expense.expense_end_age}
                    </span>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteExpense(expense.id);
                    }}
                    className="text-gray-500 hover:text-red-500 p-1 h-auto"
                    disabled={readOnly}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {!collapsedExpenseIds[expense.id] && (
                <div className="p-4 space-y-4 bg-white dark:bg-gray-800">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`annual_expense_${expense.id}`}
                        label="Annual Expense"
                        tooltipText="Total yearly spending for this expense category"
                      />
                      <Input
                        id={`annual_expense_${expense.id}`}
                        name="annual_expense"
                        type="number"
                        value={expense.annual_expense}
                        onChange={(e) => handleInputChange(expense.id, 'annual_expense', e.target.value)}
                        readOnly={readOnly}
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`frequency_${expense.id}`}
                        label="Frequency (years)"
                        tooltipText="How often this expense occurs (1 = yearly, 2 = every other year, etc.)"
                      />
                      <Input
                        id={`frequency_${expense.id}`}
                        name="frequency"
                        type="number"
                        min="1"
                        value={expense.frequency || 1}
                        onChange={(e) => handleInputChange(expense.id, 'frequency', e.target.value)}
                        readOnly={readOnly}
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`inflation_rate_${expense.id}`}
                        label="Inflation Rate (%)"
                        tooltipText="Individual inflation rate for this expense. Overrides the default rate."
                      />
                      <Input
                        id={`inflation_rate_${expense.id}`}
                        name="inflation_rate"
                        type="number"
                        max={20}
                        step={0.5}
                        value={expense.inflation_rate}
                        onChange={(e) => {
                          const value = e.target.value === '' ? '0' : e.target.value;
                          handleInputChange(expense.id, 'inflation_rate', value);
                        }}
                        readOnly={readOnly}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_start_age_${expense.id}`}
                        label="Start Age"
                        tooltipText={`Age when these expenses begin (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                      />
                      <Input
                        id={`expense_start_age_${expense.id}`}
                        name="expense_start_age"
                        type="number"
                        value={expense.expense_start_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_start_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure start age is not greater than end age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge > currentExpense.expense_end_age) {
                            handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                        }}
                        min={inputData.starting_age}
                        max={inputData.ending_age}
                        readOnly={readOnly}
                      />
                    </div>
                    <div>
                      <LabelWithTooltip
                        htmlFor={`expense_end_age_${expense.id}`}
                        label="End Age"
                        tooltipText={`Age when these expenses stop (must be between ${inputData.starting_age} and ${inputData.ending_age})`}
                      />
                      <Input
                        id={`expense_end_age_${expense.id}`}
                        name="expense_end_age"
                        type="number"
                        value={expense.expense_end_age}
                        onChange={(e) => handleInputChange(expense.id, 'expense_end_age', e.target.value)}
                        onBlur={(e) => {
                          let newAge = Number(e.target.value) || 0;
                          // Apply 0-110 bounds
                          newAge = Math.max(0, Math.min(110, newAge));
                          // Apply scenario bounds
                          newAge = Math.max(inputData.starting_age, Math.min(inputData.ending_age, newAge));

                          // Ensure end age is not less than start age
                          const currentExpense = expenses.find(exp => exp.id === expense.id);
                          if (currentExpense && newAge < currentExpense.expense_start_age) {
                            handleInputChange(expense.id, 'expense_start_age', newAge.toString());
                          }

                          handleInputChange(expense.id, 'expense_end_age', newAge.toString());
                        }}
                        min={inputData.starting_age}
                        max={inputData.ending_age}
                        readOnly={readOnly}
                      />
                    </div>
                  </div>
                </div>
              )}
              </Card>
            ))}

          <Button onClick={addExpense} className="w-full mt-4" disabled={readOnly}>
            <Plus className="h-4 w-4 mr-2" />
            Add Expense
          </Button>
          </div>
      </div>
    </div>
  );
}
