import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FUND_TAILWIND_COLORS } from '@/app/constants/fundColors';
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Pencil, X, Trash2 } from "lucide-react";
import { InputData } from "../../app/protected/planner/types";
import { PropertyModal } from '../modals/PropertyModal';
import { DeleteConfirmModal } from '../modals/DeleteConfirmModal';
import { cn } from '@/lib/utils';

interface PropertyData {
  id: number;
  property_value: number;
  property_growth: number;
  debt: number;
  debt_ir: number;
  initial_debt_years: number;
  additional_debt_repayments: number;
  additional_debt_repayments_start_age?: number;
  additional_debt_repayments_end_age?: number;
  sell_property: boolean;
  show_repayments: boolean;
  show_purchase_details?: boolean;
  purchase_age?: number;
  deposit_amount?: number;
  deposit_sources?: Record<string, number> | {
    savings?: number;
    investments?: number;
    main_kiwisaver?: number;
    partner_kiwisaver?: number;
    gifting?: number;
    other?: number;
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
    [key: string]: number | undefined;
  };
  property_title?: string;
  main_property_sale_age?: number;
  main_prop_sale_value?: number;
  pay_off_debt?: boolean;
  sale_allocate_to_investment?: boolean;
  is_second_property?: boolean;
  property_title2?: string;
  property_title3?: string;
  property_title4?: string;
  property_title5?: string;
  property_index?: number;
  property_index2?: number;
  property_index3?: number;
  property_index4?: number;
  property_index5?: number;
  lump_sum_payment_age?: number;
  lump_sum_payment_age2?: number;
  lump_sum_payment_age3?: number;
  lump_sum_payment_age4?: number;
  lump_sum_payment_age5?: number;
  lump_sum_payment_amount?: number;
  lump_sum_payment_amount2?: number;
  lump_sum_payment_amount3?: number;
  lump_sum_payment_amount4?: number;
  lump_sum_payment_amount5?: number;
  lump_sum_payment_source?: string;
  lump_sum_payment_source2?: string;
  lump_sum_payment_source3?: string;
  lump_sum_payment_source4?: string;
  lump_sum_payment_source5?: string;
  interest_only_period?: boolean;
  interest_only_start_age?: number;
  interest_only_end_age?: number;
  // Rental income fields
  rental_income?: boolean;
  rental_amount?: number;
  rental_start_age?: number;
  rental_end_age?: number;
  // Board income fields
  board_income?: boolean;
  board_amount?: number;
  board_start_age?: number;
  board_end_age?: number;
}

interface PropertyTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
  allMetrics?: any[];
}

export function PropertyTab({ inputData, setInputData, readOnly = false, allMetrics = [] }: PropertyTabProps) {
  const [additionalProperties, setAdditionalProperties] = useState<PropertyData[]>([]);
  const [selectedProperty, setSelectedProperty] = useState<InputData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [propertyToDelete, setPropertyToDelete] = useState<PropertyData | null>(null);

  const calculateFuturePropertyValue = (saleAge: number) => {
    const yearsToSale = saleAge - inputData.starting_age;
    const totalPropertyValue = inputData.property_value + additionalProperties.reduce((sum, p) => sum + p.property_value, 0);
    const futureValue = totalPropertyValue * Math.pow(1 + (inputData.property_growth / 100), yearsToSale);
    return Math.round(futureValue);
  };

  const handleInputChange = (name: keyof InputData, value: string | number) => {
    if (readOnly) return;
    if (name === 'main_property_sale_age') {
      const saleAge = typeof value === 'string' ? parseFloat(value) : value;
      setInputData(prevData => ({
        ...prevData,
        [name]: saleAge,
        main_prop_sale_value: calculateFuturePropertyValue(saleAge),
      }));
    } else if (name === 'lump_sum_payment_source') {
      const sourceValue = value as 'investments' | 'savings';
      setInputData(prevData => ({
        ...prevData,
        [name]: sourceValue,
      }));
    } else {
      setInputData(prevData => ({
        ...prevData,
        [name]: typeof value === 'string' ? parseFloat(value) : value,
      }));
    }
  };

  const handleSourceChange = (value: 'investments' | 'savings') => {
    if (readOnly) return;
    setInputData(prevData => ({
      ...prevData,
      lump_sum_payment_source: value,
    }));
  };

  const handleCheckboxChange = (name: keyof InputData, checked: boolean) => {
    if (readOnly) return;
    setInputData(prevData => ({
      ...prevData,
      [name]: checked,
    }));
  };

  const handleAddProperty = () => {
    if (readOnly) return;
    const existingIndices = additionalProperties.map(p => p.property_index).filter(Boolean) as number[];
    const availableIndices = [2, 3, 4, 5].filter(idx => !existingIndices.includes(idx));
    const nextIndex = availableIndices.length > 0 ? availableIndices[0] : undefined;

    if (nextIndex) {
      const newProperty: PropertyData = {
        id: Date.now(),
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 0,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        sell_property: false,
        show_repayments: false,
        show_purchase_details: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {
          savings: 0,
          investments: 0,
          main_kiwisaver: 0,
          partner_kiwisaver: 0,
          gifting: 0,
          other: 0,
          fund1: 0,
          fund2: 0,
          fund3: 0,
          fund4: 0,
          fund5: 0
        } as Record<string, number>,
        property_title: `Property ${nextIndex}`,
        property_index: nextIndex,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0
      };

      // Update the inputData with the property title
      const propertyTitleField = `property_title${nextIndex}` as keyof InputData;
      setInputData(prevData => ({
        ...prevData,
        [propertyTitleField]: `Property ${nextIndex}`
      }));

      setAdditionalProperties([...additionalProperties, newProperty]);
    }
  };

  const handleRemoveProperty = (propertyId: number) => {
    if (readOnly) return;
    const propertyToRemove = additionalProperties.find(p => p.id === propertyId);
    if (propertyToRemove && propertyToRemove.property_index) {
      const index = propertyToRemove.property_index;

      const resetData: any = {};
      resetData[`property_value${index}`] = 0;
      resetData[`property_growth${index}`] = 0;
      resetData[`debt${index}`] = 0;
      resetData[`debt_ir${index}`] = 0;
      resetData[`debt_years${index}`] = 0;
      resetData[`additional_debt_repayments${index}`] = 0;
      resetData[`additional_debt_repayments_start_age${index}`] = 0;
      resetData[`additional_debt_repayments_end_age${index}`] = 0;
      resetData[`main_property_sale_age${index}`] = 0;
      resetData[`main_prop_sale_value${index}`] = 0;
      resetData[`pay_off_debt${index}`] = false;
      resetData[`sale${index}_allocate_to_investment`] = false;
      resetData[`sell_main_property${index}`] = false;
      resetData[`interest_only_period${index}`] = false;
      resetData[`interest_only_start_age${index}`] = 0;
      resetData[`interest_only_end_age${index}`] = 0;

      // Reset rental income fields
      resetData[`rental_income${index}`] = false;
      resetData[`rental_amount${index}`] = 0;
      resetData[`rental_start_age${index}`] = 0;
      resetData[`rental_end_age${index}`] = 0;

      // Reset board income fields
      resetData[`board_income${index}`] = false;
      resetData[`board_amount${index}`] = 0;
      resetData[`board_start_age${index}`] = 0;
      resetData[`board_end_age${index}`] = 0;

      // Reset lump sum payment fields
      resetData[`lump_sum_payment_age${index}`] = 0;
      resetData[`lump_sum_payment_amount${index}`] = 0;
      resetData[`lump_sum_payment_source${index}`] = 'investments';

      setInputData(prevData => ({
        ...prevData,
        ...resetData
      }));
    }

    setAdditionalProperties(additionalProperties.filter(p => p.id !== propertyId));
  };

  const handlePropertyInputChange = (propertyId: number, field: keyof PropertyData, value: number | boolean) => {
    if (readOnly) return;
    setAdditionalProperties(additionalProperties.map(property => {
      if (property.id === propertyId) {
        return { ...property, [field]: value };
      }
      return property;
    }));
  };

  const handleEditProperty = (property: InputData | PropertyData) => {
    if ('property_index' in property && property.property_index) {
      const propertyIndex = property.property_index;
      const propertyAsInputData = {
        ...inputData,
        property_value: property.property_value,
        property_growth: property.property_growth,
        debt: property.debt,
        debt_ir: property.debt_ir,
        initial_debt_years: property.initial_debt_years,
        additional_debt_repayments: property.additional_debt_repayments,
        additional_debt_repayments_start_age: property.additional_debt_repayments_start_age,
        additional_debt_repayments_end_age: property.additional_debt_repayments_end_age,
        sell_main_property: property.sell_property === true,
        show_repayments: property.show_repayments,
        show_purchase_details: property.show_purchase_details,
        purchase_age: property.purchase_age,
        deposit_amount: property.deposit_amount,
        deposit_sources: property.deposit_sources,
        property_title: property.property_title || `Property ${propertyIndex}`,
        main_property_sale_age: property.main_property_sale_age || 0,
        main_prop_sale_value: property.main_prop_sale_value || 0,
        pay_off_debt: property.pay_off_debt === true,
        sale_allocate_to_investment: property.sale_allocate_to_investment === true,
        property_index: propertyIndex,
        sell_property: property.sell_property,
        interest_only_period: property.interest_only_period,
        interest_only_start_age: property.interest_only_start_age,
        interest_only_end_age: property.interest_only_end_age,
        lump_sum_payment_age: property.lump_sum_payment_age,
        lump_sum_payment_amount: property.lump_sum_payment_amount,
        lump_sum_payment_source: property.lump_sum_payment_source,
      } as InputData;
      setSelectedProperty(propertyAsInputData);
    } else {
      setSelectedProperty(property as InputData);
    }
    setIsModalOpen(true);
  };

  const handlePropertyChange = (updatedProperty: InputData) => {

    if ('property_index' in updatedProperty && updatedProperty.property_index) {
      const propertyIndex = updatedProperty.property_index;


      const propertyValueField = `property_value${propertyIndex}` as keyof InputData;
      const propertyGrowthField = `property_growth${propertyIndex}` as keyof InputData;
      const debtField = `debt${propertyIndex}` as keyof InputData;
      const debtIrField = `debt_ir${propertyIndex}` as keyof InputData;
      const debtYearsField = `initial_debt_years${propertyIndex}` as keyof InputData;
      const additionalDebtRepaymentsField = `additional_debt_repayments${propertyIndex}` as keyof InputData;
      const additionalDebtRepaymentsStartAgeField = `additional_debt_repayments_start_age${propertyIndex}` as keyof InputData;
      const additionalDebtRepaymentsEndAgeField = `additional_debt_repayments_end_age${propertyIndex}` as keyof InputData;
      const mainPropertySaleAgeField = `main_property_sale_age${propertyIndex}` as keyof InputData;
      const mainPropSaleValueField = `main_prop_sale_value${propertyIndex}` as keyof InputData;
      const payOffDebtField = `pay_off_debt${propertyIndex}` as keyof InputData;
      const saleAllocateToInvestmentField = `sale${propertyIndex}_allocate_to_investment` as keyof InputData;
      const sellMainPropertyField = `sell_main_property${propertyIndex}` as keyof InputData;
      const interestOnlyPeriodField = `interest_only_period${propertyIndex}` as keyof InputData;
      const interestOnlyStartAgeField = `interest_only_start_age${propertyIndex}` as keyof InputData;
      const interestOnlyEndAgeField = `interest_only_end_age${propertyIndex}` as keyof InputData;
      const propertyTitleField = `property_title${propertyIndex}` as keyof InputData;

      // Lump sum payment fields
      const lumpSumPaymentAgeField = `lump_sum_payment_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const lumpSumPaymentAmountField = `lump_sum_payment_amount${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const lumpSumPaymentSourceField = `lump_sum_payment_source${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;

      // Rental income fields
      const rentalIncomeField = `rental_income${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const rentalAmountField = `rental_amount${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const rentalStartAgeField = `rental_start_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const rentalEndAgeField = `rental_end_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;

      // Board income fields
      const boardIncomeField = `board_income${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const boardAmountField = `board_amount${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const boardStartAgeField = `board_start_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const boardEndAgeField = `board_end_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;

      const updateData: any = {};
      updateData[propertyValueField] = updatedProperty.property_value;
      updateData[propertyGrowthField] = updatedProperty.property_growth;
      updateData[debtField] = updatedProperty.debt;
      updateData[debtIrField] = updatedProperty.debt_ir;
      updateData[debtYearsField] = updatedProperty.initial_debt_years;
      updateData[additionalDebtRepaymentsField] = updatedProperty.additional_debt_repayments;

      // Add additional repayment start and end ages
      const additionalRepaymentStartAgeKey = `additional_debt_repayments_start_age${propertyIndex === 1 ? '' : propertyIndex}`;
      const additionalRepaymentEndAgeKey = `additional_debt_repayments_end_age${propertyIndex === 1 ? '' : propertyIndex}`;

      updateData[additionalDebtRepaymentsStartAgeField] = updatedProperty[additionalRepaymentStartAgeKey as keyof InputData];
      updateData[additionalDebtRepaymentsEndAgeField] = updatedProperty[additionalRepaymentEndAgeKey as keyof InputData];
      updateData[mainPropertySaleAgeField] = updatedProperty.main_property_sale_age;
      updateData[mainPropSaleValueField] = updatedProperty.main_prop_sale_value;
      updateData[payOffDebtField] = Boolean(updatedProperty.pay_off_debt);
      updateData[saleAllocateToInvestmentField] = Boolean(updatedProperty.sale_allocate_to_investment);
      updateData[sellMainPropertyField] = Boolean(updatedProperty.sell_main_property);

      // Add purchase details fields
      const showPurchaseDetailsField = `show_purchase_details${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const purchaseAgeField = `purchase_age${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const depositAmountField = `deposit_amount${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;
      const depositSourcesField = `deposit_sources${propertyIndex === 1 ? '' : propertyIndex}` as keyof InputData;

      updateData[showPurchaseDetailsField] = updatedProperty[showPurchaseDetailsField];
      updateData[purchaseAgeField] = updatedProperty[purchaseAgeField];
      updateData[depositAmountField] = updatedProperty[depositAmountField];
      updateData[depositSourcesField] = updatedProperty[depositSourcesField];

      // Check if deposit_sources has fund-specific withdrawals
      let depositSources = updatedProperty[depositSourcesField];

      // Ensure deposit_sources is a proper object
      if (typeof depositSources !== 'object' || depositSources === null) {
        depositSources = {
          savings: 0,
          investments: 0,
          main_kiwisaver: 0,
          partner_kiwisaver: 0,
          gifting: 0,
          other: 0
        };

        // Update the property and updateData with the new deposit sources
        (updatedProperty as any)[depositSourcesField] = depositSources;
        updateData[depositSourcesField] = depositSources;
      }

      // Check for fund-specific withdrawals
      for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
        const fundKey = `fund${fundNumber}`;
        if (depositSources[fundKey] && depositSources[fundKey] > 0) {
        }
      }

      // Add interest-only period fields
      const interestOnlyPeriodKey = `interest_only_period${propertyIndex === 1 ? '' : propertyIndex}`;
      const interestOnlyStartAgeKey = `interest_only_start_age${propertyIndex === 1 ? '' : propertyIndex}`;
      const interestOnlyEndAgeKey = `interest_only_end_age${propertyIndex === 1 ? '' : propertyIndex}`;

      // For property 1, use the base field names without numbers
      if (propertyIndex === 1) {
        updateData[interestOnlyPeriodField] = Boolean(updatedProperty.interest_only_period);
        updateData[interestOnlyStartAgeField] = updatedProperty.interest_only_start_age;
        updateData[interestOnlyEndAgeField] = updatedProperty.interest_only_end_age;

        // Lump sum payment fields for property 1
        updateData[lumpSumPaymentAgeField] = updatedProperty.lump_sum_payment_age;
        updateData[lumpSumPaymentAmountField] = updatedProperty.lump_sum_payment_amount;
        updateData[lumpSumPaymentSourceField] = updatedProperty.lump_sum_payment_source;

        // Rental income fields for property 1
        updateData[rentalIncomeField] = Boolean(updatedProperty.rental_income);
        updateData[rentalAmountField] = updatedProperty.rental_amount;
        updateData[rentalStartAgeField] = updatedProperty.rental_start_age;
        updateData[rentalEndAgeField] = updatedProperty.rental_end_age;

        // Board income fields for property 1
        updateData[boardIncomeField] = Boolean(updatedProperty.board_income);
        updateData[boardAmountField] = updatedProperty.board_amount;
        updateData[boardStartAgeField] = updatedProperty.board_start_age;
        updateData[boardEndAgeField] = updatedProperty.board_end_age;
      } else {
        // For properties 2-5, use the numbered field names
        updateData[interestOnlyPeriodField] = Boolean(updatedProperty[interestOnlyPeriodKey as keyof InputData]);
        updateData[interestOnlyStartAgeField] = updatedProperty[interestOnlyStartAgeKey as keyof InputData];
        updateData[interestOnlyEndAgeField] = updatedProperty[interestOnlyEndAgeKey as keyof InputData];

        // Lump sum payment fields for properties 2-5
        updateData[lumpSumPaymentAgeField] = updatedProperty[`lump_sum_payment_age${propertyIndex}` as keyof InputData];
        updateData[lumpSumPaymentAmountField] = updatedProperty[`lump_sum_payment_amount${propertyIndex}` as keyof InputData];
        updateData[lumpSumPaymentSourceField] = updatedProperty[`lump_sum_payment_source${propertyIndex}` as keyof InputData];

        // Rental income fields for properties 2-5
        updateData[rentalIncomeField] = Boolean(updatedProperty[`rental_income${propertyIndex}` as keyof InputData]);
        updateData[rentalAmountField] = updatedProperty[`rental_amount${propertyIndex}` as keyof InputData];
        updateData[rentalStartAgeField] = updatedProperty[`rental_start_age${propertyIndex}` as keyof InputData];
        updateData[rentalEndAgeField] = updatedProperty[`rental_end_age${propertyIndex}` as keyof InputData];

        // Board income fields for properties 2-5
        updateData[boardIncomeField] = Boolean(updatedProperty[`board_income${propertyIndex}` as keyof InputData]);
        updateData[boardAmountField] = updatedProperty[`board_amount${propertyIndex}` as keyof InputData];
        updateData[boardStartAgeField] = updatedProperty[`board_start_age${propertyIndex}` as keyof InputData];
        updateData[boardEndAgeField] = updatedProperty[`board_end_age${propertyIndex}` as keyof InputData];
      }

      // Add property title to updateData
      if (propertyIndex > 1) {
        updateData[propertyTitleField] = updatedProperty.property_title || `Property ${propertyIndex}`;
      }

      setInputData(prevData => {
        const updatedData = {
          ...prevData,
          ...updateData
        };

        return updatedData;
      });

      setAdditionalProperties(prev => {
        const propertyIndex = prev.findIndex(p => p.property_index === updatedProperty.property_index);

        if (propertyIndex >= 0) {
          const updatedProperties = [...prev];
          updatedProperties[propertyIndex] = {
            ...updatedProperties[propertyIndex],
            property_value: updatedProperty.property_value,
            property_growth: updatedProperty.property_growth,
            debt: updatedProperty.debt,
            debt_ir: updatedProperty.debt_ir,
            initial_debt_years: updatedProperty.initial_debt_years,
            additional_debt_repayments: updatedProperty.additional_debt_repayments,
            additional_debt_repayments_start_age: updatedProperty[`additional_debt_repayments_start_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            additional_debt_repayments_end_age: updatedProperty[`additional_debt_repayments_end_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            sell_property: updatedProperty.sell_main_property || false,
            property_title: updatedProperty.property_title || `Property ${updatedProperty.property_index}`,
            main_property_sale_age: updatedProperty.main_property_sale_age,
            main_prop_sale_value: updatedProperty.main_prop_sale_value,
            pay_off_debt: updatedProperty.pay_off_debt === true,
            sale_allocate_to_investment: updatedProperty.sale_allocate_to_investment === true,
            show_purchase_details: updatedProperty[`show_purchase_details${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            purchase_age: updatedProperty[`purchase_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            deposit_amount: updatedProperty[`deposit_amount${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            deposit_sources: updatedProperty[`deposit_sources${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            interest_only_period: updatedProperty[`interest_only_period${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            interest_only_start_age: updatedProperty[`interest_only_start_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            interest_only_end_age: updatedProperty[`interest_only_end_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            rental_income: updatedProperty[`rental_income${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            rental_amount: updatedProperty[`rental_amount${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            rental_start_age: updatedProperty[`rental_start_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            rental_end_age: updatedProperty[`rental_end_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            board_income: updatedProperty[`board_income${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            board_amount: updatedProperty[`board_amount${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            board_start_age: updatedProperty[`board_start_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            board_end_age: updatedProperty[`board_end_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            lump_sum_payment_age: updatedProperty[`lump_sum_payment_age${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            lump_sum_payment_amount: updatedProperty[`lump_sum_payment_amount${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData],
            lump_sum_payment_source: updatedProperty[`lump_sum_payment_source${updatedProperty.property_index === 1 ? '' : updatedProperty.property_index}` as keyof InputData]
          };
          return updatedProperties;
        }
        return prev;
      });
    } else {
      setInputData(() => ({
        ...updatedProperty,
        downsize: updatedProperty.downsize_age !== undefined,
        downsize_age: updatedProperty.downsize_age,
        new_property_value: updatedProperty.new_property_value,
        allocate_to_investment: updatedProperty.allocate_to_investment || false,
        pay_off_debt_on_downsize: updatedProperty.pay_off_debt
      }));
    }
    setIsModalOpen(false);
    setSelectedProperty(null);
  };

  const handleCloseModal = () => {
    setSelectedProperty(null);
    setIsModalOpen(false);
  };

  const handleOpenDeleteModal = (property: PropertyData) => {
    if (readOnly) return;
    setPropertyToDelete(property);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setPropertyToDelete(null);
    setIsDeleteModalOpen(false);
  };

  const handleConfirmDelete = () => {
    if (propertyToDelete) {
      handleRemoveProperty(propertyToDelete.id);
      setIsDeleteModalOpen(false);
      setPropertyToDelete(null);
    }
  };

  useEffect(() => {

    const newProperties: PropertyData[] = [];

    if (inputData.property_value2 && !additionalProperties.some(p => p.property_index === 2)) {
      newProperties.push({
        id: Date.now() + 2,
        property_value: inputData.property_value2 || 0,
        property_growth: inputData.property_growth2 || 0,
        debt: inputData.debt2 || 0,
        debt_ir: inputData.debt_ir2 || 0,
        initial_debt_years: inputData.initial_debt_years2 || 0,
        additional_debt_repayments: inputData.additional_debt_repayments2 || 0,
        additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age2 || 0,
        additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age2 || 0,
        sell_property: Boolean(inputData.sell_main_property2),
        show_repayments: inputData.show_repayments || false,
        property_title: inputData.property_title2 || 'Property 2',
        main_property_sale_age: inputData.main_property_sale_age2 || 0,
        main_prop_sale_value: inputData.main_prop_sale_value2 || 0,
        pay_off_debt: Boolean(inputData.pay_off_debt2),
        sale_allocate_to_investment: Boolean(inputData.sale2_allocate_to_investment),
        property_index: 2,
        interest_only_period: Boolean(inputData.interest_only_period2),
        interest_only_start_age: inputData.interest_only_start_age2 || 0,
        interest_only_end_age: inputData.interest_only_end_age2 || 0,
        rental_income: Boolean(inputData.rental_income2),
        rental_amount: inputData.rental_amount2 || 0,
        rental_start_age: inputData.rental_start_age2 || 0,
        rental_end_age: inputData.rental_end_age2 || 0,
        board_income: Boolean(inputData.board_income2),
        board_amount: inputData.board_amount2 || 0,
        board_start_age: inputData.board_start_age2 || 0,
        board_end_age: inputData.board_end_age2 || 0,
        lump_sum_payment_age: inputData.lump_sum_payment_age2 || 0,
        lump_sum_payment_amount: inputData.lump_sum_payment_amount2 || 0,
        lump_sum_payment_source: inputData.lump_sum_payment_source2 || 'investments'
      });
    }

    if (inputData.property_value3 && !additionalProperties.some(p => p.property_index === 3)) {
      newProperties.push({
        id: Date.now() + 3,
        property_value: inputData.property_value3 || 0,
        property_growth: inputData.property_growth3 || 0,
        debt: inputData.debt3 || 0,
        debt_ir: inputData.debt_ir3 || 0,
        initial_debt_years: inputData.initial_debt_years3 || 0,
        additional_debt_repayments: inputData.additional_debt_repayments3 || 0,
        additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age3 || 0,
        additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age3 || 0,
        sell_property: Boolean(inputData.sell_main_property3),
        show_repayments: inputData.show_repayments || false,
        property_title: inputData.property_title3 || 'Property 3',
        main_property_sale_age: inputData.main_property_sale_age3 || 0,
        main_prop_sale_value: inputData.main_prop_sale_value3 || 0,
        pay_off_debt: Boolean(inputData.pay_off_debt3),
        sale_allocate_to_investment: Boolean(inputData.sale3_allocate_to_investment),
        property_index: 3,
        interest_only_period: Boolean(inputData.interest_only_period3),
        interest_only_start_age: inputData.interest_only_start_age3 || 0,
        interest_only_end_age: inputData.interest_only_end_age3 || 0,
        rental_income: Boolean(inputData.rental_income3),
        rental_amount: inputData.rental_amount3 || 0,
        rental_start_age: inputData.rental_start_age3 || 0,
        rental_end_age: inputData.rental_end_age3 || 0,
        board_income: Boolean(inputData.board_income3),
        board_amount: inputData.board_amount3 || 0,
        board_start_age: inputData.board_start_age3 || 0,
        board_end_age: inputData.board_end_age3 || 0,
        lump_sum_payment_age: inputData.lump_sum_payment_age3 || 0,
        lump_sum_payment_amount: inputData.lump_sum_payment_amount3 || 0,
        lump_sum_payment_source: inputData.lump_sum_payment_source3 || 'investments'
      });
    }

    if (inputData.property_value4 && !additionalProperties.some(p => p.property_index === 4)) {
      newProperties.push({
        id: Date.now() + 4,
        property_value: inputData.property_value4 || 0,
        property_growth: inputData.property_growth4 || 0,
        debt: inputData.debt4 || 0,
        debt_ir: inputData.debt_ir4 || 0,
        initial_debt_years: inputData.initial_debt_years4 || 0,
        additional_debt_repayments: inputData.additional_debt_repayments4 || 0,
        additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age4 || 0,
        additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age4 || 0,
        sell_property: Boolean(inputData.sell_main_property4),
        show_repayments: inputData.show_repayments || false,
        property_title: inputData.property_title4 || 'Property 4',
        main_property_sale_age: inputData.main_property_sale_age4 || 0,
        main_prop_sale_value: inputData.main_prop_sale_value4 || 0,
        pay_off_debt: Boolean(inputData.pay_off_debt4),
        sale_allocate_to_investment: Boolean(inputData.sale4_allocate_to_investment),
        property_index: 4,
        interest_only_period: Boolean(inputData.interest_only_period4),
        interest_only_start_age: inputData.interest_only_start_age4 || 0,
        interest_only_end_age: inputData.interest_only_end_age4 || 0,
        rental_income: Boolean(inputData.rental_income4),
        rental_amount: inputData.rental_amount4 || 0,
        rental_start_age: inputData.rental_start_age4 || 0,
        rental_end_age: inputData.rental_end_age4 || 0,
        board_income: Boolean(inputData.board_income4),
        board_amount: inputData.board_amount4 || 0,
        board_start_age: inputData.board_start_age4 || 0,
        board_end_age: inputData.board_end_age4 || 0,
        lump_sum_payment_age: inputData.lump_sum_payment_age4 || 0,
        lump_sum_payment_amount: inputData.lump_sum_payment_amount4 || 0,
        lump_sum_payment_source: inputData.lump_sum_payment_source4 || 'investments'
      });
    }

    if (inputData.property_value5 && !additionalProperties.some(p => p.property_index === 5)) {
      newProperties.push({
        id: Date.now() + 5,
        property_value: inputData.property_value5 || 0,
        property_growth: inputData.property_growth5 || 0,
        debt: inputData.debt5 || 0,
        debt_ir: inputData.debt_ir5 || 0,
        initial_debt_years: inputData.initial_debt_years5 || 0,
        additional_debt_repayments: inputData.additional_debt_repayments5 || 0,
        additional_debt_repayments_start_age: inputData.additional_debt_repayments_start_age5 || 0,
        additional_debt_repayments_end_age: inputData.additional_debt_repayments_end_age5 || 0,
        sell_property: Boolean(inputData.sell_main_property5),
        show_repayments: inputData.show_repayments || false,
        property_title: inputData.property_title5 || 'Property 5',
        main_property_sale_age: inputData.main_property_sale_age5 || 0,
        main_prop_sale_value: inputData.main_prop_sale_value5 || 0,
        pay_off_debt: Boolean(inputData.pay_off_debt5),
        sale_allocate_to_investment: Boolean(inputData.sale5_allocate_to_investment),
        property_index: 5,
        interest_only_period: Boolean(inputData.interest_only_period5),
        interest_only_start_age: inputData.interest_only_start_age5 || 0,
        interest_only_end_age: inputData.interest_only_end_age5 || 0,
        rental_income: Boolean(inputData.rental_income5),
        rental_amount: inputData.rental_amount5 || 0,
        rental_start_age: inputData.rental_start_age5 || 0,
        rental_end_age: inputData.rental_end_age5 || 0,
        board_income: Boolean(inputData.board_income5),
        board_amount: inputData.board_amount5 || 0,
        board_start_age: inputData.board_start_age5 || 0,
        board_end_age: inputData.board_end_age5 || 0,
        lump_sum_payment_age: inputData.lump_sum_payment_age5 || 0,
        lump_sum_payment_amount: inputData.lump_sum_payment_amount5 || 0,
        lump_sum_payment_source: inputData.lump_sum_payment_source5 || 'investments'
      });
    }

    if (newProperties.length > 0) {
      setAdditionalProperties(prev => {
        const existingIndices = prev.map(p => p.property_index);
        const filteredNewProps = newProperties.filter(p => !existingIndices.includes(p.property_index));
        const updatedProperties = [...prev, ...filteredNewProps];

        return updatedProperties;
      });
    }
  }, [
    // Property values
    inputData.property_value2, inputData.property_value3,
    inputData.property_value4, inputData.property_value5,
    // Property titles
    inputData.property_title, inputData.property_title2,
    inputData.property_title3, inputData.property_title4,
    inputData.property_title5,
    // Sell main property flags
    inputData.sell_main_property2, inputData.sell_main_property3,
    inputData.sell_main_property4, inputData.sell_main_property5,
    // Sale allocate to investment flags
    inputData.sale2_allocate_to_investment, inputData.sale3_allocate_to_investment,
    inputData.sale4_allocate_to_investment, inputData.sale5_allocate_to_investment
  ]);

  useEffect(() => {
    const updateProperties = (propertyIndex: number) => {
      if (additionalProperties.some(p => p.property_index === propertyIndex)) {
        setAdditionalProperties(prev => {
          const updatedProperties = [...prev];
          const propIndex = updatedProperties.findIndex(p => p.property_index === propertyIndex);

          if (propIndex >= 0) {
            const propValueField = `property_value${propertyIndex}` as keyof InputData;
            const propGrowthField = `property_growth${propertyIndex}` as keyof InputData;
            const debtField = `debt${propertyIndex}` as keyof InputData;
            const debtIrField = `debt_ir${propertyIndex}` as keyof InputData;
            const debtYearsField = `initial_debt_years${propertyIndex}` as keyof InputData;
            const additionalDebtRepaymentsField = `additional_debt_repayments${propertyIndex}` as keyof InputData;
            const additionalDebtRepaymentsStartAgeField = `additional_debt_repayments_start_age${propertyIndex}` as keyof InputData;
            const additionalDebtRepaymentsEndAgeField = `additional_debt_repayments_end_age${propertyIndex}` as keyof InputData;
            const sellMainPropertyField = `sell_main_property${propertyIndex}` as keyof InputData;
            const mainPropertySaleAgeField = `main_property_sale_age${propertyIndex}` as keyof InputData;
            const mainPropSaleValueField = `main_prop_sale_value${propertyIndex}` as keyof InputData;
            const payOffDebtField = `pay_off_debt${propertyIndex}` as keyof InputData;
            const saleAllocateToInvestmentField = `sale${propertyIndex}_allocate_to_investment` as keyof InputData;
            const interestOnlyPeriodField = `interest_only_period${propertyIndex}` as keyof InputData;
            const interestOnlyStartAgeField = `interest_only_start_age${propertyIndex}` as keyof InputData;
            const interestOnlyEndAgeField = `interest_only_end_age${propertyIndex}` as keyof InputData;

            // Lump sum payment fields
            const lumpSumPaymentAgeField = `lump_sum_payment_age${propertyIndex}` as keyof InputData;
            const lumpSumPaymentAmountField = `lump_sum_payment_amount${propertyIndex}` as keyof InputData;
            const lumpSumPaymentSourceField = `lump_sum_payment_source${propertyIndex}` as keyof InputData;

            // Rental income fields
            const rentalIncomeField = `rental_income${propertyIndex}` as keyof InputData;
            const rentalAmountField = `rental_amount${propertyIndex}` as keyof InputData;
            const rentalStartAgeField = `rental_start_age${propertyIndex}` as keyof InputData;
            const rentalEndAgeField = `rental_end_age${propertyIndex}` as keyof InputData;

            // Board income fields
            const boardIncomeField = `board_income${propertyIndex}` as keyof InputData;
            const boardAmountField = `board_amount${propertyIndex}` as keyof InputData;
            const boardStartAgeField = `board_start_age${propertyIndex}` as keyof InputData;
            const boardEndAgeField = `board_end_age${propertyIndex}` as keyof InputData;

            updatedProperties[propIndex] = {
              ...updatedProperties[propIndex],
              property_value: inputData[propValueField] as number || 0,
              property_growth: inputData[propGrowthField] as number || 0,
              debt: inputData[debtField] as number || 0,
              debt_ir: inputData[debtIrField] as number || 0,
              initial_debt_years: inputData[debtYearsField] as number || 0,
              additional_debt_repayments: inputData[additionalDebtRepaymentsField] as number || 0,
              additional_debt_repayments_start_age: inputData[additionalDebtRepaymentsStartAgeField] as number || 0,
              additional_debt_repayments_end_age: inputData[additionalDebtRepaymentsEndAgeField] as number || 0,
              sell_property: inputData[sellMainPropertyField] as boolean || false,
              main_property_sale_age: inputData[mainPropertySaleAgeField] as number || 0,
              main_prop_sale_value: inputData[mainPropSaleValueField] as number || 0,
              pay_off_debt: inputData[payOffDebtField] as boolean || false,
              sale_allocate_to_investment: inputData[saleAllocateToInvestmentField] as boolean || false,
              interest_only_period: inputData[interestOnlyPeriodField] as boolean || false,
              interest_only_start_age: inputData[interestOnlyStartAgeField] as number || 0,
              interest_only_end_age: inputData[interestOnlyEndAgeField] as number || 0,
              lump_sum_payment_age: inputData[lumpSumPaymentAgeField] as number || 0,
              lump_sum_payment_amount: inputData[lumpSumPaymentAmountField] as number || 0,
              lump_sum_payment_source: inputData[lumpSumPaymentSourceField] as string || 'investments',
              rental_income: inputData[rentalIncomeField] as boolean || false,
              rental_amount: inputData[rentalAmountField] as number || 0,
              rental_start_age: inputData[rentalStartAgeField] as number || 0,
              rental_end_age: inputData[rentalEndAgeField] as number || 0,
              board_income: inputData[boardIncomeField] as boolean || false,
              board_amount: inputData[boardAmountField] as number || 0,
              board_start_age: inputData[boardStartAgeField] as number || 0,
              board_end_age: inputData[boardEndAgeField] as number || 0
            };
          }

          return updatedProperties;
        });
      }
    }

    updateProperties(2);
    updateProperties(3);
    updateProperties(4);
    updateProperties(5);
  }, [
    inputData.property_value2, inputData.property_growth2, inputData.debt2, inputData.debt_ir2,
    inputData.property_value3, inputData.property_growth3, inputData.debt3, inputData.debt_ir3,
    inputData.property_value4, inputData.property_growth4, inputData.debt4, inputData.debt_ir4,
    inputData.property_value5, inputData.property_growth5, inputData.debt5, inputData.debt_ir5,
    // Rental income fields
    inputData.rental_income2, inputData.rental_amount2, inputData.rental_start_age2, inputData.rental_end_age2,
    inputData.rental_income3, inputData.rental_amount3, inputData.rental_start_age3, inputData.rental_end_age3,
    inputData.rental_income4, inputData.rental_amount4, inputData.rental_start_age4, inputData.rental_end_age4,
    inputData.rental_income5, inputData.rental_amount5, inputData.rental_start_age5, inputData.rental_end_age5,
    // Board income fields
    inputData.board_income2, inputData.board_amount2, inputData.board_start_age2, inputData.board_end_age2,
    inputData.board_income3, inputData.board_amount3, inputData.board_start_age3, inputData.board_end_age3,
    inputData.board_income4, inputData.board_amount4, inputData.board_start_age4, inputData.board_end_age4,
    inputData.board_income5, inputData.board_amount5, inputData.board_start_age5, inputData.board_end_age5,
    // Lump sum payment fields
    inputData.lump_sum_payment_age2, inputData.lump_sum_payment_amount2, inputData.lump_sum_payment_source2,
    inputData.lump_sum_payment_age3, inputData.lump_sum_payment_amount3, inputData.lump_sum_payment_source3,
    inputData.lump_sum_payment_age4, inputData.lump_sum_payment_amount4, inputData.lump_sum_payment_source4,
    inputData.lump_sum_payment_age5, inputData.lump_sum_payment_amount5, inputData.lump_sum_payment_source5,
    // Additional repayment start and end ages
    inputData.additional_debt_repayments_start_age2, inputData.additional_debt_repayments_end_age2,
    inputData.additional_debt_repayments_start_age3, inputData.additional_debt_repayments_end_age3,
    inputData.additional_debt_repayments_start_age4, inputData.additional_debt_repayments_end_age4,
    inputData.additional_debt_repayments_start_age5, inputData.additional_debt_repayments_end_age5
  ]);

  const inputClassName = cn(
    "transition-colors",
    readOnly && "bg-muted text-muted-foreground cursor-not-allowed"
  );

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-md">
        <Card className="p-3 bg-white dark:bg-gray-800 flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <Badge
              variant="outline"
              style={{ backgroundColor: '#008FFB80', borderColor: '#008FFB80', color: 'white' }}
            >
              1
            </Badge>
            <div className="font-medium">
              {inputData.property_title || 'Main Property'}
              <div className="text-sm text-muted-foreground">
                {inputData.property_value ? `Value: $${inputData.property_value.toLocaleString()}` : ''} {inputData.debt ? `| Debt: $${inputData.debt.toLocaleString()}` : ''}
                {inputData.rental_income && inputData.rental_amount ? ` | Rent: $${inputData.rental_amount}/yr` : ''}
                {inputData.board_income && inputData.board_amount ? ` | Board: $${inputData.board_amount}/yr` : ''}
                {inputData.main_property_sale_age && inputData.main_property_sale_age > 0 ? ` | Sale Age: ${inputData.main_property_sale_age}` : ''}
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditProperty(inputData)}
            disabled={readOnly}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Pencil className="h-4 w-4" />
          </Button>
        </Card>

        {additionalProperties.map((property, index) => {
          const propertyNumber = index + 2;
          const colors = ['#00E396', '#FEB019', '#FF4560', '#775DD0'];
          const color = colors[propertyNumber - 2] || '#008FFB';
          
          return (
            <Card key={property.id} className="p-3 bg-white dark:bg-gray-800 flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <Badge
                  variant="outline"
                  style={{ backgroundColor: color, borderColor: color, color: 'white' }}
                >
                  {propertyNumber}
                </Badge>
                <div className="font-medium">
                {property.property_title || `Property ${propertyNumber}`}
                <div className="text-sm text-muted-foreground">
                  {property.property_value ? `Value: $${property.property_value.toLocaleString()}` : ''} {property.debt ? `| Debt: $${property.debt.toLocaleString()}` : ''}
                  {property.rental_income && property.rental_amount ? ` | Rent: $${property.rental_amount}/yr` : ''}
                  {property.board_income && property.board_amount ? ` | Board: $${property.board_amount}/yr` : ''}
                  {property.main_property_sale_age && property.main_property_sale_age > 0 ? ` | Sale Age: ${property.main_property_sale_age}` : ''}
                </div>
              </div>
              </div>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleOpenDeleteModal(property)}
                  disabled={readOnly}
                  className="text-destructive hover:text-destructive/80"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditProperty(property as unknown as InputData)}
                  disabled={readOnly}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <Pencil className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          );
        })}

        {/* Show Add Property button if less than 5 additional properties (total 6 including main) */}
        {additionalProperties.length < 5 && !readOnly ? (
          <Button
            variant="outline"
            className="w-full mt-2"
            onClick={handleAddProperty}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Button>
        ) : (
          !readOnly && (
            <div className="w-full mt-2 text-center text-muted-foreground">
              Maximum 5 Properties reached
            </div>
          )
        )}
      </div>

      <div className="mt-4 ml-1">
        {/* Show Repayments checkbox removed and moved to Property.tsx */}
      </div>

      {selectedProperty && (
        <PropertyModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          property={selectedProperty}
          onPropertyChange={handlePropertyChange}
          readOnly={readOnly}
          allMetrics={allMetrics}
        />
      )}

      {propertyToDelete && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          onConfirm={handleConfirmDelete}
          title={propertyToDelete.property_title || `Property ${propertyToDelete.property_index}`}
        />
      )}
    </div>
  );
}
