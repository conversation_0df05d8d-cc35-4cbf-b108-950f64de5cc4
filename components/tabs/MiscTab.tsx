import { useState, useEffect } from 'react';
import { Checkbox } from "@/components/ui/checkbox";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SaveScenarioModal from '@/components/modals/SaveScenarioModal';
import PresentationSaveScenarioModal from '@/components/modals/PresentationSaveScenarioModal';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { useFinancialCalculations } from '@/app/utils/financialCalculations';
import html2canvas from 'html2canvas';
import { useRef, createRef } from 'react';
import { Switch } from "@/components/ui/switch";
import DownloadPDFModal from '@/components/modals/DownloadPDFModal';
import { generateScenarioSummary } from '@/app/utils/aiAgent';
import chartConfig from '@/app/protected/planner/page';
import { useRouter, usePathname } from 'next/navigation';

interface MiscTabProps {
  onCheckboxChange: (data: any) => void;
  onInputChange: (name: string, value: number) => void;
  onDataChange: (name: string, value: boolean) => void;
  inputData: any;
  currentScenarioId: number | null;
  householdId: number;
  householdName: string;
  scenarioName: string;
  mainMemberName?: string; // Added main member's name
  onRegenerateSeed?: () => void;
  onAnnotationModeChange: (enabled: boolean) => void;
  onShowAnnotationsChange: (show: boolean) => void;
  isAnnotationMode: boolean;
}

type Metric = {
  'Age': number;
  'Savings Fund': number;
  'Gross Income': number;
  'Net Income': number;
  'Total Expenditure': number;
  'Additional Expenditure': number;
  'Net Wealth': number;
  'Realistic Net Wealth': number;
  'Net Cashflow': number;
  'Total Withdrawals': number;
  'Investments Fund': number;
  'Realistic Investments Fund': number;
  // Individual investment funds
  'Investment Fund 1'?: number;
  'Investment Fund 2'?: number;
  'Investment Fund 3'?: number;
  'Investment Fund 4'?: number;
  'Investment Fund 5'?: number;
  // Investment fund contributions
  'Annual Investment Contribution 1'?: number;
  'Annual Investment Contribution 2'?: number;
  'Annual Investment Contribution 3'?: number;
  'Annual Investment Contribution 4'?: number;
  'Annual Investment Contribution 5'?: number;
  // Investment fund returns
  'Annual Investment Return 1'?: number;
  'Annual Investment Return 2'?: number;
  'Annual Investment Return 3'?: number;
  'Annual Investment Return 4'?: number;
  'Annual Investment Return 5'?: number;
  'Total KiwiSaver': number;
  'Realistic Total KiwiSaver': number;
  'Main KiwiSaver': number;
  'Partner KiwiSaver': number;
  'Annual Investment Return': number;
  'Annual KiwiSaver Return': number;
  'Minimum Investment Fund': number;
  'Maximum Investment Fund': number;
  'Realistic Investment Fund': number;
  'Annual Investment Contribution': number;
  'Main Employee KiwiSaver': number;
  'Main Employer KiwiSaver': number;
  'Partner Employee KiwiSaver': number;
  'Partner Employer KiwiSaver': number;
  'KiwiSaver Contributions': number;
  'Partner KiwiSaver Contributions': number;
  'Property Value': number;
  'Property Value 2'?: number;
  'Property Value 3'?: number;
  'Property Value 4'?: number;
  'Property Value 5'?: number;
  'Debt Value': number;
  'Debt Value 2'?: number;
  'Debt Value 3'?: number;
  'Debt Value 4'?: number;
  'Debt Value 5'?: number;
  'Monthly Debt repayment': number;
  'Monthly Debt repayment 2'?: number;
  'Monthly Debt repayment 3'?: number;
  'Monthly Debt repayment 4'?: number;
  'Monthly Debt repayment 5'?: number;
  'Annual Debt Repayments': number;
  'Annual Debt Repayments 2'?: number;
  'Annual Debt Repayments 3'?: number;
  'Annual Debt Repayments 4'?: number;
  'Annual Debt Repayments 5'?: number;
  'Annual Interest Payments': number;
  'Annual Interest Payments 2'?: number;
  'Annual Interest Payments 3'?: number;
  'Annual Interest Payments 4'?: number;
  'Annual Interest Payments 5'?: number;
  'Annual Principal Repayments': number;
  'Annual Principal Repayments 2'?: number;
  'Annual Principal Repayments 3'?: number;
  'Annual Principal Repayments 4'?: number;
  'Annual Principal Repayments 5'?: number;
  'Income Tax': number;
  'MTR Investment Tax': number;
  'PIE Investment tax': number;
  'KiwiSaver Tax': number;
  'Main Income Tax': number;
  'Partner Income Tax': number;
  'Property Sale Proceeds': number;
  'Property Sale Proceeds 2'?: number;
  'Property Sale Proceeds 3'?: number;
  'Property Sale Proceeds 4'?: number;
  'Property Sale Proceeds 5'?: number;
  'Transaction Costs': number;
  'Transaction Costs 2'?: number;
  'Transaction Costs 3'?: number;
  'Transaction Costs 4'?: number;
  'Transaction Costs 5'?: number;
  'Debt Paid': number;
  'Debt Paid 2'?: number;
  'Debt Paid 3'?: number;
  'Debt Paid 4'?: number;
  'Debt Paid 5'?: number;
  'Rental Income'?: number;
  'Board Income'?: number;
  'Superannuation'?: number;
  'Partner Income'?: number;
  'Main Income'?: number;
};

interface SelectedSections {
  table: {
    selected: boolean;
    fields: {
      age: boolean;
      grossIncome: boolean;
      netIncome: boolean;
      totalExpenditure: boolean;
      additionalExpenditure: boolean;
      netWealth: boolean;
      netCashflow: boolean;
      totalWithdrawals: boolean;
      investmentsFund: boolean;
      // Individual investment funds
      investmentFund1: boolean;
      investmentFund2: boolean;
      investmentFund3: boolean;
      investmentFund4: boolean;
      investmentFund5: boolean;
      // Investment fund contributions
      annualInvestmentContribution1: boolean;
      annualInvestmentContribution2: boolean;
      annualInvestmentContribution3: boolean;
      annualInvestmentContribution4: boolean;
      annualInvestmentContribution5: boolean;
      // Investment fund returns
      annualInvestmentReturn1: boolean;
      annualInvestmentReturn2: boolean;
      annualInvestmentReturn3: boolean;
      annualInvestmentReturn4: boolean;
      annualInvestmentReturn5: boolean;
      totalKiwiSaver: boolean;
      mainKiwiSaver: boolean;
      partnerKiwiSaver: boolean;
      annualInvestmentReturn: boolean;
      annualKiwiSaverReturn: boolean;
      minimumInvestmentReturn: boolean;
      maximumInvestmentReturn: boolean;
      annualInvestmentContribution: boolean;
      kiwiSaverContributions: boolean;
      partnerKiwiSaverContributions: boolean;
      propertyValue: boolean;
      debtValue: boolean;
      monthlyDebtRepayment: boolean;
      annualDebtRepayments: boolean;
      annualInterestPayments: boolean;
      annualPrincipalRepayments: boolean;
      incomeTax: boolean;
      mtrInvestmentTax: boolean;
      pieInvestmentTax: boolean;
      kiwiSaverTax: boolean;
      mainIncomeTax: boolean;
      partnerIncomeTax: boolean;
    };
  };
}

const columnDefinitions: Array<{
  key: keyof SelectedSections['table']['fields'];
  label: string;
  metricKey: keyof Metric;
}> = [
  { key: 'age', label: 'Age', metricKey: 'Age' },
  { key: 'grossIncome', label: 'Gross Income', metricKey: 'Gross Income' },
  { key: 'netIncome', label: 'Net Income', metricKey: 'Net Income' },
  { key: 'totalExpenditure', label: 'Total Expenditure', metricKey: 'Total Expenditure' },
  { key: 'additionalExpenditure', label: 'Additional Expenditure', metricKey: 'Additional Expenditure' },
  { key: 'netWealth', label: 'Net Wealth', metricKey: 'Net Wealth' },
  { key: 'netCashflow', label: 'Net Cashflow', metricKey: 'Net Cashflow' },
  { key: 'totalWithdrawals', label: 'Total Withdrawals', metricKey: 'Total Withdrawals' },
  { key: 'investmentsFund', label: 'Investments Fund', metricKey: 'Investments Fund' },
  // Individual investment funds
  { key: 'investmentFund1', label: 'Investment Fund 1', metricKey: 'Investment Fund 1' },
  { key: 'investmentFund2', label: 'Investment Fund 2', metricKey: 'Investment Fund 2' },
  { key: 'investmentFund3', label: 'Investment Fund 3', metricKey: 'Investment Fund 3' },
  { key: 'investmentFund4', label: 'Investment Fund 4', metricKey: 'Investment Fund 4' },
  { key: 'investmentFund5', label: 'Investment Fund 5', metricKey: 'Investment Fund 5' },
  { key: 'totalKiwiSaver', label: 'Total KiwiSaver', metricKey: 'Total KiwiSaver' },
  { key: 'mainKiwiSaver', label: 'Main KiwiSaver', metricKey: 'Main KiwiSaver' },
  { key: 'partnerKiwiSaver', label: 'Partner KiwiSaver', metricKey: 'Partner KiwiSaver' },
  { key: 'annualInvestmentReturn', label: 'Annual Investment Return', metricKey: 'Annual Investment Return' },
  { key: 'annualKiwiSaverReturn', label: 'Annual KiwiSaver Return', metricKey: 'Annual KiwiSaver Return' },
  { key: 'minimumInvestmentReturn', label: 'Minimum Investment Return', metricKey: 'Minimum Investment Fund' },
  { key: 'maximumInvestmentReturn', label: 'Maximum Investment Return', metricKey: 'Maximum Investment Fund' },
  { key: 'annualInvestmentContribution', label: 'Annual Investment Contribution', metricKey: 'Annual Investment Contribution' },
  { key: 'kiwiSaverContributions', label: 'KiwiSaver Contributions', metricKey: 'KiwiSaver Contributions' },
  { key: 'partnerKiwiSaverContributions', label: 'Partner KiwiSaver Contributions', metricKey: 'Partner KiwiSaver Contributions' },
  { key: 'propertyValue', label: 'Property Value', metricKey: 'Property Value' },
  { key: 'debtValue', label: 'Debt Value', metricKey: 'Debt Value' },
  { key: 'monthlyDebtRepayment', label: 'Monthly Debt Repayment', metricKey: 'Monthly Debt repayment' },
  { key: 'annualDebtRepayments', label: 'Annual Debt Repayments', metricKey: 'Annual Debt Repayments' },
  { key: 'annualInterestPayments', label: 'Annual Interest Payments', metricKey: 'Annual Interest Payments' },
  { key: 'annualPrincipalRepayments', label: 'Annual Principal Repayments', metricKey: 'Annual Principal Repayments' },
  { key: 'incomeTax', label: 'Income Tax', metricKey: 'Income Tax' },
  { key: 'mtrInvestmentTax', label: 'MTR Investment Tax', metricKey: 'MTR Investment Tax' },
  { key: 'pieInvestmentTax', label: 'PIE Investment Tax', metricKey: 'PIE Investment tax' },
  { key: 'kiwiSaverTax', label: 'KiwiSaver Tax', metricKey: 'KiwiSaver Tax' },
  { key: 'mainIncomeTax', label: 'Main Income Tax', metricKey: 'Main Income Tax' },
  { key: 'partnerIncomeTax', label: 'Partner Income Tax', metricKey: 'Partner Income Tax' },
];

export function MiscTab({
  onCheckboxChange,
  onInputChange,
  onDataChange,
  inputData,
  currentScenarioId,
  householdId,
  householdName,
  scenarioName,
  mainMemberName,
  onRegenerateSeed,
  onAnnotationModeChange,
  onShowAnnotationsChange,
  isAnnotationMode
}: MiscTabProps) {
  const router = useRouter();
  const pathname = usePathname();
  const isPresentationPage = pathname === '/protected/presentation';

  const initialData = {
    show_savings: false,
    show_investment: false,
    show_individual_investments: false,
    show_kiwisaver: false,
    show_individual_kiwisavers: false,
    show_monte_carlo: false,
    show_property_value: false,
    show_debt_value: false,
    show_annotations: false,
    show_realistic_netwealth: false,
    inflation_rate: inputData?.inflation_rate || 2.0,
    num_simulations: inputData?.num_simulations || 1000,
    confidence_interval: inputData?.confidence_interval || 95,
    show_cashflow: false,
  };

  const [data, setData] = useState(() => {
    // First prioritize inputData from props (from database)
    if (inputData) {
      return {
        ...initialData,
        show_savings: inputData.show_savings || false,
        show_investment: inputData.show_investment || false,
        show_individual_investments: inputData.show_individual_investments || false,
        show_kiwisaver: inputData.show_kiwisaver || false,
        show_individual_kiwisavers: inputData.show_individual_kiwisavers || false,
        show_monte_carlo: inputData.show_monte_carlo || false,
        show_property_value: inputData.show_property_value || false,
        show_debt_value: inputData.show_debt_value || false,
        show_annotations: inputData.show_annotations || false,
        show_realistic_netwealth: inputData.show_realistic_netwealth || false,
        show_cashflow: inputData.show_cashflow || false,
        inflation_rate: inputData.inflation_rate || initialData.inflation_rate,
        num_simulations: inputData.num_simulations || initialData.num_simulations,
        confidence_interval: inputData.confidence_interval || initialData.confidence_interval,
      };
    }
    // Then check localStorage
    const savedData = localStorage.getItem('miscTabData');
    return savedData ? JSON.parse(savedData) : initialData;
  });

  // Generate a unique storage key based on scenario ID
  const storageKey = currentScenarioId ? `miscTabData_${currentScenarioId}` : 'miscTabData_temp';

  // Load data from localStorage on mount or when scenario changes
  useEffect(() => {
    // Only run this effect when we have a valid scenario ID
    if (!currentScenarioId) return;

    // Check if we have saved data for this scenario
    const savedData = localStorage.getItem(storageKey);

    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        // Update local state without triggering parent updates
        setData(parsedData);
      } catch (e) {
        console.error('Error parsing saved data:', e);
      }
    }
  }, [currentScenarioId]); // Only depend on scenario ID changes

  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);

  const { calculateFinancialLife } = useFinancialCalculations(inputData);
  const { allMetrics, chanceOfSuccess } = calculateFinancialLife(inputData);

  const growthChartRef = useRef(null);
  const cashflowChartRef = useRef(null);
  const propertyChartRef = useRef(null);
  const downloadPdfModalRef = createRef<any>();
  const [pdfPreviewElement, setPdfPreviewElement] = useState<HTMLDivElement | null>(null);

  const [aiSummary, setAiSummary] = useState<string>('');

  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  const [annotations, setAnnotations] = useState<Array<{
    x: string;
    y: string;
    text: string;
    color: string;
    series: string;
  }>>([]);

  // Add this useEffect to sync annotations from NetWealth component
  useEffect(() => {
    const handleAnnotationsUpdate = (event: CustomEvent) => {
      setAnnotations(event.detail);
    };
    window.addEventListener('annotationsUpdated', handleAnnotationsUpdate as EventListener);
    return () => {
      window.removeEventListener('annotationsUpdated', handleAnnotationsUpdate as EventListener);
    };
  }, []);

  const getSectionKey = (title: string): string => {
    switch (title) {
      case "Personal": return "personal";
      case "Income": return "income";
      case "Savings": return "savings";
      case "Expenditure": return "expenditure";
      case "Investment": return "investment";
      case "KiwiSaver": return "kiwiSaver";
      case "Property": return "property";
      case "Economic": return "economic";
      case "Monte Carlo": return "monteCarlo";
      default: return title.toLowerCase();
    }
  };



  const handleCheckboxChange = (key: string) => (checked: boolean) => {
    const newData = {
      ...data,
      [key]: checked,
    };
    setData(newData);

    // Save to localStorage with the scenario-specific key
    localStorage.setItem(storageKey, JSON.stringify(newData));

    // Notify parent component of the change
    onCheckboxChange(newData);

    // Handle special cases
    if (key === 'show_monte_carlo') {
      onDataChange('show_monte_carlo', checked);
    } else if (key === 'show_realistic_netwealth') {
      onDataChange('show_realistic_netwealth', checked);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Apply constraints for specific fields
    let numericValue = parseFloat(value);
    if (name === 'num_simulations') {
      numericValue = Math.min(numericValue, 1000); // Cap at 1,000 simulations
    } else if (name === 'confidence_interval') {
      numericValue = Math.min(numericValue, 100); // Cap at 100%
    }

    // Update local state
    setData((prevData: typeof initialData) => {
      const newData = {
        ...prevData,
        [name]: numericValue
      };

      // Save to localStorage
      localStorage.setItem(storageKey, JSON.stringify(newData));

      return newData;
    });

    // Directly notify parent of changes for specific fields
    if (name === 'inflation_rate' || name === 'num_simulations' || name === 'confidence_interval') {
      onInputChange(name, numericValue);
    }
  };

  const handleDownloadPDF = async (selectedSections: any, successfulScenarios: number | null, failedScenarios: number | null) => {

    // First try to get the element from state
    let previewElement = pdfPreviewElement;

    // If not available in state, try to get it from the ref
    if (!previewElement) {
      previewElement = downloadPdfModalRef.current?.getPdfPreviewElement();
    }

    if (!previewElement) {
      console.error('PDF preview element not found');
      return;
    }

    // Wait longer for the preview to fully render
    await new Promise(resolve => setTimeout(resolve, 1500));

    // If both approaches fail, try a direct DOM query
    if (!previewElement) {
      const directElement = document.querySelector('.pdf-preview-container');
      if (directElement) {
        // Cast to HTMLDivElement as expected later
        previewElement = directElement as HTMLDivElement;
      }
    }


    try {
      // Double-check that the PDF preview element is available
      if (!previewElement) {
        console.error('PDF preview element still not available after waiting');
        return;
      }

      // Capture each A4 page in the preview
      const pages = previewElement.querySelectorAll('.a4-page');
      if (!pages || pages.length === 0) {
        console.error('No A4 pages found in the preview');
        return;
      }


      // Create a new PDF document with A4 size and optimized settings
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        hotfixes: ["px_scaling"], // Enable px_scaling hotfix for better rendering
        compress: true, // Enable compression for smaller file size
        precision: 2, // Lower precision for better performance
        putOnlyUsedFonts: true // Only include used fonts
      });

      // Process each page
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i] as HTMLElement;

        // If not the first page, add a new page to the PDF
        if (i > 0) {
          doc.addPage();
        }

        // Capture the page as an image with improved settings for PDF output
        const canvas = await html2canvas(page, {
          scale: 1.5, // Balanced scale for better text clarity without overflow
          useCORS: true,
          logging: false,
          allowTaint: true,
          backgroundColor: '#ffffff',
          imageTimeout: 0, // No timeout for images
          // Set window size to match the element's dimensions
          windowWidth: page.offsetWidth,
          windowHeight: page.offsetHeight,
          // Standard rendering options
          x: 0,
          y: 0,
          width: page.offsetWidth,
          height: page.offsetHeight,
          // Ensure content is fully rendered
          onclone: (clonedDoc) => {
            // Force any lazy-loaded content to render in the clone
            const clonedPage = clonedDoc.querySelector(`.a4-page:nth-child(${i + 1})`) as HTMLElement;
            if (clonedPage) {
              // Use standard A4 dimensions (96 DPI)
              clonedPage.style.width = `794px`; // A4 width in pixels
              clonedPage.style.height = `1123px`; // A4 height in pixels
              clonedPage.style.padding = `60px 60px 80px`; // Reduced padding for better content fit
              clonedPage.style.boxSizing = 'border-box'; // Ensure padding is included in dimensions

              // Ensure all sections and subsections are visible and properly styled
              const sections = clonedPage.querySelectorAll('[data-pdf-section], [data-pdf-subsection]');
              sections.forEach((section) => {
                (section as HTMLElement).style.visibility = 'visible';
                (section as HTMLElement).style.display = 'block';
              });

              // Make sure all images are loaded
              const images = clonedPage.querySelectorAll('img');
              images.forEach((img) => {
                if (!img.complete) {
                  img.src = img.src; // Force reload
                }
              });

              // Ensure proper spacing between subsections with indentation
              const subsections = clonedPage.querySelectorAll('.pdf-subsection');
              subsections.forEach((subsection) => {
                (subsection as HTMLElement).style.marginBottom = '2px'; // Reduced spacing between subsections
                (subsection as HTMLElement).style.paddingBottom = '2px'; // Reduced padding
                (subsection as HTMLElement).style.paddingLeft = '10px'; // Add indentation
              });

              // Style section titles - NO separators
              const sectionTitles = clonedPage.querySelectorAll('.pdf-subsection h2');
              sectionTitles.forEach((title) => {
                (title as HTMLElement).style.fontSize = '15px';
                (title as HTMLElement).style.fontWeight = '600';
                (title as HTMLElement).style.marginTop = '6px';
                (title as HTMLElement).style.marginBottom = '4px';
                (title as HTMLElement).style.color = '#333';
                (title as HTMLElement).style.marginLeft = '-10px'; // Counteract the subsection indentation
                (title as HTMLElement).style.paddingBottom = '4px';
                (title as HTMLElement).style.borderBottom = 'none'; // Remove separator under section titles
              });

              // Style subsection titles with reduced margins - NO separators
              const subsectionTitles = clonedPage.querySelectorAll('.pdf-subsection h3');
              subsectionTitles.forEach((title) => {
                (title as HTMLElement).style.fontSize = '13px';
                (title as HTMLElement).style.fontWeight = '500';
                (title as HTMLElement).style.marginTop = '3px';
                (title as HTMLElement).style.marginBottom = '1px';
                (title as HTMLElement).style.color = '#444';
                (title as HTMLElement).style.paddingBottom = '2px';
                // Removed the border-bottom
              });

              // Adjust content text size and reduce vertical spacing
              const contentText = clonedPage.querySelectorAll('p, span, div:not(.pdf-subsection):not(.a4-page):not(.pdf-page-number)');
              contentText.forEach((element) => {
                (element as HTMLElement).style.fontSize = '12px';
                (element as HTMLElement).style.lineHeight = '1.2'; // Reduced from 1.3
                (element as HTMLElement).style.marginTop = '2px';
                (element as HTMLElement).style.marginBottom = '2px';
              });

              // Remove ALL separators except main section title ones
              const separators = clonedPage.querySelectorAll('.border-b');
              separators.forEach((separator) => {
                const parent = separator.parentElement;

                // Only keep separators for the main header
                const isInHeader = parent && parent.classList.contains('pdf-section');

                // Remove all other separators
                if (!isInHeader) {
                  separator.remove();
                }
              });

              // Also remove any border-bottom styles from section descriptions
              const sectionDescriptions = clonedPage.querySelectorAll('p.text-muted-foreground');
              sectionDescriptions.forEach((desc) => {
                (desc as HTMLElement).style.borderBottom = 'none';
              });

              // Adjust table styles for better fit with reduced margins
              const tables = clonedPage.querySelectorAll('table');
              tables.forEach((table) => {
                (table as HTMLElement).style.fontSize = '11px';
                (table as HTMLElement).style.width = '100%';
                (table as HTMLElement).style.tableLayout = 'fixed';
                (table as HTMLElement).style.marginBottom = '4px'; // Reduced from 8px
                (table as HTMLElement).style.borderSpacing = '0';
                (table as HTMLElement).style.borderCollapse = 'collapse';

                // Make table cells more compact
                const cells = table.querySelectorAll('td, th');
                cells.forEach((cell) => {
                  (cell as HTMLElement).style.padding = '2px 4px';
                  (cell as HTMLElement).style.verticalAlign = 'top';
                });
              });

              // Position page numbers
              const pageNumber = clonedPage.querySelector('.pdf-page-number');
              if (pageNumber) {
                (pageNumber as HTMLElement).style.position = 'absolute';
                (pageNumber as HTMLElement).style.bottom = '30px';
                (pageNumber as HTMLElement).style.right = '60px';
                (pageNumber as HTMLElement).style.fontSize = '9px';
                (pageNumber as HTMLElement).style.color = '#888';
                (pageNumber as HTMLElement).style.fontFamily = 'Arial, sans-serif';
              }
            }
          }
        });

        // Convert the canvas to an image (using PNG for better quality)
        // Use maximum quality (1.0) for the PNG image
        const imgData = canvas.toDataURL('image/png', 1.0);

        // Calculate the dimensions to fit the A4 page with proper margins
        const imgWidth = 210; // A4 width in mm
        const imgHeight = 297; // A4 height in mm

        // Calculate the aspect ratio of the canvas
        const canvasAspectRatio = canvas.width / canvas.height;

        // Calculate the dimensions to maintain aspect ratio while fitting within A4
        let finalWidth = imgWidth;
        let finalHeight = imgWidth / canvasAspectRatio;

        // If the height exceeds A4 height, scale down proportionally
        if (finalHeight > imgHeight) {
          finalHeight = imgHeight;
          finalWidth = imgHeight * canvasAspectRatio;
        }

        // Calculate margins to center the image
        const marginLeft = (imgWidth - finalWidth) / 2;
        const marginTop = (imgHeight - finalHeight) / 2;

        // Add the image to the PDF with proper positioning
        doc.addImage(imgData, 'PNG', marginLeft, marginTop, finalWidth, finalHeight);
      }

      // Save the PDF
      doc.save(`${householdName}_${scenarioName}_Report.pdf`);

      return;

    } catch (error) {
      console.error('Error generating PDF from preview:', error);

      // Fallback to a simpler approach if the advanced method fails
      try {
        const simpleDoc = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });

        // Add a simple header
        simpleDoc.setFontSize(16);
        simpleDoc.text(`${householdName} - ${scenarioName}`, 20, 20);
        simpleDoc.setFontSize(12);
        simpleDoc.text("Financial Planning Report", 20, 30);

        // Add basic content from the selected sections
        let yPos = 40;

        // Add selected metrics directly to the PDF
        // This is a simplified version that ensures at least some content is saved
        if (selectedSections.personal && selectedSections.personal.selected) {
          simpleDoc.setFontSize(14);
          simpleDoc.text("Personal Information", 20, yPos);
          yPos += 10;

          if (inputData.name) {
            simpleDoc.setFontSize(10);
            simpleDoc.text(`Name: ${inputData.name}`, 20, yPos);
            yPos += 7;
          }

          if (inputData.starting_age) {
            simpleDoc.text(`Current Age: ${inputData.starting_age}`, 20, yPos);
            yPos += 7;
          }

          if (inputData.ending_age) {
            simpleDoc.text(`Life Expectancy: ${inputData.ending_age}`, 20, yPos);
            yPos += 7;
          }
        }

        simpleDoc.save(`${householdName}_${scenarioName}_Report_Simple.pdf`);

        return;
      } catch (fallbackError) {
        console.error("Even fallback PDF generation failed:", fallbackError);
        return;
      }
    }
  };

  // Removed unused addSelectedScenarioInputs function


  // addTableData function removed

  // Removed unused formatter and handleReset functions


  return (
    <div className="space-y-1 p-1">

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex flex-col space-y-2 pt-5">
          <div className="flex items-center space-x-2">
            <Switch
              id="show_savings"
              checked={data.show_savings}
              onCheckedChange={handleCheckboxChange('show_savings')}
              size={"sm"}
            />
            <LabelWithTooltip
              htmlFor="show_savings"
              label="Show Savings Fund"
              tooltipText="Display the savings fund balance over time in the results"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="show_investment"
              checked={data.show_investment}
              onCheckedChange={handleCheckboxChange('show_investment')}
              size={"sm"}
            />
            <LabelWithTooltip
              htmlFor="show_investment"
              label="Show Investment"
              tooltipText="Display investment portfolio value over time in the results"
            />
          </div>
          {data.show_investment && (
            <div className="flex items-center space-x-2 ml-6">
              <Switch
                id="show_individual_investments"
                checked={data.show_individual_investments}
                onCheckedChange={handleCheckboxChange('show_individual_investments')}
                size={"sm"}
              />
              <LabelWithTooltip
                htmlFor="show_individual_investments"
                label="Show Individual Investments"
                tooltipText="Display individual investment funds as separate lines"
              />
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Switch
              id="show_kiwisaver"
              checked={data.show_kiwisaver}
              onCheckedChange={handleCheckboxChange('show_kiwisaver')}
              size={"sm"}
            />
            <LabelWithTooltip
              htmlFor="show_kiwisaver"
              label="Show KiwiSaver"
              tooltipText="Display KiwiSaver balance over time in the results"
            />
          </div>
          {data.show_kiwisaver && (
            <div className="flex items-center space-x-2 ml-6">
              <Switch
                id="show_individual_kiwisavers"
                checked={data.show_individual_kiwisavers}
                onCheckedChange={handleCheckboxChange('show_individual_kiwisavers')}
                size={"sm"}
              />
              <LabelWithTooltip
                htmlFor="show_individual_kiwisavers"
                label="Show Individual KiwiSavers"
                tooltipText="Display individual KiwiSaver balances (main and partner) as separate lines"
              />
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Switch
              id="show_monte_carlo"
              checked={data.show_monte_carlo}
              onCheckedChange={handleCheckboxChange('show_monte_carlo')}
              size={"sm"}
            />
            <LabelWithTooltip
              htmlFor="show_monte_carlo"
              label="Show Monte Carlo"
              tooltipText="Display Monte Carlo simulation lines showing the range of possible outcomes"
            />
          </div>
          {/*
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show_realistic_netwealth"
              checked={data.show_realistic_netwealth}
              onCheckedChange={handleCheckboxChange('show_realistic_netwealth')}
            />
            <LabelWithTooltip
              htmlFor="show_realistic_netwealth"
              label="Show Realistic Market Returns"
              tooltipText="Display more realistic projections with varying market conditions over time for Net Wealth, Investments, and KiwiSaver"
            />
          </div>
          */}

          <div className="flex items-center space-x-2">
            <Switch
              id="show_annotations"
              checked={inputData?.show_annotations || false}
              onCheckedChange={(checked) => {
                onDataChange('show_annotations', checked === true);
                onShowAnnotationsChange(checked === true);
              }}
              size={"sm"}
            />
            <label
              htmlFor="show_annotations"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show Annotations
            </label>
          </div>

        </div>

        <div className="flex flex-col justify-end space-y-4">
          <div className="flex flex-col space-y-2">
            <LabelWithTooltip
              htmlFor="inflation_rate"
              label="Inflation Rate (%)"
              tooltipText="Expected annual inflation rate"
            />
            <Input
              id="inflation_rate"
              name="inflation_rate"
              type="number"
              value={data.inflation_rate}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col space-y-2">
              <LabelWithTooltip
                htmlFor="num_simulations"
                label="Number of Simulations"
                tooltipText="Number of scenarios to simulate (max 10,000). More simulations provide more accurate results but take longer"
              />
              <Input
                id="num_simulations"
                name="num_simulations"
                type="number"
                value={data.num_simulations}
                onChange={handleInputChange}
                min={0}
                max={1000}
                className="w-full"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <LabelWithTooltip
                htmlFor="confidence_interval"
                label="Confidence Interval (%)"
                tooltipText="Percentage of simulations that should fall within the displayed range (e.g., 95% shows the range excluding the top and bottom 2.5%)"
              />
              <Input
                type="number"
                min={0}
                max={100}
                value={data.confidence_interval}
                onChange={handleInputChange}
                name="confidence_interval"
                className="w-full"
                step={1}
              />
            </div>
          </div>

          <div className="flex flex-col space-y-2 pt-4">
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => {
                setIsSaveModalOpen(true);
              }}>
                Save Scenario
              </Button>
              <Button variant="outline" onClick={() => setIsDownloadModalOpen(true)}>
                Download PDF
              </Button>
              {/** <Button variant="outline" onClick={() => setIsOptimizationSettingsOpen(true)}>
                Optimise Scenario
              </Button> */}
              <Button
                variant={isAnnotationMode ? "secondary" : "outline"}
                onClick={() => {
                  onAnnotationModeChange(!isAnnotationMode);
                }}
                className="w-full"
              >
                {isAnnotationMode ? "Stop Annotating" : "Add Annotation"}
              </Button>

            {/*
            <Button
              onClick={handleGenerateLink}
              disabled={isGeneratingLink || !currentScenarioId}
              variant="outline"
            >
              {isGeneratingLink ? 'Generating...' : 'Generate Presentation Link'}
            </Button>
            */}
            </div>
          </div>
        </div>
      </div>

      {isSaveModalOpen && (
        isPresentationPage ? (
          <PresentationSaveScenarioModal
            isOpen={isSaveModalOpen}
            onClose={() => setIsSaveModalOpen(false)}
            inputData={inputData}
            currentScenarioId={currentScenarioId}
            householdId={householdId}
            annotations={annotations}
          />
        ) : (
          <SaveScenarioModal
            isOpen={isSaveModalOpen}
            onClose={() => setIsSaveModalOpen(false)}
            inputData={inputData}
            currentScenarioId={currentScenarioId}
            householdId={householdId}
            annotations={annotations}
          />
        )
      )}

      <DownloadPDFModal
        ref={downloadPdfModalRef}
        isOpen={isDownloadModalOpen}
        onClose={() => setIsDownloadModalOpen(false)}
        onDownload={handleDownloadPDF}
        householdName={householdName}
        scenarioName={scenarioName}
        mainMemberName={mainMemberName}
        allMetrics={allMetrics}
        inputData={inputData}
        showAdditionalData={{
          show_savings: data.show_savings,
          show_investment: data.show_investment,
          show_individual_investments: data.show_individual_investments,
          show_kiwisaver: data.show_kiwisaver,
          show_individual_kiwisavers: data.show_individual_kiwisavers,
          show_cashflow: data.show_cashflow
        }}
        minNetWealthAtAge={inputData.worst_scenario || []}
        maxNetWealthAtAge={inputData.best_scenario || []}
        averageNetWealthAtAge={inputData.average_scenario || []}
        chanceOfSuccess={chanceOfSuccess}
        successfulScenarios={null}
        failedScenarios={null}
        chartConfig={chartConfig}
        getPdfPreviewRef={(ref) => {
          setPdfPreviewElement(ref);
        }}
      />
    </div>
  );
}
