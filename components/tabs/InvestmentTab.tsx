import { useState, useEffect } from 'react';
import { InputData } from '@/app/protected/planner/types';
import { Button } from "@/components/ui/button";
import { Plus, Trash2, Pencil, Check, X } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { InvestmentModal } from '../modals/InvestmentModal';
import { InvestmentFundCard } from '@/components/InvestmentFundCard';
import { useToast } from "@/hooks/use-toast";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  RadioGroup,
  RadioGroupItem,
} from "@/components/ui/radio-group";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface InvestmentTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
}

interface OneOffInvestment {
  amount: number;
  age: number;
  details?: string;
  specificFund?: number;
  allocations?: {
    fund1?: number;
    fund2?: number;
    fund3?: number;
    fund4?: number;
    fund5?: number;
  };
}

interface EditingState {
  index: number;
  amount: string;
  age: string;
  details: string;
  allocationType: 'equal' | 'specific' | 'custom'; // How to allocate the investment
  specificFund?: number; // If allocationType is 'specific'
  allocations: {
    fund1: string;
    fund2: string;
    fund3: string;
    fund4: string;
    fund5: string;
  };
}

export function InvestmentTab({ inputData, setInputData, readOnly }: InvestmentTabProps) {
  const { toast } = useToast();
  const [oneOffAmount, setOneOffAmount] = useState<string>('');
  const [oneOffAge, setOneOffAge] = useState<string>('');
  const [oneOffDetails, setOneOffDetails] = useState<string>('');
  const [oneOffAllocationType, setOneOffAllocationType] = useState<'equal' | 'specific' | 'custom'>('equal');
  const [oneOffSpecificFund, setOneOffSpecificFund] = useState<number>(1);
  const [oneOffAllocations, setOneOffAllocations] = useState<{
    fund1: string;
    fund2: string;
    fund3: string;
    fund4: string;
    fund5: string;
  }>({
    fund1: '20',
    fund2: '20',
    fund3: '20',
    fund4: '20',
    fund5: '20'
  });
  const [error, setError] = useState<string | null>(null);
  const [isAccordionOpen, setIsAccordionOpen] = useState<boolean>(false);
  const [editingState, setEditingState] = useState<EditingState | null>(null);

  // Track available fund slots (1-5) that can be activated
  const [availableFundSlots, setAvailableFundSlots] = useState<number[]>([]);

  // Track the number of active investment funds (1-5)
  const [activeInvestmentFunds, setActiveInvestmentFunds] = useState<number>(() => {
    // Initialize based on existing data
    if (inputData.initial_investment5) return 5;
    if (inputData.initial_investment4) return 4;
    if (inputData.initial_investment3) return 3;
    if (inputData.initial_investment2) return 2;
    if (inputData.initial_investment1) return 1;

    return 1; // Default to 1 fund
  });

  // Update activeInvestmentFunds count whenever inputData changes
  useEffect(() => {
    // Count active investment funds
    const activeFunds = Array.from({ length: 5 }, (_, i) => i + 1)
      .filter(i => (inputData as any)[`initial_investment${i}`] !== undefined &&
                   Number((inputData as any)[`initial_investment${i}`]) > 0);

    // Update the active funds count
    if (activeFunds.length !== activeInvestmentFunds) {
      setActiveInvestmentFunds(activeFunds.length || 1); // Default to 1 if no funds
    }


  }, [inputData, activeInvestmentFunds]);

  // Update available fund slots and withdrawal priorities
  useEffect(() => {
    // Get active investment funds (those in the priority list)
    const activePriorityFunds = inputData.withdrawal_priorities || [];

    // Find all funds that have data but are not in the priority list
    const inactiveFunds = Array.from({ length: 5 }, (_, i) => i + 1)
      .filter(i => {
        const hasData = (inputData as any)[`initial_investment${i}`] !== undefined;
        const isActive = activePriorityFunds.includes(i);
        return hasData && !isActive;
      });

    // Find empty slots (1-5) that can be used for new funds
    const usedSlots = new Set([...activePriorityFunds, ...inactiveFunds]);
    const emptySlots = Array.from({ length: 5 }, (_, i) => i + 1)
      .filter(i => !usedSlots.has(i));

    // Update available slots (inactive funds + empty slots)
    const availableSlots = [...inactiveFunds, ...emptySlots];
    setAvailableFundSlots(availableSlots);

    // If no active funds in priority list, default to fund 1
    if (activePriorityFunds.length === 0) {
      setInputData((prevData) => ({
        ...prevData,
        withdrawal_priorities: [1]
      }));
    }

  }, [inputData, setInputData]);

  // Function moved to InvestmentFundCard component

  // Add a new investment fund
  const handleAddInvestmentFund = () => {
    // Get current active funds from the priority list
    const activeFunds = inputData.withdrawal_priorities || [];

    // Check if we've reached the maximum number of funds
    if (activeFunds.length >= 5) {
      toast({
        title: "Maximum funds reached",
        description: "You can have a maximum of 5 investment funds active at once.",
        variant: "destructive"
      });
      return;
    }

    // Determine which fund number to add next
    // First, try to use an inactive fund that already has data
    // If none available, use the next empty slot
    const nextFundNumber = availableFundSlots[0] || 1;

    // Initialize the new fund with default values
    setInputData((prevData) => {
      // If this is the first fund and there's a legacy initial_investment value, use that
      const initialValue = nextFundNumber === 1 && prevData.initial_investment ?
        prevData.initial_investment :
        ((prevData as any)[`initial_investment${nextFundNumber}`] || 0);

      // If we have a total investment value but no individual funds, distribute it
      const totalInvestment = prevData.initial_investment || 0;
      const hasIndividualFunds = prevData.initial_investment1 || prevData.initial_investment2 ||
                                prevData.initial_investment3 || prevData.initial_investment4 ||
                                prevData.initial_investment5;

      // Get current priorities
      const currentPriorities = [...(prevData.withdrawal_priorities || [])];

      // If we're adding the first fund and there's a legacy investment but no individual funds
      if (nextFundNumber === 1 && totalInvestment > 0 && !hasIndividualFunds) {
        // Distribute the total investment across all 5 funds
        return {
          ...prevData,
          initial_investment1: totalInvestment * 0.2,
          annual_investment_return1: prevData.annual_investment_return || 5.5,
          inv_std_dev1: prevData.inv_std_dev || 8.0,
          investment_description1: prevData.investment_description || 'Investment Fund 1',

          initial_investment2: totalInvestment * 0.2,
          annual_investment_return2: 5.5,
          inv_std_dev2: 8.0,
          investment_description2: 'Investment Fund 2',

          initial_investment3: totalInvestment * 0.2,
          annual_investment_return3: 5.5,
          inv_std_dev3: 8.0,
          investment_description3: 'Investment Fund 3',

          initial_investment4: totalInvestment * 0.2,
          annual_investment_return4: 5.5,
          inv_std_dev4: 8.0,
          investment_description4: 'Investment Fund 4',

          initial_investment5: totalInvestment * 0.2,
          annual_investment_return5: 5.5,
          inv_std_dev5: 8.0,
          investment_description5: 'Investment Fund 5',

          // Clear the legacy initial_investment field to avoid confusion
          initial_investment: 0,

          // Set withdrawal priorities for all 5 funds
          withdrawal_priorities: [1, 2, 3, 4, 5]
        };
      }

      // Add the new fund to the priorities if it's not already there
      let newPriorities = [...currentPriorities];
      if (!newPriorities.includes(nextFundNumber)) {
        newPriorities.push(nextFundNumber);
      }

      return {
        ...prevData,
        [`initial_investment${nextFundNumber}`]: initialValue || 0,
        [`annual_investment_return${nextFundNumber}`]: 5.5, // Default to balanced fund
        [`inv_std_dev${nextFundNumber}`]: 8.0, // Default to balanced fund
        [`investment_description${nextFundNumber}`]: `Investment Fund ${nextFundNumber}`,

        // If this is the first fund and we're using the legacy value, clear it
        ...(nextFundNumber === 1 && initialValue === prevData.initial_investment ? { initial_investment: 0 } : {}),

        // Update withdrawal priorities to include the new fund
        withdrawal_priorities: newPriorities
      };
    });

    // If we're distributing across all 5 funds, set activeInvestmentFunds to 5
    if (nextFundNumber === 1 && inputData.initial_investment > 0 &&
        !(inputData.initial_investment1 || inputData.initial_investment2 ||
          inputData.initial_investment3 || inputData.initial_investment4 ||
          inputData.initial_investment5)) {

      // Use setTimeout to ensure this runs after the state update
      setTimeout(() => {
        setActiveInvestmentFunds(5);
      }, 0);
    }
  };

  // Remove an investment fund from the priority list (deactivate it)
  const handleRemoveInvestmentFund = (fundNumber: number) => {
    // Get current active funds from the priority list
    const activeFunds = inputData.withdrawal_priorities || [];

    // Can't remove if it's the only fund or if the fund doesn't exist
    if (activeFunds.length <= 1 || fundNumber < 1 || fundNumber > 5) {
      if (activeFunds.length <= 1) {
        toast({
          title: "Cannot remove fund",
          description: "You must have at least one active investment fund.",
          variant: "destructive"
        });
      }
      return;
    }

    // Just remove the fund from the priority list without deleting its data
    setInputData((prevData) => {
      const currentPriorities = [...(prevData.withdrawal_priorities || [])];

      // Remove the fund from priorities
      const newPriorities = currentPriorities.filter(p => p !== fundNumber);

      return {
        ...prevData,
        withdrawal_priorities: newPriorities
      };
    });

    toast({
      title: "Fund deactivated",
      description: `Investment Fund ${fundNumber} has been deactivated. You can add it back later.`,
    });
  };

  const validateInvestmentInputs = (amount: number, age: number): string | null => {
    if (isNaN(amount) || amount <= 0) {
      return 'Please enter a valid amount greater than 0';
    }

    if (isNaN(age) || age < inputData.starting_age || age > inputData.ending_age) {
      return `Age must be between ${inputData.starting_age} and ${inputData.ending_age}`;
    }

    return null;
  };

  const handleAddOneOffInvestment = () => {
    // Validate inputs
    const amount = parseFloat(oneOffAmount);
    const age = parseInt(oneOffAge);

    const validationError = validateInvestmentInputs(amount, age);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Validate allocation percentages if using custom allocation
    if (oneOffAllocationType === 'custom') {
      const total = Object.values(oneOffAllocations).reduce((sum, val) => sum + parseFloat(val || '0'), 0);
      if (Math.abs(total - 100) > 0.01) {
        setError(`Allocation percentages must sum to 100%. Current total: ${total.toFixed(2)}%`);
        return;
      }
    }

    setError(null);

    // Prepare the investment object based on allocation type
    let investmentObj: any = {
      amount,
      age,
      details: oneOffDetails.trim() || undefined
    };

    // Add allocation information based on the selected type
    if (oneOffAllocationType === 'specific') {
      investmentObj.specificFund = oneOffSpecificFund;
    } else if (oneOffAllocationType === 'custom') {
      investmentObj.allocations = {
        fund1: parseFloat(oneOffAllocations.fund1),
        fund2: parseFloat(oneOffAllocations.fund2),
        fund3: parseFloat(oneOffAllocations.fund3),
        fund4: parseFloat(oneOffAllocations.fund4),
        fund5: parseFloat(oneOffAllocations.fund5)
      };
    }
    // For 'equal', we don't need to add any allocation info as it's the default

    // Add the one-off investment
    setInputData((prevData) => {
      const newOneOffInvestments = [...(prevData.one_off_investments || []), investmentObj];
      return {
        ...prevData,
        one_off_investments: newOneOffInvestments
      };
    });

    // Clear the input fields
    setOneOffAmount('');
    setOneOffAge('');
    setOneOffDetails('');
    setOneOffAllocationType('equal');
    setOneOffSpecificFund(1);
    setOneOffAllocations({
      fund1: '20',
      fund2: '20',
      fund3: '20',
      fund4: '20',
      fund5: '20'
    });

    // Open the accordion if it's the first one-off investment
    if (!inputData.one_off_investments || inputData.one_off_investments.length === 0) {
      setIsAccordionOpen(true);
    }
  };

  const handleRemoveOneOffInvestment = (index: number) => {
    setInputData((prevData) => {
      const newOneOffInvestments = [...(prevData.one_off_investments || [])];
      newOneOffInvestments.splice(index, 1);
      return {
        ...prevData,
        one_off_investments: newOneOffInvestments
      };
    });
  };

  const handleEditOneOffInvestment = (investment: OneOffInvestment, index: number) => {
    // Determine allocation type based on investment properties
    let allocationType: 'equal' | 'specific' | 'custom' = 'equal';
    if (investment.specificFund !== undefined) {
      allocationType = 'specific';
    } else if (investment.allocations !== undefined) {
      allocationType = 'custom';
    }

    // Set up allocations with default values or from the investment
    const allocations = {
      fund1: investment.allocations?.fund1?.toString() || '20',
      fund2: investment.allocations?.fund2?.toString() || '20',
      fund3: investment.allocations?.fund3?.toString() || '20',
      fund4: investment.allocations?.fund4?.toString() || '20',
      fund5: investment.allocations?.fund5?.toString() || '20'
    };

    setEditingState({
      index,
      amount: investment.amount.toString(),
      age: investment.age.toString(),
      details: investment.details || '',
      allocationType,
      specificFund: investment.specificFund || 1,
      allocations
    });
  };

  const handleUpdateOneOffInvestment = () => {
    if (!editingState) return;

    const amount = parseFloat(editingState.amount);
    const age = parseInt(editingState.age);

    const validationError = validateInvestmentInputs(amount, age);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Validate allocation percentages if using custom allocation
    if (editingState.allocationType === 'custom') {
      const total = Object.values(editingState.allocations).reduce((sum, val) => sum + parseFloat(val || '0'), 0);
      if (Math.abs(total - 100) > 0.01) {
        setError(`Allocation percentages must sum to 100%. Current total: ${total.toFixed(2)}%`);
        return;
      }
    }

    setError(null);

    setInputData((prevData) => {
      const newOneOffInvestments = [...(prevData.one_off_investments || [])];

      // Prepare the investment object based on allocation type
      let investmentObj: any = {
        amount,
        age,
        details: editingState.details.trim() || undefined
      };

      // Add allocation information based on the selected type
      if (editingState.allocationType === 'specific') {
        investmentObj.specificFund = editingState.specificFund;
      } else if (editingState.allocationType === 'custom') {
        investmentObj.allocations = {
          fund1: parseFloat(editingState.allocations.fund1),
          fund2: parseFloat(editingState.allocations.fund2),
          fund3: parseFloat(editingState.allocations.fund3),
          fund4: parseFloat(editingState.allocations.fund4),
          fund5: parseFloat(editingState.allocations.fund5)
        };
      }

      newOneOffInvestments[editingState.index] = investmentObj;

      return {
        ...prevData,
        one_off_investments: newOneOffInvestments
      };
    });

    // Clear editing state
    setEditingState(null);
  };

  const handleCancelEdit = () => {
    setEditingState(null);
    setError(null);
  };

  // Fund type information is now handled in the InvestmentFundCard component

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end for priority reordering
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const currentPriorities = [...(inputData.withdrawal_priorities || [])];
      const oldIndex = currentPriorities.indexOf(Number(active.id));
      const newIndex = currentPriorities.indexOf(Number(over.id));

      if (oldIndex !== -1 && newIndex !== -1) {
        const newPriorities = arrayMove(currentPriorities, oldIndex, newIndex);

        // Update the input data with new priorities
        setInputData(prevData => ({
          ...prevData,
          withdrawal_priorities: newPriorities
        }));
      }
    }
  };

  // Handle moving a fund up in priority
  const handleMoveUp = (id: number) => {
    const currentPriorities = [...(inputData.withdrawal_priorities || [])];
    const index = currentPriorities.indexOf(id);

    if (index > 0) {
      const newPriorities = arrayMove(currentPriorities, index, index - 1);

      setInputData(prevData => ({
        ...prevData,
        withdrawal_priorities: newPriorities
      }));
    }
  };

  // Handle moving a fund down in priority
  const handleMoveDown = (id: number) => {
    const currentPriorities = [...(inputData.withdrawal_priorities || [])];
    const index = currentPriorities.indexOf(id);

    if (index < currentPriorities.length - 1) {
      const newPriorities = arrayMove(currentPriorities, index, index + 1);

      setInputData(prevData => ({
        ...prevData,
        withdrawal_priorities: newPriorities
      }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Investment Funds Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-medium">Investment Funds</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Drag to reorder withdrawal priorities. Funds will be withdrawn in order from top to bottom.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {(inputData.withdrawal_priorities || []).length} of 5 funds active
            </div>
            {(inputData.withdrawal_priorities || []).length < 5 && (
              <Button
                onClick={handleAddInvestmentFund}
                disabled={readOnly || (inputData.withdrawal_priorities || []).length >= 5}
                variant="outline"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" /> Add Fund
              </Button>
            )}
          </div>
        </div>

        {/* Render all active investment funds with drag and drop */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={inputData.withdrawal_priorities || []}
            strategy={verticalListSortingStrategy}
          >
            {/* Show a message if no funds are available */}
            {activeInvestmentFunds === 0 && (
              <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                No investment funds added yet. Click "Add Fund" to get started.
              </div>
            )}

            {/* Map through the withdrawal priorities to render funds in priority order */}
            {(inputData.withdrawal_priorities || []).map((fundId, index) => {
              const fundNumber = fundId;
              const description = (inputData as any)[`investment_description${fundNumber}`] || `Investment Fund ${fundNumber}`;
              const amount = (inputData as any)[`initial_investment${fundNumber}`] || 0;

              return (
                <InvestmentFundCard
                  key={fundNumber}
                  id={fundNumber}
                  fundNumber={fundNumber}
                  priorityIndex={index}
                  description={description}
                  amount={amount}
                  inputData={inputData}
                  setInputData={setInputData}
                  readOnly={readOnly}
                  onMoveUp={handleMoveUp}
                  onMoveDown={handleMoveDown}
                  onRemove={handleRemoveInvestmentFund}
                  isFirst={index === 0}
                  isLast={index === (inputData.withdrawal_priorities || []).length - 1}
                  totalFunds={activeInvestmentFunds}
                />
              );
            })}
          </SortableContext>
        </DndContext>
      </div>

      {/* One-off Investments Section */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">One-off Investments</h3>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Add lump sum investments at specific ages
          </div>
        </div>

        {/* Add One-off Investment Form */}
        <Card className="p-4 mb-4 border border-dashed">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <LabelWithTooltip
                htmlFor="oneOffAmount"
                label="Amount"
                tooltipText="The amount of money for this one-off investment"
              />
              <Input
                id="oneOffAmount"
                type="number"
                value={oneOffAmount}
                onChange={(e) => setOneOffAmount(e.target.value)}
                placeholder="e.g., 10000"
                readOnly={readOnly}
                className="bg-white dark:bg-gray-800"
              />
            </div>
            <div>
              <LabelWithTooltip
                htmlFor="oneOffAge"
                label="Age"
                tooltipText={`The age at which this investment will be added (between ${inputData.starting_age} and ${inputData.ending_age})`}
              />
              <Input
                id="oneOffAge"
                type="number"
                value={oneOffAge}
                onChange={(e) => setOneOffAge(e.target.value)}
                placeholder={`e.g., ${inputData.starting_age + 5}`}
                min={inputData.starting_age}
                max={inputData.ending_age}
                readOnly={readOnly}
                className="bg-white dark:bg-gray-800"
              />
            </div>
            <div>
              <LabelWithTooltip
                htmlFor="oneOffDetails"
                label="Details (Optional)"
                tooltipText="Optional details about this investment (e.g., Inheritance, Bonus)"
              />
              <Input
                id="oneOffDetails"
                type="text"
                value={oneOffDetails}
                onChange={(e) => setOneOffDetails(e.target.value)}
                placeholder="e.g., Inheritance"
                readOnly={readOnly}
                className="bg-white dark:bg-gray-800"
              />
            </div>
          </div>

          {/* Fund Allocation Options */}
          <div className="mb-4">
            <LabelWithTooltip
              htmlFor="oneOffAllocationType"
              label="Fund Allocation"
              tooltipText="How to allocate this investment across funds"
            />
            <RadioGroup
              value={oneOffAllocationType}
              onValueChange={(value) => setOneOffAllocationType(value as 'equal' | 'specific' | 'custom')}
              className="flex gap-6 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="equal" id="equal-allocation" />
                <Label htmlFor="equal-allocation">Equal Distribution</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="specific" id="specific-allocation" />
                <Label htmlFor="specific-allocation">Specific Fund</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom-allocation" />
                <Label htmlFor="custom-allocation">Custom Percentages</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Specific Fund Selection */}
          {oneOffAllocationType === 'specific' && (
            <div className="mb-4">
              <LabelWithTooltip
                htmlFor="oneOffSpecificFund"
                label="Select Fund"
                tooltipText="Choose which fund to allocate this investment to"
              />
              <Select
                value={oneOffSpecificFund.toString()}
                onValueChange={(value) => setOneOffSpecificFund(parseInt(value))}
              >
                <SelectTrigger className="bg-white dark:bg-gray-800">
                  <SelectValue placeholder="Select a fund" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: activeInvestmentFunds }, (_, i) => i + 1).map(fundNumber => (
                    <SelectItem key={fundNumber} value={fundNumber.toString()}>
                      Fund {fundNumber} - {(inputData as any)[`investment_description${fundNumber}`] || `Investment Fund ${fundNumber}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Custom Allocation Percentages */}
          {oneOffAllocationType === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
              {Array.from({ length: activeInvestmentFunds }, (_, i) => i + 1).map(fundNumber => (
                <div key={fundNumber}>
                  <LabelWithTooltip
                    htmlFor={`oneOffAllocation${fundNumber}`}
                    label={`Fund ${fundNumber} %`}
                    tooltipText={`Percentage to allocate to Fund ${fundNumber}`}
                  />
                  <Input
                    id={`oneOffAllocation${fundNumber}`}
                    type="number"
                    value={oneOffAllocations[`fund${fundNumber}` as keyof typeof oneOffAllocations]}
                    onChange={(e) => setOneOffAllocations({
                      ...oneOffAllocations,
                      [`fund${fundNumber}`]: e.target.value
                    })}
                    min="0"
                    max="100"
                    className="bg-white dark:bg-gray-800"
                  />
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-end">
            <Button
              onClick={handleAddOneOffInvestment}
              disabled={readOnly}
              className="w-1/4"
            >
              <Plus className="h-4 w-4 mr-2" /> Add Investment
            </Button>
          </div>

          {error && (
            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm rounded border border-red-200 dark:border-red-800">
              {error}
            </div>
          )}
        </Card>

        {/* List of one-off investments */}
        {inputData.one_off_investments && inputData.one_off_investments.length > 0 ? (
          <Accordion
            type="single"
            collapsible
            value={isAccordionOpen ? "one-off-investments" : undefined}
            onValueChange={(value) => setIsAccordionOpen(value === "one-off-investments")}
            className="w-full border rounded-md"
          >
            <AccordionItem value="one-off-investments" className="border-none">
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center gap-2">
                  <span className="font-medium">One-off investments</span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
                    {inputData.one_off_investments.length}
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4 pt-2">
                <div className="space-y-3">
                  {inputData.one_off_investments.map((investment, index) => (
                    <Card key={index} className={`overflow-hidden ${editingState && editingState.index === index ? 'border-blue-300 dark:border-blue-700' : ''}`}>
                      {editingState && editingState.index === index ? (
                        // Editing mode
                        <div className="p-4 space-y-3">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                              <LabelWithTooltip
                                htmlFor={`edit-amount-${index}`}
                                label="Amount"
                                tooltipText="The amount of money for this one-off investment"
                              />
                              <Input
                                id={`edit-amount-${index}`}
                                type="number"
                                value={editingState.amount}
                                onChange={(e) => setEditingState({
                                  ...editingState,
                                  amount: e.target.value
                                })}
                                className="bg-white dark:bg-gray-800"
                              />
                            </div>
                            <div>
                              <LabelWithTooltip
                                htmlFor={`edit-age-${index}`}
                                label="Age"
                                tooltipText={`The age at which this investment will be added (between ${inputData.starting_age} and ${inputData.ending_age})`}
                              />
                              <Input
                                id={`edit-age-${index}`}
                                type="number"
                                value={editingState.age}
                                onChange={(e) => setEditingState({
                                  ...editingState,
                                  age: e.target.value
                                })}
                                min={inputData.starting_age}
                                max={inputData.ending_age}
                                className="bg-white dark:bg-gray-800"
                              />
                            </div>
                            <div>
                              <LabelWithTooltip
                                htmlFor={`edit-details-${index}`}
                                label="Details (Optional)"
                                tooltipText="Optional details about this investment"
                              />
                              <Input
                                id={`edit-details-${index}`}
                                type="text"
                                value={editingState.details}
                                onChange={(e) => setEditingState({
                                  ...editingState,
                                  details: e.target.value
                                })}
                                className="bg-white dark:bg-gray-800"
                              />
                            </div>
                          </div>

                          {/* Fund Allocation Options */}
                          <div className="mt-4">
                            <LabelWithTooltip
                              htmlFor={`edit-allocation-type-${index}`}
                              label="Fund Allocation"
                              tooltipText="How to allocate this investment across funds"
                            />
                            <RadioGroup
                              value={editingState.allocationType}
                              onValueChange={(value) => setEditingState({
                                ...editingState,
                                allocationType: value as 'equal' | 'specific' | 'custom'
                              })}
                              className="flex gap-6 mt-2"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="equal" id={`edit-equal-allocation-${index}`} />
                                <Label htmlFor={`edit-equal-allocation-${index}`}>Equal Distribution</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="specific" id={`edit-specific-allocation-${index}`} />
                                <Label htmlFor={`edit-specific-allocation-${index}`}>Specific Fund</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="custom" id={`edit-custom-allocation-${index}`} />
                                <Label htmlFor={`edit-custom-allocation-${index}`}>Custom Percentages</Label>
                              </div>
                            </RadioGroup>
                          </div>

                          {/* Specific Fund Selection */}
                          {editingState.allocationType === 'specific' && (
                            <div className="mt-4">
                              <LabelWithTooltip
                                htmlFor={`edit-specific-fund-${index}`}
                                label="Select Fund"
                                tooltipText="Choose which fund to allocate this investment to"
                              />
                              <Select
                                value={editingState.specificFund?.toString() || '1'}
                                onValueChange={(value) => setEditingState({
                                  ...editingState,
                                  specificFund: parseInt(value)
                                })}
                              >
                                <SelectTrigger className="bg-white dark:bg-gray-800">
                                  <SelectValue placeholder="Select a fund" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Array.from({ length: activeInvestmentFunds }, (_, i) => i + 1).map(fundNumber => (
                                    <SelectItem key={fundNumber} value={fundNumber.toString()}>
                                      Fund {fundNumber} - {(inputData as any)[`investment_description${fundNumber}`] || `Investment Fund ${fundNumber}`}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}

                          {/* Custom Allocation Percentages */}
                          {editingState.allocationType === 'custom' && (
                            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
                              {Array.from({ length: activeInvestmentFunds }, (_, i) => i + 1).map(fundNumber => (
                                <div key={fundNumber}>
                                  <LabelWithTooltip
                                    htmlFor={`edit-allocation-${index}-${fundNumber}`}
                                    label={`Fund ${fundNumber} %`}
                                    tooltipText={`Percentage to allocate to Fund ${fundNumber}`}
                                  />
                                  <Input
                                    id={`edit-allocation-${index}-${fundNumber}`}
                                    type="number"
                                    value={editingState.allocations[`fund${fundNumber}` as keyof typeof editingState.allocations]}
                                    onChange={(e) => setEditingState({
                                      ...editingState,
                                      allocations: {
                                        ...editingState.allocations,
                                        [`fund${fundNumber}`]: e.target.value
                                      }
                                    })}
                                    min="0"
                                    max="100"
                                    className="bg-white dark:bg-gray-800"
                                  />
                                </div>
                              ))}
                            </div>
                          )}

                          <div className="flex justify-end space-x-2 mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCancelEdit}
                            >
                              <X className="h-4 w-4 mr-1" /> Cancel
                            </Button>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={handleUpdateOneOffInvestment}
                            >
                              <Check className="h-4 w-4 mr-1" /> Update
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // Display mode
                        <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800">
                          <div className="flex items-center gap-3">
                            <div className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-3 py-1 rounded-md font-medium">
                              ${investment.amount.toLocaleString()}
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium">Age {investment.age}</span>
                              {investment.details && (
                                <span className="text-xs text-gray-500 dark:text-gray-400">{investment.details}</span>
                              )}

                              {/* Show allocation information */}
                              <span className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                {(investment as any).specificFund ?
                                  `Allocated to Fund ${(investment as any).specificFund}` :
                                  (investment as any).allocations ?
                                    'Custom allocation' :
                                    'Equal distribution across funds'}
                              </span>
                            </div>
                          </div>
                          {!readOnly && (
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditOneOffInvestment(investment, index)}
                                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveOneOffInvestment(index)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ) : (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-md">
            No one-off investments added yet
          </div>
        )}
      </div>
    </div>
  );
}
