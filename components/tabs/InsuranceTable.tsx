'use client';

import React, { useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Plus } from "lucide-react";
import { createClient } from '@/utils/supabase/client';

export interface Insurance {
  id: number;
  type: string;
  provider: string;
  policy_number: string;
  premium: number;
  frequency: string;
  coverage_amount: number;
  renewal_date: string;
  details: string;
  household_id: number;
  person_insured?: string; // Person who is insured (name1 or name2)
  policy_owner?: string; // Person who owns the policy (name1 or name2)
}

interface InsuranceTableProps {
  data: Insurance[];
  onDataChange: () => void;
  householdId: string;
  onAddInsurance: () => void;
  onEditInsurance: (insurance: Insurance) => void;
  householdMembers?: { name1: string; name2?: string };
}

export function InsuranceTable({ data, onDataChange, householdId, onAddInsurance, onEditInsurance, householdMembers }: InsuranceTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const handleDelete = async (id: number) => {
    const supabase = createClient();
    const { error } = await supabase
      .from('insurances')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting insurance:', error);
    } else {
      onDataChange();
    }
  };

  const handleEdit = (insurance: Insurance) => {
    onEditInsurance(insurance);
  };

  const columns: ColumnDef<Insurance>[] = [
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => (
        <button
          onClick={() => handleEdit(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('type')}
        </button>
      ),
    },
    {
      accessorKey: "provider",
      header: "Provider",
      cell: ({ row }) => (
        <button
          onClick={() => handleEdit(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('provider')}
        </button>
      ),
    },
    {
      accessorKey: "policy_number",
      header: "Policy Number",
      cell: ({ row }) => (
        <button
          onClick={() => handleEdit(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('policy_number')}
        </button>
      ),
    },
    {
      accessorKey: "premium",
      header: "Premium",
      cell: ({ row }) => (
        <button
          onClick={() => handleEdit(row.original)}
          className="text-right hover:underline block w-full"
        >
          {new Intl.NumberFormat('en-AU', {
            style: 'currency',
            currency: 'AUD',
          }).format(row.original.premium)}
        </button>
      ),
    },
    {
      accessorKey: "frequency",
      header: "Frequency",
    },
    {
      accessorKey: "coverage_amount",
      header: "Coverage Amount",
      cell: ({ row }) => {
        return new Intl.NumberFormat('en-AU', {
          style: 'currency',
          currency: 'AUD',
        }).format(row.original.coverage_amount);
      },
    },
    {
      accessorKey: "renewal_date",
      header: "Renewal Date",
      cell: ({ row }) => {
        return new Date(row.original.renewal_date).toLocaleDateString('en-AU');
      },
    },
    {
      accessorKey: "person_insured",
      header: "Person Insured",
      cell: ({ row }) => {
        const personInsured = row.original.person_insured;
        if (!personInsured) return '-';
        return personInsured;
      },
    },
    {
      accessorKey: "policy_owner",
      header: "Policy Owner",
      cell: ({ row }) => {
        const policyOwner = row.original.policy_owner;
        if (!policyOwner) return '-';
        return policyOwner;
      },
    },
    {
      accessorKey: "details",
      header: "Details",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDelete(row.original.id)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Input
          placeholder="Filter insurances..."
          value={(table.getColumn("type")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("type")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <Button 
          className="flex items-center gap-2"
          onClick={onAddInsurance}
        >
          <Plus className="h-4 w-4" />
          Add Insurance
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No insurances found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>
    </div>
  );
}

export default InsuranceTable;
