import { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Checkbox } from "@/components/ui/checkbox";
import { InputData } from '@/app/protected/planner/types';
import { FUND_TAILWIND_COLORS } from '@/app/constants/fundColors';
import { Switch } from '../ui/switch';

interface SavingsTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
}

export function SavingsTab({ inputData, setInputData, readOnly }: SavingsTabProps) {
  // Get active investment funds from withdrawal priorities
  const [activeInvestmentFunds, setActiveInvestmentFunds] = useState<number[]>([]);

  // Initialize savings allocation
  const [savingsAllocation, setSavingsAllocation] = useState<number>(20);

  // Track total allocation percentage
  const [totalAllocation, setTotalAllocation] = useState<number>(100);

  // Effect to get active investment funds and calculate total allocation
  useEffect(() => {
    // Get active funds from withdrawal priorities
    const activeFunds = inputData.withdrawal_priorities || [];
    setActiveInvestmentFunds(activeFunds);

    // Initialize savings allocation if not set
    if (inputData.savings_allocation === undefined) {
      handleInputChange('savings_allocation', 20);
    } else {
      setSavingsAllocation(inputData.savings_allocation);
    }

    // Calculate total allocation
    let total = inputData.savings_allocation || 0;

    // Add all active investment fund allocations
    activeFunds.forEach(fundNumber => {
      const allocation = inputData[`investment_allocation${fundNumber}` as keyof InputData];
      if (allocation !== undefined) {
        total += Number(allocation);
      }
    });

    setTotalAllocation(total);
  }, [inputData]);

  // Handle input change with validation
  const handleInputChange = (name: keyof InputData, value: string | number) => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate percentage inputs to be between 0 and 100
    if (String(name).includes('_allocation')) {
      const validatedValue = Math.min(Math.max(numValue, 0), 100);
      setInputData((prevData) => ({
        ...prevData,
        [name]: validatedValue,
      }));

      // Update savings allocation state if needed
      if (name === 'savings_allocation') {
        setSavingsAllocation(validatedValue);
      }
      return;
    }

    setInputData((prevData) => ({
      ...prevData,
      [name]: numValue,
    }));

    // Update savings allocation state if needed
    if (name === 'savings_allocation') {
      setSavingsAllocation(numValue);
    }
  };

  const handleCheckboxChange = (name: keyof InputData, checked: boolean) => {
    setInputData((prevData) => ({
      ...prevData,
      [name]: checked,
    }));
  };


  return (
    <div className="space-y-4">
      <div className="flex space-x-4 items-end">
        <div className="flex-grow">
          <LabelWithTooltip
            htmlFor="savings_amount"
            label="Savings Amount"
            tooltipText="Current total savings and investments available at the start of planning"
          />
          <Input
            id="savings_amount"
            name="savings_amount"
            type="number"
            value={inputData.savings_amount}
            onChange={(e) => handleInputChange('savings_amount', e.target.value)}
            readOnly={readOnly}
          />
        </div>
        <div className="w-[200px]">
          <LabelWithTooltip
            htmlFor="cash_rate"
            label="Cash Rate"
            tooltipText="Annual interest rate for cash/savings. This is the rate of return over and above the inflation rate."
          />
          <Input
            id="cash_rate"
            name="cash_rate"
            type="number"
            value={inputData.cash_rate || 0}
            onChange={(e) => handleInputChange('cash_rate', e.target.value)}
            readOnly={readOnly}
          />
        </div>
        <div className="w-[200px]">
          <LabelWithTooltip
            htmlFor="cash_reserve"
            label="Cash Reserve"
            tooltipText="Amount to keep in cash/emergency fund, not invested in the market"
          />
          <Input
            id="cash_reserve"
            name="cash_reserve"
            type="number"
            value={inputData.cash_reserve}
            onChange={(e) => handleInputChange('cash_reserve', e.target.value)}
            readOnly={readOnly}
          />
        </div>
      </div>
      <div className="flex space-x-4 items-center">
        <div className="flex items-center space-x-2">
          <Switch
            id="utilise_excess_cashflow"
            checked={!!inputData.utilise_excess_cashflow}
            onCheckedChange={(checked) => handleCheckboxChange('utilise_excess_cashflow', checked as boolean)}
            disabled={readOnly}
            size={"sm"}
          />
          <LabelWithTooltip
            htmlFor="utilise_excess_cashflow"
            label="Utilise Excess Cashflow"
            tooltipText="When enabled, excess income will be automatically invested according to the specified percentage"
          />
        </div>

      </div>
              {inputData.utilise_excess_cashflow && (
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="w-full">
                <p className="text-sm text-gray-500">
                  Allocate excess cashflow between savings and investment funds. Percentages should total 100%.
                </p>
              </div>

              {/* Total allocation indicator */}
              <div className={`text-sm font-medium ${Math.abs(totalAllocation - 100) < 0.01 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                Total allocation: {totalAllocation.toFixed(0)}%
                {Math.abs(totalAllocation - 100) >= 0.01 && (
                  <span className="ml-2">
                    {totalAllocation > 100 ? '(Over-allocated)' : '(Under-allocated)'}
                  </span>
                )}
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              {/* Savings Fund allocation */}
              <div className="w-[150px]">
                <LabelWithTooltip
                  htmlFor="savings_allocation"
                  label="Savings Fund %"
                  tooltipText="Percentage of excess cashflow to allocate to Savings Fund"
                />
                <Input
                  id="savings_allocation"
                  name="savings_allocation"
                  type="number"
                  value={inputData.savings_allocation !== undefined ? inputData.savings_allocation : savingsAllocation}
                  onChange={(e) => {
                    setSavingsAllocation(parseFloat(e.target.value));
                    handleInputChange('savings_allocation', e.target.value);
                  }}
                  readOnly={readOnly}
                  className={`bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800 ${
                    Math.abs(totalAllocation - 100) >= 0.01 ? 'border-red-300 dark:border-red-700' : ''
                  }`}
                />
              </div>

              {/* Only show active investment funds from withdrawal priorities */}
              {activeInvestmentFunds.map((fundNumber) => (
                <div key={fundNumber} className="w-[150px]">
                  <LabelWithTooltip
                    htmlFor={`investment_allocation${fundNumber}`}
                    label={`Fund ${fundNumber} %`}
                    tooltipText={`Percentage of excess cashflow to allocate to Fund ${fundNumber}`}
                  />
                  <Input
                    id={`investment_allocation${fundNumber}`}
                    name={`investment_allocation${fundNumber}`}
                    type="number"
                    value={inputData[`investment_allocation${fundNumber}` as keyof InputData] !== undefined
                      ? inputData[`investment_allocation${fundNumber}` as keyof InputData]
                      : 20}
                    onChange={(e) => handleInputChange(`investment_allocation${fundNumber}` as keyof InputData, e.target.value)}
                    readOnly={readOnly}
                    className={`${FUND_TAILWIND_COLORS[fundNumber as keyof typeof FUND_TAILWIND_COLORS]?.bg || ''} ${
                      Math.abs(totalAllocation - 100) >= 0.01 ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                </div>
              ))}

              {/* Show a message if no active investment funds */}
              {activeInvestmentFunds.length === 0 && (
                <div className="text-gray-500 italic flex items-center">
                  No active investment funds. Add funds in the Investment tab.
                </div>
              )}
            </div>
          </div>
        )}
    </div>
  );
}
