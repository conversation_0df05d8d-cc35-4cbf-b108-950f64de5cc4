
import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Trash2, Pencil, HelpCircle } from "lucide-react";
import { InputData } from '../../app/protected/planner/types';
import { WhatIfEventModal } from "@/components/WhatIfEventModal";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { eventTypeTooltips } from "@/components/WhatIfEventTooltips";

interface WhatIfTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  mainName?: string;
  partnerName?: string;
}

export type EventType =
  | 'recession'
  | 'death'
  | 'tpd'
  | 'trauma'
  | 'redundancy'
  | 'maternity'
  | 'inheritance';

interface WhatIfEvent {
  id: string;
  type: EventType;
  age: number;
  // Additional fields will be added based on event type
  [key: string]: any;
}

export function WhatIfTab({ inputData, setInputData, mainName = "Main", partnerName = "Partner" }: WhatIfTabProps) {
  const [events, setEvents] = useState<WhatIfEvent[]>(inputData.whatIfEvents || []);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<WhatIfEvent | undefined>(undefined);

  const handleSaveEvent = (event: WhatIfEvent) => {
    let updatedEvents: WhatIfEvent[];

    if (editingEvent) {
      // Update existing event
      updatedEvents = events.map(e => e.id === event.id ? event : e);
    } else {
      // Add new event
      updatedEvents = [...events, event];
    }

    setEvents(updatedEvents);
    setInputData(prev => ({
      ...prev,
      whatIfEvents: updatedEvents
    }));
    setEditingEvent(undefined);
  };

  const handleEditEvent = (event: WhatIfEvent) => {
    setEditingEvent(event);
    setIsModalOpen(true);
  };

  const handleRemoveEvent = (id: string) => {
    const updatedEvents = events.filter(event => event.id !== id);
    setEvents(updatedEvents);

    setInputData(prev => ({
      ...prev,
      whatIfEvents: updatedEvents
    }));
  };

  const handleToggleEvent = (id: string, enabled: boolean) => {
    const updatedEvents = events.map(event =>
      event.id === id ? { ...event, enabled } : event
    );
    setEvents(updatedEvents);

    setInputData(prev => ({
      ...prev,
      whatIfEvents: updatedEvents
    }));
  };

  const getEventTitle = (event: WhatIfEvent) => {
    const typeMap: Record<EventType, string> = {
      'recession': 'Market Recession',
      'death': 'Death',
      'tpd': 'Total Permanent Disability',
      'trauma': 'Trauma/Critical Illness',
      'redundancy': 'Redundancy',
      'maternity': 'Maternity/Paternity Leave',
      'inheritance': 'Inheritance',
    };

    return `${typeMap[event.type]} at age ${event.age}`;
  };

  const getEventDetails = (event: WhatIfEvent) => {
    switch (event.type) {
      case 'recession':
        return `${event.marketLoss}% market loss, ${event.reboundType} rebound over ${event.reboundPeriod} years`;
      case 'death':
        return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout}`;
      case 'tpd':
        return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout}, Expense reduction: ${event.expenseReduction}%`;
      case 'trauma':
        return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout}, Effect: ${event.recoveryPeriod} years, Income: -${event.incomeReduction}%, Expenses: -${event.expenseReduction}%`;
      case 'redundancy':
        return `${event.person === 'main' ? mainName : partnerName}, Severance: $${event.severancePay}, Unemployment: ${event.unemploymentPeriod} months`;
      case 'maternity':
        return `${event.person === 'main' ? mainName : partnerName}, ${event.leavePeriod} months leave, Return at ${event.returnToWorkPercentage}%`;
      case 'inheritance':
        return `$${event.amount}, ${event.investPercentage}% invested`;
      default:
        return 'Event details';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">What If Scenarios</h3>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-md">
                <p>Model life events to see how they impact your financial plan. Add events like market recessions, insurance events, redundancy, maternity leave, or inheritance to test your financial resilience.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Button onClick={() => {
          setEditingEvent(undefined);
          setIsModalOpen(true);
        }}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Event
        </Button>
      </div>

      <WhatIfEventModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSave={handleSaveEvent}
        defaultAge={inputData.starting_age || 30}
        editingEvent={editingEvent}
        mainName={mainName}
        partnerName={partnerName}
        includePartner={inputData.includePartner}
      />

      {events.length > 0 ? (
        <div className="space-y-4">
          {events.map((event) => (
            <Card key={event.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Switch
                        id={`event-${event.id}-enabled`}
                        checked={event.enabled !== false}
                        onCheckedChange={(checked) => handleToggleEvent(event.id, checked)}
                      />
                      <div className="flex items-center gap-1">
                        <Label htmlFor={`event-${event.id}-enabled`} className="font-medium cursor-pointer">
                          {getEventTitle(event)}
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-xs">
                              <p>{eventTypeTooltips[event.type]}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex-1"></div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" onClick={() => handleEditEvent(event)}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleRemoveEvent(event.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground pl-12">{getEventDetails(event)}</p>
                    {event.enabled === false && (
                      <p className="text-sm text-muted-foreground italic pl-12">This event is disabled and will not affect the scenario</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          No what-if events added yet. Add an event to see how it impacts your financial plan.
        </div>
      )}
    </div>
  );
}

export default WhatIfTab;

