import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { InputData } from '@/app/protected/planner/types';
import { Card, CardContent } from "@/components/ui/card";
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from '../ui/switch';

interface PersonalTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  hasPartner: boolean;
  setHasPartner: (hasPartner: boolean) => void;
  readOnly?: boolean;
  mainName?: string;
  partnerName?: string;
}

export function PersonalTab({
  inputData,
  setInputData,
  hasPartner,
  setHasPartner,
  readOnly = false,
  mainName = 'Main',
  partnerName = 'Partner'
}: PersonalTabProps) {
  // Use temporary state to hold input values while typing
  const [tempInputs, setTempInputs] = useState<{[key: string]: string}>({});

  // Validate and adjust age values to ensure start age < end age
  const validateAgeValues = (name: string, value: number | string, prevData: NonNullable<InputData>) => {
    // Handle empty or non-numeric input
    if (value === '' || isNaN(Number(value))) {
      return name.includes('starting') ? 0 : 110; // Default values
    }

    // Convert to number and ensure it's an integer
    let numValue = Math.floor(Number(value));

    // Clamp values between 0 and 110
    numValue = Math.max(0, Math.min(110, numValue));

    // Handle main person age validation
    if (name === 'starting_age') {
      // If start age is >= end age, set end age to start age + 1
      if (numValue >= prevData.ending_age) {
        setInputData(prev => ({ ...prev, ending_age: numValue + 1 }));
      }
    } else if (name === 'ending_age') {
      // If end age is <= start age, set it to start age + 1
      if (numValue <= prevData.starting_age) {
        numValue = prevData.starting_age + 1;
      }
    }

    // Handle partner age validation if needed in the future
    // Currently partner_ending_age is not in the UI, but adding logic for completeness
    if (name === 'partner_starting_age' && prevData.partner_ending_age !== undefined) {
      if (numValue >= prevData.partner_ending_age) {
        // Assuming setInputData is available in this scope or passed down
        // setInputData(prev => ({ ...prev, partner_ending_age: numValue + 1 }));
      }
    } else if (name === 'partner_ending_age') {
      if (numValue <= prevData.partner_starting_age) {
        numValue = prevData.partner_starting_age + 1;
      }
    }

    return numValue;
  };

  // Handle changes to input fields while typing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (readOnly) return;
    const { name, value } = e.target;

    // Store the current input value in temporary state
    setTempInputs(prev => ({
      ...prev,
      [name]: value
    }));

    // For non-age fields or when validation isn't needed, update immediately
    if (!name.includes('age') ||
        (name.includes('age') && !['starting_age', 'ending_age', 'partner_starting_age', 'partner_ending_age'].includes(name))) {
      updateInputData(name, value);
    }
  };

  // Handle blur event to apply validation when user finishes typing
  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (readOnly) return;
    const { name } = e.target;
    const value = tempInputs[name] || '';

    // Apply validation for age fields
    if (['starting_age', 'ending_age', 'partner_starting_age', 'partner_ending_age'].includes(name)) {
      setInputData((prevData) => {
        // Handle empty or non-numeric input
        if (value === '' || isNaN(Number(value))) {
          const validatedValue = name.includes('starting') ? 0 : 110; // Default values
          setTempInputs(prev => ({ ...prev, [name]: String(validatedValue) }));
          return { ...prevData, [name]: validatedValue };
        }

        // Convert to number and ensure it's an integer
        let numValue = Math.floor(Number(value));

        // Clamp values between 0 and 110
        numValue = Math.max(0, Math.min(110, numValue));

        let updatedData = { ...prevData };

        // Handle main person age validation
        if (name === 'starting_age') {
          // If start age is >= end age, set end age to start age + 1
          if (numValue >= updatedData.ending_age) {
            updatedData = { ...updatedData, ending_age: numValue + 1 };
            setTempInputs(prev => ({ ...prev, ending_age: String(numValue + 1) }));
          }
        } else if (name === 'ending_age') {
          // If end age is <= start age, set it to start age + 1
          if (numValue <= updatedData.starting_age) {
            numValue = updatedData.starting_age + 1;
          }
        }

        // Handle partner age validation if needed in the future
        // Currently partner_ending_age is not in the UI, but adding logic for completeness
        if (name === 'partner_starting_age' && updatedData.partner_ending_age !== undefined) {
          if (numValue >= updatedData.partner_ending_age) {
            // Assuming setInputData is available in this scope or passed down
            // updatedData = { ...updatedData, partner_ending_age: numValue + 1 };
            // setTempInputs(prev => ({ ...prev, partner_ending_age: String(numValue + 1) }));
          }
        } else if (name === 'partner_ending_age') {
          if (numValue <= updatedData.partner_starting_age) {
            numValue = updatedData.partner_starting_age + 1;
          }
        }

        setTempInputs(prev => ({ ...prev, [name]: String(numValue) }));
        return { ...updatedData, [name]: numValue };
      });
    } else {
       // For other age or rate fields, parse as integer
      if (name.includes('age') || name.includes('rate')) {
        // Handle empty input
        if (value === '') {
           setInputData(prevData => ({
            ...prevData,
            [name]: name.includes('rate') ? 0 : (name.includes('starting') ? 0 : 110),
          }));
          return;
        }

        const parsedValue = parseInt(value);
         setInputData(prevData => ({
          ...prevData,
          [name]: isNaN(parsedValue) ? prevData[name] : parsedValue,
        }));
        return;
      }

      // For non-numeric fields, use the value as is
       setInputData(prevData => ({
        ...prevData,
        [name]: value,
      }));
    }
  };

  // Update the actual input data with validation
  const updateInputData = (name: string, value: string) => {
    setInputData((prevData) => {
      // For other age or rate fields, parse as integer
      if (name.includes('age') || name.includes('rate')) {
        // Handle empty input
        if (value === '') {
          return {
            ...prevData,
            [name]: name.includes('rate') ? 0 : (name.includes('starting') ? 0 : 110),
          };
        }

        const parsedValue = parseInt(value);
        return {
          ...prevData,
          [name]: isNaN(parsedValue) ? prevData[name] : parsedValue,
        };
      }

      // For non-numeric fields, use the value as is
      return {
        ...prevData,
        [name]: value,
      };
    });
  };

  const handleCheckboxChange = (checked: boolean | "indeterminate", name: string) => {
    if (readOnly) return;
    setInputData((prevData) => ({
      ...prevData,
      [name]: checked === "indeterminate" ? false : checked,
    }));
  };

  const handlePartnerCheckboxChange = (checked: boolean | "indeterminate") => {
    if (readOnly) return;
    const newValue = checked === "indeterminate" ? false : checked;
    setHasPartner(newValue);
    setInputData((prevData) => ({
      ...prevData,
      includePartner: newValue,
    }));
  };

  // Initialize superannuation values with defaults if not set
  const single_super_rate = inputData.single_super_rate || 27000;
  const couple_one_super_rate = inputData.couple_one_super_rate || 19500;
  const couple_both_super_rate = inputData.couple_both_super_rate || 39500;
  const super_eligibility_age = inputData.super_eligibility_age || 65;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="starting_age">{`${mainName}'s Current Age`}</Label>
          <Input
            id="starting_age"
            name="starting_age"
            type="number"
            value={tempInputs.starting_age !== undefined ? tempInputs.starting_age : inputData.starting_age}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            readOnly={readOnly}
            min={0}
            max={110}
          />
        </div>
        <div>
          <Label htmlFor="ending_age">{`${mainName}'s Life Expectancy`}</Label>
          <Input
            id="ending_age"
            name="ending_age"
            type="number"
            value={tempInputs.ending_age !== undefined ? tempInputs.ending_age : inputData.ending_age}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            readOnly={readOnly}
            min={0}
            max={110}
          />
        </div>
      </div>

      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="includePartner"
          checked={hasPartner}
          onCheckedChange={handlePartnerCheckboxChange}
          disabled={readOnly}
          size={"sm"}
        />
        <Label htmlFor="includePartner" className="cursor-pointer">Include Partner</Label>
      </div>

      {hasPartner && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="partner_starting_age">{`${partnerName}'s Current Age`}</Label>
            <Input
              id="partner_starting_age"
              name="partner_starting_age"
              type="number"
              value={tempInputs.partner_starting_age !== undefined ? tempInputs.partner_starting_age : inputData.partner_starting_age}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              readOnly={readOnly}
              min={0}
              max={110}
            />
          </div>
        </div>
      )}

      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="superannuation"
          checked={inputData.superannuation}
          onCheckedChange={(checked) => handleCheckboxChange(checked, "superannuation")}
          disabled={readOnly}
          size={"sm"}
        />
        <Label htmlFor="superannuation" className="cursor-pointer">Include Superannuation</Label>
      </div>

      {inputData.superannuation && (
        <Card className="mt-2 bg-gray-50 dark:bg-gray-900 ">
          <CardContent className="pt-4">
            <div className="space-y-4">
              <div className="flex items-center">
                <h3 className="text-sm font-medium">NZ Superannuation Settings</h3>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoCircledIcon className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-sm">
                      <p>Current NZ Superannuation rates as of April 2025. These values are annual amounts before tax.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="super_eligibility_age">Eligibility Age</Label>
                  <Input
                    id="super_eligibility_age"
                    name="super_eligibility_age"
                    type="number"
                    value={tempInputs.super_eligibility_age !== undefined ? tempInputs.super_eligibility_age : super_eligibility_age}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    readOnly={readOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="single_super_rate">Single Rate (Annual)</Label>
                  <Input
                    id="single_super_rate"
                    name="single_super_rate"
                    type="number"
                    value={single_super_rate}
                    onChange={handleInputChange}
                    readOnly={readOnly}
                  />
                </div>
              </div>

              {hasPartner && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="couple_one_super_rate">Couple - One Qualifying (Annual)</Label>
                    <Input
                      id="couple_one_super_rate"
                      name="couple_one_super_rate"
                      type="number"
                      value={couple_one_super_rate}
                      onChange={handleInputChange}
                      readOnly={readOnly}
                    />
                  </div>
                  <div>
                    <Label htmlFor="couple_both_super_rate">Couple - Both Qualifying (Annual)</Label>
                    <Input
                      id="couple_both_super_rate"
                      name="couple_both_super_rate"
                      type="number"
                      value={couple_both_super_rate}
                      onChange={handleInputChange}
                      readOnly={readOnly}
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
