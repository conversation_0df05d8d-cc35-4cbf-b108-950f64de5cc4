import { Input } from "@/components/ui/input";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { InputData } from '../../app/protected/planner/types';
import { KiwiSaverModal } from '../modals/KiwiSaverModal';
import { Dispatch, SetStateAction, useState } from 'react';

interface KiwiSaverTabProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  hasPartner: boolean;
  readOnly?: boolean;
  mainName?: string;
  partnerName?: string;
}

interface KiwiSaverModalProps {
  inputData: InputData;
  setInputData: (updater: (prevData: InputData) => InputData) => void;
  isPartner: boolean;
  isOpen: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  mainName?: string;
  partnerName?: string;
}

export function KiwiSaverTab({ inputData, setInputData, hasPartner, readOnly, mainName = 'Client', partnerName = 'Partner' }: KiwiSaverTabProps) {
  const [modalOpen, setModalOpen] = useState(false);
  const [partnerModalOpen, setPartnerModalOpen] = useState(false);

  const handleInputChange = (name: string, value: string) => {
    setInputData((prevData) => ({
      ...prevData,
      [name]: parseFloat(value),
    }));
  };

  return (
    <div className="space-y-4">
      <div className="flex space-x-4 items-end">
        <div className="flex-grow">
          <LabelWithTooltip 
            htmlFor="initial_kiwiSaver" 
            label={`${mainName} KiwiSaver Fund`}
            tooltipText={`${mainName}'s current KiwiSaver balance. Click the icon to the right to configure contribution rates and fund type.`}
          />
          <div className="flex items-center">
            <Input 
              id="initial_kiwiSaver" 
              name="initial_kiwiSaver" 
              type="number" 
              value={inputData.initial_kiwiSaver} 
              onChange={(e) => handleInputChange('initial_kiwiSaver', e.target.value)}
              readOnly={readOnly}
            />
            <KiwiSaverModal 
              inputData={inputData} 
              setInputData={setInputData} 
              isPartner={false} 
              isOpen={modalOpen} 
              setOpen={setModalOpen} 
              mainName={mainName}
              partnerName={partnerName}
            />
          </div>
        </div>
      </div>

      {hasPartner && (
        <div className="flex space-x-4 items-end">
          <div className="flex-grow">
            <LabelWithTooltip 
              htmlFor="partner_initial_kiwisaver" 
              label={`${partnerName}'s KiwiSaver Fund`}
              tooltipText={`${partnerName}'s current KiwiSaver balance. Click the icon to the right to configure contribution rates and fund type.`}
            />
            <div className="flex items-center">
              <Input 
                id="partner_initial_kiwisaver" 
                name="partner_initial_kiwisaver" 
                type="number" 
                value={inputData.partner_initial_kiwisaver} 
                onChange={(e) => handleInputChange('partner_initial_kiwisaver', e.target.value)}
                readOnly={readOnly}
              />
              <KiwiSaverModal 
                inputData={inputData} 
                setInputData={setInputData} 
                isPartner={true} 
                isOpen={partnerModalOpen} 
                setOpen={setPartnerModalOpen} 
                mainName={mainName}
                partnerName={partnerName}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
