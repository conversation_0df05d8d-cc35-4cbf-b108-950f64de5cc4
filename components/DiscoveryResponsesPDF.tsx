import React from 'react';
import { Document, Page, Text, View, StyleSheet, Font } from '@react-pdf/renderer';

interface FormResponse {
  [key: string]: string;
}

interface DiscoveryResponsesPDFProps {
  responses: FormResponse;
  questions: Array<{
    id: number;
    question: string;
    options: string[];
  }>;
  createdAt: string;
}

// Register a default font for better text rendering
Font.register({
  family: 'Helvetica',
  fonts: [
    { src: 'https://fonts.cdnfonts.com/s/29107/helvetica-light.woff', fontWeight: 300 },
    { src: 'https://fonts.cdnfonts.com/s/29107/helvetica.woff', fontWeight: 400 },
    { src: 'https://fonts.cdnfonts.com/s/29107/helvetica-bold.woff', fontWeight: 700 },
  ],
});

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 30,
    borderBottom: 1,
    borderBottomColor: '#000000',
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    marginBottom: 10,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
  },
  date: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 5,
  },
  section: {
    margin: 10,
    padding: 10,
    borderBottom: 0.5,
    borderBottomColor: '#CCCCCC',
  },
  question: {
    fontSize: 12,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  answer: {
    fontSize: 11,
    marginLeft: 20,
    color: '#333333',
  },
  noResponse: {
    fontSize: 11,
    marginLeft: 20,
    color: '#999999',
    fontStyle: 'italic',
  },
  summary: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#f5f5f5',
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  summaryText: {
    fontSize: 10,
    color: '#666666',
  },
});

export function DiscoveryResponsesPDF({ responses, questions, createdAt }: DiscoveryResponsesPDFProps) {
  // Calculate response statistics
  const totalQuestions = questions.length;
  const answeredQuestions = Object.keys(responses).length;
  const completionRate = Math.round((answeredQuestions / totalQuestions) * 100);

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>Financial Discovery Questionnaire</Text>
          <Text style={styles.subtitle}>Response Summary</Text>
          <Text style={styles.date}>
            Submitted on: {new Date(createdAt).toLocaleDateString()} at{' '}
            {new Date(createdAt).toLocaleTimeString()}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={styles.summaryTitle}>Completion Summary</Text>
          <Text style={styles.summaryText}>
            Questions Answered: {answeredQuestions} out of {totalQuestions} ({completionRate}% complete)
          </Text>
        </View>

        {questions.map((q) => {
          const response = responses[q.id.toString()];
          return (
            <View key={q.id} style={styles.section}>
              <Text style={styles.question}>
                {q.id}. {q.question}
              </Text>
              {response ? (
                <Text style={styles.answer}>Response: {response}</Text>
              ) : (
                <Text style={styles.noResponse}>No response provided</Text>
              )}
            </View>
          );
        })}
      </Page>
    </Document>
  );
}
