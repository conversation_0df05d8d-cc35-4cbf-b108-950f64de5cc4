/**
 * Server-Side Authentication Guard
 *
 * Provides server-side authentication validation that cannot be bypassed
 * by client-side manipulation. Use this for sensitive pages.
 */

import { requireAuth, requireMFA, getAuthUser } from '@/utils/auth-guard';
import { redirect } from 'next/navigation';

interface ServerAuthGuardProps {
  children: React.ReactNode;
  requireMFA?: boolean;
  fallbackUrl?: string;
}

/**
 * Server component that validates authentication on the server
 * This cannot be bypassed by client-side manipulation
 */
export async function ServerAuthGuard({
  children,
  requireMFA: needsMFA = false,
  fallbackUrl = '/sign-in'
}: ServerAuthGuardProps) {
  try {
    if (needsMFA) {
      // Require both authentication and MFA
      await requireMFA();
    } else {
      // Just require authentication
      await requireAuth();
    }

    return <>{children}</>;
  } catch (error) {
    // If authentication fails, redirect
    redirect(fallbackUrl);
  }
}

/**
 * Server component that checks authentication without redirecting
 * Useful for conditional rendering based on auth status
 */
export async function ServerAuthCheck({
  children,
  fallback = null
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const user = await getAuthUser();

  if (!user) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Higher-order component for protecting entire pages on the server
 */
export function withServerAuth<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  options: {
    requireMFA?: boolean;
    fallbackUrl?: string;
  } = {}
) {
  return async function ProtectedComponent(props: T) {
    try {
      if (options.requireMFA) {
        await requireMFA();
      } else {
        await requireAuth();
      }

      return <Component {...props} />;
    } catch (error) {
      redirect(options.fallbackUrl || '/sign-in');
    }
  };
}

/**
 * Server component that provides user data to child components
 */
export async function ServerUserProvider({
  children
}: {
  children: (user: any) => React.ReactNode
}) {
  const user = await getAuthUser();
  return <>{children(user)}</>;
}

/**
 * Server component for role-based access control
 */
export async function ServerRoleGuard({
  children,
  allowedRoles,
  fallbackUrl = '/unauthorized'
}: {
  children: React.ReactNode;
  allowedRoles: string[];
  fallbackUrl?: string;
}) {
  try {
    const user = await requireAuth();

    // Get user profile to check role
    const { createClient } = await import('@/utils/supabase/server');
    const supabase = createClient();

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('org_role')
      .eq('user_id', user.id)
      .single();

    if (error || !profile || !allowedRoles.includes(profile.org_role)) {
      redirect(fallbackUrl);
    }

    return <>{children}</>;
  } catch (error) {
    redirect(fallbackUrl);
  }
}
