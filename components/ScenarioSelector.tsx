'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from '@/utils/supabase/client'
import ScenarioDataTable from './tables/ScenarioDataTable'
import { FUND_TYPES } from '@/app/constants/fundTypes';

interface ScenarioSelectorProps {
  householdId: number | null;
  onScenarioSelect: (data: any) => void;
}

export default function ScenarioSelector({ householdId, onScenarioSelect }: ScenarioSelectorProps) {
  const [scenarios, setScenarios] = useState<any[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [scenarioData, setScenarioData] = useState<any | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchScenarios = async () => {
      if (householdId) {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('scenarios_data1')
          .select('scenario_name')
          .eq('household_id', householdId);

        if (error) {
          console.error('Error fetching scenarios:', error);
        } else {
          setScenarios(data || []);
        }
      }
    };

    fetchScenarios();
  }, [householdId]);

  const getFundType = (type: string | null): keyof typeof FUND_TYPES => {
    if (type && type in FUND_TYPES) {
      return type as keyof typeof FUND_TYPES;
    }
    return 'Balanced'; // default value
  };

  const handleScenarioChange = async (value: string) => {
    setSelectedScenario(value);
    const supabase = createClient();
    const { data, error } = await supabase
      .from('scenarios_data1')
      .select('*')
      .eq('scenario_name', value)
      .single();

    if (error) {
      console.error('Error fetching scenario data:', error);
    } else {
      // Process fund periods data
      const processedData = {
        ...data,
        fund_periods: Array.isArray(data.fund_periods) ? 
          data.fund_periods.map((period: { fundType: string | null; period: any; }) => ({
            fundType: getFundType(period.fundType),
            period: period.period,
            return: FUND_TYPES[getFundType(period.fundType)].return ?? 0,
            stdDev: FUND_TYPES[getFundType(period.fundType)].stdDev ?? 0
          })) : 
          [{
            fundType: getFundType(data.fund_type),
            period: [data.starting_age || 0, data.ending_age || 0],
            return: FUND_TYPES[getFundType(data.fund_type)].return ?? 0,
            stdDev: FUND_TYPES[getFundType(data.fund_type)].stdDev ?? 0
          }]
      };
      
      setScenarioData(processedData);
      onScenarioSelect(processedData);
    }
  };

  return (
    <div className="w-full max-w-md">
      <Select onValueChange={handleScenarioChange} disabled={!householdId}>
        <SelectTrigger>
          <SelectValue placeholder="Select Scenario" />
        </SelectTrigger>
        <SelectContent>
          {scenarios.map((scenario) => (
            <SelectItem key={scenario.scenario_name} value={scenario.scenario_name}>
              {scenario.scenario_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {scenarioData && <ScenarioDataTable data={scenarioData} />}
    </div>
  )
}
