'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { TasksTable } from '@/components/tables/TasksTable';
import TaskModal from '@/components/modals/TaskModal';

// Update the Task interface to match the one in TasksTable.tsx
interface Comment {
  id: number;
  task_id: number;
  content: string;
  created_at: string;
  user_id: string;
}

interface Task {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  comments: Comment[];
  assigned_to?: string;
  assigned_name?: string;
  linked_interactions?: {
    id: number;
    interaction_id: number;
    title: string;
    date: string;
    type: string;
  }[];
}

// Renamed component to avoid conflict
export default function TasksPageClient() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | undefined>(undefined);
  const searchParams = useSearchParams();
  const supabase = createClient();

  const fetchTasks = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return;
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_comments (*),
        households (
          householdName
        )
      `)
      .order('due_date', { ascending: true });
      
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    if (data) {
      // Get assigned user names in a separate query if needed
      let assignedUserNames: {[key: string]: string} = {};
      const assignedUserIds = data
        .filter(task => task.assigned_to)
        .map(task => task.assigned_to);
        
      if (assignedUserIds.length > 0) {
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, name')
          .in('user_id', assignedUserIds);
          
        if (!profilesError && profilesData) {
          assignedUserNames = profilesData.reduce((acc: {[key: string]: string}, profile) => {
            acc[profile.user_id] = profile.name;
            return acc;
          }, {});
        }
      }
      
      // Format the tasks to match the TasksTable Task interface
      const formattedTasks: Task[] = data.map(task => ({
        id: task.id,
        household_id: task.household_id,
        household_name: task.households?.householdName || 'Unknown Household',
        title: task.title,
        content: task.content || '',
        importance: task.importance || '',
        created_at: task.created_at,
        due_date: task.due_date,
        updated_at: task.updated_at,
        user_id: task.user_id,
        status: task.status || '',
        comments: task.task_comments || [],
        assigned_to: task.assigned_to,
        assigned_name: task.assigned_to ? assignedUserNames[task.assigned_to] || 'Unknown User' : undefined
      }));
      
      setTasks(formattedTasks);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  // Add this useEffect to handle the view parameter
  useEffect(() => {
    const viewTaskId = searchParams.get('view');
    
    if (viewTaskId) {
      console.log(`Task view parameter detected: ${viewTaskId}`);
      
      // Find the task in our loaded data
      const taskToView = tasks.find(
        task => task.id.toString() === viewTaskId
      );
      
      if (taskToView) {
        console.log('Found task to view:', taskToView);
        // Open the modal with the task
        setSelectedTask(taskToView);
        setIsModalOpen(true);
      } else if (tasks.length > 0) {
        // Only fetch if we already loaded tasks but didn't find this one
        console.log('Task not found in current data, fetching...');
        fetchSpecificTask(parseInt(viewTaskId));
      }
      // If tasks.length is 0, we'll wait for fetchTasks to complete first
    }
  }, [searchParams, tasks]); // Re-run when searchParams or tasks change

  // Update the fetchSpecificTask function to match the Task interface
  const fetchSpecificTask = async (taskId: number) => {
    console.log(`Fetching specific task: ${taskId}`);
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_comments (
          id,
          content,
          created_at,
          user_id
        ),
        households (
          householdName
        )
      `)
      .eq('id', taskId)
      .single();
      
    if (error) {
      console.error('Error fetching specific task:', error);
      return;
    }
    
    if (data) {
      console.log('Successfully fetched task:', data);
      
      // Get assigned user name in a separate query if needed
      let assignedName;
      if (data.assigned_to) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('name')
          .eq('user_id', data.assigned_to)
          .single();
          
        if (!profileError && profileData) {
          assignedName = profileData.name;
        }
      }
      
      // Format the task to match the TasksTable Task interface
      const formattedTask: Task = {
        id: data.id,
        household_id: data.household_id,
        household_name: data.households?.householdName || 'Unknown Household',
        title: data.title,
        content: data.content || '',
        importance: data.importance || '',
        created_at: data.created_at,
        due_date: data.due_date,
        updated_at: data.updated_at,
        user_id: data.user_id,
        status: data.status || '',
        comments: data.task_comments || [],
        assigned_to: data.assigned_to,
        assigned_name: assignedName || 'Unknown User'
      };
      
      // Open the modal with the fetched task
      setSelectedTask(formattedTask);
      setIsModalOpen(true);
    }
  };

  return (
    <Card className="h-[calc(100vh-62px)] mt-[57px] mr-2 mb-12 flex flex-col">
      <CardContent className="flex-grow pt-4">
        <TasksTable
          data={tasks}
          onDataChange={fetchTasks}
          onCreateTask={() => {
            setSelectedTask(undefined);
            setIsModalOpen(true);
          }}
          onEditTask={(task: Task) => {
            setSelectedTask(task);
            setIsModalOpen(true);
          }}
        />
      </CardContent>

      {isModalOpen && (
        <TaskModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedTask(undefined);
          }}
          task={selectedTask}
          onSave={fetchTasks}
        />
      )}
    </Card>
  );
}
