"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Bell, Check, ExternalLink, Trash2 } from "lucide-react";
import Link from "next/link";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface Notification {
  from_user_name: any;
  id: number;
  user_id: string;
  content: string;
  type: string;
  link: string | null;
  read: boolean;
  created_at: string;
}

export default function Notifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    fetchNotifications();

    // Set up real-time subscription for new notifications
    const channel = supabase
      .channel('notifications-changes')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
      }, () => {
        fetchNotifications();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const fetchNotifications = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // Fetch user's notification preferences from profiles table
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('notification_preferences')
      .eq('user_id', user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching notification preferences:', profileError);
    }

    // Get notification preferences
    const notificationPreferences = profileData?.notification_preferences || [];

    // Fetch notifications for the current user
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching notifications:', error);
      return;
    }

    // Get user information for notifications that involve tagging
    const notificationsWithUserInfo = await Promise.all(
      data.map(async (notification) => {
        // Check if this is a mention notification
        if (notification.type.includes('mention')) {
          // Extract the user ID from the notification content if possible
          // This is a simplification - you might need to store the from_user_id in the notification
          const { data: commentData, error: commentError } = await supabase
            .from(notification.type.includes('task') ? 'task_comments' : 'comments')
            .select('user_id')
            .order('created_at', { ascending: false })
            .limit(1);

          if (!commentError && commentData.length > 0) {
            const fromUserId = commentData[0].user_id;

            // Get the user's name
            const { data: userData, error: userError } = await supabase
              .from('profiles')
              .select('name')
              .eq('user_id', fromUserId)
              .single();

            if (!userError && userData) {
              return {
                ...notification,
                from_user_id: fromUserId,
                from_user_name: userData.name
              };
            }
          }
        }

        return notification;
      })
    );

    // Filter notifications based on user preferences
    const filteredNotifications = notificationsWithUserInfo.filter(notification => {
      // If no preferences are set, show all notifications
      if (!notificationPreferences.length) return true;

      // Find the preference for this notification type
      let preference = notificationPreferences.find((p: any) => p.type === notification.type);

      // For calendar notifications, check the specific calendar preference type
      if (!preference && notification.type.includes('calendar')) {
        // Map calendar notification types to preference types
        const calendarTypeMap: Record<string, string> = {
          'calendar_meeting_created': 'calendar_meeting_invite',
          'calendar_meeting_updated': 'calendar_event_update',
          'calendar_meeting_cancelled': 'calendar_event_cancellation',
          'calendar_meeting_reminder': 'calendar_meeting_reminder',
          'calendar_meeting_response': 'calendar_meeting_response'
        };

        // Find the mapped preference type
        const mappedType = Object.entries(calendarTypeMap).find(([key]) =>
          notification.type.includes(key)
        )?.[1];

        if (mappedType) {
          preference = notificationPreferences.find((p: any) => p.type === mappedType);
        }
      }

      // If no preference is found for this type, show the notification (default to enabled)
      if (!preference) return true;

      // Show notification only if the preference is enabled
      return preference.enabled;
    });

    setNotifications(filteredNotifications);
    setUnreadCount(filteredNotifications.filter(n => !n.read).length);
  };

  const markAsRead = async (id: number) => {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', id);

    if (error) {
      console.error('Error marking notification as read:', error);
      return;
    }

    // Update local state
    setNotifications(notifications.map(n =>
      n.id === id ? { ...n, read: true } : n
    ));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('user_id', user.id)
      .eq('read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      return;
    }

    // Update local state
    setNotifications(notifications.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'task_mention':
        return 'Task Mention';
      case 'interaction_mention':
        return 'Interaction Mention';
      case 'discovery_form_submission':
        return 'Discovery Form';
      case 'tpa_form_submission':
        return 'TPA Form';
      case 'toe_form_submission':
        return 'TOE Form';
      case 'risk_profiler_submission':
        return 'Risk Profiler';
      default:
        return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Add this function to determine the correct link format based on notification type
  const getFormattedLink = (notification: Notification) => {
    // If it's a task notification, ensure we're using the correct URL format
    if (notification.type && notification.type.includes('task') && notification.link) {
      // Extract the task ID from the link
      const taskIdMatch = notification.link.match(/view=(\d+)/);
      const taskId = taskIdMatch ? taskIdMatch[1] : null;

      if (taskId) {
        // Format the URL correctly for the tasks page
        return `/protected/tasks?view=${taskId}`;
      }
    }

    // For other notification types, return the original link
    return notification.link;
  }

  // Add this function to determine badge variant based on notification type
  const getNotificationBadgeVariant = (type: string): "default" | "destructive" | "outline" | "secondary" | "purple" | "blue" | "green" | "yellow" | null | undefined => {
    if (type.includes('task')) {
      return 'purple';
    } else if (type.includes('interaction')) {
      return 'blue';
    } else if (type.includes('household')) {
      return 'green';
    } else if (type.includes('mention')) {
      return 'yellow';
    } else if (type.includes('discovery_form')) {
      return 'green';
    } else if (type.includes('tpa_form')) {
      return 'secondary'; // Using secondary instead of orange
    } else if (type.includes('toe_form')) {
      return 'blue';
    } else if (type.includes('risk_profiler')) {
      return 'destructive'; // Using destructive instead of red
    } else {
      return 'outline';
    }
  };

  const deleteNotification = async (id: number) => {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting notification:', error);
      return;
    }

    // Update local state
    setNotifications(notifications.filter(n => n.id !== id));
    if (!notifications.find(n => n.id === id)?.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const dismissAll = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('user_id', user.id);

    if (error) {
      console.error('Error dismissing all notifications:', error);
      return;
    }

    // Update local state
    setNotifications([]);
    setUnreadCount(0);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="relative p-2">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[450px] p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">Notifications</h3>
          <div className="flex gap-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs"
              >
                Mark all as read
              </Button>
            )}
            {notifications.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={dismissAll}
                className="text-xs text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                Dismiss all
              </Button>
            )}
          </div>
        </div>
        <ScrollArea className="h-[400px]">
          {notifications.length > 0 ? (
            <div className="divide-y">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 ${notification.read ? 'bg-background' : 'bg-muted'}`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant={getNotificationBadgeVariant(notification.type)} className="text-xs">
                          {getNotificationTypeLabel(notification.type)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(notification.created_at), 'MMM d, h:mm a')}
                        </span>
                      </div>
                      <p className="text-sm">{notification.content}</p>
                      {notification.from_user_name && (
                        <p className="text-xs text-muted-foreground mt-1">
                          From: {notification.from_user_name}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {!notification.read && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => markAsRead(notification.id)}
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => deleteNotification(notification.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      {notification.link && (
                        <Link
                          href={getFormattedLink(notification) || '#'}
                          onClick={(e) => {
                            if (!notification.read) markAsRead(notification.id);
                            setIsOpen(false);
                          }}
                        >
                          <Button variant="ghost" size="icon" className="h-6 w-6">
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-muted-foreground">
              No notifications
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
