import { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LabelWithTooltip } from "@/components/TabTooltips";
import { InputData } from "../../app/protected/planner/types";
import { cn } from '@/lib/utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface PropertyModalProps {
  isOpen: boolean;
  onClose: () => void;
  property: InputData;
  onPropertyChange: (property: InputData) => void;
  readOnly?: boolean;
  allMetrics?: any[];
}

export function PropertyModal({ isOpen, onClose, property, onPropertyChange, readOnly = false, allMetrics = [] }: PropertyModalProps) {
  // Get main and partner names for KiwiSaver labels
  const mainName = property.name || 'Main';
  const partnerName = property.partner_name || 'Partner';
  const [tempProperty, setTempProperty] = useState<InputData>(property);

  // Get active investment funds from withdrawal priorities
  const activeInvestmentFunds = property.withdrawal_priorities?.length || 1;
  const [selectedInvestmentFund, setSelectedInvestmentFund] = useState<number>(1);

  // Initialize showPurchaseDetails based on property data or existing purchase details
  const propertyIndex = property.property_index || 1;
  const purchaseAgeField = propertyIndex > 1 ?
    `purchase_age${propertyIndex}` : 'purchase_age';
  const depositAmountField = propertyIndex > 1 ?
    `deposit_amount${propertyIndex}` : 'deposit_amount';
  const depositSourcesField = propertyIndex > 1 ?
    `deposit_sources${propertyIndex}` : 'deposit_sources';
  const showPurchaseDetailsField = propertyIndex > 1 ?
    `show_purchase_details${propertyIndex}` : 'show_purchase_details';

  // Check if the show_purchase_details field exists in the property data
  const savedShowPurchaseDetails = property[showPurchaseDetailsField as keyof InputData];

  // If the field doesn't exist, fall back to checking if there are existing purchase details
  const hasPurchaseDetails = !!(property[purchaseAgeField as keyof InputData] ||
                              property[depositAmountField as keyof InputData] ||
                              property[depositSourcesField as keyof InputData]);

  const [showPurchaseDetails, setShowPurchaseDetails] = useState<boolean>(
    typeof savedShowPurchaseDetails === 'boolean' ? savedShowPurchaseDetails : hasPurchaseDetails
  );

  // Define getPropertyNumber and getFieldName functions before useEffect
  const getPropertyNumber = (property: InputData) => {
    return property.property_index !== undefined ? property.property_index : '';
  };

  const getFieldName = (baseName: string, prop: InputData = tempProperty) => {
    const propertyNum = getPropertyNumber(prop);
    // Make sure we're returning the correct field name for property 2
    // This is important for rental_income2, board_income2, etc.
    return propertyNum && propertyNum > 1 ? `${baseName}${propertyNum}` : baseName;
  };

  // Function to get fund values at a specific age from the financial metrics
  const getFundValueAtAge = (fundType: 'savings' | 'investments' | 'main_kiwisaver' | 'partner_kiwisaver' | string, targetAge: number) => {
    const startingAge = tempProperty.starting_age || 0;

    // Handle investment fund specific requests (investment_fund1, investment_fund2, etc.)
    const isInvestmentFund = typeof fundType === 'string' && fundType.startsWith('investment_fund');
    const fundNumber = isInvestmentFund ? parseInt(fundType.replace('investment_fund', '')) : null;

    // If we don't have metrics or the target age is before the starting age, use initial values
    if (allMetrics.length === 0 || targetAge < startingAge) {
      if (fundType === 'savings') return tempProperty.savings_amount || 0;
      if (fundType === 'investments') return tempProperty.initial_investment || 0;
      if (fundType === 'main_kiwisaver') return tempProperty.initial_kiwiSaver || 0;
      if (fundType === 'partner_kiwisaver') return tempProperty.partner_initial_kiwisaver || 0;
      if (isInvestmentFund && fundNumber) {
        return (tempProperty as any)[`initial_investment${fundNumber}`] || 0;
      }
      return 0;
    }

    // Find the metrics for the target age
    const ageMetrics = allMetrics.find(metric => metric.Age === targetAge);

    // If we don't have metrics for the target age, use the closest available age
    if (!ageMetrics) {
      // Find the closest age in the metrics
      const closestMetrics = allMetrics.reduce((prev, curr) => {
        return Math.abs(curr.Age - targetAge) < Math.abs(prev.Age - targetAge) ? curr : prev;
      });

      if (fundType === 'savings') return closestMetrics['Savings Fund'] || 0;
      if (fundType === 'investments') return closestMetrics['Investments Fund'] || 0;
      if (fundType === 'main_kiwisaver') return closestMetrics['Main KiwiSaver'] || 0;
      if (fundType === 'partner_kiwisaver') return closestMetrics['Partner KiwiSaver'] || 0;
      if (isInvestmentFund && fundNumber) {
        return closestMetrics[`Investment Fund ${fundNumber}`] || 0;
      }
      return 0;
    }

    // Return the fund value for the target age
    if (fundType === 'savings') return ageMetrics['Savings Fund'] || 0;
    if (fundType === 'investments') return ageMetrics['Investments Fund'] || 0;
    if (fundType === 'main_kiwisaver') return ageMetrics['Main KiwiSaver'] || 0;
    if (fundType === 'partner_kiwisaver') return ageMetrics['Partner KiwiSaver'] || 0;
    if (isInvestmentFund && fundNumber) {
      return ageMetrics[`Investment Fund ${fundNumber}`] || 0;
    }
    return 0;
  };

  useEffect(() => {
    // Create a mutable copy of the property
    const updatedProperty: InputData = { ...property };
    const purchaseAgeField = getFieldName('purchase_age', property) as keyof InputData;
    const sourcesField = getFieldName('deposit_sources', property) as keyof InputData;
    const additionalRepaymentStartAgeField = getFieldName('additional_debt_repayments_start_age', property) as keyof InputData;
    const additionalRepaymentEndAgeField = getFieldName('additional_debt_repayments_end_age', property) as keyof InputData;


    let needsUpdate = false;
    const updates: Partial<InputData> = {};

    // Set default purchase age to starting age if not already set or is 0/null/undefined
    const currentPurchaseAge = updatedProperty[purchaseAgeField];
    if (currentPurchaseAge === undefined || currentPurchaseAge === null || currentPurchaseAge === 0) {
      const defaultValue = updatedProperty.starting_age || 0;
      // Only update if the default value is different from the current value
      if (currentPurchaseAge !== defaultValue) {
        updates[purchaseAgeField] = defaultValue;
        needsUpdate = true;
      }
    }

    // Initialize deposit sources if they don't exist
    if (!updatedProperty[sourcesField]) {
      updates[sourcesField] = {
        savings: 0,
        investments: 0,
        main_kiwisaver: 0,
        partner_kiwisaver: 0,
        gifting: 0,
        other: 0
      };
      needsUpdate = true;
    }

    // Initialize additional repayment start age if not already set
    const currentStartAge = updatedProperty[additionalRepaymentStartAgeField];
    if (currentStartAge === undefined || currentStartAge === null) {
      updates[additionalRepaymentStartAgeField] = updatedProperty.starting_age || 0;
      needsUpdate = true;
    }

    // Initialize additional repayment end age if not already set
    const currentEndAge = updatedProperty[additionalRepaymentEndAgeField];
    if (currentEndAge === undefined || currentEndAge === null) {
      updates[additionalRepaymentEndAgeField] = updatedProperty.ending_age || 0;
      needsUpdate = true;
    }

    // Apply updates if any changes were made
    if (needsUpdate) {
      setTempProperty(prev => ({ ...prev, ...updates }));
    } else {
      // Ensure tempProperty is synced with property if no updates needed initially
      // This handles cases where the property prop itself changes externally
      if (JSON.stringify(tempProperty) !== JSON.stringify(property)) {
         setTempProperty(property);
      }
    }
  }, [property]); // Keep dependency on property

  const inputClassName = cn(
    "transition-colors",
    readOnly && "bg-muted text-muted-foreground cursor-not-allowed"
  );

  const handleInputChange = (name: keyof InputData, value: string | number | boolean) => {
    if (readOnly) return;
    if (name === 'main_property_sale_age') {
      let saleAge: number;
      if (typeof value === 'string') {
        saleAge = parseFloat(value) || 0;
      } else if (typeof value === 'number') {
        saleAge = value;
      } else {
        return; // ignore boolean values for sale age
      }
      setTempProperty(prev => ({
        ...prev,
        [name]: saleAge,
        main_prop_sale_value: calculateFuturePropertyValue(saleAge),
      }));
    } else if (name === 'property_title') {
      if (typeof value !== 'string') return; // only accept string values for property title

      // If this is a property with index > 1, we need to set the property_titleX field
      const propertyIndex = tempProperty.property_index;
      if (propertyIndex && propertyIndex > 1) {
        const titleField = `property_title${propertyIndex}` as keyof InputData;

        setTempProperty(prev => ({
          ...prev,
          [name]: value,
          [titleField]: value, // Also set the indexed property title field
        }));
      } else {
        setTempProperty(prev => ({
          ...prev,
          [name]: value,
        }));
      }
    } else if (name === 'main_property_sale_age3') {
      let saleAge: number;
      if (typeof value === 'string') {
        saleAge = parseFloat(value) || 0;
      } else if (typeof value === 'number') {
        saleAge = value;
      } else {
        return; // ignore boolean values for sale age
      }
      setTempProperty(prev => ({
        ...prev,
        [name]: saleAge,
        main_prop_sale_value3: calculateFuturePropertyValue(saleAge),
      }));
    } else if (name === 'main_property_sale_age4') {
      let saleAge: number;
      if (typeof value === 'string') {
        saleAge = parseFloat(value) || 0;
      } else if (typeof value === 'number') {
        saleAge = value;
      } else {
        return; // ignore boolean values for sale age
      }
      setTempProperty(prev => ({
        ...prev,
        [name]: saleAge,
        main_prop_sale_value4: calculateFuturePropertyValue(saleAge),
      }));
    } else if (name === 'main_property_sale_age5') {
      let saleAge: number;
      if (typeof value === 'string') {
        saleAge = parseFloat(value) || 0;
      } else if (typeof value === 'number') {
        saleAge = value;
      } else {
        return; // ignore boolean values for sale age
      }
      setTempProperty(prev => ({
        ...prev,
        [name]: saleAge,
        main_prop_sale_value5: calculateFuturePropertyValue(saleAge),
      }));
    } else {

      setTempProperty(prev => ({
        ...prev,
        [name]: typeof value === 'string' ? parseFloat(value) : value,
      }));
    }
  };

  const handleCheckboxChange = (name: keyof InputData, checked: boolean) => {
    if (readOnly) return;
    setTempProperty(prev => {
      const updates: Partial<InputData> = {
        [name]: checked
      };

      return {
        ...prev,
        ...updates
      };
    });
  };

  const handleSourceChange = (value: 'investments' | 'savings') => {
    if (readOnly) return;
    const fieldName = getFieldName('lump_sum_payment_source');
    setTempProperty(prev => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  const calculateFuturePropertyValue = (saleAge: number) => {
    const propertyIndex = tempProperty.property_index;
    let yearsToSale, propertyValue, propertyGrowth;

    if (propertyIndex === 2) {
      yearsToSale = saleAge - ((tempProperty as any).starting_age2 || tempProperty.starting_age);
      propertyValue = tempProperty.property_value2 || 0;
      propertyGrowth = tempProperty.property_growth2 || 0;
    } else if (propertyIndex === 3) {
      yearsToSale = saleAge - ((tempProperty as any).starting_age3 || tempProperty.starting_age);
      propertyValue = tempProperty.property_value3 || 0;
      propertyGrowth = tempProperty.property_growth3 || 0;
    } else if (propertyIndex === 4) {
      yearsToSale = saleAge - ((tempProperty as any).starting_age4 || tempProperty.starting_age);
      propertyValue = tempProperty.property_value4 || 0;
      propertyGrowth = tempProperty.property_growth4 || 0;
    } else if (propertyIndex === 5) {
      yearsToSale = saleAge - ((tempProperty as any).starting_age5 || tempProperty.starting_age);
      propertyValue = tempProperty.property_value5 || 0;
      propertyGrowth = tempProperty.property_growth5 || 0;
    } else {
      yearsToSale = saleAge - tempProperty.starting_age;
      propertyValue = tempProperty.property_value;
      propertyGrowth = tempProperty.property_growth;
    }

    const futureValue = propertyValue * Math.pow(1 + (propertyGrowth / 100), yearsToSale);
    return Math.round(futureValue);
  };



  const getFieldValue = (baseName: string) => {
    const fieldName = getFieldName(baseName) as keyof InputData;
    return tempProperty[fieldName];
  };

  const handleUpdate = () => {
    // Get the field names for additional repayment start and end ages
    const additionalRepaymentStartAgeField = getFieldName('additional_debt_repayments_start_age') as keyof InputData;
    const additionalRepaymentEndAgeField = getFieldName('additional_debt_repayments_end_age') as keyof InputData;
    const depositSourcesField = getFieldName('deposit_sources') as keyof InputData;

    // Add the showPurchaseDetails state to the property data
    const propertyIndex = tempProperty.property_index || 1;
    const showPurchaseDetailsField = propertyIndex > 1 ?
      `show_purchase_details${propertyIndex}` : 'show_purchase_details';

    const updatedProperty = {
      ...tempProperty,
      [showPurchaseDetailsField]: showPurchaseDetails
    };


    // Debug logging for deposit sources
    let depositSources = updatedProperty[depositSourcesField];

    // Ensure deposit_sources is a proper object
    if (typeof depositSources !== 'object' || depositSources === null) {
      depositSources = {
        savings: 0,
        investments: 0,
        main_kiwisaver: 0,
        partner_kiwisaver: 0,
        gifting: 0,
        other: 0
      };

      // Update the property with the new deposit sources
      (updatedProperty as any)[depositSourcesField] = depositSources;
    }

    // Check if we have fund-specific withdrawals
    for (let fundNumber = 1; fundNumber <= 5; fundNumber++) {
      const fundKey = `fund${fundNumber}`;
    }

    onPropertyChange(updatedProperty);
  };

  const handleCancel = () => {
    setTempProperty(property);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{tempProperty.property_title || 'Property Details'}</DialogTitle>
        </DialogHeader>

        <div className="min-h-[60vh]">
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details">Property & Debt</TabsTrigger>
              <TabsTrigger value="sell">{!tempProperty.property_index ? 'Sell/Downsize' : 'Sell'}</TabsTrigger>
              <TabsTrigger value="repayments">Repayments</TabsTrigger>
              <TabsTrigger value="rentboard">Rent/Board</TabsTrigger>
            </TabsList>

            {/* Property & Debt Details Tab */}
            <TabsContent value="details" className="space-y-4 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Property Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="property_title"
                          label="Property Title"
                          tooltipText="Name of the property"
                        />
                        <Input
                          id="property_title"
                          name="property_title"
                          value={tempProperty.property_title || ''}
                          onChange={(e) => handleInputChange('property_title', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="property_value"
                          label="Property Value"
                          tooltipText="Current value of the property"
                        />
                        <Input
                          id="property_value"
                          name="property_value"
                          type="number"
                          value={tempProperty.property_value}
                          onChange={(e) => handleInputChange('property_value', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="property_growth"
                          label="Property Growth (%)"
                          tooltipText="Expected annual growth rate of the property value"
                        />
                        <Input
                          id="property_growth"
                          name="property_growth"
                          type="number"
                          value={tempProperty.property_growth}
                          onChange={(e) => handleInputChange('property_growth', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="debt"
                          label="Debt"
                          tooltipText="Current mortgage or loan on the property"
                        />
                        <Input
                          id="debt"
                          name="debt"
                          type="number"
                          value={tempProperty.debt}
                          onChange={(e) => handleInputChange('debt', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="debt_ir"
                          label="Interest Rate (%)"
                          tooltipText="Annual interest rate on the mortgage or loan"
                        />
                        <Input
                          id="debt_ir"
                          name="debt_ir"
                          type="number"
                          value={tempProperty.debt_ir}
                          onChange={(e) => handleInputChange('debt_ir', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="initial_debt_years"
                          label="Loan Term (years)"
                          tooltipText="Total duration of the mortgage or loan in years"
                        />
                        <Input
                          id="initial_debt_years"
                          name="initial_debt_years"
                          type="number"
                          value={tempProperty.initial_debt_years}
                          onChange={(e) => handleInputChange('initial_debt_years', e.target.value)}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Purchase Details Card */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle>Purchase Details</CardTitle>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="show-purchase-details" className="text-sm">Show Details</Label>
                    <Switch
                      id="show-purchase-details"
                      checked={showPurchaseDetails}
                      onCheckedChange={setShowPurchaseDetails}
                      disabled={readOnly}
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  {showPurchaseDetails && (
                  <div className="space-y-4">
                    {/* Row 1: Purchase Age and Payment Sources */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="purchase_age"
                          label="Purchase Age"
                          tooltipText="Age at which the property was or will be purchased"
                        />
                          <Input
                            id="purchase_age"
                            name={getFieldName('purchase_age')}
                            type="number"
                            value={getFieldValue('purchase_age')}
                            onChange={(e) => {
                              const fieldName = getFieldName('purchase_age');
                              const value = parseFloat(e.target.value);
                              handleInputChange(fieldName as keyof InputData, value);
                            }}
                            onBlur={(e) => {
                              const fieldName = getFieldName('purchase_age');
                              const value = parseFloat(e.target.value);
                              const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                              handleInputChange(fieldName as keyof InputData, clampedValue);
                            }}
                            min={tempProperty.starting_age}
                            max={tempProperty.ending_age}
                            disabled={readOnly}
                            className={inputClassName}
                          />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="payment_sources"
                          label="Payment Sources"
                          tooltipText="Sources of funds for the property deposit"
                        />
                      </div>
                    </div>

                    {/* Row 2: Source Amount Inputs */}
                    <div className="space-y-3">
                      <div className="space-y-3">
                        {/* Standard sources */}
                        {['savings', 'main_kiwisaver', 'partner_kiwisaver', 'gifting', 'other'].map(source => {
                          const sourcesFieldName = getFieldName('deposit_sources');
                          const depositSources = tempProperty[sourcesFieldName as keyof InputData] as Record<string, number> || {
                            savings: 0,
                            investments: 0,
                            main_kiwisaver: 0,
                            partner_kiwisaver: 0,
                            gifting: 0,
                            other: 0
                          };

                          return (
                            <div key={source} className="grid grid-cols-2 gap-4 items-center">
                              <Label htmlFor={`amount-${source}`} className="capitalize">
                                {source === 'main_kiwisaver' ? `${mainName} KiwiSaver` :
                                 source === 'partner_kiwisaver' ? `${partnerName} KiwiSaver` :
                                 `${source.charAt(0).toUpperCase() + source.slice(1)}`} Amount
                              </Label>
                              <div className="relative">
                                {(() => {
                                  const purchaseAgeField = getFieldName('purchase_age');
                                  const purchaseAge = tempProperty[purchaseAgeField as keyof InputData] as number || 0;
                                  let availableFunds = 0;
                                  let showWarning = false;

                                  // Only validate for savings, investments, and KiwiSaver sources
                                  if (source === 'savings' || source === 'main_kiwisaver' || source === 'partner_kiwisaver') {
                                    // Use our getFundValueAtAge function to get the available funds at purchase age from the financial metrics
                                    availableFunds = getFundValueAtAge(source as 'savings' | 'investments' | 'main_kiwisaver' | 'partner_kiwisaver', purchaseAge);
                                    showWarning = depositSources[source] > availableFunds;
                                  } else if (source === 'investments') {
                                    // For investments, we need to check if we have specific investment funds
                                    if (property.withdrawal_priorities?.length) {
                                      // Sum up all investment funds
                                      availableFunds = property.withdrawal_priorities.reduce((sum, fundNumber) => {
                                        const fundValue = getFundValueAtAge(`investment_fund${fundNumber}` as any, purchaseAge);
                                        return sum + fundValue;
                                      }, 0);
                                      showWarning = depositSources[source] > availableFunds;
                                    } else {
                                      // Fall back to total investments
                                      availableFunds = getFundValueAtAge('investments', purchaseAge);
                                      showWarning = depositSources[source] > availableFunds;
                                    }
                                  }

                                  return (
                                    <>
                                      <Input
                                        id={`amount-${source}`}
                                        type="number"
                                        value={depositSources[source] || 0}
                                        className={cn(
                                          inputClassName,
                                          showWarning ? "border-red-500" : ""
                                        )}
                                        title={showWarning ? `Warning: Exceeds estimated available ${source === 'main_kiwisaver' ? `${mainName} KiwiSaver` : source === 'partner_kiwisaver' ? `${partnerName} KiwiSaver` : source.charAt(0).toUpperCase() + source.slice(1)} at age ${purchaseAge} ($${Math.round(availableFunds).toLocaleString()})` : ``}
                                        onChange={(e) => {
                                          const sourcesField = getFieldName('deposit_sources');
                                          const currentSources = {...(tempProperty[sourcesField as keyof InputData] as Record<string, number> || {})};
                                          const value = parseFloat(e.target.value) || 0;

                                          // Get purchase age to check available funds
                                          const purchaseAgeField = getFieldName('purchase_age');
                                          const purchaseAge = tempProperty[purchaseAgeField as keyof InputData] as number || 0;

                                          // Validate against available funds at purchase age
                                          let validatedValue = value;

                                          // Check if the source is one that needs validation
                                          if (source === 'savings' || source === 'investments' || source === 'main_kiwisaver' || source === 'partner_kiwisaver') {
                                            // Get the available funds at purchase age
                                            // In a real implementation, this would be calculated based on the financial model
                                            // For now, we'll use some simple validation logic

                                            let availableFunds = 0;

                                            if (source === 'savings' || source === 'main_kiwisaver' || source === 'partner_kiwisaver') {
                                              // Use our getFundValueAtAge function to get the available funds at purchase age from the financial metrics
                                              availableFunds = getFundValueAtAge(source as 'savings' | 'investments' | 'main_kiwisaver' | 'partner_kiwisaver', purchaseAge);
                                              // Fund name is determined later in the validation
                                            } else if (source === 'investments') {
                                              // For investments, we need to check if we have specific investment funds
                                              if (property.withdrawal_priorities?.length) {
                                                // Sum up all investment funds
                                                availableFunds = property.withdrawal_priorities.reduce((sum, fundNumber) => {
                                                  const fundValue = getFundValueAtAge(`investment_fund${fundNumber}` as any, purchaseAge);
                                                  return sum + fundValue;
                                                }, 0);
                                              } else {
                                                // Fall back to total investments
                                                availableFunds = getFundValueAtAge('investments', purchaseAge);
                                              }
                                            }

                                            // Validate the input value against available funds
                                            if (value > availableFunds) {
                                              // Get proper fund name for display
                                              let fundName = source;
                                              if (source === 'main_kiwisaver') {
                                                fundName = `${mainName} KiwiSaver`;
                                              } else if (source === 'partner_kiwisaver') {
                                                fundName = `${partnerName} KiwiSaver`;
                                              } else {
                                                fundName = source.charAt(0).toUpperCase() + source.slice(1);
                                              }

                                              console.warn(`Warning: The ${fundName} amount exceeds the estimated available funds at age ${purchaseAge}. Available: $${Math.round(availableFunds).toLocaleString()}, Requested: $${Math.round(value).toLocaleString()}`);

                                              // You could also limit the value to the available funds
                                              // validatedValue = availableFunds;
                                            }
                                          }

                                          currentSources[source] = validatedValue;

                                          // Calculate total deposit amount
                                          const totalDeposit = Object.values(currentSources).reduce((sum, val) => sum + (val || 0), 0);
                                          const depositField = getFieldName('deposit_amount');

                                          setTempProperty(prev => ({
                                            ...prev,
                                            [sourcesField]: currentSources,
                                            [depositField]: totalDeposit
                                          }));
                                        }}
                                        disabled={readOnly}
                                      />
                                      {showWarning && (
                                        <div className="text-xs text-red-500 mt-1">
                                          Exceeds available {source === 'main_kiwisaver' ? `${mainName} KiwiSaver` :
                                           source === 'partner_kiwisaver' ? `${partnerName} KiwiSaver` :
                                           source.charAt(0).toUpperCase() + source.slice(1)} funds: ${Math.round(availableFunds).toLocaleString()}
                                        </div>
                                      )}
                                    </>
                                  );
                                })()}
                              </div>
                            </div>
                          );
                        })}

                        {/* Investment funds section */}
                        {property.withdrawal_priorities && property.withdrawal_priorities.length > 0 && (
                          <>
                            <div className="pt-3 pb-2 border-t">
                              <h4 className="font-medium">Investment Funds</h4>
                            </div>
                            {property.withdrawal_priorities.map(fundNumber => {
                              const fundKey = `fund${fundNumber}`;
                              const sourcesFieldName = getFieldName('deposit_sources');
                              const depositSources = tempProperty[sourcesFieldName as keyof InputData] as Record<string, number> || {};
                              const fundDescription = property[`investment_description${fundNumber}` as keyof InputData] || `Investment Fund ${fundNumber}`;

                              return (
                                <div key={fundKey} className="grid grid-cols-2 gap-4 items-center">
                                  <Label htmlFor={`amount-${fundKey}`} className="capitalize">
                                    {fundDescription}
                                  </Label>
                                  <div className="relative">
                                    {(() => {
                                      const purchaseAgeField = getFieldName('purchase_age');
                                      const purchaseAge = tempProperty[purchaseAgeField as keyof InputData] as number || 0;
                                      let availableFunds = 0;

                                      // Get available funds for this specific investment fund
                                      availableFunds = getFundValueAtAge(`investment_fund${fundNumber}` as any, purchaseAge);

                                      const showWarning = (depositSources[fundKey] || 0) > availableFunds;

                                      return (
                                        <>
                                          <Input
                                            id={`amount-${fundKey}`}
                                            type="number"
                                            value={depositSources[fundKey] || 0}
                                            className={cn(
                                              inputClassName,
                                              showWarning ? "border-red-500" : ""
                                            )}
                                            title={showWarning ? `Warning: Exceeds estimated available ${fundDescription} at age ${purchaseAge} ($${Math.round(availableFunds).toLocaleString()})` : ``}
                                            onChange={(e) => {
                                              const sourcesField = getFieldName('deposit_sources');
                                              // Create a new object with default values if needed
                                              let currentSources: Record<string, number>;
                                              const existingSources = tempProperty[sourcesField as keyof InputData] as Record<string, number>;

                                              if (typeof existingSources !== 'object' || existingSources === null) {
                                                currentSources = {
                                                  savings: 0,
                                                  investments: 0,
                                                  main_kiwisaver: 0,
                                                  partner_kiwisaver: 0,
                                                  gifting: 0,
                                                  other: 0
                                                };
                                              } else {
                                                currentSources = {...existingSources};
                                              }

                                              const value = parseFloat(e.target.value) || 0;

                                              currentSources[fundKey] = value;

                                              // Calculate total deposit amount
                                              const totalDeposit = Object.values(currentSources).reduce((sum, val) => sum + (val || 0), 0);
                                              const depositField = getFieldName('deposit_amount');

                                              setTempProperty(prev => ({
                                                ...prev,
                                                [sourcesField]: currentSources,
                                                [depositField]: totalDeposit
                                              }));
                                            }}
                                            disabled={readOnly}
                                          />
                                          {showWarning && (
                                            <div className="text-xs text-red-500 mt-1">
                                              Exceeds available {fundDescription} funds: ${Math.round(availableFunds).toLocaleString()}
                                            </div>
                                          )}
                                        </>
                                      );
                                    })()}
                                  </div>
                                </div>
                              );
                            })}
                          </>
                        )}

                            <div className="grid grid-cols-2 gap-4 items-center pt-2 border-t">
                              <Label htmlFor="deposit_amount" className="font-semibold">
                                Total Deposit
                              </Label>
                              <Input
                                id="deposit_amount"
                                name={getFieldName('deposit_amount')}
                                type="number"
                                value={getFieldValue('deposit_amount')}
                                onChange={(e) => {
                                  const fieldName = getFieldName('deposit_amount');
                                  handleInputChange(fieldName as keyof InputData, e.target.value);
                                }}
                                disabled={true}
                                className={cn(inputClassName, "bg-muted")}
                              />
                            </div>
                          </div>
                    </div>
                  </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Sell/Downsize Tab */}
            <TabsContent value="sell" className="space-y-4 pt-4">
              {/* Sell Section */}
              <div className="flex items-center space-x-2 mb-4">
                <Checkbox
                  id="sell_main_property"
                  checked={tempProperty.sell_main_property === true}
                  onCheckedChange={(checked) => {
                    handleCheckboxChange('sell_main_property', checked === true);
                    if (checked && !tempProperty.property_index) {
                      // Clear downsizing fields when sell is selected (for main property only)
                      setTempProperty(prev => ({
                        ...prev,
                        downsize_age: undefined,
                        new_property_value: undefined
                      }));
                    }
                  }}
                  disabled={readOnly}
                />
                <LabelWithTooltip
                  htmlFor="sell_main_property"
                  label="Sell Property"
                  tooltipText="Option to sell property at a specified age"
                />
              </div>

              {tempProperty.sell_main_property && (
                <Card>
                  <CardHeader>
                    <CardTitle>Sale Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <LabelWithTooltip
                            htmlFor="main_property_sale_age"
                            label="Sale Age"
                            tooltipText="Age at which you plan to sell the property"
                          />
                              <Input
                                id="main_property_sale_age"
                                name="main_property_sale_age"
                                type="number"
                                value={tempProperty.main_property_sale_age}
                                onChange={(e) => {
                                  const value = parseFloat(e.target.value);
                                  handleInputChange('main_property_sale_age', value);
                                }}
                                onBlur={(e) => {
                                  const value = parseFloat(e.target.value);
                                  const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                                  handleInputChange('main_property_sale_age', clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor="main_prop_sale_value"
                            label="Sale Value"
                            tooltipText="Expected sale price of the property (calculated based on growth rate)"
                          />
                          <Input
                            id="main_prop_sale_value"
                            name="main_prop_sale_value"
                            type="number"
                            value={tempProperty.main_prop_sale_value}
                            onChange={(e) => handleInputChange('main_prop_sale_value', e.target.value)}
                            disabled={readOnly}
                            className={inputClassName}
                          />
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="pay_off_debt"
                            checked={tempProperty.pay_off_debt === true}
                            onCheckedChange={(checked) => handleCheckboxChange('pay_off_debt', checked === true)}
                            disabled={readOnly}
                          />
                          <LabelWithTooltip
                            htmlFor="pay_off_debt"
                            label="Pay Off Debt"
                            tooltipText="Use proceeds from the property sale to pay off the mortgage"
                          />
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="sale_allocate_to_investment"
                              checked={tempProperty.sale_allocate_to_investment === true}
                              onCheckedChange={(checked) => {
                                handleCheckboxChange('sale_allocate_to_investment', checked === true);
                                // If checked, set the default investment fund
                                if (checked) {
                                  const fundNumber = property.withdrawal_priorities?.[0] || 1;
                                  setSelectedInvestmentFund(fundNumber);
                                  setTempProperty(prev => ({
                                    ...prev,
                                    sale_investment_fund: String(fundNumber)
                                  }));
                                }
                              }}
                              disabled={readOnly}
                            />
                            <LabelWithTooltip
                              htmlFor="sale_allocate_to_investment"
                              label="Allocate to Investments"
                              tooltipText="Invest the proceeds from the property sale"
                            />
                          </div>

                          {tempProperty.sale_allocate_to_investment && (
                            <div className="pl-6">
                              <Label htmlFor="sale_investment_fund" className="text-sm mb-1 block">
                                Select Investment Fund
                              </Label>
                              <Select
                                value={String(tempProperty.sale_investment_fund || selectedInvestmentFund)}
                                onValueChange={(value) => {
                                  const fundNumber = parseInt(value);
                                  setSelectedInvestmentFund(fundNumber);
                                  setTempProperty(prev => ({
                                    ...prev,
                                    sale_investment_fund: String(fundNumber)
                                  }));
                                }}
                                disabled={readOnly}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select fund" />
                                </SelectTrigger>
                                <SelectContent>
                                  {property.withdrawal_priorities?.map((fundNumber) => (
                                    <SelectItem key={fundNumber} value={String(fundNumber)}>
                                      Fund {fundNumber}: {property[`investment_description${fundNumber}` as keyof InputData] || `Investment Fund ${fundNumber}`}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Downsize Section - Only for Main Property */}
              {!tempProperty.property_index && (
                <>
                  <div className="flex items-center space-x-2 mt-6 mb-4">
                    <Checkbox
                      id="downsize"
                      checked={tempProperty.downsize_age !== undefined}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          handleInputChange('downsize_age', tempProperty.starting_age || 65);
                          // Clear sell property when downsize is selected
                          handleCheckboxChange('sell_main_property', false);
                        } else {
                          // Clear downsizing fields
                          setTempProperty(prev => ({
                            ...prev,
                            downsize_age: undefined,
                            new_property_value: undefined
                          }));
                        }
                      }}
                      disabled={readOnly}
                    />
                    <LabelWithTooltip
                      htmlFor="downsize"
                      label="Downsize Property"
                      tooltipText="Option to move to a less expensive property"
                    />
                  </div>

                  {tempProperty.downsize_age !== undefined && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Downsize Details</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-4">
                            <div>
                              <LabelWithTooltip
                                htmlFor="downsize_age"
                                label="Downsize Age"
                                tooltipText="Age at which you plan to downsize"
                              />
                              <Input
                                id="downsize_age"
                                name="downsize_age"
                                type="number"
                                value={tempProperty.downsize_age}
                                onChange={(e) => {
                                  const value = parseFloat(e.target.value);
                                  handleInputChange('downsize_age', value);
                                }}
                                onBlur={(e) => {
                                  const value = parseFloat(e.target.value);
                                  const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                                  handleInputChange('downsize_age', clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="pay_off_debt"
                                checked={tempProperty.pay_off_debt}
                                onCheckedChange={(checked) => handleCheckboxChange('pay_off_debt', checked === true)}
                                disabled={readOnly}
                              />
                              <LabelWithTooltip
                                htmlFor="pay_off_debt"
                                label="Pay Off Debt"
                                tooltipText="Use excess funds from downsizing to clear remaining mortgage"
                              />
                            </div>
                          </div>
                          <div className="space-y-4">
                            <div>
                              <LabelWithTooltip
                                htmlFor="new_property_value"
                                label="New Property Value"
                                tooltipText="Expected purchase price of the smaller property"
                              />
                              <Input
                                id="main_property_sale_age"
                                name="main_property_sale_age"
                                type="number"
                                value={tempProperty.main_property_sale_age}
                                onChange={(e) => {
                                  const value = parseFloat(e.target.value);
                                  if (value >= tempProperty.starting_age && value <= tempProperty.ending_age) {
                                    handleInputChange('main_property_sale_age', value);
                                  }
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="allocate_to_investment"
                                  checked={tempProperty.allocate_to_investment}
                                  onCheckedChange={(checked) => {
                                    handleCheckboxChange('allocate_to_investment', checked === true);
                                    // If checked, set the default investment fund
                                    if (checked) {
                                      const fundNumber = property.withdrawal_priorities?.[0] || 1;
                                      setSelectedInvestmentFund(fundNumber);
                                      setTempProperty(prev => ({
                                        ...prev,
                                        downsize_investment_fund: fundNumber
                                      }));
                                    }
                                  }}
                                  disabled={readOnly}
                                />
                                <LabelWithTooltip
                                  htmlFor="allocate_to_investment"
                                  label="Allocate to Investments"
                                  tooltipText="Invest the excess funds from downsizing"
                                />
                              </div>

                              {tempProperty.allocate_to_investment && (
                                <div className="pl-6">
                                  <Label htmlFor="downsize_investment_fund" className="text-sm mb-1 block">
                                    Select Investment Fund
                                  </Label>
                                  <Select
                                    value={String((tempProperty as any).downsize_investment_fund || selectedInvestmentFund)}
                                    onValueChange={(value) => {
                                      const fundNumber = parseInt(value);
                                      setSelectedInvestmentFund(fundNumber);
                                      setTempProperty(prev => ({
                                        ...prev,
                                        downsize_investment_fund: String(fundNumber)
                                      } as any));
                                    }}
                                    disabled={readOnly}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="Select fund" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {property.withdrawal_priorities?.map((fundNumber) => (
                                        <SelectItem key={fundNumber} value={String(fundNumber)}>
                                          Fund {fundNumber}: {property[`investment_description${fundNumber}` as keyof InputData] || `Investment Fund ${fundNumber}`}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </>
              )}
            </TabsContent>

            {/* Repayments Tab */}
            <TabsContent value="repayments" className="space-y-4 pt-4">
              {/* Interest Only Period Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Interest Only Period</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="interest_only_period"
                        checked={getFieldValue('interest_only_period') === true}
                        onCheckedChange={(checked) => {
                          const fieldName = getFieldName('interest_only_period');
                          handleCheckboxChange(fieldName as keyof InputData, checked === true);

                          // Get the proper field names for start and end age based on property index
                          const startAgeFieldName = getFieldName('interest_only_start_age');
                          const endAgeFieldName = getFieldName('interest_only_end_age');

                          if (!checked) {
                            // Clear interest only period fields when unchecked
                            setTempProperty(prev => ({
                              ...prev,
                              [startAgeFieldName]: undefined,
                              [endAgeFieldName]: undefined
                            }));
                          } else {
                            // Set default values when checked
                            const startingAgeField = tempProperty.property_index && tempProperty.property_index > 1
                              ? `starting_age${tempProperty.property_index}` as keyof InputData
                              : 'starting_age';

                            const startingAge = tempProperty[startingAgeField] as number || 30;

                            setTempProperty(prev => ({
                              ...prev,
                              [startAgeFieldName]: startingAge,
                              [endAgeFieldName]: startingAge + 5
                            }));
                          }
                        }}
                        disabled={readOnly}
                      />
                      <LabelWithTooltip
                        htmlFor="interest_only_period"
                        label="Interest Only Period"
                        tooltipText="Enable an interest-only period where only interest is paid on the loan"
                      />
                    </div>

                    {getFieldValue('interest_only_period') && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <LabelWithTooltip
                            htmlFor="interest_only_start_age"
                            label="Start Age"
                            tooltipText="Age at which to start the interest-only period"
                          />
                          <Input
                            id="interest_only_start_age"
                            name={getFieldName('interest_only_start_age')}
                            type="number"
                            value={getFieldValue('interest_only_start_age')}
                            onChange={(e) => {
                              const fieldName = getFieldName('interest_only_start_age');
                              handleInputChange(fieldName as keyof InputData, e.target.value);
                            }}
                            disabled={readOnly}
                            className={inputClassName}
                          />
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor="interest_only_end_age"
                            label="End Age"
                            tooltipText="Age at which to end the interest-only period and resume principal payments"
                          />
                          <Input
                            id="interest_only_end_age"
                            name={getFieldName('interest_only_end_age')}
                            type="number"
                            value={getFieldValue('interest_only_end_age')}
                            onChange={(e) => {
                              const fieldName = getFieldName('interest_only_end_age');
                              handleInputChange(fieldName as keyof InputData, e.target.value);
                            }}
                            disabled={readOnly}
                            className={inputClassName}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Lump Sum Payment Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Lump Sum Payment</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="lump_sum_payment"
                          label="Lump Sum Payment"
                          tooltipText="One-time payment towards the mortgage"
                        />
                        <Input
                          id="lump_sum_payment"
                          name={getFieldName('lump_sum_payment_amount')}
                          type="number"
                          value={getFieldValue('lump_sum_payment_amount')}
                          onChange={(e) => {
                            const fieldName = getFieldName('lump_sum_payment_amount');
                            handleInputChange(fieldName as keyof InputData, e.target.value);
                          }}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="lump_sum_payment_age"
                          label="Payment Age"
                          tooltipText="Age at which you plan to make the lump sum payment"
                        />
                              <Input
                                id="lump_sum_payment_age"
                                name={getFieldName('lump_sum_payment_age')}
                                type="number"
                                value={getFieldValue('lump_sum_payment_age')}
                                onChange={(e) => {
                                  const fieldName = getFieldName('lump_sum_payment_age');
                                  const value = parseFloat(e.target.value);
                                  handleInputChange(fieldName as keyof InputData, value);
                                }}
                                onBlur={(e) => {
                                  const fieldName = getFieldName('lump_sum_payment_age');
                                  const value = parseFloat(e.target.value);
                                  const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                                  handleInputChange(fieldName as keyof InputData, clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="lump_sum_payment_source"
                          label="Payment Source"
                          tooltipText="Source of funds for the lump sum payment"
                        />
                        <RadioGroup
                          id="lump_sum_payment_source"
                          value={getFieldValue('lump_sum_payment_source') as string}
                          onValueChange={(value) => handleSourceChange(value as 'investments' | 'savings')}
                          disabled={readOnly}
                          className="mt-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="investments" id="investments" />
                            <Label htmlFor="investments">Investments</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="savings" id="savings" />
                            <Label htmlFor="savings">Savings</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Repayments Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Additional Repayments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <LabelWithTooltip
                        htmlFor="additional_debt_repayments"
                        label="Additional Monthly Repayments"
                        tooltipText="Extra monthly payments towards the mortgage"
                      />
                      <Input
                        id="additional_debt_repayments"
                        name="additional_debt_repayments"
                        type="number"
                        value={tempProperty.additional_debt_repayments}
                        onChange={(e) => handleInputChange('additional_debt_repayments', e.target.value)}
                        disabled={readOnly}
                        className={inputClassName}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="additional_debt_repayments_start_age"
                          label="Start Age"
                          tooltipText="Age at which to start making additional repayments"
                        />
                        <Input
                          id="additional_debt_repayments_start_age"
                          name={getFieldName('additional_debt_repayments_start_age')}
                          type="number"
                          value={getFieldValue('additional_debt_repayments_start_age') || tempProperty.starting_age || 0}
                          onChange={(e) => {
                            const fieldName = getFieldName('additional_debt_repayments_start_age');
                            const value = parseFloat(e.target.value);
                            handleInputChange(fieldName as keyof InputData, value);
                          }}
                          onBlur={(e) => {
                            const fieldName = getFieldName('additional_debt_repayments_start_age');
                            const value = parseFloat(e.target.value);
                            const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                            handleInputChange(fieldName as keyof InputData, clampedValue);
                          }}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="additional_debt_repayments_end_age"
                          label="End Age"
                          tooltipText="Age at which to stop making additional repayments"
                        />
                        <Input
                          id="additional_debt_repayments_end_age"
                          name={getFieldName('additional_debt_repayments_end_age')}
                          type="number"
                          value={getFieldValue('additional_debt_repayments_end_age') || tempProperty.ending_age || 0}
                          onChange={(e) => {
                            const fieldName = getFieldName('additional_debt_repayments_end_age');
                            const value = parseFloat(e.target.value);
                            handleInputChange(fieldName as keyof InputData, value);
                          }}
                          onBlur={(e) => {
                            const fieldName = getFieldName('additional_debt_repayments_end_age');
                            const value = parseFloat(e.target.value);
                            const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), tempProperty.ending_age);
                            handleInputChange(fieldName as keyof InputData, clampedValue);
                          }}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Rent/Board Tab */}
            <TabsContent value="rentboard" className="space-y-4 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Rental Income</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox
                      id="rental_income"
                      checked={getFieldValue('rental_income') === true}
                      onCheckedChange={(checked) => {
                        const fieldName = getFieldName('rental_income');
                        handleCheckboxChange(fieldName as keyof InputData, checked === true);

                        if (!checked) {
                          // Clear rental income fields when unchecked
                          const amountField = getFieldName('rental_amount');
                          const startAgeField = getFieldName('rental_start_age');
                          const endAgeField = getFieldName('rental_end_age');

                          setTempProperty(prev => ({
                            ...prev,
                            [amountField]: undefined,
                            [startAgeField]: undefined,
                            [endAgeField]: undefined
                          }));
                        } else {
                          // Set default values when checked
                          const startingAgeField = tempProperty.property_index && tempProperty.property_index > 1
                            ? `starting_age${tempProperty.property_index}` as keyof InputData
                            : 'starting_age';

                          const startingAge = tempProperty[startingAgeField] as number || 30;
                          const amountField = getFieldName('rental_amount');
                          const startAgeField = getFieldName('rental_start_age');
                          const endAgeField = getFieldName('rental_end_age');

                          setTempProperty(prev => ({
                            ...prev,
                            [amountField]: 10000,
                            [startAgeField]: startingAge,
                            [endAgeField]: startingAge + 20
                          }));
                        }
                      }}
                      disabled={readOnly}
                    />
                    <LabelWithTooltip
                      htmlFor="rental_income"
                      label="Receive Rental Income"
                      tooltipText="Enable to include rental income from this property"
                    />
                  </div>

                  {getFieldValue('rental_income') && (
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="rental_amount"
                          label="Annual Rental Amount"
                          tooltipText="Annual rental income received (taxable)"
                        />
                        <Input
                          id="rental_amount"
                          name={getFieldName('rental_amount')}
                          type="number"
                          value={getFieldValue('rental_amount')}
                          onChange={(e) => {
                            const fieldName = getFieldName('rental_amount');
                            handleInputChange(fieldName as keyof InputData, e.target.value);
                          }}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="rental_start_age"
                          label="Start Age"
                          tooltipText="Age when you start receiving rental income"
                        />
                              <Input
                                id="rental_start_age"
                                name={getFieldName('rental_start_age')}
                                type="number"
                                value={getFieldValue('rental_start_age') || tempProperty.starting_age}
                                onChange={(e) => {
                                  const fieldName = getFieldName('rental_start_age');
                                  const value = parseFloat(e.target.value);
                                  handleInputChange(fieldName as keyof InputData, value);
                                }}
                                onBlur={(e) => {
                                  const fieldName = getFieldName('rental_start_age');
                                  const value = parseFloat(e.target.value);
                                  const endAge = getFieldValue('rental_end_age') || tempProperty.ending_age;
                                  const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), Math.min(tempProperty.ending_age, endAge));
                                  handleInputChange(fieldName as keyof InputData, clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="rental_end_age"
                          label="End Age"
                          tooltipText="Age when you stop receiving rental income"
                        />
                              <Input
                                id="rental_end_age"
                                name={getFieldName('rental_end_age')}
                                type="number"
                                value={getFieldValue('rental_end_age') || tempProperty.ending_age}
                                onChange={(e) => {
                                  const fieldName = getFieldName('rental_end_age');
                                  const value = parseFloat(e.target.value);
                                  handleInputChange(fieldName as keyof InputData, value);
                                }}
                                onBlur={(e) => {
                                  const fieldName = getFieldName('rental_end_age');
                                  const value = parseFloat(e.target.value);
                                  const startAge = getFieldValue('rental_start_age') || tempProperty.starting_age;
                                  const clampedValue = Math.min(Math.max(value, Math.max(tempProperty.starting_age, startAge)), tempProperty.ending_age);
                                  handleInputChange(fieldName as keyof InputData, clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Board Income</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox
                      id="board_income"
                      checked={getFieldValue('board_income') === true}
                      onCheckedChange={(checked) => {
                        const fieldName = getFieldName('board_income');
                        handleCheckboxChange(fieldName as keyof InputData, checked === true);

                        if (!checked) {
                          // Clear board income fields when unchecked
                          const amountField = getFieldName('board_amount');
                          const startAgeField = getFieldName('board_start_age');
                          const endAgeField = getFieldName('board_end_age');

                          setTempProperty(prev => ({
                            ...prev,
                            [amountField]: undefined,
                            [startAgeField]: undefined,
                            [endAgeField]: undefined
                          }));
                        } else {
                          // Set default values when checked
                          const startingAgeField = tempProperty.property_index && tempProperty.property_index > 1
                            ? `starting_age${tempProperty.property_index}` as keyof InputData
                            : 'starting_age';

                          const startingAge = tempProperty[startingAgeField] as number || 30;
                          const amountField = getFieldName('board_amount');
                          const startAgeField = getFieldName('board_start_age');
                          const endAgeField = getFieldName('board_end_age');

                          setTempProperty(prev => ({
                            ...prev,
                            [amountField]: 5000,
                            [startAgeField]: startingAge,
                            [endAgeField]: startingAge + 20
                          }));
                        }
                      }}
                      disabled={readOnly}
                    />
                    <LabelWithTooltip
                      htmlFor="board_income"
                      label="Receive Board Income"
                      tooltipText="Enable to include board income from this property (non-taxable)"
                    />
                  </div>

                  {getFieldValue('board_income') && (
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="board_amount"
                          label="Annual Board Amount"
                          tooltipText="Annual board income received (non-taxable)"
                        />
                        <Input
                          id="board_amount"
                          name={getFieldName('board_amount')}
                          type="number"
                          value={getFieldValue('board_amount')}
                          onChange={(e) => {
                            const fieldName = getFieldName('board_amount');
                            handleInputChange(fieldName as keyof InputData, e.target.value);
                          }}
                          disabled={readOnly}
                          className={inputClassName}
                        />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="board_start_age"
                          label="Start Age"
                          tooltipText="Age when you start receiving board income"
                        />
                              <Input
                                id="board_start_age"
                                name={getFieldName('board_start_age')}
                                type="number"
                                value={getFieldValue('board_start_age') || tempProperty.starting_age}
                                onChange={(e) => {
                                  const fieldName = getFieldName('board_start_age');
                                  const value = parseFloat(e.target.value);
                                  handleInputChange(fieldName as keyof InputData, value);
                                }}
                                onBlur={(e) => {
                                  const fieldName = getFieldName('board_start_age');
                                  const value = parseFloat(e.target.value);
                                  const endAge = getFieldValue('board_end_age') || tempProperty.ending_age;
                                  const clampedValue = Math.min(Math.max(value, tempProperty.starting_age), Math.min(tempProperty.ending_age, endAge));
                                  handleInputChange(fieldName as keyof InputData, clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                      </div>
                      <div>
                        <LabelWithTooltip
                          htmlFor="board_end_age"
                          label="End Age"
                          tooltipText="Age when you stop receiving board income"
                        />
                              <Input
                                id="board_end_age"
                                name={getFieldName('board_end_age')}
                                type="number"
                                value={getFieldValue('board_end_age') || tempProperty.ending_age}
                                onChange={(e) => {
                                  const fieldName = getFieldName('board_end_age');
                                  const value = parseFloat(e.target.value);
                                  handleInputChange(fieldName as keyof InputData, value);
                                }}
                                onBlur={(e) => {
                                  const fieldName = getFieldName('board_end_age');
                                  const value = parseFloat(e.target.value);
                                  const startAge = getFieldValue('board_start_age') || tempProperty.starting_age;
                                  const clampedValue = Math.min(Math.max(value, Math.max(tempProperty.starting_age, startAge)), tempProperty.ending_age);
                                  handleInputChange(fieldName as keyof InputData, clampedValue);
                                }}
                                min={tempProperty.starting_age}
                                max={tempProperty.ending_age}
                                disabled={readOnly}
                                className={inputClassName}
                              />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>Cancel</Button>
          <Button onClick={handleUpdate} disabled={readOnly}>Update</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
