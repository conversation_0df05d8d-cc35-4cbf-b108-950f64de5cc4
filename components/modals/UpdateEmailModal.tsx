'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { updateEmailAction } from '@/app/actions';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';

interface UpdateEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentEmail: string;
  onEmailUpdate?: (newEmail: string) => void;
}

export default function UpdateEmailModal({ isOpen, onClose, currentEmail, onEmailUpdate }: UpdateEmailModalProps) {
  const [newEmail, setNewEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const router = useRouter();
  const supabase = createClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationError('');
    setSuccessMessage('');

    // Client-side validation
    if (!newEmail || !currentPassword) {
      setValidationError('All fields are required');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      setValidationError('Please enter a valid email address');
      return;
    }

    // Check if new email is the same as current email
    if (newEmail === currentEmail) {
      setValidationError('New email must be different from current email');
      return;
    }

    setIsSubmitting(true);

    try {
      // First verify the current password by attempting to update the user
      const { error } = await supabase.auth.updateUser(
        { email: newEmail },
        { password: currentPassword } as any
      );

      if (error) {
        // Handle specific error types
        if (error.message.includes('sending email')) {
          // Email service error
          setValidationError(
            'There was an issue with the email service. Your password is correct, but we could not send the verification email. ' +
            'Please try again later or contact support.'
          );
        } else if (error.message.includes('password')) {
          // Password error
          setValidationError('The password you entered is incorrect. Please try again.');
        } else {
          // Other errors
          setValidationError(error.message || 'Failed to update email. Please try again.');
        }
        return;
      }

      // If we get here, the update was successful
      setSuccessMessage('Email update initiated. Please check your new email for a confirmation link.');

      // Update parent component state if callback provided
      // Note: The email won't actually change until the user confirms via email
      // but we can update the UI to show the pending change
      if (onEmailUpdate) {
        onEmailUpdate(newEmail);
      }

      // Reset form
      setNewEmail('');
      setCurrentPassword('');

      // Close modal after a delay to show success message
      setTimeout(() => {
        onClose();
        // Refresh the page to ensure server state is updated
        router.refresh();
      }, 3000);
    } catch (error: any) {
      console.error('Error updating email:', error);

      // Handle unexpected errors
      if (error.message?.includes('sending email')) {
        setValidationError(
          'There was an issue with the email service. Your password is correct, but we could not send the verification email. ' +
          'Please try again later or contact support.'
        );
      } else {
        setValidationError(error.message || 'An unexpected error occurred. Please try again later.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state
    setNewEmail('');
    setCurrentPassword('');
    setValidationError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Email Address</DialogTitle>
          <DialogDescription>
            Enter your password and new email address. You'll need to verify the new email.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          {validationError && (
            <div className="text-sm font-medium text-destructive">{validationError}</div>
          )}

          {successMessage && (
            <div className="text-sm font-medium text-green-600 bg-green-50 p-2 rounded border border-green-200">
              {successMessage}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="currentEmail">Current Email</Label>
            <Input
              id="currentEmail"
              type="email"
              value={currentEmail}
              disabled
              className="bg-muted"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="newEmail">New Email</Label>
            <Input
              id="newEmail"
              type="email"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              placeholder="Enter your new email address"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="currentPassword">Current Password</Label>
            <Input
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              placeholder="Enter your current password"
              required
            />
          </div>

          <DialogFooter className="pt-4">
            <Button variant="outline" type="button" onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Email'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
