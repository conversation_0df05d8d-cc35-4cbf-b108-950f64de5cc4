import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Trash2, ExternalLink, Plus, Pencil } from "lucide-react";
import { createClient } from "@/utils/supabase/client";

interface ActionItemsModalProps {
  isOpen: boolean;
  onClose: () => void;
  actionItems: string[];
  onSaveActionItems: (completedItems: string[], removedItems?: string[], newItems?: string[], editedItems?: {original: string, updated: string}[]) => void;
  householdId?: number;
  noteId?: string;
}

interface TaskLink {
  actionItem: string;
  taskId: number;
  taskTitle: string;
}

interface TaskNoteLink {
  task_id: number;
  tasks: {
    id: number;
    title: string;
  } | {
    id: number;
    title: string;
  }[];
}

interface TaskWithMetadata {
  id: number;
  title: string;
  metadata?: {
    actionItem?: string;
    [key: string]: any;
  };
}

const ActionItemsModal = ({
  isOpen,
  onClose,
  actionItems,
  onSaveActionItems,
  householdId,
  noteId
}: ActionItemsModalProps) => {
  const [completedItems, setCompletedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isCreatingTasks, setIsCreatingTasks] = useState(false);
  const [removedItems, setRemovedItems] = useState<string[]>([]);
  const [taskLinks, setTaskLinks] = useState<TaskLink[]>([]);
  const [newActionItem, setNewActionItem] = useState<string>("");
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [editingItem, setEditingItem] = useState<{index: number, value: string} | null>(null);
  const [editedItems, setEditedItems] = useState<{original: string, updated: string}[]>([]);
  const supabase = createClient();

  useEffect(() => {
    if (isOpen && noteId) {
      fetchTaskLinks();
    }
  }, [isOpen, noteId]);

  const fetchTaskLinks = async () => {
    if (!noteId) return;

    try {
      const { data, error } = await supabase
        .from('task_note_links')
        .select(`
          task_id,
          tasks:tasks(id, title)
        `)
        .eq('note_id', noteId);

      if (error) throw error;

      // Get task IDs to fetch metadata separately
      const taskIds = data.map((link: TaskNoteLink) => {
        if (link.tasks) {
          return Array.isArray(link.tasks) ? link.tasks[0]?.id : link.tasks?.id;
        }
        return null;
      }).filter(Boolean) as number[];

      // Fetch tasks with metadata
      const { data: tasksWithMetadata, error: metadataError } = await supabase
        .from('tasks')
        .select('id, title, metadata')
        .in('id', taskIds) as { data: TaskWithMetadata[], error: any };

      if (metadataError) throw metadataError;

      // Map tasks with their metadata
      const links: TaskLink[] = [];
      data.forEach((link: TaskNoteLink) => {
        if (link.tasks) {
          const task = Array.isArray(link.tasks) ? link.tasks[0] : link.tasks;
          if (task) {
            const taskWithMetadata = tasksWithMetadata.find(t => t.id === task.id);
            if (taskWithMetadata?.metadata?.actionItem) {
              links.push({
                actionItem: taskWithMetadata.metadata.actionItem,
                taskId: task.id,
                taskTitle: task.title
              });
            }
          }
        }
      });

      setTaskLinks(links);
    } catch (error) {
      console.error('Error fetching task links:', error);
    }
  };

  const handleToggleItem = (item: string) => {
    setCompletedItems(prev =>
      prev.includes(item)
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  const handleToggleSelectItem = (item: string) => {
    setSelectedItems(prev =>
      prev.includes(item)
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  const handleRemoveItem = (item: string) => {
    setRemovedItems(prev => [...prev, item]);
  };

  const handleSave = () => {
    // Pass completed, removed, and edited items back to parent
    onSaveActionItems(completedItems, removedItems,
      actionItems.filter(item => !originalActionItems.includes(item)),
      editedItems);
    onClose();
  };

  const handleCreateTasks = async () => {
    if (selectedItems.length === 0) {
      toast.error("Please select at least one action item");
      return;
    }

    setIsCreatingTasks(true);
    try {
      const response = await fetch('/api/create-tasks-from-actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          actionItems: selectedItems,
          householdId,
          noteId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create tasks');
      }

      const data = await response.json();
      toast.success(`Created ${data.createdTasks} tasks successfully`);

      // Mark the created tasks as completed
      const newCompletedItems = [...completedItems, ...selectedItems];
      onSaveActionItems(newCompletedItems);

      // Refresh task links
      fetchTaskLinks();

      onClose();
    } catch (error) {
      console.error('Error creating tasks:', error);
      toast.error('Failed to create tasks');
    } finally {
      setIsCreatingTasks(false);
    }
  };

  const openTask = (taskId: number) => {
    window.open(`/protected/tasks?view=${taskId}`, '_blank');
  };

  const hasTaskLink = (item: string) => {
    return taskLinks.some(link => link.actionItem === item);
  };

  const getTaskLink = (item: string) => {
    return taskLinks.find(link => link.actionItem === item);
  };

  const handleSelectAll = () => {
    const availableItems = actionItems.filter(item => !removedItems.includes(item));

    if (selectedItems.length === availableItems.length) {
      // If all are selected, deselect all
      setSelectedItems([]);
    } else {
      // Otherwise, select all
      setSelectedItems(availableItems);
    }
  };

  const handleAddItem = () => {
    if (!newActionItem.trim()) {
      toast.error("Action item cannot be empty");
      return;
    }

    // Add the new item to the list
    actionItems.push(newActionItem);
    setNewActionItem("");
    setIsAddingItem(false);
    toast.success("Action item added");
  };

  const handleStartEdit = (index: number, item: string) => {
    setEditingItem({ index, value: item });
  };

  const handleSaveEdit = (originalItem: string) => {
    if (!editingItem) return;

    if (!editingItem.value.trim()) {
      toast.error("Action item cannot be empty");
      return;
    }

    // Update the item in the list
    actionItems[editingItem.index] = editingItem.value;

    // Track edited items for the parent component
    setEditedItems(prev => [
      ...prev,
      { original: originalItem, updated: editingItem.value }
    ]);

    setEditingItem(null);
    toast.success("Action item updated");
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
  };

  // Store original action items for comparison
  const [originalActionItems] = useState<string[]>([...actionItems]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Action Items</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {actionItems.length > 0 || isAddingItem ? (
            <>
              <div className="flex items-center justify-between ml-3 mb-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={selectedItems.length === actionItems.filter(item => !removedItems.includes(item)).length && selectedItems.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <label htmlFor="select-all" className="text-sm font-medium">
                    Select All
                  </label>
                </div>
              </div>

              <ul className="space-y-3">
                {actionItems.filter(item => !removedItems.includes(item)).map((item, index) => (
                  <li key={index} className="flex items-start space-x-3 p-3 bg-muted rounded-md">
                    {editingItem && editingItem.index === index ? (
                      <div className="flex-1 flex items-center space-x-2">
                        <Input
                          value={editingItem.value}
                          onChange={(e) => setEditingItem({...editingItem, value: e.target.value})}
                          className="flex-1"
                          autoFocus
                        />
                        <Button size="sm" onClick={() => handleSaveEdit(item)}>Save</Button>
                        <Button size="sm" variant="outline" onClick={handleCancelEdit}>Cancel</Button>
                      </div>
                    ) : (
                      <>
                        <Checkbox
                          id={`select-${index}`}
                          checked={selectedItems.includes(item)}
                          onCheckedChange={() => handleToggleSelectItem(item)}
                          className="mt-1"
                        />
                        <div className="flex-1 flex items-start justify-between">
                          <label
                            htmlFor={`item-${index}`}
                            className={`flex-1 ${completedItems.includes(item) ? 'line-through text-muted-foreground' : ''}`}
                          >
                            {item}
                          </label>
                          <div className="flex flex-col items-end space-y-2">
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleStartEdit(index, item)}
                                title="Edit item"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveItem(item)}
                                className="text-red-500 hover:text-red-700 hover:bg-red-100"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                            {hasTaskLink(item) && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openTask(getTaskLink(item)!.taskId)}
                                className="text-xs flex items-center gap-1"
                              >
                                Go to Task <ExternalLink className="h-3 w-3 ml-1" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </>
                    )}
                  </li>
                ))}
              </ul>

              {/* Moved the Add Item section below the action items list */}
              {isAddingItem ? (
                <div className="flex items-center space-x-2 p-3 bg-muted rounded-md mt-3">
                  <Input
                    value={newActionItem}
                    onChange={(e) => setNewActionItem(e.target.value)}
                    placeholder="Enter new action item"
                    className="flex-1"
                    autoFocus
                  />
                  <Button size="sm" onClick={handleAddItem}>Add</Button>
                  <Button size="sm" variant="outline" onClick={() => setIsAddingItem(false)}>Cancel</Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsAddingItem(true)}
                  className="flex items-center gap-1 mt-3 w-full"
                >
                  <Plus className="h-4 w-4" /> Add Item
                </Button>
              )}
            </>
          ) : (
            <div className="text-center space-y-4">
              <p className="text-muted-foreground">No action items found for this note.</p>
              <Button
                variant="outline"
                onClick={() => setIsAddingItem(true)}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" /> Add Action Item
              </Button>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <div className="flex gap-2">
            <Button
              variant="secondary"
              onClick={handleCreateTasks}
              disabled={isCreatingTasks || selectedItems.length === 0}
            >
              {isCreatingTasks ? "Creating..." : "Create Tasks"}
            </Button>
            <Button onClick={handleSave}>Save</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ActionItemsModal;
