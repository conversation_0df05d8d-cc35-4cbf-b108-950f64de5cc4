'use client';

import { useState, useEffect, SetStateAction } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from '@/components/ui/label';

interface Household {
  id: number;
  householdName: string;
}

interface UploadDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  preselectedHousehold?: { id: number; householdName: string };
}

export default function UploadDocumentsModal({
  isOpen,
  onClose,
  onSuccess,
  preselectedHousehold
}: UploadDocumentsModalProps) {
  const [households, setHouseholds] = useState<Household[]>([]);
  const [selectedHousehold, setSelectedHousehold] = useState<Household | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [documentName, setDocumentName] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    const fetchHouseholds = async () => {
      if (preselectedHousehold) {
        setHouseholds([preselectedHousehold]);
        setSelectedHousehold(preselectedHousehold);
        return;
      }

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('householdName');

      if (error) {
        console.error('Error fetching households:', error);
        return;
      }

      if (data) {
        setHouseholds(data);
      }
    };

    fetchHouseholds();
  }, [preselectedHousehold]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const validateFiles = (files: FileList | null): File[] => {
    if (!files) return [];
    
    const validFiles: File[] = [];
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (allowedTypes.includes(file.type)) {
        validFiles.push(file);
      } else {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a PDF or DOCX file`,
          variant: "destructive"
        });
      }
    }
    
    return validFiles;
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (!selectedHousehold) {
      toast({
        title: "Error",
        description: "Please select a household first",
        variant: "destructive"
      });
      return;
    }

    const files = validateFiles(e.dataTransfer.files);
    if (files.length === 0) return;

    await uploadFiles(files);
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedHousehold) {
      toast({
        title: "Error",
        description: "Please select a household first",
        variant: "destructive"
      });
      return;
    }

    const files = validateFiles(e.target.files);
    if (files.length === 0) return;

    await uploadFiles(files);
  };

  const uploadFiles = async (files: File[]) => {
    if (!selectedHousehold) {
      toast({
        title: "Error",
        description: "Please select a household first",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    const supabase = createClient();

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('No authenticated user found');
      }

      for (const file of files) {
        const fileType = file.name.split('.').pop()?.toLowerCase();
        
        // Validate file type
        if (!fileType || !['pdf', 'docx'].includes(fileType)) {
          toast({
            title: "Error",
            description: `Invalid file type for ${file.name}. Only PDF and DOCX files are allowed.`,
            variant: "destructive"
          });
          continue;
        }

        try {
          const timestamp = Date.now();
          const safeFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
          const filePath = `${selectedHousehold.id}/${timestamp}_${safeFileName}`;

          // Upload to Supabase Storage
          const { error: uploadError } = await supabase.storage
            .from('documents')
            .upload(filePath, file, {
              cacheControl: '3600',
              upsert: false
            });

          if (uploadError) throw uploadError;

          // Create document record in the database
          const { error: dbError } = await supabase
            .from('other_documents')
            .insert({
              household_id: selectedHousehold.id,
              file_name: file.name,
              name: documentName || file.name.split('.')[0],
              file_type: fileType === 'pdf' ? 'pdf' : 'docx',
              file_size: file.size,
              path: filePath,
              created_by: user.id
            });

          if (dbError) throw dbError;

          toast({
            title: "Success",
            description: `Successfully uploaded ${file.name}`
          });
        } catch (error) {
          console.error(`Error processing ${file.name}:`, error);
          toast({
            title: "Error",
            description: `Failed to process ${file.name}`,
            variant: "destructive"
          });
        }
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error uploading documents:', error);
      toast({
        title: "Error",
        description: "Failed to upload documents",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Input
              id="documentName"
              value={documentName}
              onChange={(e) => setDocumentName(e.target.value)}
              placeholder="Document Name (optional)"
              className="mb-4"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="household">Household</Label>
            {preselectedHousehold ? (
              <Input 
                id="household"
                value={preselectedHousehold.householdName}
                disabled
                className="bg-muted"
              />
            ) : (
              <Select
                value={selectedHousehold?.id.toString()}
                onValueChange={(value) => {
                  const household = households.find(h => h.id.toString() === value);
                  setSelectedHousehold(household || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a household" />
                </SelectTrigger>
                <SelectContent>
                  {households.map((household) => (
                    <SelectItem
                      key={household.id}
                      value={household.id.toString()}
                    >
                      {household.householdName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center ${
              dragActive ? 'border-primary bg-primary/10' : 'border-border'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              multiple
              accept=".pdf,.docx"
              className="hidden"
              id="file-upload"
              onChange={handleFileInput}
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer text-sm text-muted-foreground hover:text-foreground"
            >
              {isUploading ? (
                <div className="flex items-center justify-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Uploading...</span>
                </div>
              ) : (
                <>
                  <p>Drag and drop files here or click to select</p>
                  <p className="text-xs mt-1">(PDF, DOCX only)</p>
                </>
              )}
            </label>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
