import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { OptimizationSuggestion } from "@/app/utils/aiOptimizer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { useState } from "react";

interface OptimizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  suggestions: OptimizationSuggestion[];
  isOptimizing: boolean;
  onApplyOptimizations: (selectedSuggestions: OptimizationSuggestion[]) => void;
}

export function OptimizationModal({ 
  isOpen, 
  onClose, 
  suggestions, 
  isOptimizing,
  onApplyOptimizations 
}: OptimizationModalProps) {
  const [selectedSuggestions, setSelectedSuggestions] = useState<Set<number>>(new Set());

  const handleToggleSuggestion = (index: number) => {
    const newSelected = new Set(selectedSuggestions);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedSuggestions(newSelected);
  };

  const handleApplyOptimizations = () => {
    const selectedOptimizations = suggestions.filter((_, index) => 
      selectedSuggestions.has(index)
    );
    onApplyOptimizations(selectedOptimizations);
    onClose();
    setSelectedSuggestions(new Set()); // Reset selections
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Scenario Optimization Suggestions</DialogTitle>
        </DialogHeader>
        
        {isOptimizing ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="mt-2 text-sm text-muted-foreground">
              Analyzing scenario and generating suggestions...
            </p>
          </div>
        ) : suggestions.length > 0 ? (
          <>
            <div className="space-y-4">
              {suggestions
                .sort((a, b) => b.priority - a.priority)
                .map((suggestion, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Checkbox 
                        checked={selectedSuggestions.has(index)}
                        onCheckedChange={() => handleToggleSuggestion(index)}
                      />
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium">{suggestion.parameter}</h3>
                          <span className="text-sm px-2 py-1 bg-muted rounded-full">
                            Priority: {suggestion.priority}/5
                          </span>
                        </div>
                        <div className="text-sm space-y-2">
                          <p>
                            Current: <span className="font-mono">{suggestion.currentValue}</span>
                            {" → "}
                            Suggested: <span className="font-mono">{suggestion.suggestedValue}</span>
                          </p>
                          <p className="text-muted-foreground">{suggestion.impact}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
            <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
              <Button variant="outline" onClick={onClose}>Cancel</Button>
              <Button 
                onClick={handleApplyOptimizations}
                disabled={selectedSuggestions.size === 0}
              >
                Apply Selected Optimizations
              </Button>
            </div>
          </>
        ) : (
          <p className="text-center py-8 text-muted-foreground">
            No optimization suggestions available.
          </p>
        )}
      </DialogContent>
    </Dialog>
  );
}