import { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/expandingDialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { createClient } from '@/utils/supabase/client';
import { format, addDays } from 'date-fns';
import { useToast } from "@/hooks/use-toast";
import { Loader2, MessageSquare, Paperclip, Plus, X, MoreHorizontal, Pencil, Trash2, ChevronDown, ExternalLink } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, <PERSON><PERSON><PERSON><PERSON><PERSON>Header, AlertDialogTit<PERSON> } from "@/components/ui/alert-dialog";
import { useDropzone } from 'react-dropzone';
import { usePathname } from 'next/navigation';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { Badge } from '../ui/badge';
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import Toolbar from '../Toolbar';
import { Editor } from '@tiptap/react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { EditorContent } from '@tiptap/react';
import { Extension } from '@tiptap/core';

// Create a custom extension to handle the space key
const SpaceKeyHandler = Extension.create({
  name: 'spaceKeyHandler',
  
  addKeyboardShortcuts() {
    return {
      ' ': () => {
        this.editor.commands.insertContent(' ');
        return true;
      },
    };
  },
});

// Helper function to get badge variant based on interaction type
const getInteractionBadgeVariant = (type: string) => {
  switch (type) {
    case 'email':
      return 'blue';
    case 'phone':
      return 'green';
    case 'meeting':
      return 'purple';
    case 'note':
      return 'yellow';
    default:
      return 'outline';
  }
};

interface Attachment {
  id: number;
  interaction_id: number;
  name: string;
  url: string;
  type: string;
  size: number;
  path: string;
  created_at: string;
}

export interface Interaction { // Exporting for use in parent
  id: number;
  household_id: number;
  title: string;
  content: string;
  date: string;
  type: string;
  created_at: string;
  attachments?: Attachment[];
  comments?: {
    user_id: string;
    id: number;
    interaction_id: number;
    content: string;
    created_at: string;
  }[];
  linked_tasks?: {
    id: number;
    task_id: number;
    title: string;
    due_date: string;
    status: string;
  }[];
  interaction_task_links?: {
    id: number;
    task_id: number;
    interaction_id: number;
  }[];
}

interface Comment {
  id: number;
  interaction_id: number;
  content: string;
  created_at: string;
  user_id: string; // Add this field to match the database schema
}

interface User {
  id: string;
  name: string;
}

export type InteractionModalMode = 'new' | 'details' | 'edit' | 'delete'; // Exporting mode type

interface InteractionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdId?: number; // Make optional
  interactionToEdit?: Interaction | null; // For viewing/editing existing
  onInteractionSaved: () => void; // Callback to refresh table
  onInteractionDeleted: () => void; // Callback to refresh table
  mode: InteractionModalMode;
}

export default function InteractionsModal({
  isOpen,
  onClose,
  householdId,
  interactionToEdit,
  onInteractionSaved,
  onInteractionDeleted,
  mode: initialMode
}: InteractionsModalProps) {
  const pathname = usePathname();
  const [mode, setMode] = useState(initialMode);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [households, setHouseholds] = useState<{ id: number, householdName: string }[]>([]);
  const [selectedHouseholdId, setSelectedHouseholdId] = useState<number | undefined>(householdId);
  const [newInteraction, setNewInteraction] = useState({
    title: '',
    content: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    type: '',
  });
  const [editedInteraction, setEditedInteraction] = useState<Partial<Interaction> | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [availableTasks, setAvailableTasks] = useState<any[]>([]);
  const [selectedTaskIds, setSelectedTaskIds] = useState<number[]>([]);
  const [linkedTasks, setLinkedTasks] = useState<any[]>([]);
  const [contentEditor, setContentEditor] = useState<Editor | null>(null);
  const [commentInput, setCommentInput] = useState("");
  const [showUserSuggestions, setShowUserSuggestions] = useState(false);
  const [userSuggestions, setUserSuggestions] = useState<User[]>([]);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [taggedUsers, setTaggedUsers] = useState<{id: string, name: string}[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [profileData, setProfileData] = useState<{ user_id: string | null, org_id: string | null }>({
    user_id: null,
    org_id: null
  });
  const commentInputRef = useRef<HTMLTextAreaElement>(null);

  const supabase = createClient();
  const { toast } = useToast();
  
  // Determine if we're in the household context or global context
  const isInHouseholdContext = householdId !== undefined;

  // Fetch households for the dropdown
  useEffect(() => {
    if (!isInHouseholdContext) {
      const fetchHouseholds = async () => {
        const { data, error } = await supabase
          .from('households')
          .select('id, householdName')
          .order('householdName');
          
        if (error) {
          console.error('Error fetching households:', error);
          toast({
            title: "Error",
            description: "Failed to load households",
            variant: "destructive",
          });
          return;
        }
        
        setHouseholds(data || []);
      };
      
      fetchHouseholds();
    }
  }, [isInHouseholdContext]);

  useEffect(() => {
    setMode(initialMode); // Update mode when prop changes
    if (initialMode === 'new') {
      setNewInteraction({
        title: '',
        content: '',
        date: format(new Date(), 'yyyy-MM-dd'),
        type: '',
      });
      setSelectedFiles([]);
      setEditedInteraction(null);
    } else if (interactionToEdit) {
      // Ensure date is formatted correctly for the input type="date"
      const formattedDate = interactionToEdit.date ? format(new Date(interactionToEdit.date), 'yyyy-MM-dd') : '';
      setEditedInteraction({
        title: interactionToEdit.title,
        content: interactionToEdit.content,
        date: formattedDate, // Use formatted date
        type: interactionToEdit.type,
      });
      setSelectedFiles([]); // Reset files when opening details/edit
    } else {
      // Reset if interactionToEdit is null/undefined for non-new modes
      setEditedInteraction(null);
      setSelectedFiles([]);
    }
  }, [initialMode, interactionToEdit, isOpen]); // Re-run effect when isOpen changes too

  const handleClose = () => {
    setNewComment(''); // Reset comment on close
    onClose();
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...files]);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const sanitizeFileName = (fileName: string) => {
    const ext = fileName.split('.').pop();
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    const safeName = name
      .replace(/[^a-zA-Z0-9]/g, '_')
      .toLowerCase()
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    return `${safeName}.${ext}`;
  };

  const handleSubmitInteraction = async () => {
    if (!newInteraction.title || !newInteraction.type) return;
    if (!isInHouseholdContext && !selectedHouseholdId) {
      toast({
        title: "Error",
        description: "Please select a household",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const { data: interactionData, error: interactionError } = await supabase
        .from('interactions')
        .insert({
          household_id: isInHouseholdContext ? householdId : selectedHouseholdId,
          title: newInteraction.title,
          content: newInteraction.content,
          date: newInteraction.date,
          type: newInteraction.type,
        })
        .select()
        .single();

      if (interactionError) throw interactionError;

      for (const file of selectedFiles) {
        const timestamp = Date.now();
        const safeFileName = sanitizeFileName(file.name);
        const filePath = `${householdId}/${interactionData.id}/${timestamp}_${safeFileName}`;

        const { error: uploadError } = await supabase.storage
          .from('attachments')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from('attachments')
          .getPublicUrl(filePath);

        const { error: attachmentError } = await supabase
          .from('attachments')
          .insert({
            interaction_id: interactionData.id,
            name: file.name,
            url: publicUrl,
            type: file.type,
            size: file.size,
            path: filePath,
          });

        if (attachmentError) throw attachmentError;
      }

      if (interactionData && selectedTaskIds.length > 0) {
        // First, remove any existing links (for edit mode)
        if (interactionToEdit?.id) {
          await supabase
            .from('interaction_task_links')
            .delete()
            .eq('interaction_id', interactionToEdit.id);
        }
        
        // Create new links
        const links = selectedTaskIds.map(taskId => ({
          interaction_id: interactionToEdit?.id || interactionData.id,
          task_id: taskId
        }));
        
        const { error: linkError } = await supabase
          .from('interaction_task_links')
          .insert(links);
          
        if (linkError) throw linkError;
      }

      toast({
        title: "Success",
        description: "Interaction created successfully",
      });
      onInteractionSaved(); // Refresh table
      handleClose();
    } catch (error) {
      console.error('Error creating interaction:', error);
      toast({
        title: "Error",
        description: "Failed to create interaction. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditInteraction = async () => {
    if (!editedInteraction || !interactionToEdit) return;

    try {
      setIsSubmitting(true);
      const { error } = await supabase
        .from('interactions')
        .update({
          title: editedInteraction.title,
          content: editedInteraction.content,
          date: editedInteraction.date,
          type: editedInteraction.type,
        })
        .eq('id', interactionToEdit.id);

      if (error) throw error;

      // Handle new file uploads during edit
      for (const file of selectedFiles) {
        const timestamp = Date.now();
        const safeFileName = sanitizeFileName(file.name);
        const filePath = `${householdId}/${interactionToEdit.id}/${timestamp}_${safeFileName}`;

        const { error: uploadError } = await supabase.storage
          .from('attachments')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from('attachments')
          .getPublicUrl(filePath);

        const { error: attachmentError } = await supabase
          .from('attachments')
          .insert({
            interaction_id: interactionToEdit.id,
            name: file.name,
            url: publicUrl,
            type: file.type,
            size: file.size,
            path: filePath,
          });

        if (attachmentError) throw attachmentError;
      }

      toast({
        title: "Success",
        description: "Interaction updated successfully",
      });
      onInteractionSaved(); // Refresh table
      setMode('details'); // Switch back to details view after saving edit
      setSelectedFiles([]); // Clear selected files after upload
    } catch (error) {
      console.error('Error updating interaction:', error);
      toast({
        title: "Error",
        description: "Failed to update interaction",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteInteraction = async () => {
    if (!interactionToEdit) return;

    try {
      setIsSubmitting(true);
      // Optionally delete attachments from storage first
      if (interactionToEdit.attachments && interactionToEdit.attachments.length > 0) {
        const pathsToRemove = interactionToEdit.attachments.map(att => att.path);
        await supabase.storage.from('attachments').remove(pathsToRemove);
      }

      // Delete interaction (attachments and comments should cascade delete if set up in DB)
      const { error } = await supabase
        .from('interactions')
        .delete()
        .eq('id', interactionToEdit.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Interaction deleted successfully",
      });
      onInteractionDeleted(); // Refresh table
      handleClose();
    } catch (error) {
      console.error('Error deleting interaction:', error);
      toast({
        title: "Error",
        description: "Failed to delete interaction",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddComment = async () => {
    if (!commentInput.trim() || !interactionToEdit) return;

    try {
      // Format the comment content to include user tags
      let formattedContent = commentInput;
      
      // Insert the comment
      const { error } = await supabase
        .from('comments')
        .insert({
          interaction_id: interactionToEdit.id,
          content: formattedContent,
          user_id: profileData.user_id
        });

      if (error) throw error;

      // Send notifications to tagged users
      for (const taggedUser of taggedUsers) {
        // Skip if the tagged user is the current user
        if (taggedUser.id === profileData.user_id) continue;
        
        await supabase.from('notifications').insert({
          user_id: taggedUser.id,
          content: `You were mentioned in a comment on interaction: ${interactionToEdit.title}`,
          type: 'mention',
          link: `/protected/households/household/${interactionToEdit.household_id}/interactions?view=${interactionToEdit.id}`,
          created_at: new Date().toISOString()
        });
      }

      toast({
        title: "Success",
        description: "Comment added successfully",
      });
      setCommentInput('');
      setTaggedUsers([]);
      onInteractionSaved(); // Refresh table data to show new comment
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadAttachment = async (attachment: Attachment) => {
    try {
      const { data, error } = await supabase.storage
        .from('attachments')
        .download(attachment.path);

      if (error) throw error;

      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: "Error",
        description: "Failed to download file. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteAttachment = async (attachment: Attachment) => {
    if (!interactionToEdit) return;

    try {
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('attachments')
        .remove([attachment.path]);

      if (storageError) throw storageError;

      // Delete from database
      const { error: dbError } = await supabase
        .from('attachments')
        .delete()
        .eq('id', attachment.id);

      if (dbError) throw dbError;

      toast({
        title: "Success",
        description: "Attachment deleted successfully",
      });
      onInteractionSaved(); // Refresh table data
    } catch (error) {
      console.error('Error deleting attachment:', error);
      toast({
        title: "Error",
        description: "Failed to delete attachment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setSelectedFiles(prev => [...prev, ...acceptedFiles]);
    toast({
      title: "Success",
      description: "Email files added successfully",
    });
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'message/rfc822': ['.eml'],
      'application/octet-stream': ['.eml'],
    },
    noClick: true,
    noKeyboard: true,
  });

  const fetchComments = async () => {
    if (!interactionToEdit?.id) return;

    const { data, error } = await supabase
      .from('comments')
      .select('*')
      .eq('interaction_id', interactionToEdit.id)
      .order('created_at', { ascending: false });

    if (!error && data) {
      // Update the interactionToEdit with the new comments
      setEditedInteraction(prev => ({
        ...prev,
        comments: data
      }));
    }
  };

  const fetchAvailableTasks = async () => {
    if (!householdId && !selectedHouseholdId) return;
    
    const { data, error } = await supabase
      .from('tasks')
      .select('id, title, due_date, status')
      .eq('household_id', isInHouseholdContext ? householdId : selectedHouseholdId)
      .order('due_date', { ascending: false });
      
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    setAvailableTasks(data || []);
  };

  const fetchLinkedTasks = async (interactionId: number) => {
    const { data, error } = await supabase
      .from('interaction_task_links')
      .select(`
        id, task_id,
        tasks:task_id (id, title, due_date, status)
      `)
      .eq('interaction_id', interactionId);
      
    if (error) {
      console.error('Error fetching linked tasks:', error);
      return;
    }
    
    // Filter out any null tasks before setting state
    const validTasks = data
      ?.map(link => link.tasks)
      .filter(task => task !== null && task !== undefined) || [];
      
    setSelectedTaskIds(data?.map(link => link.task_id).filter(Boolean) || []);
    setLinkedTasks(validTasks);
  };

  useEffect(() => {
    if (interactionToEdit?.id) {
      fetchLinkedTasks(interactionToEdit.id);
    } else {
      setSelectedTaskIds([]);
      setLinkedTasks([]);
    }
    
    fetchAvailableTasks();
  }, [interactionToEdit, householdId, selectedHouseholdId]);

  useEffect(() => {
    if (mode === 'new') {
      const editor = new Editor({
        extensions: [StarterKit, SpaceKeyHandler],
        content: newInteraction.content,
        editorProps: {
          attributes: {
            class: 'min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          },
        },
        onUpdate: ({ editor }) => {
          setNewInteraction(prev => ({ ...prev, content: editor.getHTML() }));
        },
        autofocus: 'end',
      });
      setContentEditor(editor);
      
      // Give the editor a moment to initialize before focusing
      setTimeout(() => {
        editor.commands.focus('end');
      }, 100);
      
      return () => editor.destroy();
    }
  }, [mode]);

  useEffect(() => {
    if (mode === 'edit' && editedInteraction) {
      const editor = new Editor({
        extensions: [StarterKit, SpaceKeyHandler],
        content: editedInteraction.content || '',
        editorProps: {
          attributes: {
            class: 'min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          },
        },
        onUpdate: ({ editor }) => {
          setEditedInteraction(prev => ({ ...prev, content: editor.getHTML() }));
        },
        autofocus: 'end',
      });
      setContentEditor(editor);
      
      // Give the editor a moment to initialize before focusing
      setTimeout(() => {
        editor.commands.focus('end');
      }, 100);
      
      return () => editor.destroy();
    }
  }, [mode, editedInteraction]);

  // Add a function to handle keydown events
  const handleEditorKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === ' ' && contentEditor) {
      e.preventDefault();
      e.stopPropagation();
      contentEditor.commands.insertContent(' ');
    }
  };

  // Add a global event listener for space key
  useEffect(() => {
    if (contentEditor) {
      const handleGlobalKeyDown = (e: KeyboardEvent) => {
        if (e.key === ' ' && document.activeElement?.closest('.ProseMirror')) {
          e.preventDefault();
          contentEditor.commands.insertContent(' ');
        }
      };
      
      document.addEventListener('keydown', handleGlobalKeyDown);
      return () => {
        document.removeEventListener('keydown', handleGlobalKeyDown);
      };
    }
  }, [contentEditor]);

  // First, add a function to create a new task directly from the interaction modal
  const handleCreateLinkedTask = async () => {
    if (!interactionToEdit) return;
    
    try {
      setIsSubmitting(true);
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Error",
          description: "You must be logged in to create tasks",
          variant: "destructive",
        });
        return;
      }
      
      // Create a new task linked to this interaction
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .insert({
          household_id: interactionToEdit.household_id,
          title: `Task from: ${interactionToEdit.title}`,
          content: `Created from interaction: ${interactionToEdit.title}`,
          due_date: format(addDays(new Date(), 7), 'yyyy-MM-dd'), // Default due date 1 week from now
          importance: 'medium',
          status: 'not started',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: user.id // Add the user_id to satisfy RLS policy
        })
        .select('id')
        .single();
        
      if (taskError) throw taskError;
      
      // Create the link between interaction and task
      if (taskData) {
        const { error: linkError } = await supabase
          .from('interaction_task_links')
          .insert({
            interaction_id: interactionToEdit.id,
            task_id: taskData.id
          });
          
        if (linkError) throw linkError;
        
        toast({
          title: "Success",
          description: "New task created and linked to this interaction",
        });
        
        // Refresh data
        fetchLinkedTasks(interactionToEdit.id);
        fetchAvailableTasks();
        onInteractionSaved();
      }
    } catch (error) {
      console.error('Error creating linked task:', error);
      toast({
        title: "Error",
        description: "Failed to create linked task",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add this function to handle viewing a linked task
  const handleViewTask = (task: any) => {
    // Get the task ID - either directly or from the task_id property
    const taskId = task.id || task.task_id;
    
    if (!taskId) {
      console.error('No task ID found:', task);
      return;
    }
    
    // Determine the household ID to use
    const householdIdToUse = householdId || selectedHouseholdId;
    
    if (!householdIdToUse) {
      console.error('No household ID available for navigation');
      return;
    }
    
    // Create the correct URL for the task based on your app's routing structure
    const url = `/protected/households/household/${householdIdToUse}/tasks?view=${taskId}`;
    
    // Open in a new tab
    window.open(url, '_blank');
    
    console.log(`Opening task ${taskId} in new tab: ${url}`);
  };

  // Add useEffect to fetch user profile and organization members
  useEffect(() => {
    const fetchUserProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();
          
        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    };
    
    fetchUserProfile();
  }, []);

  // Add useEffect to fetch users when org_id is available
  useEffect(() => {
    const fetchUsers = async () => {
      if (!profileData.org_id) return;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('user_id, name')
        .eq('org_id', profileData.org_id)
        .order('name');
        
      if (error) {
        console.error('Error fetching users:', error);
      } else {
        setUsers(data.map(user => ({
          id: user.user_id,
          name: user.name
        })));
      }
    };
    
    fetchUsers();
  }, [profileData.org_id]);

  // Add functions for handling user tagging
  const handleCommentInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCommentInput(value);
    
    // Check if we're typing an @ symbol
    const lastAtSymbolIndex = value.lastIndexOf('@');
    if (lastAtSymbolIndex !== -1 && lastAtSymbolIndex >= value.lastIndexOf(' ') || lastAtSymbolIndex === 0) {
      const query = value.slice(lastAtSymbolIndex + 1);
      if (query) {
        // Filter users based on the query
        const filteredUsers = users.filter(user => 
          user.name.toLowerCase().includes(query.toLowerCase())
        );
        setUserSuggestions(filteredUsers);
        setShowUserSuggestions(filteredUsers.length > 0);
        setCursorPosition(lastAtSymbolIndex);
      } else {
        setUserSuggestions(users);
        setShowUserSuggestions(true);
        setCursorPosition(lastAtSymbolIndex);
      }
    } else {
      setShowUserSuggestions(false);
    }
  };

  const handleSelectUser = (user: User) => {
    const beforeTag = commentInput.slice(0, cursorPosition);
    const afterTag = commentInput.slice(commentInput.indexOf(' ', cursorPosition) > -1 
      ? commentInput.indexOf(' ', cursorPosition) 
      : commentInput.length);
    
    const newCommentInput = `${beforeTag}@${user.name} ${afterTag}`;
    setCommentInput(newCommentInput);
    setShowUserSuggestions(false);
    
    // Add to tagged users if not already included
    if (!taggedUsers.some(taggedUser => taggedUser.id === user.id)) {
      setTaggedUsers([...taggedUsers, { id: user.id, name: user.name }]);
    }
    
    // Focus back on the input
    if (commentInputRef.current) {
      commentInputRef.current.focus();
    }
  };

  // Add this state to store user profiles
  const [userProfiles, setUserProfiles] = useState<{[key: string]: {name: string}}>({}); 

  // Add this useEffect to fetch user profiles when comments change
  useEffect(() => {
    if (interactionToEdit?.comments?.length) {
      fetchUserProfiles();
    }
  }, [interactionToEdit?.comments]);

  // Add this function to fetch user profiles
  const fetchUserProfiles = async () => {
    if (!interactionToEdit?.comments) return;
    
    // Get unique user IDs from comments
    const userIds = [...new Set(interactionToEdit.comments.map(comment => comment.user_id))].filter(Boolean);
    
    if (userIds.length === 0) return;

    const { data, error } = await supabase
      .from('profiles')
      .select('user_id, name')
      .in('user_id', userIds);

    if (error) {
      console.error('Error fetching user profiles:', error);
      return;
    }

    const profiles: {[key: string]: {name: string}} = {};
    data?.forEach(profile => {
      profiles[profile.user_id] = { name: profile.name || 'Unknown User' };
    });

    setUserProfiles(profiles);
  };

  // Render logic based on mode
  if (mode === 'new') {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent 
          className="sm:max-w-[80%] sm:h-[80vh] max-h-[90vh] flex flex-col"
          fullscreenEnabled={true}
        >
          <DialogHeader>
            <DialogTitle>New Interaction</DialogTitle>
            <DialogDescription>Create a new interaction record</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Title */}
            <div className="grid grid-cols-[2fr_1fr] gap-4 h-full overflow-hidden">
            <div className="grid gap-2">
              <label>Title</label>
              <Input
                placeholder="Title"
                value={newInteraction.title}
                onChange={(e) => setNewInteraction({ ...newInteraction, title: e.target.value })}
              />
            </div>
            
            {/* Household selector - greyed out if in household context */}
            {!isInHouseholdContext ? (
              <div className="grid gap-2">
                <label>Household</label>
                <Select
                  value={selectedHouseholdId?.toString()}
                  onValueChange={(value) => setSelectedHouseholdId(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select household" />
                  </SelectTrigger>
                  <SelectContent>
                    {households.map((household) => (
                      <SelectItem key={household.id} value={household.id.toString()}>
                        {household.householdName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="grid gap-2">
                <label>Household</label>
                <Input 
                  value={households.find(h => h.id === householdId)?.householdName || ''}
                  disabled
                  className="bg-muted"
                />
              </div>
            )}
            </div>
            
            {/* Type and Date in a row */}
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label>Type</label>
                <Select
                  value={newInteraction.type}
                  onValueChange={(value) => setNewInteraction({ ...newInteraction, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="phone">Phone Call</SelectItem>
                    <SelectItem value="meeting">Meeting</SelectItem>
                    <SelectItem value="note">Note</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <label>Date</label>
                <Input
                  type="date"
                  value={newInteraction.date}
                  onChange={(e) => setNewInteraction({ ...newInteraction, date: e.target.value })}
                />
              </div>
            </div>
            
            {/* Content */}
            <div className="grid gap-2">
              <label>Content</label>
              {contentEditor && (
                <>
                  <Toolbar editor={contentEditor} />
                  <div 
                    className="flex-grow mt-2 overflow-y-auto"
                    onClick={() => contentEditor.commands.focus()}
                  >
                    <EditorContent 
                      editor={contentEditor} 
                      className="h-full"
                      onFocus={() => contentEditor.commands.focus()}
                      onKeyDown={handleEditorKeyDown}
                    />
                  </div>
                </>
              )}
            </div>
            
            {/* Attachments */}
            <div className="grid gap-2">
              <label className="flex items-center gap-2">
                <Paperclip className="h-4 w-4" />
                Attachments
              </label>
              {newInteraction.type === 'email' && (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-4 mb-4 text-center cursor-pointer transition-colors
                    ${isSubmitting ? 'bg-gray-100 cursor-not-allowed' : 'hover:bg-gray-50'}
                    ${isDragging ? 'border-primary bg-primary/10' : 'border-gray-300'}`}
                >
                  <input {...getInputProps()} disabled={isSubmitting} />
                  <p className="text-sm text-muted-foreground">
                    Drag & drop files here, or click to select files
                  </p>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  onChange={handleFileSelect}
                  multiple
                  disabled={isSubmitting}
                />
                <label
                  htmlFor="file-upload"
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 cursor-pointer"
                >
                  <Paperclip className="h-4 w-4 mr-2" />
                  Select Files
                </label>
              </div>
              <ScrollArea className="h-[100px] border rounded-md p-2">
                {selectedFiles.length > 0 ? (
                  selectedFiles.map((file, index) => (
                    <div key={index} className="flex justify-between items-center py-1">
                      <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        disabled={isSubmitting}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground p-2">No files selected</p>
                )}
              </ScrollArea>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>Cancel</Button>
            <Button 
              onClick={handleSubmitInteraction} 
              disabled={isSubmitting || !newInteraction.title || !newInteraction.type || (!isInHouseholdContext && !selectedHouseholdId)}
            >
              {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Save Interaction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  if (mode === 'details' || mode === 'edit') {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent 
          className="sm:max-w-[80%] sm:h-[80vh] max-h-[90vh] flex flex-col"
          fullscreenEnabled={true}
        >
          {interactionToEdit && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {mode === 'edit' ? 'Edit Interaction' : interactionToEdit.title}
                  {mode === 'details' && (
                    <span className="text-sm font-normal text-muted-foreground">
                      ({format(new Date(interactionToEdit.date), 'dd MMMM yyyy')})
                    </span>
                  )}
                </DialogTitle>
                {mode === 'edit' ? (
                  <DialogDescription>Make changes to this interaction</DialogDescription>
                ) : (
                  <div className="flex items-center mt-1">
                    <Badge variant={getInteractionBadgeVariant(interactionToEdit.type)} className="capitalize">
                      {interactionToEdit.type}
                    </Badge>
                  </div>
                )}
              </DialogHeader>

              {/* Two-column layout */}
              <div className="grid grid-cols-[2fr_1fr] gap-4 h-full overflow-hidden">
                {/* Left Column - Interaction Details */}
                <div className="flex flex-col h-full overflow-hidden">
                  {mode === 'edit' ? (
                    // Edit mode content
                    <div className="flex flex-col h-full gap-4">
                      {/* Title and Date in a row */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="grid gap-2">
                          <label className="text-sm font-medium">Title</label>
                          <Input
                            value={editedInteraction?.title}
                            onChange={(e) =>
                              setEditedInteraction((prev) => ({ ...prev, title: e.target.value }))
                            }
                            disabled={isSubmitting}
                          />
                        </div>
                        <div className="grid gap-2">
                          <label className="text-sm font-medium">Date</label>
                          <Input
                            type="date"
                            value={editedInteraction?.date}
                            onChange={(e) =>
                              setEditedInteraction((prev) => ({ ...prev, date: e.target.value }))
                            }
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>
                      
                      {/* Type selection */}
                      <div className="grid gap-2">
                        <label className="text-sm font-medium">Type</label>
                        <Select
                          value={editedInteraction?.type}
                          onValueChange={(value) =>
                            setEditedInteraction((prev) => ({ ...prev, type: value }))
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="phone">Phone Call</SelectItem>
                            <SelectItem value="meeting">Meeting</SelectItem>
                            <SelectItem value="note">Note</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {/* Content area - takes remaining space */}
                      <div className="grid gap-2">
                        <label>Content</label>
                        {contentEditor && (
                          <>
                            <Toolbar editor={contentEditor} />
                            <div 
                              className="flex-grow mt-2 overflow-y-auto"
                              onClick={() => contentEditor.commands.focus()}
                            >
                              <EditorContent 
                                editor={contentEditor} 
                                className="h-full"
                                onFocus={() => contentEditor.commands.focus()}
                                onKeyDown={handleEditorKeyDown}
                              />
                            </div>
                          </>
                        )}
                      </div>
                      
                      {/* Attachments section at bottom */}
                      <div className="mt-2">
                        <label className="flex items-center gap-2 text-sm font-medium mb-2">
                          <Paperclip className="h-4 w-4" />
                          Attachments
                        </label>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {selectedFiles.map((file, index) => (
                            <div key={index} className="flex items-center gap-1 bg-secondary p-2 rounded">
                              <span className="text-xs truncate max-w-[120px]">{file.name}</span>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-5 w-5 p-0"
                                onClick={() => removeFile(index)}
                                disabled={isSubmitting}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                        <div className="flex gap-2">
                          <Input
                            type="file"
                            multiple
                            onChange={handleFileSelect}
                            disabled={isSubmitting}
                            className="text-xs"
                          />
                          {editedInteraction?.type === 'email' && (
                            <Button
                              variant="outline"
                              size="sm"
                              {...getRootProps()}
                              className="whitespace-nowrap"
                            >
                              <Paperclip className="h-4 w-4 mr-1" />
                              Drop .eml
                              <input {...getInputProps()} />
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      <DialogFooter className="mt-2">
                        <Button variant="outline" onClick={() => setMode('details')} disabled={isSubmitting}>
                          Cancel
                        </Button>
                        <Button onClick={handleEditInteraction} disabled={isSubmitting}>
                          {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                          Save Changes
                        </Button>
                      </DialogFooter>
                    </div>
                  ) : (
                    // View mode content
                    <div className="flex flex-col h-full">
                      {/* Content area - takes most space */}
                      <div className="flex-grow overflow-auto mb-4">
                        <h3 className="text-sm font-medium mb-2">Content</h3>
                        <div className="text-sm" dangerouslySetInnerHTML={{ __html: interactionToEdit.content }} />
                      </div>
                      
                      {/* Attachments section at bottom */}
                      <div className="mt-auto">
                        <h3 className="text-sm font-medium mb-2">Attachments</h3>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {interactionToEdit.attachments && interactionToEdit.attachments.length > 0 ? (
                            interactionToEdit.attachments.map((attachment, index) => (
                              <div
                                key={index}
                                className="flex flex-col items-center bg-secondary p-2 rounded w-[100px] h-[100px] justify-between"
                              >
                                <Paperclip className="h-6 w-6 mb-1" />
                                <span className="text-xs text-center truncate w-full">{attachment.name}</span>
                                <div className="flex gap-1 mt-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => handleDownloadAttachment(attachment)}
                                  >
                                    <ChevronDown className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 text-destructive"
                                    onClick={() => handleDeleteAttachment(attachment)}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No attachments</p>
                          )}
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={handleClose}>
                            Close
                          </Button>
                          <Button onClick={() => setMode('edit')}>
                            Edit
                          </Button>
                        </DialogFooter>
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Column - Linked Tasks and Comments */}
                <div className="flex flex-col pl-2 border-l h-full overflow-hidden">
                  {/* Linked Tasks Section */}
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-sm font-medium">Linked Tasks</h3>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={handleCreateLinkedTask}
                        disabled={isSubmitting}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Create Task
                      </Button>
                    </div>
                    <ScrollArea className="h-[150px]">
                      {linkedTasks.length > 0 ? (
                        <div className="grid gap-2">
                          {linkedTasks.map(task => (
                            task && (  // Add null check here
                              <div key={task.id} className="flex justify-between items-center p-1 bg-secondary rounded-md min-h-12">
                                <span className='text-sm truncate'>{task?.title}</span>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs text-muted-foreground">
                                    {task?.due_date ? format(new Date(task.due_date), 'dd MMM yyyy') : 'No date'}
                                  </span>
                                  <Badge variant={task?.status === 'complete' ? 'green' : 'blue'} className="capitalize text-xs">
                                    {task?.status || 'Unknown'}
                                  </Badge>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-4 w-4" 
                                    onClick={() => handleViewTask(task)}
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            )
                          )).filter(Boolean)}  {/* Filter out any null/undefined items */}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No tasks linked to this interaction</p>
                      )}
                    </ScrollArea>
                  </div>

                  {/* Clear horizontal border */}
                  <div className="border-t my-4"></div>

                  {/* Comments Section */}
                  <div className="flex flex-col h-full overflow-hidden">
                    <h3 className="text-sm font-medium mb-2">Comments</h3>
                    
                    {/* Comments list */}
                    <ScrollArea className="flex-grow mb-4">
                      {interactionToEdit.comments && interactionToEdit.comments.map((comment) => (
                        <div key={comment.id} className="bg-muted p-3 rounded-md mb-3">
                          <div className="text-sm">{comment.content}</div>
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(comment.created_at), 'dd/MM/yyyy, HH:mm:ss')}
                            </span>
                            <span className="text-xs font-medium">
                              {userProfiles[comment.user_id]?.name || 'Unknown User'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </ScrollArea>
                    
                    {/* Comment input */}
                    <div className="relative mt-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-grow relative">
                          <textarea
                            ref={commentInputRef}
                            value={commentInput}
                            onChange={handleCommentInputChange}
                            placeholder="Add a comment... Use @ to mention team members"
                            className="min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          />
                          
                          {/* User suggestions dropdown - positioned ABOVE the input */}
                          {showUserSuggestions && userSuggestions.length > 0 && (
                            <div className="absolute z-10 bottom-full mb-1 w-full max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {userSuggestions.map((user) => (
                                <div
                                  key={user.id}
                                  className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"
                                  onClick={() => handleSelectUser(user)}
                                >
                                  <div className="flex items-center">
                                    <span className="font-medium block truncate">{user.name}</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                        <Button 
                          onClick={handleAddComment} 
                          disabled={!commentInput.trim()}
                          size="sm"
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  }

  if (mode === 'delete') {
    return (
      <AlertDialog open={isOpen} onOpenChange={handleClose}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the interaction
              and all its associated comments and attachments.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteInteraction}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isSubmitting}
            >
              {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return null; // Should not happen
}
