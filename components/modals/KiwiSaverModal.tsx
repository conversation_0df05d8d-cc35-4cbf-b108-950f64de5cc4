import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ExternalLink, Settings, Plus, Trash2, ChevronRight, ChevronLeft, ChevronDown } from 'lucide-react';
import { InputData } from '@/app/protected/planner/types';
import { FUND_TYPES } from '@/app/constants/fundTypes';
import { createClient } from '@/utils/supabase/client';
import { useFunds } from '@/hooks/useFunds';
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

// Get color for fund type
const getFundTypeColor = (fundType: string, fundId?: string, funds: any[] = []) => {
  // First check if it's a custom fund with a color
  if (fundId) {
    const customFund = funds.find(fund => fund.id === fundId);
    if (customFund && customFund.color) {
      return customFund.color;
    }
  }

  // Then check if it's a custom fund by name
  const customFundByName = funds.find(fund => fund.name === fundType && fund.type === 'kiwisaver');
  if (customFundByName && customFundByName.color) {
    return customFundByName.color;
  }

  // Default colors for built-in types
  switch (fundType) {
    case 'Custom':
      return '#9333ea'; // Purple
    case 'Conservative':
      return '#3b82f6'; // Blue
    case 'Balanced':
      return '#10b981'; // Green
    case 'Growth':
      return '#f59e0b'; // Amber
    case 'Aggressive':
      return '#f97316'; // Orange
    default:
      return '#6b7280'; // Gray
  }
};

interface KiwiSaverPeriod {
  fundId?: string; // ID of the custom fund
  fundType: keyof typeof FUND_TYPES | string; // Allow for custom fund names
  period: [number, number];
  customReturn?: number;
  customStdDev?: number;
  incomePortion?: number; // Add income portion to the interface
}

interface KiwiSaverModalProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  isPartner?: boolean;
  isOpen: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  mainName?: string;
  partnerName?: string;
}

interface ConsolidationAllocation {
  fundNumber: number;
  percentage: number;
}

export function KiwiSaverModal({
  inputData,
  setInputData,
  isPartner = false,
  isOpen,
  setOpen,
  mainName = 'Client',
  partnerName = 'Partner'
}: KiwiSaverModalProps) {
  const [activeTab, setActiveTab] = useState("details");
  const prefix = isPartner ? 'partner_' : '';
  const [userId, setUserId] = useState<string | undefined>(undefined);
  const { funds, isLoading: fundsLoading, getFundsByType } = useFunds(userId);
  const supabase = createClient();

  // State for consolidation allocations
  const [consolidationAllocations, setConsolidationAllocations] = useState<ConsolidationAllocation[]>(() => {
    // First check if we have saved allocations in inputData
    const savedAllocations = inputData[`${prefix}consolidation_allocations`];
    if (savedAllocations && savedAllocations.length > 0) {
      return savedAllocations;
    }

    // Otherwise initialize from withdrawal priorities
    const activeInvestmentFunds = inputData.withdrawal_priorities || [1];
    const initialAllocations = activeInvestmentFunds.map(fundNumber => ({
      fundNumber,
      percentage: 100 / activeInvestmentFunds.length
    }));
    return initialAllocations;
  });

  // Effect to update allocations when investment funds change or when saved allocations change
  useEffect(() => {
    // Get the saved allocations from inputData
    const savedAllocations = inputData[`${prefix}consolidation_allocations`];

    // If we have saved allocations, use those instead of recalculating
    if (savedAllocations && savedAllocations.length > 0) {
      setConsolidationAllocations(savedAllocations);
      return;
    }

    // Otherwise, update based on withdrawal priorities
    const activeInvestmentFunds = inputData.withdrawal_priorities || [1];
    setConsolidationAllocations(prevAllocations => {
      // Keep existing allocations for funds that are still active
      const existingAllocations = prevAllocations.filter(alloc =>
        activeInvestmentFunds.includes(alloc.fundNumber)
      );

      // Add new funds with equal distribution of remaining percentage
      const newFunds = activeInvestmentFunds.filter(fundNumber =>
        !existingAllocations.find(alloc => alloc.fundNumber === fundNumber)
      );

      if (newFunds.length > 0) {
        const totalExistingPercentage = existingAllocations.reduce((sum, alloc) => sum + alloc.percentage, 0);
        const remainingPercentage = Math.max(0, 100 - totalExistingPercentage);
        const percentagePerNewFund = remainingPercentage / newFunds.length;

        const updatedAllocations = [
          ...existingAllocations,
          ...newFunds.map(fundNumber => ({
            fundNumber,
            percentage: percentagePerNewFund
          }))
        ];

        return updatedAllocations;
      }

      return existingAllocations;
    });
  }, [inputData.withdrawal_priorities, inputData[`${prefix}consolidation_allocations`], prefix]);

  // Fetch user ID on component mount
  useEffect(() => {
    const fetchUserId = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (!error && data.user) {
        setUserId(data.user.id);
      }
    };

    fetchUserId();
  }, []);

  // Track the currently open period index (only one can be open at a time)
  const [openPeriodIndex, setOpenPeriodIndex] = useState<number>(0); // Default to first period open

  // Initialize ksFundPeriods from inputData
  const [ksFundPeriods, setKsFundPeriods] = useState<KiwiSaverPeriod[]>(() => {
    // Check for periods in the correct field
    const periods = (inputData as any)[`${prefix}ks_periods`];
    if (periods && periods.length > 0) {
      return periods.map((period: any) => ({
        fundId: period.fundId,
        fundType: period.fundType,
        period: period.period,
        customReturn: period.fundType === 'Custom' ? period.return : undefined,
        customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
        incomePortion: period.incomePortion // Include income portion from saved data
      }));
    }
    return [{
      fundType: (inputData as any)[`${prefix}ks_fund_type`] || 'Balanced',
      period: [inputData.starting_age, inputData.ending_age],
      customReturn: (inputData as any)[`${prefix}ks_fund_type`] === 'Custom' ? (inputData as any)[`${prefix}annual_kiwisaver_return`] : undefined,
      customStdDev: (inputData as any)[`${prefix}ks_fund_type`] === 'Custom' ? (inputData as any)[`${prefix}ks_std_dev`] : undefined,
      incomePortion: (inputData as any)[`${prefix}kiwisaver_income_portion`] // Include income portion from inputData
    }];
  });

  // Update ksFundPeriods when inputData changes
  useEffect(() => {
    const periods = (inputData as any)[`${prefix}ks_periods`];
    if (periods && periods.length > 0) {
      setKsFundPeriods(periods.map((period: any) => ({
        fundId: period.fundId,
        fundType: period.fundType,
        period: period.period,
        customReturn: period.fundType === 'Custom' ? period.return : undefined,
        customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
        incomePortion: period.incomePortion // Include income portion from saved data
      })));
    }
  }, [(inputData as any)[`${prefix}ks_periods`]]);

  const handleInputChange = (name: string, value: string) => {
    setInputData((prevData) => ({
      ...prevData,
      [name]: parseFloat(value),
    }));
  };

  const handleAddPeriod = () => {
    const lastPeriod = ksFundPeriods[ksFundPeriods.length - 1];

    // Ensure the new period starts after the last period ends
    const newStartAge = lastPeriod.period[1] + 1;

    // If there's no valid age range left, don't add a new period
    if (newStartAge >= inputData.ending_age) {
      alert("Cannot add a new period. The maximum age has been reached.");
      return;
    }

    setKsFundPeriods([...ksFundPeriods, {
      fundType: 'Balanced',
      period: [newStartAge, inputData.ending_age]
    }]);

    // Close any open period when adding a new one
    setOpenPeriodIndex(-1);
  };

  const handleRemovePeriod = (index: number) => {
    setKsFundPeriods(ksFundPeriods.filter((_, i) => i !== index));

    // Update openPeriodIndex if the removed period was open or if it affects the index
    setOpenPeriodIndex(prev => {
      if (prev === index) {
        // If we removed the open period, default to the first period
        return ksFundPeriods.length > 1 ? 0 : -1;
      } else if (prev > index) {
        // If we removed a period before the open one, adjust the index
        return prev - 1;
      }
      return prev;
    });
  };

  // This function handles changes to the period age inputs without validation
  // It allows users to type any value, even if it's temporarily invalid
  const handlePeriodChange = (index: number, field: 'start' | 'end', value: string) => {
    // If the value is empty or not a number, don't update
    if (value === '' || isNaN(parseInt(value))) return;

    const newPeriods = [...ksFundPeriods];
    const periodIndex = field === 'start' ? 0 : 1;

    // Update the value without validation
    newPeriods[index].period[periodIndex] = parseInt(value);
    setKsFundPeriods(newPeriods);
  };

  // This function validates and adjusts the period ages when the input loses focus
  const handlePeriodBlur = (index: number, field: 'start' | 'end') => {
    const newPeriods = [...ksFundPeriods];
    const periodIndex = field === 'start' ? 0 : 1;
    let currentValue = newPeriods[index].period[periodIndex];

    // Apply age restrictions based on scenario start/end ages and previous/next periods
    if (field === 'start') {
      // Start age cannot be less than scenario start age
      if (currentValue < inputData.starting_age) {
        currentValue = inputData.starting_age;
      }

      // Start age cannot be less than or equal to the end age of the previous period
      if (index > 0 && currentValue <= newPeriods[index - 1].period[1]) {
        currentValue = newPeriods[index - 1].period[1] + 1;
      }

      // Start age cannot be greater than or equal to the current end age
      if (currentValue >= newPeriods[index].period[1]) {
        currentValue = newPeriods[index].period[1] - 1;
      }
    } else { // field === 'end'
      // End age cannot be greater than scenario end age
      if (currentValue > inputData.ending_age) {
        currentValue = inputData.ending_age;
      }

      // End age cannot be greater than or equal to the start age of the next period
      if (index < newPeriods.length - 1 && currentValue >= newPeriods[index + 1].period[0]) {
        currentValue = newPeriods[index + 1].period[0] - 1;
      }

      // End age cannot be less than or equal to the current start age
      if (currentValue <= newPeriods[index].period[0]) {
        currentValue = newPeriods[index].period[0] + 1;
      }
    }

    // Update with the validated value
    newPeriods[index].period[periodIndex] = currentValue;
    setKsFundPeriods(newPeriods);
  };

  const handleFundTypeChange = (index: number, value: string) => {
    const newPeriods = [...ksFundPeriods];

    // Check if the value is a custom fund ID
    const customFund = funds.find(fund => fund.id === value && fund.type === 'kiwisaver');

    if (customFund) {
      // It's a custom fund from our scenario metrics
      newPeriods[index].fundId = customFund.id;
      newPeriods[index].fundType = customFund.name;
      newPeriods[index].customReturn = undefined;
      newPeriods[index].customStdDev = undefined;
    } else if (value === 'Custom') {
      // It's the built-in Custom option
      newPeriods[index].fundId = undefined;
      newPeriods[index].fundType = value as keyof typeof FUND_TYPES;
      newPeriods[index].customReturn = FUND_TYPES['Balanced'].return;
      newPeriods[index].customStdDev = FUND_TYPES['Balanced'].stdDev;
    } else {
      // It's one of the built-in fund types
      newPeriods[index].fundId = undefined;
      newPeriods[index].fundType = value as keyof typeof FUND_TYPES;
      newPeriods[index].customReturn = undefined;
      newPeriods[index].customStdDev = undefined;
    }

    setKsFundPeriods(newPeriods);
  };

  const handleCustomValueChange = (index: number, field: 'return' | 'stdDev' | 'incomePortion', value: string) => {
    const newPeriods = [...ksFundPeriods];
    if (field === 'return') {
      newPeriods[index].customReturn = parseFloat(value);
    } else if (field === 'stdDev') {
      newPeriods[index].customStdDev = parseFloat(value);
    } else if (field === 'incomePortion') {
      newPeriods[index].incomePortion = parseFloat(value);
      // Also update the global income portion value for backward compatibility
      setInputData(prev => ({
        ...prev,
        [`${prefix}kiwisaver_income_portion`]: parseFloat(value)
      }));
    }
    setKsFundPeriods(newPeriods);
  };

  // Get expected return, risk, and income portion for a fund type
  const getFundTypeInfo = (fundType: string, customReturn?: number, customStdDev?: number, fundId?: string): { return: number, risk: number, incomePortion?: number } => {
    // Check if we have a specific income portion set for this KiwiSaver
    const specificIncomePortion = (inputData as any)[`${prefix}kiwisaver_income_portion`];

    // First check if it's a custom fund from our scenario metrics
    if (fundId) {
      const customFund = funds.find(fund => fund.id === fundId);
      if (customFund) {
        // Use the specific income portion if available, otherwise use the fund's income portion or default
        const incomePortion = specificIncomePortion !== undefined ?
          specificIncomePortion :
          (customFund.incomePortion !== undefined ? customFund.incomePortion : 60);

        return {
          return: customFund.return || 5.5, // Ensure we always have a number
          risk: customFund.stdDev || 8.0,
          incomePortion: incomePortion
        };
      }
    }

    // Then check if it's a custom fund by name
    const customFundByName = funds.find(fund => fund.name === fundType && fund.type === 'kiwisaver');
    if (customFundByName) {
      // Use the specific income portion if available, otherwise use the fund's income portion or default
      const incomePortion = specificIncomePortion !== undefined ?
        specificIncomePortion :
        (customFundByName.incomePortion !== undefined ? customFundByName.incomePortion : 60);

      return {
        return: customFundByName.return || 5.5, // Ensure we always have a number
        risk: customFundByName.stdDev || 8.0,
        incomePortion: incomePortion
      };
    }

    // Check if it's the built-in Custom type with custom values
    if (fundType === 'Custom' && customReturn !== undefined && customStdDev !== undefined) {
      // For custom type, use the specific income portion if available, otherwise default to 60%
      const incomePortion = specificIncomePortion !== undefined ? specificIncomePortion : 60;

      return {
        return: customReturn,
        risk: customStdDev,
        incomePortion: incomePortion
      };
    }

    // Finally, check if it's one of the built-in fund types
    if (fundType in FUND_TYPES) {
      // If we have a specific income portion, use it
      if (specificIncomePortion !== undefined) {
        return {
          return: FUND_TYPES[fundType as keyof typeof FUND_TYPES].return || 5.5,
          risk: FUND_TYPES[fundType as keyof typeof FUND_TYPES].stdDev || 8.0,
          incomePortion: specificIncomePortion
        };
      }

      // Otherwise, use default income portions based on fund type
      let defaultIncomePortion = 60; // Default for Balanced

      if (fundType === 'Conservative') defaultIncomePortion = 90;
      else if (fundType === 'Moderate') defaultIncomePortion = 80;
      else if (fundType === 'Balanced') defaultIncomePortion = 60;
      else if (fundType === 'Growth') defaultIncomePortion = 40;
      else if (fundType === 'Aggressive') defaultIncomePortion = 20;

      return {
        return: FUND_TYPES[fundType as keyof typeof FUND_TYPES].return || 5.5,
        risk: FUND_TYPES[fundType as keyof typeof FUND_TYPES].stdDev || 8.0,
        incomePortion: defaultIncomePortion
      };
    }

    // Default fallback
    return {
      return: 5.5, // Balanced default
      risk: 8.0,
      incomePortion: specificIncomePortion !== undefined ? specificIncomePortion : 60 // Use specific income portion or default
    };
  };

  const handleSave = () => {
    // Validate periods don't overlap and respect scenario age limits
    const sortedPeriods = [...ksFundPeriods].sort((a, b) => a.period[0] - b.period[0]);

    // Check first period starts at or after scenario start age
    if (sortedPeriods[0].period[0] < inputData.starting_age) {
      alert(`First period must start at or after the scenario starting age (${inputData.starting_age})`);
      return;
    }

    // Check last period ends at or before scenario end age
    if (sortedPeriods[sortedPeriods.length - 1].period[1] > inputData.ending_age) {
      alert(`Last period must end at or before the scenario ending age (${inputData.ending_age})`);
      return;
    }

    // Check for overlapping periods
    for (let i = 1; i < sortedPeriods.length; i++) {
      if (sortedPeriods[i].period[0] <= sortedPeriods[i-1].period[1]) {
        alert("Fund periods cannot overlap");
        return;
      }
    }

    // Validate consolidation allocations total 100%
    if (inputData[`${prefix}consolidate_kiwisaver`]) {
      const totalAllocation = consolidationAllocations.reduce((sum, alloc) => sum + alloc.percentage, 0);
      if (Math.abs(totalAllocation - 100) > 0.01) {
        alert("Consolidation allocations must total 100%");
        return;
      }
    }

    // Prepare the KiwiSaver periods data
    const periodsData = sortedPeriods.map(period => {
      // Get fund info with the correct income portion
      const fundInfo = getFundTypeInfo(period.fundType, period.customReturn, period.customStdDev, period.fundId);
      return {
        fundId: period.fundId,
        fundType: period.fundType,
        period: period.period,
        return: fundInfo.return,
        stdDev: fundInfo.risk,
        incomePortion: fundInfo.incomePortion
      };
    });

    // Update KiwiSaver-specific fields and consolidation allocations
    setInputData(prev => {
      // Get fund info with the correct income portion
      const firstPeriodInfo = getFundTypeInfo(
        sortedPeriods[0].fundType,
        sortedPeriods[0].customReturn,
        sortedPeriods[0].customStdDev,
        sortedPeriods[0].fundId
      );

      // Determine the correct field name for consolidation age
      const consolidationAgeField = isPartner ? `${prefix}consolidate_kiwisaver_age` : 'main_consolidate_kiwisaver_age';
      const consolidationAge = prev[consolidationAgeField] || 65;

      // Prepare the consolidation allocations data
      const allocationsData = consolidationAllocations.map(alloc => ({
        fundNumber: alloc.fundNumber,
        percentage: alloc.percentage
      }));

      return {
        ...prev,
        [`${prefix}ks_fund_type`]: sortedPeriods[0].fundType,
        [`${prefix}annual_kiwisaver_return`]: firstPeriodInfo.return,
        [`${prefix}ks_std_dev`]: firstPeriodInfo.risk,
        [`${prefix}ks_periods`]: periodsData,
        // Always save the consolidation allocations if they exist, regardless of whether consolidate_kiwisaver is enabled
        // This ensures the allocations persist even when switching tabs
        [`${prefix}consolidation_allocations`]: allocationsData,
        // Ensure the consolidation age is saved with the correct field name
        [consolidationAgeField]: inputData[`${prefix}consolidate_kiwisaver`] ? consolidationAge : undefined
      };
    });
    setOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <ExternalLink className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {isPartner ? `${partnerName}'s` : `${mainName}'s`} KiwiSaver Settings
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="details">KiwiSaver Details</TabsTrigger>
            <TabsTrigger value="periods">KiwiSaver Fund Periods</TabsTrigger>
          </TabsList>

          {/* Details Tab */}
          <TabsContent value="details" className="space-y-4 pt-4">
            <Card className="p-4 border-l-4 border-l-amber-500">
              <h3 className="text-sm font-medium mb-3">Initial KiwiSaver & Contributions</h3>
              <div className="grid gap-4">
                <div>
                  <LabelWithTooltip
                    htmlFor={`${prefix}initial_kiwiSaver`}
                    label="Initial KiwiSaver Balance"
                    tooltipText="Current KiwiSaver balance at the start of financial planning"
                  />
                  <Input
                    id={`${prefix}initial_kiwiSaver`}
                    type="number"
                    value={isPartner ? inputData.partner_initial_kiwisaver : inputData.initial_kiwiSaver}
                    onChange={(e) => handleInputChange(isPartner ? 'partner_initial_kiwiSaver' : 'initial_kiwiSaver', e.target.value)}
                    className="bg-white dark:bg-gray-800"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <LabelWithTooltip
                      htmlFor={`${prefix}kiwisaver_contribution`}
                      label="Employee Contribution (%)"
                      tooltipText="Percentage of your salary that you contribute to KiwiSaver (common rates: 3%, 4%, 6%, 8%, or 10%)"
                    />
                    <Input
                      id={`${prefix}kiwisaver_contribution`}
                      type="number"
                      value={isPartner ? inputData.partner_kiwisaver_contribution : inputData.kiwisaver_contribution}
                      onChange={(e) => handleInputChange(isPartner ? 'partner_kiwisaver_contribution' : 'kiwisaver_contribution', e.target.value)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>
                  <div>
                    <LabelWithTooltip
                      htmlFor={`${prefix}employer_contribution`}
                      label="Employer Contribution (%)"
                      tooltipText="Percentage your employer contributes to your KiwiSaver (minimum 3% for most employers)"
                    />
                    <Input
                      id={`${prefix}employer_contribution`}
                      type="number"
                      value={isPartner ? inputData.partner_employer_contribution : inputData.employer_contribution}
                      onChange={(e) => handleInputChange(isPartner ? 'partner_employer_contribution' : 'employer_contribution', e.target.value)}
                      className="bg-white dark:bg-gray-800"
                    />
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4 border-l-4 border-l-blue-500">
              <h3 className="text-sm font-medium mb-3">KiwiSaver Consolidation</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`${prefix}consolidate_kiwisaver`}
                      checked={inputData[`${prefix}consolidate_kiwisaver`] || false}
                      onCheckedChange={(checked) => {
                        setInputData(prev => {
                          const ageFieldName = isPartner ? `${prefix}consolidate_kiwisaver_age` : 'main_consolidate_kiwisaver_age';
                          const newData = {
                            ...prev,
                            [`${prefix}consolidate_kiwisaver`]: checked,
                            [ageFieldName]: checked ? 65 : undefined
                          };
                          return newData;
                        });
                      }}
                    />
                    <LabelWithTooltip
                      htmlFor={`${prefix}consolidate_kiwisaver`}
                      label="Consolidate KiwiSaver"
                      tooltipText="Option to transition KiwiSaver funds to a different investment strategy at retirement"
                    />
                  </div>
                  {inputData[`${prefix}consolidate_kiwisaver`] && (
                    <div className="flex items-center space-x-2">
                      <LabelWithTooltip
                        htmlFor={`${prefix}consolidate_kiwisaver_age`}
                        label="Consolidation Age"
                        tooltipText="Specify the age at which you plan to consolidate your KiwiSaver funds (minimum 65)"
                      />
                      <Input
                        id={`${prefix}consolidate_kiwisaver_age`}
                        type="number"
                        min={65}
                        className="w-20 bg-white dark:bg-gray-800"
                        value={(isPartner ? inputData[`${prefix}consolidate_kiwisaver_age`] : inputData.main_consolidate_kiwisaver_age) || 65}
                        onChange={(e) => {
                          const value = Math.max(65, parseInt(e.target.value) || 65);
                          const ageFieldName = isPartner ? `${prefix}consolidate_kiwisaver_age` : 'main_consolidate_kiwisaver_age';
                          setInputData(prev => ({
                            ...prev,
                            [ageFieldName]: value
                          }));
                        }}
                      />
                    </div>
                  )}
                </div>

                {inputData[`${prefix}consolidate_kiwisaver`] && (
                  <div className="space-y-4">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Specify how your KiwiSaver funds will be allocated across your investment funds at consolidation age:
                    </div>
                    {consolidationAllocations.map((allocation, index) => (
                      <div key={allocation.fundNumber} className="flex items-center space-x-4">
                        <div className="flex-grow">
                          <LabelWithTooltip
                            htmlFor={`allocation-${allocation.fundNumber}`}
                            label={`Investment Fund ${allocation.fundNumber}`}
                            tooltipText={`Allocation percentage for Investment Fund ${allocation.fundNumber}`}
                          />
                          <Input
                            id={`allocation-${allocation.fundNumber}`}
                            type="number"
                            min={0}
                            max={100}
                            step={1}
                            value={allocation.percentage}
                            onChange={(e) => {
                              const newValue = Math.min(100, Math.max(0, parseFloat(e.target.value) || 0));
                              setConsolidationAllocations(prev => {
                                const newAllocations = [...prev];
                                newAllocations[index].percentage = newValue;

                                // Adjust other allocations proportionally
                                const total = newAllocations.reduce((sum, alloc) => sum + alloc.percentage, 0);
                                if (total !== 100) {
                                  const otherAllocations = newAllocations.filter((_, i) => i !== index);
                                  const remainingPercentage = 100 - newValue;
                                  const totalOtherPercentages = otherAllocations.reduce((sum, alloc) => sum + alloc.percentage, 0);

                                  otherAllocations.forEach(alloc => {
                                    const allocIndex = newAllocations.findIndex(a => a.fundNumber === alloc.fundNumber);
                                    if (totalOtherPercentages > 0) {
                                      newAllocations[allocIndex].percentage = (alloc.percentage / totalOtherPercentages) * remainingPercentage;
                                    } else {
                                      newAllocations[allocIndex].percentage = remainingPercentage / otherAllocations.length;
                                    }
                                  });
                                }

                                return newAllocations;
                              });
                            }}
                            className="bg-white dark:bg-gray-800"
                          />
                        </div>
                        <div className="text-sm font-medium">%</div>
                      </div>
                    ))}
                    <div className="text-sm font-medium text-right">
                      Total: {consolidationAllocations.reduce((sum, alloc) => sum + alloc.percentage, 0).toFixed(1)}%
                    </div>
                  </div>
                )}
              </div>
            </Card>

          </TabsContent>

          {/* Fund Periods Tab */}
          <TabsContent value="periods" className="space-y-4 pt-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">KiwiSaver Fund Periods</h3>
            </div>

            <div className="space-y-4">
              {ksFundPeriods.map((period, index) => {
                return (
                  <Card key={index} className="overflow-hidden">
                    <button
                      className="w-full px-4 py-3 flex justify-between items-center text-black dark:text-white"
                      style={{
                        backgroundColor: period.fundId
                          ? `${getFundTypeColor(period.fundType, period.fundId, funds)}40`
                          : period.fundType === 'Custom'
                            ? '#9333ea20'
                            : '#6b728020'
                      }}
                      onClick={() => setOpenPeriodIndex(prev => prev === index ? -1 : index)}
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Period {index + 1}</span>
                        <span className="text-sm">
                          (Ages {period.period[0]} - {period.period[1]})
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        {index > 0 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemovePeriod(index);
                            }}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                        <Badge
                          variant="outline"
                          className="bg-white/80 dark:bg-black/20 mr-1"
                          style={period.fundId ? {
                            borderColor: getFundTypeColor(period.fundType, period.fundId, funds)
                          } : {}}
                        >
                          {period.fundId ?
                            funds.find(f => f.id === period.fundId)?.name :
                            period.fundType
                          }
                        </Badge>
                        <ChevronDown
                          className={`h-4 w-4 transition-transform ${openPeriodIndex === index ? 'rotate-180' : ''}`}
                        />
                      </div>
                    </button>

                    {openPeriodIndex === index && (
                      <div className="p-4 space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <LabelWithTooltip
                            htmlFor={`fund-type-${index}`}
                            label="Fund Type"
                            tooltipText="The KiwiSaver fund strategy for this period"
                          />
                          <Select
                            value={period.fundId || period.fundType}
                            onValueChange={(value) => handleFundTypeChange(index, value)}
                          >
                            <SelectTrigger
                              id={`fund-type-${index}`}
                              className="h-10 bg-white dark:bg-gray-800"
                              style={period.fundId ? {
                                borderColor: getFundTypeColor(period.fundType, period.fundId, funds),
                                color: getFundTypeColor(period.fundType, period.fundId, funds)
                              } : {}}
                            >
                              <SelectValue placeholder="Select fund type">
                                {period.fundId ? (
                                  funds.find(f => f.id === period.fundId)?.name || period.fundType
                                ) : period.fundType}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {/* Custom option for manual values */}
                              <SelectItem key="Custom" value="Custom">Custom (Manual Values)</SelectItem>

                              {/* Custom funds from scenario metrics */}
                              {!fundsLoading && getFundsByType('kiwisaver').length > 0 && (
                                <>
                                  <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                    My Funds
                                  </div>
                                  {getFundsByType('kiwisaver').map((fund) => (
                                    <SelectItem key={fund.id} value={fund.id}>
                                      {fund.name} ({fund.return}%, {fund.stdDev}%)
                                    </SelectItem>
                                  ))}
                                </>
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor={`period-start-${index}`}
                            label="Start Age"
                            tooltipText="Age when this KiwiSaver fund strategy begins"
                          />
                          <Input
                            id={`period-start-${index}`}
                            type="number"
                            value={period.period[0]}
                            onChange={(e) => handlePeriodChange(index, 'start', e.target.value)}
                            onBlur={() => handlePeriodBlur(index, 'start')}
                            placeholder="Start Age"
                            className="h-10 bg-white dark:bg-gray-800"
                          />
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor={`period-end-${index}`}
                            label="End Age"
                            tooltipText="Age when this KiwiSaver fund strategy ends"
                          />
                          <Input
                            id={`period-end-${index}`}
                            type="number"
                            value={period.period[1]}
                            onChange={(e) => handlePeriodChange(index, 'end', e.target.value)}
                            onBlur={() => handlePeriodBlur(index, 'end')}
                            placeholder="End Age"
                            className="h-10 bg-white dark:bg-gray-800"
                          />
                        </div>
                      </div>

                      {period.fundType === 'Custom' && (
                        <Card className="p-4 mt-2 bg-purple-50 dark:bg-purple-900/10 border-purple-200 dark:border-purple-800">
                          <div className="space-y-4">
                            <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">Custom Fund Parameters</h4>
                            <div className="grid grid-cols-3 gap-4">
                              <div className="space-y-2">
                                <LabelWithTooltip
                                  htmlFor={`custom-return-${index}`}
                                  label="Rate of Return (%)"
                                  tooltipText="Customize the expected annual return percentage for this specific KiwiSaver fund period"
                                />
                                <Input
                                  id={`custom-return-${index}`}
                                  type="number"
                                  value={period.customReturn}
                                  onChange={(e) => handleCustomValueChange(index, 'return', e.target.value)}
                                  placeholder="Enter return rate"
                                  className="h-10 bg-white dark:bg-gray-800"
                                />
                              </div>
                              <div className="space-y-2">
                                <LabelWithTooltip
                                  htmlFor={`custom-stddev-${index}`}
                                  label="Standard Deviation (%)"
                                  tooltipText="Measure of volatility/risk for this specific KiwiSaver fund period"
                                />
                                <Input
                                  id={`custom-stddev-${index}`}
                                  type="number"
                                  value={period.customStdDev}
                                  onChange={(e) => handleCustomValueChange(index, 'stdDev', e.target.value)}
                                  placeholder="Enter std dev"
                                  className="h-10 bg-white dark:bg-gray-800"
                                />
                              </div>
                              <div className="space-y-2">
                                <LabelWithTooltip
                                  htmlFor={`income-portion-${index}`}
                                  label="Income Portion (%)"
                                  tooltipText="Percentage of returns that are taxable income (vs capital gains)"
                                />
                                <Input
                                  id={`income-portion-${index}`}
                                  type="number"
                                  value={period.incomePortion || (inputData as any)[`${prefix}kiwisaver_income_portion`] || 60}
                                  onChange={(e) => handleCustomValueChange(index, 'incomePortion', e.target.value)}
                                  placeholder="Enter income portion"
                                  className="h-10 bg-white dark:bg-gray-800"
                                />
                              </div>
                            </div>
                          </div>
                        </Card>
                      )}
                      </div>
                    )}
                  </Card>
                );
              })}

              <Button
                variant="outline"
                onClick={handleAddPeriod}
                className="w-full mt-4"
              >
                <Plus className="h-4 w-4 mr-2" /> Add KiwiSaver Period
              </Button>
            </div>

          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between items-center mt-6 gap-4">
          <div className="flex-grow">
            <Button variant="outline" onClick={() => {
              setKsFundPeriods([{
                fundType: 'Balanced',
                period: [inputData.starting_age, inputData.ending_age]
              }]);
              // Reset to first period being open
              setOpenPeriodIndex(0);
            }}>
              Reset to Default
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
