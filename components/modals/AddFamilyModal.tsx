'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FamilyMember {
  [x: string]: any;
  id: string;
  name: string;
  family_relationship: string;
  phone: string;
  email: string;
  notes?: string;
}

interface AddFamilyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (member: FamilyMember) => void;
  memberToEdit?: FamilyMember;
  isEditing?: boolean;
  onUpdate?: (member: FamilyMember) => void;
}

export default function AddFamilyModal({ 
  isOpen, 
  onClose, 
  onAdd, 
  memberToEdit, 
  isEditing = false, 
  onUpdate 
}: AddFamilyModalProps) {
  const [newMember, setNewMember] = useState<Partial<FamilyMember>>(
    memberToEdit || {}
  );

  useEffect(() => {
    if (isOpen) {
      setNewMember(memberToEdit || {});
    }
  }, [isOpen, memberToEdit]);

  const handleSubmit = () => {
    if (newMember.name && newMember.family_relationship) {
      if (isEditing && onUpdate && memberToEdit) {
        onUpdate({
          id: memberToEdit.id,
          name: newMember.name,
          family_relationship: newMember.family_relationship,
          phone: newMember.phone || '',
          email: newMember.email || '',
          notes: newMember.notes
        });
      } else {
        onAdd({
          id: crypto.randomUUID(),
          name: newMember.name,
          family_relationship: newMember.family_relationship,
          phone: newMember.phone || '',
          email: newMember.email || '',
          notes: newMember.notes
        });
      }
      setNewMember({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit' : 'Add'} Family Member</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Update' : 'Add'} details about a family member related to this household.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={newMember.name || ''}
              onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
              placeholder="Enter name"
            />
          </div>
          <div>
            <Label htmlFor="relationship">Relationship</Label>
            <Select
              value={newMember.family_relationship}
              onValueChange={(value) => setNewMember({ ...newMember, family_relationship: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select relationship" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="spouse">Spouse</SelectItem>
                <SelectItem value="child">Child</SelectItem>
                <SelectItem value="parent">Parent</SelectItem>
                <SelectItem value="sibling">Sibling</SelectItem>
                <SelectItem value="grandparent">Grandparent</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={newMember.phone || ''}
              onChange={(e) => setNewMember({ ...newMember, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={newMember.email || ''}
              onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={newMember.notes || ''}
              onChange={(e) => setNewMember({ ...newMember, notes: e.target.value })}
              placeholder="Add any additional notes"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit}>{isEditing ? 'Update' : 'Add'} Member</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
