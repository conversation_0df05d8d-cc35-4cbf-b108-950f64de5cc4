'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { Loader2, Laptop, Smartphone, Tablet, X, Edit2 } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { removeDeviceAction, updateDeviceNameAction } from '@/app/actions';
import { useRouter } from 'next/navigation';

interface ManageSessionsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Device {
  id: string;
  device_id: string;
  device_name: string;
  user_agent: string;
  last_active: string;
  created_at: string;
  is_current: boolean;
}

export default function ManageSessionsModal({ isOpen, onClose }: ManageSessionsModalProps) {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingDevice, setEditingDevice] = useState<string | null>(null);
  const [newDeviceName, setNewDeviceName] = useState('');
  const router = useRouter();
  const supabase = createClient();

  // Fetch user devices
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError('');

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      console.log('Fetching devices for user:', user.id);

      const { data, error } = await supabase
        .from('user_devices')
        .select('*')
        .eq('user_id', user.id)
        .order('is_current', { ascending: false })
        .order('last_active', { ascending: false });

      if (error) {
        throw error;
      }

      console.log('Devices fetched:', data);
      setDevices(data || []);
    } catch (err: any) {
      console.error('Error fetching devices:', err);
      setError(err.message || 'Failed to load devices');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchDevices();
    }
  }, [isOpen]);

  const getDeviceIcon = (device: Device) => {
    const userAgent = device.user_agent?.toLowerCase() || '';

    if (/ipad|android(?!.*mobile)/i.test(userAgent)) {
      return <Tablet className="h-5 w-5" />;
    } else if (/iphone|android|mobile/i.test(userAgent)) {
      return <Smartphone className="h-5 w-5" />;
    } else {
      return <Laptop className="h-5 w-5" />;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleRemoveDevice = async (device: Device) => {
    const isCurrent = device.is_current;

    if (isCurrent && !confirm('This will sign you out of this device. Are you sure?')) {
      return;
    }

    try {
      console.log('Removing device:', {
        id: device.id,
        deviceId: device.device_id,
        name: device.device_name,
        isCurrent
      });

      const formData = new FormData();
      formData.append('deviceId', device.device_id);
      formData.append('isCurrent', isCurrent.toString());
      formData.append('returnPath', '/protected/profile');

      await removeDeviceAction(formData);

      if (!isCurrent) {
        // Refresh the device list
        fetchDevices();
      }
      // If current device, the action will redirect to sign-in
    } catch (err: any) {
      console.error('Error removing device:', err);
      setError(err.message || 'Failed to remove device');
    }
  };

  const handleUpdateDeviceName = async (deviceId: string) => {
    if (!newDeviceName.trim()) {
      setError('Device name cannot be empty');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('deviceId', deviceId);
      formData.append('deviceName', newDeviceName);
      formData.append('returnPath', '/protected/profile');

      await updateDeviceNameAction(formData);

      // Reset editing state
      setEditingDevice(null);
      setNewDeviceName('');

      // Refresh the device list
      fetchDevices();
    } catch (err: any) {
      console.error('Error updating device name:', err);
      setError(err.message || 'Failed to update device name');
    }
  };

  const startEditing = (device: Device) => {
    setEditingDevice(device.device_id);
    setNewDeviceName(device.device_name);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Devices</DialogTitle>
          <DialogDescription>
            You can have up to 3 devices registered. Remove unused devices to add new ones.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="text-sm font-medium text-destructive mb-4">{error}</div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : devices.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No devices registered
          </div>
        ) : (
          <div className="space-y-4">
            {devices.map((device) => (
              <Card key={device.device_id} className={device.is_current ? 'border-primary' : ''}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="mt-1">{getDeviceIcon(device)}</div>
                      <div>
                        {editingDevice === device.device_id ? (
                          <div className="space-y-2 mt-1">
                            <Label htmlFor={`device-name-${device.device_id}`} className="sr-only">
                              Device Name
                            </Label>
                            <div className="flex items-center space-x-2">
                              <Input
                                id={`device-name-${device.device_id}`}
                                value={newDeviceName}
                                onChange={(e) => setNewDeviceName(e.target.value)}
                                className="h-8"
                                autoFocus
                              />
                              <Button
                                size="sm"
                                onClick={() => handleUpdateDeviceName(device.device_id)}
                              >
                                Save
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setEditingDevice(null)}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium">{device.device_name}</h3>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => startEditing(device)}
                            >
                              <Edit2 className="h-3 w-3" />
                              <span className="sr-only">Edit name</span>
                            </Button>
                          </div>
                        )}
                        <div className="text-sm text-muted-foreground mt-1">
                          Last active: {formatDate(device.last_active)}
                        </div>
                        <div className="mt-2 flex items-center space-x-2">
                          {device.is_current && (
                            <Badge variant="outline" className="bg-primary/10">Current Device</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                      onClick={() => handleRemoveDevice(device)}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Remove device</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <DialogFooter className="pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
