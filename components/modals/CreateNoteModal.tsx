import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Input } from '../ui/input';


interface CreateNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateNote: (noteId: string) => void;
  userId: string;
  orgId: string; // Add orgId prop
}

interface Household {
  id: number;
  householdName: string;
}

const noteTypes = [
  { value: "general", label: "General" },
  { value: "important", label: "Important" },
  { value: "info", label: "Information" },
  { value: "task", label: "Task" },
  { value: "meeting", label: "Meeting" }
];

export default function CreateNoteModal({ isOpen, onClose, onCreateNote, userId, orgId }: CreateNoteModalProps) {
  const [selectedHousehold, setSelectedHousehold] = useState('');
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [title, setTitle] = useState('');
  const [noteType, setNoteType] = useState('general');

  useEffect(() => {
    const fetchHouseholds = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('householdName', { ascending: true });

      if (error) {
        console.error('Error fetching households:', error);
      } else {
        setHouseholds(data || []);
      }
      setIsLoading(false);
    };

    fetchHouseholds();
  }, []);

  const handleCreate = async () => {
    if (selectedHousehold && title) {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('notes')
        .insert([
          {
            user_id: userId,
            org_id: orgId, // Add org_id
            household_id: selectedHousehold,
            title: title,
            note_type: noteType,
            content: JSON.stringify({ type: 'doc', content: [{ type: 'paragraph' }] }),
            created_at: new Date().toISOString(),
            last_edited_at: new Date().toISOString(),
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error creating note:', error);
      } else if (data) {
        onCreateNote(data.id);
        onClose();
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Note</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="noteTitle" className="text-right">
                Note Title
              </Label>
              <Input
                id="noteTitle"
                className="col-span-3"
                placeholder="Enter note title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="noteType" className="text-right">
                Note Type
              </Label>
              <Select onValueChange={setNoteType} value={noteType}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select note type" />
                </SelectTrigger>
                <SelectContent>
                  {noteTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="household" className="text-right">
                Select Household
              </Label>
              <Select onValueChange={setSelectedHousehold} value={selectedHousehold}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select a household" />
                </SelectTrigger>
                <SelectContent>
                  {households.map((household) => (
                    <SelectItem key={household.id} value={household.id.toString()}>
                      {household.householdName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button onClick={handleCreate} disabled={!selectedHousehold || !title}>
            Create Note
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
