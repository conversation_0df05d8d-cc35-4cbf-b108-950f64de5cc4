import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState } from 'react';

interface OptimizationSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartOptimization: (settings: OptimizationSettings) => void;
}

export interface OptimizationSettings {
  targetSuccessRate: number;
  personal: {
    selected: boolean;
    fields: {
      startingAge: boolean;
      endingAge: boolean;
    };
  };
  income: {
    selected: boolean;
    fields: {
      annualIncome: boolean;
      superannuation: boolean;
      partnerIncome: boolean;
    };
  };
  savings: {
    selected: boolean;
    fields: {
      savingsAmount: boolean;
      cashReserve: boolean;
      savingPercentage: boolean;
    };
  };
  expenditure: {
    selected: boolean;
    fields: {
      annualExpenses1: boolean;
      annualExpenses2: boolean;
    };
  };
  investment: {
    selected: boolean;
    fields: {
      initialInvestment: boolean;
      annualContribution: boolean;
      annualReturn: boolean;
    };
  };
  kiwiSaver: {
    selected: boolean;
    fields: {
      initialKiwiSaver: boolean;
      contribution: boolean;
      employerContribution: boolean;
      annualReturn: boolean;
    };
  };
  property: {
    selected: boolean;
    fields: {
      propertyValue: boolean;
      debt: boolean;
      propertyGrowth: boolean;
      debtInterestRate: boolean;
      initialDebtYears: boolean;
      additionalDebtRepayments: boolean;
    };
  };
}

export function OptimizationSettingsModal({ isOpen, onClose, onStartOptimization }: OptimizationSettingsModalProps) {
  const [targetSuccessRate, setTargetSuccessRate] = useState(80);
  const [settings, setSettings] = useState<Omit<OptimizationSettings, 'targetSuccessRate'>>({
    personal: {
      selected: true,
      fields: {
        startingAge: true,
        endingAge: true,
      },
    },
    income: {
      selected: true,
      fields: {
        annualIncome: true,
        superannuation: true,
        partnerIncome: true,
      },
    },
    savings: {
      selected: true,
      fields: {
        savingsAmount: true,
        cashReserve: true,
        savingPercentage: true,
      },
    },
    expenditure: {
      selected: true,
      fields: {
        annualExpenses1: true,
        annualExpenses2: true,
      },
    },
    investment: {
      selected: true,
      fields: {
        initialInvestment: true,
        annualContribution: true,
        annualReturn: true,
      },
    },
    kiwiSaver: {
      selected: true,
      fields: {
        initialKiwiSaver: true,
        contribution: true,
        employerContribution: true,
        annualReturn: true,
      },
    },
    property: {
      selected: true,
      fields: {
        propertyValue: true,
        debt: true,
        propertyGrowth: true,
        debtInterestRate: true,
        initialDebtYears: true,
        additionalDebtRepayments: true,
      },
    },
  });

  const handleSectionChange = (section: keyof typeof settings, checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        selected: checked,
        fields: Object.fromEntries(
          Object.entries(prev[section].fields).map(([key]) => [key, checked])
        ),
      },
    }));
  };

  const handleFieldChange = (section: keyof typeof settings, field: string, checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        fields: {
          ...prev[section].fields,
          [field]: checked,
        },
      },
    }));
  };

  const handleStartOptimization = () => {
    onStartOptimization({
      targetSuccessRate,
      ...settings
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Optimization Settings</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center gap-4">
            <Label htmlFor="targetSuccessRate">Target Success Rate (%)</Label>
            <Input
              id="targetSuccessRate"
              type="number"
              min={1}
              max={100}
              value={targetSuccessRate}
              onChange={(e) => setTargetSuccessRate(Number(e.target.value))}
              className="w-24"
            />
          </div>

          <div className="text-sm text-muted-foreground">
            Select which parameters the AI can modify to optimize your scenario:
          </div>

          <Accordion type="multiple" className="w-full">
            {Object.entries(settings).map(([section, data]) => (
              <AccordionItem value={section} key={section}>
                <AccordionTrigger className="flex items-center">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={data.selected}
                      onCheckedChange={(checked) => 
                        handleSectionChange(section as keyof typeof settings, checked as boolean)
                      }
                      onClick={(e) => e.stopPropagation()}
                    />
                    <Label>{section.charAt(0).toUpperCase() + section.slice(1)}</Label>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="pl-6 space-y-2">
                    {Object.entries(data.fields).map(([field, checked]) => (
                      <div key={field} className="flex items-center space-x-2">
                        <Checkbox
                          checked={checked}
                          onCheckedChange={(checked) => 
                            handleFieldChange(section as keyof typeof settings, field, checked as boolean)
                          }
                        />
                        <Label>{field.replace(/([A-Z])/g, ' $1').toLowerCase()}</Label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleStartOptimization}>
            Start Optimization
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}