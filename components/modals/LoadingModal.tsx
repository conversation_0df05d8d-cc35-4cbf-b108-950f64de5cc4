import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Loader2 } from 'lucide-react';

interface LoadingModalProps {
  isOpen: boolean;
  message: string;
}

export function LoadingModal({ isOpen, message }: LoadingModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-[425px]" hideCloseButton={true}>
        <div className="flex flex-col items-center justify-center p-4">
          <Loader2 className="h-8 w-8 animate-spin mb-4" />
          <p className="text-lg font-semibold">{message}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}