import React from 'react';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ScenarioSavedModalProps {
  isOpen: boolean;
  onClose: () => void;
  scenarioName: string;
  scenarioId: number;
}

const ScenarioSavedModal: React.FC<ScenarioSavedModalProps> = ({ isOpen, onClose, scenarioName, scenarioId }) => {
  const router = useRouter();

  const handleGoToScenario = () => {
    router.push(`/protected/planner?scenarioId=${scenarioId}`);
  };

  const handleGoBack = () => {
    router.push('/protected/scenarios');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{scenarioName} Created!</DialogTitle>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={handleGoToScenario}>Go to Scenario</Button>
          <Button variant="outline" onClick={handleGoBack}>Go Back</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ScenarioSavedModal;