"use client";

import { useState, useEffect } from 'react';
import { pdf } from '@react-pdf/renderer';
import { FinancialSummaryPDF } from '../FinancialSummaryPDF';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea"; // Import Textarea
import { Checkbox } from "@/components/ui/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, Copy, Lock } from "lucide-react";
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from '../ui/switch';
import * as bcrypt from 'bcryptjs';

interface Household {
  id: number;
  householdName: string;
}

interface Scenario {
  id: number;
  name: string;
}

interface GenerateDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  preselectedHousehold?: { id: number; householdName: string };
}

interface GeneratedLink {
  type: string;
  link: string;
}

interface AdviceScope {
  investment: boolean;
  kiwisaver: boolean;
  financialPlanning: boolean;
  estatePlanning: boolean;
  insurance: boolean;
  accountancy: boolean;
  budgeting: boolean;
}

interface FinancialSections {
  assets: boolean;
  liabilities: boolean;
  incomes: boolean;
  expenses: boolean;
  insurances: boolean;
  goals: boolean;
  recommendations: boolean;
}

const documentTypes = [
  { id: 'discovery', label: 'Discovery Document' },
  { id: 'toe', label: 'TOE' },
  { id: 'tpa', label: 'Third Party Authority' },
  { id: 'risk', label: 'Risk Profiler' },
  { id: 'financial', label: 'Financial Summary' }
];

export default function GenerateDocumentsModal({
  isOpen,
  onClose,
  onSuccess,
  preselectedHousehold
}: GenerateDocumentsModalProps) {
  const [households, setHouseholds] = useState<Household[]>([]);
  const [scenarios, setScenarios] = useState<Scenario[]>([]);
  const [selectedHousehold, setSelectedHousehold] = useState<string>('');
  const [selectedDocument, setSelectedDocument] = useState<string>('');
  const [selectedScenarios, setSelectedScenarios] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [templates, setTemplates] = useState<{id: string, title: string, type: string}[]>([]);
  const [riskProfilerTemplates, setRiskProfilerTemplates] = useState<{id: string, title: string, type: string}[]>([]);
  const [toeTemplates, setToeTemplates] = useState<{id: string, title: string, type: string}[]>([]);
  const [generatePDF, setGeneratePDF] = useState(false);
  const [generateWord, setGenerateWord] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedLinks, setGeneratedLinks] = useState<GeneratedLink[]>([]);
  const [showLinks, setShowLinks] = useState(false);
  const [openScenarioSelect, setOpenScenarioSelect] = useState(false);
  const [soaName, setSOAName] = useState<string>('');
  const [isCheckingName, setIsCheckingName] = useState(false);
  const [terms, setTerms] = useState<string>('');
  const [riskProfilerType, setRiskProfilerType] = useState<'10q' | '25q' | string>('10q');
  const [selectedMember, setSelectedMember] = useState<number | null>(null);
  const [householdMembers, setHouseholdMembers] = useState<{id: number, name: string}[]>([]);
  const [adviceScope, setAdviceScope] = useState<AdviceScope>({
    investment: false,
    kiwisaver: false,
    financialPlanning: false,
    estatePlanning: false,
    insurance: false,
    accountancy: false,
    budgeting: false,
  });
  // Fee states
  const [oneOffFee, setOneOffFee] = useState<string>('');
  const [isOneOffGstInclusive, setIsOneOffGstInclusive] = useState<boolean>(true);
  const [ongoingFee, setOngoingFee] = useState<string>('');
  const [isOngoingGstInclusive, setIsOngoingGstInclusive] = useState<boolean>(true);
  const [ongoingFeeType, setOngoingFeeType] = useState<'percentage' | 'fixed'>('percentage');

  // For backward compatibility
  const [adviceFee, setAdviceFee] = useState<string>('');
  const [isGstInclusive, setIsGstInclusive] = useState<boolean>(true);
  const [advisers, setAdvisers] = useState<{id: string, name: string, org_name?: string}[]>([]);
  const [selectedAdviser, setSelectedAdviser] = useState<string>("");
  const [tpaAdviserAddress, setTpaAdviserAddress] = useState<string>(""); // State for TPA adviser address
  const [isPasswordProtected, setIsPasswordProtected] = useState<boolean>(false);
  const [password, setPassword] = useState<string>("");
  const [financialSections, setFinancialSections] = useState<FinancialSections>({
    assets: true,
    liabilities: true,
    incomes: true,
    expenses: true,
    insurances: true,
    goals: true,
    recommendations: true
  });
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [templateContent, setTemplateContent] = useState<any[] | null>(null);
  const [hasCheckboxSection, setHasCheckboxSection] = useState(false);
  const [hasFeeSection, setHasFeeSection] = useState(false);
  const [hasTextSection, setHasTextSection] = useState(false);
  const { toast } = useToast();

  const fetchHouseholdMembers = async (householdId: string) => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('households')
      .select('members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household members:', error);
      return;
    }

    const members = [];
    if (data?.members) {
      // For members stored as name1/name2 format
      const memberData = data.members as { name1?: string, name2?: string };
      if (memberData.name1) {
        members.push({
          id: 1,
          name: memberData.name1
        });
      }
      if (memberData.name2) {
        members.push({
          id: 2,
          name: memberData.name2
        });
      }
    }

    setHouseholdMembers(members);
  };

  const fetchAdvisers = async () => {
    const supabase = createClient();

    // First get the current user's org_id
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user.id)
      .single();

    if (profileError || !userProfile?.org_id) {
      console.error('Error fetching user profile:', profileError);
      return;
    }
    // Then fetch all advisers in the same organization, including org_name
    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, user_id, org_name') // Include org_name
      .eq('org_id', userProfile.org_id)
      .order('name');

    if (error) {
      console.error('Error fetching advisers:', error);
      return;
    }

    if (data) {
      setAdvisers(data);
      // Set the current user as default if they're in the list
      const currentUserAdviser = data.find(adviser => adviser.user_id === user.id);
      if (currentUserAdviser) {
        setSelectedAdviser(currentUserAdviser.id.toString());
      } else if (data.length > 0) {
        setSelectedAdviser(data[0].id.toString());
      }
    }
  };

  useEffect(() => {
    const fetchHouseholds = async () => {
      if (preselectedHousehold) {
        setHouseholds([preselectedHousehold]);
        setSelectedHousehold(preselectedHousehold.id.toString());
        return;
      }

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching households:', error);
        return;
      }

      if (data) {
        setHouseholds(data);
      }
    };

    if (isOpen) {
      fetchHouseholds();
      setSelectedDocument('');
      setSelectedScenarios([]);
      setSelectedTemplate('default');
      setGeneratePDF(false);
      setGenerateWord(false);
      setGeneratedLinks([]);
      setShowLinks(false);
      setSOAName('');
      setTerms('');
      setTpaAdviserAddress(''); // Reset TPA address on open
      setIsPasswordProtected(false);
      setPassword('');
      setFinancialSections({
        assets: true,
        liabilities: true,
        incomes: true,
        expenses: true,
        insurances: true,
        goals: true,
        recommendations: true
      });
      fetchAdvisers();
    }
  }, [isOpen, preselectedHousehold]);

  useEffect(() => {
    const fetchScenarios = async () => {
      if (selectedHousehold && selectedDocument === 'soa') {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('scenarios_data1')
          .select('id, name')
          .eq('household_id', selectedHousehold)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching scenarios:', error);
        } else {
          setScenarios(data || []);
        }
      }
    };

    fetchScenarios();
  }, [selectedHousehold, selectedDocument]);

  // Fetch templates when document type is selected
  useEffect(() => {
    const fetchTemplates = async () => {
      const supabase = createClient();

      if (selectedDocument === 'discovery') {
        const { data, error } = await supabase
          .from('templates')
          .select('id, title, type')
          .eq('type', 'Discovery Document')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching discovery templates:', error);
        } else {
          setTemplates(data || []);
          // Reset selected template to default
          setSelectedTemplate('default');
        }
      } else if (selectedDocument === 'risk') {
        const { data, error } = await supabase
          .from('templates')
          .select('id, title, type')
          .eq('type', 'Risk Profiler')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching risk profiler templates:', error);
        } else {
          setRiskProfilerTemplates(data || []);
          // Reset selected template/type to 10q default
          setRiskProfilerType('10q');
        }
      } else if (selectedDocument === 'toe') {
        const { data, error } = await supabase
          .from('templates')
          .select('id, title, type')
          .eq('type', 'Terms of Engagement')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching TOE templates:', error);
        } else {
          setToeTemplates(data || []);
          // Reset selected template to default
          setSelectedTemplate('default');
          // Reset template content
          setTemplateContent(null);
          setHasCheckboxSection(false);
          setHasFeeSection(false);
          setHasTextSection(false);
        }
      }
    };

    fetchTemplates();
  }, [selectedDocument]);

  // State for custom checkbox options
  const [customCheckboxOptions, setCustomCheckboxOptions] = useState<{id: string, label: string, checked: boolean}[]>([]);

  // Fetch template content when a template is selected
  useEffect(() => {
    const fetchTemplateContent = async () => {
      // Reset states when document type changes
      if (selectedDocument !== 'toe') {
        setTemplateContent(null);
        setHasCheckboxSection(false);
        setHasFeeSection(false);
        setHasTextSection(false);
        setCustomCheckboxOptions([]);
        return;
      }

      // Handle default template
      if (selectedTemplate === 'default' || !selectedTemplate) {
        // Reset template content for default template
        setTemplateContent(null);
        setHasCheckboxSection(true); // Default template always has advice scope
        setHasFeeSection(true); // Default template always has fee section
        setHasTextSection(true); // Default template always has terms section
        setCustomCheckboxOptions([]);
        return;
      }

      // Only fetch content for TOE templates that are not the default
      if (selectedDocument === 'toe' && selectedTemplate && selectedTemplate !== 'default') {
        console.log('Fetching template content for:', selectedTemplate);
        const supabase = createClient();
        const { data, error } = await supabase
          .from('templates')
          .select('content')
          .eq('id', selectedTemplate)
          .single();

        if (error) {
          console.error('Error fetching template content:', error);
          setTemplateContent(null);
          setHasCheckboxSection(false);
          setHasFeeSection(false);
          setHasTextSection(false);
          setCustomCheckboxOptions([]);
          return;
        }

        if (data?.content) {
          try {
            const parsedContent = JSON.parse(data.content);
            setTemplateContent(parsedContent);

            // Check for specific section types
            let hasCheckbox = false;
            let hasFee = false;
            let hasText = false;
            let checkboxOptions: {id: string, label: string, checked: boolean}[] = [];

            if (Array.isArray(parsedContent)) {
              console.log('Template content:', parsedContent);
              parsedContent.forEach(section => {
                console.log('Section type:', section.type);
                if (section.type === 'checkbox') {
                  hasCheckbox = true;
                  // Extract checkbox options
                  if (section.content?.options && Array.isArray(section.content.options)) {
                    console.log('Checkbox options:', section.content.options);
                    checkboxOptions = section.content.options.map((option: any) => ({
                      id: option.id,
                      label: option.label,
                      checked: false // Default to unchecked
                    }));
                  }
                }
                if (section.type === 'fee') {
                  hasFee = true;
                  // Set ongoing fee type if present
                  if (section.content?.showOngoing && section.content?.ongoingType) {
                    setOngoingFeeType(section.content.ongoingType as 'percentage' | 'fixed');
                  }
                }
                if (section.type === 'text') hasText = true;
              });
            }

            console.log('Has checkbox:', hasCheckbox);
            console.log('Has fee:', hasFee);
            console.log('Has text:', hasText);
            console.log('Checkbox options:', checkboxOptions);

            setHasCheckboxSection(hasCheckbox);
            setHasFeeSection(hasFee);
            setHasTextSection(hasText);
            setCustomCheckboxOptions(checkboxOptions);

            // Reset advice scope to match custom options
            if (checkboxOptions.length > 0) {
              const newAdviceScope: any = {};
              // Initialize all standard options to false
              Object.keys(adviceScope).forEach(key => {
                newAdviceScope[key] = false;
              });
              // Set options that match custom options to false (will be toggled by user)
              checkboxOptions.forEach(option => {
                if (Object.keys(adviceScope).includes(option.id)) {
                  newAdviceScope[option.id] = false;
                }
              });
              setAdviceScope(newAdviceScope);
            }
          } catch (e) {
            console.error('Error parsing template content:', e);
            setTemplateContent(null);
            setHasCheckboxSection(false);
            setHasFeeSection(false);
            setHasTextSection(false);
            setCustomCheckboxOptions([]);
          }
        }
      }
    };

    fetchTemplateContent();
  }, [selectedDocument, selectedTemplate]);

  const checkSOANameExists = async (name: string, householdId: string) => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('soa_documents')
      .select('id')
      .eq('household_id', parseInt(householdId))
      .ilike('file_name', `%${name}%`)
      .limit(1);

    if (error) {
      console.error('Error checking SOA name:', error);
      throw error;
    }

    return data && data.length > 0;
  };

  const generateSOA = async (format: 'pdf' | 'docx') => {
    const supabase = createClient();
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate file name using the custom SOA name
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
      const sanitizedSOAName = soaName.replace(/[^a-zA-Z0-9]/g, '_');
      const fileName = `${sanitizedSOAName}_${timestamp}.${format}`;

      // Create a Blob with the dummy text
      const content = 'This is a dummy SOA';
      let blob;

      if (format === 'pdf') {
        // For PDF, we'll use a simple text PDF
        const pdfContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/MediaBox[0 0 612 792]/Parent 2 0 R/Resources<<>>/Contents 4 0 R>>endobj
4 0 obj<</Length 51>>stream
BT /F1 12 Tf 72 720 Td (${content}) Tj ET
endstream
endobj
xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000052 00000 n
0000000101 00000 n
0000000192 00000 n
trailer<</Size 5/Root 1 0 R>>
startxref
292
%%EOF`;
        blob = new Blob([pdfContent], { type: 'application/pdf' });
      } else {
        // For Word, we'll use a simple text file with .docx extension
        blob = new Blob([content], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      }

      // Create a download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      // Save the document record
      const { error } = await supabase
        .from('soa_documents')
        .insert([
          {
            household_id: parseInt(selectedHousehold),
            scenario_ids: selectedScenarios,
            created_by: user.id,
            file_name: fileName,
            file_type: format,
            file_size: blob.size
          }
        ]);

      if (error) throw error;
    } catch (error) {
      console.error(`Error generating SOA ${format}:`, error);
      throw error;
    }
  };

  const generateDiscoveryLink = async (householdId: string) => {
    const supabase = createClient();
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate a unique token
      const token = Math.random().toString(36).substring(2) + Date.now().toString(36);

      // Hash password if password protection is enabled
      let passwordHash = null;
      if (isPasswordProtected && password.trim()) {
        passwordHash = await bcrypt.hash(password, 10);
      }

      // Save the token and associate it with this household
      // Create the base token data
      const tokenData: any = {
        token: token,
        household_id: parseInt(householdId),
        created_at: new Date().toISOString(),
        status: 'pending',
        created_by: user.id,
        password_hash: passwordHash
      };

      // Only add template_id if a template is selected (not default)
      // This is to handle cases where the template_id column might not exist yet
      if (selectedTemplate && selectedTemplate !== 'default') {
        try {
          // First check if the template_id column exists
          const { data: templateCheck, error: templateCheckError } = await supabase
            .from('templates')
            .select('id')
            .eq('id', selectedTemplate)
            .single();

          if (!templateCheckError && templateCheck) {
            // Template exists, add it to the token data
            tokenData.template_id = selectedTemplate;
          }
        } catch (e) {
          console.error('Error checking template:', e);
          // Continue without template_id if there's an error
        }
      }

      // Insert the token
      const { error } = await supabase
        .from('discovery_tokens')
        .insert([tokenData]);

      if (error) throw error;

      // Generate the full URL
      const baseUrl = window.location.origin;
      const discoveryLink = `${baseUrl}/discovery-form/${token}`;

      return discoveryLink;
    } catch (error) {
      console.error('Error generating discovery link:', error);
      throw error;
    }
  };

  const generateTOELink = async (householdId: string) => {
    const supabase = createClient();
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Get household details including members data
      const { data: household, error: householdError } = await supabase
        .from('households')
        .select('householdName, members')
        .eq('id', householdId)
        .single();

      if (householdError || !household) {
        throw new Error('Could not fetch household details');
      }

      // Generate a unique token using Web Crypto API
      const tokenBytes = new Uint8Array(16);
      window.crypto.getRandomValues(tokenBytes);
      const token = Array.from(tokenBytes)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      // Set expiry to 7 days from now
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Hash password if password protection is enabled
      let passwordHash = null;
      if (isPasswordProtected && password.trim()) {
        passwordHash = await bcrypt.hash(password, 10);
      }

      // Create the base token data
      const tokenData: any = {
        household_id: parseInt(householdId),
        token,
        created_by: user.id,
        adviser_id: selectedAdviser ? parseInt(selectedAdviser) : null,
        expires_at: expiresAt.toISOString(),
        client_name: household.householdName,
        password_hash: passwordHash
      };

      // Include form fields based on template sections
      if (selectedTemplate === 'default' || !selectedTemplate) {
        // For default template, include all fields
        tokenData.terms = terms;
        tokenData.advice_scope = adviceScope;

        // For default template, we only use one-off fee (for backward compatibility)
        tokenData.advice_fee = oneOffFee ? parseFloat(oneOffFee) : null;
        tokenData.is_gst_inclusive = isOneOffGstInclusive;
      } else {
        // For custom templates, include fields based on section types
        console.log('Generating TOE link with custom template');
        console.log('hasTextSection:', hasTextSection);
        console.log('hasCheckboxSection:', hasCheckboxSection);
        console.log('hasFeeSection:', hasFeeSection);

        if (hasTextSection) {
          tokenData.terms = terms;
          console.log('Including terms:', terms);
        }
        if (hasCheckboxSection) {
          // For custom templates with checkbox section, only include the options that are in the template
          if (customCheckboxOptions.length > 0) {
            const customAdviceScope: any = {};
            customCheckboxOptions.forEach(option => {
              customAdviceScope[option.id] = adviceScope[option.id as keyof typeof adviceScope] || false;
            });
            tokenData.advice_scope = customAdviceScope;
            console.log('Including custom advice scope:', customAdviceScope);
          } else {
            tokenData.advice_scope = adviceScope;
            console.log('Including advice scope:', adviceScope);
          }
        }
        if (hasFeeSection) {
          // Check if the template has fee sections
          const feeSection = templateContent?.find((section: any) => section.type === 'fee');

          if (feeSection?.content?.showOneOff) {
            tokenData.advice_fee = oneOffFee ? parseFloat(oneOffFee) : null;
            tokenData.is_gst_inclusive = isOneOffGstInclusive;
            console.log('Including one-off fee:', oneOffFee, isOneOffGstInclusive);

            // For backward compatibility
            tokenData.advice_fee = oneOffFee ? parseFloat(oneOffFee) : null;
            tokenData.is_gst_inclusive = isOneOffGstInclusive;
          }

          if (feeSection?.content?.showOngoing) {
            tokenData.ongoing_fee = ongoingFee ? parseFloat(ongoingFee) : null;
            tokenData.ongoing_fee_type = ongoingFeeType;
            tokenData.is_ongoing_gst_inclusive = isOngoingGstInclusive;
            console.log('Including ongoing fee:', ongoingFee, ongoingFeeType, isOngoingGstInclusive);
          }
        }
      }

      // Add template_id if a custom template is selected
      if (selectedTemplate && selectedTemplate !== 'default') {
        try {
          // First check if the template exists
          const { data: templateCheck, error: templateCheckError } = await supabase
            .from('templates')
            .select('id')
            .eq('id', selectedTemplate)
            .single();

          if (!templateCheckError && templateCheck) {
            // Template exists, add it to the token data
            tokenData.template_id = selectedTemplate;
          }
        } catch (e) {
          console.error('Error checking template:', e);
          // Continue without template_id if there's an error
        }
      }

      // Insert the token
      const { error } = await supabase
        .from('toe_tokens')
        .insert([tokenData]);

      if (error) throw error;

      // Generate the form URL
      const baseUrl = window.location.origin;
      return `${baseUrl}/toe-form/${token}`;
    } catch (error) {
      console.error('Error generating TOE link:', error);
      throw error;
    }
  };

  const generateTPALink = async (householdId: string) => {
    const supabase = createClient();
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Get household details
      const { data: household, error: householdError } = await supabase
        .from('households')
        .select('householdName')
        .eq('id', householdId)
        .single();

      if (householdError || !household) {
        throw new Error('Could not fetch household details');
      }

      // Generate a unique token using Web Crypto API
      const tokenBytes = new Uint8Array(16);
      window.crypto.getRandomValues(tokenBytes);
      const token = Array.from(tokenBytes)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      // Set expiry to 7 days from now
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Hash password if password protection is enabled
      let passwordHash = null;
      if (isPasswordProtected && password.trim()) {
        passwordHash = await bcrypt.hash(password, 10);
      }

      // Fetch the selected adviser's details directly from the database
      if (!selectedAdviser) {
         throw new Error('No adviser selected');
      }

      const adviserId = parseInt(selectedAdviser, 10); // Convert string ID to integer
      if (isNaN(adviserId)) {
        throw new Error(`Invalid adviser ID format: ${selectedAdviser}`);
      }

      const { data: adviserProfile, error: adviserError } = await supabase
        .from('profiles')
        .select('name, org_name')
        .eq('id', adviserId) // Use the integer ID for comparison
        .single();

      if (adviserError || !adviserProfile) {
        console.error('Error fetching adviser profile:', adviserError);
        throw new Error(`Could not fetch adviser profile for ID: ${selectedAdviser}`);
      }

      // Create TPA token record with adviser details and address
      const { error } = await supabase
        .from('tpa_tokens')
        .insert([
          {
            household_id: parseInt(householdId),
            token,
            created_by: user.id,
            adviser_id: parseInt(selectedAdviser), // Store adviser ID
            adviser_name: adviserProfile.name, // Store adviser name
            adviser_org_name: adviserProfile.org_name || '', // Store adviser org name
            adviser_address: tpaAdviserAddress, // Store adviser address
            expires_at: expiresAt.toISOString(),
            client_name: household.householdName,
            address: 'Enter Address...', // Provide a non-null default for client address
            member_id: selectedMember, // Add the selected member ID
            password_hash: passwordHash
          }
        ]);

      if (error) throw error;

      // Generate the form URL
      const baseUrl = window.location.origin;
      return `${baseUrl}/tpa-form/${token}`;
    } catch (error) {
      console.error('Error generating TPA link:', error);
      throw error;
    }
  };

  const generateRiskProfilerLink = async (householdId: string) => {
    const supabase = createClient();
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }

      // Generate a unique token
      const token = Math.random().toString(36).substring(2) + Date.now().toString(36);

      // Hash password if password protection is enabled
      let passwordHash = null;
      if (isPasswordProtected && password.trim()) {
        passwordHash = await bcrypt.hash(password, 10);
      }

      // Create the base token data
      const tokenData: any = {
        token: token,
        household_id: parseInt(householdId),
        member_id: selectedMember, // Add the selected member ID
        created_at: new Date().toISOString(),
        status: 'pending',
        created_by: user.id,
        responses: null,
        risk_score: null,
        completed_at: null,
        password_hash: passwordHash
      };

      // Handle the risk profiler type and template
      if (riskProfilerType === '10q' || riskProfilerType === '25q') {
        // This is a default template
        tokenData.profiler_type = riskProfilerType;
        // No template_id for default templates
      } else {
        // This is a custom template
        try {
          // First check if the template exists
          const { data: templateCheck, error: templateCheckError } = await supabase
            .from('templates')
            .select('id')
            .eq('id', riskProfilerType)
            .single();

          if (!templateCheckError && templateCheck) {
            // Template exists, add it to the token data
            tokenData.template_id = riskProfilerType;
            // Set a default profiler_type (needed for backward compatibility)
            tokenData.profiler_type = '10q';
          } else {
            // Template doesn't exist, fall back to default
            tokenData.profiler_type = '10q';
          }
        } catch (e) {
          console.error('Error checking template:', e);
          // Continue with default if there's an error
          tokenData.profiler_type = '10q';
        }
      }

      // Insert the token
      const { error } = await supabase
        .from('risk_profiler_tokens')
        .insert([tokenData]);

      if (error) throw error;

      // Generate the full URL
      const baseUrl = window.location.origin;
      const riskProfilerLink = `${baseUrl}/risk-profiler-form/${token}`;

      return riskProfilerLink;
    } catch (error) {
      console.error('Error generating risk profiler link:', error);
      throw error;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied",
        description: "Link copied to clipboard"
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to copy link"
      });
    }
  };

  const generateFinancialSummary = async (householdId: string) => {
    setIsGeneratingPDF(true);
    const supabase = createClient();

    try {
      // Fetch household name
      const { data: household, error: householdError } = await supabase
        .from('households')
        .select('householdName')
        .eq('id', householdId)
        .single();

      if (householdError) {
        throw new Error('Could not fetch household details');
      }

      // Fetch assets
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('*')
        .eq('household_id', householdId);

      if (assetsError) {
        console.error('Error fetching assets:', assetsError);
      }

      // Fetch liabilities
      const { data: liabilities, error: liabilitiesError } = await supabase
        .from('liabilities')
        .select('*')
        .eq('household_id', householdId);

      if (liabilitiesError) {
        console.error('Error fetching liabilities:', liabilitiesError);
      }

      // Fetch incomes with specific column selection
      const { data: incomes, error: incomesError } = await supabase
        .from('income')
        .select('id, amount, frequency, source, description, start_date, end_date, is_guaranteed, household_id, created_at, member_id')
        .eq('household_id', householdId);

      if (incomesError) {
        console.error('Error fetching incomes:', incomesError);
      }

      // Fetch expenses with specific column selection
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select('id, amount, frequency, category, description, is_essential, notes, household_id, created_at, linked_liability_id, member_id')
        .eq('household_id', householdId);

      if (expensesError) {
        console.error('Error fetching expenses:', expensesError);
      }

      // Fetch insurances with specific column selection
      const { data: insurances, error: insurancesError } = await supabase
        .from('insurances')
        .select('id, type, provider, coverage_amount, premium, deductible, household_id, created_at, policy_number, frequency, renewal_date')
        .eq('household_id', householdId);

      // Map the insurance data to include default values for missing fields
      const mappedInsurances = insurances?.map(insurance => ({
        ...insurance,
        policy_number: insurance.policy_number || '',
        frequency: insurance.frequency || 'annual',
        renewal_date: insurance.renewal_date || null
      })) || [];

      if (insurancesError) {
        console.error('Error fetching insurances:', insurancesError);
      }

      // Fetch goals
      const { data: goals, error: goalsError } = await supabase
        .from('goals')
        .select('*')
        .eq('household_id', householdId);

      if (goalsError) {
        console.error('Error fetching goals:', goalsError);
      }

      // Fetch recommendations
      const { data: recommendations, error: recommendationsError } = await supabase
        .from('recommendations')
        .select('*')
        .eq('household_id', householdId);

      if (recommendationsError) {
        console.error('Error fetching recommendations:', recommendationsError);
      }

      // Process household members for incomes
      const { data: householdData, error: householdMembersError } = await supabase
        .from('households')
        .select('members')
        .eq('id', householdId)
        .single();

      let memberMap: {[key: string]: string} = {};
      if (!householdMembersError && householdData?.members) {
        const members = householdData.members;
        memberMap['0'] = 'Household';
        if (members.name1) memberMap['1'] = members.name1;
        if (members.name2) memberMap['2'] = members.name2;
      }

      // Add member names to incomes
      const processedIncomes = incomes?.map(income => {
        let memberId = '0'; // Default to household
        if (income.member_id !== undefined && income.member_id !== null) {
          memberId = income.member_id.toString();
        }
        return {
          ...income,
          member_name: memberMap[memberId] || `Member ${memberId}`
        };
      }) || [];

      // Generate PDF with error handling
      try {
        const pdfDoc = await pdf(
          <FinancialSummaryPDF
            householdName={household.householdName}
            assets={assets || []}
            liabilities={liabilities || []}
            incomes={processedIncomes}
            expenses={(expenses || []).map(expense => ({
              id: expense.id,
              name: expense.description || expense.category || 'Unnamed Expense', // Add name field
              category: expense.category,
              amount: expense.amount,
              frequency: expense.frequency,
              details: expense.notes,
              household_id: expense.household_id,
              linked_liability_id: expense.linked_liability_id
            }))}
            insurances={insurances || []}
            goals={goals || []}
            recommendations={recommendations || []}
            showAssets={financialSections.assets}
            showLiabilities={financialSections.liabilities}
            showIncomes={financialSections.incomes}
            showExpenses={financialSections.expenses}
            showInsurances={financialSections.insurances}
            showGoals={financialSections.goals}
            showRecommendations={financialSections.recommendations}
            createdAt={new Date().toISOString()}
          />
        ).toBlob();

        // Generate file name
        const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
        const fileName = `Financial_Summary_${household.householdName.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.pdf`;

        // Create download link
        const url = URL.createObjectURL(pdfDoc);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        return fileName;
      } catch (pdfError) {
        console.error('PDF generation error:', pdfError);
        toast({
          variant: "destructive",
          title: "PDF Generation Error",
          description: "There was an error generating the PDF. Please try again or contact support."
        });
        throw new Error('PDF generation failed: ' + ((pdfError as Error)?.message || 'Unknown error'));
      }

      // This section is now handled inside the try/catch block above
    } catch (error) {
      console.error('Error generating financial summary:', error);
      throw error;
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleGenerate = async () => {
    if (!selectedHousehold) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a household"
      });
      return;
    }

    if (selectedDocument === 'tpa') {
      if (!selectedAdviser) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please select an adviser"
        });
        return;
      }
      if (!tpaAdviserAddress.trim()) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please enter the adviser's address"
        });
        return;
      }
    } else if (selectedDocument === 'soa') {
      if (!soaName.trim()) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please enter an SOA name"
        });
        return;
      }

      setIsCheckingName(true);
      try {
        const exists = await checkSOANameExists(soaName, selectedHousehold);
        if (exists) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "An SOA with this name already exists for this household"
          });
          setIsCheckingName(false);
          return;
        }
      } catch (error) {
        console.error('Error checking SOA name:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to check SOA name"
        });
        setIsCheckingName(false);
        return;
      }
      setIsCheckingName(false);
    } else if (selectedDocument === 'financial') {
      // Check if at least one section is selected
      const hasSelectedSection = Object.values(financialSections).some(value => value === true);
      if (!hasSelectedSection) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please select at least one section to include in the financial summary"
        });
        return;
      }
    }

    setIsGenerating(true);
    const links: GeneratedLink[] = [];

    try {
      if (selectedDocument === 'discovery') {
        const link = await generateDiscoveryLink(selectedHousehold);
        links.push({ type: 'Discovery Document', link });
        setShowLinks(true);
      } else if (selectedDocument === 'toe') {
        const link = await generateTOELink(selectedHousehold);
        links.push({ type: 'Terms of Engagement', link });
        setShowLinks(true);
      } else if (selectedDocument === 'tpa') {
        const link = await generateTPALink(selectedHousehold);
        links.push({ type: 'Third Party Authority', link });
        setShowLinks(true);
      } else if (selectedDocument === 'soa') {
        if (!generatePDF && !generateWord) {
          throw new Error('Please select at least one format (PDF or Word)');
        }

        if (!selectedScenarios || selectedScenarios.length === 0) {
          throw new Error('Please select at least one scenario');
        }

        if (generatePDF) {
          await generateSOA('pdf');
        }
        if (generateWord) {
          await generateSOA('docx');
        }
      } else if (selectedDocument === 'risk') {
        const link = await generateRiskProfilerLink(selectedHousehold);
        links.push({ type: 'Risk Profiler', link });
        setShowLinks(true);
      } else if (selectedDocument === 'financial') {
        await generateFinancialSummary(selectedHousehold);
        toast({
          title: "Success",
          description: "Financial summary generated successfully"
        });
      }

      setGeneratedLinks(links);
      if (links.length > 0) {
        toast({
          title: "Success",
          description: "Link generated successfully"
        });
        setShowLinks(true);
      }

      // Call the onSuccess callback to refresh the table
      onSuccess?.();
    } catch (error) {
      console.error('Error generating document:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate document"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    if (selectedHousehold) {
      fetchHouseholdMembers(selectedHousehold);
    } else {
      setHouseholdMembers([]);
    }
  }, [selectedHousehold]);

  // Ensure component doesn't render prematurely if essential data isn't ready (though unlikely needed here)
  // if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`overflow-hidden ${selectedDocument === 'toe' ? 'sm:max-w-[80vw]' : 'sm:max-w-[425px]'}`}>
        <DialogHeader>
          <DialogTitle>Generate Documents</DialogTitle>
          <DialogDescription>
            Generate documents for a household
          </DialogDescription>
        </DialogHeader>

        {!showLinks ? (
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="household">Household</Label>
              {preselectedHousehold ? (
                <Input
                  id="household"
                  value={preselectedHousehold.householdName}
                  disabled
                  className="bg-muted"
                />
              ) : (
                <Select
                  value={selectedHousehold}
                  onValueChange={setSelectedHousehold}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a household" />
                  </SelectTrigger>
                  <SelectContent>
                    {households.map((household) => (
                      <SelectItem key={household.id} value={household.id.toString()}>
                        {household.householdName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label>Select Document Type</Label>
                <Select
                  value={selectedDocument}
                  onValueChange={setSelectedDocument}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a document type" />
                  </SelectTrigger>
                  <SelectContent>
                    {documentTypes.map((doc) => (
                      <SelectItem key={doc.id} value={doc.id}>
                        {doc.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

            </div>

            {/* Template Selection for Discovery Document */}
            {selectedDocument === 'discovery' && (
              <div className="grid gap-2 mt-4">
                <Label>Select Template</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Template</SelectItem>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Select a template for the discovery document or use the default template.
                </p>
              </div>
            )}

            {/* Password Protection Switch */}
            {selectedDocument !== 'soa' && selectedDocument !== 'financial' && (
              <div className="grid gap-2 mt-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="passwordProtection" className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Password Protect
                  </Label>
                  <Switch
                    id="passwordProtection"
                    checked={isPasswordProtected}
                    onCheckedChange={setIsPasswordProtected}
                  />
                </div>
                {isPasswordProtected && (
                  <div className="mt-2">
                    <Input
                      type="password"
                      placeholder="Enter password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      This password will be required to access the form before submission.
                    </p>
                  </div>
                )}
              </div>
            )}

            {selectedDocument === 'soa' && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="soaName">SOA Name</Label>
                  <Input
                    id="soaName"
                    value={soaName}
                    onChange={(e) => setSOAName(e.target.value)}
                    placeholder="Enter SOA name"
                    disabled={isCheckingName}
                  />
                </div>
                <div className="grid gap-2">
                  <Label>Select Scenarios</Label>
                  <Popover open={openScenarioSelect} onOpenChange={setOpenScenarioSelect}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openScenarioSelect}
                        className="justify-between"
                      >
                        {selectedScenarios.length === 0
                          ? "Select scenarios..."
                          : `${selectedScenarios.length} selected`}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0">
                      <Command>
                        <CommandInput placeholder="Search scenarios..." />
                        <CommandEmpty>No scenarios found.</CommandEmpty>
                        <CommandGroup>
                          {scenarios.map((scenario) => (
                            <CommandItem
                              key={scenario.id}
                              onSelect={() => {
                                setSelectedScenarios(current =>
                                  current.includes(scenario.id.toString())
                                    ? current.filter(id => id !== scenario.id.toString())
                                    : [...current, scenario.id.toString()]
                                );
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedScenarios.includes(scenario.id.toString())
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {scenario.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid gap-2">
                  <Label>Document Format</Label>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pdf"
                        checked={generatePDF}
                        onCheckedChange={(checked) => setGeneratePDF(checked as boolean)}
                      />
                      <Label htmlFor="pdf">Generate PDF</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="word"
                        checked={generateWord}
                        onCheckedChange={(checked) => setGenerateWord(checked as boolean)}
                      />
                      <Label htmlFor="word">Generate Word Document</Label>
                    </div>
                  </div>
                </div>
              </>
            )}

            {selectedDocument === 'financial' && (
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label>Sections to Include</Label>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeAssets">Assets</Label>
                      <Switch
                        id="includeAssets"
                        checked={financialSections.assets}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, assets: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeLiabilities">Liabilities</Label>
                      <Switch
                        id="includeLiabilities"
                        checked={financialSections.liabilities}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, liabilities: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeIncomes">Income</Label>
                      <Switch
                        id="includeIncomes"
                        checked={financialSections.incomes}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, incomes: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeExpenses">Expenses</Label>
                      <Switch
                        id="includeExpenses"
                        checked={financialSections.expenses}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, expenses: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeInsurances">Insurance</Label>
                      <Switch
                        id="includeInsurances"
                        checked={financialSections.insurances}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, insurances: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeGoals">Goals</Label>
                      <Switch
                        id="includeGoals"
                        checked={financialSections.goals}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, goals: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="includeRecommendations">Recommendations</Label>
                      <Switch
                        id="includeRecommendations"
                        checked={financialSections.recommendations}
                        onCheckedChange={(checked) =>
                          setFinancialSections(prev => ({ ...prev, recommendations: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  This will generate a comprehensive financial summary document for the selected household.
                  Toggle the switches to include or exclude specific sections.
                </p>
              </div>
            )}
            {selectedDocument === 'toe' && (
              <>
              <div className="grid gap-2 mt-4">
                <Label>Select Template</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Template</SelectItem>
                    {toeTemplates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Select a template for the Terms of Engagement or use the default template.
                </p>
              </div>

              {/* Always show adviser selection regardless of template */}
              <div className="grid gap-2">
                <Label>Adviser</Label>
                <Select
                  value={selectedAdviser}
                  onValueChange={setSelectedAdviser}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an adviser" />
                  </SelectTrigger>
                  <SelectContent>
                    {advisers.map((adviser) => (
                      <SelectItem key={adviser.id} value={adviser.id.toString()}>
                        {adviser.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Show advice scope section if default template or custom template with checkbox section */}
              {(selectedTemplate === 'default' || hasCheckboxSection) && (
                <div className="grid gap-2 mt-4">
                  <Label>Advice Scope</Label>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    {/* Show default checkboxes for default template */}
                    {selectedTemplate === 'default' && (
                      <>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="investment"
                            checked={adviceScope.investment}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, investment: checked === true }))
                            }
                          />
                          <Label htmlFor="investment">Investment Management</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="kiwisaver"
                            checked={adviceScope.kiwisaver}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, kiwisaver: checked === true }))
                            }
                          />
                          <Label htmlFor="kiwisaver">KiwiSaver</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="financialPlanning"
                            checked={adviceScope.financialPlanning}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, financialPlanning: checked === true }))
                            }
                          />
                          <Label htmlFor="financialPlanning">Financial Planning</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="estatePlanning"
                            checked={adviceScope.estatePlanning}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, estatePlanning: checked === true }))
                            }
                          />
                          <Label htmlFor="estatePlanning">Estate Planning</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="insurance"
                            checked={adviceScope.insurance}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, insurance: checked === true }))
                            }
                          />
                          <Label htmlFor="insurance">Insurance</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="accountancy"
                            checked={adviceScope.accountancy}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, accountancy: checked === true }))
                            }
                          />
                          <Label htmlFor="accountancy">Accountancy</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="budgeting"
                            checked={adviceScope.budgeting}
                            onCheckedChange={(checked) =>
                              setAdviceScope(prev => ({ ...prev, budgeting: checked === true }))
                            }
                          />
                          <Label htmlFor="budgeting">Budgeting</Label>
                        </div>
                      </>
                    )}

                    {/* Show custom checkboxes for custom template */}
                    {selectedTemplate !== 'default' && customCheckboxOptions.length > 0 && (
                      <>
                        {customCheckboxOptions.map(option => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={option.id}
                              checked={adviceScope[option.id as keyof typeof adviceScope] || false}
                              onCheckedChange={(checked) =>
                                setAdviceScope(prev => ({ ...prev, [option.id]: checked === true }))
                              }
                            />
                            <Label htmlFor={option.id}>{option.label}</Label>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Show terms section if default template or custom template with text section */}
              {(selectedTemplate === 'default' || hasTextSection) && (
                <div className="grid gap-2 mt-4">
                  <Label htmlFor="terms">Terms</Label>
                  <textarea
                    id="terms"
                    value={terms}
                    onChange={(e) => setTerms(e.target.value)}
                    placeholder="Enter terms of engagement..."
                    className="min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              )}

              {/* Show fee section if default template or custom template with fee section */}
              {(selectedTemplate === 'default' || hasFeeSection) && (
                <div className="grid gap-4 mt-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Fee Information</h3>

                    {/* One-off fee section */}
                    {(selectedTemplate === 'default' ||
                      (templateContent && templateContent.some((section: any) =>
                        section.type === 'fee' && section.content?.showOneOff))) && (
                      <div className="border rounded-md p-4 mb-4">
                        <Label htmlFor="oneOffFee" className="font-medium">One-Off Advice Fee</Label>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="relative flex-grow">
                            <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                            <Input
                              id="oneOffFee"
                              type="number"
                              value={oneOffFee}
                              onChange={(e) => {
                                setOneOffFee(e.target.value);
                                // For backward compatibility
                                setAdviceFee(e.target.value);
                              }}
                              className="pl-7"
                              placeholder="0.00"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <Switch
                              id="oneOffGstSwitch"
                              checked={isOneOffGstInclusive}
                              onCheckedChange={(checked) => {
                                setIsOneOffGstInclusive(checked);
                                // For backward compatibility
                                setIsGstInclusive(checked);
                              }}
                            />
                            <Label htmlFor="oneOffGstSwitch">
                              {isOneOffGstInclusive ? "Incl GST" : "Excl GST"}
                            </Label>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Ongoing fee section */}
                    {(selectedTemplate !== 'default' &&
                      templateContent && templateContent.some((section: any) =>
                        section.type === 'fee' && section.content?.showOngoing)) && (
                      <div className="border rounded-md p-4">
                        <Label htmlFor="ongoingFee" className="font-medium">Ongoing Advice Fee</Label>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="relative flex-grow">
                            {/* Show % or $ based on fee type */}
                            <span className="absolute left-3 top-1/2 -translate-y-1/2">
                              {ongoingFeeType === 'percentage' ? '%' : '$'}
                            </span>
                            <Input
                              id="ongoingFee"
                              type="number"
                              value={ongoingFee}
                              onChange={(e) => setOngoingFee(e.target.value)}
                              className="pl-7"
                              placeholder="0.00"
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <Switch
                              id="ongoingGstSwitch"
                              checked={isOngoingGstInclusive}
                              onCheckedChange={setIsOngoingGstInclusive}
                            />
                            <Label htmlFor="ongoingGstSwitch">
                              {isOngoingGstInclusive ? "Incl GST" : "Excl GST"}
                            </Label>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          {ongoingFeeType === 'percentage' ?
                            'Percentage of assets under management per annum' :
                            'Fixed monthly fee'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Message when custom template is selected but no editable sections are found */}
              {selectedTemplate !== 'default' && selectedTemplate !== '' &&
               !hasCheckboxSection && !hasFeeSection && !hasTextSection && (
                <div className="mt-4 p-4 bg-blue-50 text-blue-800 rounded-md">
                  <p className="text-sm">Using custom template. The form will be generated with the template's predefined content.</p>
                </div>
              )}
              </>
            )}
            {selectedDocument === 'risk' && selectedHousehold && (
              <div className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="riskProfilerType">Risk Profiler Template</Label>
                  <Select
                    value={riskProfilerType}
                    onValueChange={setRiskProfilerType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select risk profiler template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Default Templates</SelectLabel>
                        <SelectItem value="10q">10-Question Assessment (Default)</SelectItem>
                        <SelectItem value="25q">25-Question Assessment (Default)</SelectItem>
                      </SelectGroup>

                      {riskProfilerTemplates.length > 0 && (
                        <SelectGroup>
                          <SelectLabel>Custom Templates</SelectLabel>
                          {riskProfilerTemplates.map((template) => (
                            <SelectItem key={template.id} value={template.id}>
                              {template.title}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      )}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Select a risk profiler template. You can use the default 10 or 25 question assessments or a custom template.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="memberSelect">For Member</Label>
                  <Select
                    value={selectedMember?.toString() || "none"}
                    onValueChange={(value) => setSelectedMember(value === "none" ? null : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select household member" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Household (General)</SelectItem>
                      {householdMembers.map(member => (
                        <SelectItem key={member.id} value={member.id.toString()}>
                          {member.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
            {selectedDocument === 'tpa' && (
              <> {/* Add fragment */}
                <div className="grid gap-2">
                  <Label>Adviser</Label>
                  <Select
                    value={selectedAdviser}
                    onValueChange={setSelectedAdviser}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an adviser" />
                    </SelectTrigger>
                    <SelectContent>
                      {advisers.map((adviser) => (
                        <SelectItem key={adviser.id} value={adviser.id.toString()}>
                          {adviser.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="memberSelect">For Member</Label>
                  <Select
                    value={selectedMember?.toString() || "none"}
                    onValueChange={(value) => setSelectedMember(value === "none" ? null : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select household member" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Household (General)</SelectItem>
                      {householdMembers.map(member => (
                        <SelectItem key={member.id} value={member.id.toString()}>
                          {member.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2"> {/* Move address input here */}
                  <Label htmlFor="tpaAdviserAddress">Adviser Address</Label>
                  <Textarea
                    id="tpaAdviserAddress"
                    value={tpaAdviserAddress}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setTpaAdviserAddress(e.target.value)} // Add type
                    placeholder="Enter the adviser's full address"
                    className="min-h-[100px]"
                  />
                </div>
              </>
            )}
          </div>
        ) : (
          <div className="space-y-4 py-4 pb-4">
            {generatedLinks.length > 0 && (
              <div className="space-y-4">
                <h3 className="font-medium">Generated Links</h3>
                {generatedLinks.map((link, index) => (
                  <div key={index} className="flex flex-col space-y-2 p-2 border rounded-md">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-sm">{link.type}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(link.link)}
                        className="flex items-center gap-2 shrink-0"
                      >
                        <Copy className="h-4 w-4" />
                        Copy
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground break-all pr-2">
                      {link.link}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        <DialogFooter>
          {!showLinks ? (
            <>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleGenerate}
                disabled={
                  !selectedHousehold ||
                  !selectedDocument ||
                  isGenerating ||
                  (selectedDocument === 'soa' && (selectedScenarios.length === 0 || (!generatePDF && !generateWord))) ||
                  (selectedDocument === 'financial' && !Object.values(financialSections).some(value => value === true)) ||
                  (selectedDocument === 'tpa' && (!selectedAdviser || !tpaAdviserAddress.trim())) ||
                  (selectedDocument === 'toe' && !selectedAdviser) ||
                  (selectedDocument === 'toe' && selectedTemplate === 'default' && !terms.trim()) ||
                  (selectedDocument === 'toe' && selectedTemplate !== 'default' && hasTextSection && !terms.trim())
                }
              >
                {isGenerating || isGeneratingPDF ? 'Generating...' : (selectedDocument === 'soa' || selectedDocument === 'financial' ? 'Generate Document(s)' : 'Generate Link')}
              </Button>
            </>
          ) : (
            <Button onClick={onClose}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
