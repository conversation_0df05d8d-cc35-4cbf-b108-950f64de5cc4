'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter // Keep DialogFooter if needed for buttons, but remove if buttons are moved inside content
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect } from "react";
import { HouseholdData } from "@/types/household";

// Marital Status Options
const MARITAL_STATUS_OPTIONS = [
  'Single',
  'Married',
  'De Facto',
  'Divorced',
  'Widowed'
];

// Preferred Contact Options
const PREFERRED_CONTACT_OPTIONS = [
  'Phone',
  'Email',
  'SMS',
  'Post'
];

// Employment Status Options
const EMPLOYMENT_STATUS_OPTIONS = [
  'Full-time',
  'Part-time',
  'Self-employed',
  'Unemployed',
  'Retired',
  'Student'
];

interface HouseholdInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  householdData: HouseholdData | null;
  onUpdate: () => void;
  section: 'household' | 'individual' | 'additional' | null; // Make section type more specific
}

// Helper to ensure members object exists and has default values
const ensureMembersData = (members: any): HouseholdData['members'] => {
  const defaultMemberData = {
    name1: '', last_name1: '', email1: '', phone1: '', income1: '', riskProfile: '',
    kiwisaverValue1: '', kiwisaverProfile1: '', kiwisaverContribution1: '', employerContribution1: '', age1: '',
    propertyValue: '', investmentValue: '', savingsValue: '', debtValue: '', annualExpenses: '',
    name2: '', last_name2: '', email2: '', phone2: '', income2: '', riskProfile2: '',
    kiwisaverValue2: '', kiwisaverProfile2: '', kiwisaverContribution2: '', employerContribution2: '', age2: '',
    occupation1: "", employer1: "", date_of_birth1: "", tax_file_number1: "", citizenship1: "",
    tax_residency1: "", employment_status1: "", occupation2: "", employer2: "", date_of_birth2: "",
    tax_file_number2: "", citizenship2: "", tax_residency2: "", employment_status2: ""
  };
  return { ...defaultMemberData, ...(members || {}) };
};


export default function HouseholdInfoModal({ isOpen, onClose, householdData, onUpdate, section }: HouseholdInfoModalProps) {
  const [formData, setFormData] = useState<HouseholdData>(() => {
    const defaultData: HouseholdData = {
      id: 0, householdName: '', phone: '', email: '', occupation: '', employer: '',
      marital_status: '', tax_file_number: '', notes: '', street: '', city: '', state: '',
      zip_code: '', country: '', property_type: '', preferred_contact: '', best_time_to_call: '',
      alternative_contact: '', total_assets: '', investment_strategy: '', risk_tolerance: '',
      primary_advisor: '', last_review: '', next_review: '', additional_info: null,
      created_at: new Date().toISOString(), date_of_birth: '', user_id: '',
      members: ensureMembersData(null), // Initialize with default structure
      address: "", updated_at: undefined
    };
    if (!householdData) {
      return defaultData;
    }
    // Ensure members is initialized even if householdData.members is null/undefined
    return {
      ...defaultData, // Start with defaults
      ...householdData, // Override with actual data
      members: ensureMembersData(householdData.members) // Ensure members is structured
    };
  });

  const { toast } = useToast();

  // Update formData when householdData prop changes
  useEffect(() => {
    if (householdData) {
        const initialData = {
            ...householdData,
            members: ensureMembersData(householdData.members)
        };
        setFormData(initialData);
    }
  }, [householdData, isOpen]); // Re-run when modal opens or data changes


  const handleInputChange = (field: keyof HouseholdData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleMemberInputChange = (field: keyof HouseholdData['members'], value: string) => {
    setFormData((prev) => ({
      ...prev,
      members: {
        ...(prev.members), // No need for null check due to ensureMembersData
        [field]: value,
      },
    }));
  };

  const handleSubmit = async () => {
    const supabase = createClient();

    if (!formData.id) {
      toast({
        title: "Error",
        description: "Invalid household ID",
        variant: "destructive",
      });
      return;
    }

    let updateData: Partial<HouseholdData> = {};

    // Prepare update data based ONLY on the current section
    if (section === 'household') {
      updateData = {
        householdName: formData.householdName,
        marital_status: formData.marital_status,
        preferred_contact: formData.preferred_contact,
        best_time_to_call: formData.best_time_to_call,
        alternative_contact: formData.alternative_contact,
        // address: formData.address, // Keep or remove based on whether it's used elsewhere
        street: formData.street,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zip_code,
        country: formData.country
      };
    } else if (section === 'individual') {
      // Only update the members object
      updateData = {
        members: formData.members // Send the whole members object as it contains all individual fields
      };
    } else if (section === 'additional') {
      updateData = {
        primary_advisor: formData.primary_advisor,
        last_review: formData.last_review,
        next_review: formData.next_review,
        notes: formData.notes
      };
    } else {
        console.error("Invalid section for saving:", section);
        toast({ title: "Error", description: "Cannot save, invalid section.", variant: "destructive" });
        return;
    }

    console.log("Submitting update for section:", section, "Data:", updateData);


    try {
      const { error: updateError } = await supabase
        .from('households')
        .update(updateData)
        .eq('id', formData.id);

      if (updateError) {
        console.error("Supabase update error:", updateError);
        throw updateError;
      }

      toast({
        title: "Success",
        description: "Household information updated successfully",
      });

      onUpdate(); // Refresh data on the overview page
      onClose(); // Close the modal
    } catch (error: any) {
      console.error("Error updating household:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update household information",
        variant: "destructive",
      });
    }
  };

  // Determine the title based on the section
  const getTitle = () => {
    switch (section) {
      case 'household': return 'Edit Household Information';
      case 'individual': return 'Edit Individual Metrics';
      case 'additional': return 'Edit Additional Details';
      default: return 'Edit Information'; // Fallback title
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4"> {/* Add padding and spacing */}

          {/* Household Information Section */}
          {section === 'household' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium mb-4">Household Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="householdName">Household Name</Label>
                  <Input
                    id="householdName"
                    value={formData.householdName || ''}
                    onChange={(e) => handleInputChange("householdName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="marital_status">Marital Status</Label>
                  <Select
                    value={formData.marital_status || ''}
                    onValueChange={(value) => handleInputChange("marital_status", value)}
                  >
                    <SelectTrigger id="marital_status">
                      <SelectValue placeholder="Select Marital Status" />
                    </SelectTrigger>
                    <SelectContent>
                      {MARITAL_STATUS_OPTIONS.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="preferred_contact">Preferred Contact</Label>
                  <Select
                    value={formData.preferred_contact || ''}
                    onValueChange={(value) => handleInputChange("preferred_contact", value)}
                  >
                    <SelectTrigger id="preferred_contact">
                      <SelectValue placeholder="Select Preferred Contact" />
                    </SelectTrigger>
                    <SelectContent>
                      {PREFERRED_CONTACT_OPTIONS.map((contact) => (
                        <SelectItem key={contact} value={contact}>
                          {contact}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="best_time_to_call">Best Time to Call</Label>
                  <Input
                    id="best_time_to_call"
                    value={formData.best_time_to_call || ''}
                    onChange={(e) => handleInputChange("best_time_to_call", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="alternative_contact">Alternative Contact</Label>
                  <Input
                    id="alternative_contact"
                    value={formData.alternative_contact || ''}
                    onChange={(e) => handleInputChange("alternative_contact", e.target.value)}
                  />
                </div>
              </div>

              <h3 className="text-lg font-medium mt-6 mb-4">Address Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 {/* <div className="space-y-2 md:col-span-2">
                   <Label htmlFor="address">Full Address (Optional)</Label>
                   <Input
                     id="address"
                     value={formData.address || ''}
                     onChange={(e) => handleInputChange("address", e.target.value)}
                     placeholder="e.g., 123 Main St, Anytown, USA 12345"
                   />
                 </div> */}
                <div className="space-y-2">
                  <Label htmlFor="street">Street</Label>
                  <Input
                    id="street"
                    value={formData.street || ''}
                    onChange={(e) => handleInputChange("street", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.city || ''}
                    onChange={(e) => handleInputChange("city", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">State / Province</Label>
                  <Input
                    id="state"
                    value={formData.state || ''}
                    onChange={(e) => handleInputChange("state", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="zip_code">ZIP / Postal Code</Label>
                  <Input
                    id="zip_code"
                    value={formData.zip_code || ''}
                    onChange={(e) => handleInputChange("zip_code", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.country || ''}
                    onChange={(e) => handleInputChange("country", e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Individual Metrics Section */}
          {section === 'individual' && (
            <div className="space-y-6">
              {/* Primary Member */}
              <div className="space-y-4 border p-4 rounded-md">
                 <h3 className="text-lg font-medium mb-4">Primary Member Details</h3>
                 {/* Add Name fields if needed, or assume they are managed elsewhere */}
                 {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="name1">First Name</Label>
                        <Input id="name1" value={formData.members?.name1 || ''} onChange={(e) => handleMemberInputChange("name1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="last_name1">Last Name</Label>
                        <Input id="last_name1" value={formData.members?.last_name1 || ''} onChange={(e) => handleMemberInputChange("last_name1", e.target.value)} />
                    </div>
                 </div> */}
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="occupation1">Occupation</Label>
                        <Input id="occupation1" value={formData.members?.occupation1 || ''} onChange={(e) => handleMemberInputChange("occupation1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="employer1">Employer</Label>
                        <Input id="employer1" value={formData.members?.employer1 || ''} onChange={(e) => handleMemberInputChange("employer1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="date_of_birth1">Date of Birth</Label>
                        <Input id="date_of_birth1" type="date" value={formData.members?.date_of_birth1?.split('T')[0] || ''} onChange={(e) => handleMemberInputChange("date_of_birth1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="tax_file_number1">IRD Number</Label>
                        <Input id="tax_file_number1" value={formData.members?.tax_file_number1 || ''} onChange={(e) => handleMemberInputChange("tax_file_number1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="citizenship1">Citizenship</Label>
                        <Input id="citizenship1" value={formData.members?.citizenship1 || ''} onChange={(e) => handleMemberInputChange("citizenship1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="tax_residency1">Tax Residency</Label>
                        <Input id="tax_residency1" value={formData.members?.tax_residency1 || ''} onChange={(e) => handleMemberInputChange("tax_residency1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="employment_status1">Employment Status</Label>
                        <Select value={formData.members?.employment_status1 || ''} onValueChange={(value) => handleMemberInputChange("employment_status1", value)}>
                            <SelectTrigger id="employment_status1"><SelectValue placeholder="Select Status" /></SelectTrigger>
                            <SelectContent>
                                {EMPLOYMENT_STATUS_OPTIONS.map((status) => (<SelectItem key={status} value={status}>{status}</SelectItem>))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="email1">Email</Label>
                        <Input id="email1" type="email" value={formData.members?.email1 || ''} onChange={(e) => handleMemberInputChange("email1", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="phone1">Phone</Label>
                        <Input id="phone1" type="tel" value={formData.members?.phone1 || ''} onChange={(e) => handleMemberInputChange("phone1", e.target.value)} />
                    </div>
                 </div>
              </div>

              {/* Secondary Member - Conditionally render if name2 exists or based on some logic */}
              {(formData.members?.name2 || formData.members?.last_name2) && ( // Example condition: render if name exists
                <div className="space-y-4 border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Secondary Member Details</h3>
                   {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="name2">First Name</Label>
                            <Input id="name2" value={formData.members?.name2 || ''} onChange={(e) => handleMemberInputChange("name2", e.target.value)} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="last_name2">Last Name</Label>
                            <Input id="last_name2" value={formData.members?.last_name2 || ''} onChange={(e) => handleMemberInputChange("last_name2", e.target.value)} />
                        </div>
                    </div> */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="occupation2">Occupation</Label>
                        <Input id="occupation2" value={formData.members?.occupation2 || ''} onChange={(e) => handleMemberInputChange("occupation2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="employer2">Employer</Label>
                        <Input id="employer2" value={formData.members?.employer2 || ''} onChange={(e) => handleMemberInputChange("employer2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="date_of_birth2">Date of Birth</Label>
                        <Input id="date_of_birth2" type="date" value={formData.members?.date_of_birth2?.split('T')[0] || ''} onChange={(e) => handleMemberInputChange("date_of_birth2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="tax_file_number2">IRD Number</Label>
                        <Input id="tax_file_number2" value={formData.members?.tax_file_number2 || ''} onChange={(e) => handleMemberInputChange("tax_file_number2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="citizenship2">Citizenship</Label>
                        <Input id="citizenship2" value={formData.members?.citizenship2 || ''} onChange={(e) => handleMemberInputChange("citizenship2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="tax_residency2">Tax Residency</Label>
                        <Input id="tax_residency2" value={formData.members?.tax_residency2 || ''} onChange={(e) => handleMemberInputChange("tax_residency2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="employment_status2">Employment Status</Label>
                        <Select value={formData.members?.employment_status2 || ''} onValueChange={(value) => handleMemberInputChange("employment_status2", value)}>
                            <SelectTrigger id="employment_status2"><SelectValue placeholder="Select Status" /></SelectTrigger>
                            <SelectContent>
                                {EMPLOYMENT_STATUS_OPTIONS.map((status) => (<SelectItem key={status} value={status}>{status}</SelectItem>))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="email2">Email</Label>
                        <Input id="email2" type="email" value={formData.members?.email2 || ''} onChange={(e) => handleMemberInputChange("email2", e.target.value)} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="phone2">Phone</Label>
                        <Input id="phone2" type="tel" value={formData.members?.phone2 || ''} onChange={(e) => handleMemberInputChange("phone2", e.target.value)} />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Additional Information Section */}
          {section === 'additional' && (
            <div className="space-y-4">
               <h3 className="text-lg font-medium mb-4">Advisory & Review</h3>
               <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                 <div className="space-y-2">
                   <Label htmlFor="primary_advisor">Primary Advisor</Label>
                   <Input
                     id="primary_advisor"
                     value={formData.primary_advisor || ''}
                     onChange={(e) => handleInputChange("primary_advisor", e.target.value)}
                   />
                 </div>
                 <div className="space-y-2">
                   <Label htmlFor="last_review">Last Review Date</Label>
                   <Input
                     id="last_review"
                     type="date"
                     value={formData.last_review?.split('T')[0] || ''}
                     onChange={(e) => handleInputChange("last_review", e.target.value)}
                   />
                 </div>
                 <div className="space-y-2">
                   <Label htmlFor="next_review">Next Review Date</Label>
                   <Input
                     id="next_review"
                     type="date"
                     value={formData.next_review?.split('T')[0] || ''}
                     onChange={(e) => handleInputChange("next_review", e.target.value)}
                   />
                 </div>
               </div>

               <h3 className="text-lg font-medium mt-6 mb-4">Notes</h3>
               <div className="space-y-2">
                 <Label htmlFor="notes">Additional Notes</Label>
                 <Textarea
                   id="notes"
                   value={formData.notes || ''}
                   onChange={(e) => handleInputChange("notes", e.target.value)}
                   rows={5} // Adjust rows as needed
                   placeholder="Enter any additional notes here..."
                 />
               </div>
            </div>
          )}
        </div>

        {/* Footer with buttons */}
        <DialogFooter className="pt-4 border-t"> {/* Add top padding and border */}
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
