import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Loader2, Upload, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createAudioFileFromVideo } from '@/utils/client-video-processing';

interface TranscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTranscriptionComplete: (noteId: string) => void;
  userId: string;
  orgId: string;
}

interface Household {
  id: number;
  householdName: string;
}

// Helper function to extract action items from TipTap JSON content
const extractActionItems = (content: any) => {
  if (!content) return [];

  // Parse content if it's a string
  const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;

  const actionItems: string[] = [];
  let inActionItemsSection = false;

  // Function to recursively search for action items in the content
  const searchForActionItems = (node: any) => {
    // Check if this is a heading with "Action Items"
    if (node.type === 'heading' &&
        node.content &&
        node.content.some((child: any) =>
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = true;
      return;
    }

    // If we're in the action items section and this is a list item, extract it
    if (inActionItemsSection && node.type === 'listItem') {
      let itemText = '';

      // Extract text from the list item
      const extractText = (n: any) => {
        if (n.text) {
          itemText += n.text + ' ';
        }
        if (n.content && Array.isArray(n.content)) {
          n.content.forEach(extractText);
        }
      };

      extractText(node);
      if (itemText.trim()) {
        actionItems.push(itemText.trim());
      }
    }

    // If we find another heading after the action items section, stop collecting
    if (inActionItemsSection && node.type === 'heading' &&
        node.content &&
        !node.content.some((child: any) =>
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = false;
    }

    // Continue searching in child nodes
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(searchForActionItems);
    }
  };

  // Start the search from the top level
  if (parsedContent.content && Array.isArray(parsedContent.content)) {
    parsedContent.content.forEach(searchForActionItems);
  }

  return actionItems;
};

export default function TranscriptionModal({
  isOpen,
  onClose,
  onTranscriptionComplete,
  userId,
  orgId
}: TranscriptionModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [title, setTitle] = useState('');
  const [selectedHousehold, setSelectedHousehold] = useState('');
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const supabase = createClient();

  // Reset state when modal is opened/closed
  useEffect(() => {
    if (!isOpen) {
      setFile(null);
      setIsUploading(false);
      setIsTranscribing(false);
      setProgress(0);
      setProgressMessage('');
      setTitle('');
      setSelectedHousehold('');
    }
  }, [isOpen]);

  // Fetch households when modal is opened
  useEffect(() => {
    if (isOpen) {
      const fetchHouseholds = async () => {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('households')
          .select('id, householdName')
          .order('householdName', { ascending: true });

        if (error) {
          console.error('Error fetching households:', error);
          toast.error('Failed to load households');
        } else {
          setHouseholds(data || []);
        }
        setIsLoading(false);
      };

      fetchHouseholds();
    }
  }, [isOpen]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check if file is audio or video
      if (!selectedFile.type.startsWith('audio/') && !selectedFile.type.startsWith('video/')) {
        toast.error('Please select an audio or video file');
        return;
      }

      // Check file size (limit to 100MB)
      if (selectedFile.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      // Warn about large files
      if (selectedFile.size > 25 * 1024 * 1024) {
        toast.warning('Large files may take longer to process and might result in lower quality transcription.');
      }

      setFile(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      // Check if file is audio or video
      if (!droppedFile.type.startsWith('audio/') && !droppedFile.type.startsWith('video/')) {
        toast.error('Please select an audio or video file');
        return;
      }

      // Check file size (limit to 100MB)
      if (droppedFile.size > 100 * 1024 * 1024) {
        toast.error('File size must be less than 100MB');
        return;
      }

      // Warn about large files
      if (droppedFile.size > 25 * 1024 * 1024) {
        toast.warning('Large files may take longer to process and might result in lower quality transcription.');
      }

      setFile(droppedFile);
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleTranscribe = async () => {
    if (!file || !userId || !title || !selectedHousehold) return;

    try {
      setIsUploading(true);
      setProgressMessage('Preparing file...');
      setProgress(10);

      // Variables to track what we'll upload and transcribe
      let fileToTranscribe = file;
      let fileToDisplay = file;
      let fileTypeForTranscription = file.type;

      // If it's a video, extract the audio for transcription but keep the video for display
      if (file.type.startsWith('video/')) {
        try {
          setProgressMessage('Converting video to audio...');
          setProgress(15);

          // Extract audio from video on the client side
          const { videoFile, audioFile } = await createAudioFileFromVideo(file);
          fileToTranscribe = audioFile;  // Use audio for transcription
          fileToDisplay = videoFile;     // Keep original video for display
          fileTypeForTranscription = 'audio/mp3';

          setProgress(20);
          setProgressMessage('Audio extracted successfully');
          console.log('Converted video to audio:', fileToTranscribe);
        } catch (conversionError) {
          console.error('Error converting video to audio:', conversionError);
          toast.warning('Could not convert video to audio. Using original file instead.');
          // Continue with the original file for both display and transcription
        }
      }

      setProgressMessage('Uploading files...');
      setProgress(25);

      // First, upload the display file (original video or audio)
      const timestamp = Date.now();
      const safeDisplayFileName = fileToDisplay.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const displayFileName = `${timestamp}-${safeDisplayFileName}`;
      const displayFilePath = `transcriptions/${userId}/${displayFileName}`;

      console.log(`Uploading display file to media/${displayFilePath}`);
      const { error: displayUploadError } = await supabase.storage
        .from('media')
        .upload(displayFilePath, fileToDisplay, {
          cacheControl: '3600',
          upsert: false
        });

      if (displayUploadError) {
        throw new Error(`Display file upload error: ${displayUploadError.message}`);
      }

      // If we're using a separate file for transcription (audio extracted from video)
      let transcriptionFilePath = displayFilePath;
      if (fileToTranscribe !== fileToDisplay) {
        const safeTranscriptionFileName = fileToTranscribe.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        const transcriptionFileName = `${timestamp}-${safeTranscriptionFileName}`;
        transcriptionFilePath = `transcriptions/${userId}/${transcriptionFileName}`;

        console.log(`Uploading transcription file to media/${transcriptionFilePath}`);
        const { error: transcriptionUploadError } = await supabase.storage
          .from('media')
          .upload(transcriptionFilePath, fileToTranscribe, {
            cacheControl: '3600',
            upsert: false
          });

        if (transcriptionUploadError) {
          throw new Error(`Transcription file upload error: ${transcriptionUploadError.message}`);
        }
      }

      setProgress(30);
      setProgressMessage('Processing transcription...');
      setIsUploading(false);
      setIsTranscribing(true);

      // Start transcription process
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath: transcriptionFilePath, // Path to the file to transcribe
          displayFilePath: displayFilePath, // Path to the file to display
          fileName: file.name,
          fileType: fileTypeForTranscription, // Type of the file to transcribe
          displayFileType: fileToDisplay.type, // Type of the file to display
          userId,
          orgId,
          title,
          household_id: selectedHousehold,
          originalFileName: file.name // Store the original file name for reference
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start transcription');
      }

      const { noteId, jobId } = await response.json();

      // Poll for transcription status
      await pollTranscriptionStatus(jobId, noteId);

    } catch (error) {
      console.error('Transcription error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to transcribe file');
      setIsUploading(false);
      setIsTranscribing(false);
      setProgress(0);
    }
  };

  const pollTranscriptionStatus = async (jobId: string, noteId: string) => {
    try {
      let completed = false;

      while (!completed) {
        // Wait for 2 seconds between polls
        await new Promise(resolve => setTimeout(resolve, 2000));

        const response = await fetch(`/api/transcription-status?jobId=${jobId}`);

        if (!response.ok) {
          throw new Error('Failed to check transcription status');
        }

        const { status, progress: currentProgress } = await response.json();

        // Update progress
        setProgress(30 + (currentProgress * 0.7)); // Scale from 30% to 100%

        if (status === 'completed') {
          completed = true;
          setProgress(90);
          setProgressMessage('Transcription complete! Generating summary...');

          // Process the transcription with the refactor-note API
          try {
            // First, get the transcription content
            const { data: noteData, error: noteError } = await supabase
              .from('notes')
              .select('content')
              .eq('id', noteId)
              .single();

            if (noteError) throw noteError;

            // Call the refactor-note API to generate a summary
            const refactorResponse = await fetch('/api/refactor-note', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                content: noteData.content,
                refactorType: 'transcription-summary'
              }),
            });

            if (!refactorResponse.ok) {
              throw new Error('Failed to generate transcription summary');
            }

            const { refactoredContent } = await refactorResponse.json();

            // Extract action items from the refactored content
            const actionItems = extractActionItems(refactoredContent);

            // Update the note with the summary and action items
            const { error: updateError } = await supabase
              .from('notes')
              .update({
                ai_content: JSON.stringify(refactoredContent),
                action_items: actionItems,
                last_edited_at: new Date().toISOString()
              })
              .eq('id', noteId);

            if (updateError) throw updateError;

            setProgress(100);
            setProgressMessage('Summary generated successfully!');
          } catch (summaryError) {
            console.error('Error generating summary:', summaryError);
            // Continue even if summary generation fails
            setProgress(100);
            setProgressMessage('Transcription complete! (Summary generation failed)');
          }

          // Wait a moment to show the completion state
          setTimeout(() => {
            setIsTranscribing(false);
            onTranscriptionComplete(noteId);
            onClose();
            // Redirect to the transcription page instead of the note page
            window.location.href = `/protected/notes/transcription/${noteId}`;
          }, 1500);
        } else if (status === 'failed') {
          setProgress(0);
          setProgressMessage('Transcription failed');
          setIsTranscribing(false);
          toast.error('Transcription failed. Please try again.');
          return; // Exit the polling loop
        } else {
          setProgressMessage(`Transcribing... ${Math.round(currentProgress)}%`);
        }
      }
    } catch (error) {
      console.error('Error polling transcription status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to get transcription status');
      setIsTranscribing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Transcription</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <div className="grid gap-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="transcriptionTitle" className="text-right">
                  Title
                </Label>
                <Input
                  id="transcriptionTitle"
                  className="col-span-3"
                  placeholder="Enter transcription title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="household" className="text-right">
                  Select Household
                </Label>
                <Select onValueChange={setSelectedHousehold} value={selectedHousehold}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a household" />
                  </SelectTrigger>
                  <SelectContent>
                    {households.map((household) => (
                      <SelectItem key={household.id} value={household.id.toString()}>
                        {household.householdName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {!file && !isUploading && !isTranscribing && (
                <div
                  className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-muted/50 transition-colors mt-4"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-sm font-medium mb-1">Drag and drop or click to upload</p>
                  <p className="text-xs text-muted-foreground mb-4">
                    Supports audio and video files (max 100MB)<br/>
                    <span className="text-amber-500">Note: Videos will be converted to audio for better transcription</span><br/>
                    <span className="text-xs">For best results, use files under 25MB</span>
                  </p>
                  <Button variant="outline" size="sm" type="button">
                    Select File
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="audio/*,video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              )}
            </div>
          )}

          {file && !isUploading && !isTranscribing && (
            <div className="border rounded-lg p-4 mt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <Upload className="h-5 w-5 text-primary" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium truncate max-w-[250px]">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleRemoveFile}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {(isUploading || isTranscribing) && (
            <div className="space-y-4 mt-8">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{progressMessage}</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>

              <div className="flex justify-center">
                {isUploading && <Loader2 className="h-6 w-6 animate-spin text-primary" />}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isUploading || isTranscribing}
          >
            Cancel
          </Button>
          <Button
            onClick={handleTranscribe}
            disabled={!file || !title || !selectedHousehold || isUploading || isTranscribing}
          >
            Transcribe
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
