import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from 'lucide-react';
import dynamic from 'next/dynamic';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

// Add this debugging function at the top of your component
const isValidJSON = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// Helper function to extract action items from the refactored content
const extractActionItems = (content: any) => {
  if (!content) return [];
  
  // Parse content if it's a string
  const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;
  
  const actionItems: string[] = [];
  let inActionItemsSection = false;
  
  // Function to recursively search for action items in the content
  const searchForActionItems = (node: any) => {
    // Check if this is a heading with "Action Items"
    if (node.type === 'heading' && 
        node.content && 
        node.content.some((child: any) => 
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = true;
      return;
    }
    
    // If we're in the action items section and this is a list item, extract it
    if (inActionItemsSection && node.type === 'listItem') {
      let itemText = '';
      
      // Extract text from the list item
      const extractText = (n: any) => {
        if (n.text) {
          itemText += n.text + ' ';
        }
        if (n.content && Array.isArray(n.content)) {
          n.content.forEach(extractText);
        }
      };
      
      extractText(node);
      if (itemText.trim()) {
        actionItems.push(itemText.trim());
      }
    }
    
    // If we find another heading after the action items section, stop collecting
    if (inActionItemsSection && node.type === 'heading' && 
        node.content && 
        !node.content.some((child: any) => 
          child.text && child.text.toLowerCase().includes('action item'))) {
      inActionItemsSection = false;
    }
    
    // Continue searching in child nodes
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(searchForActionItems);
    }
  };
  
  // Start the search from the top level
  if (parsedContent && parsedContent.content && Array.isArray(parsedContent.content)) {
    parsedContent.content.forEach(searchForActionItems);
  }
  
  return actionItems;
};

// Import the editor with dynamic loading to avoid SSR issues
const NoteEditor = dynamic(() => import('@/components/tiptap/NoteEditor'), {
  ssr: false,
  loading: () => <div className="flex justify-center p-4"><Loader2 className="h-8 w-8 animate-spin" /></div>
});

interface RefactorNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  originalContent: string;
  refactoredContent: string | null;
  onRefactor: (type: string) => void;
  onAccept: () => void;
  isLoading: boolean;
  refactorType: string;
  onRefactorTypeChange: (type: string) => void;
}

const RefactorNoteModal = ({
  isOpen,
  onClose,
  originalContent,
  refactoredContent,
  onRefactor,
  onAccept,
  isLoading,
  refactorType = 'format',
  onRefactorTypeChange
}: RefactorNoteModalProps) => {
  const [activeTab, setActiveTab] = useState<'original' | 'refactored' | 'actionItems'>('original');
  const [actionItems, setActionItems] = useState<string[]>([]);

  useEffect(() => {
    // Extract action items when refactored content changes
    if (refactoredContent) {
      const items = extractActionItems(refactoredContent);
      setActionItems(items);
      // Switch to refactored tab when content is available
      setActiveTab('refactored');
    } else {
      // Reset to original tab when no refactored content
      setActiveTab('original');
    }
  }, [refactoredContent]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>AI Note Refactoring</DialogTitle>
        </DialogHeader>
        
        <RadioGroup 
          value={refactorType} 
          onValueChange={onRefactorTypeChange}
          className="flex space-x-4 mb-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="format" id="format" />
            <Label htmlFor="format">Format</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="summarize" id="summarize" />
            <Label htmlFor="summarize">Summarize</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="keypoints" id="keypoints" />
            <Label htmlFor="keypoints">Key Points</Label>
          </div>
        </RadioGroup>
        
        {!refactoredContent && !isLoading && (
          <div className="flex justify-center my-4">
            <Button onClick={() => onRefactor(refactorType)}>
              Refactor Note
            </Button>
          </div>
        )}
        
        {(refactoredContent || isLoading) && (
          <>
            <div className="flex border-b mb-4">
              <button
                className={`px-4 py-2 ${activeTab === 'original' ? 'border-b-2 border-primary font-medium' : ''}`}
                onClick={() => setActiveTab('original')}
              >
                Original
              </button>
              <button
                className={`px-4 py-2 ${activeTab === 'refactored' ? 'border-b-2 border-primary font-medium' : ''}`}
                onClick={() => setActiveTab('refactored')}
                disabled={!refactoredContent}
              >
                Refactored
              </button>
              <button
                className={`px-4 py-2 ${activeTab === 'actionItems' ? 'border-b-2 border-primary font-medium' : ''}`}
                onClick={() => setActiveTab('actionItems')}
                disabled={!refactoredContent || actionItems.length === 0}
              >
                Action Items
              </button>
            </div>
            
            <div className="flex-1 overflow-auto min-h-[300px]">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin mr-2" />
                  <p>Refactoring your note...</p>
                </div>
              ) : (
                <div className="h-full">
                  {activeTab === 'original' ? (
                    <NoteEditor 
                      content={originalContent} 
                      onChange={() => {}} 
                      editable={false} 
                    />
                  ) : activeTab === 'refactored' && refactoredContent ? (
                    <NoteEditor 
                      content={refactoredContent} 
                      onChange={() => {}} 
                      editable={false} 
                    />
                  ) : (
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-4">Action Items</h3>
                      {actionItems.length > 0 ? (
                        <ul className="space-y-2">
                          {actionItems.map((item, index) => (
                            <li key={index} className="p-3 bg-muted rounded-md flex items-start">
                              <div className="mr-2">•</div>
                              <div>{item}</div>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-muted-foreground">No action items found in the refactored note.</p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              {refactoredContent && (
                <Button onClick={onAccept} disabled={isLoading}>
                  Accept Refactored Version
                </Button>
              )}
              {!isLoading && refactoredContent && (
                <Button 
                  variant="secondary" 
                  onClick={() => onRefactor(refactorType)}
                >
                  Refactor Again
                </Button>
              )}
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RefactorNoteModal;
