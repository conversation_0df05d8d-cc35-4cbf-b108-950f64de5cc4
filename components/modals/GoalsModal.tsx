'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from '../ui/textarea';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface Goal {
  id?: string;
  title: string;
  type: string;
  details?: string;
  start_date?: string;
  achieved_date?: string;
  status: string;
  member?: string;
  target_amount?: number;
  priority?: string;
}

interface GoalsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  householdId: string;
  goal?: Goal;
  householdMembers?: { name1: string; name2?: string };
}

export function GoalsModal({ isOpen, onClose, onSuccess, householdId, goal, householdMembers }: GoalsModalProps) {
  const [title, setTitle] = useState('');
  const [type, setType] = useState('');
  const [details, setDetails] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [achievedDate, setAchievedDate] = useState<Date | undefined>(undefined);
  const [status, setStatus] = useState('Not Started');
  const [member, setMember] = useState('');
  const [targetAmount, setTargetAmount] = useState<string>('');
  const [priority, setPriority] = useState('');
  const [loading, setLoading] = useState(false);

  // Load goal data if editing an existing goal
  useEffect(() => {
    if (goal) {
      setTitle(goal.title || '');
      setType(goal.type || '');
      setDetails(goal.details || '');
      setStatus(goal.status || 'Not Started');
      setMember(goal.member || '');
      setPriority(goal.priority || '');
      
      if (goal.target_amount) {
        setTargetAmount(goal.target_amount.toString());
      }
      
      if (goal.start_date) {
        setStartDate(new Date(goal.start_date));
      }
      
      if (goal.achieved_date) {
        setAchievedDate(new Date(goal.achieved_date));
      }
    }
  }, [goal]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const supabase = createClient();
    
    // Get user and org info for RLS
    const { data: { user } } = await supabase.auth.getUser();
    const { data: profileData } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user?.id)
      .single();
    
    const goalData = {
      title,
      type,
      details,
      start_date: startDate ? format(startDate, 'yyyy-MM-dd') : null,
      achieved_date: achievedDate ? format(achievedDate, 'yyyy-MM-dd') : null,
      status,
      member,
      target_amount: targetAmount ? parseFloat(targetAmount) : null,
      priority,
      household_id: householdId,
      user_id: user?.id,
      org_id: profileData?.org_id
    };

    let error;

    if (goal?.id) {
      // Update existing goal
      const { error: updateError } = await supabase
        .from('goals')
        .update(goalData)
        .eq('id', goal.id);
      error = updateError;
    } else {
      // Insert new goal
      const { error: insertError } = await supabase
        .from('goals')
        .insert([goalData]);
      error = insertError;
    }

    setLoading(false);

    if (error) {
      console.error('Error saving goal:', error);
    } else {
      onSuccess();
      handleClose();
    }
  };

  const handleClose = () => {
    setTitle('');
    setType('');
    setDetails('');
    setStartDate(undefined);
    setAchievedDate(undefined);
    setStatus('Not Started');
    setMember('');
    setTargetAmount('');
    setPriority('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{goal?.id ? 'Edit Goal' : 'Add Goal'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Goal Title*</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter goal title"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="type">Goal Type*</Label>
            <Select value={type} onValueChange={setType} required>
              <SelectTrigger>
                <SelectValue placeholder="Select goal type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Financial">Financial</SelectItem>
                <SelectItem value="Lifestyle">Lifestyle</SelectItem>
                <SelectItem value="Retirement">Retirement</SelectItem>
                <SelectItem value="Education">Education</SelectItem>
                <SelectItem value="Health">Health</SelectItem>
                <SelectItem value="Travel">Travel</SelectItem>
                <SelectItem value="Family">Family</SelectItem>
                <SelectItem value="Career">Career</SelectItem>
                <SelectItem value="Personal">Personal</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="member">For Member</Label>
            <Select value={member} onValueChange={setMember}>
              <SelectTrigger>
                <SelectValue placeholder="Select household member" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Household">Entire Household</SelectItem>
                {householdMembers?.name1 && (
                  <SelectItem value={householdMembers.name1}>{householdMembers.name1}</SelectItem>
                )}
                {householdMembers?.name2 && (
                  <SelectItem value={householdMembers.name2}>{householdMembers.name2}</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              placeholder="Enter goal details"
              rows={3}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div>
              <Label htmlFor="achievedDate">Achieved Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !achievedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {achievedDate ? format(achievedDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={achievedDate}
                    onSelect={setAchievedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status*</Label>
              <Select value={status} onValueChange={setStatus} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Not Started">Not Started</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Delayed">Delayed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="targetAmount">Target Amount</Label>
            <Input
              id="targetAmount"
              type="number"
              value={targetAmount}
              onChange={(e) => setTargetAmount(e.target.value)}
              placeholder="Enter target amount"
              step="0.01"
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : (goal?.id ? 'Update Goal' : 'Add Goal')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
