import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

interface CreateHouseholdModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CreateHouseholdModal({ isOpen, onClose }: CreateHouseholdModalProps) {
  const [householdName, setHouseholdName] = useState('');
  const [member1Name, setMember1Name] = useState('');
  const [member2Name, setMember2Name] = useState('');
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [nameError, setNameError] = useState('');
  const router = useRouter();

  useEffect(() => {
    setIsFormValid(householdName.trim() !== '' && !nameError);
  }, [householdName, nameError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('No authenticated user found');
      setIsLoading(false);
      return;
    }
    
    // Get the current view mode and org_id if in organization mode
    const viewMode = localStorage.getItem('viewMode') || 'user';
    let orgId = null;
    
    if (viewMode === 'organization') {
      const { data } = await supabase
        .from('profiles')
        .select('org_id')
        .eq('user_id', user.id)
        .single();
        
      if (data) {
        orgId = data.org_id;
      }
    }
    
    const { error } = await supabase
      .from('households')
      .insert({
        householdName: householdName,
        members: {
          name1: member1Name,
          name2: member2Name
        },
        user_id: user.id,
        org_id: orgId
      });
      
    if (error) {
      console.error('Error creating household:', error);
      setNameError(error.message);
    } else {
      onClose();
    }
    
    setIsLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Household</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Household Name
                </Label>
                <div className="col-span-3">
                  <Input
                    id="name"
                    value={householdName}
                    onChange={(e) => {
                      setHouseholdName(e.target.value);
                      setNameError('');
                    }}
                    className={`${nameError ? 'border-red-500' : ''}`}
                    required
                  />
                  {nameError && <p className="text-red-500 text-sm mt-1">{nameError}</p>}
                </div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="member1" className="text-right">
                  Member 1
                </Label>
                <div className="col-span-3">
                  <Input
                    id="member1"
                    value={member1Name}
                    onChange={(e) => setMember1Name(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="member2" className="text-right">
                  Member 2
                </Label>
                <div className="col-span-3">
                  <Input
                    id="member2"
                    value={member2Name}
                    onChange={(e) => setMember2Name(e.target.value)}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={!isFormValid || isLoading}>
                Create Household
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
