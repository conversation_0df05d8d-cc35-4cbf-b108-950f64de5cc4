import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LabelWithTooltip } from "@/components/TabTooltips";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ExternalLink, Settings, Plus, Trash2, AlertCircle, Info, ChevronDown } from 'lucide-react';
import { InputData } from '@/app/protected/planner/types';
import { FUND_TYPES } from '@/app/constants/fundTypes';
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, TooltipContent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { createClient } from '@/utils/supabase/client';
import { useFunds } from '@/hooks/useFunds';

interface InvestmentPeriod {
  fundId?: string; // ID of the custom fund
  fundType: keyof typeof FUND_TYPES | string; // Allow for custom fund names
  period: [number, number];
  customReturn?: number;
  customStdDev?: number;
  incomePortion?: number; // Add income portion to the interface
}

interface InvestmentModalProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
}

export function InvestmentModal({ inputData, setInputData }: InvestmentModalProps) {
  const [open, setOpen] = useState(false);
  const [activeContentTab, setActiveContentTab] = useState("basic"); // "basic" or "advanced"
  const [activeFundTab, setActiveFundTab] = useState("fund1");
  const [validationError, setValidationError] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | undefined>(undefined);
  const { funds, isLoading: fundsLoading, getFundsByType } = useFunds(userId);
  const supabase = createClient();

  // Determine the number of active investment funds
  const [activeInvestmentFunds] = useState<number>(() => {
    // Initialize based on existing data
    if ((inputData as any).initial_investment5) return 5;
    if ((inputData as any).initial_investment4) return 4;
    if ((inputData as any).initial_investment3) return 3;
    if ((inputData as any).initial_investment2) return 2;
    if ((inputData as any).initial_investment1) return 1;
    return 1; // Default to 1 fund
  });

  // Fetch user ID on component mount
  useEffect(() => {
    const fetchUserId = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (!error && data.user) {
        setUserId(data.user.id);
      }
    };

    fetchUserId();
  }, []);

  // Initialize fundPeriods for each investment fund
  const [fundPeriodsMap, setFundPeriodsMap] = useState<Record<number, InvestmentPeriod[]>>(() => {
    const periodsMap: Record<number, InvestmentPeriod[]> = {};

    // Initialize fund 1 periods
    if (inputData.fund_periods && inputData.fund_periods.length > 0) {
      periodsMap[1] = inputData.fund_periods.map(period => ({
        fundId: period.fundId,
        fundType: period.fundType,
        period: period.period,
        customReturn: period.fundType === 'Custom' ? period.return : undefined,
        customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
        incomePortion: period.incomePortion // Include income portion from saved data
      }));
    } else {
      periodsMap[1] = [{
        fundType: inputData.inv_fund_type || 'Balanced',
        period: inputData.investment_return_period || [inputData.starting_age, inputData.ending_age],
        customReturn: inputData.inv_fund_type === 'Custom' ? inputData.annual_investment_return : undefined,
        customStdDev: inputData.inv_fund_type === 'Custom' ? inputData.inv_std_dev : undefined,
        incomePortion: (inputData as any).fund1_income_portion // Include income portion from inputData
      }];
    }

    // Initialize periods for funds 2-5 if they exist
    for (let i = 2; i <= activeInvestmentFunds; i++) {
      const suffix = i.toString();

      // Check if fund_periods{i} exists and use it
      if ((inputData as any)[`fund_periods${i}`] && (inputData as any)[`fund_periods${i}`].length > 0) {
        periodsMap[i] = (inputData as any)[`fund_periods${i}`].map((period: any) => ({
          fundId: period.fundId,
          fundType: period.fundType,
          period: period.period,
          customReturn: period.fundType === 'Custom' ? period.return : undefined,
          customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
          incomePortion: period.incomePortion // Include income portion from saved data
        }));
      } else {
        // Fallback to using the basic fund settings
        const fundType = (inputData as any)[`inv_fund_type${suffix}`] || 'Balanced';
        const returnPeriod = (inputData as any)[`investment_return_period${suffix}`] || [inputData.starting_age, inputData.ending_age];
        const annualReturn = (inputData as any)[`annual_investment_return${suffix}`] || 5.5;
        const stdDev = (inputData as any)[`inv_std_dev${suffix}`] || 8.0;

        periodsMap[i] = [{
          fundType: fundType,
          period: returnPeriod,
          customReturn: fundType === 'Custom' ? annualReturn : undefined,
          customStdDev: fundType === 'Custom' ? stdDev : undefined,
          incomePortion: (inputData as any)[`fund${i}_income_portion`] // Include income portion from inputData
        }];
      }
    }

    return periodsMap;
  });

  // Track the currently open period index for each fund (only one can be open at a time per fund)
  const [openPeriodIndexMap, setOpenPeriodIndexMap] = useState<Record<number, number>>(() => {
    const indexMap: Record<number, number> = {};
    for (let i = 1; i <= activeInvestmentFunds; i++) {
      indexMap[i] = 0; // Default to first period open for each fund
    }
    return indexMap;
  });

  // Get the current fund number from the active fund tab
  const getCurrentFundNumber = (): number => {
    return parseInt(activeFundTab.replace('fund', ''));
  };

  // Toggle period - if clicking on the open period, close it
  // If clicking on a closed period, open it and close any other open period
  const togglePeriod = (index: number) => {
    const fundNumber = getCurrentFundNumber();
    setOpenPeriodIndexMap(prev => ({
      ...prev,
      [fundNumber]: prev[fundNumber] === index ? -1 : index
    }));
  };

  // Update openPeriodIndexMap when fundPeriodsMap changes
  useEffect(() => {
    setOpenPeriodIndexMap(prev => {
      const newMap = { ...prev };
      for (let i = 1; i <= activeInvestmentFunds; i++) {
        if (!fundPeriodsMap[i] || prev[i] >= fundPeriodsMap[i].length) {
          newMap[i] = fundPeriodsMap[i]?.length > 0 ? 0 : -1;
        }
      }
      return newMap;
    });
  }, [fundPeriodsMap, activeInvestmentFunds]);

  // Update fundPeriodsMap when inputData.fund_periods or fund_periods{i} changes
  useEffect(() => {
    // Update fund 1 periods
    if (inputData.fund_periods && inputData.fund_periods.length > 0) {
      setFundPeriodsMap(prev => ({
        ...prev,
        1: inputData.fund_periods.map(period => ({
          fundId: period.fundId,
          fundType: period.fundType,
          period: period.period,
          customReturn: period.fundType === 'Custom' ? period.return : undefined,
          customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
          incomePortion: period.incomePortion // Include income portion from saved data
        }))
      }));
    }

    // Update periods for funds 2-5
    for (let i = 2; i <= 5; i++) {
      if ((inputData as any)[`fund_periods${i}`] && (inputData as any)[`fund_periods${i}`].length > 0) {
        setFundPeriodsMap(prev => ({
          ...prev,
          [i]: (inputData as any)[`fund_periods${i}`].map((period: any) => ({
            fundId: period.fundId,
            fundType: period.fundType,
            period: period.period,
            customReturn: period.fundType === 'Custom' ? period.return : undefined,
            customStdDev: period.fundType === 'Custom' ? period.stdDev : undefined,
            incomePortion: period.incomePortion // Include income portion from saved data
          }))
        }));
      }
    }
  }, [
    inputData.fund_periods,
    (inputData as any).fund_periods2,
    (inputData as any).fund_periods3,
    (inputData as any).fund_periods4,
    (inputData as any).fund_periods5
  ]);

  // handleInputChange function removed as it's not being used

  const handleAddPeriod = () => {
    const fundNumber = getCurrentFundNumber();
    const currentPeriods = fundPeriodsMap[fundNumber] || [];

    // If there are no existing periods, create the first one
    if (currentPeriods.length === 0) {
      setFundPeriodsMap(prev => ({
        ...prev,
        [fundNumber]: [
          {
            fundType: 'Balanced',
            period: [inputData.starting_age, inputData.ending_age]
          }
        ]
      }));

      // Open the newly created period
      setOpenPeriodIndexMap(prev => ({
        ...prev,
        [fundNumber]: 0
      }));

      setValidationError(null);
      return;
    }

    // If there are existing periods, get the last one
    const lastPeriod = currentPeriods[currentPeriods.length - 1];

    // Check if there's room for another period
    if (lastPeriod.period[1] >= inputData.ending_age) {
      setValidationError("Cannot add more periods - the last period already extends to the end age");
      return;
    }

    setFundPeriodsMap(prev => ({
      ...prev,
      [fundNumber]: [
        ...currentPeriods,
        {
          fundType: 'Balanced',
          period: [lastPeriod.period[1] + 1, inputData.ending_age]
        }
      ]
    }));

    // Open the newly created period
    setOpenPeriodIndexMap(prev => ({
      ...prev,
      [fundNumber]: currentPeriods.length // Index of the new period
    }));

    setValidationError(null);
  };

  const handleRemovePeriod = (index: number) => {
    const fundNumber = getCurrentFundNumber();
    const currentPeriods = fundPeriodsMap[fundNumber] || [];

    setFundPeriodsMap(prev => ({
      ...prev,
      [fundNumber]: currentPeriods.filter((_, i) => i !== index)
    }));

    // Update openPeriodIndexMap if the removed period was open or if it affects the index
    setOpenPeriodIndexMap(prev => {
      const currentOpenIndex = prev[fundNumber];
      let newOpenIndex = currentOpenIndex;

      if (currentOpenIndex === index) {
        // If we removed the open period, default to the first period
        newOpenIndex = currentPeriods.length > 1 ? 0 : -1;
      } else if (currentOpenIndex > index) {
        // If we removed a period before the open one, adjust the index
        newOpenIndex = currentOpenIndex - 1;
      }

      return {
        ...prev,
        [fundNumber]: newOpenIndex
      };
    });

    setValidationError(null);
  };

  const handlePeriodChange = (index: number, field: 'start' | 'end', value: string) => {
    const newValue = parseInt(value);
    if (isNaN(newValue)) return;

    const fundNumber = getCurrentFundNumber();
    const currentPeriods = [...(fundPeriodsMap[fundNumber] || [])];

    if (!currentPeriods[index]) return;

    const periodIndex = field === 'start' ? 0 : 1;
    currentPeriods[index].period[periodIndex] = newValue;

    // Validate period sequencing
    if (index > 0 && currentPeriods[index].period[0] <= currentPeriods[index - 1].period[1]) {
      setValidationError("Each period must start after the previous period ends");
      return;
    }

    if (index < currentPeriods.length - 1 && currentPeriods[index].period[1] >= currentPeriods[index + 1].period[0]) {
      setValidationError("Each period must end before the next period starts");
      return;
    }

    setFundPeriodsMap(prev => ({
      ...prev,
      [fundNumber]: currentPeriods
    }));

    setValidationError(null);
  };

  const handleFundTypeChange = (index: number, value: string) => {
    const fundNumber = getCurrentFundNumber();
    const currentPeriods = [...(fundPeriodsMap[fundNumber] || [])];

    if (!currentPeriods[index]) return;

    // Check if the value is a custom fund ID
    const customFund = funds.find(fund => fund.id === value && fund.type === 'investment');

    if (customFund) {
      // It's a custom fund from our scenario metrics
      // Keep the UI the same by storing the fundId and name
      currentPeriods[index].fundId = customFund.id;
      currentPeriods[index].fundType = customFund.name;
      currentPeriods[index].customReturn = undefined;
      currentPeriods[index].customStdDev = undefined;

      // Also update the income portion for this fund
      if (customFund.incomePortion !== undefined) {
        setInputData(prev => ({
          ...prev,
          [`fund${fundNumber}_income_portion`]: customFund.incomePortion
        }));
      }
    } else if (value === 'Custom') {
      // It's the built-in Custom option
      currentPeriods[index].fundId = undefined;
      currentPeriods[index].fundType = value as keyof typeof FUND_TYPES;
      currentPeriods[index].customReturn = FUND_TYPES['Balanced'].return;
      currentPeriods[index].customStdDev = FUND_TYPES['Balanced'].stdDev;
    } else {
      // It's one of the built-in fund types
      currentPeriods[index].fundId = undefined;
      currentPeriods[index].fundType = value as keyof typeof FUND_TYPES;
      currentPeriods[index].customReturn = undefined;
      currentPeriods[index].customStdDev = undefined;
    }

    setFundPeriodsMap(prev => ({
      ...prev,
      [fundNumber]: currentPeriods
    }));
  };

  const handleCustomValueChange = (index: number, field: 'return' | 'stdDev' | 'incomePortion', value: string) => {
    const newValue = parseFloat(value);
    if (isNaN(newValue)) return;

    const fundNumber = getCurrentFundNumber();
    const currentPeriods = [...(fundPeriodsMap[fundNumber] || [])];

    if (!currentPeriods[index]) return;

    if (field === 'return') {
      currentPeriods[index].customReturn = newValue;
    } else if (field === 'stdDev') {
      currentPeriods[index].customStdDev = newValue;
    } else if (field === 'incomePortion') {
      currentPeriods[index].incomePortion = newValue;
      // Also update the global income portion value for backward compatibility
      setInputData(prev => ({
        ...prev,
        [`fund${fundNumber}_income_portion`]: newValue
      }));
    }

    setFundPeriodsMap(prev => ({
      ...prev,
      [fundNumber]: currentPeriods
    }));
  };

  const validatePeriods = (fundNumber: number): string | null => {
    const currentPeriods = fundPeriodsMap[fundNumber] || [];
    if (currentPeriods.length === 0) return null;

    // Check if periods cover the entire range
    const sortedPeriods = [...currentPeriods].sort((a, b) => a.period[0] - b.period[0]);

    // Check if first period starts at starting age
    if (sortedPeriods[0].period[0] > inputData.starting_age) {
      return `First period must start at or before the starting age (${inputData.starting_age})`;
    }

    // Check if last period ends at ending age
    if (sortedPeriods[sortedPeriods.length - 1].period[1] < inputData.ending_age) {
      return `Last period must end at or after the ending age (${inputData.ending_age})`;
    }

    // Check for gaps between periods
    for (let i = 1; i < sortedPeriods.length; i++) {
      if (sortedPeriods[i].period[0] > sortedPeriods[i-1].period[1] + 1) {
        return `Gap detected between periods ending at age ${sortedPeriods[i-1].period[1]} and starting at age ${sortedPeriods[i].period[0]}`;
      }
    }

    // Check for overlaps
    for (let i = 1; i < sortedPeriods.length; i++) {
      if (sortedPeriods[i].period[0] <= sortedPeriods[i-1].period[1]) {
        return "Fund periods cannot overlap";
      }
    }

    return null;
  };

  const handleSave = () => {
    // Validate periods for each fund in the withdrawal priorities list
    for (const fundNumber of (inputData.withdrawal_priorities || [])) {
      const error = validatePeriods(fundNumber);
      if (error) {
        setActiveFundTab(`fund${fundNumber}`);
        setValidationError(`Fund ${fundNumber}: ${error}`);
        return;
      }
    }

    // Create a new inputData object with all the fund settings
    setInputData(prev => {
      const newData = { ...prev };

      // Update each fund's settings
      for (const fundNumber of (inputData.withdrawal_priorities || [])) {
        // Get current periods or create a default period if none exists
        let currentPeriods = fundPeriodsMap[fundNumber] || [];

        // If there are no periods for this fund, create a default one
        if (currentPeriods.length === 0) {
          currentPeriods = [{
            fundType: 'Balanced',
            period: [inputData.starting_age, inputData.ending_age]
          }];

          // Update the fundPeriodsMap with the default period
          setFundPeriodsMap(prevMap => ({
            ...prevMap,
            [fundNumber]: currentPeriods
          }));
        }

        // Sort periods by start age
        const sortedPeriods = [...currentPeriods].sort((a, b) => a.period[0] - b.period[0]);

        // For fund 1, update the legacy fields and fund_periods
        if (fundNumber === 1) {
          newData.fund_periods = sortedPeriods.map(period => {
            // Get fund info with the correct income portion for this specific fund
            const fundInfo = getFundTypeInfo(period.fundType, period.customReturn, period.customStdDev, period.fundId, fundNumber);

            // Keep the UI representation (fundId and fundType) but use the calculated values
            // This ensures that the financial loop gets the correct values while preserving the UI
            return {
              fundId: period.fundId,
              fundType: period.fundType,
              period: period.period,
              return: fundInfo.return,
              stdDev: fundInfo.risk,
              incomePortion: fundInfo.incomePortion
            };
          });

          // For the first period, determine if it's a custom fund and handle accordingly
          const firstPeriod = sortedPeriods[0];
          const fundInfo = getFundTypeInfo(
            firstPeriod.fundType,
            firstPeriod.customReturn,
            firstPeriod.customStdDev,
            firstPeriod.fundId,
            fundNumber
          );

          newData.investment_return_period = firstPeriod.period;

          // Keep the UI representation of the fund type
          newData.inv_fund_type = firstPeriod.fundType;

          newData.annual_investment_return = fundInfo.return;
          newData.inv_std_dev = fundInfo.risk;
        }

        // Update fund-specific fields
        const suffix = fundNumber === 1 ? '1' : fundNumber.toString();

        // Set the fund type, return rate, and std dev for the first period
        // Keep the UI representation of the fund type
        (newData as any)[`inv_fund_type${suffix}`] = sortedPeriods[0].fundType;

        (newData as any)[`investment_return_period${suffix}`] = sortedPeriods[0].period;

        const fundInfo = getFundTypeInfo(
          sortedPeriods[0].fundType,
          sortedPeriods[0].customReturn,
          sortedPeriods[0].customStdDev,
          sortedPeriods[0].fundId,
          fundNumber
        );

        (newData as any)[`annual_investment_return${suffix}`] = fundInfo.return;
        (newData as any)[`inv_std_dev${suffix}`] = fundInfo.risk;

        // Store the fund periods for this fund
        (newData as any)[`fund_periods${suffix}`] = sortedPeriods.map(period => {
          const periodFundInfo = getFundTypeInfo(period.fundType, period.customReturn, period.customStdDev, period.fundId, fundNumber);

          // Keep the UI representation (fundId and fundType) but use the calculated values
          // This ensures that the financial loop gets the correct values while preserving the UI
          return {
            fundId: period.fundId,
            fundType: period.fundType,
            period: period.period,
            return: periodFundInfo.return,
            stdDev: periodFundInfo.risk,
            incomePortion: periodFundInfo.incomePortion
          };
        });
      }

      // Update fund-specific contribution periods
      for (const fundNumber of (inputData.withdrawal_priorities || [])) {
        const periodKey = `contribution_period${fundNumber}`;
        const startAge = (inputData as any)[periodKey]?.[0] ??
                        (fundNumber === 1 ? inputData.contribution_period?.[0] : null) ??
                        inputData.starting_age;
        const endAge = (inputData as any)[periodKey]?.[1] ??
                      (fundNumber === 1 ? inputData.contribution_period?.[1] : null) ??
                      inputData.ending_age;

        (newData as any)[periodKey] = [
          parseInt(startAge.toString()),
          parseInt(endAge.toString())
        ];

        // Also update the annual contribution amount for this fund
        const contribKey = `annual_investment_contribution${fundNumber}`;
        if ((inputData as any)[contribKey] !== undefined) {
          (newData as any)[contribKey] = (inputData as any)[contribKey];
        }
      }

      // Update legacy contribution period for backward compatibility
      newData.contribution_period = [
        parseInt((inputData as any).contribution_period1?.[0]?.toString() ??
                inputData.contribution_period?.[0]?.toString() ??
                inputData.starting_age.toString()),
        parseInt((inputData as any).contribution_period1?.[1]?.toString() ??
                inputData.contribution_period?.[1]?.toString() ??
                inputData.ending_age.toString())
      ];

      return newData;
    });

    // Clear any validation errors
    setValidationError(null);
  };

  // Get color for fund type
  const getFundTypeColor = (fundType: string, fundId?: string) => {
    // First check if it's a custom fund with a color
    if (fundId) {
      const customFund = funds.find(fund => fund.id === fundId);
      if (customFund && customFund.color) {
        return customFund.color;
      }
    }

    // Then check if it's a custom fund by name
    const customFundByName = funds.find(fund => fund.name === fundType && fund.type === 'investment');
    if (customFundByName && customFundByName.color) {
      return customFundByName.color;
    }

    // Default colors for built-in types
    switch (fundType) {
      case 'Custom':
        return '#9333ea'; // Purple
      case 'Conservative':
        return '#3b82f6'; // Blue
      case 'Balanced':
        return '#10b981'; // Green
      case 'Growth':
        return '#f59e0b'; // Amber
      case 'Aggressive':
        return '#f97316'; // Orange
      default:
        return '#6b7280'; // Gray
    }
  };

  // Get expected return, risk, and income portion for a fund type
  const getFundTypeInfo = (fundType: string, customReturn?: number, customStdDev?: number, fundId?: string, specificFundNumber?: number): { return: number, risk: number, incomePortion?: number } => {
    // Use the provided fund number or get the current one
    const fundNumber = specificFundNumber !== undefined ? specificFundNumber : getCurrentFundNumber();

    // First check if it's a custom fund from our scenario metrics
    if (fundId) {
      const customFund = funds.find(fund => fund.id === fundId);
      if (customFund) {
        // Use the fund's income portion if available, otherwise use the fund-specific income portion from inputData
        const incomePortion = customFund.incomePortion !== undefined ?
          customFund.incomePortion :
          (inputData as any)[`fund${fundNumber}_income_portion`] || 60;

        // Return the custom fund's values directly
        // The UI will show the custom fund name, but the calculations will use these values
        // This ensures that a stdDev of 0 is properly respected in the financial loop
        return {
          // Use nullish coalescing to preserve 0 values
          return: customFund.return !== undefined && customFund.return !== null ? customFund.return : 5.5,
          risk: customFund.stdDev !== undefined && customFund.stdDev !== null ? customFund.stdDev : 8.0,
          incomePortion: incomePortion
        };
      }
    }

    // Then check if it's a custom fund by name
    const customFundByName = funds.find(fund => fund.name === fundType && fund.type === 'investment');
    if (customFundByName) {
      // Use the fund's income portion if available, otherwise use the fund-specific income portion from inputData
      const incomePortion = customFundByName.incomePortion !== undefined ?
        customFundByName.incomePortion :
        (inputData as any)[`fund${fundNumber}_income_portion`] || 60;

      // Return the custom fund's values directly
      // The UI will show the custom fund name, but the calculations will use these values
      // This ensures that a stdDev of 0 is properly respected in the financial loop
      return {
        // Use nullish coalescing to preserve 0 values
        return: customFundByName.return !== undefined && customFundByName.return !== null ? customFundByName.return : 5.5,
        risk: customFundByName.stdDev !== undefined && customFundByName.stdDev !== null ? customFundByName.stdDev : 8.0,
        incomePortion: incomePortion
      };
    }

    // Check if it's the built-in Custom type with custom values
    if (fundType === 'Custom' && customReturn !== undefined && customStdDev !== undefined) {
      // For custom type, use the income portion from the specific fund
      const incomePortion = (inputData as any)[`fund${fundNumber}_income_portion`] || 60;

      return {
        return: customReturn,
        risk: customStdDev,
        incomePortion: incomePortion
      };
    }

    // Finally, check if it's one of the built-in fund types
    if (fundType in FUND_TYPES) {
      // Check if we have a specific income portion for this fund
      const specificIncomePortion = (inputData as any)[`fund${fundNumber}_income_portion`];

      // If we have a specific income portion for this fund, use it
      if (specificIncomePortion !== undefined) {
        return {
          return: FUND_TYPES[fundType as keyof typeof FUND_TYPES].return || 5.5,
          risk: FUND_TYPES[fundType as keyof typeof FUND_TYPES].stdDev || 8.0,
          incomePortion: specificIncomePortion
        };
      }

      // Otherwise, use default income portions based on fund type
      let defaultIncomePortion = 50; // Default for Balanced

      if (fundType === 'Conservative') defaultIncomePortion = 90;
      else if (fundType === 'Moderate') defaultIncomePortion = 80;
      else if (fundType === 'Balanced') defaultIncomePortion = 60;
      else if (fundType === 'Growth') defaultIncomePortion = 40;
      else if (fundType === 'Aggressive') defaultIncomePortion = 20;

      return {
        return: FUND_TYPES[fundType as keyof typeof FUND_TYPES].return || 5.5,
        risk: FUND_TYPES[fundType as keyof typeof FUND_TYPES].stdDev || 8.0,
        incomePortion: defaultIncomePortion
      };
    }

    // Default fallback
    return {
      return: 5.5, // Balanced default
      risk: 8.0,
      incomePortion: (inputData as any)[`fund${fundNumber}_income_portion`] || 60 // Use fund-specific income portion or default
    };
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <ExternalLink className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Investment Settings
          </DialogTitle>
        </DialogHeader>

        {/* Fund Tabs - Now at the top level */}
        <Tabs defaultValue="fund1" value={activeFundTab} onValueChange={setActiveFundTab} className="mt-2">
          <TabsList className="grid" style={{ gridTemplateColumns: `repeat(${(inputData.withdrawal_priorities || []).length}, 1fr)` }}>
            {(inputData.withdrawal_priorities || []).map(fundNumber => (
              <TabsTrigger key={`fund-tab-${fundNumber}`} value={`fund${fundNumber}`}>
                Fund {fundNumber}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Content for each fund tab */}
          {(inputData.withdrawal_priorities || []).map(fundNumber => (
            <TabsContent key={`fund-content-${fundNumber}`} value={`fund${fundNumber}`} className="pt-4">
              {/* Nested tabs for Basic/Advanced within each fund tab */}
              <Tabs defaultValue="basic" value={activeContentTab} onValueChange={setActiveContentTab} className="mt-2">
                <TabsList className="grid grid-cols-2">
                  <TabsTrigger value="basic">Basic Settings</TabsTrigger>
                  <TabsTrigger value="advanced">Investment Periods</TabsTrigger>
                </TabsList>

                {/* Basic Settings Tab */}
                <TabsContent value="basic" className="space-y-4 pt-4">
                  <Card className="p-4 border-l-4 border-l-amber-500">
                    <h3 className="text-sm font-medium mb-3">Initial Investment & Contributions</h3>
                    <div className="grid gap-4">
                      <div className="grid grid-cols-1 gap-4 mb-4">
                        {/* Initial Investment Fields */}
                        <div>
                          <LabelWithTooltip
                            htmlFor={`initial_investment${fundNumber}`}
                            label="Initial Investment"
                            tooltipText="The amount of money you start with in your investment portfolio"
                          />
                          <Input
                            id={`initial_investment${fundNumber}`}
                            type="number"
                            value={(inputData as any)[`initial_investment${fundNumber}`] || (fundNumber === 1 ? inputData.initial_investment : 0) || 0}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              setInputData(prev => ({
                                ...prev,
                                [`initial_investment${fundNumber}`]: value,
                                // Also update legacy field for backward compatibility if this is fund 1
                                ...(fundNumber === 1 ? { initial_investment: value } : {})
                              }));
                            }}
                            className="bg-white dark:bg-gray-800"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <LabelWithTooltip
                            htmlFor={`annual_investment_contribution${fundNumber}`}
                            label="Annual Contribution"
                            tooltipText="How much you plan to invest in this fund each year"
                          />
                          <Input
                            id={`annual_investment_contribution${fundNumber}`}
                            type="number"
                            value={(inputData as any)[`annual_investment_contribution${fundNumber}`] || (fundNumber === 1 ? inputData.annual_investment_contribution : 0)}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              setInputData(prev => ({
                                ...prev,
                                [`annual_investment_contribution${fundNumber}`]: value,
                                // Also update legacy field for backward compatibility if this is fund 1
                                ...(fundNumber === 1 ? { annual_investment_contribution: value } : {})
                              }));
                            }}
                            className="bg-white dark:bg-gray-800"
                          />
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor={`contribution_start${fundNumber}`}
                            label="Start Age"
                            tooltipText="Age when you'll begin making annual contributions to this fund"
                          />
                            <Input
                              id={`contribution_start${fundNumber}`}
                              type="number"
                              value={(inputData as any)[`contribution_period${fundNumber}`]?.[0] ??
                                    (fundNumber === 1 ? inputData.contribution_period?.[0] : null) ??
                                    inputData.starting_age}
                              onChange={(e) => {
                                const newStart = parseInt(e.target.value);
                                setInputData(prev => {
                                  const periodKey = `contribution_period${fundNumber}`;
                                  const currentEnd = (prev as any)[periodKey]?.[1] ??
                                                   (fundNumber === 1 ? prev.contribution_period?.[1] : null) ??
                                                   prev.ending_age;

                                  return {
                                    ...prev,
                                    [periodKey]: [newStart, currentEnd],
                                    // Also update legacy field for backward compatibility if this is fund 1
                                    ...(fundNumber === 1 ? {
                                      contribution_period: [newStart, currentEnd]
                                    } : {})
                                  };
                                });
                              }}
                              onBlur={(e) => {
                                const newStart = parseInt(e.target.value);
                                const clampedStart = Math.min(Math.max(newStart, inputData.starting_age), inputData.ending_age);
                                setInputData(prev => {
                                  const periodKey = `contribution_period${fundNumber}`;
                                  const currentEnd = (prev as any)[periodKey]?.[1] ??
                                                   (fundNumber === 1 ? prev.contribution_period?.[1] : null) ??
                                                   prev.ending_age;

                                  return {
                                    ...prev,
                                    [periodKey]: [clampedStart, currentEnd],
                                    // Also update legacy field for backward compatibility if this is fund 1
                                    ...(fundNumber === 1 ? {
                                      contribution_period: [clampedStart, currentEnd]
                                    } : {})
                                  };
                                });
                              }}
                              className="bg-white dark:bg-gray-800"
                            />
                        </div>
                        <div>
                          <LabelWithTooltip
                            htmlFor={`contribution_end${fundNumber}`}
                            label="End Age"
                            tooltipText="Age when you'll stop making annual contributions to this fund"
                          />
                            <Input
                              id={`contribution_end${fundNumber}`}
                              type="number"
                              value={(inputData as any)[`contribution_period${fundNumber}`]?.[1] ??
                                    (fundNumber === 1 ? inputData.contribution_period?.[1] : null) ??
                                    inputData.ending_age}
                              onChange={(e) => {
                                const newEnd = parseInt(e.target.value);
                                setInputData(prev => {
                                  const periodKey = `contribution_period${fundNumber}`;
                                  const currentStart = (prev as any)[periodKey]?.[0] ??
                                                   (fundNumber === 1 ? prev.contribution_period?.[0] : null) ??
                                                   prev.starting_age;

                                  return {
                                    ...prev,
                                    [periodKey]: [currentStart, newEnd],
                                    // Also update legacy field for backward compatibility if this is fund 1
                                    ...(fundNumber === 1 ? {
                                      contribution_period: [currentStart, newEnd]
                                    } : {})
                                  };
                                });
                              }}
                              onBlur={(e) => {
                                const newEnd = parseInt(e.target.value);
                                const clampedEnd = Math.min(Math.max(newEnd, inputData.starting_age), inputData.ending_age);
                                setInputData(prev => {
                                  const periodKey = `contribution_period${fundNumber}`;
                                  const currentStart = (prev as any)[periodKey]?.[0] ??
                                                   (fundNumber === 1 ? prev.contribution_period?.[0] : null) ??
                                                   prev.starting_age;

                                  return {
                                    ...prev,
                                    [periodKey]: [currentStart, clampedEnd],
                                    // Also update legacy field for backward compatibility if this is fund 1
                                    ...(fundNumber === 1 ? {
                                      contribution_period: [currentStart, clampedEnd]
                                    } : {})
                                  };
                                });
                              }}
                              className="bg-white dark:bg-gray-800"
                            />
                        </div>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4 border-l-4 border-l-green-500">
                    <h3 className="text-sm font-medium mb-3">Tax Settings</h3>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <LabelWithTooltip
                          htmlFor="tax_type"
                          label="Tax Type"
                          tooltipText="PIE: Portfolio Investment Entity tax rates, MTR: Marginal Tax Rate"
                        />
                        <RadioGroup
                          defaultValue={inputData.inv_tax ?? 'PIE'}
                          onValueChange={(value) => {
                            setInputData((prevData) => ({
                              ...prevData,
                              inv_tax: value
                            }));
                          }}
                          className="flex gap-6 mt-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="PIE" id="pie" />
                            <LabelWithTooltip
                              htmlFor="pie"
                              label="PIE"
                              tooltipText="Portfolio Investment Entity tax rates"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="MTR" id="mtr" />
                            <LabelWithTooltip
                              htmlFor="mtr"
                              label="MTR"
                              tooltipText="Marginal Tax Rate based on your income"
                            />
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </Card>
                </TabsContent>

                {/* Advanced Settings Tab */}
                <TabsContent value="advanced" className="space-y-4 pt-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium">Investment Periods - Fund {fundNumber}</h3>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Info className="h-4 w-4 mr-1" />
                            <span>Define different investment strategies for different age periods</span>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            You can define different investment strategies for different periods of your life.
                            For example, you might want a more aggressive strategy when you're younger and a more conservative one as you approach retirement.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  {validationError && (
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm rounded border border-red-200 dark:border-red-800 flex items-start gap-2">
                      <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                      <div>{validationError}</div>
                    </div>
                  )}

                  <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                    {(fundPeriodsMap[fundNumber] || []).map((period, index) => {
                      // No need for fund info here as we're using it directly in the badge
                      return (
                        <Card key={index} className="overflow-hidden">
                          <button
                            className="w-full px-4 py-3 flex justify-between items-center text-black dark:text-white"
                            style={{
                              backgroundColor: period.fundId
                                ? `${getFundTypeColor(period.fundType, period.fundId)}40`
                                : period.fundType === 'Custom'
                                  ? '#9333ea20'
                                  : '#6b728020'
                            }}
                            onClick={() => togglePeriod(index)}
                          >
                            <div className="flex items-center gap-2">
                              <span className="font-medium">Period {index + 1}</span>
                              <span className="text-sm">
                                (Ages {period.period[0]} - {period.period[1]})
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              {index > 0 && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRemovePeriod(index);
                                  }}
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Badge
                                variant="outline"
                                className="bg-white/80 dark:bg-black/20 mr-1"
                                style={period.fundId ? {
                                  borderColor: getFundTypeColor(period.fundType, period.fundId)
                                } : {}}
                              >
                                {period.fundId ?
                                  funds.find(f => f.id === period.fundId)?.name :
                                  period.fundType
                                }
                              </Badge>
                              <ChevronDown
                                className={`h-4 w-4 transition-transform ${openPeriodIndexMap[fundNumber] === index ? 'rotate-180' : ''}`}
                              />
                            </div>
                          </button>

                          {openPeriodIndexMap[fundNumber] === index && (
                            <div className="p-4 space-y-4">
                              <div className="grid grid-cols-3 gap-4">
                                <div>
                                  <LabelWithTooltip
                                    htmlFor={`fund-type-${fundNumber}-${index}`}
                                    label="Fund Type"
                                    tooltipText="The investment strategy for this period"
                                  />
                                  <Select
                                    value={period.fundId || period.fundType}
                                    onValueChange={(value) => handleFundTypeChange(index, value)}
                                  >
                                    <SelectTrigger id={`fund-type-${fundNumber}-${index}`} className="h-10 bg-white dark:bg-gray-800">
                                      <SelectValue placeholder="Select fund type">
                                        {period.fundId ? (
                                          funds.find(f => f.id === period.fundId)?.name || period.fundType
                                        ) : period.fundType}
                                      </SelectValue>
                                    </SelectTrigger>
                                    <SelectContent>
                                      {/* Custom option for manual values */}
                                      <SelectItem key="Custom" value="Custom">Custom (Manual Values)</SelectItem>

                                      {/* Built-in fund types */}
                                      <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                        Standard Funds
                                      </div>
                                      {Object.keys(FUND_TYPES).filter(key => key !== 'Custom').map((fundType) => (
                                        <SelectItem key={fundType} value={fundType}>
                                          {fundType} ({FUND_TYPES[fundType as keyof typeof FUND_TYPES].return}%, {FUND_TYPES[fundType as keyof typeof FUND_TYPES].stdDev}%)
                                        </SelectItem>
                                      ))}

                                      {/* Custom funds from scenario metrics */}
                                      {!fundsLoading && getFundsByType('investment').length > 0 && (
                                        <>
                                          <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
                                            My Funds
                                          </div>
                                          {getFundsByType('investment').map((fund) => (
                                            <SelectItem key={fund.id} value={fund.id}>
                                              {fund.name} ({fund.return}%, {fund.stdDev}%)
                                            </SelectItem>
                                          ))}
                                        </>
                                      )}
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <LabelWithTooltip
                                    htmlFor={`period-start-${fundNumber}-${index}`}
                                    label="Start Age"
                                    tooltipText="Age when this investment strategy begins"
                                  />
                                  <Input
                                    id={`period-start-${fundNumber}-${index}`}
                                    type="number"
                                    value={period.period[0]}
                                    onChange={(e) => handlePeriodChange(index, 'start', e.target.value)}
                                    onBlur={(e) => {
                                      const newStart = parseInt(e.target.value);
                                      const currentPeriods = fundPeriodsMap[fundNumber] || [];
                                      let clampedStart = newStart;

                                      // First period must be >= start age
                                      if (index === 0) {
                                        clampedStart = Math.max(newStart, inputData.starting_age);
                                      }

                                      // Must be > previous period's end age
                                      if (index > 0) {
                                        clampedStart = Math.max(newStart, currentPeriods[index - 1].period[1] + 1);
                                      }

                                      // Must be < next period's start age (if exists)
                                      if (index < currentPeriods.length - 1) {
                                        clampedStart = Math.min(clampedStart, currentPeriods[index + 1].period[0] - 1);
                                      }

                                      // Must be <= current period's end age
                                      clampedStart = Math.min(clampedStart, period.period[1]);

                                      if (clampedStart !== newStart) {
                                        handlePeriodChange(index, 'start', clampedStart.toString());
                                      }
                                    }}
                                    placeholder="Start Age"
                                    className="h-10 bg-white dark:bg-gray-800"
                                  />
                                </div>
                                <div>
                                  <LabelWithTooltip
                                    htmlFor={`period-end-${fundNumber}-${index}`}
                                    label="End Age"
                                    tooltipText="Age when this investment strategy ends"
                                  />
                                  <Input
                                    id={`period-end-${fundNumber}-${index}`}
                                    type="number"
                                    value={period.period[1]}
                                    onChange={(e) => handlePeriodChange(index, 'end', e.target.value)}
                                    onBlur={(e) => {
                                      const newEnd = parseInt(e.target.value);
                                      const currentPeriods = fundPeriodsMap[fundNumber] || [];
                                      let clampedEnd = newEnd;

                                      // Last period must be <= end age
                                      if (index === currentPeriods.length - 1) {
                                        clampedEnd = Math.min(newEnd, inputData.ending_age);
                                      }

                                      // Must be >= current period's start age
                                      clampedEnd = Math.max(clampedEnd, period.period[0]);

                                      // Must be < next period's start age (if exists)
                                      if (index < currentPeriods.length - 1) {
                                        clampedEnd = Math.min(clampedEnd, currentPeriods[index + 1].period[0] - 1);
                                      }

                                      // Must be > previous period's end age
                                      if (index > 0) {
                                        clampedEnd = Math.max(clampedEnd, currentPeriods[index - 1].period[1] + 1);
                                      }

                                      if (clampedEnd !== newEnd) {
                                        handlePeriodChange(index, 'end', clampedEnd.toString());
                                      }
                                    }}
                                    placeholder="End Age"
                                    className="h-10 bg-white dark:bg-gray-800"
                                  />
                                </div>
                              </div>

                              {period.fundType === 'Custom' && (
                                <Card className="p-4 mt-2 bg-purple-50 dark:bg-purple-900/10 border-purple-200 dark:border-purple-800">
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">Custom Parameters</h4>
                                    <div className="grid grid-cols-3 gap-4">
                                      <div className="space-y-2">
                                        <LabelWithTooltip
                                          htmlFor={`custom-return-${fundNumber}-${index}`}
                                          label="Rate of Return (%)"
                                          tooltipText="Expected annual return percentage for this investment strategy"
                                        />
                                        <Input
                                          id={`custom-return-${fundNumber}-${index}`}
                                          type="number"
                                          value={period.customReturn}
                                          onChange={(e) => handleCustomValueChange(index, 'return', e.target.value)}
                                          placeholder="Enter return rate"
                                          className="h-10 bg-white dark:bg-gray-800"
                                        />
                                      </div>
                                      <div className="space-y-2">
                                        <LabelWithTooltip
                                          htmlFor={`custom-stddev-${fundNumber}-${index}`}
                                          label="Standard Deviation (%)"
                                          tooltipText="Measure of volatility/risk in the investment returns"
                                        />
                                        <Input
                                          id={`custom-stddev-${fundNumber}-${index}`}
                                          type="number"
                                          value={period.customStdDev}
                                          onChange={(e) => handleCustomValueChange(index, 'stdDev', e.target.value)}
                                          placeholder="Enter std deviation"
                                          className="h-10 bg-white dark:bg-gray-800"
                                        />
                                      </div>
                                      <div className="space-y-2">
                                        <LabelWithTooltip
                                          htmlFor={`income-portion-${fundNumber}-${index}`}
                                          label="Income Portion (%)"
                                          tooltipText="Percentage of returns that are taxable income (vs capital gains)"
                                        />
                                        <Input
                                          id={`income-portion-${fundNumber}-${index}`}
                                          type="number"
                                          value={period.incomePortion || (inputData as any)[`fund${fundNumber}_income_portion`] || 60}
                                          onChange={(e) => handleCustomValueChange(index, 'incomePortion', e.target.value)}
                                          placeholder="Enter income portion"
                                          className="h-10 bg-white dark:bg-gray-800"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                              )}
                            </div>
                          )}
                        </Card>
                      );
                    })}

                    <Button
                      variant="outline"
                      onClick={handleAddPeriod}
                      className="w-full mt-4"
                    >
                      <Plus className="h-4 w-4 mr-2" /> Add Investment Period
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </TabsContent>
          ))}
        </Tabs>

        <DialogFooter className="flex justify-between items-center mt-6 gap-4">
          <div className="flex-grow">
            <Button variant="outline" onClick={() => {
              // Reset fund periods for the current fund
              const fundNumber = getCurrentFundNumber();

              // Reset the fund periods
              setFundPeriodsMap(prev => ({
                ...prev,
                [fundNumber]: [{
                  fundType: 'Balanced',
                  period: [inputData.starting_age, inputData.ending_age]
                }]
              }));

              // Reset to first period being open
              setOpenPeriodIndexMap(prev => ({
                ...prev,
                [fundNumber]: 0
              }));

              // Reset the contribution period for this fund
              setInputData(prev => {
                const periodKey = `contribution_period${fundNumber}`;
                return {
                  ...prev,
                  [periodKey]: [prev.starting_age, prev.ending_age],
                  // Also update legacy field for backward compatibility if this is fund 1
                  ...(fundNumber === 1 ? {
                    contribution_period: [prev.starting_age, prev.ending_age]
                  } : {})
                };
              });

              setValidationError(null);
            }}>
              Reset to Default
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
            <Button onClick={() => {
              handleSave();
              setOpen(false);
            }}>Save Changes</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
