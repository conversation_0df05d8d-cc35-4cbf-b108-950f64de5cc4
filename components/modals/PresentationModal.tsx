import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Check, ChevronsUpDown, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";

interface PresentationModalProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedHousehold?: { id: number; householdName: string };
  preselectedScenarios?: number[];
  onScenariosUpdate?: (householdId: number, scenarioIds: number[]) => void;
}

interface Household {
  id: number;
  householdName: string;
}

interface Scenario {
  id: number;
  scenario_name: string;
  household_id: number;
  household_name: string;
}

export default function PresentationModal({
  isOpen,
  onClose,
  preselectedHousehold,
  preselectedScenarios,
  onScenariosUpdate
}: PresentationModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedHousehold, setSelectedHousehold] = useState<string>('');
  const [selectedHouseholdName, setSelectedHouseholdName] = useState<string>('');
  const [households, setHouseholds] = useState<Household[]>([]);
  const [scenarios, setScenarios] = useState<Scenario[]>([]);
  const [selectedScenarios, setSelectedScenarios] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  // Set preselected household and scenarios if provided
  useEffect(() => {
    if (preselectedHousehold) {
      setSelectedHousehold(preselectedHousehold.id.toString());
      setSelectedHouseholdName(preselectedHousehold.householdName);
      fetchScenariosForHousehold(preselectedHousehold.id);
    }
    if (preselectedScenarios && preselectedScenarios.length > 0) {
      setSelectedScenarios(preselectedScenarios);
    }
  }, [preselectedHousehold, preselectedScenarios]);

  // Fetch households on component mount
  useEffect(() => {
    if (isOpen && !preselectedHousehold) {
      fetchHouseholds();
    }
  }, [isOpen, preselectedHousehold]);

  const fetchHouseholds = async () => {
    setIsLoading(true);
    const supabase = createClient();

    try {
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('householdName', { ascending: true });

      if (error) throw error;

      setHouseholds(data || []);
    } catch (error) {
      console.error('Error fetching households:', error);
      toast({
        title: "Error",
        description: "Failed to load households",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchScenariosForHousehold = async (householdId: number) => {
    setIsLoading(true);
    const supabase = createClient();

    try {
      const { data, error } = await supabase
        .from('scenarios_data1')
        .select('id, scenario_name, household_id, household_name')
        .eq('household_id', householdId)
        .order('scenario_name', { ascending: true });

      if (error) throw error;

      setScenarios(data || []);
      setSelectedScenarios([]); // Reset selected scenarios when household changes
    } catch (error) {
      console.error('Error fetching scenarios:', error);
      toast({
        title: "Error",
        description: "Failed to load scenarios for this household",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleHouseholdChange = (value: string) => {
    setSelectedHousehold(value);
    setOpen(false);
    const selected = households.find(h => h.id.toString() === value);
    if (selected) {
      setSelectedHouseholdName(selected.householdName);
      fetchScenariosForHousehold(selected.id);
    }
  };

  const handleScenarioToggle = (scenarioId: number) => {
    setSelectedScenarios(prev => {
      // If already selected, remove it
      if (prev.includes(scenarioId)) {
        return prev.filter(id => id !== scenarioId);
      }

      // If we already have 3 scenarios selected, show a toast and don't add
      if (prev.length >= 3) {
        toast({
          title: "Maximum Reached",
          description: "You can only select up to 3 scenarios for presentation",
          variant: "default",
        });
        return prev;
      }

      // Otherwise, add it to the selection
      return [...prev, scenarioId];
    });
  };

  const handlePresent = () => {
    if (selectedScenarios.length === 0) {
      toast({
        title: "No Scenarios Selected",
        description: "Please select at least one scenario to present",
        variant: "destructive",
      });
      return;
    }

    if (onScenariosUpdate) {
      onScenariosUpdate(parseInt(selectedHousehold), selectedScenarios);
      onClose();
    } else {
      // Navigate to the presentation page with the selected scenarios
      const queryParams = new URLSearchParams();
      queryParams.append('household_id', selectedHousehold);
      selectedScenarios.forEach((id, index) => {
        queryParams.append(`scenarioId${index + 1}`, id.toString());
      });

      router.push(`/protected/presentation?${queryParams.toString()}`);
      onClose();
    }
  };

  const isFormValid = selectedHousehold !== '' && selectedScenarios.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Present Scenarios</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="household">Household</Label>
            {preselectedHousehold ? (
              <Input
                id="household"
                value={preselectedHousehold.householdName}
                disabled
                className="bg-muted"
              />
            ) : (
              <div className="relative">
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className="w-full justify-between"
                  onClick={() => {
                    setOpen(!open);
                  }}
                  disabled={isLoading}
                >
                  {selectedHousehold
                    ? selectedHouseholdName
                    : "Select household..."}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
                {open && (
                  <div className="absolute top-full z-10 w-full rounded-md border bg-popover shadow-md mt-1">
                    <Input
                      placeholder="Search households..."
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      className="border-0 focus-visible:ring-0"
                    />
                    <ScrollArea className="h-72">
                      <div className="p-1">
                        {households
                          .filter(household =>
                            household.householdName.toLowerCase().includes(search.toLowerCase())
                          )
                          .map(household => (
                            <div
                              key={household.id}
                              className={cn(
                                "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                                selectedHousehold === household.id.toString() && "bg-accent text-accent-foreground"
                              )}
                              onClick={() => handleHouseholdChange(household.id.toString())}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedHousehold === household.id.toString() ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {household.householdName}
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>
            )}
          </div>

          {selectedHousehold && !isLoading && (
            <div className="grid gap-2">
              <Label>Select up to 3 scenarios to present</Label>
              {scenarios.length === 0 ? (
                <div className="text-sm text-muted-foreground p-2 border rounded-md">
                  No scenarios found for this household
                </div>
              ) : (
                <ScrollArea className="h-[200px] border rounded-md p-2">
                  {scenarios.map(scenario => (
                    <div key={scenario.id} className="flex items-center space-x-2 py-2">
                      <Checkbox
                        id={`scenario-${scenario.id}`}
                        checked={selectedScenarios.includes(scenario.id)}
                        onCheckedChange={() => handleScenarioToggle(scenario.id)}
                      />
                      <Label
                        htmlFor={`scenario-${scenario.id}`}
                        className="cursor-pointer flex-grow"
                      >
                        {scenario.scenario_name}
                      </Label>
                    </div>
                  ))}
                </ScrollArea>
              )}
              <div className="text-sm text-muted-foreground mt-1">
                {selectedScenarios.length} of 3 scenarios selected
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            onClick={handlePresent}
            disabled={!isFormValid || isLoading}
          >
            Present Scenarios
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
