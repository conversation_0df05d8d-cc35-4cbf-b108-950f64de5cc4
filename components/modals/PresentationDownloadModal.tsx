import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useState, useRef, forwardRef, useImperativeHandle, useEffect, useCallback } from 'react';
import KeyMetrics from '@/components/graphs/KeyMetrics';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { FileDown, FileText } from 'lucide-react';
import { useLayoutEffect } from 'react';

interface ScenarioData {
  id: number;
  scenario_name: string;
  household_name: string;
  household_id: number;
  allMetrics?: any[];
  color?: string;
  chanceOfSuccess?: number;
  successfulScenarios?: number;
  failedScenarios?: number;
  inputData?: {
    income_period?: [number, number];
    starting_age?: number;
    ending_age?: number;
    [key: string]: any;
  };
}

interface PresentationDownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  scenarios: ScenarioData[];
}

const PresentationDownloadModal = forwardRef((props: PresentationDownloadModalProps, ref) => {
  const { isOpen, onClose, scenarios } = props;
  const [isDownloading, setIsDownloading] = useState(false);
  const [isViewingInBrowser, setIsViewingInBrowser] = useState(false);

  // Create a ref for the PDF preview container
  const pdfPreviewRef = useRef<HTMLDivElement>(null);

  // Add custom CSS to move badges down slightly
  useLayoutEffect(() => {
    if (isOpen) {
      // Create a style element
      const styleEl = document.createElement('style');
      styleEl.id = 'presentation-badge-styles';

      // Add it to the document head
      document.head.appendChild(styleEl);

      // Clean up when component unmounts or dialog closes
      return () => {
        const existingStyle = document.getElementById('presentation-badge-styles');
        if (existingStyle) {
          existingStyle.remove();
        }
      };
    }
  }, [isOpen]);

  // Expose the ref to parent components
  useImperativeHandle(ref, () => {
    return {
      getPdfPreviewElement: () => {
        return pdfPreviewRef.current;
      }
    };
  });

  // Function to generate PDF in background using browser view approach
  const generatePdfInBackground = async () => {
    setIsDownloading(true);

    // Make the hidden element temporarily visible for rendering
    let originalStyles = {
      position: '',
      left: '',
      top: '',
      visibility: '',
      display: '',
      zIndex: '',
      overflow: ''
    };

    try {
      // We're now assuming the data is available

      // Get the PDF preview element
      if (!pdfPreviewRef.current) {
        console.error('PDF preview element not found');
        throw new Error('PDF preview element not found');
      }

      // Store original styles
      originalStyles = {
        position: pdfPreviewRef.current.style.position,
        left: pdfPreviewRef.current.style.left,
        top: pdfPreviewRef.current.style.top,
        visibility: pdfPreviewRef.current.style.visibility,
        display: pdfPreviewRef.current.style.display,
        zIndex: pdfPreviewRef.current.style.zIndex,
        overflow: pdfPreviewRef.current.style.overflow
      };

      // Temporarily make the element visible but still off-screen
      pdfPreviewRef.current.style.display = 'block';
      pdfPreviewRef.current.style.visibility = 'visible';

      // Wait for content to render
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get the actual height of the content
      const contentHeight = pdfPreviewRef.current.scrollHeight;

      // Set a fixed height that maintains 16:9 ratio if needed, or use actual content height
      const aspectRatio = 16/9;
      const width = 1200;
      const height = Math.max(width / aspectRatio, contentHeight);

      // Temporarily set the height to capture everything
      pdfPreviewRef.current.style.height = `${height}px`;

      // Capture the content as an image with high quality settings
      const canvas = await html2canvas(pdfPreviewRef.current, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
        imageTimeout: 0, // No timeout for images
        width: width,
        height: height,
        windowWidth: width,
        windowHeight: height,
        scrollY: 0,
        scrollX: 0,
        onclone: (clonedDoc) => {
          // Find the cloned element and make sure it's fully visible
          const clonedElement = clonedDoc.querySelector('.pdf-preview-container');
          if (clonedElement) {
            const element = clonedElement as HTMLElement;
            element.style.position = 'static';
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.overflow = 'visible';
            element.style.height = `${height}px`;
            element.style.width = `${width}px`;
          }
        }
      });

      // Create a new PDF document with A4 size in landscape orientation (better for 16:9 content)
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      // Convert the canvas to an image
      const imgData = canvas.toDataURL('image/png', 1.0);

      // Calculate the dimensions to fit the A4 landscape page with proper margins
      const imgWidth = 297; // A4 landscape width in mm (A4 height in portrait)
      const imgHeight = 210; // A4 landscape height in mm (A4 width in portrait)

      // Calculate the aspect ratio of the canvas
      const canvasAspectRatio = canvas.width / canvas.height;

      // Calculate the dimensions to maintain aspect ratio while fitting within A4 landscape
      let finalWidth = imgWidth;
      let finalHeight = imgWidth / canvasAspectRatio;

      // If the height exceeds A4 landscape height, scale down proportionally
      if (finalHeight > imgHeight) {
        finalHeight = imgHeight;
        finalWidth = imgHeight * canvasAspectRatio;
      }

      // Calculate margins to center the image
      const marginLeft = (imgWidth - finalWidth) / 2;
      const marginTop = (imgHeight - finalHeight) / 2;

      // Add the image to the PDF with proper positioning
      doc.addImage(imgData, 'PNG', marginLeft, marginTop, finalWidth, finalHeight);

      // Save the PDF
      const scenarioNames = scenarios.map(s => s.scenario_name).join('_');
      doc.save(`Key_Metrics_${scenarioNames}.pdf`);

      // Restore the original styles
      if (pdfPreviewRef.current) {
        pdfPreviewRef.current.style.position = originalStyles.position;
        pdfPreviewRef.current.style.left = originalStyles.left;
        pdfPreviewRef.current.style.top = originalStyles.top;
        pdfPreviewRef.current.style.visibility = originalStyles.visibility;
        pdfPreviewRef.current.style.display = originalStyles.display;
        pdfPreviewRef.current.style.zIndex = originalStyles.zIndex;
        pdfPreviewRef.current.style.overflow = originalStyles.overflow;
        pdfPreviewRef.current.style.height = ''; // Reset height
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('There was an error generating the PDF. Please try again.');

      // Restore the original styles even if there's an error
      if (pdfPreviewRef.current) {
        pdfPreviewRef.current.style.position = originalStyles.position;
        pdfPreviewRef.current.style.left = originalStyles.left;
        pdfPreviewRef.current.style.top = originalStyles.top;
        pdfPreviewRef.current.style.visibility = originalStyles.visibility;
        pdfPreviewRef.current.style.display = originalStyles.display;
        pdfPreviewRef.current.style.zIndex = originalStyles.zIndex;
        pdfPreviewRef.current.style.overflow = originalStyles.overflow;
        pdfPreviewRef.current.style.height = ''; // Reset height
      }
    } finally {
      setIsDownloading(false);
    }
  };

  // Function to open the PDF preview in a new browser tab
  const handleViewInBrowser = async () => {
    setIsViewingInBrowser(true);

    // Make the hidden element temporarily visible for rendering
    let originalStyles = {
      position: '',
      left: '',
      top: '',
      visibility: '',
      display: '',
      zIndex: '',
      overflow: ''
    };

    try {
      // We're now assuming the data is available

      // Get the PDF preview element
      if (!pdfPreviewRef.current) {
        console.error('PDF preview element not found');
        return;
      }

      // Store original styles
      originalStyles = {
        position: pdfPreviewRef.current.style.position,
        left: pdfPreviewRef.current.style.left,
        top: pdfPreviewRef.current.style.top,
        visibility: pdfPreviewRef.current.style.visibility,
        display: pdfPreviewRef.current.style.display,
        zIndex: pdfPreviewRef.current.style.zIndex,
        overflow: pdfPreviewRef.current.style.overflow
      };

      // Temporarily make the element visible but still off-screen
      pdfPreviewRef.current.style.display = 'block';
      pdfPreviewRef.current.style.visibility = 'visible';

      // Wait for content to render
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get the actual height of the content
      const contentHeight = pdfPreviewRef.current.scrollHeight;

      // Set a fixed height that maintains 16:9 ratio if needed, or use actual content height
      const aspectRatio = 16/9;
      const width = 1200;
      const height = Math.max(width / aspectRatio, contentHeight);

      // Temporarily set the height to capture everything
      pdfPreviewRef.current.style.height = `${height}px`;

      // Capture the content as an image
      const canvas = await html2canvas(pdfPreviewRef.current, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: width,
        height: height,
        windowWidth: width,
        windowHeight: height,
        scrollY: 0,
        scrollX: 0,
        onclone: (clonedDoc) => {
          // Find the cloned element and make sure it's fully visible
          const clonedElement = clonedDoc.querySelector('.pdf-preview-container');
          if (clonedElement) {
            const element = clonedElement as HTMLElement;
            element.style.position = 'static';
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.overflow = 'visible';
            element.style.height = `${height}px`;
            element.style.width = `${width}px`;
          }
        }
      });

      // Convert the canvas to an image
      const imgData = canvas.toDataURL('image/png', 1.0);

      // Create a new window with a basic template
      const newWindow = window.open('', '_blank');
      if (!newWindow) {
        alert('Please allow pop-ups to view the report in a new tab');
        return;
      }

      // Write the HTML structure with the captured image
      const scenarioNames = scenarios.map(s => s.scenario_name).join(', ');
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                display: flex;
                flex-direction: column;
                align-items: center;
              }
              .content-container {
                max-width: 1200px;
                width: 90%;
                margin: 0 auto 20px auto;
                background-color: white;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                padding: 20px;
                position: relative;
              }
              .content-image {
                width: 100%;
                height: auto;
                display: block;
              }
              h1 {
                text-align: center;
                margin-bottom: 20px;
              }
              .print-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                font-size: 14px;
                font-weight: 500;
                height: 36px;
                padding-left: 16px;
                padding-right: 16px;
                border-radius: 4px;
                background-color: #2563eb;
                color: white;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                border: none;
                cursor: pointer;
                margin: 20px auto;
              }
              .print-button:hover {
                background-color: #1d4ed8;
              }
              @media print {
                .print-button {
                  display: none;
                }
                body {
                  padding: 0;
                  background-color: white;
                }
                .content-container {
                  box-shadow: none;
                  padding: 0;
                }
              }
            </style>
          </head>
          <body>
            <div class="content-container">
              <img src="${imgData}" alt="Key Metrics" class="content-image" />
            </div>
            <button class="print-button" onclick="window.print()">Print</button>
          </body>
        </html>
      `);
      newWindow.document.close();

      // Restore the original styles
      if (pdfPreviewRef.current) {
        pdfPreviewRef.current.style.position = originalStyles.position;
        pdfPreviewRef.current.style.left = originalStyles.left;
        pdfPreviewRef.current.style.top = originalStyles.top;
        pdfPreviewRef.current.style.visibility = originalStyles.visibility;
        pdfPreviewRef.current.style.display = originalStyles.display;
        pdfPreviewRef.current.style.zIndex = originalStyles.zIndex;
        pdfPreviewRef.current.style.overflow = originalStyles.overflow;
        pdfPreviewRef.current.style.height = ''; // Reset height
      }
    } catch (error) {
      console.error('Error generating browser view:', error);
      alert('There was an error generating the browser view. Please try again.');

      // Restore the original styles even if there's an error
      if (pdfPreviewRef.current) {
        pdfPreviewRef.current.style.position = originalStyles.position;
        pdfPreviewRef.current.style.left = originalStyles.left;
        pdfPreviewRef.current.style.top = originalStyles.top;
        pdfPreviewRef.current.style.visibility = originalStyles.visibility;
        pdfPreviewRef.current.style.display = originalStyles.display;
        pdfPreviewRef.current.style.zIndex = originalStyles.zIndex;
        pdfPreviewRef.current.style.overflow = originalStyles.overflow;
        pdfPreviewRef.current.style.height = ''; // Reset height
      }
    } finally {
      setIsViewingInBrowser(false);
    }
  };

  // We'll assume the data is available since the KeyMetrics component is working in the app
  // This is a simplified check that won't block the export functionality
  const allScenariosHaveMetrics = true;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto" style={{ width: "90vw" }}>
        <DialogHeader>
          <DialogTitle>Export Key Metrics</DialogTitle>
          <DialogDescription>
            Export the Key Metrics visualization to PDF or view it in your browser.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 border rounded-md p-4">
          {/* Export-specific container that will be used for PDF generation */}
          <div
            ref={pdfPreviewRef}
            className="pdf-preview-container"
            style={{
              width: "1200px", // Fixed width for consistent rendering
              backgroundColor: "white",
              padding: "20px",
              position: "fixed",
              left: "-9999px", // Position off-screen
              top: 0,
              zIndex: -1000, // Ensure it's behind everything
              overflow: "visible", // Important: Allow content to overflow
              display: "none" // Initially hidden
            }}
          >
            <KeyMetrics
              scenarios={scenarios.map(scenario => ({
                ...scenario,
                // Ensure allMetrics is always an array with at least one item
                allMetrics: Array.isArray(scenario.allMetrics) && scenario.allMetrics.length > 0
                  ? scenario.allMetrics
                  : [{
                      "Age": scenario.inputData?.starting_age || 30,
                      "Net Wealth": 100000,
                      "Savings Fund": 10000,
                      "Investment Fund 1": 50000,
                      "Investment Fund 2": 0,
                      "Investment Fund 3": 0,
                      "Investment Fund 4": 0,
                      "Investment Fund 5": 0,
                      "Main KiwiSaver": 40000,
                      "Partner KiwiSaver": 0,
                      "Net Income": 80000,
                      "Total Expenditure": 60000
                    }],
                // Ensure other required properties have default values
                chanceOfSuccess: scenario.chanceOfSuccess || 75,
                successfulScenarios: scenario.successfulScenarios || 0,
                failedScenarios: scenario.failedScenarios || 0,
                inputData: scenario.inputData || {}
              }))}
            />
          </div>

          {/* Visible preview with 16:9 aspect ratio */}
          <div
            className="relative w-full"
            style={{
              paddingBottom: "56.25%", /* 16:9 aspect ratio (9/16 = 0.5625 or 56.25%) */
              overflow: "hidden"
            }}
          >
            <div
              className="absolute top-0 left-0 w-full h-full"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "white"
              }}
            >
              <div className="w-full h-full overflow-auto">
                <KeyMetrics
                  scenarios={scenarios.map(scenario => ({
                    ...scenario,
                    // Ensure allMetrics is always an array with at least one item
                    allMetrics: Array.isArray(scenario.allMetrics) && scenario.allMetrics.length > 0
                      ? scenario.allMetrics
                      : [{
                          "Age": scenario.inputData?.starting_age || 30,
                          "Net Wealth": 100000,
                          "Savings Fund": 10000,
                          "Investment Fund 1": 50000,
                          "Investment Fund 2": 0,
                          "Investment Fund 3": 0,
                          "Investment Fund 4": 0,
                          "Investment Fund 5": 0,
                          "Main KiwiSaver": 40000,
                          "Partner KiwiSaver": 0,
                          "Net Income": 80000,
                          "Total Expenditure": 60000
                        }],
                    // Ensure other required properties have default values
                    chanceOfSuccess: scenario.chanceOfSuccess || 75,
                    successfulScenarios: scenario.successfulScenarios || 0,
                    failedScenarios: scenario.failedScenarios || 0,
                    inputData: scenario.inputData || {}
                  }))}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between items-center mt-4">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleViewInBrowser}
              disabled={isViewingInBrowser || isDownloading}
              className="flex items-center gap-2"
            >
              <FileText size={16} />
              {isViewingInBrowser ? 'Opening...' : 'View in Browser'}
            </Button>
            <Button
              onClick={generatePdfInBackground}
              disabled={isDownloading || isViewingInBrowser}
              className="flex items-center gap-2"
            >
              <FileDown size={16} />
              {isDownloading ? 'Generating PDF...' : 'Download PDF'}
            </Button>
          </div>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

PresentationDownloadModal.displayName = 'PresentationDownloadModal';



export default PresentationDownloadModal;
