import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';

interface CreateScribbleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateScribble: (householdId: string) => void;
}

interface Household {
  id: number;
  householdName: string;
}

export default function CreateScribbleModal({ isOpen, onClose, onCreateScribble }: CreateScribbleModalProps) {
  const [selectedHousehold, setSelectedHousehold] = useState('');
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchHouseholds = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('householdName', { ascending: true });

      if (error) {
        console.error('Error fetching households:', error);
      } else {
        setHouseholds(data || []);
      }
      setIsLoading(false);
    };

    fetchHouseholds();
  }, []);

  const handleCreate = () => {
    if (selectedHousehold) {
      onCreateScribble(selectedHousehold);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Scribble</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="household" className="text-right">
                Select Household
              </Label>
              <Select onValueChange={setSelectedHousehold} value={selectedHousehold}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select a household" />
                </SelectTrigger>
                <SelectContent>
                  {households.map((household) => (
                    <SelectItem key={household.id} value={household.id.toString()}>
                      {household.householdName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button onClick={handleCreate} disabled={!selectedHousehold}>
            Create Scribble
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}