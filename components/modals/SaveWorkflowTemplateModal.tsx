'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';

interface SaveWorkflowTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  workflowData: any; // The workflow data to save as a template
}

export default function SaveWorkflowTemplateModal({
  isOpen,
  onClose,
  workflowData
}: SaveWorkflowTemplateModalProps) {
  const [templateName, setTemplateName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [actualWorkflowData, setActualWorkflowData] = useState<any>(null);
  const supabase = createClient();

  // When the modal opens, get the latest workflow data
  useEffect(() => {
    const fetchWorkflowData = async () => {
      if (isOpen && typeof window !== 'undefined' && window.saveWorkflow) {
        try {
          // Call the saveWorkflow function with returnData=true to get the data without saving
          // Since saveWorkflow returns a Promise, we need to await it
          const data = await window.saveWorkflow(true);

          // Ensure we have a valid object with nodes and edges
          if (data && typeof data === 'object' && Array.isArray(data.nodes) && Array.isArray(data.edges)) {
            setActualWorkflowData(data);
          } else {
            toast.error('Unable to get valid workflow data');
          }
        } catch (error) {
          toast.error('Error getting workflow data');
        }
      }
    };

    fetchWorkflowData();
  }, [isOpen]);

  const handleSave = async () => {
    if (!templateName.trim()) {
      toast.error('Please enter a template name');
      return;
    }

    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('No authenticated user found');
        return;
      }

      // Get user's profile to get org_id
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('org_id, name')
        .eq('user_id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        toast.error('Error fetching user profile');
        return;
      }

      // Get the latest workflow data directly from the window function
      let dataToSave = null;

      if (typeof window !== 'undefined' && window.saveWorkflow) {
        try {
          // Since saveWorkflow returns a Promise, we need to await it
          dataToSave = await window.saveWorkflow(true);
        } catch (error) {
          // Silently handle error and fall back to previously captured data
        }
      }

      // Fall back to the data we captured when the modal opened
      if (!dataToSave) {
        dataToSave = actualWorkflowData || workflowData;
      }

      // Final validation
      if (!dataToSave || !Array.isArray(dataToSave.nodes) || !Array.isArray(dataToSave.edges)) {
        toast.error('No valid workflow data to save');
        return;
      }

      // Save the workflow as a template
      const { data, error } = await supabase
        .from('templates')
        .insert([
          {
            title: templateName,
            type: 'Workflow',
            content: 'Workflow Template', // Simple text description
            workflow_data: dataToSave, // Store the actual workflow data as JSONB
            created_by: profile?.name || user.email,
            user_id: user.id,
            org_id: profile?.org_id,
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Error saving workflow template:', error);
        toast.error('Error saving workflow template');
        return;
      }

      toast.success('Workflow template saved successfully');
      onClose();
    } catch (error) {
      console.error('Error saving workflow template:', error);
      toast.error('Error saving workflow template');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save as Template</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="templateName">Template Name</Label>
            <Input
              id="templateName"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="Enter template name"
              autoFocus
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!templateName.trim() || isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Template'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
