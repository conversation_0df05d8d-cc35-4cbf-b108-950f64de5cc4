'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';

interface Household {
  id: number;
  householdName: string;
  members: string;
}

interface WorkflowTemplate {
  id: string;
  title: string;
  content: string;
  workflow_data: any;
}

interface CreateWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  households: Household[];
}

export default function CreateWorkflowModal({ isOpen, onClose, households }: CreateWorkflowModalProps) {
  const [title, setTitle] = useState('');
  const [selectedHousehold, setSelectedHousehold] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('blank');
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  // Fetch workflow templates when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchWorkflowTemplates();
    }
  }, [isOpen]);

  const fetchWorkflowTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('templates')
        .select('*') // Select all fields to ensure we get everything
        .eq('type', 'Workflow')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching workflow templates:', error);
        return;
      }

      setTemplates(data || []);
    } catch (error) {
      console.error('Error fetching workflow templates:', error);
    }
  };

  const handleCreate = async () => {
    if (!title || !selectedHousehold) return;

    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('No authenticated user found');
        return;
      }

      // Get template workflow data if a template is selected
      let templateContent = null;
      if (selectedTemplate !== 'blank') {
        const template = templates.find(t => t.id === selectedTemplate);
        if (template) {
          console.log('Selected template:', template);

          // Make sure we're using the workflow_data field
          if (template.workflow_data && typeof template.workflow_data === 'object') {
            templateContent = template.workflow_data;

            // Validate that the template has nodes and edges
            if (!Array.isArray(templateContent.nodes) || !Array.isArray(templateContent.edges)) {
              toast.error('Selected template has invalid data structure. Creating a blank workflow instead.');
              templateContent = null;
            }
          } else {
            toast.warning('Selected template has no workflow data. Creating a blank workflow instead.');
          }
        }
      }

      // Ensure we have a valid flow_data structure even if template is blank or missing data
      // This is critical - we need a valid structure with nodes and edges arrays
      const flow_data = templateContent && typeof templateContent === 'object' &&
                        Array.isArray(templateContent.nodes) && Array.isArray(templateContent.edges)
        ? templateContent
        : { nodes: [], edges: [] };

      console.log('Final flow_data to save:', JSON.stringify(flow_data));

      const { data, error } = await supabase
        .from('workflows')
        .insert([
          {
            title,
            household_id: parseInt(selectedHousehold),
            user_id: user.id,
            flow_data: flow_data,
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Error creating workflow:', error);
        toast.error('Error creating workflow');
        return;
      }

      toast.success('Workflow created successfully');
      router.push(`/protected/workflows/${data.id}`);
      onClose();
    } catch (error) {
      console.error('Error creating workflow:', error);
      toast.error('Error creating workflow');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Workflow</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="household">Household</label>
            <Select
              value={selectedHousehold}
              onValueChange={setSelectedHousehold}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select household" />
              </SelectTrigger>
              <SelectContent>
                {households.map((household) => (
                  <SelectItem
                    key={household.id}
                    value={household.id.toString()}
                  >
                    {household.householdName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <label htmlFor="title">Workflow Title</label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter workflow title"
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="template">Template</label>
            <Select
              value={selectedTemplate}
              onValueChange={setSelectedTemplate}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="blank">Blank Canvas</SelectItem>
                {templates.map((template) => (
                  <SelectItem
                    key={template.id}
                    value={template.id}
                  >
                    {template.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreate}
            disabled={!title || !selectedHousehold || isLoading}
          >
            Create
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}