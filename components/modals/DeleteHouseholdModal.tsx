import { Dialog, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface DeleteHouseholdModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  householdName: string;
}

export default function DeleteHouseholdModal({ isOpen, onClose, onConfirm, householdName }: DeleteHouseholdModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Deletion</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          Are you sure you want to delete {householdName}?
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button variant="destructive" onClick={onConfirm}>Delete</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}