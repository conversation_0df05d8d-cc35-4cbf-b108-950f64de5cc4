'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from '../ui/textarea';
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface Recommendation {
  id?: string;
  title: string;
  type: string;
  details?: string;
  created_date?: string;
  implementation_date?: string;
  status: string;
  member?: string;
  priority?: string;
  financial_impact?: number;
  adviser_notes?: string;
}

interface RecommendationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  householdId: string;
  recommendation?: Recommendation;
  householdMembers?: { name1: string; name2?: string };
}

export function RecommendationsModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  householdId, 
  recommendation, 
  householdMembers 
}: RecommendationsModalProps) {
  const [title, setTitle] = useState('');
  const [type, setType] = useState('');
  const [details, setDetails] = useState('');
  const [implementationDate, setImplementationDate] = useState<Date | undefined>(undefined);
  const [status, setStatus] = useState('Not Started');
  const [member, setMember] = useState('');
  const [financialImpact, setFinancialImpact] = useState<string>('');
  const [priority, setPriority] = useState('');
  const [adviserNotes, setAdviserNotes] = useState('');
  const [loading, setLoading] = useState(false);

  // Load recommendation data if editing an existing recommendation
  useEffect(() => {
    if (recommendation) {
      setTitle(recommendation.title || '');
      setType(recommendation.type || '');
      setDetails(recommendation.details || '');
      setStatus(recommendation.status || 'Not Started');
      setMember(recommendation.member || '');
      setPriority(recommendation.priority || '');
      setAdviserNotes(recommendation.adviser_notes || '');
      
      if (recommendation.financial_impact !== undefined) {
        setFinancialImpact(recommendation.financial_impact.toString());
      }
      
      if (recommendation.implementation_date) {
        setImplementationDate(new Date(recommendation.implementation_date));
      }
    }
  }, [recommendation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const supabase = createClient();
    
    // Get user and org info for RLS
    const { data: { user } } = await supabase.auth.getUser();
    const { data: profileData } = await supabase
      .from('profiles')
      .select('org_id')
      .eq('user_id', user?.id)
      .single();
    
    const recommendationData = {
      title,
      type,
      details,
      created_date: recommendation?.created_date || new Date().toISOString(),
      implementation_date: implementationDate ? format(implementationDate, 'yyyy-MM-dd') : null,
      status,
      member,
      financial_impact: financialImpact ? parseFloat(financialImpact) : null,
      priority,
      adviser_notes: adviserNotes,
      household_id: householdId,
      user_id: user?.id,
      org_id: profileData?.org_id
    };

    let error;

    if (recommendation?.id) {
      // Update existing recommendation
      const { error: updateError } = await supabase
        .from('recommendations')
        .update(recommendationData)
        .eq('id', recommendation.id);
      error = updateError;
    } else {
      // Insert new recommendation
      const { error: insertError } = await supabase
        .from('recommendations')
        .insert([recommendationData]);
      error = insertError;
    }

    setLoading(false);

    if (error) {
      console.error('Error saving recommendation:', error);
    } else {
      onSuccess();
      handleClose();
    }
  };

  const handleClose = () => {
    setTitle('');
    setType('');
    setDetails('');
    setImplementationDate(undefined);
    setStatus('Not Started');
    setMember('');
    setFinancialImpact('');
    setPriority('');
    setAdviserNotes('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{recommendation?.id ? 'Edit Recommendation' : 'Add Recommendation'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Recommendation Title*</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter recommendation title"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="type">Recommendation Type*</Label>
            <Select value={type} onValueChange={setType} required>
              <SelectTrigger>
                <SelectValue placeholder="Select recommendation type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Investment">Investment</SelectItem>
                <SelectItem value="Insurance">Insurance</SelectItem>
                <SelectItem value="Retirement">Retirement</SelectItem>
                <SelectItem value="Estate Planning">Estate Planning</SelectItem>
                <SelectItem value="Tax Strategy">Tax Strategy</SelectItem>
                <SelectItem value="Debt Management">Debt Management</SelectItem>
                <SelectItem value="Cash Flow">Cash Flow</SelectItem>
                <SelectItem value="Portfolio Adjustment">Portfolio Adjustment</SelectItem>
                <SelectItem value="Risk Profile">Risk Profile</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="member">For Member</Label>
            <Select value={member} onValueChange={setMember}>
              <SelectTrigger>
                <SelectValue placeholder="Select household member" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Household">Entire Household</SelectItem>
                {householdMembers?.name1 && (
                  <SelectItem value={householdMembers.name1}>{householdMembers.name1}</SelectItem>
                )}
                {householdMembers?.name2 && (
                  <SelectItem value={householdMembers.name2}>{householdMembers.name2}</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="details">Details*</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              placeholder="Enter recommendation details"
              rows={3}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="implementationDate">Implementation Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !implementationDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {implementationDate ? format(implementationDate, "PPP") : "Select date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={implementationDate}
                  onSelect={setImplementationDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">Status*</Label>
              <Select value={status} onValueChange={setStatus} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Not Started">Not Started</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Implemented">Implemented</SelectItem>
                  <SelectItem value="Pending Client Approval">Pending Client Approval</SelectItem>
                  <SelectItem value="Declined">Declined</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="financialImpact">Financial Impact ($)</Label>
            <Input
              id="financialImpact"
              type="number"
              value={financialImpact}
              onChange={(e) => setFinancialImpact(e.target.value)}
              placeholder="Enter financial impact amount"
              step="0.01"
            />
          </div>
          
          <div>
            <Label htmlFor="adviserNotes">Adviser Notes (Internal)</Label>
            <Textarea
              id="adviserNotes"
              value={adviserNotes}
              onChange={(e) => setAdviserNotes(e.target.value)}
              placeholder="Enter internal notes (not visible to client)"
              rows={2}
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : (recommendation?.id ? 'Update Recommendation' : 'Add Recommendation')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}