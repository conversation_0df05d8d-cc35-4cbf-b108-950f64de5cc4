'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface TaskOutcome {
  id: string;
  label: string;
  color: string;
}

interface TaskOutcomesModalProps {
  isOpen: boolean;
  onClose: () => void;
  outcomes: TaskOutcome[];
  onSave: (outcomes: TaskOutcome[]) => void;
  nodeId: string;
}

// Color options for outcomes
const COLOR_OPTIONS = [
  { value: 'green', label: 'Green', class: 'bg-green-500' },
  { value: 'blue', label: 'Blue', class: 'bg-blue-500' },
  { value: 'red', label: 'Red', class: 'bg-red-500' },
  { value: 'yellow', label: 'Yellow', class: 'bg-yellow-500' },
  { value: 'purple', label: 'Purple', class: 'bg-purple-500' },
  { value: 'gray', label: 'Gray', class: 'bg-gray-500' },
];

export default function TaskOutcomesModal({
  isOpen,
  onClose,
  outcomes = [],
  onSave,
  nodeId
}: TaskOutcomesModalProps) {
  const [editedOutcomes, setEditedOutcomes] = useState<TaskOutcome[]>([]);
  const [newOutcomeLabel, setNewOutcomeLabel] = useState('');
  const [newOutcomeColor, setNewOutcomeColor] = useState('green');

  // Initialize edited outcomes when the modal opens
  useEffect(() => {
    if (isOpen) {
      setEditedOutcomes(outcomes.length > 0 ? [...outcomes] : [
        { id: 'pending', label: 'Pending', color: 'gray' },
        { id: 'completed', label: 'Completed', color: 'green' }
      ]);
      setNewOutcomeLabel('');
      setNewOutcomeColor('green');
    }
  }, [isOpen, outcomes]);

  const handleAddOutcome = () => {
    if (newOutcomeLabel.trim() === '') return;

    const newId = newOutcomeLabel.toLowerCase().replace(/\s+/g, '_');

    // Check if an outcome with this ID already exists
    if (editedOutcomes.some(outcome => outcome.id === newId)) {
      // Maybe show an error message here
      return;
    }

    setEditedOutcomes([
      ...editedOutcomes,
      { id: newId, label: newOutcomeLabel, color: newOutcomeColor }
    ]);

    setNewOutcomeLabel('');
  };

  const handleRemoveOutcome = (id: string) => {
    setEditedOutcomes(editedOutcomes.filter(outcome => outcome.id !== id));
  };

  const handleSave = () => {
    // Ensure we always have at least the pending outcome
    if (!editedOutcomes.some(outcome => outcome.id === 'pending')) {
      const updatedOutcomes = [
        { id: 'pending', label: 'Pending', color: 'gray' },
        ...editedOutcomes
      ];
      onSave(updatedOutcomes);
    } else {
      onSave(editedOutcomes);
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Task Outcomes</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-4">
              {editedOutcomes.map((outcome) => (
                <div key={outcome.id} className="flex items-center space-x-2 p-2 border rounded-md">
                  <div className={`w-4 h-4 rounded-full ${COLOR_OPTIONS.find(c => c.value === outcome.color)?.class || 'bg-gray-500'}`} />
                  <div className="flex-1">
                    <Input
                      value={outcome.label}
                      onChange={(e) => {
                        const updatedOutcomes = editedOutcomes.map(o =>
                          o.id === outcome.id ? { ...o, label: e.target.value } : o
                        );
                        setEditedOutcomes(updatedOutcomes);
                      }}
                      className="h-8"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <select
                      value={outcome.color}
                      onChange={(e) => {
                        const updatedOutcomes = editedOutcomes.map(o =>
                          o.id === outcome.id ? { ...o, color: e.target.value } : o
                        );
                        setEditedOutcomes(updatedOutcomes);
                      }}
                      className="h-8 rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      {COLOR_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveOutcome(outcome.id)}
                      disabled={outcome.id === 'pending'} // Don't allow removing the pending state
                      className="h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          <div className="mt-4 border-t pt-4">
            <Label className="text-sm font-medium">Add New Outcome</Label>
            <div className="flex items-center space-x-2 mt-2">
              <Input
                placeholder="Outcome name"
                value={newOutcomeLabel}
                onChange={(e) => setNewOutcomeLabel(e.target.value)}
                className="flex-1 h-8"
              />
              <select
                value={newOutcomeColor}
                onChange={(e) => setNewOutcomeColor(e.target.value)}
                className="h-8 rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                {COLOR_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddOutcome}
                disabled={newOutcomeLabel.trim() === ''}
                className="h-8"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
