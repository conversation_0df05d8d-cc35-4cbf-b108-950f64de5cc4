'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createClient } from '@/utils/supabase/client';
import { Textarea } from '../ui/textarea';
import { format } from 'date-fns';
import { Insurance } from '../tabs/InsuranceTable';

interface InsuranceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  householdId: string;
  insurance?: Insurance;
  householdMembers?: { name1: string; name2?: string };
}

export function InsuranceModal({ isOpen, onClose, onSuccess, householdId, insurance, householdMembers }: InsuranceModalProps) {
  const [type, setType] = useState('');
  const [provider, setProvider] = useState('');
  const [policyNumber, setPolicyNumber] = useState('');
  const [premium, setPremium] = useState('');
  const [frequency, setFrequency] = useState('');
  const [coverageAmount, setCoverageAmount] = useState('');
  const [renewalDate, setRenewalDate] = useState('');
  const [details, setDetails] = useState('');
  const [personInsured, setPersonInsured] = useState('');
  const [policyOwner, setPolicyOwner] = useState('');
  const [loading, setLoading] = useState(false);

  // Load insurance data if editing an existing insurance policy
  useEffect(() => {
    if (insurance) {
      setType(insurance.type || '');
      setProvider(insurance.provider || '');
      setPolicyNumber(insurance.policy_number || '');
      setPremium(insurance.premium?.toString() || '');
      setFrequency(insurance.frequency || '');
      setCoverageAmount(insurance.coverage_amount?.toString() || '');
      setRenewalDate(insurance.renewal_date || '');
      setDetails(insurance.details || '');
      setPersonInsured(insurance.person_insured || '');
      setPolicyOwner(insurance.policy_owner || '');
    }
  }, [insurance]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const supabase = createClient();
    const insuranceData = {
      type,
      provider,
      policy_number: policyNumber,
      premium: parseFloat(premium) || 0,
      frequency,
      coverage_amount: parseFloat(coverageAmount) || 0,
      renewal_date: renewalDate,
      details,
      household_id: householdId,
      person_insured: personInsured,
      policy_owner: policyOwner,
    };

    let error;

    if (insurance?.id) {
      // Update existing insurance
      const { error: updateError } = await supabase
        .from('insurances')
        .update(insuranceData)
        .eq('id', insurance.id);
      error = updateError;
    } else {
      // Insert new insurance
      const { error: insertError } = await supabase
        .from('insurances')
        .insert([insuranceData]);
      error = insertError;
    }

    setLoading(false);

    if (error) {
      console.error('Error saving insurance:', error);
    } else {
      onSuccess();
      handleClose();
    }
  };

  const handleClose = () => {
    setType('');
    setProvider('');
    setPolicyNumber('');
    setPremium('');
    setFrequency('');
    setCoverageAmount('');
    setRenewalDate('');
    setDetails('');
    setPersonInsured('');
    setPolicyOwner('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{insurance?.id ? 'Edit Insurance' : 'Add Insurance'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="type">Insurance Type</Label>
            <Select
              value={type}
              onValueChange={setType}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select insurance type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="life">Life</SelectItem>
                <SelectItem value="health">Health</SelectItem>
                <SelectItem value="income_protection">Income Protection</SelectItem>
                <SelectItem value="tpd">TPD</SelectItem>
                <SelectItem value="trauma">Trauma</SelectItem>
                <SelectItem value="home">Home</SelectItem>
                <SelectItem value="contents">Contents</SelectItem>
                <SelectItem value="vehicle">Vehicle</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="provider">Provider</Label>
            <Input
              id="provider"
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
              placeholder="Enter insurance provider"
              required
            />
          </div>
          <div>
            <Label htmlFor="policyNumber">Policy Number</Label>
            <Input
              id="policyNumber"
              value={policyNumber}
              onChange={(e) => setPolicyNumber(e.target.value)}
              placeholder="Enter policy number"
            />
          </div>
          <div>
            <Label htmlFor="premium">Premium Amount</Label>
            <Input
              id="premium"
              type="number"
              step="0.01"
              min="0"
              value={premium}
              onChange={(e) => setPremium(e.target.value)}
              placeholder="Enter premium amount"
              required
            />
          </div>
          <div>
            <Label htmlFor="frequency">Payment Frequency</Label>
            <Select
              value={frequency}
              onValueChange={setFrequency}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="fortnightly">Fortnightly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="annually">Annually</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="coverageAmount">Coverage Amount</Label>
            <Input
              id="coverageAmount"
              type="number"
              step="0.01"
              min="0"
              value={coverageAmount}
              onChange={(e) => setCoverageAmount(e.target.value)}
              placeholder="Enter coverage amount"
            />
          </div>
          <div>
            <Label htmlFor="renewalDate">Renewal Date</Label>
            <Input
              id="renewalDate"
              type="date"
              value={renewalDate}
              onChange={(e) => setRenewalDate(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="personInsured">Person Insured</Label>
            <Select
              value={personInsured}
              onValueChange={setPersonInsured}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select person insured" />
              </SelectTrigger>
              <SelectContent>
                {householdMembers?.name1 && (
                  <SelectItem value={householdMembers.name1}>{householdMembers.name1}</SelectItem>
                )}
                {householdMembers?.name2 && (
                  <SelectItem value={householdMembers.name2}>{householdMembers.name2}</SelectItem>
                )}
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="policyOwner">Policy Owner</Label>
            <Select
              value={policyOwner}
              onValueChange={setPolicyOwner}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select policy owner" />
              </SelectTrigger>
              <SelectContent>
                {householdMembers?.name1 && (
                  <SelectItem value={householdMembers.name1}>{householdMembers.name1}</SelectItem>
                )}
                {householdMembers?.name2 && (
                  <SelectItem value={householdMembers.name2}>{householdMembers.name2}</SelectItem>
                )}
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="details">Details</Label>
            <Textarea
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              placeholder="Add any additional details"
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : insurance?.id ? 'Save Changes' : 'Add Insurance'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
