"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/expandingDialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { createClient } from "@/utils/supabase/client";
import { CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Editor } from '@tiptap/react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { EditorContent } from '@tiptap/react';
import Toolbar from '../Toolbar';
import { Calendar } from "@/components/ui/calendar";
import Comments from "../Comments";
import { MessageSquare, Paperclip, Link, ExternalLink, Maximize2, FileText, Mic, Headphones } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import InteractionsModal from "./InteractionsModal";
import { Check, ChevronsUpDown } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { toast } from "@/hooks/use-toast";

interface Comment {
  id: number;
  task_id: number;
  content: string;
  created_at: string;
  user_id: string;
}

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task?: {
    id: number;
    title: string;
    due_date: string;
    household_id: number;
    importance: string;
    content: string;
    status: string;
    comments: Comment[];
    linked_interactions?: {
      id: number;
      interaction_id: number;
      title: string;
      date: string;
      type: string;
    }[];
    linked_notes?: {
      id: number;
      note_id: number;
      title: string;
      created_at: string;
    }[];
    assigned_to?: string;
  };
  onSave: () => Promise<void>;
  householdId?: number;
  householdName?: string;
  readonly?: boolean;
  initialDueDate?: Date;
}

interface Household {
  id: number;
  householdName: string;
}

interface User {
  id: string;
  name: string;
}

export default function TaskModal({ isOpen, onClose, task, onSave, householdId, householdName, readonly = false, initialDueDate }: TaskModalProps) {
  const [title, setTitle] = useState(task?.title || "");
  const [dueDate, setDueDate] = useState<Date | undefined>(
    task?.due_date ? new Date(task?.due_date) : initialDueDate || undefined
  );
  const [selectedHouseholdId, setSelectedHouseholdId] = useState<string>(
    householdId?.toString() || task?.household_id?.toString() || ""
  );
  const [importance, setImportance] = useState(task?.importance || "medium");
  const [status, setStatus] = useState(task?.status || "not started");
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [comments, setComments] = useState<Comment[]>([]);
  const [availableInteractions, setAvailableInteractions] = useState<any[]>([]);
  const [selectedInteractionIds, setSelectedInteractionIds] = useState<number[]>([]);
  const [linkedInteractions, setLinkedInteractions] = useState<any[]>([]);
  const [finalTaskId, setFinalTaskId] = useState<number | null>(null);
  const [isInteractionModalOpen, setIsInteractionModalOpen] = useState(false);
  const [selectedInteraction, setSelectedInteraction] = useState<any>(null);
  const [profileData, setProfileData] = useState<{ user_id: string | null, org_id: string | null }>({
    user_id: null,
    org_id: null
  });
  const [users, setUsers] = useState<User[]>([]);
  const [assignedTo, setAssignedTo] = useState<string | undefined>(task?.assigned_to);
  const [openUserCombobox, setOpenUserCombobox] = useState(false);
  const [commentInput, setCommentInput] = useState("");
  const [showUserSuggestions, setShowUserSuggestions] = useState(false);
  const [userSuggestions, setUserSuggestions] = useState<User[]>([]);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [taggedUsers, setTaggedUsers] = useState<{id: string, name: string}[]>([]);
  const commentInputRef = useRef<HTMLTextAreaElement>(null);
  const [viewMode, setViewMode] = useState<string | null>(null); // Add state for viewMode
  const [isFullscreen, setIsFullscreen] = useState(false); // Add state for fullscreen mode
  const [linkedNotes, setLinkedNotes] = useState<any[]>([]);

  const editor = useEditor({
    extensions: [StarterKit],
    content: task?.content || "",
    editable: !readonly,
    editorProps: {
      attributes: {
        class: 'min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      },
    },
  });

  const supabase = createClient();

  useEffect(() => {
    fetchHouseholds();
  }, []);

  useEffect(() => {
    if (task) {
      setTitle(task.title);
      setDueDate(task.due_date ? new Date(task.due_date) : undefined);
      setSelectedHouseholdId(task.household_id.toString());
      setImportance(task.importance);
      setStatus(task.status || "not started");
      editor?.commands.setContent(task.content || "");
      setAssignedTo(task.assigned_to);
    } else {
      setTitle("");
      setDueDate(initialDueDate || undefined);
      setSelectedHouseholdId(householdId?.toString() || "");
      setImportance("medium");
      setStatus("not started");
      editor?.commands.setContent("");
      setAssignedTo(undefined);
    }
  }, [task, editor, householdId, initialDueDate]);

  useEffect(() => {
    if (task?.id) {
      fetchComments();
    }
  }, [task?.id]);

  const fetchHouseholds = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from('households')
      .select('id, householdName')
      .order('householdName');

    if (error) {
      console.error('Error fetching households:', error);
    } else {
      setHouseholds(data || []);
    }
  };

  const fetchComments = async () => {
    if (!task?.id) return;

    const { data, error } = await supabase
      .from('task_comments')
      .select('*')
      .eq('task_id', task.id)
      .order('created_at', { ascending: false });

    if (!error && data) {
      setComments(data);
    }
  };

  const fetchAvailableInteractions = async () => {
    if (!householdId && !selectedHouseholdId) return;

    const { data, error } = await supabase
      .from('interactions')
      .select('id, title, date, type')
      .eq('household_id', householdId || parseInt(selectedHouseholdId))
      .order('date', { ascending: false });

    if (error) {
      console.error('Error fetching interactions:', error);
      return;
    }

    setAvailableInteractions(data || []);
  };

  const fetchLinkedInteractions = async (taskId: number) => {
    const { data, error } = await supabase
      .from('interaction_task_links')
      .select(`
        id, interaction_id,
        interactions:interaction_id (id, title, date, type)
      `)
      .eq('task_id', taskId);

    if (error) {
      console.error('Error fetching linked interactions:', error);
      return;
    }

    const linkedIds = data?.map(link => link.interaction_id) || [];
    setSelectedInteractionIds(linkedIds);
    setLinkedInteractions(data?.map(link => link.interactions) || []);
  };

  const fetchLinkedNotes = async (taskId: number) => {
    const { data, error } = await supabase
      .from('task_note_links')
      .select(`
        id, note_id,
        notes:notes(id, title, created_at)
      `)
      .eq('task_id', taskId);

    if (error) {
      console.error('Error fetching linked notes:', error);
      return;
    }

    setLinkedNotes(data?.map(link => ({
      id: link.id,
      note_id: link.note_id,
      ...link.notes
    })) || []);
  };

  useEffect(() => {
    if (task?.id) {
      fetchLinkedInteractions(task.id);
      fetchLinkedNotes(task.id);
    } else {
      setSelectedInteractionIds([]);
      setLinkedInteractions([]);
      setLinkedNotes([]);
    }

    fetchAvailableInteractions();
  }, [task, householdId, selectedHouseholdId]);

  useEffect(() => {
    const fetchUserProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();

        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    };

    fetchUserProfile();
  }, []);

  const fetchUsers = async () => {
    if (!profileData.org_id) return;

    const { data, error } = await supabase
      .from('profiles')
      .select('user_id, name')
      .eq('org_id', profileData.org_id)
      .order('name');

    if (error) {
      console.error('Error fetching users:', error);
    } else {
      setUsers(data.map(user => ({
        id: user.user_id,
        name: user.name || 'Unnamed User'
      })) || []);
    }
  };

  useEffect(() => {
    if (profileData.org_id) {
      fetchUsers();
    }
  }, [profileData]);

  // Fetch viewMode only on the client side
  useEffect(() => {
    const mode = localStorage.getItem('viewMode') || 'user';
    setViewMode(mode);
  }, []); // Empty dependency array ensures this runs once on mount

  const handleSave = async () => {
    setIsLoading(true);

    try {
      // Get the current user first
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('No authenticated user found');
        setIsLoading(false);
        return;
      }

      // Use the state variable 'viewMode' instead of accessing localStorage directly
      // const viewMode = localStorage.getItem('viewMode') || 'user'; // REMOVED THIS LINE
      let orgId = null;

      // Get profile data to check for org_id
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('org_id')
        .eq('user_id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
      }

      // Use the state variable here
      if (viewMode === 'organization' && profileData?.org_id) {
        orgId = profileData.org_id;
      }

      // Check if dueDate is defined
      if (!dueDate) {
        toast({
          title: "Error",
          description: "Please select a due date",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Get content from editor
      const content = editor ? editor.getHTML() : "";

      // Get the final household ID
      const finalHouseholdId = parseInt(selectedHouseholdId);

      if (task?.id) {
        // Update existing task
        const { error } = await supabase
          .from('tasks')
          .update({
            title,
            due_date: dueDate.toISOString(),
            household_id: finalHouseholdId,
            importance,
            content,
            status,
            updated_at: new Date().toISOString(),
            org_id: orgId,
            assigned_to: assignedTo
          })
          .eq('id', task.id);

        if (error) throw error;
      } else {
        // Create new task
        const { data, error } = await supabase
          .from('tasks')
          .insert({
            title,
            due_date: dueDate.toISOString(),
            household_id: finalHouseholdId,
            importance,
            content,
            status,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user_id: user.id,
            org_id: orgId,
            assigned_to: assignedTo
          })
          .select('id')
          .single();

        if (error) throw error;
        if (data) setFinalTaskId(data.id);
      }

      if (task?.id || finalTaskId) {
        const taskId = task?.id || finalTaskId;

        // First, remove any existing links
        if (taskId) {
          await supabase
            .from('interaction_task_links')
            .delete()
            .eq('task_id', taskId);

          // Create new links
          if (selectedInteractionIds.length > 0) {
            const links = selectedInteractionIds.map(interactionId => ({
              task_id: taskId,
              interaction_id: interactionId
            }));

            const { error: linkError } = await supabase
              .from('interaction_task_links')
              .insert(links);

            if (linkError) throw linkError;
          }
        }
      }

      await onSave();
      onClose();
    } catch (error) {
      console.error('Error saving task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!task?.id) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', task.id);

      if (error) throw error;

      await onSave();
      onClose();
    } catch (error) {
      console.error('Error deleting task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCommentInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCommentInput(value);

    // Get cursor position
    const cursorPos = e.target.selectionStart || 0;
    setCursorPosition(cursorPos);

    // Check if we need to show user suggestions
    const textBeforeCursor = value.substring(0, cursorPos);
    const atSymbolIndex = textBeforeCursor.lastIndexOf('@');

    if (atSymbolIndex !== -1 && !textBeforeCursor.substring(atSymbolIndex + 1).includes(' ')) {
      const searchTerm = textBeforeCursor.substring(atSymbolIndex + 1);
      // Filter users based on search term
      const filteredUsers = users.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setUserSuggestions(filteredUsers);
      setShowUserSuggestions(true);
    } else {
      setShowUserSuggestions(false);
    }
  };

  const handleSelectUser = (user: User) => {
    const textBeforeCursor = commentInput.substring(0, cursorPosition);
    const atSymbolIndex = textBeforeCursor.lastIndexOf('@');

    if (atSymbolIndex !== -1) {
      // Replace @searchTerm with the selected user's name
      const textBefore = commentInput.substring(0, atSymbolIndex);
      const textAfter = commentInput.substring(cursorPosition);
      const newText = `${textBefore}@${user.name} ${textAfter}`;

      setCommentInput(newText);
      setShowUserSuggestions(false);

      // Add user to tagged users if not already added
      if (!taggedUsers.some(taggedUser => taggedUser.id === user.id)) {
        setTaggedUsers([...taggedUsers, { id: user.id, name: user.name }]);
      }

      // Set focus back to input and place cursor after the inserted name
      setTimeout(() => {
        if (commentInputRef.current) {
          commentInputRef.current.focus();
          const newCursorPos = atSymbolIndex + user.name.length + 2; // +2 for @ and space
          commentInputRef.current.setSelectionRange(newCursorPos, newCursorPos);
          setCursorPosition(newCursorPos);
        }
      }, 0);
    }
  };

  const handleAddComment = async () => {
    if (!commentInput.trim() || !task?.id) return;

    try {
      // Format the comment content to include user tags
      let formattedContent = commentInput;

      // Insert the comment
      const { data: commentData, error } = await supabase
        .from('task_comments')
        .insert({
          task_id: task.id,
          content: formattedContent,
          user_id: (await supabase.auth.getUser()).data.user?.id,
          tagged_users: taggedUsers.length > 0 ? taggedUsers.map(user => user.id) : null
        })
        .select('id')
        .single();

      if (error) throw error;

      // Send notifications to tagged users
      for (const taggedUser of taggedUsers) {
        // Skip if the tagged user is the current user
        if (taggedUser.id === (await supabase.auth.getUser()).data.user?.id) continue;

        await supabase.from('notifications').insert({
          user_id: taggedUser.id,
          content: `You were mentioned in a comment on task: ${task.title}`,
          type: 'task_mention',
          link: `/protected/tasks?view=${task.id}`,
          created_at: new Date().toISOString()
        });
      }

      toast({
        title: "Success",
        description: "Comment added successfully",
      });

      setCommentInput('');
      setTaggedUsers([]);
      fetchComments();
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "Failed to add comment",
        variant: "destructive",
      });
    }
  };

  const statusOptions = [
    { value: "not started", label: "Not Started" },
    { value: "in progress", label: "In Progress" },
    { value: "with client", label: "With Client" },
    { value: "with third party", label: "With Third Party" },
    { value: "complete", label: "Complete" },
    { value: "cancelled", label: "Cancelled" }
  ];

  const handleViewInteraction = (interaction: any) => {
    // Get the interaction ID - either directly or from the interaction_id property
    const interactionId = interaction.id || interaction.interaction_id;

    if (!interactionId) {
      console.error('No interaction ID found:', interaction);
      return;
    }

    // Determine the household ID to use
    const householdIdToUse = householdId || selectedHouseholdId;

    if (!householdIdToUse) {
      console.error('No household ID available for navigation');
      return;
    }

    // Create the correct URL for the interaction based on your app's routing structure
    const url = `/protected/households/household/${householdIdToUse}/interactions?view=${interactionId}`;

    // Open in a new tab
    window.open(url, '_blank');

    console.log(`Opening interaction ${interactionId} in new tab: ${url}`);
  };

  const handleViewNote = async (note: any) => {
    const noteId = note.id || note.note_id;

    if (!noteId) {
      console.error('No note ID found:', note);
      return;
    }

    try {
      // Check if note_type is already available in the note object
      if (note.note_type) {
        // Use the note_type from the note object
        const url = note.note_type === 'transcription'
          ? `/protected/notes/transcription/${noteId}`
          : `/protected/notes/note/${noteId}`;

        window.open(url, '_blank');
        console.log(`Opening ${note.note_type} ${noteId} in new tab: ${url}`);
        return;
      }

      // If note_type is not available, fetch it from the database
      const { data: noteData, error } = await supabase
        .from('notes')
        .select('note_type')
        .eq('id', noteId)
        .single();

      if (error) {
        console.error('Error fetching note type:', error);
        throw error;
      }

      // Determine the correct URL based on note type
      const url = noteData?.note_type === 'transcription'
        ? `/protected/notes/transcription/${noteId}`
        : `/protected/notes/note/${noteId}`;

      window.open(url, '_blank');

      console.log(`Opening ${noteData?.note_type || 'note'} ${noteId} in new tab: ${url}`);
    } catch (error) {
      console.error('Error handling note view:', error);
      // Fallback to the default note URL if there's an error
      const url = `/protected/notes/note/${noteId}`;
      window.open(url, '_blank');
    }
  };

  // Update the fetchTaskWithLinks function to include notes
  const fetchTaskWithLinks = async (taskId: number) => {
    console.log("Fetching task with links for taskId:", taskId);

    // First fetch the task with its basic data
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select(`
        *,
        comments:task_comments(*)
      `)
      .eq('id', taskId)
      .single();

    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }

    console.log("Task data fetched:", taskData);

    // Then fetch the linked interactions
    const { data: linkData, error: linkError } = await supabase
      .from('interaction_task_links')
      .select(`
        id,
        interaction_id,
        interactions:interaction_id (
          id,
          title,
          date,
          type
        )
      `)
      .eq('task_id', taskId);

    if (linkError) {
      console.error('Error fetching linked interactions:', linkError);
      return;
    }

    // Fetch linked notes
    const { data: noteData, error: noteError } = await supabase
      .from('task_note_links')
      .select(`
        id,
        note_id,
        notes:note_id (
          id,
          title,
          created_at,
          note_type
        )
      `)
      .eq('task_id', taskId);

    if (noteError) {
      console.error('Error fetching linked notes:', noteError);
      return;
    }

    // Transform the data to match the expected format
    const linkedInteractions = linkData?.map(link => ({
      id: link.id,
      interaction_id: link.interaction_id,
      ...link.interactions
    })) || [];

    const linkedNotes = noteData?.map(link => ({
      id: link.id,
      note_id: link.note_id,
      ...link.notes
    })) || [];

    // Update the task object with linked interactions and notes
    const updatedTask = {
      ...taskData,
      linked_interactions: linkedInteractions,
      linked_notes: linkedNotes
    };

    // Update the component state with the fetched data
    setLinkedInteractions(linkedInteractions);
    setLinkedNotes(linkedNotes);

    return updatedTask;
  };

  // Update the useEffect to properly update the component state
  useEffect(() => {
    if (task?.id) {
      console.log("Task ID changed, fetching linked interactions:", task.id);
      fetchTaskWithLinks(task.id).then(updatedTask => {
        // If you have a setTask function, uncomment this:
        // setTask(updatedTask);
        console.log("Task updated with linked interactions");
      });
    } else {
      console.log("No task ID available, clearing linked interactions");
      setLinkedInteractions([]);
      setLinkedNotes([]);
    }
  }, [task?.id]);

  const renderFormattedComment = (content: string) => {
    // Replace @username with highlighted span
    let formattedContent = content;
    users.forEach(user => {
      const regex = new RegExp(`@${user.name}\\b`, 'g');
      formattedContent = formattedContent.replace(
        regex,
        `<span class="bg-gray-200 text-gray-800 rounded px-1">@${user.name}</span>`
      );
    });

    return <div dangerouslySetInnerHTML={{ __html: formattedContent }} />;
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[80%] sm:h-[80vh] max-h-[90vh]"
        fullscreenEnabled
      >
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{task ? task.title : "Create Task"}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-[3fr_2fr] gap-6 overflow-auto">
          {/* Left Column - Task Details */}
          <div className="flex flex-col border-r pr-6 overflow-y-auto">
            <div className="grid gap-2 mb-4">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                disabled={readonly}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="grid gap-2">
                <Label>Due Date</Label>
                <Input
                  type="date"
                  value={dueDate ? format(dueDate, "yyyy-MM-dd") : ""}
                  onChange={(e) => {
                    if (e.target.value) {
                      setDueDate(new Date(e.target.value));
                    } else {
                      setDueDate(undefined);
                    }
                  }}
                  disabled={readonly}
                />
              </div>

              <div className="grid gap-2">
                <Label>Household</Label>
                {householdId ? (
                  <Input
                    value={householdName}
                    disabled
                    className="bg-muted"
                  />
                ) : (
                  <Select
                    value={selectedHouseholdId}
                    onValueChange={setSelectedHouseholdId}
                    disabled={readonly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select household" />
                    </SelectTrigger>
                    <SelectContent>
                      {households.map((household) => (
                        <SelectItem key={household.id} value={household.id.toString()}>
                          {household.householdName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div className="grid gap-2 mb-4">
              <Label>Assigned To</Label>
              <Popover open={openUserCombobox} onOpenChange={setOpenUserCombobox}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openUserCombobox}
                    className="justify-between"
                    disabled={readonly}
                  >
                    {assignedTo
                      ? users.find((user) => user.id === assignedTo)?.name || "Select user"
                      : "Select user"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                  <Command>
                    <CommandInput placeholder="Search users..." />
                    <CommandEmpty>No user found.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        key="unassigned"
                        value="unassigned"
                        onSelect={() => {
                          setAssignedTo(undefined);
                          setOpenUserCombobox(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            !assignedTo ? "opacity-100" : "opacity-0"
                          )}
                        />
                        Unassigned
                      </CommandItem>
                      {users.map((user) => (
                        <CommandItem
                          key={user.id}
                          value={user.id}
                          onSelect={() => {
                            setAssignedTo(user.id);
                            setOpenUserCombobox(false);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              assignedTo === user.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {user.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="grid gap-2">
                <Label>Importance</Label>
                <Select
                  value={importance}
                  onValueChange={setImportance}
                  disabled={readonly}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select importance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label>Status</Label>
                <Select
                  value={status}
                  onValueChange={setStatus}
                  disabled={readonly}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex flex-col flex-grow">
              <Label>Task Details</Label>
              {!readonly && (
                <>
                  <Toolbar editor={editor} readonly={readonly} />
                  <div className="flex-grow mt-2 overflow-y-auto">
                    <EditorContent editor={editor} className="h-full" />
                  </div>
                </>
              )}
              {readonly && task?.content && (
                <div className="flex-grow mt-2 overflow-y-auto">
                  <div className="text-sm" dangerouslySetInnerHTML={{ __html: task.content }} />
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Links and Comments */}
          <div className="flex flex-col pl-2 overflow-hidden">
            {/* Links Section */}
            <div className="mb-4">
              <Label className="text-lg font-semibold mb-2">Linked Resources</Label>
              <ScrollArea className="h-[150px] mb-4">
                {(linkedInteractions && linkedInteractions.length > 0) || (linkedNotes && linkedNotes.length > 0) ? (
                  <div className="grid gap-2">
                    {linkedNotes && linkedNotes.map((note) => (
                      <div key={`note-${note.id}`} className="flex justify-between items-center p-2 bg-secondary rounded-md">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="secondary"
                            className="capitalize text-xs"
                          >
                            {note.note_type === 'transcription' ?
                              <Headphones className="h-3 w-3 mr-1" /> :
                              <FileText className="h-3 w-3 mr-1" />
                            }
                            {note.note_type === 'transcription' ? 'Transcription' : 'Note'}
                          </Badge>
                          <span className="text-sm truncate">{note.title}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(note.created_at), 'dd MMM yyyy')}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleViewNote(note)}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    {linkedInteractions && linkedInteractions.map((interaction) => (
                      <div key={`interaction-${interaction.id}`} className="flex justify-between items-center p-2 bg-secondary rounded-md">
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            interaction.type === 'email' ? 'default' :
                            interaction.type === 'phone' ? 'outline' :
                            interaction.type === 'meeting' ? 'secondary' : 'default'
                          } className="capitalize text-xs">
                            {interaction.type}
                          </Badge>
                          <span className="text-sm truncate">{interaction.title}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(interaction.date), 'dd MMM yyyy')}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleViewInteraction(interaction)}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No linked resources</p>
                )}
              </ScrollArea>
            </div>

            {/* Clear horizontal border */}
            <div className="border-t my-4"></div>

            {/* Comments Section */}
            <div className="flex flex-col flex-grow overflow-hidden">
              <Label className="text-lg font-semibold mb-2">Comments</Label>

              {/* Comments list */}
              <ScrollArea className="flex-grow mb-4">
                {comments && comments.map((comment) => (
                  <div key={comment.id} className="bg-muted p-3 rounded-md mb-3">
                    <div className="text-sm">{renderFormattedComment(comment.content)}</div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(comment.created_at), 'dd/MM/yyyy, HH:mm:ss')}
                      </span>
                      <span className="text-xs font-medium">
                        {users.find(user => user.id === comment.user_id)?.name || 'Unknown User'}
                      </span>
                    </div>
                  </div>
                ))}
                {(!comments || comments.length === 0) && (
                  <p className="text-sm text-muted-foreground text-center">No comments yet</p>
                )}
              </ScrollArea>

              {/* Comment input */}
              <div className="relative mt-2">
                <div className="flex items-center space-x-2">
                  <div className="flex-grow relative">
                    <textarea
                      ref={commentInputRef}
                      value={commentInput}
                      onChange={handleCommentInputChange}
                      placeholder="Add a comment... Use @ to mention team members"
                      className="min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.ctrlKey) {
                          e.preventDefault();
                          handleAddComment();
                        }
                      }}
                    />

                    {showUserSuggestions && userSuggestions.length > 0 && (
                      <div className="absolute z-10 bottom-full mb-1 w-full max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        {userSuggestions.map((user) => (
                          <div
                            key={user.id}
                            className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"
                            onClick={() => handleSelectUser(user)}
                          >
                            <div className="flex items-center">
                              <span className="font-medium block truncate">{user.name}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Button
                    onClick={handleAddComment}
                    disabled={!commentInput.trim()}
                  >
                    Add Comment
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-6 pt-4 border-t">
          {task && (
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              Delete
            </Button>
          )}
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            {!readonly && (
              <Button onClick={handleSave} disabled={isLoading}>
                {isLoading ? "Saving..." : "Save"}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  {isInteractionModalOpen && selectedInteraction && (
    <InteractionsModal
      isOpen={isInteractionModalOpen}
      onClose={() => setIsInteractionModalOpen(false)}
      householdId={parseInt(selectedHouseholdId)}
      interactionToEdit={selectedInteraction}
      onInteractionSaved={() => {}}
      onInteractionDeleted={() => {}}
      mode="details"
    />
  )}
}
