import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export interface NodeType {
  id: string;
  label: string;
  type: string;
}

interface AddNodeModalProps {
  open: boolean;
  onClose: () => void;
  availableNodes: NodeType[];
  onAddNode: (nodeType: string) => void;
}

const AddNodeModal: React.FC<AddNodeModalProps> = ({
  open,
  onClose,
  availableNodes,
  onAddNode,
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Node</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {availableNodes.map((node) => (
            <Button
              key={node.id}
              onClick={() => {
                onAddNode(node.type);
                onClose();
              }}
              className="w-full justify-start"
              variant="outline"
            >
              {node.label}
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddNodeModal;