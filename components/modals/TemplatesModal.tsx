'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { createClient } from '@/utils/supabase/client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

interface TemplatesModalProps {
  isOpen: boolean;
  onClose: () => void;
  templateToEdit?: Template | null;
  onSuccess: () => Promise<void>;
}

interface Template {
  id?: string;
  title: string;
  type: string;
  content: string;
  use_default?: boolean;
  created_at?: string;
  last_edited_at?: string;
  created_by?: string;
  user_id?: string;
  org_id?: string;
}

export default function TemplatesModal({
  isO<PERSON>,
  onClose,
  templateToEdit,
  onSuccess
}: TemplatesModalProps) {
  const [title, setTitle] = useState('');
  const [type, setType] = useState('');
  const [useDefault, setUseDefault] = useState(false);
  const [riskProfilerType, setRiskProfilerType] = useState<'10q' | '25q'>('10q');
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userId, setUserId] = useState<string | null>(null);
  const [orgId, setOrgId] = useState<string | null>(null);
  const [userName, setUserName] = useState<string | null>(null);

  // Template types
  const templateTypes = [
    'Discovery Document',
    'Terms of Engagement',
    'Risk Profiler',
    'Third Party Authority',
    'Workflow',
    'Email'
  ];

  // Check form validity
  useEffect(() => {
    setIsFormValid(
      title.trim() !== '' &&
      type !== ''
    );
  }, [title, type]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        setUserId(user.id);

        // Get user profile data
        const { data: profileData } = await supabase
          .from('profiles')
          .select('name, org_id')
          .eq('user_id', user.id)
          .single();

        if (profileData) {
          setUserName(profileData.name);
          setOrgId(profileData.org_id);
        }
      }
    };

    fetchUserData();
  }, []);

  // Set form data if editing an existing template
  useEffect(() => {
    if (templateToEdit) {
      setTitle(templateToEdit.title);
      setType(templateToEdit.type);
      setUseDefault(templateToEdit.use_default || false);
    } else {
      // Reset form for new template
      setTitle('');
      setType('');
      setUseDefault(false);
    }
  }, [templateToEdit]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // For specialized editors, redirect to the appropriate page
    if (type === 'Discovery Document') {
      // Pass the useDefault parameter in the URL
      window.location.href = `/protected/admin/templates/discovery-document?useDefault=${useDefault}`;
      return;
    } else if (type === 'Risk Profiler') {
      // Pass the useDefault parameter and risk profiler type in the URL
      window.location.href = `/protected/admin/templates/risk-profiler?useDefault=${useDefault}&profilerType=${riskProfilerType}`;
      return;
    } else if (type === 'Terms of Engagement') {
      // Redirect to the TOE template editor
      window.location.href = `/protected/admin/templates/toe`;
      return;
    }

    try {
      const supabase = createClient();

      // Generate default content based on template type if useDefault is true
      let content = '';
      if (useDefault) {
        // This would be replaced with actual default content logic
        content = `Default content for ${type}`;
      }

      const templateData: Template = {
        title,
        type,
        content,
        use_default: useDefault,
        user_id: userId || '',
        org_id: orgId || '',
      };

      if (templateToEdit?.id) {
        // Update existing template
        const { error } = await supabase
          .from('templates')
          .update({
            ...templateData,
            last_edited_at: new Date().toISOString(),
          })
          .eq('id', templateToEdit.id);

        if (error) throw error;
      } else {
        // Create new template
        const { error } = await supabase
          .from('templates')
          .insert({
            ...templateData,
            created_by: userName || 'Unknown',
            created_at: new Date().toISOString(),
            last_edited_at: new Date().toISOString(),
          });

        if (error) throw error;
      }

      await onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error saving template:', err);
      setError(err.message || 'An error occurred while saving the template');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {templateToEdit ? 'Edit Template' : 'Create New Template'}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              {error && (
                <div className="bg-red-50 text-red-600 p-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right">
                  Title
                </Label>
                <div className="col-span-3">
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Template title"
                  />
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <div className="col-span-3">
                  <Select value={type} onValueChange={setType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select template type" />
                    </SelectTrigger>
                    <SelectContent>
                      {templateTypes.map((templateType) => (
                        <SelectItem key={templateType} value={templateType}>
                          {templateType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="useDefault" className="text-right">
                  Add From Default
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="useDefault"
                    checked={useDefault}
                    onCheckedChange={setUseDefault}
                  />
                  <span className="text-sm text-muted-foreground">
                    {useDefault ? 'Using default template content' : 'No default content'}
                  </span>
                </div>
              </div>

              {/* Risk Profiler Type Selection - Only show when Risk Profiler is selected */}
              {type === 'Risk Profiler' && useDefault && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="riskProfilerType" className="text-right">
                    Profiler Type
                  </Label>
                  <div className="col-span-3">
                    <Select value={riskProfilerType} onValueChange={(value) => setRiskProfilerType(value as '10q' | '25q')}>
                      <SelectTrigger id="riskProfilerType">
                        <SelectValue placeholder="Select profiler type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10q">10-Question Assessment</SelectItem>
                        <SelectItem value="25q">25-Question Assessment</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground mt-1">
                      Select which default question set to use as a starting point.
                    </p>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={!isFormValid || isLoading}>
                {templateToEdit ? 'Update Template' : 'Create Template'}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
