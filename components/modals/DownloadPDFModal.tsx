import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useState, useRef, forwardRef, useImperativeHandle, useEffect, useCallback } from 'react';
import NetWealth from '@/components/graphs/NetWealth';
import { Property } from '@/components/graphs/Property';
import { Cashflow } from '@/components/graphs/Cashflow';
import MonteCarloResults from '@/components/MonteCarloResults';
import { InputData } from "@/app/protected/planner/types";
import chartConfig from '@/app/protected/planner/page';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { FileDown, FileText, Settings, BarChart, Table as TableIcon, FileCheck, Activity, LineChart } from 'lucide-react';
import { cn } from "@/lib/utils";
import { A4_WIDTH_PX, A4_HEIGHT_PX, paginateContent } from "@/utils/pdfUtils";
import React from "react";
import ModellingTableForPDF from '@/components/tables/ModellingTableForPDF';

// Helper function to get color based on fund type
const getFundColor = (fundType: string): string => {
  switch (fundType?.toLowerCase()) {
    case 'conservative':
      return '#3b82f6'; // blue-500
    case 'moderate':
      return '#6366f1'; // indigo-500
    case 'balanced':
      return '#8b5cf6'; // violet-500
    case 'growth':
      return '#10b981'; // emerald-500
    case 'high growth':
      return '#f59e0b'; // amber-500
    case 'aggressive':
      return '#ef4444'; // red-500
    case 'custom':
      return '#8b5cf6'; // violet-500
    default:
      return '#6366f1'; // indigo-500
  }
};

interface DownloadPDFModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDownload: (selectedSections: SelectedSections, successfulScenarios: number | null, failedScenarios: number | null) => Promise<void>;
  householdName: string;
  scenarioName: string;
  mainMemberName?: string; // Added main member's name
  allMetrics: any[];
  inputData: InputData;
  showAdditionalData: {
    show_savings: boolean;
    show_investment: boolean;
    show_individual_investments?: boolean;
    show_kiwisaver: boolean;
    show_individual_kiwisavers?: boolean;
    show_cashflow: boolean;
  };
  minNetWealthAtAge: number[];
  maxNetWealthAtAge: number[];
  averageNetWealthAtAge: number[];
  chanceOfSuccess: number | null;
  successfulScenarios: number | null;
  failedScenarios: number | null;
  chartConfig: typeof chartConfig;
  getPdfPreviewRef?: (ref: HTMLDivElement | null) => void;
}

interface SelectedSections {
  personal: {
    selected: boolean;
    fields: {
      name: boolean;
      startingAge: boolean;
      endingAge: boolean;
      partnerName: boolean;
      partnerStartingAge: boolean;
    };
  };
  income: {
    selected: boolean;
    fields: {
      annualIncome: boolean;
      incomePeriod: boolean;
      superannuation: boolean;
      partnerIncome: boolean;
      additionalIncomes: boolean;
    };
  };
  savings: {
    selected: boolean;
    fields: {
      savingsAmount: boolean;
      cashReserve: boolean;
      savingPercentage: boolean;
    };
  };
  expenditure: {
    selected: boolean;
    fields: {
      annualExpenses1: boolean;
      expensePeriod1: boolean;
      annualExpenses2: boolean;
      expensePeriod2: boolean;
      additionalExpenses: boolean;
    };
  };
  investment: {
    selected: boolean;
    fields: {
      initialInvestment: boolean;
      annualContribution: boolean;
      annualReturn: boolean;
      standardDeviation: boolean;
    };
  };
  kiwiSaver: {
    selected: boolean;
    fields: {
      initialKiwiSaver: boolean;
      contribution: boolean;
      employerContribution: boolean;
      annualReturn: boolean;
      standardDeviation: boolean;
      partnerDetails: boolean;
    };
  };
  property: {
    selected: boolean;
    fields: {
      propertyValue: boolean;
      debt: boolean;
      growth: boolean;
      interestRate: boolean;
      debtYears: boolean;
      additionalRepayments: boolean;
      includeDebt: boolean;
    };
  };
  economic: {
    selected: boolean;
    fields: {
      inflationRate: boolean;
    };
  };
  monteCarlo: {
    selected: boolean;
    fields: {
      simulations: boolean;
      confidenceInterval: boolean;
    };
  };
  table: {
    selected: boolean;
    fields: {
      age: boolean;
      grossIncome: boolean;
      netIncome: boolean;
      totalExpenditure: boolean;
      additionalExpenditure: boolean;
      netWealth: boolean;
      netCashflow: boolean;
      totalWithdrawals: boolean;
      investmentsFund: boolean;
      totalKiwiSaver: boolean;
      mainKiwiSaver: boolean;
      partnerKiwiSaver: boolean;
      annualInvestmentReturn: boolean;
      annualKiwiSaverReturn: boolean;
      minimumInvestmentReturn: boolean;
      maximumInvestmentReturn: boolean;
      annualInvestmentContribution: boolean;
      annualKiwiSaverContribution: boolean;
      propertyValue: boolean;
      debtValue: boolean;
      monthlyDebtRepayment: boolean;
      annualDebtRepayments: boolean;
      annualInterestPayments: boolean;
      annualPrincipalRepayments: boolean;
      incomeTax: boolean;
      mtrInvestmentTax: boolean;
      pieInvestmentTax: boolean;
      kiwiSaverTax: boolean;
      mainIncomeTax: boolean;
      partnerIncomeTax: boolean;
    };
  };
  aiSummary: {
    selected: boolean;
  };
  whatIf: {
    selected: boolean;
    fields: {
      recessionEvents: boolean;
      insuranceEvents: boolean;
      lifeEvents: boolean;
    };
  };
  financialProjections: {
    selected: boolean;
    fields: {
      netWealth: boolean;
      cashflow: boolean;
      property: boolean;
      debt: boolean;
      debtRepayments: boolean;
    };
  };
  modellingTable: {
    selected: boolean;
    fields: {
      incomeExpenses: boolean;
      investmentsKiwiSaver: boolean;
      property: boolean;
      taxation: boolean;
      all: boolean;
    };
  };
}

// Function to get color for fund types is defined at the top of the file

const DownloadPDFModal = forwardRef((props: DownloadPDFModalProps, ref) => {
  const {
  isOpen,
  onClose,
  onDownload, // Kept for type compatibility but not used directly
  householdName,
  scenarioName,
  mainMemberName,
  allMetrics,
  inputData,
  showAdditionalData,
  minNetWealthAtAge,
  maxNetWealthAtAge,
  averageNetWealthAtAge,
  chanceOfSuccess,
  successfulScenarios,
  failedScenarios,
  chartConfig
} = props;
  const [selectedSections, setSelectedSections] = useState<SelectedSections>({
    personal: {
      selected: true,
      fields: {
        name: true,
        startingAge: true,
        endingAge: true,
        partnerName: true,
        partnerStartingAge: true,
      },
    },
    income: {
      selected: true,
      fields: {
        annualIncome: true,
        incomePeriod: true,
        superannuation: true,
        partnerIncome: true,
        additionalIncomes: true,
      },
    },
    savings: {
      selected: true,
      fields: {
        savingsAmount: true,
        cashReserve: true,
        savingPercentage: true,
      },
    },
    expenditure: {
      selected: true,
      fields: {
        annualExpenses1: true,
        expensePeriod1: true,
        annualExpenses2: true,
        expensePeriod2: true,
        additionalExpenses: true,
      },
    },
    investment: {
      selected: true,
      fields: {
        initialInvestment: true,
        annualContribution: true,
        annualReturn: true,
        standardDeviation: true,
      },
    },
    kiwiSaver: {
      selected: true,
      fields: {
        initialKiwiSaver: true,
        contribution: true,
        employerContribution: true,
        annualReturn: true,
        standardDeviation: true,
        partnerDetails: true,
      },
    },
    property: {
      selected: true,
      fields: {
        propertyValue: true,
        debt: true,
        growth: true,
        interestRate: true,
        debtYears: true,
        additionalRepayments: true,
        includeDebt: true,
      },
    },
    economic: {
      selected: true,
      fields: {
        inflationRate: true,
      },
    },
    monteCarlo: {
      selected: true,
      fields: {
        simulations: true,
        confidenceInterval: true,
      },
    },

    table: {
      selected: false,
      fields: {
        age: true,
        grossIncome: true,
        netIncome: true,
        totalExpenditure: true,
        additionalExpenditure: true,
        netWealth: true,
        netCashflow: true,
        totalWithdrawals: true,
        investmentsFund: true,
        totalKiwiSaver: true,
        mainKiwiSaver: true,
        partnerKiwiSaver: true,
        annualInvestmentReturn: true,
        annualKiwiSaverReturn: true,
        minimumInvestmentReturn: true,
        maximumInvestmentReturn: true,
        annualInvestmentContribution: true,
        annualKiwiSaverContribution: true,
        propertyValue: true,
        debtValue: true,
        monthlyDebtRepayment: true,
        annualDebtRepayments: true,
        annualInterestPayments: true,
        annualPrincipalRepayments: true,
        incomeTax: true,
        mtrInvestmentTax: true,
        pieInvestmentTax: true,
        kiwiSaverTax: true,
        mainIncomeTax: true,
        partnerIncomeTax: true,
      },
    },
    aiSummary: {
      selected: false
    },
    whatIf: {
      selected: true,
      fields: {
        recessionEvents: true,
        insuranceEvents: true,
        lifeEvents: true
      }
    },
    financialProjections: {
      selected: false,
      fields: {
        netWealth: true,
        cashflow: true,
        property: true,
        debt: true,
        debtRepayments: true
      }
    },
    modellingTable: {
      selected: false,
      fields: {
        incomeExpenses: true,
        investmentsKiwiSaver: true,
        property: true,
        taxation: true,
        all: false
      }
    }
  });

  const handleSectionChange = (section: keyof SelectedSections, checked: boolean) => {
    setSelectedSections(prev => ({
      ...prev,
      [section]: section === 'aiSummary'
        ? { selected: checked }
        : {
            ...prev[section],
            selected: checked,
            fields: Object.fromEntries(
              Object.entries((prev[section] as any).fields).map(([key]) => [key, checked])
            ),
          },
    }));
  };

  const handleFieldChange = (section: keyof SelectedSections, field: string, checked: boolean) => {
    // Special handling for modellingTable section
    if (section === 'modellingTable') {
      if (field === 'all') {
        // If "all" is toggled, update all fields accordingly
        setSelectedSections(prev => {
          const updatedFields = {
            incomeExpenses: checked,
            investmentsKiwiSaver: checked,
            property: checked,
            taxation: checked,
            all: checked
          };
          return {
            ...prev,
            modellingTable: {
              ...prev.modellingTable,
              fields: updatedFields
            }
          };
        });
      } else {
        // For other fields, just update that field
        setSelectedSections(prev => ({
          ...prev,
          [section]: {
            ...prev[section],
            fields: {
              ...(prev[section] as any).fields,
              [field]: checked,
              // If any field is unchecked, also uncheck "all"
              ...(field !== 'all' && !checked ? { all: false } : {})
            },
          },
        }));
      }
    } else {
      // Default behavior for other sections
      setSelectedSections(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          fields: {
            ...(prev[section] as any).fields,
            [field]: checked,
          },
        },
      }));
    }
  };
  const [isDownloading, setIsDownloading] = useState(false);
  const [isViewingInBrowser, setIsViewingInBrowser] = useState(false);
  const [activeTab, setActiveTab] = useState("note");

  // Create a ref for the PDF preview container
  const pdfPreviewRef = useRef<HTMLDivElement>(null);

  // Container ref for content sections
  const contentSectionsRef = useRef<HTMLDivElement>(null);

  // Function to paginate content
  const paginatePreviewContent = useCallback(() => {
    if (!pdfPreviewRef.current || !contentSectionsRef.current) return;

    // Assign to local variable after null check
    const previewContainer = pdfPreviewRef.current;
    const contentContainer = contentSectionsRef.current;

    // Get all content sections
    const contentSections = Array.from(contentContainer.children) as HTMLElement[];

    // Use the enhanced paginateContent function from pdfUtils
    // This will handle section splitting and better space utilization
    paginateContent(previewContainer, contentSections);

    console.log('[paginatePreviewContent] Content paginated with enhanced algorithm');

    // If the callback is provided, call it with the current ref
    if (props.getPdfPreviewRef && previewContainer) {
      props.getPdfPreviewRef(previewContainer);
    }
  }, [props.getPdfPreviewRef]); // Keep dependencies as they are for now

  // Log when the ref is created or updated and call the callback if provided
  useEffect(() => {
    // If the preview tab is active, paginate the content
    if (activeTab === 'preview') {
      // Wait longer for the content to render completely
      const timeoutId = setTimeout(() => {
        paginatePreviewContent();

        // Add a second pagination after a short delay to handle any rendering issues
        setTimeout(() => {
          paginatePreviewContent();
        }, 300);
      }, 500);

      // Cleanup function to clear timeout if effect re-runs or component unmounts
      return () => {
        clearTimeout(timeoutId);
      };
    }

    // If the callback is provided, call it with the current ref
    if (props.getPdfPreviewRef && pdfPreviewRef.current) {
      props.getPdfPreviewRef(pdfPreviewRef.current);
    }
  }, [isOpen, activeTab, paginatePreviewContent, props.getPdfPreviewRef]);

  // Expose the ref to parent components
  useImperativeHandle(ref, () => {
    return {
      getPdfPreviewElement: () => {
        return pdfPreviewRef.current;
      }
    };
  });

  // Function to generate PDF in background using browser view approach
  const generatePdfInBackground = async () => {
    // First set the active tab to preview to ensure the preview is rendered
    setActiveTab("preview");

    // Wait a moment for the preview to render
    await new Promise(resolve => setTimeout(resolve, 500));

    // Double-check that the preview tab is active
    if (activeTab !== "preview") {
      setActiveTab("preview");
      // Wait again for the preview to render
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Paginate the content
    paginatePreviewContent();

    // Wait for pagination to complete
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get the PDF preview element
    if (!pdfPreviewRef.current) {
      console.error('PDF preview element not found');
      throw new Error('PDF preview element not found');
    }

    // Get all A4 pages
    const pages = pdfPreviewRef.current.querySelectorAll('.a4-page');
    if (!pages || pages.length === 0) {
      console.error('No A4 pages found in the preview');
      throw new Error('No A4 pages found in the preview');
    }

    // Create a hidden iframe for background processing
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.top = '-9999px';
    iframe.style.left = '-9999px';
    iframe.style.width = '0';
    iframe.style.height = '0';
    document.body.appendChild(iframe);

    // Get the iframe document
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      document.body.removeChild(iframe);
      throw new Error('Could not access iframe document');
    }

    // Write the HTML structure to the iframe
    iframeDoc.open();
    iframeDoc.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${householdName} - ${scenarioName} Report</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta name="print-color-adjust" content="exact">
          <meta name="color-adjust" content="exact">
          <style>
            body {
              margin: 0;
              padding: 0;
              background-color: white;
            }
            .page-container {
              max-width: 794px;
              margin: 0;
              padding: 0;
              background-color: white;
              page-break-after: always;
            }
            .page-image {
              width: 100%;
              height: auto;
              display: block;
            }
            /* Only adjust separator lines */
            .border-b {
              margin-top: 8px; /* Move separator lines down more */
            }
            /* Withdrawal priorities styling */
            .withdrawal-priorities-list {
              display: block;
              margin-top: 8px;
            }
            .withdrawal-priority-item {
              display: block;
              margin-bottom: 4px;
              font-weight: 500;
            }
            /* Investment fund card styling */
            .investment-fund-card {
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              padding: 12px;
              margin-bottom: 12px;
            }
            .investment-fund-header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              font-weight: 500;
            }
            .investment-fund-detail {
              font-size: 11px;
              line-height: 1.4;
              color: #666;
            }
            .investment-fund-periods {
              margin-top: 8px;
              padding-top: 8px;
              border-top: 1px solid #e5e7eb;
            }
            .investment-fund-periods-title {
              font-size: 12px;
              font-weight: 500;
              margin-bottom: 4px;
            }
            .investment-fund-period {
              font-size: 10px;
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 4px;
              margin-bottom: 2px;
            }
            /* KiwiSaver card styling */
            .kiwisaver-card {
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              padding: 12px;
              margin-bottom: 12px;
            }
            /* Fund metric styling */
            .fund-metric-label {
              color: #6b7280;
              font-size: 0.85em;
              font-weight: 400;
              opacity: 0.8;
            }
            .fund-metric-value {
              font-weight: 500;
            }
            .fund-period-metrics {
              font-size: 0.9em;
              color: #4b5563;
            }
            @page {
              margin: 0;
              size: A4 portrait;
            }
          </style>
        </head>
        <body>
          <div id="pages-container"></div>
        </body>
      </html>
    `);
    iframeDoc.close();

    // Get the container in the iframe
    const container = iframeDoc.getElementById('pages-container');
    if (!container) {
      document.body.removeChild(iframe);
      throw new Error('Container not found in iframe');
    }

    // Process each page
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i] as HTMLElement;

      // Capture the page as an image with high quality settings
      const canvas = await html2canvas(page, {
        scale: 3, // Higher scale for better quality (600 DPI equivalent)
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
        imageTimeout: 0, // No timeout for images
        windowWidth: page.offsetWidth,
        windowHeight: page.offsetHeight,
        onclone: (clonedDoc) => {
          // Get the cloned page
          const clonedPage = clonedDoc.querySelector(`.a4-page:nth-child(${i + 1})`) as HTMLElement;
          if (clonedPage) {
            // Apply the same styling as in the browser view
            const sections = clonedPage.querySelectorAll('[data-pdf-section], [data-pdf-subsection]');
            sections.forEach((section) => {
              (section as HTMLElement).style.visibility = 'visible';
              (section as HTMLElement).style.display = 'block';
            });

            // Fix separator spacing issues
            const separators = clonedPage.querySelectorAll('.border-b');
            separators.forEach((separator) => {
              const parent = separator.parentElement;
              const isInHeader = parent && parent.classList.contains('pdf-section');
              if (!isInHeader) {
                separator.remove();
              }
            });

            // Remove border-bottom styles from section descriptions
            const sectionDescriptions = clonedPage.querySelectorAll('p.text-muted-foreground');
            sectionDescriptions.forEach((desc) => {
              (desc as HTMLElement).style.borderBottom = 'none';
            });

            // Move separator lines down
            const separatorLines = clonedPage.querySelectorAll('.border-b');
            separatorLines.forEach((separator) => {
              (separator as HTMLElement).style.marginTop = '8px';
            });

            // Fix withdrawal priorities display
            const withdrawalPriorities = clonedPage.querySelectorAll('.withdrawal-priorities-list');
            withdrawalPriorities.forEach((list) => {
              (list as HTMLElement).style.display = 'block';

              // Style each priority item
              const priorityItems = list.querySelectorAll('.withdrawal-priority-item');
              priorityItems.forEach((item) => {
                (item as HTMLElement).style.display = 'block';
                (item as HTMLElement).style.marginBottom = '4px';
                (item as HTMLElement).style.fontWeight = '500';
              });
            });

            // Fix investment fund cards
            const investmentCards = clonedPage.querySelectorAll('.investment-fund-card');
            investmentCards.forEach((card) => {
              // Ensure proper spacing and borders
              (card as HTMLElement).style.border = '1px solid #e5e7eb';
              (card as HTMLElement).style.borderRadius = '6px';
              (card as HTMLElement).style.padding = '12px';
              (card as HTMLElement).style.marginBottom = '12px';

              // Style the header
              const header = card.querySelector('.investment-fund-header');
              if (header) {
                (header as HTMLElement).style.display = 'flex';
                (header as HTMLElement).style.justifyContent = 'space-between';
                (header as HTMLElement).style.marginBottom = '8px';
                (header as HTMLElement).style.fontWeight = '500';
              }

              // Style the details
              const details = card.querySelectorAll('.investment-fund-detail');
              details.forEach((detail) => {
                (detail as HTMLElement).style.fontSize = '11px';
                (detail as HTMLElement).style.lineHeight = '1.4';
                (detail as HTMLElement).style.color = '#666';
              });

              // Style the fund metric labels
              const metricLabels = card.querySelectorAll('.fund-metric-label');
              metricLabels.forEach((label) => {
                (label as HTMLElement).style.color = '#6b7280';
                (label as HTMLElement).style.fontSize = '0.85em';
                (label as HTMLElement).style.fontWeight = '400';
                (label as HTMLElement).style.opacity = '0.8';
              });

              // Style the fund metric values
              const metricValues = card.querySelectorAll('.fund-metric-value');
              metricValues.forEach((value) => {
                (value as HTMLElement).style.fontWeight = '500';
              });

              // Style the fund period metrics
              const periodMetrics = card.querySelectorAll('.fund-period-metrics');
              periodMetrics.forEach((metrics) => {
                (metrics as HTMLElement).style.fontSize = '0.9em';
                (metrics as HTMLElement).style.color = '#4b5563';
              });



              // Style fund periods
              const periods = card.querySelector('.investment-fund-periods');
              if (periods) {
                (periods as HTMLElement).style.marginTop = '8px';
                (periods as HTMLElement).style.paddingTop = '8px';
                (periods as HTMLElement).style.borderTop = '1px solid #e5e7eb';

                const periodsTitle = periods.querySelector('.investment-fund-periods-title');
                if (periodsTitle) {
                  (periodsTitle as HTMLElement).style.fontSize = '12px';
                  (periodsTitle as HTMLElement).style.fontWeight = '500';
                  (periodsTitle as HTMLElement).style.marginBottom = '4px';
                }

                const periodItems = periods.querySelectorAll('.investment-fund-period');
                periodItems.forEach((item) => {
                  (item as HTMLElement).style.fontSize = '10px';
                  (item as HTMLElement).style.display = 'grid';
                  (item as HTMLElement).style.gridTemplateColumns = '1fr 1fr';
                  (item as HTMLElement).style.gap = '4px';
                  (item as HTMLElement).style.marginBottom = '2px';
                });
              }
            });

            // Fix KiwiSaver cards
            const kiwiSaverCards = clonedPage.querySelectorAll('.kiwisaver-card');
            kiwiSaverCards.forEach((card) => {
              // Ensure proper spacing and borders
              (card as HTMLElement).style.border = '1px solid #e5e7eb';
              (card as HTMLElement).style.borderRadius = '6px';
              (card as HTMLElement).style.padding = '12px';
              (card as HTMLElement).style.marginBottom = '12px';
            });
          }
        }
      });

      // Convert the canvas to an image
      const imgData = canvas.toDataURL('image/png', 1.0);

      // Create a page container
      const pageContainer = iframeDoc.createElement('div');
      pageContainer.className = 'page-container';

      // Create an image element
      const img = iframeDoc.createElement('img');
      img.src = imgData;
      img.className = 'page-image';
      img.alt = `Page ${i + 1}`;

      // Add the image to the page container
      pageContainer.appendChild(img);
      container.appendChild(pageContainer);
    }

    // Wait a moment for everything to render
    await new Promise(resolve => setTimeout(resolve, 500));

    // Use jsPDF to generate and download the PDF automatically
    try {
      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      // Process each page and add it to the PDF
      for (let i = 0; i < container.children.length; i++) {
        const pageContainer = container.children[i] as HTMLElement;
        const img = pageContainer.querySelector('img');

        if (img) {
          // Get the image data
          const imgData = img.src;

          // Add a new page for each page after the first one
          if (i > 0) {
            pdf.addPage();
          }

          // Add the image to the PDF
          pdf.addImage(
            imgData,
            'PNG',
            0, // x position
            0, // y position
            210, // width (A4 width in mm)
            297, // height (A4 height in mm)
            `page-${i}`, // alias
            'FAST' // compression
          );
        }
      }

      // Save the PDF with a filename
      pdf.save(`${householdName}-${scenarioName}-Report.pdf`);

      // Clean up the iframe
      document.body.removeChild(iframe);

      console.log('PDF generated and downloaded successfully');
    } catch (error) {
      console.error('Error generating PDF with jsPDF:', error);

      // Fallback to browser print dialog if jsPDF fails
      const printWindow = iframe.contentWindow;
      if (printWindow) {
        // Add a script to automatically trigger print and handle cleanup
        const script = iframeDoc.createElement('script');
        script.textContent = `
          // Wait for all images to load before printing
          window.addEventListener('load', function() {
            // Small delay to ensure everything is rendered
            setTimeout(function() {
              // Print the document
              window.print();

              // Signal to parent that printing is complete
              window.parent.postMessage('print-complete', '*');
            }, 500);
          });
        `;
        iframeDoc.body.appendChild(script);

        // Set up message listener to clean up iframe after printing
        const messageListener = (event: MessageEvent) => {
          if (event.data === 'print-complete') {
            // Clean up the iframe
            document.body.removeChild(iframe);
            window.removeEventListener('message', messageListener);
          }
        };
        window.addEventListener('message', messageListener);
      }

      // Backup cleanup in case the message event doesn't fire
      setTimeout(() => {
        if (document.body.contains(iframe)) {
          document.body.removeChild(iframe);
        }
      }, 10000); // 10 seconds timeout
    }
  };

  const handleStartDownload = async () => {
    setIsDownloading(true);
    try {
      // Use the new background PDF generation approach
      await generatePdfInBackground();

      // We're completely replacing the original PDF generation
      // with our new approach that uses the same styling as the browser view
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('There was an error generating the PDF. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  // Function to open the PDF preview in a new browser tab
  const handleViewInBrowser = async () => {
    // Set loading state
    setIsViewingInBrowser(true);
    console.log('[handleViewInBrowser] Starting browser view generation');

    try {
      // First set the active tab to preview to ensure the preview is rendered
      console.log('[handleViewInBrowser] Setting active tab to preview, current tab:', activeTab);
      setActiveTab("preview");

      // Wait a moment for the preview to render
      await new Promise(resolve => setTimeout(resolve, 500));

      // Double-check that the preview tab is active
      if (activeTab !== "preview") {
        console.log('[handleViewInBrowser] Tab not set to preview yet, setting again');
        setActiveTab("preview");
        // Wait again for the preview to render
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Log the state of the data
      console.log('[handleViewInBrowser] inputData:', inputData);
      console.log('[handleViewInBrowser] allMetrics length:', allMetrics?.length || 0);
      console.log('[handleViewInBrowser] Selected sections:', selectedSections);

      // Paginate the content
      console.log('[handleViewInBrowser] Paginating content');
      paginatePreviewContent();

      // Wait for pagination to complete
      await new Promise(resolve => setTimeout(resolve, 1000)); // Increased timeout for better rendering

      // Force render the tables directly in the DOM
      console.log('[handleViewInBrowser] Forcing table rendering in the DOM');
      if (selectedSections.modellingTable.selected && pdfPreviewRef.current) {
        const modellingTableSections = pdfPreviewRef.current.querySelectorAll('.pdf-subsection');
        modellingTableSections.forEach(section => {
          const title = section.querySelector('h2');
          if (title && title.textContent?.includes('Financial Metrics Table')) {
            console.log('[handleViewInBrowser] Found Financial Metrics Table section, ensuring tables are rendered');

            // Check if tables exist
            const existingTables = section.querySelectorAll('table');
            if (existingTables.length === 0) {
              console.log('[handleViewInBrowser] No tables found in section, this is the issue!');

              // Force re-render by directly manipulating the DOM
              if (allMetrics && allMetrics.length > 0) {
                console.log('[handleViewInBrowser] Attempting to force render tables with available metrics');

                // Create a temporary container
                const tempContainer = document.createElement('div');
                tempContainer.style.display = 'none';
                document.body.appendChild(tempContainer);

                // Render the ModellingTableForPDF component directly
                const tableHTML = `
                  <div class="space-y-8">
                    <div class="overflow-x-auto mb-8">
                      <table class="w-full border-collapse">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-2 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">Age</th>
                            <th class="px-2 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">Income</th>
                            <th class="px-2 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200">Savings</th>
                          </tr>
                        </thead>
                        <tbody>
                          ${allMetrics.slice(0, 10).map((metric, index) => `
                            <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}">
                              <td class="px-2 py-1 whitespace-nowrap text-xs text-gray-600 border-b border-gray-100">${metric.Age || '-'}</td>
                              <td class="px-2 py-1 whitespace-nowrap text-xs text-gray-600 border-b border-gray-100">${metric['Net Income'] ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(metric['Net Income']) : '-'}</td>
                              <td class="px-2 py-1 whitespace-nowrap text-xs text-gray-600 border-b border-gray-100">${metric['Savings Fund'] ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(metric['Savings Fund']) : '-'}</td>
                            </tr>
                          `).join('')}
                        </tbody>
                      </table>
                    </div>
                  </div>
                `;

                // Append the table HTML to the section
                const tableContainer = document.createElement('div');
                tableContainer.innerHTML = tableHTML;
                section.appendChild(tableContainer);

                console.log('[handleViewInBrowser] Forced table rendering complete');

                // Clean up
                document.body.removeChild(tempContainer);
              }
            }
          }
        });
      }

      // Get the PDF preview element
      if (!pdfPreviewRef.current) {
        console.error('[handleViewInBrowser] PDF preview element not found');
        return;
      }

      console.log('[handleViewInBrowser] PDF preview element found:', pdfPreviewRef.current);

    // Use html2canvas to capture the exact appearance of each page
      // Get all A4 pages
      const pages = pdfPreviewRef.current.querySelectorAll('.a4-page');
      console.log('[handleViewInBrowser] A4 pages found:', pages.length);

      if (!pages || pages.length === 0) {
        console.error('[handleViewInBrowser] No A4 pages found in the preview');
        return;
      }

      // Check if the pages contain the ModellingTableForPDF component
      const tableElements = pdfPreviewRef.current.querySelectorAll('table');
      console.log('[handleViewInBrowser] Table elements found in preview:', tableElements.length);

      // Check for the ModellingTableForPDF component specifically
      const modellingTableSections = pdfPreviewRef.current.querySelectorAll('.pdf-subsection h2');
      modellingTableSections.forEach(section => {
        console.log('[handleViewInBrowser] Section title:', section.textContent);
        if (section.textContent?.includes('Financial Metrics Table')) {
          console.log('[handleViewInBrowser] Found Financial Metrics Table section');
          const parentSection = section.closest('.pdf-subsection');
          if (parentSection) {
            const tables = parentSection.querySelectorAll('table');
            console.log('[handleViewInBrowser] Tables in Financial Metrics section:', tables.length);

            // Check table content
            if (tables.length > 0) {
              const rows = tables[0].querySelectorAll('tr');
              console.log('[handleViewInBrowser] Rows in first table:', rows.length);

              // Log the first row (header)
              if (rows.length > 0) {
                const headerCells = rows[0].querySelectorAll('th');
                const headerTexts = Array.from(headerCells).map(cell => cell.textContent);
                console.log('[handleViewInBrowser] Table headers:', headerTexts);
              }
            }
          }
        }
      });

      // Log the content of each page for debugging
      pages.forEach((page, i) => {
        const tables = page.querySelectorAll('table');
        const tableRows = page.querySelectorAll('tr');
        console.log(`[handleViewInBrowser] Page ${i+1} contains ${tables.length} tables and ${tableRows.length} table rows`);
      });

      // Create a new window with a basic template
      const newWindow = window.open('', '_blank');
      if (!newWindow) {
        alert('Please allow pop-ups to view the report in a new tab');
        return;
      }

      // Extract the tables from the Financial Metrics section for direct HTML rendering
      let tableHTML = '';
      const financialMetricsSections = pdfPreviewRef.current.querySelectorAll('.pdf-subsection');
      financialMetricsSections.forEach(section => {
        const title = section.querySelector('h2');
        if (title && title.textContent?.includes('Financial Metrics Table')) {
          console.log('[handleViewInBrowser] Extracting tables from Financial Metrics section for direct HTML rendering');

          // Instead of using the tables from the preview, we'll create new ones with all rows
          if (allMetrics && allMetrics.length > 0) {
            console.log('[handleViewInBrowser] Creating new tables with all metrics rows:', allMetrics.length);

            // Get the selected fields from the state
            const selectedFields = selectedSections.modellingTable.fields;

            // Define column groups based on selected fields
            const columnGroups = [];

            if (selectedFields.incomeExpenses) {
              columnGroups.push(['Age', 'Savings Fund', 'Income', 'Net Income', 'Total Expenditure', 'Additional Expenditure', 'Net Cashflow']);
            }

            if (selectedFields.investmentsKiwiSaver) {
              columnGroups.push(['Age', 'Investments Fund', 'KiwiSaver', 'PIE Income', 'Total Withdrawals', 'Retirement Income']);
            }

            if (selectedFields.property) {
              columnGroups.push(['Age', 'Property Value', 'Mortgage Balance', 'Property Equity', 'Mortgage Repayments']);
            }

            if (selectedFields.taxation) {
              columnGroups.push(['Age', 'Scenario 1 Income Tax', 'Scenario 2 Income Tax', 'PIE Tax', 'Investment Tax', 'KiwiSaver Tax']);
            }

            if (selectedFields.all || columnGroups.length === 0) {
              // If "all" is selected or no specific fields are selected, include all columns
              columnGroups.push(['Age', 'Savings Fund', 'Income', 'Net Income', 'Total Expenditure', 'Additional Expenditure', 'Net Cashflow']);
              columnGroups.push(['Age', 'Investments Fund', 'KiwiSaver', 'PIE Income', 'Total Withdrawals', 'Retirement Income']);
              columnGroups.push(['Age', 'Property Value', 'Mortgage Balance', 'Property Equity', 'Mortgage Repayments']);
              columnGroups.push(['Age', 'Scenario 1 Income Tax', 'Scenario 2 Income Tax', 'PIE Tax', 'Investment Tax', 'KiwiSaver Tax']);
            }

            // Constants for pagination
            const HEADER_HEIGHT = 150; // Approximate height for header in pixels
            const ROW_HEIGHT = 30; // Approximate height for each row in pixels
            const PAGE_CONTENT_HEIGHT = 1043; // A4 height minus padding (1123px - 80px)

            // Start building the HTML for tables
            tableHTML = '';

            // Process each column group
            columnGroups.forEach((columns, groupIndex) => {
              // Calculate how many rows we can fit per page
              const availableHeightForRows = PAGE_CONTENT_HEIGHT - HEADER_HEIGHT;
              const maxRowsPerPage = Math.floor(availableHeightForRows / ROW_HEIGHT);

              // Split metrics into pages
              for (let pageStart = 0; pageStart < allMetrics.length; pageStart += maxRowsPerPage) {
                const pageEnd = Math.min(pageStart + maxRowsPerPage, allMetrics.length);
                const pageMetrics = allMetrics.slice(pageStart, pageEnd);

                // Create a new page for this chunk of data
                tableHTML += `<div class="financial-metrics-page">`;

                // Add header only on the first page of each table type
                if (pageStart === 0) {
                  tableHTML += `
                    <h2 style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">Financial Metrics Table</h2>
                    <p style="font-size: 12px; color: #666; margin-bottom: 12px;">Detailed financial metrics over time.</p>
                    <div style="margin-bottom: 20px; border-bottom: 1px solid #e5e7eb;"></div>
                  `;
                }

                // Add table title
                if (columnGroups.length > 1) {
                  tableHTML += `
                    <h4 style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">
                      Financial Metrics Table ${groupIndex + 1} of ${columnGroups.length}
                      (Ages ${pageMetrics[0]?.Age || ''}-${pageMetrics[pageMetrics.length-1]?.Age || ''})
                    </h4>
                  `;
                } else {
                  tableHTML += `
                    <h4 style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">
                      Ages ${pageMetrics[0]?.Age || ''}-${pageMetrics[pageMetrics.length-1]?.Age || ''}
                    </h4>
                  `;
                }

                // Add the table
                tableHTML += `
                  <table class="w-full border-collapse" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead class="bg-gray-50" style="background-color: #f9fafb;">
                      <tr>
                        ${columns.map(col => `<th style="padding: 8px; text-align: left; font-size: 11px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">${col}</th>`).join('')}
                      </tr>
                    </thead>
                    <tbody>
                      ${pageMetrics.map((metric, rowIndex) => `
                        <tr style="background-color: ${rowIndex % 2 === 0 ? '#ffffff' : '#f9fafb'};">
                          ${columns.map(col => {
                            const value = metric[col];
                            let formattedValue = value;

                            // Format numbers as currency if they look like monetary values
                            if (typeof value === 'number' && col !== 'Age') {
                              formattedValue = new Intl.NumberFormat('en-NZ', {
                                style: 'currency',
                                currency: 'NZD',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                              }).format(value);
                            }

                            return `<td style="padding: 6px; text-align: left; font-size: 10px; color: #4b5563; border-bottom: 1px solid #f3f4f6;">${formattedValue || '-'}</td>`;
                          }).join('')}
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                `;

                // Close the page div
                tableHTML += `</div>`;
              }
            });
          }
        }
      });

      // Write the initial HTML structure
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${householdName} - ${scenarioName} Report</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="print-color-adjust" content="exact">
            <meta name="color-adjust" content="exact">
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                display: flex;
                flex-direction: column;
                align-items: center;
              }
              .ui-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                font-size: 14px;
                font-weight: 500;
                height: 36px;
                padding-left: 16px;
                padding-right: 16px;
                border-radius: 4px;
                background-color: hsl(222.2 47.4% 11.2%);
                color: hsl(210 40% 98%);
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 150ms;
                margin: 30px auto;
                border: none;
                cursor: pointer;
              }
              .ui-button:hover {
                background-color: hsl(222.2 47.4% 15.2%);
              }
              .ui-button:focus {
                outline: 2px solid transparent;
                outline-offset: 2px;
                box-shadow: 0 0 0 2px hsl(215 20.2% 65.1%);
              }
              @media (prefers-color-scheme: light) {
                .ui-button {
                  background-color: hsl(221.2 83.2% 53.3%);
                  color: hsl(210 40% 98%);
                }
                .ui-button:hover {
                  background-color: hsl(221.2 83.2% 47.3%);
                }
              }
              .page-container {
                max-width: 794px;
                margin: 0 auto 20px auto;
                background-color: white;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              }
              .page-image {
                width: 100%;
                height: auto;
                display: block;
              }
              /* Financial metrics section styling */
              .financial-metrics-section {
                width: 794px;
                margin: 0 auto 20px auto;
                background-color: white;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                padding: 40px;
                box-sizing: border-box;
              }
              .financial-metrics-page {
                width: 794px;
                height: 1123px;
                margin: 0 auto 20px auto;
                background-color: white;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                padding: 40px;
                box-sizing: border-box;
                page-break-after: always;
                overflow: hidden;
              }
              /* Table styling */
              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-family: Arial, sans-serif;
              }
              th {
                padding: 6px 4px;
                text-align: left;
                font-size: 9px;
                font-weight: 600;
                text-transform: uppercase;
                background-color: #f9fafb;
                border: 1px solid #e5e7eb;
                max-width: 80px;
                overflow: hidden;
                white-space: normal;
                word-break: break-word;
                line-height: 1.2;
              }
              td {
                padding: 4px;
                font-size: 9px;
                border: 1px solid #e5e7eb;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              tr:nth-child(even) {
                background-color: #f9fafb;
              }
              /* Only adjust separator lines */
              .border-b {
                margin-top: 8px; /* Move separator lines down more */
              }
              /* Withdrawal priorities styling */
              .withdrawal-priorities-list {
                display: block;
                margin-top: 8px;
              }
              .withdrawal-priority-item {
                display: block;
                margin-bottom: 4px;
                font-weight: 500;
              }
              /* Investment fund card styling */
              .investment-fund-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 12px;
              }
              .investment-fund-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                font-weight: 500;
              }
              .investment-fund-detail {
                font-size: 11px;
                line-height: 1.4;
                color: #666;
              }
              .investment-fund-periods {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #e5e7eb;
              }
              .investment-fund-periods-title {
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 4px;
              }
              .investment-fund-period {
                font-size: 10px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 4px;
                margin-bottom: 2px;
              }
              /* KiwiSaver card styling */
              .kiwisaver-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 12px;
                margin-bottom: 12px;
              }
              /* Fund metric styling */
              .fund-metric-label {
                color: #6b7280;
                font-size: 0.85em;
                font-weight: 400;
                opacity: 0.8;
              }
              .fund-metric-value {
                font-weight: 500;
              }
              .fund-period-metrics {
                font-size: 0.9em;
                color: #4b5563;
              }
              @media print {
                /* Remove browser default headers and footers */
                @page {
                  margin: 0;
                  size: A4 portrait;
                }
                html, body {
                  background-color: white;
                  padding: 0;
                  margin: 0;
                  width: 100%;
                  height: 100%;
                }
                .ui-button {
                  display: none;
                }
                .page-container {
                  box-shadow: none;
                  margin: 0;
                  padding: 0;
                  page-break-after: always;
                  max-width: 100%;
                  /* Ensure each page takes exactly one printed page */
                  height: 100vh;
                  box-sizing: border-box;
                }
                /* Hide any browser-generated content */
                body::before, body::after {
                  display: none !important;
                }
              }
            </style>
          </head>
          <body>
            <div id="pages-container"></div>
            ${tableHTML ? `<div id="tables-container">${tableHTML}</div>` : ''}
            <div style="text-align: center; margin: 20px auto; max-width: 600px;">
              <button class="ui-button" id="print-button">Print / Download PDF</button>
              <p style="font-size: 12px; color: #666; margin-top: 10px;">
                For best results, in the print dialog, select "No headers and footers" or similar option in your browser's print settings.
              </p>
            </div>
            <script>
              // Add loading indicator
              const container = document.getElementById('pages-container');
              container.innerHTML = '<div style="text-align: center; padding: 50px;"><h2>Loading report...</h2><p>This may take a few moments.</p></div>';

              // Set up print button with optimized print settings
              document.getElementById('print-button').addEventListener('click', function() {
                // Set print options
                const printOptions = {
                  printBackground: true,
                  headerTemplate: ' ',
                  footerTemplate: ' ',
                  marginTop: 0,
                  marginBottom: 0,
                  marginLeft: 0,
                  marginRight: 0
                };

                // Print with optimized settings
                window.print();
              });
            </script>
          </body>
        </html>
      `);

      newWindow.document.close();

      // Wait for the new window to load
      await new Promise<void>((resolve) => {
        newWindow.onload = () => resolve();
      });

      // Get the container in the new window
      const container = newWindow.document.getElementById('pages-container');
      if (!container) {
        console.error('Container not found in new window');
        return;
      }

      // Clear any loading indicator
      container.innerHTML = '';

      // Process each page
      for (let i = 0; i < pages.length; i++) {
        // Get the current page
        const page = pages[i] as HTMLElement;

        // Skip any page that contains the financial metrics table (we're handling that separately with direct HTML)
        const hasFinancialMetricsTable = page.querySelector('h2')?.textContent?.includes('Financial Metrics Table');
        if (hasFinancialMetricsTable && tableHTML) {
          console.log(`[handleViewInBrowser] Skipping page ${i+1} (tables) as we are rendering it directly with HTML`);
          continue;
        }

        // Create a progress indicator
        const progressDiv = newWindow.document.createElement('div');
        progressDiv.style.textAlign = 'center';
        progressDiv.style.padding = '20px';
        progressDiv.innerHTML = `<p>Rendering page ${i + 1} of ${pages.length}...</p>`;
        container.appendChild(progressDiv);

        // Capture the page as an image with high quality settings
        const canvas = await html2canvas(page, {
          scale: 3, // Higher scale for better quality (600 DPI equivalent)
          useCORS: true,
          logging: false,
          allowTaint: true,
          backgroundColor: '#ffffff',
          imageTimeout: 0, // No timeout for images
          // Set window size to match the element's dimensions
          windowWidth: page.offsetWidth,
          windowHeight: page.offsetHeight,
          onclone: (clonedDoc) => {
            console.log(`[html2canvas] Cloning page ${i + 1}`);

            // Get the cloned page
            const clonedPage = clonedDoc.querySelector(`.a4-page:nth-child(${i + 1})`) as HTMLElement;
            if (clonedPage) {
              console.log(`[html2canvas] Found cloned page ${i + 1}`);

              // Apply the same styling as in the PDF generation
              // Ensure all sections and subsections are visible
              const sections = clonedPage.querySelectorAll('[data-pdf-section], [data-pdf-subsection]');
              console.log(`[html2canvas] Found ${sections.length} sections in cloned page ${i + 1}`);

              sections.forEach((section) => {
                (section as HTMLElement).style.visibility = 'visible';
                (section as HTMLElement).style.display = 'block';
              });

              // Fix separator spacing issues
              // 1. Remove all separators except main section title ones
              const separators = clonedPage.querySelectorAll('.border-b');
              separators.forEach((separator) => {
                const parent = separator.parentElement;
                // Only keep separators for the main header
                const isInHeader = parent && parent.classList.contains('pdf-section');
                // Remove all other separators
                if (!isInHeader) {
                  separator.remove();
                }
              });

              // 2. Also remove any border-bottom styles from section descriptions
              const sectionDescriptions = clonedPage.querySelectorAll('p.text-muted-foreground');
              sectionDescriptions.forEach((desc) => {
                (desc as HTMLElement).style.borderBottom = 'none';
              });

              // 3. Style section titles - NO separators
              const sectionTitles = clonedPage.querySelectorAll('.pdf-subsection h2');
              sectionTitles.forEach((title) => {
                (title as HTMLElement).style.fontSize = '15px';
                (title as HTMLElement).style.fontWeight = '600';
                (title as HTMLElement).style.marginTop = '6px';
                (title as HTMLElement).style.marginBottom = '4px';
                (title as HTMLElement).style.color = '#333';
                (title as HTMLElement).style.marginLeft = '-10px'; // Counteract the subsection indentation
                (title as HTMLElement).style.paddingBottom = '4px';
                (title as HTMLElement).style.borderBottom = 'none'; // Remove separator under section titles
              });

              // 4. Style subsection titles with reduced margins - NO separators
              const subsectionTitles = clonedPage.querySelectorAll('.pdf-subsection h3');
              subsectionTitles.forEach((title) => {
                (title as HTMLElement).style.fontSize = '13px';
                (title as HTMLElement).style.fontWeight = '500';
                (title as HTMLElement).style.marginTop = '3px';
                (title as HTMLElement).style.marginBottom = '1px';
                (title as HTMLElement).style.color = '#444';
                (title as HTMLElement).style.paddingBottom = '2px';
              });

              // Only adjust the position of horizontal separator lines
              const separatorLines = clonedPage.querySelectorAll('.border-b');
              separatorLines.forEach((separator) => {
                // Move the separator down more to prevent it from running through text
                (separator as HTMLElement).style.marginTop = '8px';
              });

              // Fix withdrawal priorities display
              const withdrawalPriorities = clonedPage.querySelectorAll('.withdrawal-priorities-list');
              withdrawalPriorities.forEach((list) => {
                (list as HTMLElement).style.display = 'block';

                // Style each priority item
                const priorityItems = list.querySelectorAll('.withdrawal-priority-item');
                priorityItems.forEach((item) => {
                  (item as HTMLElement).style.display = 'block';
                  (item as HTMLElement).style.marginBottom = '4px';
                  (item as HTMLElement).style.fontWeight = '500';
                });
              });

              // Fix investment fund cards
              const investmentCards = clonedPage.querySelectorAll('.investment-fund-card');
              investmentCards.forEach((card) => {
                // Ensure proper spacing and borders
                (card as HTMLElement).style.border = '1px solid #e5e7eb';
                (card as HTMLElement).style.borderRadius = '6px';
                (card as HTMLElement).style.padding = '12px';
                (card as HTMLElement).style.marginBottom = '12px';

                // Style the header
                const header = card.querySelector('.investment-fund-header');
                if (header) {
                  (header as HTMLElement).style.display = 'flex';
                  (header as HTMLElement).style.justifyContent = 'space-between';
                  (header as HTMLElement).style.marginBottom = '8px';
                  (header as HTMLElement).style.fontWeight = '500';
                }

                // Style the details
                const details = card.querySelectorAll('.investment-fund-detail');
                details.forEach((detail) => {
                  (detail as HTMLElement).style.fontSize = '11px';
                  (detail as HTMLElement).style.lineHeight = '1.4';
                  (detail as HTMLElement).style.color = '#666';
                });

                // Style fund periods
                const periods = card.querySelector('.investment-fund-periods');
                if (periods) {
                  (periods as HTMLElement).style.marginTop = '8px';
                  (periods as HTMLElement).style.paddingTop = '8px';
                  (periods as HTMLElement).style.borderTop = '1px solid #e5e7eb';

                  const periodsTitle = periods.querySelector('.investment-fund-periods-title');
                  if (periodsTitle) {
                    (periodsTitle as HTMLElement).style.fontSize = '12px';
                    (periodsTitle as HTMLElement).style.fontWeight = '500';
                    (periodsTitle as HTMLElement).style.marginBottom = '4px';
                  }

                  const periodItems = periods.querySelectorAll('.investment-fund-period');
                  periodItems.forEach((item) => {
                    (item as HTMLElement).style.fontSize = '10px';
                    (item as HTMLElement).style.display = 'grid';
                    (item as HTMLElement).style.gridTemplateColumns = '1fr 1fr';
                    (item as HTMLElement).style.gap = '4px';
                    (item as HTMLElement).style.marginBottom = '2px';
                  });
                }
              });

              // Fix KiwiSaver cards
              const kiwiSaverCards = clonedPage.querySelectorAll('.kiwisaver-card');
              kiwiSaverCards.forEach((card) => {
                // Ensure proper spacing and borders
                (card as HTMLElement).style.border = '1px solid #e5e7eb';
                (card as HTMLElement).style.borderRadius = '6px';
                (card as HTMLElement).style.padding = '12px';
                (card as HTMLElement).style.marginBottom = '12px';
              });

              // Ensure tables are properly rendered
              const tables = clonedPage.querySelectorAll('table');
              tables.forEach((table) => {
                (table as HTMLElement).style.display = 'table';
                (table as HTMLElement).style.width = '100%';
                (table as HTMLElement).style.tableLayout = 'fixed';
                (table as HTMLElement).style.borderCollapse = 'collapse';
                (table as HTMLElement).style.border = '1px solid #e5e7eb';
              });

              // Ensure table headers are properly rendered
              const tableHeaders = clonedPage.querySelectorAll('th');
              tableHeaders.forEach((header) => {
                (header as HTMLElement).style.padding = '2px 4px';
                (header as HTMLElement).style.border = '1px solid #e5e7eb';
                (header as HTMLElement).style.maxWidth = '80px';
                (header as HTMLElement).style.overflow = 'hidden';
                (header as HTMLElement).style.whiteSpace = 'normal';
                (header as HTMLElement).style.wordBreak = 'break-word';
                (header as HTMLElement).style.fontSize = '9px';
                (header as HTMLElement).style.lineHeight = '1.2';
                (header as HTMLElement).style.backgroundColor = '#f9fafb';
              });

              // Ensure table cells are properly rendered
              const tableCells = clonedPage.querySelectorAll('td');
              tableCells.forEach((cell) => {
                (cell as HTMLElement).style.padding = '2px 4px';
                (cell as HTMLElement).style.border = '1px solid #e5e7eb';
                (cell as HTMLElement).style.maxWidth = '80px';
                (cell as HTMLElement).style.overflow = 'hidden';
                (cell as HTMLElement).style.textOverflow = 'ellipsis';
                (cell as HTMLElement).style.fontSize = '9px';
              });
            }
          }
        });

        // Convert the canvas to an image
        const imgData = canvas.toDataURL('image/png', 1.0);

        // Create a page container
        const pageContainer = newWindow.document.createElement('div');
        pageContainer.className = 'page-container';

        // Create an image element
        const img = newWindow.document.createElement('img');
        img.src = imgData;
        img.className = 'page-image';
        img.alt = `Page ${i + 1}`;

        // Add the image to the page container
        pageContainer.appendChild(img);

        // Replace the progress indicator with the actual page
        container.replaceChild(pageContainer, progressDiv);
      }

      console.log(`Browser view generated with ${pages.length} pages`);
    } catch (error) {
      console.error('Error generating browser view:', error);
      alert('There was an error generating the browser view. Please try again.');
    } finally {
      // Reset loading state
      setIsViewingInBrowser(false);
    }
  };

  // Group sections for better organization
  const sectionGroups = {
    personal: ['personal', 'income', 'savings', 'expenditure'],
    financial: ['investment', 'kiwiSaver', 'property', 'economic'],
    analysis: ['monteCarlo', 'financialProjections', 'whatIf', 'table', 'aiSummary', 'modellingTable']
  };

  const getIcon = (section: string) => {
    switch(section) {
      case 'personal': return <FileText className="h-4 w-4 mr-2" />;
      case 'income': return <FileText className="h-4 w-4 mr-2" />;
      case 'savings': return <FileText className="h-4 w-4 mr-2" />;
      case 'expenditure': return <FileText className="h-4 w-4 mr-2" />;
      case 'investment': return <BarChart className="h-4 w-4 mr-2" />;
      case 'kiwiSaver': return <BarChart className="h-4 w-4 mr-2" />;
      case 'property': return <BarChart className="h-4 w-4 mr-2" />;
      case 'economic': return <BarChart className="h-4 w-4 mr-2" />;
      case 'monteCarlo': return <TableIcon className="h-4 w-4 mr-2" />;
      case 'table': return <TableIcon className="h-4 w-4 mr-2" />;
      case 'aiSummary': return <FileCheck className="h-4 w-4 mr-2" />;
      case 'whatIf': return <Activity className="h-4 w-4 mr-2" />;
      case 'financialProjections': return <LineChart className="h-4 w-4 mr-2" />;
      case 'modellingTable': return <TableIcon className="h-4 w-4 mr-2" />;
      default: return <FileText className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[70%] max-h-[90vh] p-0">
        <div className="flex h-[80vh]">
          {/* Left sidebar with tabs */}
          <div className="w-[200px] border-r border-border bg-muted/40 p-4 flex flex-col">
            <DialogTitle className="text-xl mb-4 flex items-center">
              <FileDown className="h-5 w-5 mr-2" />
              Export PDF
            </DialogTitle>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="flex flex-col h-auto w-full gap-1 bg-transparent">
                <TabsTrigger
                  value="content"
                  className={cn(
                    "justify-start w-full",
                    activeTab === "content" ? "bg-background" : "bg-transparent"
                  )}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Content
                </TabsTrigger>
                <TabsTrigger
                  value="preview"
                  className={cn(
                    "justify-start w-full",
                    activeTab === "preview" ? "bg-background" : "bg-transparent"
                  )}
                >
                  <FileCheck className="h-4 w-4 mr-2" />
                  Preview
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="mt-auto">
              <Button
                onClick={handleViewInBrowser}
                className="w-full mb-2"
                variant="outline"
                disabled={isDownloading || isViewingInBrowser}
              >
                {isViewingInBrowser ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    Preparing View...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    View in Browser
                  </>
                )}
              </Button>
              <Button
                onClick={handleStartDownload}
                className="w-full"
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    Generating...
                  </>
                ) : (
                  <>
                    <FileDown className="h-4 w-4 mr-2" />
                    Download PDF
                  </>
                )}
              </Button>
              <Button
                variant="ghost"
                onClick={onClose}
                className="w-full mt-2"
                disabled={isDownloading}
              >
                Cancel
              </Button>
            </div>
          </div>

          {/* Main content area */}
          <div className="flex-1 p-6 overflow-y-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsContent value="content" className="mt-0 overflow-y-auto">
                <div className="space-y-6">
                  {/* Personal Information Group */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                      <CardDescription>Basic details and financial information</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {sectionGroups.personal.map(sectionKey => {
                        const section = selectedSections[sectionKey as keyof SelectedSections];
                        if (!section) return null;

                        return (
                          <div key={sectionKey} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                {getIcon(sectionKey)}
                                <h3 className="font-medium">
                                  {sectionKey.charAt(0).toUpperCase() + sectionKey.slice(1)}
                                </h3>
                              </div>
                              <Switch
                                checked={section.selected}
                                onCheckedChange={(checked) =>
                                  handleSectionChange(sectionKey as keyof SelectedSections, checked)
                                }
                              />
                            </div>

                            {'fields' in section && section.selected && (
                              <div className="pl-6 grid grid-cols-2 gap-2 mt-2">
                                {Object.entries(section.fields).map(([field, checked]) => (
                                  <div key={field} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`${sectionKey}-${field}`}
                                      checked={checked as boolean}
                                      onCheckedChange={(checked) =>
                                        handleFieldChange(sectionKey as keyof SelectedSections, field, checked as boolean)
                                      }
                                      className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                    />
                                    <Label htmlFor={`${sectionKey}-${field}`} className="text-sm">
                                      {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </CardContent>
                  </Card>

                  {/* Financial Information Group */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg font-medium">Financial Information</CardTitle>
                      <CardDescription>Investment, KiwiSaver, property and economic data</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {sectionGroups.financial.map(sectionKey => {
                        const section = selectedSections[sectionKey as keyof SelectedSections];
                        if (!section) return null;

                        return (
                          <div key={sectionKey} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                {getIcon(sectionKey)}
                                <h3 className="font-medium">
                                  {sectionKey.charAt(0).toUpperCase() + sectionKey.slice(1)}
                                </h3>
                              </div>
                              <Switch
                                checked={section.selected}
                                onCheckedChange={(checked) =>
                                  handleSectionChange(sectionKey as keyof SelectedSections, checked)
                                }
                              />
                            </div>

                            {'fields' in section && section.selected && (
                              <div className="pl-6 grid grid-cols-2 gap-2 mt-2">
                                {Object.entries(section.fields).map(([field, checked]) => (
                                  <div key={field} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`${sectionKey}-${field}`}
                                      checked={checked as boolean}
                                      onCheckedChange={(checked) =>
                                        handleFieldChange(sectionKey as keyof SelectedSections, field, checked as boolean)
                                      }
                                      className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                    />
                                    <Label htmlFor={`${sectionKey}-${field}`} className="text-sm">
                                      {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </CardContent>
                  </Card>

                  {/* Analysis & Results Group */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg font-medium">Analysis & Results</CardTitle>
                      <CardDescription>Monte Carlo simulations, what-if scenarios, data tables and AI summary</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Monte Carlo section */}
                      <div className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getIcon('monteCarlo')}
                            <h3 className="font-medium">Monte Carlo</h3>
                          </div>
                          <Switch
                            checked={selectedSections.monteCarlo.selected}
                            onCheckedChange={(checked) =>
                              handleSectionChange('monteCarlo', checked)
                            }
                          />
                        </div>

                        {selectedSections.monteCarlo.selected && (
                          <div className="pl-6 grid grid-cols-2 gap-2 mt-2">
                            {Object.entries(selectedSections.monteCarlo.fields).map(([field, checked]) => (
                              <div key={field} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`monteCarlo-${field}`}
                                  checked={checked as boolean}
                                  onCheckedChange={(checked) =>
                                    handleFieldChange('monteCarlo', field, checked as boolean)
                                  }
                                  className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                />
                                <Label htmlFor={`monteCarlo-${field}`} className="text-sm">
                                  {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                                </Label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>



                      {/* What-If Scenarios section */}
                      <div className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getIcon('whatIf')}
                            <h3 className="font-medium">What-If Scenarios</h3>
                          </div>
                          <Switch
                            checked={selectedSections.whatIf.selected}
                            onCheckedChange={(checked) =>
                              handleSectionChange('whatIf', checked)
                            }
                          />
                        </div>

                        {selectedSections.whatIf.selected && (
                          <div className="pl-6 grid grid-cols-2 gap-2 mt-2">
                            {Object.entries(selectedSections.whatIf.fields).map(([field, checked]) => (
                              <div key={field} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`whatIf-${field}`}
                                  checked={checked as boolean}
                                  onCheckedChange={(checked) =>
                                    handleFieldChange('whatIf', field, checked as boolean)
                                  }
                                  className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                />
                                <Label htmlFor={`whatIf-${field}`} className="text-sm">
                                  {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                                </Label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Modelling Table section */}
                      <div className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <TableIcon className="h-4 w-4 mr-2" />
                            <h3 className="font-medium">Modelling Table</h3>
                          </div>
                          <Switch
                            checked={selectedSections.modellingTable.selected}
                            onCheckedChange={(checked) =>
                              handleSectionChange('modellingTable', checked)
                            }
                          />
                        </div>

                        {selectedSections.modellingTable.selected && (
                          <div className="pl-6 grid grid-cols-2 gap-2 mt-2">
                            {Object.entries(selectedSections.modellingTable.fields).map(([field, checked]) => {
                              // If "all" is selected, other fields should be disabled
                              const isDisabled = field !== 'all' && selectedSections.modellingTable.fields.all;

                              return (
                                <div key={field} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modellingTable-${field}`}
                                    checked={field === 'all' ? checked as boolean : isDisabled ? true : checked as boolean}
                                    onCheckedChange={(checked) => {
                                      if (field === 'all' && checked) {
                                        // If "all" is checked, check all other fields
                                        setSelectedSections(prev => {
                                          const updatedFields = {
                                            incomeExpenses: true,
                                            investmentsKiwiSaver: true,
                                            property: true,
                                            taxation: true,
                                            all: true
                                          };
                                          return {
                                            ...prev,
                                            modellingTable: {
                                              ...prev.modellingTable,
                                              fields: updatedFields
                                            }
                                          };
                                        });
                                      } else {
                                        handleFieldChange('modellingTable', field, checked as boolean);
                                      }
                                    }}
                                    disabled={isDisabled}
                                    className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                  />
                                  <Label
                                    htmlFor={`modellingTable-${field}`}
                                    className={`text-sm ${isDisabled ? 'text-muted-foreground' : ''}`}
                                  >
                                    {field === 'incomeExpenses' ? 'Income/Expenses' :
                                     field === 'investmentsKiwiSaver' ? 'Investments/KiwiSaver' :
                                     field === 'property' ? 'Property' :
                                     field === 'taxation' ? 'Taxation' :
                                     field === 'all' ? 'All Metrics' : field}
                                  </Label>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="preview" className="mt-0 overflow-y-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>PDF Preview</CardTitle>
                    <CardDescription>
                      This is how your PDF will look when downloaded (A4 format)
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex justify-center overflow-y-auto max-h-[70vh]">
                    <div ref={pdfPreviewRef} className="pdf-preview-container overflow-y-auto w-full max-w-[794px]" style={{ height: 'auto', fontSize: '10pt', fontFamily: 'Arial, sans-serif' }}>
                      <style>
                        {`
                          /* Typography */
                          .pdf-preview-container h1 { font-size: 16pt; margin-bottom: 10px; font-family: Arial, sans-serif; font-weight: 600; }
                          .pdf-preview-container h2 { font-size: 14pt; margin-bottom: 8px; font-family: Arial, sans-serif; font-weight: 600; }
                          .pdf-preview-container h3 { font-size: 12pt; margin-bottom: 6px; font-family: Arial, sans-serif; font-weight: 500; }
                          .pdf-preview-container h4 { font-size: 10pt; margin-bottom: 5px; font-family: Arial, sans-serif; font-weight: 500; }
                          .pdf-preview-container .text-sm { font-size: 9pt; font-family: Arial, sans-serif; }
                          .pdf-preview-container .text-xs { font-size: 8pt; font-family: Arial, sans-serif; }
                          .pdf-preview-container p { font-size: 10pt; margin-bottom: 6px; font-family: Arial, sans-serif; }
                          .pdf-preview-container div { font-size: 10pt; font-family: Arial, sans-serif; }

                          /* Improved PDF section and subsection styling */
                          .pdf-section, .pdf-subsection {
                            margin-bottom: 4px;
                            position: relative;
                          }

                          /* Subsection specific styling */
                          .pdf-subsection {
                            break-inside: avoid; /* Prevent subsections from breaking across pages */
                            page-break-inside: avoid;
                            min-height: 20px; /* Ensure even empty subsections take some space */
                            margin-bottom: 2px; /* Reduced spacing between subsections */
                            padding-bottom: 2px; /* Reduced padding at the bottom */
                          }

                          /* Section title styling */
                          .pdf-subsection h2 {
                            font-size: 16px;
                            font-weight: 600;
                            margin-top: 10px;
                            margin-bottom: 4px;
                            color: #333;
                            padding-bottom: 6px;
                            border-bottom: 1px solid #ddd; /* Add a subtle border under section titles */
                          }

                          /* Subsection title styling */
                          .pdf-subsection h3 {
                            font-size: 14px;
                            font-weight: 500;
                            margin-top: 8px;
                            margin-bottom: 6px;
                            color: #444;
                            padding-bottom: 4px;
                            border-bottom: 1px solid #eee; /* Add a very subtle border under subsection titles */
                          }

                          /* Sub-subsection title styling */
                          .pdf-subsection h4 {
                            font-size: 13px;
                            font-weight: 500;
                            margin-top: 6px;
                            margin-bottom: 4px;
                            color: #555;
                          }

                          /* A4 page styling */
                          .a4-page {
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            margin-bottom: 30px;
                            box-sizing: border-box;
                            background-color: white;
                            position: relative;
                            overflow: hidden;
                            width: 794px; /* A4 width in pixels */
                            min-height: 1123px; /* A4 height in pixels */
                          }

                          .a4-page > div {
                            display: flex;
                            flex-direction: column;
                            height: 100%;
                            padding: 75px 75px 100px; /* Top, sides, bottom padding */
                            font-family: Arial, sans-serif;
                          }

                          /* Ensure content flows properly */
                          .pdf-section h1, .pdf-section h2, .pdf-section h3,
                          .pdf-subsection h1, .pdf-subsection h2, .pdf-subsection h3 {
                            margin-top: 0;
                            page-break-after: avoid;
                            break-after: avoid;
                          }

                          /* Prevent orphaned headings */
                          .pdf-section h1:last-child, .pdf-section h2:last-child, .pdf-section h3:last-child,
                          .pdf-subsection h1:last-child, .pdf-subsection h2:last-child, .pdf-subsection h3:last-child {
                            margin-bottom: 0;
                          }

                          /* Page number styling */
                          .pdf-page-number {
                            position: absolute;
                            bottom: 40px;
                            right: 75px;
                            font-size: 12px;
                            color: #888;
                            font-family: Arial, sans-serif;
                            font-weight: normal;
                          }

                          /* Data attributes for debugging */
                          [data-pdf-section], [data-pdf-subsection] {
                            position: relative;
                            padding-bottom: 4px;
                            margin-bottom: 4px;
                          }

                          /* Table styling for better PDF rendering */
                          table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 10px;
                          }

                          table th, table td {
                            border: 1px solid #ddd;
                            padding: 4px;
                            font-size: 9pt;
                          }

                          table th {
                            background-color: #f5f5f5;
                            font-weight: bold;
                          }

                          /* List styling */
                          ul, ol {
                            padding-left: 20px;
                            margin-bottom: 10px;
                          }

                          li {
                            margin-bottom: 4px;
                          }

                          /* Grid layout for better space utilization */
                          .grid {
                            display: grid;
                            grid-gap: 8px;
                          }

                          .grid-cols-2 {
                            grid-template-columns: repeat(2, 1fr);
                          }
                        `}
                      </style>
                      {/* A4 pages will be generated dynamically by the pagination function */}
                    </div>

                    {/* Hidden container for content sections that will be paginated */}
                    <div ref={contentSectionsRef} className="hidden">
                      {/* Header section */}
                      <div className="pdf-section mb-6">
                        <h1 className="text-xl font-bold text-center">{householdName || 'Household'} - {scenarioName || 'Scenario'}</h1>
                        <p className="text-center text-muted-foreground mt-1 text-xs">Financial Planning Report</p>
                        <div className="border-b w-full mt-4 mb-4"></div>
                      </div>

                      {/* Personal information section - broken into subsections */}
                      {selectedSections.personal.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Personal Information</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Key details about the client and planning parameters used in this financial projection.
                          </p>
                          <div className="grid grid-cols-2 gap-2 text-sm mt-4">
                            <div>Name: {mainMemberName || inputData?.name || 'John Doe'}</div>
                            <div>Current Age: {inputData?.starting_age || 30}</div>
                            <div>Life Expectancy: {inputData?.ending_age || 90}</div>
                            <div>Projection Time: {(inputData?.ending_age || 90) - (inputData?.starting_age || 30)} years</div>
                            <div>Include Superannuation: {inputData?.superannuation ? 'Yes' : 'No'}</div>
                          </div>

                        </div>
                      )}

                      {/* Partner information subsection */}
                      {selectedSections.personal.selected && inputData?.includePartner && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="text-sm font-semibold mb-2">Partner Information</h3>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>Partner Name: {inputData?.partner_name || 'Jane Doe'}</div>
                            <div>Partner Age: {inputData?.partner_starting_age || 30}</div>
                            {inputData?.partner_ending_age && (
                              <div>Partner Life Expectancy: {inputData?.partner_ending_age}</div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Income section header */}
                      {selectedSections.income.selected && (
                        <div className="pdf-subsection mb-2">
                          <h2 className="text-lg font-semibold mb-2">Income</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            All income sources included in the financial projection, including employment, business, and other income.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Main Income subsection */}
                      {selectedSections.income.selected && inputData?.annual_income > 0 && (
                        <div className="pdf-subsection mb-1">
                          <div className="border rounded-lg p-2 bg-blue-50/30 dark:bg-blue-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>{mainMemberName || inputData?.name || 'Main'} Income</span>
                              <span className="text-blue-700 dark:text-blue-400">${inputData?.annual_income?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="grid grid-cols-2 gap-1 text-muted-foreground">
                              <div>Period: Age {inputData?.income_period?.[0] || 30} to {inputData?.income_period?.[1] || 65}</div>
                              <div>Inflation: {inputData?.main_income_inflation_rate !== undefined ? inputData?.main_income_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              <div>Tax Type: Main</div>
                              {inputData?.superannuation && <div>Superannuation: Yes</div>}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Partner Income subsection */}
                      {selectedSections.income.selected && inputData?.includePartner && inputData?.partner_annual_income > 0 && (
                        <div className="pdf-subsection mb-1">
                          <div className="border rounded-lg p-2 bg-purple-50/30 dark:bg-purple-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>{inputData?.partner_name || 'Partner'} Income</span>
                              <span className="text-purple-700 dark:text-purple-400">${inputData?.partner_annual_income?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="grid grid-cols-2 gap-1 text-muted-foreground">
                              <div>Period: Age {inputData?.partner_income_period?.[0] || inputData?.income_period?.[0]} to {inputData?.partner_income_period?.[1] || inputData?.income_period?.[1]}</div>
                              <div>Inflation: {inputData?.partner_income_inflation_rate !== undefined ? inputData?.partner_income_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              <div>Tax Type: Partner</div>
                              {inputData?.superannuation && <div>Superannuation: Yes</div>}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Additional Incomes header subsection */}
                      {selectedSections.income.selected && inputData?.additional_incomes && inputData.additional_incomes.length > 0 && (
                        <div className="pdf-subsection mb-1">
                          <h3 className="font-medium">Additional Incomes:</h3>
                        </div>
                      )}

                      {/* Individual Additional Income subsections */}
                      {selectedSections.income.selected && inputData?.additional_incomes && inputData.additional_incomes.map((income, index) => (
                        <div key={index} className="pdf-subsection mb-1">
                          <div className="border rounded-lg p-2 bg-green-50/30 dark:bg-green-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>{income.title}</span>
                              <span className="text-green-700 dark:text-green-400">${income.value.toLocaleString()}/year</span>
                            </div>
                            <div className="grid grid-cols-2 gap-1 text-muted-foreground">
                              <div>Period: Age {income.period[0]} to {income.period[1]}</div>
                              <div>Inflation: {income.inflation_rate !== undefined ? income.inflation_rate : inputData?.inflation_rate || 2}%</div>
                              <div>Tax Type: {income.tax_type === 'main' ? 'Main' : income.tax_type === 'partner' ? 'Partner' : 'Tax Free'}</div>
                              {income.frequency && typeof income.frequency === 'number' && income.frequency !== 1 && <div>Frequency: Every {income.frequency} years</div>}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Savings Section header */}
                      {selectedSections.savings.selected && (
                        <div className="pdf-subsection mb-2">
                          <h2 className="text-lg font-semibold mb-1">Savings</h2>
                          <p className="text-sm text-muted-foreground mb-1">
                            Current savings, cash reserves, and allocation strategy for excess cashflow.
                          </p>
                          <div className="border-b w-full mt-1 mb-1"></div>
                        </div>
                      )}

                      {/* Current Savings subsection */}
                      {selectedSections.savings.selected && (
                        <div className="pdf-subsection mb-1">
                          <div className="border rounded-lg p-2 bg-cyan-50/30 dark:bg-cyan-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Current Savings Balance</span>
                              <span className="text-cyan-700 dark:text-cyan-400">${inputData?.savings_amount?.toLocaleString() || '0'}</span>
                            </div>
                            <div className="text-muted-foreground text-xs">
                              Total liquid savings available at the start of planning (excluding KiwiSaver, investments, property equity).
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Cash Reserve subsection */}
                      {selectedSections.savings.selected && (
                        <div className="pdf-subsection mb-1">
                          <div className="border rounded-lg p-2 bg-teal-50/30 dark:bg-teal-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Cash Reserve</span>
                              <span className="text-teal-700 dark:text-teal-400">${inputData?.cash_reserve?.toLocaleString() || '0'}</span>
                            </div>
                            <div className="grid grid-cols-1 gap-1 text-muted-foreground">
                              <div>Cash Rate: {inputData?.cash_rate || 0}% annual interest</div>
                              <div className="text-xs">Minimum cash amount to keep readily available as an emergency fund, not invested in the market.</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Investment Allocation subsection */}
                      {selectedSections.savings.selected && (
                        <div className="pdf-subsection mb-2">
                          <div className="border rounded-lg p-2 bg-emerald-50/30 dark:bg-emerald-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Excess Cashflow Settings</span>
                              <span className="text-emerald-700 dark:text-emerald-400">{inputData?.utilise_excess_cashflow ? 'Enabled' : 'Disabled'}</span>
                            </div>
                            {inputData?.utilise_excess_cashflow ? (
                              <div className="grid grid-cols-1 gap-1 text-muted-foreground">
                                <div>Investment Allocation: {inputData?.saving_percentage || 0}% of surplus cash</div>
                                <div>Cash Allocation: {100 - (inputData?.saving_percentage || 0)}% of surplus cash</div>
                                <div className="text-xs">Excess income will be automatically allocated according to these percentages after expenses and cash reserve top-up.</div>
                              </div>
                            ) : (
                              <div className="text-muted-foreground text-xs">
                                Excess cashflow will not be automatically allocated to investments or cash reserves.
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Investment Section header */}
                      {selectedSections.investment.selected && (
                        <div className="pdf-subsection mb-2">
                          <h2 className="text-lg font-semibold mb-1">Investments</h2>
                          <p className="text-sm text-muted-foreground mb-1">
                            Investment portfolio details including initial balance, contributions, fund types, and expected returns.
                          </p>
                          <div className="border-b w-full mt-1 mb-1"></div>
                        </div>
                      )}

                      {/* Investment Withdrawal Priorities */}
                      {selectedSections.investment.selected && inputData?.withdrawal_priorities && inputData.withdrawal_priorities.length > 0 && (
                        <div className="pdf-subsection mb-2">
                          <h3 className="text-md font-medium mb-1">Withdrawal Priorities</h3>
                          <div className="border rounded-lg p-2 bg-gray-50/30 dark:bg-gray-900/10 mb-3">
                            <div className="text-sm">
                              <div className="mb-1">Funds will be withdrawn in the following order:</div>
                              <div className="withdrawal-priorities-list">
                                {inputData.withdrawal_priorities.map((fundNumber, index) => (
                                  <div
                                    key={fundNumber}
                                    className="withdrawal-priority-item mb-1"
                                    data-fund-number={fundNumber}
                                    data-priority={index + 1}
                                  >
                                    <span className="withdrawal-priority-number font-medium" style={{
                                      color: getFundColor((inputData as any)[`inv_fund_type${fundNumber}`] || 'Balanced')
                                    }}>
                                      {index + 1}.
                                    </span>
                                    <span className="withdrawal-priority-name ml-1" style={{
                                      color: getFundColor((inputData as any)[`inv_fund_type${fundNumber}`] || 'Balanced')
                                    }}>
                                      {(inputData as any)[`investment_description${fundNumber}`] || `Fund ${fundNumber}`}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Display individual investment funds directly from inputData */}
                      {selectedSections.investment.selected && inputData?.withdrawal_priorities && inputData.withdrawal_priorities.length > 0 && (
                        <>
                          {inputData.withdrawal_priorities.map((fundNumber) => {
                            // Skip funds with zero initial investment
                            if (!(inputData as any)[`initial_investment${fundNumber}`] ||
                                (inputData as any)[`initial_investment${fundNumber}`] <= 0) {
                              return null;
                            }

                            // Get fund type and color
                            const fundType = (inputData as any)[`inv_fund_type${fundNumber}`] || 'Balanced';
                            const fundColor = getFundColor(fundType);

                            // Get fund periods if they exist
                            const fundPeriods = (inputData as any)[`fund_periods${fundNumber}`] || [];
                            const hasFundPeriods = fundPeriods && fundPeriods.length > 0;

                            return (
                              <div key={fundNumber} className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 investment-fund-card" style={{ backgroundColor: `${fundColor}10` }} data-fund-number={fundNumber}>
                                  <div className="font-medium mb-1 flex justify-between investment-fund-header">
                                    <span className="investment-fund-title">{(inputData as any)[`investment_description${fundNumber}`] || `Investment Fund ${fundNumber}`}</span>
                                    <span className="investment-fund-amount" style={{ color: fundColor }}>${(inputData as any)[`initial_investment${fundNumber}`].toLocaleString()}</span>
                                  </div>
                                  <div className="grid grid-cols-2 gap-2 text-muted-foreground investment-fund-details">
                                    <div className="investment-fund-detail">
                                      <span className="fund-metric-label">Expected Return:</span>
                                      <span className="fund-metric-value">{(inputData as any)[`annual_investment_return${fundNumber}`] || 0}%</span>
                                    </div>
                                    <div className="investment-fund-detail">
                                      <span className="fund-metric-label">Std Deviation (Risk):</span>
                                      <span className="fund-metric-value">{(inputData as any)[`inv_std_dev${fundNumber}`] || 0}%</span>
                                    </div>
                                    <div className="investment-fund-detail">Tax Type: {(inputData as any)[`inv_tax${fundNumber}`] || inputData?.inv_tax || 'PIE'}</div>
                                    {(inputData as any)[`fund${fundNumber}_income_portion`] !== undefined && (
                                      <div className="investment-fund-detail">Income Portion: {(inputData as any)[`fund${fundNumber}_income_portion`]}%</div>
                                    )}
                                    {(inputData as any)[`annual_investment_contribution${fundNumber}`] > 0 && (
                                      <div className="investment-fund-detail">Annual Contribution: ${(inputData as any)[`annual_investment_contribution${fundNumber}`].toLocaleString()}/year</div>
                                    )}
                                    {(inputData as any)[`contribution_period${fundNumber}`] && (
                                      <div className="investment-fund-detail">Contribution Period: Ages {(inputData as any)[`contribution_period${fundNumber}`][0]} to {(inputData as any)[`contribution_period${fundNumber}`][1]}</div>
                                    )}
                                  </div>

                                  {/* Show fund periods if they exist */}
                                  {hasFundPeriods && (
                                    <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 investment-fund-periods">
                                      <div className="text-sm font-medium mb-1 investment-fund-periods-title">Fund Periods:</div>
                                      <div className="grid gap-1 investment-fund-periods-list">
                                        {fundPeriods.map((period: any, idx: number) => (
                                          <div key={idx} className="text-xs grid grid-cols-2 gap-1 investment-fund-period">
                                            <div className="investment-fund-period-age">Ages {period.period[0]}-{period.period[1]}:</div>
                                            <div className="investment-fund-period-details">
                                              {period.fundType} <span className="text-gray-400 mx-1">|</span> <span className="fund-period-metrics">
                                                <span className="fund-metric-label">Return:</span> {period.return}%,
                                                <span className="fund-metric-label">Risk:</span> {period.stdDev}%
                                              </span>
                                              {period.incomePortion !== undefined && `, Income: ${period.incomePortion}%`}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </>
                      )}

                      {/* Legacy Investment Fund (if no individual funds are defined) */}
                      {selectedSections.investment.selected &&
                       (!inputData?.withdrawal_priorities || inputData.withdrawal_priorities.length === 0) &&
                       inputData?.initial_investment > 0 && (
                        <>
                          {/* Initial Investment subsection */}
                          <div className="pdf-subsection mb-4">
                            <div className="border rounded-lg p-3 bg-blue-50/30 dark:bg-blue-950/10">
                              <div className="font-medium mb-1 flex justify-between">
                                <span>Initial Investment</span>
                                <span className="text-blue-700 dark:text-blue-400">${inputData?.initial_investment?.toLocaleString() || '0'}</span>
                              </div>
                              <div className="grid grid-cols-1 gap-2 text-muted-foreground">
                                <div>
                                  <span className="fund-metric-label">Expected Return:</span>
                                  <span className="fund-metric-value">{inputData?.annual_investment_return || 0}%</span>
                                </div>
                                <div>
                                  <span className="fund-metric-label">Std Deviation (Risk):</span>
                                  <span className="fund-metric-value">{inputData?.inv_std_dev || 0}%</span>
                                </div>
                                <div>Tax Type: {inputData?.inv_tax || 'PIE'}</div>
                                <div className="text-xs">Starting investment balance at the beginning of the planning period.</div>
                              </div>
                            </div>
                          </div>

                          {/* Annual Contribution subsection */}
                          <div className="pdf-subsection mb-4">
                            <div className="border rounded-lg p-3 bg-indigo-50/30 dark:bg-indigo-950/10">
                              <div className="font-medium mb-1 flex justify-between">
                                <span>Annual Contribution</span>
                                <span className="text-indigo-700 dark:text-indigo-400">${inputData?.annual_investment_contribution?.toLocaleString() || '0'}/year</span>
                              </div>
                              <div className="grid grid-cols-1 gap-2 text-muted-foreground">
                                {inputData?.contribution_period && (
                                      <div>Period: Age {inputData.contribution_period[0]} to {inputData.contribution_period[1]}</div>
                                    )}
                                    <div className="text-xs">Regular annual contributions to your investment portfolio.</div>
                                  </div>
                                </div>
                              </div>
                            </>
                          )}
                      )

                      {/* One-off Investments header subsection */}
                      {selectedSections.investment.selected && inputData?.one_off_investments && inputData.one_off_investments.length > 0 && (
                        <div className="pdf-subsection mb-2">
                          <h3 className="font-medium">One-off Investments:</h3>
                        </div>
                      )}

                      {/* Individual One-off Investment subsections */}
                      {selectedSections.investment.selected && inputData?.one_off_investments && inputData.one_off_investments.length > 0 &&
                        inputData.one_off_investments.map((investment, index) => (
                          <div key={index} className="pdf-subsection mb-4">
                            <div className="border rounded-lg p-3 bg-green-50/30 dark:bg-green-950/10">
                              <div className="font-medium mb-1 flex justify-between">
                                <span>{investment.details || `Investment at age ${investment.age}`}</span>
                                <span className="text-green-700 dark:text-green-400">${investment.amount.toLocaleString()}</span>
                              </div>
                              <div className="text-muted-foreground">
                                <div>Age: {investment.age}</div>
                              </div>
                            </div>
                            {index < (inputData.one_off_investments?.length || 0) - 1 && (
                              <div className="border-b w-full mt-4 mb-2"></div>
                            )}
                          </div>
                        ))
                      }

                      {/* KiwiSaver Section header */}
                      {selectedSections.kiwiSaver.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">KiwiSaver</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            KiwiSaver retirement savings details including balances, contribution rates, fund types, and consolidation plans.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Main KiwiSaver header subsection */}
                      {selectedSections.kiwiSaver.selected && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium">{mainMemberName || inputData?.name || 'Main'} KiwiSaver</h3>
                        </div>
                      )}

                      {/* Main KiwiSaver Combined Card */}
                      {selectedSections.kiwiSaver.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 investment-fund-card kiwisaver-card" style={{
                            backgroundColor: `${getFundColor(inputData?.ks_fund_type || 'Balanced')}10`
                          }}>
                            <div className="font-medium mb-1 flex justify-between investment-fund-header">
                              <span className="investment-fund-title">KiwiSaver Account</span>
                              <span className="investment-fund-amount" style={{
                                color: getFundColor(inputData?.ks_fund_type || 'Balanced')
                              }}>${inputData?.initial_kiwiSaver?.toLocaleString() || '0'}</span>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-muted-foreground investment-fund-details">
                              <div className="investment-fund-detail">Employee Contribution: {inputData?.kiwisaver_contribution || 0}%</div>
                              <div className="investment-fund-detail">Employer Contribution: {inputData?.employer_contribution || 0}%</div>

                              {/* Show fund type and return info if no periods */}
                              {(!inputData?.ks_periods || inputData.ks_periods.length === 0) && (
                                <>
                                  <div className="investment-fund-detail">Fund Type: {inputData?.ks_fund_type || 'Balanced'}</div>
                                  <div className="investment-fund-detail">
                                    <span className="fund-metric-label">Expected Return:</span>
                                    <span className="fund-metric-value">{inputData?.annual_kiwisaver_return || 0}%</span>
                                  </div>
                                  <div className="investment-fund-detail">
                                    <span className="fund-metric-label">Std Deviation (Risk):</span>
                                    <span className="fund-metric-value">{inputData?.ks_std_dev || 0}%</span>
                                  </div>
                                  {inputData?.ks_income_portion !== undefined && (
                                    <div className="investment-fund-detail">Income Portion: {inputData.ks_income_portion}%</div>
                                  )}
                                  {inputData?.ks_tax_rate !== undefined && (
                                    <div className="investment-fund-detail">Tax Rate: {inputData.ks_tax_rate}%</div>
                                  )}
                                </>
                              )}

                              {/* Show consolidation info if enabled */}
                              {inputData?.consolidate_kiwisaver && (
                                <div className="investment-fund-detail">Consolidation Age: {inputData?.consolidate_kiwisaver_age || 65}</div>
                              )}
                            </div>

                            {/* Show fund periods if they exist */}
                            {inputData?.ks_periods && inputData.ks_periods.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 investment-fund-periods">
                                <div className="text-sm font-medium mb-1 investment-fund-periods-title">KiwiSaver Fund Periods:</div>
                                <div className="grid gap-1 investment-fund-periods-list">
                                  {inputData.ks_periods.map((period, idx) => (
                                    <div key={idx} className="text-xs grid grid-cols-2 gap-1 investment-fund-period">
                                      <div className="investment-fund-period-age">Ages {period.period[0]}-{period.period[1]}:</div>
                                      <div className="investment-fund-period-details">
                                        {period.fundType} <span className="text-gray-400 mx-1">|</span> <span className="fund-period-metrics">
                                          <span className="fund-metric-label">Return:</span> {period.return}%,
                                          <span className="fund-metric-label">Risk:</span> {period.stdDev}%
                                        </span>
                                        {(period as any).incomePortion !== undefined && `, Income: ${(period as any).incomePortion}%`}
                                        {(period as any).taxRate !== undefined && `, Tax: ${(period as any).taxRate}%`}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Partner KiwiSaver header subsection */}
                      {selectedSections.kiwiSaver.selected && inputData?.includePartner && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium">{inputData?.partner_name || 'Partner'} KiwiSaver</h3>
                        </div>
                      )}

                      {/* Partner KiwiSaver Combined Card */}
                      {selectedSections.kiwiSaver.selected && inputData?.includePartner && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 investment-fund-card kiwisaver-card" style={{
                            backgroundColor: `${getFundColor(inputData?.partner_ks_fund_type || 'Balanced')}10`
                          }}>
                            <div className="font-medium mb-1 flex justify-between investment-fund-header">
                              <span className="investment-fund-title">KiwiSaver Account</span>
                              <span className="investment-fund-amount" style={{
                                color: getFundColor(inputData?.partner_ks_fund_type || 'Balanced')
                              }}>${inputData?.partner_initial_kiwisaver?.toLocaleString() || '0'}</span>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-muted-foreground investment-fund-details">
                              <div className="investment-fund-detail">Employee Contribution: {inputData?.partner_kiwisaver_contribution || 0}%</div>
                              <div className="investment-fund-detail">Employer Contribution: {inputData?.partner_employer_contribution || 0}%</div>

                              {/* Show fund type and return info if no periods */}
                              {(!inputData?.partner_ks_periods || inputData.partner_ks_periods.length === 0) && (
                                <>
                                  <div className="investment-fund-detail">Fund Type: {inputData?.partner_ks_fund_type || 'Balanced'}</div>
                                  <div className="investment-fund-detail">
                                    <span className="fund-metric-label">Expected Return:</span>
                                    <span className="fund-metric-value">{inputData?.partner_annual_kiwisaver_return || 0}%</span>
                                  </div>
                                  <div className="investment-fund-detail">
                                    <span className="fund-metric-label">Std Deviation (Risk):</span>
                                    <span className="fund-metric-value">{inputData?.partner_ks_std_dev || 0}%</span>
                                  </div>
                                  {inputData?.partner_ks_income_portion !== undefined && (
                                    <div className="investment-fund-detail">Income Portion: {inputData.partner_ks_income_portion}%</div>
                                  )}
                                  {inputData?.partner_ks_tax_rate !== undefined && (
                                    <div className="investment-fund-detail">Tax Rate: {inputData.partner_ks_tax_rate}%</div>
                                  )}
                                </>
                              )}

                              {/* Show consolidation info if enabled */}
                              {inputData?.partner_consolidate_kiwisaver && (
                                <div className="investment-fund-detail">Consolidation Age: {inputData?.partner_consolidate_kiwisaver_age || 65}</div>
                              )}
                            </div>

                            {/* Show fund periods if they exist */}
                            {inputData?.partner_ks_periods && inputData.partner_ks_periods.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 investment-fund-periods">
                                <div className="text-sm font-medium mb-1 investment-fund-periods-title">KiwiSaver Fund Periods:</div>
                                <div className="grid gap-1 investment-fund-periods-list">
                                  {inputData.partner_ks_periods.map((period, idx) => (
                                    <div key={idx} className="text-xs grid grid-cols-2 gap-1 investment-fund-period">
                                      <div className="investment-fund-period-age">Ages {period.period[0]}-{period.period[1]}:</div>
                                      <div className="investment-fund-period-details">
                                        {period.fundType} <span className="text-gray-400 mx-1">|</span> <span className="fund-period-metrics">
                                          <span className="fund-metric-label">Return:</span> {period.return}%,
                                          <span className="fund-metric-label">Risk:</span> {period.stdDev}%
                                        </span>
                                        {(period as any).incomePortion !== undefined && `, Income: ${(period as any).incomePortion}%`}
                                        {(period as any).taxRate !== undefined && `, Tax: ${(period as any).taxRate}%`}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Expenditure Section header */}
                      {selectedSections.expenditure.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Expenditure</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Expense projections for pre-retirement, post-retirement, and additional specific expenses.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Pre-Retirement Expenses subsection */}
                      {selectedSections.expenditure.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-red-50/30 dark:bg-red-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Pre-Retirement Expenses</span>
                              <span className="text-red-700 dark:text-red-400">${inputData?.annual_expenses1?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Period: Age {inputData?.expense_period1?.[0] || 30} to {inputData?.expense_period1?.[1] || 65}</div>
                              <div>Inflation: {inputData?.expense1_inflation_rate !== undefined ? inputData?.expense1_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              <div>Frequency: Yearly</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Post-Retirement Expenses subsection */}
                      {selectedSections.expenditure.selected && inputData?.second_expense && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-orange-50/30 dark:bg-orange-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Post-Retirement Expenses</span>
                              <span className="text-orange-700 dark:text-orange-400">${inputData?.annual_expenses2?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Period: Age {inputData?.expense_period2?.[0] || 65} to {inputData?.expense_period2?.[1] || 90}</div>
                              <div>Inflation: {inputData?.expense2_inflation_rate !== undefined ? inputData?.expense2_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              <div>Frequency: Yearly</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Additional Expenses header subsection */}
                      {selectedSections.expenditure.selected && inputData?.additional_expenses && inputData.additional_expenses.length > 0 && (
                        <div className="pdf-subsection mb-2">
                          <h3 className="font-medium">Additional Expenses:</h3>
                        </div>
                      )}

                      {/* Individual Additional Expense subsections */}
                      {selectedSections.expenditure.selected && inputData?.additional_expenses && inputData.additional_expenses.length > 0 &&
                        inputData.additional_expenses.map((expense, index) => (
                          <div key={index} className="pdf-subsection mb-4">
                            <div className="border rounded-lg p-3 bg-yellow-50/30 dark:bg-yellow-950/10">
                              <div className="font-medium mb-1 flex justify-between">
                                <span>{expense.title}</span>
                                <span className="text-yellow-700 dark:text-yellow-400">${expense.value.toLocaleString()}/year</span>
                              </div>
                              <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                                <div>Period: Age {expense.period[0]} to {expense.period[1]}</div>
                                <div>Inflation: {expense.inflation_rate !== undefined ? expense.inflation_rate : inputData?.inflation_rate || 2}%</div>
                                <div>Frequency: {expense.frequency ? `Every ${expense.frequency} year(s)` : 'Yearly'}</div>
                              </div>
                            </div>
                          </div>
                        ))
                      }

                      {/* Property Section header */}
                      {selectedSections.property.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Property</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Property assets including values, mortgages, rental income, and future sale or downsize plans.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Main Property header subsection */}
                      {selectedSections.property.selected && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium">{inputData?.property_title || 'Main Property'}</h3>
                        </div>
                      )}

                      {/* Main Property Value subsection */}
                      {selectedSections.property.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-emerald-50/30 dark:bg-emerald-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Property Value</span>
                              <span className="text-emerald-700 dark:text-emerald-400">${inputData?.property_value?.toLocaleString() || '0'}</span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Growth Rate: {inputData?.property_growth || 0}% per year</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Purchase Details subsection */}
                      {selectedSections.property.selected && inputData?.show_purchase_details && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-blue-50/30 dark:bg-blue-950/10">
                            <div className="font-medium mb-1">Purchase Details</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Purchase Age: {inputData?.purchase_age || inputData?.starting_age}</div>
                              <div>Deposit Amount: ${inputData?.deposit_amount?.toLocaleString() || '0'}</div>
                            </div>

                            {inputData?.deposit_sources && Object.values(inputData.deposit_sources).some(value => value > 0) && (
                              <div className="mt-2 space-y-1">
                                <div className="text-xs font-medium">Deposit Sources:</div>
                                <div className="grid grid-cols-2 gap-2 pl-2 text-xs">
                                  {inputData.deposit_sources?.savings && inputData.deposit_sources.savings > 0 && (
                                    <div className="flex justify-between">
                                      <span>Savings:</span>
                                      <span>${inputData.deposit_sources.savings.toLocaleString()}</span>
                                    </div>
                                  )}
                                  {inputData.deposit_sources?.investments && inputData.deposit_sources.investments > 0 && (
                                    <div className="flex justify-between">
                                      <span>Investments:</span>
                                      <span>${inputData.deposit_sources.investments.toLocaleString()}</span>
                                    </div>
                                  )}
                                  {inputData.deposit_sources?.main_kiwisaver && inputData.deposit_sources.main_kiwisaver > 0 && (
                                    <div className="flex justify-between">
                                      <span>{mainMemberName || inputData?.name || 'Main'} KiwiSaver:</span>
                                      <span>${inputData.deposit_sources.main_kiwisaver.toLocaleString()}</span>
                                    </div>
                                  )}
                                  {inputData.deposit_sources?.partner_kiwisaver && inputData.deposit_sources.partner_kiwisaver > 0 && (
                                    <div className="flex justify-between">
                                      <span>{inputData?.partner_name || 'Partner'} KiwiSaver:</span>
                                      <span>${inputData.deposit_sources.partner_kiwisaver.toLocaleString()}</span>
                                    </div>
                                  )}
                                  {inputData.deposit_sources?.gifting && inputData.deposit_sources.gifting > 0 && (
                                    <div className="flex justify-between">
                                      <span>Gifting:</span>
                                      <span>${inputData.deposit_sources.gifting.toLocaleString()}</span>
                                    </div>
                                  )}
                                  {inputData.deposit_sources?.other && inputData.deposit_sources.other > 0 && (
                                    <div className="flex justify-between">
                                      <span>Other:</span>
                                      <span>${inputData.deposit_sources.other.toLocaleString()}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Mortgage Details subsection */}
                      {selectedSections.property.selected && inputData?.include_property_debt && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-red-50/30 dark:bg-red-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Mortgage</span>
                              <span className="text-red-700 dark:text-red-400">${inputData?.debt?.toLocaleString() || '0'}</span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Interest Rate: {inputData?.debt_ir || 0}%</div>
                              <div>Loan Term: {inputData?.initial_debt_years || 0} years</div>
                              {inputData?.additional_debt_repayments > 0 && (
                                <div>Additional Repayments: ${inputData?.additional_debt_repayments?.toLocaleString() || '0'}/year</div>
                              )}
                              {inputData?.interest_only_period && (
                                <div className="col-span-2">Interest Only: Ages {inputData?.interest_only_start_age} to {inputData?.interest_only_end_age}</div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Rental Income subsection */}
                      {selectedSections.property.selected && inputData?.rental_income && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-amber-50/30 dark:bg-amber-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Rental Income</span>
                              <span className="text-amber-700 dark:text-amber-400">${inputData?.rental_amount?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="text-muted-foreground">
                              <div>Period: Ages {inputData?.rental_start_age || inputData?.starting_age} to {inputData?.rental_end_age || inputData?.ending_age}</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Board Income subsection */}
                      {selectedSections.property.selected && inputData?.board_income && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-orange-50/30 dark:bg-orange-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Board Income</span>
                              <span className="text-orange-700 dark:text-orange-400">${inputData?.board_amount?.toLocaleString() || '0'}/year</span>
                            </div>
                            <div className="text-muted-foreground">
                              <div>Period: Ages {inputData?.board_start_age || inputData?.starting_age} to {inputData?.board_end_age || inputData?.ending_age}</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Sale/Downsize Details subsection */}
                      {selectedSections.property.selected && inputData?.sell_main_property && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-purple-50/30 dark:bg-purple-950/10">
                            <div className="font-medium mb-1">Property Sale</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Sale Age: {inputData?.main_property_sale_age}</div>
                              <div>Sale Value: ${inputData?.main_prop_sale_value?.toLocaleString() || '0'}</div>
                              {inputData?.pay_off_debt && <div>Pay Off Debt: Yes</div>}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Downsize Details subsection */}
                      {selectedSections.property.selected && inputData?.downsize && !inputData?.property_index && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-indigo-50/30 dark:bg-indigo-950/10">
                            <div className="font-medium mb-1">Downsize Property</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Downsize Age: {inputData?.downsize_age}</div>
                              <div>New Property Value: ${inputData?.new_property_value?.toLocaleString() || '0'}</div>
                              {inputData?.pay_off_debt_on_downsize && <div>Pay Off Debt: Yes</div>}
                              {inputData?.allocate_to_investment && <div>Allocate Surplus to Investment: Yes</div>}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Additional Properties header */}
                      {selectedSections.property.selected && [2, 3, 4, 5].some(index => inputData?.[`property_value${index}` as keyof InputData]) && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium">Additional Properties</h3>
                        </div>
                      )}

                      {/* Individual Additional Properties */}
                      {selectedSections.property.selected && [2, 3, 4, 5].map(index => {
                        const propertyValue = inputData?.[`property_value${index}` as keyof InputData];
                        if (!propertyValue) return null;

                        return (
                          <React.Fragment key={index}>
                            {/* Additional Property header */}
                            <div className="pdf-subsection mb-4">
                              <h4 className="font-medium">{inputData?.[`property_title${index}` as keyof InputData] || `Property ${index}`}</h4>
                            </div>

                            {/* Property Value */}
                            <div className="pdf-subsection mb-4">
                              <div className="border rounded-lg p-3 bg-emerald-50/30 dark:bg-emerald-950/10">
                                <div className="font-medium mb-1 flex justify-between">
                                  <span>Property Value</span>
                                  <span className="text-emerald-700 dark:text-emerald-400">${(propertyValue as number)?.toLocaleString() || '0'}</span>
                                </div>
                                <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                                  <div>Growth Rate: {inputData?.[`property_growth${index}` as keyof InputData] || 0}% per year</div>
                                </div>
                              </div>
                            </div>

                            {/* Purchase Details */}
                            {inputData?.[`show_purchase_details${index}` as keyof InputData] && (
                              <div className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 bg-blue-50/30 dark:bg-blue-950/10">
                                  <div className="font-medium mb-1">Purchase Details</div>
                                  <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                                    <div>Purchase Age: {inputData?.[`purchase_age${index}` as keyof InputData] || inputData?.starting_age}</div>
                                    <div>Deposit Amount: ${(inputData?.[`deposit_amount${index}` as keyof InputData] as number)?.toLocaleString() || '0'}</div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Mortgage Details */}
                            {inputData?.[`include_property_debt${index}` as keyof InputData] && (
                              <div className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 bg-red-50/30 dark:bg-red-950/10">
                                  <div className="font-medium mb-1 flex justify-between">
                                    <span>Mortgage</span>
                                    <span className="text-red-700 dark:text-red-400">${(inputData?.[`debt${index}` as keyof InputData] as number)?.toLocaleString() || '0'}</span>
                                  </div>
                                  <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                                    <div>Interest Rate: {inputData?.[`debt_ir${index}` as keyof InputData] || 0}%</div>
                                    <div>Loan Term: {inputData?.[`initial_debt_years${index}` as keyof InputData] || 0} years</div>
                                    {(inputData?.[`additional_debt_repayments${index}` as keyof InputData] as number) > 0 && (
                                      <div>Additional Repayments: ${(inputData?.[`additional_debt_repayments${index}` as keyof InputData] as number)?.toLocaleString() || '0'}/year</div>
                                    )}
                                    {inputData?.[`interest_only_period${index}` as keyof InputData] && (
                                      <div className="col-span-2">Interest Only: Ages {inputData?.[`interest_only_start_age${index}` as keyof InputData]} to {inputData?.[`interest_only_end_age${index}` as keyof InputData]}</div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Rental Income */}
                            {inputData?.[`rental_income${index}` as keyof InputData] && (
                              <div className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 bg-amber-50/30 dark:bg-amber-950/10">
                                  <div className="font-medium mb-1 flex justify-between">
                                    <span>Rental Income</span>
                                    <span className="text-amber-700 dark:text-amber-400">${(inputData?.[`rental_amount${index}` as keyof InputData] as number)?.toLocaleString() || '0'}/year</span>
                                  </div>
                                  <div className="text-muted-foreground">
                                    <div>Period: Ages {inputData?.[`rental_start_age${index}` as keyof InputData] || inputData?.starting_age} to {inputData?.[`rental_end_age${index}` as keyof InputData] || inputData?.ending_age}</div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Board Income */}
                            {inputData?.[`board_income${index}` as keyof InputData] && (
                              <div className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 bg-orange-50/30 dark:bg-orange-950/10">
                                  <div className="font-medium mb-1 flex justify-between">
                                    <span>Board Income</span>
                                    <span className="text-orange-700 dark:text-orange-400">${(inputData?.[`board_amount${index}` as keyof InputData] as number)?.toLocaleString() || '0'}/year</span>
                                  </div>
                                  <div className="text-muted-foreground">
                                    <div>Period: Ages {inputData?.[`board_start_age${index}` as keyof InputData] || inputData?.starting_age} to {inputData?.[`board_end_age${index}` as keyof InputData] || inputData?.ending_age}</div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Sale Details */}
                            {inputData?.[`sell_property${index}` as keyof InputData] && (
                              <div className="pdf-subsection mb-4">
                                <div className="border rounded-lg p-3 bg-purple-50/30 dark:bg-purple-950/10">
                                  <div className="font-medium mb-1">Property Sale</div>
                                  <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                                    <div>Sale Age: {inputData?.[`property_sale_age${index}` as keyof InputData]}</div>
                                    <div>Sale Value: ${(inputData?.[`prop_sale_value${index}` as keyof InputData] as number)?.toLocaleString() || '0'}</div>
                                    {inputData?.[`pay_off_debt${index}` as keyof InputData] && <div>Pay Off Debt: Yes</div>}
                                  </div>
                                </div>
                              </div>
                            )}
                          </React.Fragment>
                        );
                      })}

                      {/* Economic Section header */}
                      {selectedSections.economic.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Economic Assumptions</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Key economic parameters used in the financial projections.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Default Inflation Rate subsection */}
                      {selectedSections.economic.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-slate-50/30 dark:bg-slate-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Default Inflation Rate</span>
                              <span className="text-slate-700 dark:text-slate-400">{inputData?.inflation_rate || 2}%</span>
                            </div>
                            <div className="text-muted-foreground text-xs">
                              Base inflation rate used for calculations when specific inflation rates are not provided.
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Income Inflation Rates subsection */}
                      {selectedSections.economic.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-blue-50/30 dark:bg-blue-950/10">
                            <div className="font-medium mb-1">Income Inflation Rates</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Main Income: {inputData?.main_income_inflation_rate !== undefined ? inputData?.main_income_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              {inputData?.includePartner && (
                                <div>Partner Income: {inputData?.partner_income_inflation_rate !== undefined ? inputData?.partner_income_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Expense Inflation Rates subsection */}
                      {selectedSections.economic.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-red-50/30 dark:bg-red-950/10">
                            <div className="font-medium mb-1">Expense Inflation Rates</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Pre-Retirement: {inputData?.expense1_inflation_rate !== undefined ? inputData?.expense1_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              {inputData?.second_expense && (
                                <div>Post-Retirement: {inputData?.expense2_inflation_rate !== undefined ? inputData?.expense2_inflation_rate : inputData?.inflation_rate || 2}%</div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Monte Carlo Section header */}
                      {selectedSections.monteCarlo.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Monte Carlo Simulation</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Probability analysis based on {inputData?.num_simulations || 1000} simulations with varying market conditions.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Simulation Parameters subsection */}
                      {selectedSections.monteCarlo.selected && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-violet-50/30 dark:bg-violet-950/10">
                            <div className="font-medium mb-1">Simulation Parameters</div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              <div>Number of Simulations: {inputData?.num_simulations || 1000}</div>
                              <div>Confidence Interval: {inputData?.confidence_interval || 95}%</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Simulation Results subsection */}
                      {selectedSections.monteCarlo.selected && chanceOfSuccess !== null && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-indigo-50/30 dark:bg-indigo-950/10">
                            <div className="font-medium mb-1 flex justify-between">
                              <span>Chance of Success</span>
                              <span className="text-indigo-700 dark:text-indigo-400">{chanceOfSuccess.toFixed(1)}%</span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                              {successfulScenarios !== null && failedScenarios !== null && (
                                <>
                                  <div>Successful Scenarios: {successfulScenarios}</div>
                                  <div>Failed Scenarios: {failedScenarios}</div>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Net Wealth Range subsection */}
                      {selectedSections.monteCarlo.selected && minNetWealthAtAge && maxNetWealthAtAge && averageNetWealthAtAge && (
                        <div className="pdf-subsection mb-4">
                          <div className="border rounded-lg p-3 bg-fuchsia-50/30 dark:bg-fuchsia-950/10">
                            <div className="font-medium mb-1">Net Wealth Range at Age {inputData?.ending_age || 90}</div>
                            <div className="grid grid-cols-1 gap-2 text-muted-foreground">
                              <div>Minimum: ${minNetWealthAtAge[minNetWealthAtAge.length - 1]?.toLocaleString() || '0'}</div>
                              <div>Average: ${averageNetWealthAtAge[averageNetWealthAtAge.length - 1]?.toLocaleString() || '0'}</div>
                              <div>Maximum: ${maxNetWealthAtAge[maxNetWealthAtAge.length - 1]?.toLocaleString() || '0'}</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* What-If Events Section header */}
                      {selectedSections.whatIf.selected && inputData?.whatIfEvents && inputData.whatIfEvents.length > 0 && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">What-If Scenarios</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Analysis of potential life events and their impact on the financial plan.
                          </p>
                        </div>
                      )}

                      {/* Individual What-If Event subsections */}
                      {selectedSections.whatIf.selected && inputData?.whatIfEvents && inputData.whatIfEvents.filter(event => event.enabled !== false).map((event, index) => {
                        const getEventTitle = (event: any) => {
                          const typeMap: Record<string, string> = {
                            'recession': 'Market Recession',
                            'death': 'Death',
                            'tpd': 'Total Permanent Disability',
                            'trauma': 'Trauma/Critical Illness',
                            'redundancy': 'Redundancy',
                            'maternity': 'Maternity/Paternity Leave',
                            'inheritance': 'Inheritance',
                          };
                          return `${typeMap[event.type]} at age ${event.age}`;
                        };

                        const getEventDetails = (event: any) => {
                          const mainName = mainMemberName || inputData?.name || 'Main';
                          const partnerName = inputData?.partner_name || 'Partner';

                          switch (event.type) {
                            case 'recession':
                              return `${event.marketLoss}% market loss, ${event.reboundType} rebound over ${event.reboundPeriod} years`;
                            case 'death':
                              return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout.toLocaleString()}`;
                            case 'tpd':
                              return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout.toLocaleString()}, Expense reduction: ${event.expenseReduction}%`;
                            case 'trauma':
                              return `${event.person === 'main' ? mainName : partnerName}, Insurance: $${event.insurancePayout.toLocaleString()}, Effect: ${event.recoveryPeriod} years, Income: -${event.incomeReduction}%, Expenses: -${event.expenseReduction}%`;
                            case 'redundancy':
                              return `${event.person === 'main' ? mainName : partnerName}, Severance: $${event.severancePay.toLocaleString()}, Unemployment: ${event.unemploymentPeriod} months`;
                            case 'maternity':
                              return `${event.person === 'main' ? mainName : partnerName}, ${event.leavePeriod || event.maternityLeaveMonths} months leave, Return at ${event.returnToWorkPercentage || event.backToWorkIncomePercent}%`;
                            case 'inheritance':
                              return `$${event.amount.toLocaleString()}, ${event.investPercentage}% invested`;
                            default:
                              return 'Event details';
                          }
                        };

                        return (
                          <div key={index} className="pdf-subsection mb-2">
                            <div className="border rounded p-3">
                              <div className="font-medium">{getEventTitle(event)}</div>
                              <div className="text-muted-foreground">{getEventDetails(event)}</div>
                            </div>
                          </div>
                        );
                      })}

                      {/* Financial Projections Section header */}
                      {selectedSections.financialProjections.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Financial Projections</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Visual representation of key financial metrics over time.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>
                        </div>
                      )}

                      {/* Net Wealth Chart */}
                      {selectedSections.financialProjections.selected && selectedSections.financialProjections.fields.netWealth && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium mb-2">Net Wealth Projection</h3>
                          <div className="border rounded p-3">
                            <NetWealth
                              inputData={inputData}
                              allMetrics={allMetrics}
                              showAdditionalData={{
                                ...showAdditionalData,
                                show_monte_carlo: false,
                                show_realistic_netwealth: false
                              }}
                              minNetWealthAtAge={minNetWealthAtAge}
                              maxNetWealthAtAge={maxNetWealthAtAge}
                              averageNetWealthAtAge={averageNetWealthAtAge}
                              isExpanded={false}
                              chartConfig={chartConfig as any}
                              isAnnotationMode={false}
                              showAnnotations={false}
                              onAnnotationComplete={() => {}}
                              annotations={[]}
                              onAnnotationsChange={() => {}}
                            />
                          </div>
                        </div>
                      )}

                      {/* Cashflow Chart */}
                      {selectedSections.financialProjections.selected && selectedSections.financialProjections.fields.cashflow && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium mb-2">Cashflow Projection</h3>
                          <div className="border rounded p-3">
                            <Cashflow
                              inputData={inputData}
                              allMetrics={allMetrics}
                              isExpanded={false}
                              chartConfig={chartConfig as any}
                            />
                          </div>
                        </div>
                      )}

                      {/* Property Chart */}
                      {selectedSections.financialProjections.selected && selectedSections.financialProjections.fields.property && (
                        <div className="pdf-subsection mb-4">
                          <h3 className="font-medium mb-2">Property Value Projection</h3>
                          <div className="border rounded p-3">
                            <Property
                              inputData={inputData}
                              allMetrics={allMetrics}
                              isExpanded={false}
                              chartConfig={chartConfig as any}
                            />
                          </div>
                        </div>
                      )}

                      {/* Modelling Table Section */}
                      {selectedSections.modellingTable.selected && (
                        <div className="pdf-subsection mb-4">
                          <h2 className="text-lg font-semibold mb-2">Financial Metrics Table</h2>
                          <p className="text-sm text-muted-foreground mb-2">
                            Detailed financial metrics over time.
                          </p>
                          <div className="border-b w-full mt-2 mb-2"></div>

                          <ModellingTableForPDF
                            inputData={inputData}
                            allMetrics={allMetrics} // Pass metrics directly
                            maxRows={5} // Limit to 5 rows for preview
                            maxColumns={8} // Maximum 8 columns per table
                            mainName={inputData?.name?.toString() || 'Main'}
                            partnerName={inputData?.partner_name?.toString() || 'Partner'}
                            selectedFields={selectedSections.modellingTable.fields}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

export default DownloadPDFModal;
