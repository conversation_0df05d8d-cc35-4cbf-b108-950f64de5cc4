'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Professional {
  id: string;
  name: string;
  profession: string;
  phone: string;
  email: string;
  notes?: string;
}

interface AddProfessionalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (professional: Professional) => void;
  professionalToEdit?: Professional;
  isEditing?: boolean;
  onUpdate?: (professional: Professional) => void;
}

export default function AddProfessionalModal({ 
  isOpen, 
  onClose, 
  onAdd, 
  professionalToEdit, 
  isEditing = false, 
  onUpdate 
}: AddProfessionalModalProps) {
  const [newProfessional, setNewProfessional] = useState<Partial<Professional>>(
    professionalToEdit || {}
  );

  useEffect(() => {
    if (isOpen) {
      setNewProfessional(professionalToEdit || {});
    }
  }, [isOpen, professionalToEdit]);

  const handleSubmit = () => {
    if (newProfessional.name && newProfessional.profession) {
      if (isEditing && onUpdate && professionalToEdit) {
        onUpdate({
          id: professionalToEdit.id,
          name: newProfessional.name,
          profession: newProfessional.profession,
          phone: newProfessional.phone || '',
          email: newProfessional.email || '',
          notes: newProfessional.notes
        });
      } else {
        onAdd({
          id: crypto.randomUUID(),
          name: newProfessional.name,
          profession: newProfessional.profession,
          phone: newProfessional.phone || '',
          email: newProfessional.email || '',
          notes: newProfessional.notes
        });
      }
      setNewProfessional({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit' : 'Add'} Professional Contact</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Update' : 'Add'} details about a professional contact related to this household.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={newProfessional.name || ''}
              onChange={(e) => setNewProfessional({ ...newProfessional, name: e.target.value })}
              placeholder="Enter name"
            />
          </div>
          <div>
            <Label htmlFor="profession">Profession</Label>
            <Select
              value={newProfessional.profession}
              onValueChange={(value) => setNewProfessional({ ...newProfessional, profession: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select profession" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="accountant">Accountant</SelectItem>
                <SelectItem value="lawyer">Lawyer</SelectItem>
                <SelectItem value="financial_advisor">Financial Advisor</SelectItem>
                <SelectItem value="insurance_agent">Insurance Agent</SelectItem>
                <SelectItem value="real_estate_agent">Real Estate Agent</SelectItem>
                <SelectItem value="tax_advisor">Tax Advisor</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={newProfessional.phone || ''}
              onChange={(e) => setNewProfessional({ ...newProfessional, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={newProfessional.email || ''}
              onChange={(e) => setNewProfessional({ ...newProfessional, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={newProfessional.notes || ''}
              onChange={(e) => setNewProfessional({ ...newProfessional, notes: e.target.value })}
              placeholder="Add any additional notes"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit}>{isEditing ? 'Update' : 'Add'} Contact</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
