import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { useEffect, useState, useMemo } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';
import { ArrowUpRight } from 'lucide-react';
import dynamic from 'next/dynamic';

// Dynamically import ApexCharts to avoid SSR issues
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

interface RecentScenarioCardProps {
  scenario: {
    id: number;
    scenario_name: string;
    household_name: string | null;
    household_id?: number;
    property_value?: number;
    debt?: number;
    annual_income?: number;
    savings_amount?: number;
    initial_investment?: number;
  };
}

// Pre-defined chart patterns
const chartPatterns = [
  // Growth pattern
  {
    colors: ['#00E396', '#008FFB'],
    data: [[10, 45, 30, 75, 95, 140, 190, 250, 320], [5, 25, 60, 90, 130, 180, 230, 290, 380]]
  },
  // Decline then growth pattern
  {
    colors: ['#FF4560', '#00E396'],
    data: [[180, 150, 110, 80, 60, 90, 130, 190, 260], [120, 90, 70, 50, 30, 70, 120, 180, 240]]
  },
  // Steady growth pattern
  {
    colors: ['#775DD0', '#FEB019'],
    data: [[40, 65, 90, 120, 160, 200, 240, 290, 350], [20, 40, 70, 100, 140, 180, 220, 270, 330]]
  },
  // Volatile pattern
  {
    colors: ['#008FFB', '#FF4560'],
    data: [[60, 120, 80, 150, 100, 190, 140, 220, 280], [40, 90, 60, 130, 80, 160, 110, 190, 250]]
  },
  // Slow start, fast finish pattern
  {
    colors: ['#FEB019', '#775DD0'],
    data: [[20, 30, 45, 70, 110, 170, 250, 350, 480], [10, 20, 35, 60, 100, 160, 240, 340, 460]]
  }
];

export default function RecentScenarioCard({ scenario }: RecentScenarioCardProps) {
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});

  // Listen for view mode changes from FooterMenu
  useEffect(() => {
    // Ensure viewMode has a default value if not in localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      // Set default to 'user' if nothing is stored
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
      localStorage.setItem('scenariosViewMode', 'user');
    }
    
    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };
    
    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();
          
        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    };
    
    fetchUserProfile();
  }, []);

  const handleClick = () => {
    window.open(`/protected/planner?scenarioId=${scenario.id}${scenario.household_id ? `&household_id=${scenario.household_id}` : ''}`, '_blank', 'noopener,noreferrer');
  };

  // Select chart pattern based on scenario ID (deterministic but appears random)
  const chartPattern = useMemo(() => {
    // Use scenario ID to select a pattern (modulo to ensure it's within range)
    const patternIndex = scenario.id % chartPatterns.length;
    return chartPatterns[patternIndex];
  }, [scenario.id]);

  // Chart options
  const chartOptions = useMemo(() => ({
    chart: {
      type: 'area' as const,
      toolbar: {
        show: false
      },
      sparkline: {
        enabled: false
      },
      animations: {
        enabled: false
      },
      fontFamily: 'inherit'
    },
    colors: chartPattern.colors,
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'smooth' as const,
      width: 1
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.9,
        opacityTo: 0.3,
        stops: [0, 100]
      }
    },
    xaxis: {
      categories: ['Year 1', 'Year 3', 'Year 5', 'Year 7', 'Year 9', 'Year 11', 'Year 13', 'Year 15', 'Year 20'],
      labels: {
        show: false
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    yaxis: {
      labels: {
        show: false
      }
    },
    tooltip: {
      enabled: false
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      }
    },
    legend: {
      show: false
    }
  }), [chartPattern]);

  // Chart series
  const series = useMemo(() => [
    {
      name: 'Assets',
      data: chartPattern.data[0]
    },
    {
      name: 'Net Worth',
      data: chartPattern.data[1]
    }
  ], [chartPattern]);

  return (
    <Card className="h-full">
      <CardHeader className="pt-2 pb-0">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-sm">{scenario.scenario_name}</CardTitle>
            <p className="text-xs text-muted-foreground">
              Household: {scenario.household_name || 'Unknown Household'}
            </p>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <ArrowUpRight
                  className="w-4 h-4 cursor-pointer"
                  onClick={handleClick}
                />
              </TooltipTrigger>
              <TooltipContent>
                <span>Open in Planner</span>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="w-full h-[14vh]">
          {typeof window !== 'undefined' && (
            <ReactApexChart
              options={chartOptions}
              series={series}
              type="area"
              height="100%"
              width="100%"
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
