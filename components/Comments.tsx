
import React from 'react';
import { createClient } from '@/utils/supabase/client';
import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Comment {
  id: number;
  content: string;
  created_at: string;
  user_id: string;
  tagged_users?: string[];
}

interface User {
  id: string;
  name: string;
}

interface CommentsProps {
  comments: Comment[];
  entityId: number;
  entityType: 'task' | 'interaction';
  onCommentAdded: () => void;
  renderFormattedComment?: (content: string) => React.ReactNode;
  users?: User[];
}

export default function Comments({ 
  comments, 
  entityId, 
  entityType, 
  onCommentAdded,
  renderFormattedComment,
  users = []
}: CommentsProps) {
  const [newComment, setNewComment] = useState('');
  const [userProfiles, setUserProfiles] = useState<{[key: string]: {name: string}}>({}); 
  const supabase = createClient();

  useEffect(() => {
    fetchUserProfiles();
  }, [comments]);

  const fetchUserProfiles = async () => {
    // Get unique user IDs from comments
    const userIds = [...new Set(comments.map(comment => comment.user_id))].filter(Boolean);
    
    if (userIds.length === 0) return;

    console.log('Fetching profiles for user IDs:', userIds);

    const { data, error } = await supabase
      .from('profiles')
      .select('user_id, name')
      .in('user_id', userIds);

    if (error) {
      console.error('Error fetching user profiles:', error);
      return;
    }

    console.log('Fetched profiles:', data);

    const profiles: {[key: string]: {name: string}} = {};
    data?.forEach(profile => {
      profiles[profile.user_id] = { name: profile.name || 'Unknown User' };
    });

    console.log('Processed profiles:', profiles);
    setUserProfiles(profiles);
  };

  // Function to format comment content with user tags
  const formatCommentContent = (content: string) => {
    if (renderFormattedComment) {
      return renderFormattedComment(content);
    }
    
    // Default formatting if no custom renderer provided
    let formattedContent = content;
    users.forEach(user => {
      const regex = new RegExp(`@${user.name}\\b`, 'g');
      formattedContent = formattedContent.replace(
        regex, 
        `<span class="bg-gray-200 text-gray-800 rounded px-1">@${user.name}</span>`
      );
    });
    
    return <div dangerouslySetInnerHTML={{ __html: formattedContent }} />;
  };

  // Add this function to handle adding a comment
  const handleAddComment = async () => {
    if (!newComment.trim() || !entityId) return;
    
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      // Extract tagged users
      const taggedUserIds = users
        .filter(user => newComment.includes(`@${user.name}`))
        .map(user => user.id);
      
      // Determine the table name based on entity type
      const tableName = entityType === 'task' ? 'task_comments' : 'comments';
      const idField = entityType === 'task' ? 'task_id' : 'interaction_id';
      
      // Insert the comment
      const { data: commentData, error } = await supabase
        .from(tableName)
        .insert({
          [idField]: entityId,
          content: newComment,
          user_id: user.id,
          tagged_users: taggedUserIds.length > 0 ? taggedUserIds : null
        })
        .select('id')
        .single();
        
      if (error) throw error;
      
      // Get entity title for notification
      let entityTitle = '';
      if (entityType === 'task') {
        const { data } = await supabase
          .from('tasks')
          .select('title')
          .eq('id', entityId)
          .single();
        entityTitle = data?.title || 'task';
      } else {
        const { data } = await supabase
          .from('interactions')
          .select('title')
          .eq('id', entityId)
          .single();
        entityTitle = data?.title || 'interaction';
      }
      
      // Send notifications to tagged users
      for (const taggedUserId of taggedUserIds) {
        // Skip if the tagged user is the current user
        if (taggedUserId === user.id) continue;
        
        await supabase.from('notifications').insert({
          user_id: taggedUserId,
          content: `You were mentioned in a comment on ${entityType}: ${entityTitle}`,
          type: `${entityType}_mention`,
          link: `/protected/${entityType === 'task' ? 'tasks' : 'households/household/0/interactions'}?view=${entityId}`,
          created_at: new Date().toISOString()
        });
      }
      
      setNewComment('');
      onCommentAdded();
    } catch (error) {
      console.error(`Error adding ${entityType} comment:`, error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="max-h-[300px] overflow-y-auto space-y-3">
        {comments.map((comment) => (
          <div key={comment.id} className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-600">
              {formatCommentContent(comment.content)}
            </div>
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-gray-400">
                {new Date(comment.created_at).toLocaleString()}
              </p>
              <p className="text-xs font-medium text-gray-500">
                {userProfiles[comment.user_id]?.name || 'Unknown User'}
              </p>
            </div>
          </div>
        ))}
        {comments.length === 0 && (
          <p className="text-sm text-gray-700 text-center">No comments yet</p>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <Input
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="!w-full"
        />
        <Button onClick={handleAddComment}>Comment</Button>
      </div>
    </div>
  );
}
