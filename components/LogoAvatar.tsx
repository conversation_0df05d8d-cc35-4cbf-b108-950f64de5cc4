import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { AvatarImage } from '@/components/ui/avatar';

interface LogoAvatarProps {
  logoPath: string;
  size?: 'small' | 'medium' | 'large';
  onError?: () => void;
}

export default function LogoAvatar({ logoPath, size = 'medium', onError }: LogoAvatarProps) {
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const fetchLogoUrl = async () => {
      if (!logoPath) {
        setLogoUrl(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Get a signed URL for the logo file
        const { data, error } = await supabase.storage
          .from('media')
          .createSignedUrl(logoPath, 3600); // 1 hour expiry

        if (error) {
          console.error('Error creating signed URL:', error);
          if (onError) onError();
          setLogoUrl(null);
        } else if (data?.signedUrl) {
          setLogoUrl(data.signedUrl);
        } else {
          console.error('No signed URL returned from Supabase');
          if (onError) onError();
          setLogoUrl(null);
        }
      } catch (err) {
        console.error('Error fetching logo URL:', err);
        if (onError) onError();
        setLogoUrl(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLogoUrl();
  }, [logoPath, onError]);

  if (isLoading || !logoUrl) {
    return null;
  }

  // Determine the appropriate CSS classes based on size
  const getContainerClasses = () => {
    switch(size) {
      case 'small':
        return 'h-8 w-8';
      case 'large':
        return 'h-16 w-16';
      case 'medium':
      default:
        return 'h-12 w-12';
    }
  };

  return (
    <div className={`relative rounded-full overflow-hidden ${getContainerClasses()}`}>
      <img
        src={logoUrl}
        alt="Company Logo"
        className="object-contain w-full h-full"
        onError={() => {
          console.error('Error loading logo image');
          if (onError) onError();
        }}
      />
    </div>
  );
}
