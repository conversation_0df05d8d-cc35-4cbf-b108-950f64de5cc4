import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { useEffect, useState, useRef } from 'react';
import { createClient } from '@/utils/supabase/client';

const Overview = () => {
  const [counts, setCounts] = useState({
    households: 0,
    scenarios: 0,
    notes: 0,
    scribbles: 0,
  });

  const containerRef = useRef(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const fetchCounts = async () => {
      const supabase = createClient();

      const fetchCount = async (table: string) => {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.error(`Error fetching ${table} count:`, error);
          return 0;
        }
        return count;
      };

      const households = await fetchCount('households');
      const scenarios = await fetchCount('scenarios_data1');
      const notes = await fetchCount('notes');
      const scribbles = await fetchCount('scribbles');

      setCounts({
        households: households ?? 0,
        scenarios: scenarios ?? 0,
        notes: notes ?? 0,
        scribbles: scribbles ?? 0,
      });
    };

    fetchCounts();
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver(entries => {
      for (let entry of entries) {
        setContainerSize({
          width: entry.contentRect.width,
          height: entry.contentRect.height,
        });
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Determine layout and font size based on container width
  const isHorizontalLayout = containerSize.width > 500;
  const getFontSize = (baseSize: number) => {
    const scaleFactor = Math.min(containerSize.width / 1000, 1);
    return Math.max(baseSize * scaleFactor, baseSize * 0.6); // Ensure minimum size
  };

  return (
    <Card ref={containerRef} className="h-full flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle>Overview</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow flex items-center justify-center overflow-hidden">
        <ul
          className={`flex ${
            isHorizontalLayout ? 'flex-row' : 'flex-col'
          } justify-around items-center w-full h-full`}
        >
          {Object.entries(counts).map(([key, value]) => (
            <li key={key} className="text-center flex-1 min-w-0 p-2">
              <div 
                style={{ fontSize: `${getFontSize(1.875)}rem` }} 
                className="font-bold truncate"
              >
                {value}
              </div>
              <div 
                style={{ fontSize: `${getFontSize(1)}rem` }}
                className="truncate"
              >
                Total {key.charAt(0).toUpperCase() + key.slice(1)}
              </div>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default Overview;
