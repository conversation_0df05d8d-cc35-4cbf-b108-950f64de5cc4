import { <PERSON>, CardHeader, <PERSON>Title, CardContent } from "@/components/ui/card";
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';

interface Scribble {
  id: number;
  title: string;
}

const RecentScribbles = () => {
  const [scribbles, setScribbles] = useState<Scribble[]>([]);

  useEffect(() => {
    const fetchScribbles = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('scribbles')
        .select('id, title')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Error fetching scribbles:', error);
      } else {
        setScribbles(data || []);
      }
    };

    fetchScribbles();
  }, []);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Recent Scribbles</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        <ul
          className="grid gap-2 h-full"
          style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))' }}
        >
          {scribbles.map((scribble) => (
            <li key={scribble.id}>
              <Link
                href={`/protected/scribbles/${scribble.id}`}
                className="block p-2 bg-gray-100 rounded hover:bg-gray-200"
              >
                {scribble.title}
              </Link>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default RecentScribbles;
