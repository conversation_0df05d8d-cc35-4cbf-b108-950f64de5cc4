import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useFinancialCalculations } from '@/app/utils/financialCalculations';
import { FinancialData } from '@/app/utils/financialTypes';
import { optimizeScenario } from '@/app/utils/aiAgent';

interface OptimiseScenarioProps {
  inputData: FinancialData;
  onOptimize: (optimizedData: FinancialData) => void;
}

export default function OptimiseScenario({ inputData, onOptimize }: OptimiseScenarioProps) {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationResult, setOptimizationResult] = useState<FinancialData | null>(null);
  const { calculateFinancialLife } = useFinancialCalculations(inputData);

  const handleOptimize = async () => {
    setIsOptimizing(true);
    try {
      const optimizedData = await optimizeScenario(inputData);
      const result = calculateFinancialLife(optimizedData);
      
      if (result.chanceOfSuccess >= 80) {
        setOptimizationResult(optimizedData);
        onOptimize(optimizedData);
      } else {
        console.log('Optimization did not reach 80% success rate. Trying again...');
        // You could implement a retry mechanism here if needed
      }
    } catch (error) {
      console.error('Error optimizing scenario:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Optimize Scenario</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={handleOptimize} disabled={isOptimizing}>
          {isOptimizing ? 'Optimizing...' : 'Optimize Scenario'}
        </Button>
        {optimizationResult && (
          <div>
            <h3>Optimization Suggestions:</h3>
            <ul>
              {Object.entries(optimizationResult).map(([key, value]) => (
                <li key={key}>
                  {key}: {JSON.stringify(value)}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
