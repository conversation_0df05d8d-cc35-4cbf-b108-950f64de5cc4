
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  PaginationState,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { createClient } from "@/utils/supabase/client";
import { format } from "date-fns";
import { Pencil, Trash2, ChevronDown, ChevronUp } from "lucide-react";

interface Note {
  id: string;
  title: string;
  household_id: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  user_id: string;
  created_by: string;
  note_type: string;
  summary: string | null;
}

interface NotesTableProps {
  data: Note[];
  onDataChange: () => Promise<void>;
  onCreateNote: () => void;
}

export function NotesTable({ data, onDataChange, onCreateNote }: NotesTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const router = useRouter();

  // Toggle row expansion
  const toggleRowExpansion = (rowId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [rowId]: !prev[rowId]
    }));
  };

  const handleDeleteNote = async () => {
    if (!noteToDelete) return;
    
    const supabase = createClient();
    const { error } = await supabase
      .from("notes")
      .delete()
      .eq("id", noteToDelete);

    if (error) {
      console.error("Error deleting note:", error);
    } else {
      onDataChange();
    }
    
    setNoteToDelete(null);
  };

  const columns: ColumnDef<Note>[] = [
    {
      id: "expander",
      header: "",
      size: 40,
      cell: ({ row }) => {
        const note = row.original;
        const isExpanded = expandedRows[note.id] || false;
        
        return note.summary ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              toggleRowExpansion(note.id);
            }}
            className="h-8 w-8"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        ) : null;
      },
    },
    {
      accessorKey: "title",
      header: "Title",
      size: 250,
      cell: ({ row }) => {
        const note = row.original;
        return (
          <button
            onClick={() => router.push(`/protected/notes/note/${note.id}`)}
            className="text-blue-600 hover:text-blue-800 hover:underline text-left"
          >
            {note.title}
          </button>
        );
      },
    },
    {
      accessorKey: "note_type",
      header: "Type",
      size: 100,
      cell: ({ row }) => {
        const type = row.original.note_type || "general";
        const colorMap: Record<string, string> = {
          general: "bg-gray-100 text-gray-800",
          important: "bg-red-100 text-red-800",
          info: "bg-blue-100 text-blue-800",
          task: "bg-green-100 text-green-800",
          meeting: "bg-purple-100 text-purple-800"
        };
        
        const bgColor = colorMap[type] || colorMap.general;
        
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${bgColor}`}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </span>
        );
      },
    },
    {
      accessorKey: "household_name",
      header: "Household",
      size: 180,
    },
    {
      accessorKey: "created_by",
      header: "Created by",
      size: 150,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.original.created_at), "MMM d, yyyy");
      },
    },
    {
      accessorKey: "last_edited_at",
      header: "Last Edited",
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.original.last_edited_at), "MMM d, yyyy");
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: ({ row }) => {
        const note = row.original;
        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                router.push(`/protected/notes/note/${note.id}`);
              }}
              className="h-8 w-8"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                setNoteToDelete(note.id);
                setDeleteDialogOpen(true);
              }}
              className="h-8 w-8 text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      pagination,
    },
  });

  return (
    <div className="flex flex-col h-full">
      {/* Fixed header with filters and create button */}
      <div className="flex items-center justify-between py-4 sticky top-0 bg-white z-20">
        <Input
          placeholder="Filter notes..."
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <Button onClick={onCreateNote}>Create Note</Button>
      </div>

      <div className="rounded-md border flex-1 overflow-auto">
        <Table className="min-w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead 
                      key={header.id}
                      style={{ width: header.getSize() }}
                      className="px-4 py-3"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <>
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell 
                        key={cell.id}
                        style={{ width: cell.column.getSize() }}
                        className="px-4 py-3"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  {expandedRows[row.original.id] && row.original.summary && (
                    <TableRow key={`${row.id}-expanded`} className="bg-muted/50">
                      <TableCell colSpan={columns.length} className="px-6 py-4">
                        <div className="text-sm">
                          <h4 className="font-medium mb-1">Summary:</h4>
                          <p>{row.original.summary}</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No notes found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <div className="flex items-center justify-center text-sm font-medium">
          Page {table.getState().pagination.pageIndex + 1} of{' '}
          {table.getPageCount()}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the note.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteNote} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

