import React, { forwardRef, useRef } from 'react';
import { exportComponentAsPNG } from 'react-component-export-image';

interface LogoProps {
  width?: number;
  height?: number;
}

const Logo = forwardRef<SVGSVGElement, LogoProps>(({ width = 40, height = 40 }, ref) => (
  <svg ref={ref} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width={width} height={height}>
    <style>
      {`
        .circle { fill: #f0f0f0; }
        .tree-left { fill: #1a5f38; }
        .tree-middle { fill: #2e8b57; }
        .tree-right { fill: #3cb371; }
      `}
    </style>
    
    {/* Background Circle */}
    <circle className="circle" cx="20" cy="20" r="19.6" />
    
    {/* Clipping Path for Trees */}
    <clipPath id="circle-clip">
      <circle cx="20" cy="20" r="19.6" />
    </clipPath>
    
    <g clipPath="url(#circle-clip)">
      {/* Left Tree */}
      <polygon className="tree-left" points="6,40 16,12 26,40" />
      <polygon className="tree-left" points="8,40 16,16 24,40" />
      <polygon className="tree-left" points="10,36 16,14 22,36" />
      
      {/* Middle Tree */}
      <polygon className="tree-middle" points="13,40 22,10 31,40" />
      <polygon className="tree-middle" points="15,40 22,14 29,40" />
      <polygon className="tree-middle" points="17,36 22,12 27,36" />
      
      {/* Right Tree */}
      <polygon className="tree-right" points="20,40 30,12 40,40" />
      <polygon className="tree-right" points="22,40 30,16 38,40" />
      <polygon className="tree-right" points="24,36 30,14 36,36" />
    </g>
    
    {/* Circle Border */}
    <circle fill="none" stroke="#333" strokeWidth="0.8" cx="20" cy="20" r="19.6" />
  </svg>
));

export const exportLogoAsPNG = () => {
  const componentRef = useRef<HTMLDivElement>(null);

  const LogoComponent = () => (
    <div ref={componentRef}>
      <Logo width={40} height={40} />
    </div>
  );

  if (componentRef.current) {
    exportComponentAsPNG(componentRef as any, {
      fileName: 'logo.png',
    });
  }
};

export default Logo;
