'use client';

import { useState, useEffect, useRef } from 'react';
import { format, startOfWeek, endOfWeek, addDays, parseISO, isSameDay, isWithinInterval, setHours, differenceInDays, addMonths, subMonths, getMonth, getYear, getDaysInMonth, getDay } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Plus, Construction, Maximize2, Minimize2, Calendar as CalendarIcon, Users, X, Loader2 } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import TaskModal from '@/components/modals/TaskModal';
import CalendarEventModal, { CalendarEvent, EVENT_TYPES } from './CalendarEventModal';
import CalendarEventHoverCard from './CalendarEventHoverCard';

interface Task {
  id: number;
  title: string;
  due_date: string;
  household_name: string;
  household_id: number;
  importance: string;
  content: string;
  status: string;
  comments: {
    id: number;
    task_id: number;
    content: string;
    created_at: string;
    user_id: string;
  }[];
}

interface WeekCalendarViewProps {
  tasks: Task[];
  onTaskClick: (task: Task) => void;
  onTaskUpdate?: () => void;
}

interface OrgMember {
  id: string;
  name: string;
  email: string;
}

// Time slots for the calendar (8 AM to 5 PM)
const timeSlots = [
  '8:00 AM', '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
  '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'
];

export default function WeekCalendarView({ tasks, onTaskClick, onTaskUpdate }: WeekCalendarViewProps) {
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 })); // Start on Monday
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isEventModalOpen, setIsEventModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isAllDayRowExpanded, setIsAllDayRowExpanded] = useState(false);
  const allDayRowRef = useRef<HTMLDivElement>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | undefined>(undefined);
  const [isMaximized, setIsMaximized] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [orgMembers, setOrgMembers] = useState<OrgMember[]>([]);
  const [selectedOrgMembers, setSelectedOrgMembers] = useState<string[]>([]);
  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>(EVENT_TYPES.map(type => type.value));
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});
  const [isLoading, setIsLoading] = useState(true);

  const supabase = createClient();


  // Generate the days of the week
  const weekDays = Array.from({ length: 5 }).map((_, i) => {
    const day = addDays(currentWeekStart, i);
    // Ensure the date is set to midnight to avoid timezone issues
    const normalizedDay = new Date(day);
    normalizedDay.setHours(0, 0, 0, 0);
    return {
      date: normalizedDay,
      dayName: format(normalizedDay, 'EEE'),
      dayNumber: format(normalizedDay, 'd')
    };
  });

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentWeekStart(addDays(currentWeekStart, -7));
  };

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentWeekStart(addDays(currentWeekStart, 7));
  };

  // Go to current week
  const goToCurrentWeek = () => {
    setCurrentWeekStart(startOfWeek(new Date(), { weekStartsOn: 1 }));
  };

  // Get tasks for a specific day and time slot
  const getTasksForTimeSlot = (day: Date, timeSlot: string) => {
    return tasks.filter(task => {
      const taskDate = parseISO(task.due_date);
      // Match the day and the hour, but exclude midnight (00:00) which is for all-day tasks
      return isSameDay(taskDate, day) &&
             format(taskDate, 'h:00 a').toLowerCase() === timeSlot.toLowerCase() &&
             !(taskDate.getHours() === 0 && taskDate.getMinutes() === 0);
    });
  };

  // Helper function to convert time string to hour number
  const timeToHour = (timeStr: string): number => {
    const isPM = timeStr.toLowerCase().includes('pm');
    let hour = parseInt(timeStr.split(':')[0]);
    if (isPM && hour !== 12) hour += 12;
    if (!isPM && hour === 12) hour = 0;
    return hour;
  };

  // Get tasks for a specific day without a specific time (all-day tasks)
  const getTasksForDay = (day: Date) => {
    return tasks.filter(task => {
      const taskDate = parseISO(task.due_date);
      // Match the day but check if the time is midnight (00:00) which we use for all-day tasks
      return isSameDay(taskDate, day) &&
             (taskDate.getHours() === 0 && taskDate.getMinutes() === 0);
    });
  };

  // Handle task click
  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setIsTaskModalOpen(true);
  };

  // Handle task modal close
  const handleTaskModalClose = () => {
    setIsTaskModalOpen(false);
    setSelectedTask(null);
  };

  // Fetch calendar events
  const fetchEvents = async () => {
    try {
      if (!profileData.org_id) return;

      setIsLoading(true);

      // Calculate the date range for the current week view
      const startDate = format(currentWeekStart, 'yyyy-MM-dd');
      const endDate = format(addDays(currentWeekStart, 6), 'yyyy-MM-dd');

      // Build the query for events that start within this week
      let query = supabase
        .from('calendar_events')
        .select('*, households(householdName)')
        .eq('org_id', profileData.org_id)
        .gte('date', startDate)
        .lte('date', endDate);

      // Filter by selected event types if not all are selected
      if (selectedEventTypes.length < EVENT_TYPES.length) {
        query = query.in('event_type', selectedEventTypes);
      }

      // Filter by selected organization members if not all are selected
      if (selectedOrgMembers.length > 0) {
        query = query.or(`user_id.eq.${selectedOrgMembers.join(',user_id.eq.')},required_members.cs.{${selectedOrgMembers.join(',')}},optional_members.cs.{${selectedOrgMembers.join(',')}}`);
      }

      const { data: startInWeekData, error: startInWeekError } = await query;

      if (startInWeekError) throw startInWeekError;

      // Check if the database has the necessary columns for multi-day events
      let multiDayData: any[] = [];
      try {
        // Build the query for multi-day events
        let multiDayQuery = supabase
          .from('calendar_events')
          .select('*, households(householdName)')
          .eq('org_id', profileData.org_id)
          .lt('date', startDate)
          .gte('end_date', startDate)
          .eq('is_all_day', true);

        // Filter by selected event types if not all are selected
        if (selectedEventTypes.length < EVENT_TYPES.length) {
          multiDayQuery = multiDayQuery.in('event_type', selectedEventTypes);
        }

        // Filter by selected organization members if not all are selected
        if (selectedOrgMembers.length > 0) {
          multiDayQuery = multiDayQuery.or(`user_id.eq.${selectedOrgMembers.join(',user_id.eq.')},required_members.cs.{${selectedOrgMembers.join(',')}},optional_members.cs.{${selectedOrgMembers.join(',')}}`);
        }

        const { data, error } = await multiDayQuery;

        if (!error) {
          multiDayData = data || [];
        }
      } catch (multiDayError) {
        // If this fails, it likely means the end_date column doesn't exist yet
        console.warn('Could not fetch multi-day events. The end_date column might not exist yet:', multiDayError);
        // Continue with just the events that start within this week
      }

      // Combine both result sets
      const combinedData = [...(startInWeekData || []), ...multiDayData];

      // Remove duplicates (in case an event appears in both queries)
      const uniqueEvents = Array.from(new Map(combinedData.map(event => [event.id, event])).values());

      // Transform the data to include household_name
      const formattedEvents = uniqueEvents.map((event: any) => ({
        ...event,
        household_name: event.households?.householdName || null
      }));

      setEvents(formattedEvents);
    } catch (error) {
      console.error('Error fetching calendar events:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle event click
  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event);
    setIsEventModalOpen(true);
  };

  // Handle cell click to create a new event
  const handleCellClick = (date: Date, timeSlot?: string) => {
    console.log('Cell clicked with date:', format(date, 'yyyy-MM-dd')); // Debug log
    setSelectedDate(date);
    setSelectedTimeSlot(timeSlot);
    setSelectedEvent(null);
    setIsEventModalOpen(true);
  };

  // Helper function to convert time string to hour number
  const timeStringToHour = (timeStr: string): number => {
    const [hourStr, minuteStr] = timeStr.split(':');
    return parseInt(hourStr) + parseInt(minuteStr) / 60;
  };

  // Helper function to convert time slot string to hour number
  const timeSlotToHour = (timeSlot: string): number => {
    const slotHour = timeSlot.match(/^(\d+)/);
    if (!slotHour) return 0;

    let hour = parseInt(slotHour[1]);
    if (timeSlot.toLowerCase().includes('pm') && hour < 12) hour += 12;
    if (timeSlot.toLowerCase().includes('am') && hour === 12) hour = 0;

    return hour;
  };

  // Get all events for a specific day that are not all-day events
  const getTimeEventsForDay = (day: Date) => {
    const dayStr = format(day, 'yyyy-MM-dd');

    return events.filter(event => {
      // Explicitly marked all-day events should be excluded
      if (event.is_all_day) return false;

      // Only include events for this specific day
      if (event.date !== dayStr) return false;

      // Exclude events that span the entire day (for backward compatibility)
      const [startHour] = event.start_time.split(':').map(Number);
      const [endHour] = event.end_time.split(':').map(Number);

      return !(startHour === 0 && (endHour === 23 || endHour === 0));
    });
  };

  // Get single-day all-day events for a specific day
  const getSingleDayEventsForDay = (day: Date) => {
    const dayStr = format(day, 'yyyy-MM-dd');

    return events.filter(event => {
      try {
        // For explicitly marked all-day events, only include single-day events
        if (event.is_all_day) {
          // If it has an end_date and it's different from the start date, it's a multi-day event
          if (event.end_date && event.date !== event.end_date) {
            return false; // Skip multi-day events, they'll be handled separately
          }

          // For single-day all-day events
          return event.date === dayStr;
        }

        // For backward compatibility, also check the old way
        if (event.date === dayStr) {
          // Consider an event all-day if it spans the entire day (e.g., 00:00 to 23:59)
          const [startHour] = event.start_time.split(':').map(Number);
          const [endHour] = event.end_time.split(':').map(Number);

          return startHour === 0 && (endHour === 23 || endHour === 0);
        }

        return false;
      } catch (error) {
        console.warn('Error processing event in getSingleDayEventsForDay:', error, event);
        // If there's an error processing this event, don't include it
        return false;
      }
    });
  };

  // Get all multi-day events that span the current week
  const getMultiDayEvents = () => {
    return events.filter(event => {
      try {
        // Only include explicitly marked all-day events
        if (!event.is_all_day) return false;

        // Only include events with an end_date that's different from the start date
        if (!event.end_date || event.date === event.end_date) return false;

        // Parse dates
        const eventStartDate = parseISO(event.date);
        const eventEndDate = parseISO(event.end_date);

        // Check if the event overlaps with the current week
        const weekStart = currentWeekStart;
        const weekEnd = addDays(currentWeekStart, 4); // Friday

        // Event starts before week ends AND event ends after week starts
        return eventStartDate <= weekEnd && eventEndDate >= weekStart;
      } catch (error) {
        console.warn('Error processing event in getMultiDayEvents:', error, event);
        return false;
      }
    });
  };

  // Check if a day is within an event's date range
  const isDayInEventRange = (day: Date, event: CalendarEvent) => {
    try {
      const eventStartDate = parseISO(event.date);
      const eventEndDate = event.end_date ? parseISO(event.end_date) : eventStartDate;

      return isWithinInterval(day, { start: eventStartDate, end: eventEndDate });
    } catch (error) {
      console.warn('Error checking if day is in event range:', error);
      return false;
    }
  };

  // For backward compatibility - get all all-day events for a specific day
  const getEventsForDay = (day: Date) => {
    // This is kept for backward compatibility with existing code
    return getSingleDayEventsForDay(day);
  };

  // Helper function to calculate the height of the all-day row based on event count and expanded state
  const calculateAllDayRowHeight = () => {
    const multiDayEventGroups = (() => {
      const events = getMultiDayEvents();
      const groups: CalendarEvent[][] = [];

      // Sort events by start date
      const sortedEvents = [...events].sort((a, b) => {
        return parseISO(a.date).getTime() - parseISO(b.date).getTime();
      });

      // Group overlapping events
      sortedEvents.forEach(event => {
        const eventStart = parseISO(event.date);
        const eventEnd = parseISO(event.end_date || event.date);

        const groupIndex = groups.findIndex(group => {
          return group.every(existingEvent => {
            const existingStart = parseISO(existingEvent.date);
            const existingEnd = parseISO(existingEvent.end_date || existingEvent.date);
            return eventEnd < existingStart || eventStart > existingEnd;
          });
        });

        if (groupIndex >= 0) {
          groups[groupIndex].push(event);
        } else {
          groups.push([event]);
        }
      });

      // Sort groups by earliest start date
      groups.sort((a, b) => {
        const aEarliestStart = Math.min(...a.map(e => parseISO(e.date).getTime()));
        const bEarliestStart = Math.min(...b.map(e => parseISO(e.date).getTime()));
        return aEarliestStart - bEarliestStart;
      });

      return groups;
    })();

    // Calculate height based on number of event groups and single-day events
    const numGroups = multiDayEventGroups.length;

    // Get the maximum number of single-day events and tasks for any day in the week
    const numSingleDayEvents = Math.max(
      ...weekDays.map(day => getEventsForDay(day.date).length + getTasksForDay(day.date).length)
    );

    const totalRows = numGroups + numSingleDayEvents;

    // Default (non-expanded) state - fixed small height
    const defaultHeight = {
      allDayHeight: '40px', // Fixed compact height when not expanded
      timeSlotHeight: 'calc(100% - 40px - 40px)',
      expandedHeight: false
    };

    // If the row is not expanded or there are very few events, return the default height
    if (!isAllDayRowExpanded || totalRows <= 1) {
      return defaultHeight;
    }

    // Expanded (hovered) state - calculate based on number of events
    if (totalRows <= 3) {
      return {
        allDayHeight: '90px', // Fixed height for 2-3 events
        timeSlotHeight: 'calc(100% - 40px - 90px)',
        expandedHeight: true
      };
    } else if (totalRows <= 5) {
      return {
        allDayHeight: '120px', // Fixed height for 4-5 events
        timeSlotHeight: 'calc(100% - 40px - 120px)',
        expandedHeight: true
      };
    } else if (totalRows <= 8) {
      return {
        allDayHeight: '160px', // Fixed height for 6-8 events
        timeSlotHeight: 'calc(100% - 40px - 160px)',
        expandedHeight: true
      };
    } else {
      return {
        allDayHeight: '200px', // Maximum fixed height for 9+ events
        timeSlotHeight: 'calc(100% - 40px - 200px)',
        expandedHeight: true
      };
    }
  };

  // Effect to fetch events when the week changes
  // Fetch user profile data to get org_id and load preferences
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data, error } = await supabase
            .from('profiles')
            .select('user_id, org_id')
            .eq('user_id', user.id)
            .single();

          if (data) {
            setProfileData({
              user_id: data.user_id,
              org_id: data.org_id
            });

            // After setting profile data, load user preferences
            await loadUserPreferences(user.id);
          } else if (error) {
            console.error('Error fetching user profile:', error);
          }
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, []);

  // Load user preferences from Supabase
  const loadUserPreferences = async (userId: string) => {
    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('calendar_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // Record not found
          // Set default preferences - current user selected by default
          setSelectedOrgMembers([userId]);
          setSelectedEventTypes(EVENT_TYPES.map(type => type.value));
          setIsMaximized(false);

          // Create default preferences record
          await saveUserPreferences(userId, {
            selected_event_types: EVENT_TYPES.map(type => type.value),
            selected_org_members: [userId],
            is_maximized: false
          });
        } else {
          console.error('Error loading user preferences:', error);
          // Set default values if error
          setSelectedOrgMembers([userId]);
          setSelectedEventTypes(EVENT_TYPES.map(type => type.value));
        }
      } else if (data) {
        // Apply loaded preferences
        setSelectedEventTypes(data.selected_event_types || EVENT_TYPES.map(type => type.value));
        setSelectedOrgMembers(data.selected_org_members || [userId]);
        setIsMaximized(data.is_maximized || false);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      // Set default values if error
      setSelectedOrgMembers([userId]);
      setSelectedEventTypes(EVENT_TYPES.map(type => type.value));
    } finally {
      setIsLoading(false);
    }
  };

  // Save user preferences to Supabase
  const saveUserPreferences = async (userId: string, preferences: {
    selected_event_types: string[],
    selected_org_members: string[],
    is_maximized: boolean
  }) => {
    try {
      const { error } = await supabase
        .from('calendar_preferences')
        .upsert({
          user_id: userId,
          selected_event_types: preferences.selected_event_types,
          selected_org_members: preferences.selected_org_members,
          is_maximized: preferences.is_maximized,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Error saving user preferences:', error);
      }
    } catch (error) {
      console.error('Error saving user preferences:', error);
    }
  };

  // Fetch organization members
  useEffect(() => {
    const fetchOrgMembers = async () => {
      if (!profileData.org_id) return;

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, name, email')
          .eq('org_id', profileData.org_id);

        if (error) throw error;
        setOrgMembers(data?.map(({ user_id, name, email }) => ({ id: user_id, name, email })) || []);
      } catch (error) {
        console.error('Error fetching organization members:', error);
      }
    };

    fetchOrgMembers();
  }, [profileData.org_id]);

  // Fetch events when week changes or filters change
  useEffect(() => {
    // Only fetch events if we have the org_id and preferences are loaded
    if (profileData.org_id && (selectedOrgMembers.length > 0 || selectedEventTypes.length > 0)) {
      fetchEvents();
    }
  }, [currentWeekStart, selectedOrgMembers, selectedEventTypes, profileData.org_id]);

  // Effect to handle mouse tracking for the all-day row expansion
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isAllDayRowExpanded) return;

      // Get the position of the all-day row section
      // Use the ref for the all-day row to find the calendar container
      const calendarElement = document.querySelector('.calendar-grid');
      if (!calendarElement) return;

      const calendarRect = calendarElement.getBoundingClientRect();
      const allDayRowHeight = parseInt(calculateAllDayRowHeight().allDayHeight);

      // Check if mouse is within the calendar but outside the all-day row area
      const isMouseInCalendar = (
        e.clientX >= calendarRect.left &&
        e.clientX <= calendarRect.right &&
        e.clientY >= calendarRect.top
      );

      const isMouseInAllDayArea = (
        e.clientY >= calendarRect.top + 40 && // Header height
        e.clientY <= calendarRect.top + 40 + allDayRowHeight // All-day row height
      );

      // If mouse is in calendar but not in all-day area, collapse the all-day row
      if (isMouseInCalendar && !isMouseInAllDayArea) {
        setIsAllDayRowExpanded(false);
      }
    };

    // Add event listener
    document.addEventListener('mousemove', handleMouseMove);

    // Clean up
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isAllDayRowExpanded]);

  // Mini month calendar functions
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1));
  };

  const getDaysForMonth = (date: Date) => {
    const month = getMonth(date);
    const year = getYear(date);
    const daysInMonth = getDaysInMonth(new Date(year, month));
    const firstDayOfMonth = new Date(year, month, 1);
    const startingDayOfWeek = getDay(firstDayOfMonth);

    // Adjust for Monday as first day of week (0 = Monday, 6 = Sunday)
    const adjustedStartingDay = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < adjustedStartingDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }

    return days;
  };

  const isDateInCurrentWeek = (date: Date | null) => {
    if (!date) return false;
    const weekStart = startOfWeek(currentWeekStart, { weekStartsOn: 1 });
    const weekEnd = addDays(weekStart, 6);
    return date >= weekStart && date <= weekEnd;
  };

  const isInSameWeekRow = (date: Date | null) => {
    if (!date) return false;

    // Find the week row that contains this date
    const weekRowStart = startOfWeek(date, { weekStartsOn: 1 });
    const weekRowEnd = addDays(weekRowStart, 6);

    // Check if any date in the current week is in this row
    const weekStart = startOfWeek(currentWeekStart, { weekStartsOn: 1 });
    return weekRowStart <= weekStart && weekRowEnd >= weekStart;
  };

  const handleDateClick = (date: Date | null) => {
    if (!date) return;
    setCurrentWeekStart(startOfWeek(date, { weekStartsOn: 1 }));
  };

  // Toggle maximize/minimize
  const toggleMaximize = () => {
    const newValue = !isMaximized;
    setIsMaximized(newValue);

    // Save preference
    if (profileData.user_id) {
      saveUserPreferences(profileData.user_id, {
        selected_event_types: selectedEventTypes,
        selected_org_members: selectedOrgMembers,
        is_maximized: newValue
      });
    }
  };

  // Toggle event type selection
  const toggleEventType = (eventType: string) => {
    setSelectedEventTypes(prev => {
      const newTypes = prev.includes(eventType)
        ? prev.filter(type => type !== eventType)
        : [...prev, eventType];

      // Save preference
      if (profileData.user_id) {
        saveUserPreferences(profileData.user_id, {
          selected_event_types: newTypes,
          selected_org_members: selectedOrgMembers,
          is_maximized: isMaximized
        });
      }

      return newTypes;
    });
  };

  // Toggle organization member selection
  const toggleOrgMember = (memberId: string) => {
    setSelectedOrgMembers(prev => {
      const newMembers = prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId];

      // Save preference
      if (profileData.user_id) {
        saveUserPreferences(profileData.user_id, {
          selected_event_types: selectedEventTypes,
          selected_org_members: newMembers,
          is_maximized: isMaximized
        });
      }

      return newMembers;
    });
  };

  return (
    <div className={`transition-all duration-300 ${isMaximized ? 'fixed inset-0 z-50 bg-white' : 'relative h-full'}`}>
      <Card className={`flex flex-col overflow-hidden ${isMaximized ? 'h-full rounded-none' : 'h-full pb-2 pr-2 pl-2'}`}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center">Calendar <Construction className="h-4 w-4 ml-2 text-amber-500" /></CardTitle>
            <div className="flex space-x-2 items-center">
              <Button variant="outline" size="sm" onClick={goToPreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={goToCurrentWeek}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={goToNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={toggleMaximize}>
                {isMaximized ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          <div className="text-sm text-muted-foreground mt-1">
            {format(currentWeekStart, 'MMMM d')} - {format(addDays(currentWeekStart, 4), 'MMMM d, yyyy')}
          </div>
        </CardHeader>
      <CardContent className="flex-grow overflow-hidden p-0 max-h-[calc(100%-60px)]" style={{ height: 'calc(100% - 60px)' }}>
        <div className={`flex h-full ${isMaximized ? '' : 'border-t border-l rounded-md'}`}>
          {/* Sidebar - Only visible when maximized */}
          {isMaximized && (
            <div className="w-64 overflow-y-auto p-4 flex flex-col space-y-6">
              {/* Mini Month Calendar */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Calendar</h3>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => navigateMonth('prev')}>
                      <ChevronLeft className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => navigateMonth('next')}>
                      <ChevronRight className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div className="text-xs font-medium text-center">
                  {format(currentMonth, 'MMMM yyyy')}
                </div>
                <div className="grid grid-cols-7 gap-1">
                  {/* Weekday headers - Mon to Sun */}
                  {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
                    <div key={`header-${index}`} className="text-xs text-center text-muted-foreground">
                      {day}
                    </div>
                  ))}

                  {/* Calendar days */}
                  {(() => {
                    const days = getDaysForMonth(currentMonth);
                    return days.map((day, index) => {
                      // Check if this day is in the same week row as the current week
                      const isInCurrentWeekRow = day && isInSameWeekRow(day);
                      // Check if this specific day is in the current week
                      const isCurrentDay = day && isDateInCurrentWeek(day);

                      return (
                        <div
                          key={`day-${index}`}
                          className={`text-xs text-center p-1 cursor-pointer ${day ? (
                            isInCurrentWeekRow
                              ? 'bg-gray-100' // Soft gray background for the entire week row
                              : 'hover:bg-muted'
                          ) : ''} ${isCurrentDay ? 'font-bold' : ''}`}
                          onClick={() => day && handleDateClick(day)}
                        >
                          {day ? format(day, 'd') : ''}
                        </div>
                      );
                    });
                  })()
                  }
                </div>
              </div>

              {/* Event Types Accordion */}
              <Accordion type="single" collapsible defaultValue="event-types" className="w-full">
                <AccordionItem value="event-types">
                  <AccordionTrigger className="text-sm py-2">Event Types</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {EVENT_TYPES.map((type) => (
                        <div key={type.value} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`event-type-${type.value}`}
                            checked={selectedEventTypes.includes(type.value)}
                            onChange={() => toggleEventType(type.value)}
                            className="h-4 w-4 rounded border-gray-300"
                          />
                          <label htmlFor={`event-type-${type.value}`} className="flex items-center text-sm cursor-pointer">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: type.color }}
                            />
                            {type.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              {/* Organization Members Accordion */}
              <Accordion type="single" collapsible defaultValue="org-members" className="w-full">
                <AccordionItem value="org-members">
                  <AccordionTrigger className="text-sm py-2">Firm Members</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {orgMembers.map((member) => (
                        <div key={member.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`member-${member.id}`}
                            checked={selectedOrgMembers.includes(member.id)}
                            onChange={() => toggleOrgMember(member.id)}
                            className="h-4 w-4 rounded border-gray-300"
                          />
                          <label htmlFor={`member-${member.id}`} className="text-sm cursor-pointer truncate">
                            {member.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          )}

          {/* Main Calendar Grid */}
          <div className={`${isMaximized ? 'flex-1 pr-2 pb-2 pt-2' : 'w-full'}`}>
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <div className={`grid grid-cols-[80px_repeat(5,1fr)] h-full relative calendar-grid ${isMaximized ? 'border-t border-l border-r border-b' : ''}`}>
          {/* Multi-day events layer */}
          <div className="absolute top-[40px] left-[80px] right-0 z-10 pointer-events-none">
            <div
              className="transition-all duration-300 ease-in-out"
              style={{
                height: calculateAllDayRowHeight().allDayHeight,
                minHeight: '40px',
                maxHeight: calculateAllDayRowHeight().allDayHeight,
                position: 'relative',
                overflow: 'hidden' // Contain events within the container
              }}
            >
              {/* Scrollable container for multi-day events */}
              <div style={{
                height: '100%',
                overflowY: 'auto',
                position: 'relative'
              }}>
              {/* Render multi-day events */}
              {(() => {
                const multiDayEvents = getMultiDayEvents();

                // Group events by their overlapping time periods
                const eventGroups: CalendarEvent[][] = [];

                // Sort events by start date (earliest first)
                const sortedEvents = [...multiDayEvents].sort((a, b) => {
                  const aStart = parseISO(a.date);
                  const bStart = parseISO(b.date);
                  return aStart.getTime() - bStart.getTime();
                });

                // Group overlapping events
                sortedEvents.forEach(event => {
                  const eventStart = parseISO(event.date);
                  const eventEnd = parseISO(event.end_date || event.date);

                  // Find a group where this event doesn't overlap with any event
                  const groupIndex = eventGroups.findIndex(group => {
                    return group.every(existingEvent => {
                      const existingStart = parseISO(existingEvent.date);
                      const existingEnd = parseISO(existingEvent.end_date || existingEvent.date);

                      // Check if they don't overlap
                      return eventEnd < existingStart || eventStart > existingEnd;
                    });
                  });

                  if (groupIndex >= 0) {
                    // Add to existing group
                    eventGroups[groupIndex].push(event);
                  } else {
                    // Create a new group
                    eventGroups.push([event]);
                  }
                });

                // Sort groups by earliest start date
                eventGroups.sort((a, b) => {
                  const aEarliestStart = Math.min(...a.map(e => parseISO(e.date).getTime()));
                  const bEarliestStart = Math.min(...b.map(e => parseISO(e.date).getTime()));
                  return aEarliestStart - bEarliestStart;
                });

                // Render each group of events
                return eventGroups.map((group, groupIndex) => {
                  return group.map((event, eventIndex) => {
                    // Calculate the event's position in the current week
                    const eventStart = parseISO(event.date);
                    const eventEnd = parseISO(event.end_date || event.date);

                    // Determine if the event starts before the current week
                    const startsBeforeWeek = eventStart < currentWeekStart;

                    // Determine if the event ends after the current week
                    const endsAfterWeek = eventEnd > addDays(currentWeekStart, 4);

                    // Calculate the start day index (0-4 for Mon-Fri)
                    const startDayIndex = startsBeforeWeek ? 0 :
                      Math.min(4, differenceInDays(eventStart, currentWeekStart));

                    // Calculate the end day index (0-4 for Mon-Fri)
                    const endDayIndex = endsAfterWeek ? 4 :
                      Math.min(4, differenceInDays(eventEnd, currentWeekStart));

                    // Calculate width as percentage of the week
                    const width = ((endDayIndex - startDayIndex + 1) / 5) * 100;

                    // Calculate left position as percentage of the week
                    const left = (startDayIndex / 5) * 100;

                    // Calculate top position based on group index (stacking)
                    const rowHeight = 24; // Height in pixels for each event row
                    const top = 4 + (groupIndex * rowHeight); // 4px initial padding + stacking

                    return (
                      <CalendarEventHoverCard key={`multi-day-event-${event.id}`} event={event}>
                        <div
                          className="absolute pointer-events-auto text-xs p-1 rounded cursor-pointer truncate shadow-sm flex items-center"
                          style={{
                            backgroundColor: `${event.color}20`, // 20% opacity
                            borderLeftWidth: '2px',
                            borderLeftColor: event.color,
                            borderRadius: startsBeforeWeek ? '0 4px 4px 0' : endsAfterWeek ? '4px 0 0 4px' : '4px',
                            left: `${left}%`,
                            width: `${width * 0.9}%`, // 90% of the calculated width
                            top: `${top}px`,
                            height: '20px', // Fixed height for consistency
                            zIndex: 20 + groupIndex // Ensure proper stacking
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEventClick(event);
                          }}
                        >
                          {startsBeforeWeek && <span className="mr-1">◀</span>}
                          <span className="truncate">{event.title}</span>
                          {endsAfterWeek && <span className="ml-1">▶</span>}
                        </div>
                      </CalendarEventHoverCard>
                    );
                  });
                }).flat();
              })()}
              </div>
            </div>
          </div>
          {/* Time column */}
          <div className="border-r">
            <div className="border-b" style={{ height: '40px' }}></div> {/* Empty cell for header row */}
            <div
              className="border-b flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-all duration-300 ease-in-out"
              style={{
                height: calculateAllDayRowHeight().allDayHeight,
                minHeight: '40px',
                maxHeight: calculateAllDayRowHeight().allDayHeight
              }}
              onMouseEnter={() => setIsAllDayRowExpanded(true)}
              ref={allDayRowRef}
            >
              <span className="text-xs font-medium text-gray-500 flex items-center">
                All Day
                {!isAllDayRowExpanded && (
                  <svg className="ml-1" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                )}
                {isAllDayRowExpanded && (
                  <svg className="ml-1" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="18 15 12 9 6 15"></polyline>
                  </svg>
                )}
              </span>
            </div>
            {/* Time slots in the left column */}
            <div className="flex flex-col h-full" style={{ height: calculateAllDayRowHeight().timeSlotHeight }}>
              {timeSlots.map((time, index) => (
                <div
                  key={time}
                  className="border-b flex items-center justify-center"
                  style={{ height: 'calc(100% / 10)' }} // 10 time slots
                >
                  <span className="text-xs text-muted-foreground">{time}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Days columns */}
          {weekDays.map((day, index) => (
            <div key={day.dayName} className="border-r relative">
              {/* Day header */}
              <div className="border-b flex flex-col items-center justify-center" style={{ height: '40px' }}>
                <div className="font-medium">{day.dayName}</div>
                <div className="text-sm">{day.dayNumber}</div>
              </div>

              {/* Tasks and Events without specific time (All Day row) */}
              <div
                className="border-b p-1 bg-gray-50 overflow-y-auto transition-all duration-300 ease-in-out"
                style={{
                  minHeight: '40px',
                  height: calculateAllDayRowHeight().allDayHeight,
                  maxHeight: calculateAllDayRowHeight().allDayHeight,
                  overflowY: 'auto'
                }}
                onClick={() => handleCellClick(day.date)}
                onMouseEnter={() => setIsAllDayRowExpanded(true)}
              >
                {/* Container for all-day content - 90% width */}
                <div style={{ width: '90%' }}>
                  {/* Expandable indicator */}
                  {(() => {
                    const dayEvents = getEventsForDay(day.date);
                    const dayTasks = getTasksForDay(day.date);
                    const hasMultiDayEvents = getMultiDayEvents().some(event => isDayInEventRange(day.date, event));
                    const totalItems = dayEvents.length + dayTasks.length + (hasMultiDayEvents ? 1 : 0);

                    // Only show indicator if there are multiple items and we're not already expanded
                    if (totalItems > 1 && !isAllDayRowExpanded) {
                      return (
                        <div className="text-xs text-gray-500 mb-1 italic flex items-center">
                          <span className="mr-1">More events</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="6 9 12 15 18 9"></polyline>
                          </svg>
                        </div>
                      );
                    }
                    return null;
                  })()}
                  {/* All-day tasks */}
                  {(() => {
                    // Get all tasks for this day
                    const dayTasks = getTasksForDay(day.date);

                    // Sort tasks by importance (high, medium, low) and then by title
                    const sortedTasks = [...dayTasks].sort((a, b) => {
                      // First sort by importance
                      const importanceOrder = { high: 0, medium: 1, low: 2 };
                      const importanceA = importanceOrder[a.importance as keyof typeof importanceOrder] || 3;
                      const importanceB = importanceOrder[b.importance as keyof typeof importanceOrder] || 3;

                      if (importanceA !== importanceB) {
                        return importanceA - importanceB;
                      }

                      // Then sort by title
                      return a.title.localeCompare(b.title);
                    });

                    return sortedTasks.map((task) => (
                      <div
                        key={`task-${task.id}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTaskClick(task);
                        }}
                        className={`
                          text-xs p-1 mb-1 rounded cursor-pointer truncate shadow-sm
                          ${task.importance === 'high' ? 'bg-red-100 hover:bg-red-200 border-l-2 border-red-500' :
                            task.importance === 'medium' ? 'bg-yellow-100 hover:bg-yellow-200 border-l-2 border-yellow-500' :
                            'bg-blue-100 hover:bg-blue-200 border-l-2 border-blue-500'}
                        `}
                        style={{
                          display: 'block', // Ensure block display for stacking
                          width: '100%', // Full width
                          marginBottom: '4px', // Consistent spacing
                        }}
                      >
                        {task.title}
                      </div>
                    ));
                  })()}

                  {/* All-day events - only single-day events */}
                  {(() => {
                    // Get all single-day events for this day
                    const dayEvents = getEventsForDay(day.date);

                    // Sort events by title for consistent display
                    const sortedEvents = [...dayEvents].sort((a, b) => {
                      return a.title.localeCompare(b.title);
                    });

                    return sortedEvents.map((event, index) => (
                      <CalendarEventHoverCard key={`event-${event.id}`} event={event}>
                        <div
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEventClick(event);
                          }}
                          className="text-xs p-1 mb-1 cursor-pointer truncate shadow-sm"
                          style={{
                            backgroundColor: `${event.color}20`, // 20% opacity
                            borderLeftWidth: '2px',
                            borderLeftColor: event.color,
                            borderRadius: '4px',
                            display: 'block', // Ensure block display for stacking
                            width: '100%', // Full width
                            marginBottom: '4px', // Consistent spacing
                          }}
                        >
                          <span className="truncate">{event.title}</span>
                        </div>
                      </CalendarEventHoverCard>
                    ));
                  })()}
                </div>
              </div>

              {/* Time slots container */}
              <div className="relative transition-all duration-300 ease-in-out" style={{
                height: calculateAllDayRowHeight().timeSlotHeight
              }}>
                {/* Time slots */}
                <div className="flex flex-col h-full">
                  {timeSlots.map((timeSlot, slotIndex) => {
                    const tasksInSlot = getTasksForTimeSlot(day.date, timeSlot);
                    const currentSlotHour = timeSlotToHour(timeSlot);

                    return (
                      <div
                        key={`${day.dayName}-${timeSlot}`}
                        className="border-b p-1 overflow-hidden text-ellipsis relative"
                        style={{ height: 'calc(100% / 10)' }} // 10 time slots, matching the left column
                        onClick={() => handleCellClick(day.date, timeSlot)}
                      >
                        {/* Container for time slot content - 90% width */}
                        <div style={{ width: '90%' }}>
                          {/* Tasks in this time slot */}
                          {tasksInSlot.map((task) => (
                            <div
                              key={`task-${task.id}`}
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent event from bubbling up to the cell
                                handleTaskClick(task);
                              }}
                              className={`
                                text-xs p-0.5 mb-0.5 rounded cursor-pointer truncate shadow-sm text-[10px] leading-tight
                                ${task.importance === 'high' ? 'bg-red-100 hover:bg-red-200 border-l-2 border-red-500' :
                                  task.importance === 'medium' ? 'bg-yellow-100 hover:bg-yellow-200 border-l-2 border-yellow-500' :
                                  'bg-blue-100 hover:bg-blue-200 border-l-2 border-blue-500'}
                              `}

                          >
                            {task.title}
                          </div>
                        ))}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Render events that span multiple time slots */}
                <div className="absolute inset-0 pointer-events-none" style={{ width: '90%' }}>
                  {getTimeEventsForDay(day.date).map((event, eventIndex, eventsArray) => {
                    // Calculate event position and height
                    const startHour = timeStringToHour(event.start_time);
                    const endHour = timeStringToHour(event.end_time);

                    // Find overlapping events
                    const overlappingEvents = eventsArray.filter(e => {
                      const eStartHour = timeStringToHour(e.start_time);
                      const eEndHour = timeStringToHour(e.end_time);

                      return (
                        (eStartHour < endHour && eEndHour > startHour) || // Overlaps
                        (eStartHour === startHour && eEndHour === endHour) // Same time range
                      );
                    });

                    // Find position in overlapping group
                    const position = overlappingEvents.findIndex(e => e.id === event.id);
                    const totalOverlapping = overlappingEvents.length;

                    // Calculate width and left position based on overlapping events
                    const width = 100 / totalOverlapping;
                    const left = position * width;

                    // Calculate top and height based on time
                    const firstTimeSlotHour = timeSlotToHour(timeSlots[0]);
                    const lastTimeSlotHour = timeSlotToHour(timeSlots[timeSlots.length - 1]) + 1; // Add 1 hour for the end of the last slot
                    const totalHours = lastTimeSlotHour - firstTimeSlotHour;
                    const slotHeight = 100 / totalHours;

                    // Calculate position relative to the first time slot
                    const top = Math.max(0, (startHour - firstTimeSlotHour) * slotHeight);
                    const height = Math.min(100 - top, (endHour - startHour) * slotHeight);

                    // Ensure minimum height for visibility
                    const minHeight = 5; // 5% minimum height

                    // Only render if the event has a positive height and is within the visible time range
                    if (height <= 0 || startHour >= lastTimeSlotHour || endHour <= firstTimeSlotHour) {
                      return null;
                    }

                    return (
                      <CalendarEventHoverCard key={`event-${event.id}`} event={event}>
                        <div
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEventClick(event);
                          }}
                          className="absolute rounded cursor-pointer shadow-sm text-[10px] leading-tight overflow-hidden pointer-events-auto hover:z-10 transition-all duration-100 hover:shadow-md"
                          style={{
                            backgroundColor: `${event.color}20`, // 20% opacity
                            borderLeftWidth: '2px',
                            borderLeftColor: event.color,
                            top: `${top}%`,
                            height: `${Math.max(minHeight, height)}%`,
                            left: `${left}%`,
                            width: `${width}%`,
                            padding: '2px 4px',
                            zIndex: 5 + eventIndex, // Ensure proper stacking
                          }}
                        >
                          <div className="font-medium truncate">{event.title}</div>
                          {height > 10 && (
                            <div className="text-[8px] truncate">
                              {format(parseISO(`${event.date}T${event.start_time}`), 'h:mm a')} -
                              {format(parseISO(`${event.date}T${event.end_time}`), 'h:mm a')}
                            </div>
                          )}
                        </div>
                      </CalendarEventHoverCard>
                    );
                  })}
                </div>
              </div>
            </div>
          ))}
        </div> 
      )}
      </div> 
    </div> 
  </CardContent> 

      {/* View Task Modal */}
      {isTaskModalOpen && selectedTask && (
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={handleTaskModalClose}
          task={selectedTask}
          onSave={async () => {
            // Refresh tasks after saving
            if (onTaskUpdate) {
              onTaskUpdate();
            }
          }}
          readonly={true}
        />
      )}

      {/* Calendar Event Modal */}
      <CalendarEventModal
        isOpen={isEventModalOpen}
        onClose={() => {
          setIsEventModalOpen(false);
          setSelectedEvent(null);
          setSelectedDate(undefined);
          setSelectedTimeSlot(undefined);
        }}
        event={selectedEvent}
        selectedDate={selectedDate}
        selectedTimeSlot={selectedTimeSlot}
        onSave={async () => {
          await fetchEvents();
        }}
        onDelete={async () => {
          await fetchEvents();
        }}
      />
    </Card>
  </div>
  );
}
