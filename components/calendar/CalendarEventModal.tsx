'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/expandingDialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { createClient } from "@/utils/supabase/client";
import { CalendarIcon, Clock, Trash2, CheckCircle2, XCircle, AlertCircle } from "lucide-react";
import { format, addHours, setHours, setMinutes, parseISO, addDays } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { DateRange } from "react-day-picker";

interface CalendarEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event?: CalendarEvent | null;
  onSave: () => Promise<void>;
  selectedDate?: Date;
  selectedTimeSlot?: string;
  onDelete?: () => Promise<void>;
}

interface Household {
  id: number;
  householdName: string;
}

interface User {
  id: string;
  name: string;
  email: string;
}

export interface CalendarEvent {
  id: number;
  title: string;
  start_time: string;
  end_time: string;
  date: string;
  end_date?: string;
  is_all_day?: boolean;
  household_id?: number;
  household_name?: string;
  event_type: string;
  details?: string;
  location?: string;
  members?: string[];
  required_members?: string[];
  optional_members?: string[];
  household_members?: string[];
  external_emails?: string[];
  responses?: Record<string, string>;
  color?: string;
}

// Event type options with their colors and field configurations
export const EVENT_TYPES = [
  {
    value: "meeting",
    label: "Meeting",
    color: "#4f46e5", // Indigo
    fields: {
      location: true,
      attendees: true,
      details: true,
      household: true,
      sendEmails: true // Only meetings should send email invites
    }
  },
  {
    value: "household_event",
    label: "Household Event",
    color: "#0ea5e9", // Sky
    fields: {
      location: true,
      attendees: false,
      details: true,
      household: true,
      sendEmails: false
    }
  },
  {
    value: "internal_task",
    label: "Internal Task",
    color: "#10b981", // Emerald
    fields: {
      location: false,
      attendees: true,
      details: true,
      household: false,
      sendEmails: false
    }
  },
  {
    value: "client_call",
    label: "Client Call",
    color: "#f59e0b", // Amber
    fields: {
      location: false,
      attendees: true,
      details: true,
      household: true,
      sendEmails: false
    }
  },
  {
    value: "deadline",
    label: "Deadline",
    color: "#ef4444", // Red
    fields: {
      location: false,
      attendees: false,
      details: true,
      household: true,
      sendEmails: false
    }
  },
  {
    value: "personal",
    label: "Personal",
    color: "#8b5cf6", // Violet
    fields: {
      location: true,
      attendees: false,
      details: true,
      household: false,
      sendEmails: false
    }
  },
  {
    value: "other",
    label: "Other",
    color: "#6b7280", // Gray
    fields: {
      location: true,
      attendees: true,
      details: true,
      household: true,
      sendEmails: false // Only meeting events should send emails
    }
  },
];

// Time options for the time select
const TIME_OPTIONS = Array.from({ length: 24 * 4 }).map((_, i) => {
  const hour = Math.floor(i / 4);
  const minute = (i % 4) * 15;
  const period = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return {
    value: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
    label: `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`
  };
});

export default function CalendarEventModal({
  isOpen,
  onClose,
  event,
  onSave,
  selectedDate,
  selectedTimeSlot,
  onDelete
}: CalendarEventModalProps) {
  const [title, setTitle] = useState(event?.title || "");
  // Initialize date - ensure we preserve the exact date selected
  const [date, setDate] = useState<Date | undefined>(() => {
    if (event?.date) {
      // For existing events, use the stored date
      return parseISO(event.date);
    } else if (selectedDate) {
      // For new events, use the selected date from the calendar
      // Create a new date to avoid reference issues
      const newDate = new Date(selectedDate);
      console.log('Using selected date:', format(newDate, 'yyyy-MM-dd')); // Debug log
      return newDate;
    } else {
      return new Date();
    }
  });

  // Initialize date range for multi-day events
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    if (event?.date && event?.end_date) {
      return {
        from: parseISO(event.date),
        to: parseISO(event.end_date)
      };
    } else if (date) {
      return {
        from: date,
        to: date
      };
    }
    return undefined;
  });

  // Initialize all-day event state
  const [isAllDay, setIsAllDay] = useState(event?.is_all_day || false);

  // Parse the selected time slot if provided
  const getInitialTimeFromSlot = (timeSlot?: string): string => {
    if (!timeSlot) return "09:00";

    const match = timeSlot.match(/(\d+):00\s*(AM|PM)/i);
    if (match) {
      let hour = parseInt(match[1]);
      const period = match[2].toUpperCase();

      if (period === "PM" && hour < 12) hour += 12;
      if (period === "AM" && hour === 12) hour = 0;

      return `${hour.toString().padStart(2, '0')}:00`;
    }

    return "09:00";
  };

  const [startTime, setStartTime] = useState(
    event?.start_time || getInitialTimeFromSlot(selectedTimeSlot)
  );

  const [endTime, setEndTime] = useState(
    event?.end_time || (() => {
      const start = getInitialTimeFromSlot(selectedTimeSlot);
      const [hour, minute] = start.split(':').map(Number);
      const endHour = (hour + 1) % 24;
      return `${endHour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    })()
  );

  const [selectedHouseholdId, setSelectedHouseholdId] = useState<string>(
    event?.household_id?.toString() || "none"
  );
  const [eventType, setEventType] = useState(event?.event_type || "meeting");
  const [details, setDetails] = useState(event?.details || "");
  const [location, setLocation] = useState(event?.location || "");
  const [households, setHouseholds] = useState<Household[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [requiredMembers, setRequiredMembers] = useState<string[]>(event?.required_members || []);
  const [optionalMembers, setOptionalMembers] = useState<string[]>(event?.optional_members || []);
  const [householdMembers, setHouseholdMembers] = useState<string[]>(event?.household_members || []);
  const [householdMembersList, setHouseholdMembersList] = useState<{id: string, name: string, email: string}[]>([]);
  const [externalEmails, setExternalEmails] = useState<string[]>(event?.external_emails || []);
  const [newExternalEmail, setNewExternalEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});

  // Helper function to get the current event type configuration
  const getCurrentEventTypeConfig = () => {
    const eventTypeConfig = EVENT_TYPES.find(type => type.value === eventType);
    return eventTypeConfig?.fields || {
      location: true,
      attendees: true,
      details: true,
      household: true
    };
  };

  const supabase = createClient();

  useEffect(() => {
    if (isOpen) {
      fetchHouseholds();
      fetchUsers();
      fetchUserProfile();
    }
  }, [isOpen]);

  // Fetch user profile to get org_id
  const fetchUserProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();

        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  // Fetch household members when a household is selected
  useEffect(() => {
    if (selectedHouseholdId && selectedHouseholdId !== "none") {
      fetchHouseholdMembers(parseInt(selectedHouseholdId));
    } else {
      setHouseholdMembersList([]);
    }
  }, [selectedHouseholdId]);

  // Reset fields when event type changes
  useEffect(() => {
    const config = getCurrentEventTypeConfig();

    // Reset attendees if not needed for this event type
    if (!config.attendees) {
      setRequiredMembers([]);
      setOptionalMembers([]);
      setHouseholdMembers([]);
      setExternalEmails([]);
    }

    // Reset location if not needed for this event type
    if (!config.location) {
      setLocation("");
    }

    // Reset household if not needed for this event type
    if (!config.household) {
      setSelectedHouseholdId("none");
    }

    // For household events, make sure a household is selected
    if (eventType === "household_event" && selectedHouseholdId === "none" && households.length > 0) {
      setSelectedHouseholdId(households[0].id.toString());
    }
  }, [eventType, selectedHouseholdId, households]);

  useEffect(() => {
    if (event) {
      setTitle(event.title);
      setDate(event.date ? new Date(event.date) : undefined);
      setIsAllDay(event.is_all_day || false);

      if (event.date && event.end_date) {
        setDateRange({
          from: parseISO(event.date),
          to: parseISO(event.end_date)
        });
      } else if (event.date) {
        setDateRange({
          from: parseISO(event.date),
          to: parseISO(event.date)
        });
      }

      setStartTime(event.start_time);
      setEndTime(event.end_time);
      setSelectedHouseholdId(event.household_id?.toString() || "none");
      setEventType(event.event_type);
      setDetails(event.details || "");
      setLocation(event.location || "");
      setRequiredMembers(event.required_members || []);
      setOptionalMembers(event.optional_members || []);
      setHouseholdMembers(event.household_members || []);
      setExternalEmails(event.external_emails || []);
      setNewExternalEmail("");
    } else {
      setTitle("");
      setDate(selectedDate || new Date());
      setIsAllDay(false);

      if (selectedDate) {
        setDateRange({
          from: selectedDate,
          to: selectedDate
        });
      } else {
        const today = new Date();
        setDateRange({
          from: today,
          to: today
        });
      }

      setStartTime(getInitialTimeFromSlot(selectedTimeSlot));
      const start = getInitialTimeFromSlot(selectedTimeSlot);
      const [hour, minute] = start.split(':').map(Number);
      const endHour = (hour + 1) % 24;
      setEndTime(`${endHour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
      setSelectedHouseholdId("none");
      setEventType("meeting");
      setDetails("");
      setLocation("");
      setRequiredMembers([]);
      setOptionalMembers([]);
      setHouseholdMembers([]);
      setExternalEmails([]);
      setNewExternalEmail("");
    }
  }, [event, isOpen, selectedDate, selectedTimeSlot]);

  const fetchHouseholds = async () => {
    try {
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName');

      if (error) throw error;
      setHouseholds(data || []);
    } catch (error) {
      console.error('Error fetching households:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      // Get the current user's organization ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('org_id')
        .eq('user_id', user.id)
        .single();

      if (profileError) throw profileError;
      if (!profileData?.org_id) throw new Error('Organization ID not found');

      // Get all users in the same organization
      const { data, error } = await supabase
        .from('profiles')
        .select('user_id, name, email')
        .eq('org_id', profileData.org_id);

      if (error) throw error;
      setUsers((data || []).map(({ user_id, name, email }) => ({ id: user_id, name, email })));
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const fetchHouseholdMembers = async (householdId: number) => {
    try {
      const { data, error } = await supabase
        .from('households')
        .select('members')
        .eq('id', householdId)
        .single();

      if (error) throw error;

      if (data?.members) {
        const members = [];

        // Add first member if exists
        if (data.members.name1 && data.members.email1) {
          members.push({
            id: `household-${householdId}-1`,
            name: data.members.name1,
            email: data.members.email1
          });
        }

        // Add second member if exists
        if (data.members.name2 && data.members.email2) {
          members.push({
            id: `household-${householdId}-2`,
            name: data.members.name2,
            email: data.members.email2
          });
        }

        setHouseholdMembersList(members);
      } else {
        setHouseholdMembersList([]);
      }
    } catch (error) {
      console.error('Error fetching household members:', error);
      setHouseholdMembersList([]);
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      toast({
        title: "Error",
        description: "Event title is required",
        variant: "destructive",
      });
      return;
    }

    // For household events, a household must be selected
    if (eventType === "household_event" && (selectedHouseholdId === "none" || !selectedHouseholdId)) {
      toast({
        title: "Error",
        description: "A household must be selected for household events",
        variant: "destructive",
      });
      return;
    }

    if (isAllDay && !dateRange?.from) {
      toast({
        title: "Error",
        description: "Date range is required for all-day events",
        variant: "destructive",
      });
      return;
    } else if (!isAllDay && !date) {
      toast({
        title: "Error",
        description: "Date is required",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      let dateStr;
      let endDateStr;

      if (isAllDay && dateRange && dateRange.from) {
        // For all-day events, use the date range
        dateStr = format(dateRange.from, 'yyyy-MM-dd');
        endDateStr = dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : dateStr;
        console.log('Saving all-day event with date range:', dateStr, 'to', endDateStr);
      } else if (date) {
        // For regular events, use the single date
        dateStr = format(date, 'yyyy-MM-dd');
        console.log('Saving event with date:', dateStr);
      } else {
        dateStr = format(new Date(), 'yyyy-MM-dd');
      }

      // Filter out any null or empty values from the arrays
      const validRequiredMembers = requiredMembers.filter(id => id && id.trim() !== '');
      const validOptionalMembers = optionalMembers.filter(id => id && id.trim() !== '');
      const validHouseholdMembers = householdMembers.filter(id => id && id.trim() !== '');
      const validExternalEmails = externalEmails.filter(email => email && email.trim() !== '');

      const eventData = {
        title,
        date: dateStr,
        end_date: endDateStr || null,
        is_all_day: isAllDay,
        start_time: isAllDay ? "00:00" : startTime,
        end_time: isAllDay ? "23:59" : endTime,
        household_id: selectedHouseholdId && selectedHouseholdId !== "none" ? parseInt(selectedHouseholdId) : null,
        event_type: eventType,
        details,
        location,
        // For backward compatibility
        members: [...validRequiredMembers, ...validOptionalMembers],
        // Use the required_members and optional_members fields from the schema
        required_members: validRequiredMembers,
        optional_members: validOptionalMembers,
        household_members: validHouseholdMembers,
        external_emails: validExternalEmails,
        color: EVENT_TYPES.find(type => type.value === eventType)?.color,
        // Add org_id from profile data
        org_id: profileData.org_id,
        user_id: profileData.user_id
      };

      if (event?.id) {
        // Update existing event
        const { error } = await supabase
          .from('calendar_events')
          .update(eventData)
          .eq('id', event.id);

        if (error) throw error;
      } else {
        // Create new event
        const { error } = await supabase
          .from('calendar_events')
          .insert(eventData);

        if (error) throw error;
      }

      await onSave();

      // Get the event ID if it's a new event
      let eventId = event?.id;
      if (!eventId) {
        // For new events, get the ID from the insert response
        const { data: newEvent, error: insertError } = await supabase
          .from('calendar_events')
          .select('id')
          .order('created_at', { ascending: false })
          .limit(1);

        if (insertError) {
          console.error('Error getting new event ID:', insertError);
        } else if (newEvent && newEvent.length > 0) {
          eventId = newEvent[0].id;
        }
      }

      // Send calendar invites only for event types that support emails and if there are attendees
      const eventTypeConfig = EVENT_TYPES.find(type => type.value === eventType);
      const canSendEmails = eventTypeConfig?.fields.sendEmails || false;

      const hasAttendees = (
        validRequiredMembers.length > 0 ||
        validOptionalMembers.length > 0 ||
        validHouseholdMembers.length > 0 ||
        validExternalEmails.length > 0
      );

      if (eventId && canSendEmails && hasAttendees) {
        try {
          const response = await fetch('/api/calendar/invite', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              eventId,
              title,
              description: details,
              location,
              startDate: dateStr,
              endDate: endDateStr || dateStr,
              startTime,
              endTime,
              isAllDay,
              requiredMembers: validRequiredMembers,
              optionalMembers: validOptionalMembers,
              householdMembers: validHouseholdMembers,
              externalEmails: validExternalEmails,
            }),
          });

          if (response.ok) {
            toast({
              title: "Success",
              description: `Event ${event?.id ? 'updated' : 'created'} and invites sent successfully`,
            });
          } else {
            console.error('Error sending calendar invites:', await response.text());
            toast({
              title: "Warning",
              description: `Event ${event?.id ? 'updated' : 'created'} but failed to send invites`,
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error('Error sending calendar invites:', error);
          toast({
            title: "Warning",
            description: `Event ${event?.id ? 'updated' : 'created'} but failed to send invites`,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Success",
          description: `Event ${event?.id ? 'updated' : 'created'} successfully`,
        });
      }

      onClose();
    } catch (error) {
      console.error('Error saving event:', error);
      toast({
        title: "Error",
        description: `Failed to ${event?.id ? 'update' : 'create'} event`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to validate and add an external email
  const addExternalEmail = () => {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (newExternalEmail.trim() !== "" && emailRegex.test(newExternalEmail)) {
      // Add the email if it's not already in the list
      if (!externalEmails.includes(newExternalEmail)) {
        setExternalEmails([...externalEmails, newExternalEmail]);
        setNewExternalEmail(""); // Clear the input
      } else {
        toast({
          title: "Duplicate Email",
          description: "This email is already in the list",
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!event?.id) return;

    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('calendar_events')
        .delete()
        .eq('id', event.id);

      if (error) throw error;

      if (onDelete) {
        await onDelete();
      } else {
        await onSave(); // Fallback to onSave if onDelete is not provided
      }

      onClose();
      toast({
        title: "Success",
        description: "Event deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting event:', error);
      toast({
        title: "Error",
        description: "Failed to delete event",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{event?.id ? "Edit Event" : "Create New Event"}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="flex-grow pr-4">
          <div className="space-y-4 py-2">
            {/* Event Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Event Title <span className="text-red-500">*</span></Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter event title"
              />
            </div>

            {/* Date Picker with All Day Switch */}
            {!isAllDay && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="date">Date <span className="text-red-500">*</span></Label>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="isAllDay" className="text-sm">All Day</Label>
                    <Switch
                      id="isAllDay"
                      checked={isAllDay}
                      onCheckedChange={setIsAllDay}
                    />
                  </div>
                </div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}

            {/* Date Range Picker - Show for all-day events */}
            {isAllDay && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dateRange">Date Range <span className="text-red-500">*</span></Label>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="isAllDay" className="text-sm">All Day</Label>
                    <Switch
                      id="isAllDay"
                      checked={isAllDay}
                      onCheckedChange={setIsAllDay}
                    />
                  </div>
                </div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "PPP")} - {format(dateRange.to, "PPP")}
                          </>
                        ) : (
                          format(dateRange.from, "PPP")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}

            {/* Time Range - Only show for non-all-day events */}
            {!isAllDay && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startTime">Start Time</Label>
                  <Select value={startTime} onValueChange={setStartTime}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select start time" />
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime">End Time</Label>
                  <Select value={endTime} onValueChange={setEndTime}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select end time" />
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Two-column layout for Event Type and Location */}
            <div className="grid grid-cols-2 gap-4">
              {/* Event Type */}
              <div className="space-y-2">
                <Label htmlFor="eventType">Event Type</Label>
                <Select value={eventType} onValueChange={setEventType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    {EVENT_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: type.color }}
                          />
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Location - Only show if the event type requires it */}
              {getCurrentEventTypeConfig().location && (
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    placeholder="Enter location (optional)"
                  />
                </div>
              )}
            </div>

            {/* Household - Only show if the event type requires it */}
            {getCurrentEventTypeConfig().household && (
              <div className="space-y-2">
                <Label htmlFor="household">
                  Household {eventType === "household_event" ? <span className="text-red-500">*</span> : "(Optional)"}
                </Label>
                <Select
                  value={selectedHouseholdId}
                  onValueChange={setSelectedHouseholdId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a household" />
                  </SelectTrigger>
                  <SelectContent>
                    {eventType !== "household_event" && <SelectItem value="none">None</SelectItem>}
                    {households.map((household) => (
                      <SelectItem
                        key={household.id}
                        value={household.id.toString()}
                      >
                        {household.householdName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Required Organization Members - Only show if the event type requires attendees */}
            {getCurrentEventTypeConfig().attendees && (
              <div className="space-y-2">
                <Label htmlFor="requiredMembers" className="flex items-center">
                  <span className="text-red-500 mr-1">*</span> Required Organization Members
                </Label>
                <Select
                  value={requiredMembers.length > 0 ? "selected" : "none"}
                  onValueChange={() => {}}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select required members">
                      {requiredMembers.length > 0
                        ? `${requiredMembers.length} member${requiredMembers.length > 1 ? 's' : ''} selected`
                        : "Select required members"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user, index) => (
                      <div key={`req-user-${user.id}-${index}`} className="flex items-center space-x-2 p-2">
                        <input
                          type="checkbox"
                          id={`req-user-${user.id}`}
                          checked={requiredMembers.includes(user.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setRequiredMembers([...requiredMembers, user.id]);
                              // Remove from optional if added to required
                              if (optionalMembers.includes(user.id)) {
                                setOptionalMembers(optionalMembers.filter(id => id !== user.id));
                              }
                            } else {
                              setRequiredMembers(requiredMembers.filter(id => id !== user.id));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label htmlFor={`req-user-${user.id}`} className="text-sm cursor-pointer">
                          {user.name} ({user.email})
                        </label>
                      </div>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Optional Organization Members - Only show if the event type requires attendees */}
            {getCurrentEventTypeConfig().attendees && (
              <div className="space-y-2">
                <Label htmlFor="optionalMembers">Optional Organization Members</Label>
                <Select
                  value={optionalMembers.length > 0 ? "selected" : "none"}
                  onValueChange={() => {}}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select optional members">
                      {optionalMembers.length > 0
                        ? `${optionalMembers.length} member${optionalMembers.length > 1 ? 's' : ''} selected`
                        : "Select optional members"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user, index) => (
                      <div key={`opt-user-${user.id}-${index}`} className="flex items-center space-x-2 p-2">
                        <input
                          type="checkbox"
                          id={`opt-user-${user.id}`}
                          checked={optionalMembers.includes(user.id)}
                          disabled={requiredMembers.includes(user.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setOptionalMembers([...optionalMembers, user.id]);
                            } else {
                              setOptionalMembers(optionalMembers.filter(id => id !== user.id));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50"
                        />
                        <label
                          htmlFor={`opt-user-${user.id}`}
                          className={`text-sm cursor-pointer ${requiredMembers.includes(user.id) ? 'opacity-50' : ''}`}
                        >
                          {user.name} ({user.email})
                          {requiredMembers.includes(user.id) && <span className="ml-2 text-xs text-gray-500">(Already required)</span>}
                        </label>
                      </div>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Household Members - Only show if a household is selected and attendees are enabled */}
            {getCurrentEventTypeConfig().attendees && selectedHouseholdId !== "none" && householdMembersList.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="householdMembers">Household Members</Label>
                <Select
                  value={householdMembers.length > 0 ? "selected" : "none"}
                  onValueChange={() => {}}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select household members">
                      {householdMembers.length > 0
                        ? `${householdMembers.length} member${householdMembers.length > 1 ? 's' : ''} selected`
                        : "Select household members"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {householdMembersList.map((member) => (
                      <div key={member.id} className="flex items-center space-x-2 p-2">
                        <input
                          type="checkbox"
                          id={`member-${member.id}`}
                          checked={householdMembers.includes(member.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setHouseholdMembers([...householdMembers, member.id]);
                            } else {
                              setHouseholdMembers(householdMembers.filter(id => id !== member.id));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <label htmlFor={`member-${member.id}`} className="text-sm cursor-pointer">
                          {member.name} ({member.email})
                        </label>
                      </div>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* External Email Attendees - Only show if the event type requires attendees */}
            {getCurrentEventTypeConfig().attendees && (
              <div className="space-y-2">
                <Label htmlFor="externalEmails">External Email Attendees</Label>

                {/* Input for adding new external emails */}
                <div className="flex space-x-2">
                  <Input
                    id="newExternalEmail"
                    value={newExternalEmail}
                    onChange={(e) => setNewExternalEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent form submission
                        addExternalEmail();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={addExternalEmail}
                    disabled={isLoading}
                  >
                    Add
                  </Button>
                </div>

                {/* Display list of added external emails */}
                {externalEmails.length > 0 && (
                  <div className="mt-2 space-y-2">
                    <div className="text-sm font-medium">Added Emails:</div>
                    <div className="space-y-1">
                      {externalEmails.map((email, index) => (
                        <div key={index} className="flex items-center justify-between bg-muted p-2 rounded-md">
                          <span className="text-sm">{email}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setExternalEmails(externalEmails.filter((_, i) => i !== index));
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Event Details */}
            <div className="space-y-2">
              <Label htmlFor="details">Event Details</Label>
              <Textarea
                id="details"
                value={details}
                onChange={(e) => setDetails(e.target.value)}
                placeholder="Enter event details"
                rows={4}
              />
            </div>

            {/* Attendee Responses - Only show for existing events that support email invites */}
            {event?.id && EVENT_TYPES.find(type => type.value === eventType)?.fields.sendEmails && (
              <div className="space-y-2 mt-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">Attendee Responses</Label>
                  <div className="flex items-center space-x-2">
                    {event.responses && (
                      <div className="flex items-center space-x-1">
                        <span className="inline-flex items-center text-xs text-green-600">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          {Object.values(event.responses).filter(r => r === 'yes').length}
                        </span>
                        <span className="inline-flex items-center text-xs text-amber-600">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {Object.values(event.responses).filter(r => r === 'maybe').length}
                        </span>
                        <span className="inline-flex items-center text-xs text-red-600">
                          <XCircle className="h-3 w-3 mr-1" />
                          {Object.values(event.responses).filter(r => r === 'no').length}
                        </span>
                      </div>
                    )}
                    {event.responses && Object.keys(event.responses).length > 0 && (
                      <span className="text-xs text-gray-500">
                        {Object.keys(event.responses).length} {Object.keys(event.responses).length === 1 ? 'response' : 'responses'}
                      </span>
                    )}
                  </div>
                </div>
                <div className="bg-gray-50 rounded-md p-3 max-h-[200px] overflow-y-auto">
                  <div className="grid grid-cols-3 gap-2 text-sm font-medium text-gray-500 mb-2">
                    <div>Attendee</div>
                    <div>Response</div>
                    <div>Status</div>
                  </div>
                  <div className="space-y-2">
                    {/* Get all attendees from required, optional, and household members */}
                    {[
                      // Required organization members
                      ...requiredMembers.map(id => {
                        const member = users.find(u => u.id === id);
                        return member ? {
                          id,
                          name: member.name,
                          email: member.email,
                          type: 'Required'
                        } : null;
                      }),

                      // Optional organization members
                      ...optionalMembers.map(id => {
                        const member = users.find(u => u.id === id);
                        return member ? {
                          id,
                          name: member.name,
                          email: member.email,
                          type: 'Optional'
                        } : null;
                      }),

                      // Household members
                      ...householdMembers.map(id => {
                        const member = householdMembersList.find(m => m.id === id);
                        return member ? {
                          id,
                          name: member.name,
                          email: member.email,
                          type: 'Required'
                        } : null;
                      }),

                      // External emails
                      ...(externalEmails || []).map(email => ({
                        id: email,
                        name: email,
                        email,
                        type: 'Required'
                      }))
                    ]
                      .filter(Boolean) // Filter out any null attendees
                      .map((attendee) => {
                        // Get the response for this attendee
                        // Make sure attendee is not null before accessing properties
                        if (!attendee) return null;

                        const response = event.responses?.[attendee.email];

                        return (
                          <div key={attendee.id} className="grid grid-cols-3 gap-2 text-sm py-1 border-t border-gray-100">
                            <div className="truncate" title={`${attendee.name} (${attendee.email})`}>
                              {attendee.name !== attendee.email ? (
                                <>
                                  {attendee.name} <span className="text-xs text-gray-500">({attendee.type})</span>
                                </>
                              ) : attendee.email}
                            </div>
                            <div>
                              {response === 'yes' && (
                                <span className="inline-flex items-center text-green-600">
                                  <CheckCircle2 className="h-3 w-3 mr-1" /> Attending
                                </span>
                              )}
                              {response === 'no' && (
                                <span className="inline-flex items-center text-red-600">
                                  <XCircle className="h-3 w-3 mr-1" /> Not attending
                                </span>
                              )}
                              {response === 'maybe' && (
                                <span className="inline-flex items-center text-amber-600">
                                  <AlertCircle className="h-3 w-3 mr-1" /> Maybe
                                </span>
                              )}
                              {!response && (
                                <span className="inline-flex items-center text-gray-500">
                                  <Clock className="h-3 w-3 mr-1" /> No response
                                </span>
                              )}
                            </div>
                            <div className="text-gray-500">
                              {response ? (
                                <span className="text-xs">Updated recently</span>
                              ) : (
                                <span className="text-xs">Awaiting response</span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
        <div className="flex justify-between space-x-2 pt-4">
          {event?.id ? (
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
              className="flex items-center"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          ) : (
            <div></div>
          )}
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline" onClick={onClose}>Cancel</Button>
            </DialogClose>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? "Saving..." : event?.id ? "Update Event" : "Create Event"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
