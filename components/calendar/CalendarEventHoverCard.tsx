'use client';

import { CalendarEvent, EVENT_TYPES } from './CalendarEventModal';
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";
import { format } from 'date-fns';
import { Clock, Users, FileText, Home } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { useEffect, useState } from 'react';

interface CalendarEventHoverCardProps {
  event: CalendarEvent;
  children: React.ReactNode;
}

interface User {
  id: string;
  name: string;
}

export default function CalendarEventHoverCard({ event, children }: CalendarEventHoverCardProps) {
  const [members, setMembers] = useState<User[]>([]);
  const supabase = createClient();

  // Get the event type color
  const eventTypeInfo = EVENT_TYPES.find(type => type.value === event.event_type) || EVENT_TYPES[0];

  useEffect(() => {
    if (event.members && event.members.length > 0) {
      fetchMembers();
    }
  }, [event.members]);

  const fetchMembers = async () => {
    if (!event.members || event.members.length === 0) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('user_id, name')
        .in('user_id', event.members);

      if (error) throw error;
      setMembers((data || []).map(({ user_id, name }) => ({ id: user_id, name })));
    } catch (error) {
      console.error('Error fetching members:', error);
    }
  };

  // Format time for display
  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent className="w-80 p-0">
        <div
          className="p-3 text-white font-medium"
          style={{ backgroundColor: eventTypeInfo.color }}
        >
          <div className="flex justify-between items-center">
            <h3 className="text-sm font-semibold">{event.title}</h3>
            <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full">
              {eventTypeInfo.label}
            </span>
          </div>
        </div>
        <div className="p-3 space-y-2">
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 mr-2 text-gray-500" />
            <span>
              {(() => {
                try {
                  if (event.is_all_day) {
                    if (event.end_date) {
                      // Multi-day all-day event
                      return (
                        <>
                          {format(new Date(event.date), 'EEE, MMM d')} - {format(new Date(event.end_date), 'EEE, MMM d')} • All day
                        </>
                      );
                    } else {
                      // Single day all-day event
                      return <>{format(new Date(event.date), 'EEE, MMM d')} • All day</>;
                    }
                  } else {
                    // Regular event with time
                    return <>{format(new Date(event.date), 'EEE, MMM d')} • {formatTime(event.start_time)} - {formatTime(event.end_time)}</>;
                  }
                } catch (error) {
                  console.warn('Error formatting event date/time:', error);
                  // Fallback display if there's an error
                  return <>{format(new Date(event.date), 'EEE, MMM d')}</>;
                }
              })()}
            </span>
          </div>

          {event.household_name && (
            <div className="flex items-center text-sm">
              <Home className="h-4 w-4 mr-2 text-gray-500" />
              <span>{event.household_name}</span>
            </div>
          )}

          {members.length > 0 && (
            <div className="flex items-start text-sm">
              <Users className="h-4 w-4 mr-2 text-gray-500 mt-0.5" />
              <div className="flex flex-col">
                {members.map(member => (
                  <span key={member.id} className="text-sm">{member.name}</span>
                ))}
              </div>
            </div>
          )}

          {event.details && (
            <div className="flex items-start text-sm">
              <FileText className="h-4 w-4 mr-2 text-gray-500 mt-0.5" />
              <div className="text-sm line-clamp-3">{event.details}</div>
            </div>
          )}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
