import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle } from "lucide-react";
import { eventTypeTooltips, inputTooltips } from "@/components/WhatIfEventTooltips";

type EventType =
  | 'recession'
  | 'death'
  | 'tpd'
  | 'trauma'
  | 'redundancy'
  | 'maternity'
  | 'inheritance';

interface WhatIfEvent {
  id: string;
  type: EventType;
  age: number;
  [key: string]: any;
}

interface WhatIfEventModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (event: WhatIfEvent) => void;
  defaultAge?: number;
  editingEvent?: WhatIfEvent;
  mainName?: string;
  partnerName?: string;
  includePartner?: boolean;
}

export function WhatIfEventModal({
  open,
  onOpenChange,
  onSave,
  defaultAge = 30,
  editingEvent,
  mainName = "Main",
  partnerName = "Partner",
  includePartner = false
}: WhatIfEventModalProps) {
  const [eventType, setEventType] = useState<EventType>(editingEvent?.type || 'recession');
  const [event, setEvent] = useState<WhatIfEvent>({
    id: editingEvent?.id || Date.now().toString(),
    type: editingEvent?.type || 'recession',
    age: editingEvent?.age || defaultAge,
    ...getDefaultValuesForType(editingEvent?.type || 'recession', editingEvent)
  });

  useEffect(() => {
    if (editingEvent) {
      setEventType(editingEvent.type);
      setEvent(editingEvent);
    } else {
      setEventType('recession');
      setEvent({
        id: Date.now().toString(),
        type: 'recession',
        age: defaultAge,
        ...getDefaultValuesForType('recession')
      });
    }
  }, [editingEvent, defaultAge, open]);

  function getDefaultValuesForType(type: EventType, existingEvent?: WhatIfEvent): Record<string, any> {
    if (existingEvent) return {};

    // Common properties for all event types
    const commonProps = {
      enabled: true, // Default to enabled
    };

    switch (type) {
      case 'recession':
        return {
          ...commonProps,
          marketLoss: 30,
          reboundType: 'average',
          reboundPeriod: 3,
        };
      case 'death':
        return {
          ...commonProps,
          person: 'main',
          insurancePayout: 0,
          investmentAllocation: 50, // Default 50% allocation to investments
          expenseReduction: 40, // Default 40% reduction in expenses after death
        };
      case 'tpd':
        return {
          ...commonProps,
          person: 'main',
          insurancePayout: 0,
          investmentAllocation: 50, // Default 50% allocation to investments
          expenseReduction: 40, // Default 40% reduction in expenses after TPD
        };
      case 'trauma':
        return {
          ...commonProps,
          person: 'main',
          insurancePayout: 0,
          investmentAllocation: 50, // Default 50% allocation to investments
          recoveryPeriod: 1, // Default 1 year effect period
          incomeReduction: 50, // Default 50% reduction in income during recovery
          expenseReduction: 20, // Default 20% reduction in expenses during recovery
        };
      case 'redundancy':
        return {
          ...commonProps,
          person: 'main',
          severancePay: 0,
          unemploymentPeriod: 6,
        };
      case 'maternity':
        return {
          ...commonProps,
          person: 'main',
          maternityLeaveMonths: 12, // Default 12 months maternity leave
          backToWorkMonths: 6, // Default 6 months back-to-work transition
          incomeReductionPercent: 20, // Default 20% of income during maternity leave (80% reduction)
          backToWorkIncomePercent: 60, // Default 60% of income during back-to-work period
        };
      case 'inheritance':
        return {
          ...commonProps,
          amount: 0,
          investPercentage: 50,
        };
      default:
        return {};
    }
  }

  const handleTypeChange = (type: EventType) => {
    setEventType(type);
    setEvent(prev => ({
      ...prev,
      type,
      ...getDefaultValuesForType(type)
    }));
  };

  const handleInputChange = (key: string, value: any) => {
    setEvent(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    onSave(event);
    onOpenChange(false);
  };

  const renderEventInputs = () => {
    switch (eventType) {
      case 'recession':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="marketLoss"
                  label="Market Loss (%)"
                  tooltipText={inputTooltips['marketLoss']}
                />
                <Input
                  id="marketLoss"
                  type="number"
                  value={event.marketLoss}
                  onChange={(e) => handleInputChange('marketLoss', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="reboundType"
                  label="Rebound Type"
                  tooltipText={inputTooltips['reboundType']}
                />
                <Select
                  value={event.reboundType}
                  onValueChange={(value) => handleInputChange('reboundType', value)}
                >
                  <SelectTrigger id="reboundType">
                    <SelectValue placeholder="Select rebound type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="good">Good</SelectItem>
                    <SelectItem value="average">Average</SelectItem>
                    <SelectItem value="bad">Bad</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="reboundPeriod"
                  label="Rebound Period (years)"
                  tooltipText={inputTooltips['reboundPeriod']}
                />
                <Input
                  id="reboundPeriod"
                  type="number"
                  min="1"
                  max="15"
                  value={event.reboundPeriod}
                  onChange={(e) => handleInputChange('reboundPeriod', Number(e.target.value))}
                />
              </div>
            </div>
          </>
        );

      case 'death':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="person"
                  label="Person"
                  tooltipText={inputTooltips['person']}
                />
                <Select
                  value={event.person}
                  onValueChange={(value) => handleInputChange('person', value)}
                >
                  <SelectTrigger id="person">
                    <SelectValue placeholder="Select person" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName}</SelectItem>
                    {includePartner && (
                      <SelectItem value="partner">{partnerName}</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Death"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="insurancePayout"
                  label="Insurance Payout ($)"
                  tooltipText={inputTooltips['insurancePayout']}
                />
                <Input
                  id="insurancePayout"
                  type="number"
                  value={event.insurancePayout}
                  onChange={(e) => handleInputChange('insurancePayout', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="investmentAllocation"
                  label="Investment Allocation (%)"
                  tooltipText={inputTooltips['investmentAllocation']}
                />
                <Input
                  id="investmentAllocation"
                  type="number"
                  min="0"
                  max="100"
                  value={event.investmentAllocation}
                  onChange={(e) => handleInputChange('investmentAllocation', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="expenseReduction"
                  label="Expense Reduction (%)"
                  tooltipText={inputTooltips['expenseReduction']}
                />
                <Input
                  id="expenseReduction"
                  type="number"
                  min="0"
                  max="100"
                  value={event.expenseReduction}
                  onChange={(e) => handleInputChange('expenseReduction', Number(e.target.value))}
                />
              </div>
            </div>
          </>
        );

      case 'tpd':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="person"
                  label="Person"
                  tooltipText={inputTooltips['person']}
                />
                <Select
                  value={event.person}
                  onValueChange={(value) => handleInputChange('person', value)}
                >
                  <SelectTrigger id="person">
                    <SelectValue placeholder="Select person" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName}</SelectItem>
                    {includePartner && (
                      <SelectItem value="partner">{partnerName}</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Disability"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="insurancePayout"
                  label="Insurance Payout ($)"
                  tooltipText={inputTooltips['tpdInsurancePayout'] || inputTooltips['insurancePayout']}
                />
                <Input
                  id="insurancePayout"
                  type="number"
                  value={event.insurancePayout}
                  onChange={(e) => handleInputChange('insurancePayout', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="investmentAllocation"
                  label="Investment Allocation (%)"
                  tooltipText={inputTooltips['tpdInvestmentAllocation'] || inputTooltips['investmentAllocation']}
                />
                <Input
                  id="investmentAllocation"
                  type="number"
                  min="0"
                  max="100"
                  value={event.investmentAllocation}
                  onChange={(e) => handleInputChange('investmentAllocation', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <LabelWithTooltip
                htmlFor="traumaExpensereduction"
                label="Expense Reduction (%)"
                tooltipText={inputTooltips['tpdExpenseReduction'] || inputTooltips['expenseReduction']}
              />
              <Input
                id="expenseReduction"
                type="number"
                min="0"
                max="100"
                value={event.expenseReduction}
                onChange={(e) => handleInputChange('expenseReduction', Number(e.target.value))}
              />
            </div>
          </>
        );

      case 'trauma':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="person"
                  label="Person"
                  tooltipText={inputTooltips['person']}
                />
                <Select
                  value={event.person}
                  onValueChange={(value) => handleInputChange('person', value)}
                >
                  <SelectTrigger id="person">
                    <SelectValue placeholder="Select person" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName}</SelectItem>
                    {includePartner && (
                      <SelectItem value="partner">{partnerName}</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Event"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="insurancePayout"
                  label="Insurance Payout ($)"
                  tooltipText={inputTooltips['traumaInsurancePayout'] || inputTooltips['insurancePayout']}
                />
                <Input
                  id="insurancePayout"
                  type="number"
                  value={event.insurancePayout}
                  onChange={(e) => handleInputChange('insurancePayout', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="investmentAllocation"
                  label="Investment Allocation (%)"
                  tooltipText={inputTooltips['traumaInvestmentAllocation'] || inputTooltips['investmentAllocation']}
                />
                <Input
                  id="investmentAllocation"
                  type="number"
                  min="0"
                  max="100"
                  value={event.investmentAllocation}
                  onChange={(e) => handleInputChange('investmentAllocation', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="recoveryPeriod"
                  label="Effect Period (years)"
                  tooltipText={inputTooltips['recoveryPeriod']}
                />
                <Input
                  id="recoveryPeriod"
                  type="number"
                  min="1"
                  value={event.recoveryPeriod}
                  onChange={(e) => handleInputChange('recoveryPeriod', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="incomeReduction"
                  label="Income Reduction (%)"
                  tooltipText={inputTooltips['incomeReduction']}
                />
                <Input
                  id="incomeReduction"
                  type="number"
                  min="0"
                  max="100"
                  value={event.incomeReduction}
                  onChange={(e) => handleInputChange('incomeReduction', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <LabelWithTooltip
                htmlFor="expenseReduction"
                label="Expense Reduction (%)"
                tooltipText={inputTooltips['expenseReduction']}
              />
              <Input
                id="expenseReduction"
                type="number"
                min="0"
                max="100"
                value={event.expenseReduction}
                onChange={(e) => handleInputChange('expenseReduction', Number(e.target.value))}
              />
            </div>
          </>
        );

      case 'redundancy':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="person"
                  label="Person"
                  tooltipText={inputTooltips['person']}
                />
                <Select
                  value={event.person}
                  onValueChange={(value) => handleInputChange('person', value)}
                >
                  <SelectTrigger id="person">
                    <SelectValue placeholder="Select person" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName}</SelectItem>
                    {includePartner && (
                      <SelectItem value="partner">{partnerName}</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Redundancy"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="severancePay"
                  label="Severance Pay ($)"
                  tooltipText={inputTooltips['severancePay']}
                />
                <Input
                  id="severancePay"
                  type="number"
                  value={event.severancePay}
                  onChange={(e) => handleInputChange('severancePay', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="unemploymentPeriod"
                  label="Unemployment Period (months)"
                  tooltipText={inputTooltips['unemploymentPeriod']}
                />
                <Input
                  id="unemploymentPeriod"
                  type="number"
                  value={event.unemploymentPeriod}
                  onChange={(e) => handleInputChange('unemploymentPeriod', Number(e.target.value))}
                />
              </div>
            </div>
          </>
        );

      case 'maternity':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="person"
                  label="Person"
                  tooltipText={inputTooltips['person']}
                />
                <Select
                  value={event.person}
                  onValueChange={(value) => handleInputChange('person', value)}
                >
                  <SelectTrigger id="person">
                    <SelectValue placeholder="Select person" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">{mainName}</SelectItem>
                    {includePartner && (
                      <SelectItem value="partner">{partnerName}</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Maternity Leave"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="maternityLeaveMonths"
                  label="Maternity Leave (months)"
                  tooltipText={inputTooltips['maternityLeaveMonths']}
                />
                <Input
                  id="maternityLeaveMonths"
                  type="number"
                  min="1"
                  value={event.maternityLeaveMonths}
                  onChange={(e) => handleInputChange('maternityLeaveMonths', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="incomeReductionPercent"
                  label="Income During Leave (%)"
                  tooltipText={inputTooltips['incomeReductionPercent']}
                />
                <Input
                  id="incomeReductionPercent"
                  type="number"
                  min="0"
                  max="100"
                  value={event.incomeReductionPercent}
                  onChange={(e) => handleInputChange('incomeReductionPercent', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="backToWorkMonths"
                  label="Back-to-Work Period (months)"
                  tooltipText={inputTooltips['backToWorkMonths']}
                />
                <Input
                  id="backToWorkMonths"
                  type="number"
                  min="0"
                  value={event.backToWorkMonths}
                  onChange={(e) => handleInputChange('backToWorkMonths', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="backToWorkIncomePercent"
                  label="Income During Back-to-Work (%)"
                  tooltipText={inputTooltips['backToWorkIncomePercent']}
                />
                <Input
                  id="backToWorkIncomePercent"
                  type="number"
                  min="0"
                  max="100"
                  value={event.backToWorkIncomePercent}
                  onChange={(e) => handleInputChange('backToWorkIncomePercent', Number(e.target.value))}
                />
              </div>
            </div>
          </>
        );

      case 'inheritance':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="age"
                  label="Age at Inheritance"
                  tooltipText={inputTooltips['age']}
                />
                <Input
                  id="age"
                  type="number"
                  value={event.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <LabelWithTooltip
                  htmlFor="amount"
                  label="Inheritance Amount ($)"
                  tooltipText={inputTooltips['amount']}
                />
                <Input
                  id="amount"
                  type="number"
                  value={event.amount}
                  onChange={(e) => handleInputChange('amount', Number(e.target.value))}
                />
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <LabelWithTooltip
                htmlFor="investPercentage"
                label="Percentage to Invest (%)"
                tooltipText={inputTooltips['investPercentage']}
              />
              <Input
                id="investPercentage"
                type="number"
                min="0"
                max="100"
                value={event.investPercentage}
                onChange={(e) => handleInputChange('investPercentage', Number(e.target.value))}
              />
            </div>
          </>
        );


      default:
        return <p>Configure event details</p>;
    }
  };

  // Helper function to render a label with tooltip
  const LabelWithTooltip = ({ htmlFor, label, tooltipText }: { htmlFor: string, label: string, tooltipText: string }) => (
    <div className="flex items-center gap-2">
      <Label htmlFor={htmlFor}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent side="right" className="max-w-xs">
            <p>{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{editingEvent ? 'Edit' : 'Add'} What-If Event</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="eventType">Event Type</Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-xs">
                    <p>{eventTypeTooltips[eventType as EventType]}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Select value={eventType} onValueChange={(value) => handleTypeChange(value as EventType)}>
              <SelectTrigger id="eventType">
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recession">Market Recession</SelectItem>
                <SelectItem value="death">Death</SelectItem>
                <SelectItem value="tpd">Total Permanent Disability</SelectItem>
                <SelectItem value="trauma">Trauma/Critical Illness</SelectItem>
                <SelectItem value="redundancy">Redundancy</SelectItem>
                <SelectItem value="maternity">Maternity/Paternity Leave</SelectItem>
                <SelectItem value="inheritance">Inheritance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {renderEventInputs()}
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>Save Event</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default WhatIfEventModal;
