import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { GripHorizontal } from 'lucide-react';

interface ResizableCardContainerProps {
  topCard: React.ReactNode;
  bottomCard: React.ReactNode;
  isTopCardExpanded: boolean;
  containerHeight: number; // Total container height in vh
  minCardHeight: number; // Minimum card height in vh
}

const ResizableCardContainer: React.FC<ResizableCardContainerProps> = ({
  topCard,
  bottomCard,
  isTopCardExpanded,
  containerHeight = 100,
  minCardHeight = 20,
}) => {
  // Default split is 50/50 when not expanded
  const [splitRatio, setSplitRatio] = useState(0.5);
  // Store the last manual split ratio to restore after collapse
  const [savedSplitRatio, setSavedSplitRatio] = useState(0.5);
  // Use a key to force re-render of content when expand state changes
  const [renderKey, setRenderKey] = useState(0);
  const dragHandleHeight = 6; // Height of drag handle in pixels - smaller for better aesthetics
  const isDraggingRef = useRef(false);
  const lastUpdateTimeRef = useRef(0);
  const animationFrameRef = useRef<number | null>(null);
  const startYRef = useRef(0);
  const startRatioRef = useRef(0);
  const wasExpandedRef = useRef(isTopCardExpanded);

  // When expand state changes, save or restore the split ratio and force re-render
  useEffect(() => {
    if (isTopCardExpanded !== wasExpandedRef.current) {
      // Force re-render of content when expand state changes
      setRenderKey(prev => prev + 1);

      if (isTopCardExpanded) {
        // Transitioning from collapsed to expanded - save the current split ratio
        setSavedSplitRatio(splitRatio);
      } else {
        // Transitioning from expanded to collapsed - restore the saved split ratio
        setSplitRatio(savedSplitRatio);
      }
      wasExpandedRef.current = isTopCardExpanded;

      // Reset any ongoing drag operation
      if (isDraggingRef.current) {
        isDraggingRef.current = false;
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      }
    }
  }, [isTopCardExpanded, splitRatio, savedSplitRatio]);

  // Calculate heights based on split ratio with memoization for performance
  const { topCardHeight, bottomCardHeight } = useMemo(() => {
    if (isTopCardExpanded) {
      return {
        topCardHeight: containerHeight,
        bottomCardHeight: 0
      };
    } else {
      // Apply constraints to ensure minimum heights
      const minRatio = minCardHeight / containerHeight;
      const maxRatio = 1 - minRatio;
      const constrainedRatio = Math.min(Math.max(splitRatio, minRatio), maxRatio);

      return {
        topCardHeight: constrainedRatio * containerHeight,
        bottomCardHeight: (1 - constrainedRatio) * containerHeight
      };
    }
  }, [splitRatio, containerHeight, minCardHeight, isTopCardExpanded]);

  // Optimized drag handler using requestAnimationFrame for smoother performance
  const updateSplitRatio = useCallback((clientY: number) => {
    if (!isDraggingRef.current || isTopCardExpanded) return;

    const now = Date.now();
    // Throttle updates to improve performance (max 60fps)
    if (now - lastUpdateTimeRef.current < 16) return;

    const deltaY = clientY - startYRef.current;
    const viewportHeight = window.innerHeight;
    const deltaRatio = deltaY / viewportHeight;

    // Calculate new split ratio
    const newRatio = startRatioRef.current + deltaRatio;

    // Enforce minimum heights
    const minRatio = minCardHeight / containerHeight;
    const maxRatio = 1 - minRatio;

    if (newRatio >= minRatio && newRatio <= maxRatio) {
      setSplitRatio(newRatio);
      // Also update the saved ratio so it's remembered when toggling expand/collapse
      setSavedSplitRatio(newRatio);
      lastUpdateTimeRef.current = now;
    }

    // Continue the animation loop
    animationFrameRef.current = requestAnimationFrame(() => {
      if (isDraggingRef.current) {
        updateSplitRatio(clientY);
      }
    });
  }, [containerHeight, minCardHeight, isTopCardExpanded]);

  // Clean up animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // Handle mouse down on the drag handle
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isTopCardExpanded) return; // Don't allow dragging when expanded

    e.preventDefault();

    // Store initial values
    startYRef.current = e.clientY;
    startRatioRef.current = splitRatio;
    isDraggingRef.current = true;

    // Start the animation frame loop
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(() => {
      updateSplitRatio(e.clientY);
    });

    // Add event listeners for mouse move and up
    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (isDraggingRef.current) {
        updateSplitRatio(moveEvent.clientY);
      }
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove, { passive: true });
    document.addEventListener('mouseup', handleMouseUp);
  }, [splitRatio, updateSplitRatio, isTopCardExpanded]);

  // Handle touch events for mobile devices
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (isTopCardExpanded) return; // Don't allow dragging when expanded

    e.preventDefault();
    const touch = e.touches[0];
    startYRef.current = touch.clientY;
    startRatioRef.current = splitRatio;
    isDraggingRef.current = true;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      if (isDraggingRef.current) {
        const touchMove = moveEvent.touches[0];
        updateSplitRatio(touchMove.clientY);
      }
    };

    const handleTouchEnd = () => {
      isDraggingRef.current = false;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  }, [splitRatio, updateSplitRatio, isTopCardExpanded]);

  // Create wrapper components that force re-render when expand state changes
  const TopCardWrapper = useMemo(() => {
    return (
      <div
        key={`top-${renderKey}`}
        className="will-change-transform w-full h-full"
        style={{
          height: `${topCardHeight}vh`,
          overflow: 'hidden',
          transition: isDraggingRef.current ? 'none' : 'height 0.15s ease-out'
        }}
      >
        {topCard}
      </div>
    );
  }, [topCard, topCardHeight, isDraggingRef.current, renderKey]);

  // Determine if we should use compact mode for the bottom card
  const isCompactMode = bottomCardHeight < 35; // If less than 40vh, use compact mode

  const BottomCardWrapper = useMemo(() => {
    return (
      <div
        key={`bottom-${renderKey}`}
        className={`will-change-transform w-full h-full ${isCompactMode ? 'compact-tabs' : ''}`}
        style={{
          height: `${bottomCardHeight}vh`,
          overflow: 'hidden',
          opacity: isTopCardExpanded ? 0 : 1,
          visibility: isTopCardExpanded ? 'hidden' : 'visible',
          transition: isDraggingRef.current ? 'none' : 'height 0.15s ease-out, opacity 0.2s ease'
        }}
      >
        {bottomCard}
      </div>
    );
  }, [bottomCard, bottomCardHeight, isTopCardExpanded, isDraggingRef.current, renderKey, isCompactMode]);

  return (
    <div className="flex flex-col w-full h-full relative overflow-hidden">
      {/* Top Card */}
      {TopCardWrapper}

      {/* Drag Handle */}
      {!isTopCardExpanded && (
        <div
          className="w-full flex justify-center items-center cursor-ns-resize bg-gray-200 hover:bg-gray-300 z-10 select-none"
          style={{
            height: `${dragHandleHeight}px`,
            position: 'relative',
            touchAction: 'none' // Prevents scrolling on touch devices while dragging
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <GripHorizontal size={14} className="text-gray-500" />
        </div>
      )}

      {/* Bottom Card */}
      {BottomCardWrapper}
    </div>
  );
};

export default ResizableCardContainer;
