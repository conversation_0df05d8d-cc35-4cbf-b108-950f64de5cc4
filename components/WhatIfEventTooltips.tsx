import { EventType } from "@/components/tabs/WhatIfTab";

// Tooltip text for each event type
export const eventTypeTooltips: Record<EventType, string> = {
  'recession': 'Model a market downturn that affects investment and KiwiSaver funds based on their risk profiles. Higher-risk funds experience greater losses but may recover more strongly.',
  'death': 'Model the financial impact of a death, including insurance payouts, income loss, and expense changes. The model will adjust superannuation from couple to single rate if applicable.',
  'tpd': 'Total Permanent Disability (TPD) models complete income loss for the affected person, with insurance payouts and expense adjustments. Unlike death, superannuation rates remain unchanged.',
  'trauma': 'Model a temporary critical illness or trauma event with partial income reduction for a specified period, insurance payout, and temporary expense changes.',
  'redundancy': 'Model job loss with severance pay and a period of unemployment. Income is completely reduced during the unemployment period.',
  'maternity': 'Model maternity or paternity leave with reduced income during leave and a gradual return-to-work period with partially reduced income.',
  'inheritance': 'Model receiving a lump sum inheritance with options to allocate between investments and savings.'
};

// Tooltip text for each input field by event type
export const inputTooltips: Record<string, string> = {
  // Common fields
  'age': 'The age at which this event occurs in your financial timeline.',
  'person': 'The person affected by this event.',
  
  // Recession fields
  'marketLoss': 'The percentage market loss during the recession. Higher values represent more severe market downturns. Conservative funds will experience less severe losses than growth funds.',
  'reboundType': 'How the market recovers after the recession: Good (exceeds pre-recession values for growth funds), Average (returns to pre-recession values), or Bad (recovers partially).',
  'reboundPeriod': 'The number of years it takes for the market to recover according to the selected rebound type.',
  
  // Death fields
  'insurancePayout': 'The total insurance payout received upon death.',
  'investmentAllocation': 'Percentage of insurance payout to allocate to investments. The remainder will be added to savings.',
  'expenseReduction': 'Percentage reduction in household expenses after death. For example, 40% means expenses will be reduced to 60% of the original amount.',
  
  // TPD fields
  'tpdInsurancePayout': 'The total insurance payout received upon total permanent disability.',
  'tpdInvestmentAllocation': 'Percentage of TPD insurance payout to allocate to investments. The remainder will be added to savings.',
  'tpdExpenseReduction': 'Percentage reduction in household expenses after TPD. For example, 40% means expenses will be reduced to 60% of the original amount.',
  
  // Trauma fields
  'traumaInsurancePayout': 'The total insurance payout received upon trauma/critical illness.',
  'traumaInvestmentAllocation': 'Percentage of trauma insurance payout to allocate to investments. The remainder will be added to savings.',
  'recoveryPeriod': 'Duration in years that the income and expense reductions will apply after the trauma event.',
  'incomeReduction': 'Percentage reduction in income during the recovery period. For example, 50% means income will be reduced to 50% of its original value.',
  'traumaExpenseReduction': 'Percentage reduction in expenses during the recovery period. For example, 20% means expenses will be reduced to 80% of their original value.',
  
  // Redundancy fields
  'severancePay': 'The total severance payment received upon redundancy, which will be added to your savings.',
  'unemploymentPeriod': 'The number of months you expect to be unemployed. Income will be completely reduced during this period.',
  
  // Maternity fields
  'maternityLeaveMonths': 'Duration of full maternity/paternity leave in months.',
  'incomeReductionPercent': 'Percentage of income received during maternity/paternity leave. For example, 20% means you\'ll receive 20% of your normal income.',
  'backToWorkMonths': 'Duration of the back-to-work transition period in months after maternity/paternity leave ends.',
  'backToWorkIncomePercent': 'Percentage of income received during back-to-work period. For example, 60% means you\'ll receive 60% of your normal income.',
  
  // Inheritance fields
  'amount': 'The total inheritance amount received.',
  'investPercentage': 'Percentage of inheritance to allocate to investments. The remainder will be added to savings.'
};
