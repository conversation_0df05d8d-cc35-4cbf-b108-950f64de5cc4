'use client';

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ResultTable } from "./ResultTable";
import { ResultChart } from "./ResultChart";
import { BarChart3, Table } from "lucide-react";
import { Button } from "@/components/ui/button";

interface QueryResultsProps {
  data: any[];
  columns: string[];
  chartConfig?: ChartConfig;
}

interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'area' | 'card';
  xKey: string;
  yKeys: string[];
  title: string;
  description?: string;
  colors?: Record<string, string>;
  legend?: boolean;
}

export function QueryResults({ data, columns, chartConfig }: QueryResultsProps) {
  const [activeTab, setActiveTab] = useState<string>("table");

  if (!data || data.length === 0) {
    return null;
  }

  const hasChartConfig = chartConfig && chartConfig.xKey && chartConfig.yKeys && chartConfig.yKeys.length > 0;



  return (
    <div className="mt-4 border rounded-lg p-4 bg-card">
      <Tabs defaultValue="table" value={activeTab} onValueChange={setActiveTab}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">Query Results</h3>
            <span className="text-xs text-muted-foreground">({data.length} rows)</span>
          </div>
          <div className="flex items-center gap-2">
            <TabsList>
              <TabsTrigger value="table" className="flex items-center gap-1">
                <Table className="h-4 w-4" />
                <span>Table</span>
              </TabsTrigger>
              <TabsTrigger
                value="chart"
                disabled={!hasChartConfig}
                className="flex items-center gap-1"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Chart</span>
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <TabsContent value="table" className="mt-0">
          <ResultTable data={data} columns={columns} />
        </TabsContent>

        <TabsContent value="chart" className="mt-0">
          {hasChartConfig ? (
            <ResultChart data={data} config={chartConfig} />
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              Chart visualization is not available for this query
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
