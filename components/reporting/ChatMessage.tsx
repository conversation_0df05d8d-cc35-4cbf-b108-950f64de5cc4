'use client';

import { cn } from "@/lib/utils";
import { Message } from "ai";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { QueryResults } from "./QueryResults";
import ReactMarkdown from "react-markdown";

// Extended Message type to include tool_calls property
interface ExtendedMessage extends Message {
  tool_calls?: Array<{
    name: string;
    output?: string;
  }>;
}

interface ChatMessageProps {
  message: ExtendedMessage;
}

export function ChatMessage({ message }: ChatMessageProps) {
  const [showQuery, setShowQuery] = useState(false);

  // Render message parts or fallback to content
  const renderMessageContent = () => {
    // If message has parts, use them for rendering
    if (message.parts && message.parts.length > 0) {
      return message.parts.map((part, index) => {
        if (part.type === 'text') {
          return (
            <div key={`text-${index}`} className="text-sm markdown-content">
              <ReactMarkdown>{part.text}</ReactMarkdown>
            </div>
          );
        }

        if (part.type === 'tool-invocation') {
          const toolInvocation = part.toolInvocation;

          // Handle different tool invocation states
          if (toolInvocation.toolName === 'queryDatabase') {
            switch (toolInvocation.state) {
              case 'partial-call':
                // Show thinking state
                return (
                  <div key={`tool-partial-${index}`} className="text-sm text-muted-foreground italic flex items-center gap-2 opacity-70">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Thinking...
                  </div>
                );
              case 'call':
                // Show executing state
                return (
                  <div key={`tool-call-${index}`} className="text-sm text-muted-foreground italic flex items-center gap-2 opacity-70">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Executing query...
                  </div>
                );
              case 'result':
                // Process and display the result
                try {
                  const result = typeof toolInvocation.result === 'string'
                    ? JSON.parse(toolInvocation.result)
                    : toolInvocation.result;

                  if (!result) return null;

                  const hasSqlQuery = result.query;
                  const hasError = result.status === 'error';
                  const hasData = result.status === 'success' && result.data && result.data.length > 0;
                  const hasNoResults = result.status === 'success' && (!result.data || result.data.length === 0);
                  const chartConfig = result.chartConfig;

                  return (
                    <div key={`tool-result-${index}`} className="mt-2 space-y-2">
                      {/* Display SQL query if available */}
                      {hasSqlQuery && (
                        <div className="mt-2">
                          <Button
                            variant={hasError ? "destructive" : "outline"}
                            size="sm"
                            className="flex items-center gap-1 text-xs"
                            onClick={() => setShowQuery(!showQuery)}
                          >
                            <Code className="h-3 w-3" />
                            {showQuery ? "Hide SQL Query" : "Show SQL Query"}
                            {hasError && " (Error)"}
                          </Button>

                          {showQuery && (
                            <div className="mt-2 rounded bg-slate-900 p-2 text-xs text-slate-50 font-mono overflow-x-auto">
                              {result.query}
                              {hasError && (
                                <div className="mt-2 text-red-400 font-normal">
                                  Error: {result.error}
                                </div>
                              )}
                              {hasNoResults && (
                                <div className="mt-2 text-yellow-400 font-normal">
                                  No results found. The assistant will try an alternative approach.
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Display query results if available */}
                      {hasData && (
                        <QueryResults
                          data={result.data}
                          columns={result.columns || Object.keys(result.data[0])}
                          chartConfig={chartConfig}
                        />
                      )}
                    </div>
                  );
                } catch (error) {
                  console.error('Error parsing tool result:', error);
                  return null;
                }
            }
          }
        }

        return null;
      });
    }

    // Fallback to content if no parts are available
    return (
      <div className="text-sm markdown-content">
        <ReactMarkdown>{message.content}</ReactMarkdown>
      </div>
    );
  };

  // Legacy code for backward compatibility
  const hasToolCalls = message.tool_calls && message.tool_calls.length > 0;
  const queryDatabaseCall = hasToolCalls && message.tool_calls
    ? message.tool_calls.find((call) => call.name === 'queryDatabase')
    : null;
  const queryDatabaseResult = queryDatabaseCall?.output
    ? JSON.parse(queryDatabaseCall.output)
    : null;
  const hasSqlQuery = queryDatabaseResult?.query;
  const hasError = queryDatabaseResult?.status === 'error';
  const hasData = queryDatabaseResult?.status === 'success' &&
                 queryDatabaseResult?.data &&
                 queryDatabaseResult?.data.length > 0;
  const hasNoResults = queryDatabaseResult?.status === 'success' &&
                      (!queryDatabaseResult?.data || queryDatabaseResult?.data.length === 0);
  const chartConfig = queryDatabaseResult?.chartConfig;

  return (
    <div
      className={cn(
        "flex items-start gap-4 py-4",
        message.role === "user" ? "justify-end" : "justify-start"
      )}
    >
      {message.role !== "user" && (
        <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-primary text-primary-foreground">
          <Bot className="h-4 w-4" />
        </div>
      )}
      <div
        className={cn(
          "flex max-w-[80%] flex-col gap-2 rounded-lg px-4 py-2",
          message.role === "user"
            ? "bg-primary text-primary-foreground"
            : "bg-muted"
        )}
      >
        {/* Render message content using parts or fallback to legacy approach */}
        {message.parts ? (
          renderMessageContent()
        ) : (
          <>
            <div className="text-sm markdown-content">
              <ReactMarkdown>{message.content}</ReactMarkdown>
            </div>

            {/* Legacy display for SQL query if available */}
            {hasSqlQuery && (
              <div className="mt-2">
                <Button
                  variant={hasError ? "destructive" : "outline"}
                  size="sm"
                  className="flex items-center gap-1 text-xs"
                  onClick={() => setShowQuery(!showQuery)}
                >
                  <Code className="h-3 w-3" />
                  {showQuery ? "Hide SQL Query" : "Show SQL Query"}
                  {hasError && " (Error)"}
                </Button>

                {showQuery && (
                  <div className="mt-2 rounded bg-slate-900 p-2 text-xs text-slate-50 font-mono overflow-x-auto">
                    {queryDatabaseResult.query}
                    {hasError && (
                      <div className="mt-2 text-red-400 font-normal">
                        Error: {queryDatabaseResult.error}
                      </div>
                    )}
                    {hasNoResults && (
                      <div className="mt-2 text-yellow-400 font-normal">
                        No results found. The assistant will try an alternative approach.
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Legacy display for query results if available */}
            {hasData && (
              <QueryResults
                data={queryDatabaseResult.data}
                columns={queryDatabaseResult.columns || Object.keys(queryDatabaseResult.data[0])}
                chartConfig={chartConfig}
              />
            )}
          </>
        )}
      </div>
      {message.role === "user" && (
        <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-background">
          <User className="h-4 w-4" />
        </div>
      )}
    </div>
  );
}
