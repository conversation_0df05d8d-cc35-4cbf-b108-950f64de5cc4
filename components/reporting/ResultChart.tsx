'use client';

import { useMemo } from "react";
import {
  BarChart,
  Bar,
  LineC<PERSON>,
  Line,
  PieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ResultChartProps {
  data: any[];
  config: ChartConfig;
}

interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'area' | 'card';
  xKey: string;
  yKeys: string[];
  title: string;
  description?: string;
  takeaway?: string;
  colors?: Record<string, string>;
  legend?: boolean;
}

export function ResultChart({ data, config }: ResultChartProps) {
  const { type, xKey, yKeys, title, description, takeaway, colors = {}, legend = true } = config;

  // Default colors if not provided
  const defaultColors = ['#2563eb', '#16a34a', '#dc2626', '#9333ea', '#f59e0b', '#0891b2'];

  // Prepare data for chart
  const chartData = useMemo(() => {
    return data.map(item => {
      // Convert numeric strings to numbers for better chart rendering
      const processedItem = { ...item };
      yKeys.forEach(key => {
        if (typeof processedItem[key] === 'string' && !isNaN(Number(processedItem[key]))) {
          processedItem[key] = Number(processedItem[key]);
        }
      });
      return processedItem;
    });
  }, [data, yKeys]);

  if (!data || data.length === 0) {
    return <div className="text-center py-4 text-muted-foreground">No data to visualize</div>;
  }

  // Export chart as image
  const exportChart = () => {
    const svgElement = document.querySelector('.recharts-wrapper svg');
    if (!svgElement) return;

    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    const svgRect = svgElement.getBoundingClientRect();
    canvas.width = svgRect.width;
    canvas.height = svgRect.height;

    // Create an image from the SVG
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const img = new Image();
    img.onload = () => {
      ctx.drawImage(img, 0, 0);

      // Convert canvas to PNG and download
      const link = document.createElement('a');
      link.download = `${title.replace(/\s+/g, '_')}_chart.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    };
    img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
  };

  // Render a card view for single KPI values
  if (type === 'card') {
    const value = data[0]?.[yKeys[0]];
    const formattedValue = typeof value === 'number'
      ? new Intl.NumberFormat('en-US').format(value)
      : value;

    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{formattedValue}</div>
          {description && <p className="text-sm text-muted-foreground mt-2">{description}</p>}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium">{title}</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={exportChart}
          className="flex items-center gap-1 text-xs"
        >
          <Download className="h-3 w-3" />
          Export Chart
        </Button>
      </div>

      {description && (
        <p className="text-sm text-muted-foreground mb-4">{description}</p>
      )}

      <div className="h-[350px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          {type === 'bar' ? (
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              {legend && <Legend />}
              {yKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={colors[key] || defaultColors[index % defaultColors.length]}
                />
              ))}
            </BarChart>
          ) : type === 'line' ? (
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              {legend && <Legend />}
              {yKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={colors[key] || defaultColors[index % defaultColors.length]}
                />
              ))}
            </LineChart>
          ) : type === 'area' ? (
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              {legend && <Legend />}
              {yKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  fill={colors[key] || defaultColors[index % defaultColors.length]}
                  stroke={colors[key] || defaultColors[index % defaultColors.length]}
                />
              ))}
            </AreaChart>
          ) : (
            <PieChart>
              <Tooltip />
              {legend && <Legend />}
              <Pie
                data={chartData}
                nameKey={xKey}
                dataKey={yKeys[0]}
                cx="50%"
                cy="50%"
                outerRadius={80}
                label
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={colors[entry[xKey]] || defaultColors[index % defaultColors.length]}
                  />
                ))}
              </Pie>
            </PieChart>
          )}
        </ResponsiveContainer>
      </div>

      {takeaway && (
        <div className="mt-4 p-3 bg-muted rounded-md text-sm">
          <strong>Key Takeaway:</strong> {takeaway}
        </div>
      )}
    </div>
  );
}
