'use client';

import { useChat } from "@ai-sdk/react";
import { useEffect, useRef } from "react";
import { ChatInput } from "./ChatInput";
import { ChatMessage } from "./ChatMessage";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Trash2, RotateCcw } from "lucide-react";

export function SQLChatbot() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    reload,
    stop,
    setMessages
  } = useChat({
    api: "/api/reporting/chat",
    id: "sql-assistant", // Add a persistent ID for the chat
    initialMessages: [], // This would be loaded from storage in a real implementation
    experimental_throttle: 50, // Throttle UI updates for better performance
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Reference to the scroll container
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Function to check if user is at the bottom of the scroll area
  const isUserAtBottom = () => {
    const container = scrollContainerRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    if (!container) return true;

    // Check if user is at or near the bottom (within 100px)
    const { scrollTop, scrollHeight, clientHeight } = container as HTMLElement;
    return scrollHeight - scrollTop - clientHeight < 100;
  };

  // Scroll to bottom function that respects user's scroll position
  const scrollToBottomIfNeeded = () => {
    if (messagesEndRef.current && isUserAtBottom()) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    // Always scroll to bottom when a new message is added
    if (messages.length > 0) {
      scrollToBottomIfNeeded();
    }
  }, [messages.length]);

  // Scroll to bottom when loading state changes
  useEffect(() => {
    if (status === 'streaming' || status === 'submitted') {
      scrollToBottomIfNeeded();
    }
  }, [status]);

  // Set up a MutationObserver to detect content changes and scroll to bottom
  useEffect(() => {
    if (!messagesEndRef.current) return;

    // Create a MutationObserver to watch for changes in the chat content
    const observer = new MutationObserver((mutations) => {
      // Only scroll if content was added (not just attributes changed)
      const hasContentChanges = mutations.some(mutation =>
        mutation.type === 'childList' || mutation.type === 'characterData'
      );

      if (hasContentChanges) {
        scrollToBottomIfNeeded();
      }
    });

    // Get the parent element that contains all messages
    const chatContainer = messagesEndRef.current.parentElement;

    if (chatContainer) {
      // Start observing the chat container for changes
      observer.observe(chatContainer, {
        childList: true,  // Watch for changes to child elements
        subtree: true,    // Watch for changes in the entire subtree
        characterData: true, // Watch for changes to text content
      });
    }

    // Clean up the observer when component unmounts
    return () => observer.disconnect();
  }, []);

  // Clear chat history
  const handleClearChat = () => {
    setMessages([]);
  };

  return (
    <Card className="flex flex-col h-full border-0 shadow-none">
      <CardHeader className="px-4 py-3 border-b">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">SQL Assistant</CardTitle>
            <CardDescription className="text-xs">
              Ask questions about your data in natural language
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearChat}
              title="Clear chat history"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => reload()}
              disabled={messages.length === 0}
              title="Regenerate last response"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-hidden" ref={scrollContainerRef}>
          <ScrollArea className="h-full w-full" type="always" scrollHideDelay={0}>
            <div className="p-4">
            {messages.length === 0 ? (
              <div className="flex h-full items-center justify-center text-center p-8">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Welcome to SQL Assistant!</h3>
                  <p className="text-sm text-muted-foreground">
                    Ask questions about your data in natural language and I'll convert them to SQL queries.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    For example: "How many households do we have?" or "Show me households in New York"
                  </p>
                  <div className="text-sm text-muted-foreground mt-4">
                    <p className="font-medium">Try these examples:</p>
                    <div className="flex flex-col gap-2 mt-2">
                      {[
                        "How many households do we have?",
                        "Show me households in New York",
                        "What are the top 5 states with the most households?",
                        "How has the number of households changed over time?"
                      ].map((example, i) => (
                        <Button
                          key={i}
                          variant="outline"
                          className="text-left justify-start"
                          onClick={() => {
                            handleSubmit(undefined, {
                              data: { prompt: example }
                            });
                          }}
                        >
                          {example}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4 pb-2">
                {messages.map((message) => (
                  <ChatMessage key={message.id} message={message} />
                ))}
                {(status === 'streaming' || status === 'submitted') && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse"></div>
                    <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
            </div>
          </ScrollArea>
        </div>
        <ChatInput
          input={input}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          isLoading={status === 'streaming' || status === 'submitted'}
          onStop={stop}
        />
      </CardContent>
    </Card>
  );
}
