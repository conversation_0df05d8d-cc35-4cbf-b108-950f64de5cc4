'use client';

import { FormEvent, ChangeEvent } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Loader2, StopCircle } from "lucide-react";

interface ChatInputProps {
  input: string;
  handleInputChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>, options?: any) => void;
  isLoading: boolean;
  onStop?: () => void;
}

export function ChatInput({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  onStop
}: ChatInputProps) {
  return (
    <form
      onSubmit={handleSubmit}
      className="border-t p-4 flex items-end gap-2"
    >
      <Textarea
        value={input}
        onChange={handleInputChange}
        placeholder="Ask a question about your data..."
        className="min-h-[60px] resize-none"
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e as unknown as FormEvent<HTMLFormElement>);
          }
        }}
        disabled={isLoading}
      />
      {isLoading && onStop ? (
        <Button
          type="button"
          size="icon"
          variant="destructive"
          onClick={onStop}
          className="shrink-0"
        >
          <StopCircle className="h-5 w-5" />
        </Button>
      ) : (
        <Button
          type="submit"
          size="icon"
          disabled={isLoading || input.trim() === ''}
          className="shrink-0"
        >
          {isLoading ? (
            <Loader2 className="h-5 w-5 animate-spin" />
          ) : (
            <Send className="h-5 w-5" />
          )}
        </Button>
      )}
    </form>
  );
}
