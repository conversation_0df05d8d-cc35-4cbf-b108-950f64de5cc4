'use client';

import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent, 
  ChartLegend, 
  ChartLegendContent 
} from "@/components/ui/chart";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Sector, ResponsiveContainer } from "recharts";
import { useState } from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';

interface InvestmentProfileRecommendationsProps {
  score: number;
  getRiskCategory: (score: number) => { category: string; color: string };
  getSuggestedAllocation: (category: string) => Array<{ name: string; value: number; color: string }>;
  getAllocationBands: (category: string) => { optimal: { min: number; max: number }; acceptable: { min: number; max: number }; color: string };
  getOptimalGradient: (category: string) => string;
}

export function AllocationBandMeter({ category, getAllocationBands, getOptimalGradient }: { 
  category: string; 
  getAllocationBands: (category: string) => { optimal: { min: number; max: number }; acceptable: { min: number; max: number }; color: string };
  getOptimalGradient: (category: string) => string;
}) {
  const bands = getAllocationBands(category);
  
  return (
    <div className="w-full mt-8 mb-4 space-y-3">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Recommended Growth Asset Allocation</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="w-4 h-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>The recommended range of growth assets based on your risk profile</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <span className="text-sm font-semibold text-primary">{bands.optimal.min}%-{bands.optimal.max}%</span>
      </div>
      <div className="relative h-10 w-full rounded-full overflow-hidden shadow-sm">
        <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-red-400 opacity-30"></div>
        <div 
          className="absolute h-full bg-gradient-to-r from-amber-400 to-amber-300 opacity-50"
          style={{ 
            left: `${bands.acceptable.min}%`, 
            width: `${bands.acceptable.max - bands.acceptable.min}%` 
          }}
        ></div>
        <div 
          className="absolute h-full bg-gradient-to-r from-green-500 to-green-400 opacity-80"
          style={{ 
            left: `${bands.optimal.min}%`, 
            width: `${bands.optimal.max - bands.optimal.min}%` 
          }}
        ></div>
        {category !== 'Aggressive' && category !== 'Very Conservative' && (
          <div className="absolute inset-0 flex text-[10px] text-white font-bold">
            <div className="h-full flex items-center justify-center px-2 opacity-80">
              Too Conservative
            </div>
            <div className="h-full ml-auto flex items-center justify-center px-2 opacity-80">
              Too Aggressive
            </div>
          </div>
        )}
      </div>
      <div className="flex justify-between text-xs text-muted-foreground mt-1">
        {[0, 20, 40, 60, 80, 100].map(value => (
          <span key={value}>{value}%</span>
        ))}
      </div>
      <div className="mt-2 text-sm space-y-1">
        <div>
          <span className="font-medium">Optimal range: </span>
          <span className="text-muted-foreground">{bands.optimal.min}%-{bands.optimal.max}% in growth assets</span>
        </div>
        <div>
          <span className="font-medium">Acceptable range: </span>
          <span className="text-muted-foreground">{bands.acceptable.min}%-{bands.acceptable.max}% in growth assets</span>
        </div>
      </div>
    </div>
  );
}

export function InvestmentProfileRecommendations({ 
  score, 
  getRiskCategory, 
  getSuggestedAllocation, 
  getAllocationBands, 
  getOptimalGradient 
}: InvestmentProfileRecommendationsProps) {
  const { category } = getRiskCategory(score);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const suggestedAllocation = getSuggestedAllocation(category);

  const renderActiveShape = (props: any) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
    const RADIAN = Math.PI / 180;
    const sin = Math.sin(-midAngle * RADIAN);
    const cos = Math.cos(-midAngle * RADIAN);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;

    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={cos >= 0 ? 'start' : 'end'} fill="#333">{`${payload.name}: ${value}%`}</text>
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey + 20} textAnchor={cos >= 0 ? 'start' : 'end'} fill="#999">{`${(percent * 100).toFixed(2)}% of portfolio`}</text>
      </g>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Investment Recommendations</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="w-5 h-5 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-[300px]">
                <p>These recommendations are based on your risk profile and aim to balance potential returns with your risk tolerance.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <p className="text-muted-foreground">
            {category === 'Very Conservative' && 
              "Recommended for investors who prioritize capital preservation. Suitable allocation: 80-100% in defensive assets (cash, term deposits, high-quality bonds) and 0-20% in growth assets."}
            {category === 'Conservative' && 
              "Recommended for investors seeking stability with some growth. Suitable allocation: 60-80% in defensive assets and 20-40% in growth assets (diversified across domestic and international shares)."}
            {category === 'Moderate' && 
              "Recommended for balanced investors. Suitable allocation: 40-60% in defensive assets and 40-60% in growth assets (diversified across domestic and international shares, and property)."}
            {category === 'Growth' && 
              "Recommended for growth-oriented investors. Suitable allocation: 20-40% in defensive assets and 60-80% in growth assets (diversified across domestic and international shares, property, and alternative investments)."}
            {category === 'Aggressive' && 
              "Recommended for investors seeking maximum growth. Suitable allocation: 0-20% in defensive assets and 80-100% in growth assets (diversified across domestic and international shares, property, and alternative investments)."}
          </p>
            
          <AllocationBandMeter category={category} getAllocationBands={getAllocationBands} getOptimalGradient={getOptimalGradient} />
            
          <div>
            <h3 className="text-lg font-semibold mb-4">Suggested Asset Allocation</h3>
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="w-full md:w-1/2 h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      activeIndex={activeIndex !== null ? activeIndex : undefined}
                      activeShape={renderActiveShape}
                      data={suggestedAllocation}
                      innerRadius="60%"
                      outerRadius="80%"
                      dataKey="value"
                      onMouseEnter={(_, index) => setActiveIndex(index)}
                      onMouseLeave={() => setActiveIndex(null)}
                    >
                      {suggestedAllocation.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="w-full md:w-1/2 space-y-3">
                <h4 className="text-md font-medium">Allocation Breakdown</h4>
                <ul className="space-y-2">
                  {suggestedAllocation.map((item, index) => (
                    <li 
                      key={index} 
                      className={`flex justify-between items-center p-2 rounded-md transition-colors ${
                        activeIndex === index ? 'bg-muted' : 'hover:bg-muted/50'
                      }`}
                      onMouseEnter={() => setActiveIndex(index)}
                      onMouseLeave={() => setActiveIndex(null)}
                    >
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                      <span className="font-semibold text-primary">{item.value}%</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
