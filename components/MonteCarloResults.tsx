import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';

interface MonteCarloResultsProps {
  chanceOfSuccess: number | null;
  successfulScenarios: number | null;
  failedScenarios: number | null;
  dataReady?: boolean;
}

const MonteCarloResults: React.FC<MonteCarloResultsProps> = ({
  chanceOfSuccess,
  successfulScenarios,
  failedScenarios,
  dataReady = true,
}) => {
  if (chanceOfSuccess === null || successfulScenarios === null || failedScenarios === null) {
    return null;
  }

  const data = [
    { name: 'Successful', value: successfulScenarios },
    { name: 'Failed', value: failedScenarios },
  ];

  const COLORS = ['hsl(var(--chart-2))', 'hsl(var(--chart-1))'];

  const successColor = 'hsl(173,58%,39%)';
  const failColor = 'hsl(12,76%,61%)';
  const percentageColor = chanceOfSuccess >= 80 ? successColor : failColor;

  return (
    <div className="flex items-center justify-between w-full h-full">
      <div className="w-1/2 h-full monte-carlo-chart" id="monte-carlo-chart">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={100}
              fill="#8884d8"
              paddingAngle={5}
              dataKey="value"
              cornerRadius={8}
              isAnimationActive={true}
              animationBegin={0}
              animationDuration={800}
              animationEasing="ease-in-out"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="w-1/2 space-y-6">
        <div>
          <p className="text-sm font-medium text-gray-500">Chance of Success</p>
          <p className="text-3xl font-bold" style={{ color: percentageColor }}>{chanceOfSuccess.toFixed(2)}%</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-500">Successful Scenarios</p>
          <p className="text-2xl font-bold text-[hsl(173,58%,39%)]">{successfulScenarios}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-500">Failed Scenarios</p>
          <p className="text-2xl font-bold text-[hsl(12,76%,61%)]">{failedScenarios}</p>
        </div>
      </div>
    </div>
  );
};

export default MonteCarloResults;