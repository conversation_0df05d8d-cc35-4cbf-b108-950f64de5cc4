'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Bug, 
  Shield, 
  AlertTriangle, 
  Clock, 
  RotateCcw,
  Eye,
  EyeOff
} from 'lucide-react';
import { sessionDebug, getDebugSessionState } from '@/utils/session-debug';

export default function SessionDebugPanel() {
  const [isVisible, setIsVisible] = useState(false);
  const [customMinutes, setCustomMinutes] = useState(4);
  const currentState = getDebugSessionState();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
        >
          <Bug className="h-4 w-4 mr-1" />
          Session Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-lg border-blue-200 bg-blue-50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center text-sm text-blue-800">
              <Bug className="h-4 w-4 mr-2" />
              Session Debug Panel
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
            >
              <EyeOff className="h-3 w-3" />
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-blue-600">Current State:</span>
            <Badge 
              variant={currentState.mode === 'normal' ? 'default' : 'secondary'}
              className={
                currentState.mode === 'normal' ? 'bg-green-100 text-green-800' :
                currentState.mode === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }
            >
              {currentState.mode}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {/* Normal State */}
          <Button
            onClick={() => sessionDebug.normal()}
            variant={currentState.mode === 'normal' ? 'default' : 'outline'}
            size="sm"
            className="w-full justify-start"
          >
            <Shield className="h-3 w-3 mr-2 text-green-600" />
            Normal (Secure)
          </Button>

          {/* Warning States */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Label htmlFor="custom-minutes" className="text-xs text-blue-700">
                Warning (minutes left):
              </Label>
              <Input
                id="custom-minutes"
                type="number"
                value={customMinutes}
                onChange={(e) => setCustomMinutes(Number(e.target.value))}
                className="h-6 w-16 text-xs"
                min="1"
                max="10"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => sessionDebug.warning(customMinutes)}
                variant={currentState.mode === 'warning' ? 'default' : 'outline'}
                size="sm"
                className="text-xs"
              >
                <AlertTriangle className="h-3 w-3 mr-1 text-yellow-600" />
                Custom
              </Button>
              
              <Button
                onClick={() => sessionDebug.urgent()}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                <Clock className="h-3 w-3 mr-1 text-orange-600" />
                30s Left
              </Button>
            </div>
            
            <Button
              onClick={() => sessionDebug.criticalWarning()}
              variant="outline"
              size="sm"
              className="w-full text-xs"
            >
              <AlertTriangle className="h-3 w-3 mr-2 text-red-600" />
              1 Minute Left (Critical)
            </Button>
          </div>

          {/* Error States */}
          <div className="space-y-2">
            <Button
              onClick={() => sessionDebug.expired()}
              variant={currentState.mode === 'expired' ? 'default' : 'outline'}
              size="sm"
              className="w-full justify-start"
            >
              <Clock className="h-3 w-3 mr-2 text-red-600" />
              Expired Session
            </Button>
            
            <Button
              onClick={() => sessionDebug.critical()}
              variant={currentState.mode === 'critical' ? 'default' : 'outline'}
              size="sm"
              className="w-full justify-start"
            >
              <Shield className="h-3 w-3 mr-2 text-red-600" />
              Security Breach
            </Button>
          </div>

          {/* Reset */}
          <div className="pt-2 border-t border-blue-200">
            <Button
              onClick={() => {
                sessionDebug.normal();
                setCustomMinutes(4);
              }}
              variant="ghost"
              size="sm"
              className="w-full text-blue-600 hover:text-blue-800 hover:bg-blue-100"
            >
              <RotateCcw className="h-3 w-3 mr-2" />
              Reset All
            </Button>
          </div>

          {/* Instructions */}
          <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded">
            <p className="font-medium mb-1">Console Commands:</p>
            <code className="text-xs">sessionDebug.warning(5)</code><br/>
            <code className="text-xs">sessionDebug.critical()</code><br/>
            <code className="text-xs">sessionDebug.normal()</code>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
