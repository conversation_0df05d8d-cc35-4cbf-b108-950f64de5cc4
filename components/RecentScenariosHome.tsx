import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title, CardContent } from "@/components/ui/card";
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

interface Scenario {
  id: number;
  scenario_name: string;
  household_name: string;
  household_id?: number;
}

const RecentScenarios = () => {
  const [scenarios, setScenarios] = useState<Scenario[]>([]);
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});
  const router = useRouter();

  // Set up view mode and listen for changes
  useEffect(() => {
    // Ensure viewMode has a default value if not in localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      // Set default to 'user' if nothing is stored
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
      localStorage.setItem('scenariosViewMode', 'user');
    }
    
    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };
    
    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();
          
        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        } else if (error) {
          console.error('Error fetching user profile:', error);
        }
      }
    };
    
    fetchUserProfile();
  }, []);

  // Fetch scenarios based on view mode
  useEffect(() => {
    const fetchScenarios = async () => {
      if (!profileData.user_id) return;
      
      const supabase = createClient();
      let query = supabase.from('scenarios_data1')
        .select('id, scenario_name, household_name, household_id');
      
      // Apply view mode filter
      if (viewMode === 'user') {
        query = query.eq('user_id', profileData.user_id);
      } else if (viewMode === 'organization' && profileData.org_id) {
        query = query.eq('org_id', profileData.org_id);
      }
      
      query = query.order('created_at', { ascending: false }).limit(5);
      
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching scenarios:', error);
      } else {
        setScenarios(data || []);
      }
    };

    if (profileData.user_id) {
      fetchScenarios();
    }
  }, [viewMode, profileData]);

  const handleScenarioClick = (scenarioId: number, householdId?: number) => {
    router.push(`/protected/planner?scenarioId=${scenarioId}${householdId ? `&household_id=${householdId}` : ''}`);
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Recent Scenarios</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {scenarios.map((scenario) => (
            <li key={scenario.id}>
              <button
                onClick={() => handleScenarioClick(scenario.id, scenario.household_id)}
                className="text-left hover:underline"
              >
                {scenario.scenario_name} ({scenario.household_name})
              </button>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default RecentScenarios;
