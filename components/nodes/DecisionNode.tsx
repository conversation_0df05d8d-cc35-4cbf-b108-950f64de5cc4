import { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react';
import { <PERSON>le, Position, NodeProps, useReactFlow, Node, useUpdateNodeInternals } from '@xyflow/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GitBranch, Trash2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { NODE_UPDATED_EVENT, EDGE_CREATED_EVENT, EDGE_REMOVED_EVENT, NodeUpdatedEventData, EdgeEventData } from '@/lib/workflow-events';
import { LabeledHandle } from '@/components/labeled-handle';

interface PathCondition {
  id: string;
  label: string;
  color: string;
  description: string;
}

export default function DecisionNode({ data, isConnectable, id, selected }: NodeProps) {
  const reactFlowInstance = useReactFlow();
  const updateNodeInternals = useUpdateNodeInternals();
  // Decision nodes are always expanded
  const isExpanded = true;
  // We don't need these state variables anymore as they're not used
  // Just use data directly

  // Dynamic path conditions based on input node type
  // Initialize with saved path conditions if available
  const [pathConditions, setPathConditions] = useState<PathCondition[]>(
    Array.isArray(data.pathConditions) && data.pathConditions.length > 0
      ? data.pathConditions
      : []
  );

  // Input node information - initialize with saved values if available
  const [inputNodeId, setInputNodeId] = useState<string | null>(
    typeof data.inputNodeId === 'string' ? data.inputNodeId : null
  );
  const [inputNodeType, setInputNodeType] = useState<string | null>(
    typeof data.inputNodeType === 'string' ? data.inputNodeType : null
  );
  const [inputNodeData, setInputNodeData] = useState<any>(null);

  // Decision nodes are always expanded, so no toggle function needed

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }
  }, [data]);

  // Function to find and update the input node
  const updateInputNode = useCallback(() => {
    if (!reactFlowInstance) return;

    const edges = reactFlowInstance.getEdges();
    const nodes = reactFlowInstance.getNodes();

    // Find edges that target this node
    const incomingEdges = edges.filter(edge => edge.target === data.id);

    if (incomingEdges.length > 0) {
      const sourceNodeId = incomingEdges[0].source;
      const sourceNode = nodes.find(node => node.id === sourceNodeId);

      if (sourceNode) {
        setInputNodeId(sourceNode.id);
        setInputNodeType(sourceNode.type || 'unknown');
        setInputNodeData(sourceNode.data);

        // Store the input node information in the decision node data
        // This helps maintain the connection when the workflow is saved and reloaded
        handleChange('inputNodeId', sourceNode.id);
        handleChange('inputNodeType', sourceNode.type || 'unknown');

        // Generate and store path conditions based on the input node type
        if (sourceNode.type === 'taskNode' && Array.isArray(sourceNode.data.outcomes)) {
          const newPathConditions = sourceNode.data.outcomes.map((outcome: any) => ({
            id: outcome.id,
            label: `If ${outcome.label}`,
            color: outcome.color,
            description: `Path for ${outcome.label}`
          }));

          setPathConditions(newPathConditions);
          handleChange('pathConditions', newPathConditions);
        } else if (sourceNode.type === 'eventNode') {
          const eventPathConditions = [
            { id: 'completed', label: 'If Completed', color: 'green', description: 'Path for Event Completion' },
            { id: 'cancelled', label: 'If Cancelled', color: 'red', description: 'Path for Event Cancellation' },
            { id: 'rescheduled', label: 'If Rescheduled', color: 'yellow', description: 'Path for Event Rescheduling' }
          ];

          setPathConditions(eventPathConditions);
          handleChange('pathConditions', eventPathConditions);
        }
      }
    } else {
      // Check if we have stored input node information
      if (data.inputNodeId && data.inputNodeType) {
        // Try to find the input node using the stored ID
        const storedSourceNode = nodes.find(node => node.id === data.inputNodeId);

        if (storedSourceNode) {
          // We found the stored input node, use its current data
          setInputNodeId(storedSourceNode.id);
          setInputNodeType(storedSourceNode.type || 'unknown');
          setInputNodeData(storedSourceNode.data);

          // If we have saved path conditions, use them
          if (data.pathConditions && Array.isArray(data.pathConditions) && data.pathConditions.length > 0) {
            setPathConditions(data.pathConditions);
          } else {
            // Otherwise, generate new path conditions based on the input node type
            if (storedSourceNode.type === 'taskNode' && Array.isArray(storedSourceNode.data.outcomes)) {
              const newPathConditions = storedSourceNode.data.outcomes.map((outcome: any) => ({
                id: outcome.id,
                label: `If ${outcome.label}`,
                color: outcome.color,
                description: `Path for ${outcome.label}`
              }));

              setPathConditions(newPathConditions);
              handleChange('pathConditions', newPathConditions);
            } else if (storedSourceNode.type === 'eventNode') {
              const eventPathConditions = [
                { id: 'completed', label: 'If Completed', color: 'green', description: 'Path for Event Completion' },
                { id: 'cancelled', label: 'If Cancelled', color: 'red', description: 'Path for Event Cancellation' },
                { id: 'rescheduled', label: 'If Rescheduled', color: 'yellow', description: 'Path for Event Rescheduling' }
              ];

              setPathConditions(eventPathConditions);
              handleChange('pathConditions', eventPathConditions);
            }
          }
          return;
        }
      }

      setInputNodeId(null);
      setInputNodeType(null);
      setInputNodeData(null);

      // If we have saved path conditions, keep using them
      if (data.pathConditions && Array.isArray(data.pathConditions) && data.pathConditions.length > 0) {
        setPathConditions(data.pathConditions);
      }
    }
  }, [reactFlowInstance, data.id, data.inputNodeId, data.inputNodeType, data.pathConditions, handleChange, setPathConditions]);

  // Find the input node connected to this decision node
  useEffect(() => {
    updateInputNode();
  }, [updateInputNode]);

  // Store the current node ID in a ref for event handlers
  const nodeIdRef = useRef(data.id);

  // Update the ref when the node ID changes
  useEffect(() => {
    nodeIdRef.current = data.id;
  }, [data.id]);

  // Listen for node update events
  useEffect(() => {
    const handleNodeUpdated = (event: CustomEvent<NodeUpdatedEventData>) => {
      const { nodeId, field, value } = event.detail; // nodeType not used

      // If this is our input node, update our data
      if (nodeId === inputNodeId) {
        // Update the input node data with the new value
        setInputNodeData((prevData: any) => {
          if (!prevData) return null;
          const updatedData = { ...prevData, [field]: value };

          // If the outcomes changed, we need to update our path conditions
          if (field === 'outcomes' && inputNodeType === 'taskNode' && Array.isArray(value)) {
            // Generate new path conditions based on the updated outcomes
            const newPathConditions = value.map((outcome: { id: any; label: any; color: any; }) => ({
              id: outcome.id,
              label: `If ${outcome.label}`,
              color: outcome.color,
              description: `Path for ${outcome.label}`
            }));

            // Update our path conditions
            setPathConditions(newPathConditions);

            // Save the new path conditions to the node data
            handleChange('pathConditions', newPathConditions);
          }

          return updatedData;
        });
      }
    };

    const handleEdgeCreated = (event: CustomEvent<EdgeEventData>) => {
      const { target } = event.detail;

      // If this edge targets our node, update our input node
      if (target === nodeIdRef.current) {
        setTimeout(updateInputNode, 0);
      }
    };

    const handleEdgeRemoved = (event: CustomEvent<EdgeEventData>) => {
      const { target } = event.detail;

      // If this edge targeted our node, update our input node
      if (target === nodeIdRef.current) {
        setTimeout(updateInputNode, 0);
      }
    };

    // Add event listeners
    window.addEventListener(NODE_UPDATED_EVENT, handleNodeUpdated as EventListener);
    window.addEventListener(EDGE_CREATED_EVENT, handleEdgeCreated as EventListener);
    window.addEventListener(EDGE_REMOVED_EVENT, handleEdgeRemoved as EventListener);

    // Clean up event listeners
    return () => {
      window.removeEventListener(NODE_UPDATED_EVENT, handleNodeUpdated as EventListener);
      window.removeEventListener(EDGE_CREATED_EVENT, handleEdgeCreated as EventListener);
      window.removeEventListener(EDGE_REMOVED_EVENT, handleEdgeRemoved as EventListener);
    };
  }, [data.id, inputNodeId, updateInputNode]);

  // Update path conditions based on input node type
  useEffect(() => {
    // Skip if we don't have an input node yet
    if (!inputNodeType || !inputNodeData) {
      // Only set default conditions if we don't already have saved conditions
      if (!data.pathConditions || !Array.isArray(data.pathConditions) || data.pathConditions.length === 0) {
        const defaultConditions = [
          { id: 'default', label: 'Default Path', color: 'gray', description: 'Default path for no input' }
        ];
        setPathConditions(defaultConditions);
        // Save these conditions to the node data
        handleChange('pathConditions', defaultConditions);
      } else {
        // Use the saved path conditions
        setPathConditions(data.pathConditions);
      }
      return;
    }

    // Generate conditions based on input node type
    let newPathConditions;

    if (inputNodeType === 'taskNode') {
      // For task nodes, use the outcomes from the task
      const taskOutcomes = Array.isArray(inputNodeData.outcomes) ? inputNodeData.outcomes : [
        { id: 'default', label: 'Default', color: 'gray' }
      ];

      newPathConditions = taskOutcomes.map((outcome: { id: any; label: any; color: any; }) => ({
        id: outcome.id,
        label: `If ${outcome.label}`,
        color: outcome.color,
        description: `Path for ${outcome.label}`
      }));
    } else if (inputNodeType === 'eventNode') {
      // For event nodes, use event-specific conditions
      newPathConditions = [
        { id: 'completed', label: 'If Completed', color: 'green', description: 'Path for Event Completion' },
        { id: 'cancelled', label: 'If Cancelled', color: 'red', description: 'Path for Event Cancellation' },
        { id: 'rescheduled', label: 'If Rescheduled', color: 'yellow', description: 'Path for Event Rescheduling' }
      ];
    } else {
      // Default fallback for other node types
      newPathConditions = [
        { id: 'success', label: 'If Success', color: 'green', description: 'Path for Success' },
        { id: 'failure', label: 'If Failure', color: 'red', description: 'Path for Failure' }
      ];
    }

    setPathConditions(newPathConditions);

    // Save these conditions to the node data so they persist when the workflow is saved
    handleChange('pathConditions', newPathConditions);
  }, [inputNodeType, inputNodeData, data.id, data.pathConditions, handleChange]);

  // Reference to the node element
  const nodeRef = useRef<HTMLDivElement>(null);

  // Update node internals when path conditions change
  useEffect(() => {
    // Update node internals to reflect the new handle positions
    if (typeof id === 'string') {
      updateNodeInternals(id);
    }
  }, [pathConditions, id, updateNodeInternals]);

  return (
    <Card ref={nodeRef} className={`w-[300px] shadow-md border-2 ${selected ? 'border-amber-500 ring-2 ring-amber-500' : 'border-amber-200'} bg-white`}>
      {/* Colored header with node type */}
      <div className="bg-amber-500 text-white p-2 flex items-center justify-between rounded-t-md">
        <div className="flex items-center">
          <GitBranch className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Decision</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-amber-600"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {/* Decision nodes are always expanded, so no minimize/maximize button */}
        </div>
      </div>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-0 opacity-0'}`}>
        <CardContent className="p-3 space-y-2">

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Label className="text-xs font-semibold">Path Conditions</Label>
            {!inputNodeId && (
              <Badge variant="outline" className="text-xs flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                No input connected
              </Badge>
            )}
            {inputNodeId && (
              <Badge variant="outline" className="text-xs">
                {inputNodeType === 'taskNode' ? 'Task Input' :
                 inputNodeType === 'eventNode' ? 'Event Input' : 'Other Input'}
              </Badge>
            )}
          </div>

          {pathConditions.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Connect an input node to see path conditions
            </div>
          ) : (
            pathConditions.map((condition) => (
              <LabeledHandle
                key={condition.id}
                id={condition.id}
                title={condition.label}
                onDescriptionChange={(value) => handleChange(`${condition.id}Path`, value)}
                type="source"
                position={Position.Right}
                color={condition.color}
                isConnectable={isConnectable}
              />
            ))
          )}
        </div>
        </CardContent>
      </div>

      {/* Handles for connecting nodes */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-blue-500"
      />

      {/* Handles are now part of the LabeledHandle components */}

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="notification-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
    </Card>
  );
}
