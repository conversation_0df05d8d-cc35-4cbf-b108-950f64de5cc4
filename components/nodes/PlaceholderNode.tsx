import React, { useCallback, ReactNode } from "react";
import {
  useReactFlow,
  NodeProps,
} from "@xyflow/react";
import { Bell } from "lucide-react";

export type PlaceholderNodeProps = Partial<NodeProps> & {
  children?: ReactNode;
  parentNode?: string;
  parentHandle?: string;
  position?: { x: number, y: number };
};

export const PlaceholderNode = ({
  parentNode,
  parentHandle,
}: PlaceholderNodeProps) => {
  const { setNodes, setEdges, getNode } = useReactFlow();

  // Add a notification node connected to the parent node
  const handleClick = useCallback(() => {
    if (!parentNode) return;

    // Generate a unique ID for the new notification node
    const newNodeId = `notification-${Date.now()}`;

    // Get the parent node to position the notification node relative to it
    const parent = getNode(parentNode);
    if (!parent) return;

    // Position the notification node to the right of the parent node
    const notificationNodePosition = {
      x: parent.position.x + 300,
      y: parent.position.y
    };

    // Create the notification node
    const notificationNode = {
      id: newNodeId,
      type: 'notificationNode',
      position: notificationNodePosition,
      data: {
        title: 'Send Notification',
        notificationType: 'task_update',
        content: 'Notification content',
        recipients: 'all_members',
        isExpanded: true,
        onDelete: (nodeId: string) => {
          setNodes((nds) => nds.filter((node) => node.id !== nodeId));
          setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
        }
      }
    };

    // Add the notification node to the flow
    setNodes((nodes) => [...nodes, notificationNode]);

    // Create an edge from the parent node to the notification node
    const newEdgeId = `edge-${parentNode}-${newNodeId}`;
    const newEdge = {
      id: newEdgeId,
      source: parentNode,
      target: newNodeId,
      sourceHandle: parentHandle,
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#555', strokeWidth: 2 }
    };

    // Add the edge to the flow
    setEdges((edges) => [...edges, newEdge]);

  }, [parentNode, parentHandle, setNodes, setEdges, getNode]);

  // Log for debugging
  console.log('Rendering PlaceholderNode for parent:', parentNode);

  return (
    <div
      className="absolute right-0 top-1/2 transform translate-x-[100%] -translate-y-1/2 z-50 pointer-events-auto"
      style={{
        position: 'absolute',
        right: '0px',
        top: '50%',
        transform: 'translateX(100%) translateY(-50%)',
        zIndex: 9999
      }}
    >
      <button
        className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-500 text-white hover:bg-purple-600 shadow-md border-2 border-white"
        onClick={handleClick}
        title="Add Notification Node"
      >
        <Bell className="h-5 w-5" />
      </button>
    </div>
  );
};

PlaceholderNode.displayName = "PlaceholderNode";