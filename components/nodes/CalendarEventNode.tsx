import { useState, useEffect, useCallback, useRef } from 'react';
import { Handle, Position, NodeProps, useNodeId, useReactFlow, Node } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Clock, Maximize2, Minimize2, Trash2, Bell } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { emitNodeUpdated } from '@/lib/workflow-events';

// Event type options with their colors
const EVENT_TYPES = [
  { value: "meeting", label: "Meeting", color: "#4f46e5" }, // Indigo
  { value: "household_event", label: "Household Event", color: "#0ea5e9" }, // Sky
  { value: "internal_task", label: "Internal Task", color: "#10b981" }, // Emerald
  { value: "client_call", label: "Client Call", color: "#f59e0b" }, // Amber
  { value: "deadline", label: "Deadline", color: "#ef4444" }, // Red
  { value: "personal", label: "Personal", color: "#8b5cf6" }, // Violet
  { value: "other", label: "Other", color: "#6b7280" }, // Gray
];

// Time options for the time select
const TIME_OPTIONS = [
  "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
  "12:00", "12:30", "13:00", "13:30", "14:00", "14:30",
  "15:00", "15:30", "16:00", "16:30", "17:00", "17:30"
].map(time => {
  const [hours, minutes] = time.split(':');
  const period = Number(hours) >= 12 ? 'PM' : 'AM';
  const displayHour = Number(hours) % 12 || 12;
  return {
    value: time,
    label: `${displayHour}:${minutes} ${period}`
  };
});

export default function CalendarEventNode({ data, isConnectable, selected }: NodeProps) {
  const nodeId = useNodeId();
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false
  const [isHovered, setIsHovered] = useState(false); // Track hover state
  const [hasNotificationNode, setHasNotificationNode] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for hover timeout

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'New Calendar Event');

  // Helper function to safely initialize the date state
  const getInitialDate = () => {
    const eventDateValue = data.eventDate;
    if (eventDateValue instanceof Date) {
      // Check if the date is valid
      if (!isNaN(eventDateValue.getTime())) {
        return eventDateValue;
      }
    }
    if (typeof eventDateValue === 'string' || typeof eventDateValue === 'number') {
      const date = new Date(eventDateValue);
      // Check if the date is valid after parsing
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
    // Fallback to current date if data.eventDate is invalid, missing, or not a string/number/Date
    return new Date();
  };

  const [eventDate, setEventDate] = useState<Date | undefined>(getInitialDate());
  const [startTime, setStartTime] = useState(typeof data.startTime === 'string' ? data.startTime : "09:00");
  const [endTime, setEndTime] = useState(typeof data.endTime === 'string' ? data.endTime : "10:00");
  const [isAllDay, setIsAllDay] = useState(typeof data.isAllDay === 'boolean' ? data.isAllDay : false);
  const [eventType, setEventType] = useState(typeof data.eventType === 'string' ? data.eventType : "meeting");
  const [location, setLocation] = useState(typeof data.location === 'string' ? data.location : "");
  const [details, setDetails] = useState(typeof data.details === 'string' ? data.details : "");

  // Add event outcome state for decision node integration
  const [eventOutcome, setEventOutcome] = useState<string>(
    typeof data.eventOutcome === 'string' ? data.eventOutcome : 'pending'
  );

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Check for existing notification nodes connected to this node
  useEffect(() => {
    if (!nodeId) return;

    const edges = getEdges();
    const connectedNotificationEdge = edges.find(edge =>
      edge.source === nodeId &&
      edge.target.startsWith('notification-')
    );

    setHasNotificationNode(!!connectedNotificationEdge);
    console.log('Node has notification:', !!connectedNotificationEdge, 'for node:', nodeId);

    // Listen for notification node deletion events
    const handleNotificationDeleted = (event: CustomEvent<{parentNodeId: string}>) => {
      const { parentNodeId } = event.detail;

      if (parentNodeId === nodeId) {
        console.log('Notification node deleted for parent:', nodeId);
        setHasNotificationNode(false);
      }
    };

    window.addEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);

    return () => {
      window.removeEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);
    };
  }, [nodeId, getEdges]);

  // Get the current event type color
  const eventTypeColor = EVENT_TYPES.find(type => type.value === eventType)?.color || "#6b7280";

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }

    // Emit an event for connected nodes to listen to
    emitNodeUpdated({
      nodeId: typeof data.id === 'string' ? data.id : '',
      nodeType: 'calendarEventNode',
      field,
      value
    });
  }, [data]);

  // Format time for display
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const period = Number(hours) >= 12 ? 'PM' : 'AM';
    const displayHour = Number(hours) % 12 || 12;
    return `${displayHour}:${minutes} ${period}`;
  };

  return (
    <div
      className="relative"
      onMouseEnter={() => {
        // Clear any existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
          hoverTimeoutRef.current = null;
        }
        setIsHovered(true);
      }}
      onMouseLeave={() => {
        // Add a delay before hiding the button
        hoverTimeoutRef.current = setTimeout(() => {
          setIsHovered(false);
        }, 300); // 300ms delay
      }}
      style={{
        padding: '40px 0', // Add padding to extend hover area
        margin: '-40px 0', // Negative margin to compensate for padding
      }}
    >
    <Card
      className={`w-[300px] shadow-md bg-white border-2 ${selected ? 'ring-2 ring-offset-1' : ''}`}
      style={{
        borderColor: eventTypeColor,
        borderWidth: selected ? '3px' : '2px',
        boxShadow: selected ? `0 0 0 1px ${eventTypeColor}` : 'none'
      }}>
      {/* Colored header with node type */}
      <div className="text-white p-2 flex items-center justify-between rounded-t-md" style={{ backgroundColor: eventTypeColor }}>
        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Calendar Event</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0">
        <CardTitle className="text-sm font-medium">
          <Input
            placeholder="Event title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
        <div className="space-y-1">
          <Label className="text-xs">Event Type</Label>
          <Select
            value={eventType}
            onValueChange={(value) => {
              setEventType(value);
              handleChange('eventType', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select event type" />
            </SelectTrigger>
            <SelectContent>
              {EVENT_TYPES.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  <div className="flex items-center">
                    <div
                      className="w-2 h-2 rounded-full mr-2"
                      style={{ backgroundColor: type.color }}
                    ></div>
                    {type.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="nodrag w-full justify-start text-left font-normal h-7 text-xs"
              >
                <CalendarIcon className="mr-2 h-3 w-3" />
                {eventDate ? format(eventDate, 'PPP') : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 nodrag" align="start">
              <Calendar
                mode="single"
                selected={eventDate}
                onSelect={(date) => {
                  setEventDate(date);
                  handleChange('eventDate', date);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center space-x-2">
          <Label className="text-xs">All Day</Label>
          <Switch
            checked={isAllDay}
            onCheckedChange={(checked) => {
              setIsAllDay(checked);
              handleChange('isAllDay', checked);
            }}
            className="nodrag"
          />
        </div>

        {!isAllDay && (
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1">
              <Label className="text-xs">Start Time</Label>
              <Select
                value={startTime}
                onValueChange={(value) => {
                  setStartTime(value);
                  handleChange('startTime', value);
                }}
                disabled={isAllDay}
              >
                <SelectTrigger className="nodrag h-7 text-xs">
                  <SelectValue placeholder="Start time" />
                </SelectTrigger>
                <SelectContent>
                  {TIME_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">End Time</Label>
              <Select
                value={endTime}
                onValueChange={(value) => {
                  setEndTime(value);
                  handleChange('endTime', value);
                }}
                disabled={isAllDay}
              >
                <SelectTrigger className="nodrag h-7 text-xs">
                  <SelectValue placeholder="End time" />
                </SelectTrigger>
                <SelectContent>
                  {TIME_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <div className="space-y-1">
          <Label className="text-xs">Location</Label>
          <Input
            placeholder="Event location"
            value={location}
            onChange={(e) => {
              setLocation(e.target.value);
              handleChange('location', e.target.value);
            }}
            className="nodrag h-7 text-xs"
          />
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Details</Label>
          <Textarea
            placeholder="Event details"
            value={details}
            onChange={(e) => {
              setDetails(e.target.value);
              handleChange('details', e.target.value);
            }}
            className="nodrag text-xs h-20 resize-none"
          />
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Event Outcome</Label>
          <Select
            value={eventOutcome}
            onValueChange={(value) => {
              setEventOutcome(value);
              handleChange('eventOutcome', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select outcome" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="rescheduled">Rescheduled</SelectItem>
            </SelectContent>
          </Select>
          <div className="mt-1">
            <Badge
              variant={eventOutcome === 'pending' ? 'outline' :
                      eventOutcome === 'completed' ? 'green' :
                      eventOutcome === 'cancelled' ? 'destructive' :
                      eventOutcome === 'rescheduled' ? 'yellow' : 'secondary'}
              className="text-xs"
            >
              {eventOutcome.charAt(0).toUpperCase() + eventOutcome.slice(1)}
            </Badge>
          </div>
        </div>
        </CardContent>
      </div>

      {/* Handles for connecting nodes - one input on left, one output on right */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="primary"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />

      {/* Source handle for the floating edge */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Hidden but functional */
      />
    </Card>

    {/* Add notification button at bottom only - VISIBLE ON HOVER */}
    {nodeId && !hasNotificationNode && isHovered && (
      <>
        {/* BOTTOM BUTTON ONLY */}
        <div
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 z-50"
          style={{
            position: 'absolute',
            bottom: '-30px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 9999,
            pointerEvents: 'all',
            padding: '10px', // Add padding to make the clickable area larger
            margin: '-10px', // Negative margin to compensate for padding
            cursor: 'pointer' // Show pointer cursor to indicate clickability
          }}
          onMouseEnter={() => {
            // Clear any existing timeout when hovering over the button
            if (hoverTimeoutRef.current) {
              clearTimeout(hoverTimeoutRef.current);
              hoverTimeoutRef.current = null;
            }
            setIsHovered(true);
          }}
        >
          <button
            className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-500 text-white hover:bg-purple-600 shadow-lg border-2 border-white animate-pulse"
            style={{
              boxShadow: '0 0 15px 5px rgba(168, 85, 247, 0.8)',
              transition: 'all 0.3s ease'
            }}
            onClick={() => {
              if (!nodeId) return;

              // Generate a unique ID for the new notification node
              const newNodeId = `notification-${Date.now()}`;

              // Get the parent node to position the notification node relative to it
              const allNodes = getNodes();
              const parent = allNodes.find((node: Node) => node.id === nodeId);
              if (!parent) return;

              // Position the notification node BELOW the parent node
              const notificationNodePosition = {
                x: parent.position.x,
                y: parent.position.y + 750 // Position 2.5x lower (300 * 2.5 = 750)
              };

              // Create the notification node
              const notificationNode = {
                id: newNodeId,
                type: 'notificationNode',
                position: notificationNodePosition,
                data: {
                  id: newNodeId, // Explicitly set the ID in the data object
                  title: 'Send Notification',
                  notificationType: 'task_update',
                  content: 'Notification content',
                  recipients: 'all_members',
                  isExpanded: true,
                  parentNodeId: nodeId, // Store the parent node ID
                  connectedEdges: [{ source: nodeId }], // Store edge info for deletion
                  onDelete: (nodeId: string) => {
                    console.log("onDelete called with nodeId:", nodeId);
                    if (typeof data.onDelete === 'function') {
                      data.onDelete(newNodeId); // Use the newNodeId directly
                    }
                  }
                }
              };

              // Add the notification node to the flow
              setNodes((nodes) => [...nodes, notificationNode]);

              // Create a floating edge from the parent node to the notification node
              const newEdgeId = `edge-${nodeId}-${newNodeId}`;
              const newEdge = {
                id: newEdgeId,
                source: nodeId,
                target: newNodeId,
                sourceHandle: 'bottom', // Specify a source handle ID
                targetHandle: 'target', // Specify a target handle ID
                type: 'floating', // Use floating edge type
                animated: false,
                interactionWidth: 0, // Make edge non-interactive
                updatable: false, // Prevent edge from being updated
                deletable: false, // Prevent edge from being deleted
                style: {
                  stroke: '#9333ea',
                  strokeWidth: 3,
                  strokeDasharray: '5,5' // Dashed line for visual distinction
                }
              };

              // Add the edge to the flow
              setEdges((edges) => [...edges, newEdge]);

              // Update the hasNotificationNode state
              setHasNotificationNode(true);
            }}
            title="Add Notification Node Below"
          >
            <Bell className="h-5 w-5" />
          </button>
        </div>
      </>
    )}
    </div>
  );
}
