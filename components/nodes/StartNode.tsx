import { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>le, Position, NodeProps, useNodeId, useReactFlow, Node } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Play, Maximize2, Minimize2, Trash2, Bell } from 'lucide-react';
import { emitNodeUpdated } from '@/lib/workflow-events';

export default function StartNode({ data, isConnectable, selected }: NodeProps) {
  const nodeId = useNodeId();
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'Start Workflow');
  const [description, setDescription] = useState(typeof data.description === 'string' ? data.description : '');
  const [triggerType, setTriggerType] = useState<string>(
    typeof data.triggerType === 'string' ? data.triggerType : 'manual'
  );
  const [isHovered, setIsHovered] = useState(false); // Track hover state
  const [hasNotificationNode, setHasNotificationNode] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for hover timeout

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Check for existing notification nodes connected to this node
  useEffect(() => {
    if (!nodeId) return;

    const edges = getEdges();
    const connectedNotificationEdge = edges.find(edge =>
      edge.source === nodeId &&
      edge.target.startsWith('notification-')
    );

    setHasNotificationNode(!!connectedNotificationEdge);
    console.log('Node has notification:', !!connectedNotificationEdge, 'for node:', nodeId);

    // Listen for notification node deletion events
    const handleNotificationDeleted = (event: CustomEvent<{parentNodeId: string}>) => {
      const { parentNodeId } = event.detail;

      if (parentNodeId === nodeId) {
        console.log('Notification node deleted for parent:', nodeId);
        setHasNotificationNode(false);
      }
    };

    window.addEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);

    return () => {
      window.removeEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);
    };
  }, [nodeId, getEdges]);

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }

    // Emit an event for connected nodes to listen to
    emitNodeUpdated({
      nodeId: typeof data.id === 'string' ? data.id : '',
      nodeType: 'startNode',
      field,
      value
    });
  }, [data]);

  return (
    <div
      className="relative"
      onMouseEnter={() => {
        // Clear any existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
          hoverTimeoutRef.current = null;
        }
        setIsHovered(true);
      }}
      onMouseLeave={() => {
        // Add a delay before hiding the button
        hoverTimeoutRef.current = setTimeout(() => {
          setIsHovered(false);
        }, 300); // 300ms delay
      }}
      style={{
        padding: '40px 0', // Add padding to extend hover area
        margin: '-40px 0', // Negative margin to compensate for padding
      }}
    >
    <Card className={`w-[300px] shadow-md border-2 ${selected ? 'border-green-500 ring-2 ring-green-500' : 'border-green-200'} bg-white`}>
      {/* Colored header with node type */}
      <div className="bg-green-500 text-white p-2 flex items-center justify-between rounded-t-md">
        <div className="flex items-center">
          <Play className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Start</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-green-600"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-green-600"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0 flex flex-row items-center">
        <CardTitle className="text-sm font-medium flex-1">
          <Input
            placeholder="Start node title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
          <div className="space-y-1">
            <Label className="text-xs">Trigger Type</Label>
            <Select
              value={triggerType}
              onValueChange={(value) => {
                setTriggerType(value);
                handleChange('triggerType', value);
              }}
            >
              <SelectTrigger className="nodrag h-7 text-xs">
                <SelectValue placeholder="Select trigger type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">Manual Trigger</SelectItem>
                <SelectItem value="discovery_form">Discovery Form Submission</SelectItem>
                <SelectItem value="tpa_form">TPA Form Submission</SelectItem>
                <SelectItem value="risk_profiler">Risk Profiler Submission</SelectItem>
                <SelectItem value="webhook">External Webhook</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Description</Label>
            <Textarea
              placeholder="Describe what starts this workflow"
              value={description}
              onChange={(e) => {
                setDescription(e.target.value);
                handleChange('description', e.target.value);
              }}
              className="nodrag text-xs h-20 resize-none"
            />
          </div>
        </CardContent>
      </div>

      {/* Only output handle, no input handle for start node */}
      <Handle
        type="source"
        position={Position.Right}
        id="primary"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Source handle for the floating edge */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Hidden but functional */
      />
    </Card>

    {/* Add notification button at bottom only - VISIBLE ON HOVER */}
    {nodeId && !hasNotificationNode && isHovered && (
      <>
        {/* BOTTOM BUTTON ONLY */}
        <div
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 z-50"
          style={{
            position: 'absolute',
            bottom: '-30px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 9999,
            pointerEvents: 'all',
            padding: '10px', // Add padding to make the clickable area larger
            margin: '-10px', // Negative margin to compensate for padding
            cursor: 'pointer' // Show pointer cursor to indicate clickability
          }}
          onMouseEnter={() => {
            // Clear any existing timeout when hovering over the button
            if (hoverTimeoutRef.current) {
              clearTimeout(hoverTimeoutRef.current);
              hoverTimeoutRef.current = null;
            }
            setIsHovered(true);
          }}
        >
          <button
            className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-500 text-white hover:bg-purple-600 shadow-lg border-2 border-white animate-pulse"
            style={{
              boxShadow: '0 0 15px 5px rgba(168, 85, 247, 0.8)',
              transition: 'all 0.3s ease'
            }}
            onClick={() => {
              if (!nodeId) return;

              // Generate a unique ID for the new notification node
              const newNodeId = `notification-${Date.now()}`;

              // Get the parent node to position the notification node relative to it
              const allNodes = getNodes();
              const parent = allNodes.find((node: Node) => node.id === nodeId);
              if (!parent) return;

              // Position the notification node BELOW the parent node
              const notificationNodePosition = {
                x: parent.position.x,
                y: parent.position.y + 750 // Position 2.5x lower (300 * 2.5 = 750)
              };

              // Create the notification node
              const notificationNode = {
                id: newNodeId,
                type: 'notificationNode',
                position: notificationNodePosition,
                data: {
                  id: newNodeId, // Explicitly set the ID in the data object
                  title: 'Send Notification',
                  notificationType: 'task_update',
                  content: 'Notification content',
                  recipients: 'all_members',
                  isExpanded: true,
                  parentNodeId: nodeId, // Store the parent node ID
                  connectedEdges: [{ source: nodeId }], // Store edge info for deletion
                  onDelete: (nodeId: string) => {
                    console.log("onDelete called with nodeId:", nodeId);
                    if (typeof data.onDelete === 'function') {
                      data.onDelete(newNodeId); // Use the newNodeId directly
                    }
                  }
                }
              };

              // Add the notification node to the flow
              setNodes((nodes) => [...nodes, notificationNode]);

              // Create a floating edge from the parent node to the notification node
              const newEdgeId = `edge-${nodeId}-${newNodeId}`;
              const newEdge = {
                id: newEdgeId,
                source: nodeId,
                target: newNodeId,
                sourceHandle: 'bottom', // Specify a source handle ID
                targetHandle: 'target', // Specify a target handle ID
                type: 'floating', // Use floating edge type
                animated: false,
                interactionWidth: 0, // Make edge non-interactive
                updatable: false, // Prevent edge from being updated
                deletable: false, // Prevent edge from being deleted
                style: {
                  stroke: '#9333ea',
                  strokeWidth: 3,
                  strokeDasharray: '5,5' // Dashed line for visual distinction
                }
              };

              // Add the edge to the flow
              setEdges((edges) => [...edges, newEdge]);

              // Update the hasNotificationNode state
              setHasNotificationNode(true);
            }}
            title="Add Notification Node Below"
          >
            <Bell className="h-5 w-5" />
          </button>
        </div>
      </>
    )}
    </div>
  );
}
