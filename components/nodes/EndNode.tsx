import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Flag, Maximize2, Minimize2, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function EndNode({ data, isConnectable, selected }: NodeProps) {
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'Workflow Complete');
  const [summary, setSummary] = useState(typeof data.summary === 'string' ? data.summary : '');

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Update the node data when inputs change
  const handleChange = (field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }
  };

  return (
    <Card className={`w-[250px] shadow-md border-2 ${selected ? 'border-green-500 ring-2 ring-green-500' : 'border-green-300'} bg-white`}>
      {/* Colored header with node type */}
      <div className="bg-green-500 text-white p-2 flex items-center justify-between rounded-t-md">
        <div className="flex items-center">
          <Flag className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">End</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-green-600"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-green-600"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0 flex flex-row items-center">
        <CardTitle className="text-sm font-medium flex-1">
          <Input
            placeholder="End node title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
        <Badge variant="green" className="mb-2">Workflow End</Badge>

        <div className="space-y-1">
          <Label className="text-xs">Summary</Label>
          <Textarea
            placeholder="Workflow summary or completion notes"
            value={summary}
            onChange={(e) => {
              setSummary(e.target.value);
              handleChange('summary', e.target.value);
            }}
            className="nodrag text-xs h-20 resize-none"
          />
        </div>
        </CardContent>
      </div>

      {/* Only has a target handle on the left, no source handles */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="notification-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
    </Card>
  );
}
