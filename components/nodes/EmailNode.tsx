import { useState, useEffect, useCallback } from 'react';
import { <PERSON>le, Position, NodeProps } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Mail, Maximize2, Minimize2, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { emitNodeUpdated } from '@/lib/workflow-events';

// Email priority options with their colors
const EMAIL_PRIORITIES = [
  { value: "high", label: "High Priority", color: "#ef4444" }, // Red
  { value: "medium", label: "Medium Priority", color: "#f59e0b" }, // Amber
  { value: "low", label: "Low Priority", color: "#10b981" }, // Emerald
];

export default function EmailNode({ data, isConnectable, selected }: NodeProps) {
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'New Email');
  const [subject, setSubject] = useState(typeof data.subject === 'string' ? data.subject : '');
  const [recipients, setRecipients] = useState(typeof data.recipients === 'string' ? data.recipients : '');
  const [cc, setCc] = useState(typeof data.cc === 'string' ? data.cc : '');
  const [bcc, setBcc] = useState(typeof data.bcc === 'string' ? data.bcc : '');
  const [content, setContent] = useState(typeof data.content === 'string' ? data.content : '');
  const [priority, setPriority] = useState(typeof data.priority === 'string' ? data.priority : 'medium');
  const [status, setStatus] = useState(typeof data.status === 'string' ? data.status : 'draft');

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Get the current priority color
  const priorityColor = EMAIL_PRIORITIES.find(p => p.value === priority)?.color || "#f59e0b";

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }

    // Emit an event for connected nodes to listen to
    emitNodeUpdated({
      nodeId: typeof data.id === 'string' ? data.id : '',
      nodeType: 'emailNode',
      field,
      value
    });
  }, [data]);

  return (
    <Card
      className={`w-[300px] shadow-md bg-white border-2 ${selected ? 'ring-2 ring-offset-1' : ''}`}
      style={{
        borderColor: priorityColor,
        borderWidth: selected ? '3px' : '2px',
        boxShadow: selected ? `0 0 0 1px ${priorityColor}` : 'none'
      }}>
      {/* Colored header with node type */}
      <div className="text-white p-2 flex items-center justify-between rounded-t-md" style={{ backgroundColor: '#3b82f6' }}>
        <div className="flex items-center">
          <Mail className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Email</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0">
        <CardTitle className="text-sm font-medium">
          <Input
            placeholder="Email title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
          <div className="space-y-1">
            <Label className="text-xs">Subject</Label>
            <Input
              placeholder="Email subject"
              value={subject}
              onChange={(e) => {
                setSubject(e.target.value);
                handleChange('subject', e.target.value);
              }}
              className="nodrag h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Recipients</Label>
            <Input
              placeholder="To: <EMAIL>, ..."
              value={recipients}
              onChange={(e) => {
                setRecipients(e.target.value);
                handleChange('recipients', e.target.value);
              }}
              className="nodrag h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">CC</Label>
            <Input
              placeholder="CC: <EMAIL>, ..."
              value={cc}
              onChange={(e) => {
                setCc(e.target.value);
                handleChange('cc', e.target.value);
              }}
              className="nodrag h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">BCC</Label>
            <Input
              placeholder="BCC: <EMAIL>, ..."
              value={bcc}
              onChange={(e) => {
                setBcc(e.target.value);
                handleChange('bcc', e.target.value);
              }}
              className="nodrag h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Priority</Label>
            <Select
              value={priority}
              onValueChange={(value) => {
                setPriority(value);
                handleChange('priority', value);
              }}
            >
              <SelectTrigger className="nodrag h-7 text-xs">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                {EMAIL_PRIORITIES.map(p => (
                  <SelectItem key={p.value} value={p.value}>
                    <div className="flex items-center">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: p.color }}
                      ></div>
                      {p.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Status</Label>
            <Select
              value={status}
              onValueChange={(value) => {
                setStatus(value);
                handleChange('status', value);
              }}
            >
              <SelectTrigger className="nodrag h-7 text-xs">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <div className="mt-1">
              <Badge
                variant={status === 'draft' ? 'outline' :
                        status === 'scheduled' ? 'secondary' :
                        status === 'sent' ? 'green' :
                        status === 'failed' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            </div>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Content</Label>
            <Textarea
              placeholder="Email content"
              value={content}
              onChange={(e) => {
                setContent(e.target.value);
                handleChange('content', e.target.value);
              }}
              className="nodrag text-xs h-32 resize-none"
            />
          </div>
        </CardContent>
      </div>

      {/* Handles for connecting nodes - one input on left, one output on right */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="primary"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="notification-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />

      {/* Source handle for the floating edge */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Hidden but functional */
      />
    </Card>
  );
}
