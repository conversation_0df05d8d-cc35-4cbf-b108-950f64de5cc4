import { useState, useEffect, useCallback } from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { FileText, Maximize2, Minimize2, Trash2, Mail } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { emitNodeUpdated } from '@/lib/workflow-events';
import { createClient } from '@/utils/supabase/client';

// Document type options with their colors
const DOCUMENT_TYPES = [
  { value: "discovery", label: "Discovery Document", color: "#4f46e5" }, // Indigo
  { value: "toe", label: "Terms of Engagement", color: "#0ea5e9" }, // Sky
  { value: "tpa", label: "Third Party Authority", color: "#10b981" }, // Emerald
  { value: "risk", label: "Risk Profiler", color: "#f59e0b" }, // Amber
  { value: "financial", label: "Financial Summary", color: "#ef4444" }, // Red
];

interface Template {
  id: string;
  title: string;
  type: string;
}

export default function DocumentEventNode({ data, isConnectable, selected }: NodeProps) {
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'Send Document');
  const [documentType, setDocumentType] = useState(typeof data.documentType === 'string' ? data.documentType : "discovery");
  const [emailSubject, setEmailSubject] = useState(typeof data.emailSubject === 'string' ? data.emailSubject : "");
  const [emailBody, setEmailBody] = useState(typeof data.emailBody === 'string' ? data.emailBody : "");
  const [passwordProtected, setPasswordProtected] = useState(typeof data.passwordProtected === 'boolean' ? data.passwordProtected : false);
  const [selectedTemplate, setSelectedTemplate] = useState(typeof data.selectedTemplate === 'string' ? data.selectedTemplate : "default");
  const [templates, setTemplates] = useState<Template[]>([]);

  // Add event outcome state for decision node integration
  const [eventOutcome, setEventOutcome] = useState<string>(
    typeof data.eventOutcome === 'string' ? data.eventOutcome : 'pending'
  );

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);

  // Fetch templates when document type changes
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const supabase = createClient();
        let templateType = '';

        switch (documentType) {
          case 'discovery':
            templateType = 'Discovery Document';
            break;
          case 'toe':
            templateType = 'Terms of Engagement';
            break;
          case 'risk':
            templateType = 'Risk Profiler';
            break;
          default:
            templateType = '';
        }

        if (templateType) {
          const { data: templatesData, error } = await supabase
            .from('templates')
            .select('id, title, type')
            .eq('type', templateType)
            .order('created_at', { ascending: false });

          if (error) {
            console.error(`Error fetching ${templateType} templates:`, error);
          } else {
            setTemplates(templatesData || []);
            // Reset selected template to default if current selection is not valid
            if (selectedTemplate !== 'default' && !templatesData?.some(t => t.id === selectedTemplate)) {
              setSelectedTemplate('default');
              handleChange('selectedTemplate', 'default');
            }
          }
        } else {
          setTemplates([]);
          setSelectedTemplate('default');
          handleChange('selectedTemplate', 'default');
        }
      } catch (error) {
        console.error('Error fetching templates:', error);
        setTemplates([]);
      }
    };

    fetchTemplates();
  }, [documentType]);

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Get the current document type color
  const documentTypeColor = DOCUMENT_TYPES.find(type => type.value === documentType)?.color || "#6b7280";

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }

    // Emit an event for connected nodes to listen to
    emitNodeUpdated({
      nodeId: typeof data.id === 'string' ? data.id : '',
      nodeType: 'documentEventNode',
      field,
      value
    });
  }, [data]);

  return (
    <Card
      className={`w-[300px] shadow-md bg-white border-2 ${selected ? 'ring-2 ring-offset-1' : ''}`}
      style={{
        borderColor: documentTypeColor,
        borderWidth: selected ? '3px' : '2px',
        boxShadow: selected ? `0 0 0 1px ${documentTypeColor}` : 'none'
      }}>
      {/* Colored header with node type */}
      <div className="text-white p-2 flex items-center justify-between rounded-t-md" style={{ backgroundColor: documentTypeColor }}>
        <div className="flex items-center">
          <FileText className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Document</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-opacity-20 hover:bg-white"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0">
        <CardTitle className="text-sm font-medium">
          <Input
            placeholder="Document event title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
          <div className="space-y-1">
            <Label className="text-xs">Document Type</Label>
            <Select
              value={documentType}
              onValueChange={(value) => {
                setDocumentType(value);
                handleChange('documentType', value);
              }}
            >
              <SelectTrigger className="nodrag h-7 text-xs">
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {DOCUMENT_TYPES.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: type.color }}
                      ></div>
                      {type.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Template selection - only show for document types that support templates */}
          {(documentType === 'discovery' || documentType === 'toe' || documentType === 'risk') && (
            <div className="space-y-1">
              <Label className="text-xs">Template</Label>
              <Select
                value={selectedTemplate}
                onValueChange={(value) => {
                  setSelectedTemplate(value);
                  handleChange('selectedTemplate', value);
                }}
              >
                <SelectTrigger className="nodrag h-7 text-xs">
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default Template</SelectItem>
                  {templates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}



          <div className="space-y-1">
            <Label className="text-xs">Email Subject</Label>
            <Input
              placeholder="Email subject"
              value={emailSubject}
              onChange={(e) => {
                setEmailSubject(e.target.value);
                handleChange('emailSubject', e.target.value);
              }}
              className="nodrag h-7 text-xs"
            />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Email Body</Label>
            <Textarea
              placeholder="Email body"
              value={emailBody}
              onChange={(e) => {
                setEmailBody(e.target.value);
                handleChange('emailBody', e.target.value);
              }}
              className="nodrag text-xs h-20 resize-none"
            />
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Switch
              checked={passwordProtected}
              onCheckedChange={(checked) => {
                setPasswordProtected(checked);
                handleChange('passwordProtected', checked);
              }}
              className="nodrag"
            />
            <Label className="text-xs">Password Protected</Label>
          </div>

          <div className="space-y-1 pt-2">
            <Label className="text-xs">Event Outcome</Label>
            <Select
              value={eventOutcome}
              onValueChange={(value) => {
                setEventOutcome(value);
                handleChange('eventOutcome', value);
              }}
            >
              <SelectTrigger className="nodrag h-7 text-xs">
                <SelectValue placeholder="Select outcome" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="opened">Opened</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
            <div className="mt-1">
              <Badge
                variant={eventOutcome === 'pending' ? 'outline' :
                        eventOutcome === 'sent' ? 'blue' :
                        eventOutcome === 'opened' ? 'yellow' :
                        eventOutcome === 'completed' ? 'green' :
                        eventOutcome === 'expired' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {eventOutcome.charAt(0).toUpperCase() + eventOutcome.slice(1)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </div>

      {/* Handles for connecting nodes - one input on left, one output on right */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="primary"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="notification-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
    </Card>
  );
}
