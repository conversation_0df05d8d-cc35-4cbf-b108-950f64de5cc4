import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Bell, Maximize2, Minimize2, Trash2 } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Notification types with their badge variants
const NOTIFICATION_TYPES = [
  { value: 'task_update', label: 'Task Update', variant: 'purple' },
  { value: 'task_completion', label: 'Task Completion', variant: 'green' },
  { value: 'event_reminder', label: 'Event Reminder', variant: 'blue' },
  { value: 'workflow_update', label: 'Workflow Update', variant: 'yellow' },
  { value: 'approval_required', label: 'Approval Required', variant: 'destructive' },
  { value: 'custom', label: 'Custom Notification', variant: 'secondary' },
] as const;

export default function NotificationNode({ data, isConnectable, selected }: NodeProps) {
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : 'New Notification');
  const [notificationType, setNotificationType] = useState(
    typeof data.notificationType === 'string' ? data.notificationType : 'task_update'
  );
  const [content, setContent] = useState(
    typeof data.content === 'string' ? data.content : 'Notification content'
  );
  const [recipients, setRecipients] = useState(
    typeof data.recipients === 'string' ? data.recipients : 'all_members'
  );

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Get the current notification type badge variant
  const getNotificationBadgeVariant = () => {
    const type = NOTIFICATION_TYPES.find(t => t.value === notificationType);
    return type?.variant || 'secondary';
  };

  // Update the node data when inputs change
  const handleChange = (field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }
  };

  return (
    <Card className={`w-[250px] shadow-md border-2 ${selected ? 'border-purple-500 ring-2 ring-purple-500' : 'border-purple-200'} bg-white`}>
      {/* Colored header with node type */}
      <div className="bg-purple-500 text-white p-2 flex items-center justify-between rounded-t-md">
        <div className="flex items-center">
          <Bell className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Notification</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-purple-600"
            onClick={() => {
              // Get the node ID from the data object or from the node itself
              const nodeId = data.id || data.nodeId;
              console.log("Delete button clicked for notification node", nodeId);

              // Find and remove the edge connecting this notification node to its parent
              if (nodeId) {
                console.log("Notification node ID:", nodeId);
                console.log("Parent node ID:", data.parentNodeId);

                // We'll just use the node ID to identify the parent
                // Find all edges connected to this node from the data object
                const connectedEdges = Array.isArray(data.connectedEdges) ? data.connectedEdges : [];

                // Emit a custom event to notify the parent node that the notification was deleted
                if (data.parentNodeId) {
                  console.log("Dispatching notification-node-deleted event for parent:", data.parentNodeId);
                  window.dispatchEvent(new CustomEvent('notification-node-deleted', {
                    detail: { parentNodeId: data.parentNodeId }
                  }));
                } else if (connectedEdges.length > 0) {
                  const parentNodeId = connectedEdges[0].source;
                  console.log("Dispatching notification-node-deleted event for parent from edges:", parentNodeId);
                  window.dispatchEvent(new CustomEvent('notification-node-deleted', {
                    detail: { parentNodeId }
                  }));
                }

                // Delete the node
                if (typeof data.onDelete === 'function') {
                  console.log("Calling onDelete function with ID:", nodeId);
                  data.onDelete(nodeId);
                } else {
                  console.error("onDelete function is not available");
                }
              } else {
                console.error("No node ID available for deletion");
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-purple-600"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0 flex flex-row items-center">
        <CardTitle className="text-sm font-medium flex-1">
          <Input
            placeholder="Notification title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
        <div className="space-y-1">
          <Label className="text-xs">Notification Type</Label>
          <Select
            value={notificationType}
            onValueChange={(value) => {
              setNotificationType(value);
              handleChange('notificationType', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select notification type" />
            </SelectTrigger>
            <SelectContent>
              {NOTIFICATION_TYPES.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  <div className="flex items-center">
                    <Badge variant={type.variant} className="mr-2 h-2 w-2 p-0"></Badge>
                    {type.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Content</Label>
          <Textarea
            placeholder="Notification content"
            value={content}
            onChange={(e) => {
              setContent(e.target.value);
              handleChange('content', e.target.value);
            }}
            className="nodrag text-xs h-20 resize-none"
          />
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Recipients</Label>
          <Select
            value={recipients}
            onValueChange={(value) => {
              setRecipients(value);
              handleChange('recipients', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select recipients" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_members">All Team Members</SelectItem>
              <SelectItem value="task_assignee">Task Assignee</SelectItem>
              <SelectItem value="event_attendees">Event Attendees</SelectItem>
              <SelectItem value="specific_users">Specific Users</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="mt-2">
          <Badge variant={getNotificationBadgeVariant()} className="text-xs">
            {NOTIFICATION_TYPES.find(t => t.value === notificationType)?.label || 'Notification'}
          </Badge>
        </div>
        </CardContent>
      </div>

      {/* Handles for connecting to other nodes - dynamically positioned */}
      <Handle
        type="source"
        position={Position.Top}
        id="source-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="source-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
      />
      {/* Add a target handle for the floating edge */}
      <Handle
        type="target"
        position={Position.Top}
        id="target"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} // Hide it visually but keep it functional
      />
    </Card>
  );
}
