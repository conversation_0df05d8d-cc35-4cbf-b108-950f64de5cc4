import { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>le, Position, NodeProps, useNodeId, useReactFlow, Node } from '@xyflow/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, CheckSquare, Maximize2, Minimize2, Trash2, Settings, Bell } from 'lucide-react';
import { format } from 'date-fns';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import TaskOutcomesModal from '@/components/modals/TaskOutcomesModal';
import { emitNodeUpdated, EDGE_CREATED_EVENT, EdgeEventData } from '@/lib/workflow-events';

interface TaskOutcome {
  id: string;
  label: string;
  color: string;
}

export default function TaskNode({ data, isConnectable, selected }: NodeProps) {
  const nodeId = useNodeId();
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();
  const [isExpanded, setIsExpanded] = useState(data.isExpanded !== false); // Default to expanded unless explicitly set to false
  const [isHovered, setIsHovered] = useState(false); // Track hover state
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for hover timeout

  // Update isExpanded when data.isExpanded changes
  useEffect(() => {
    if (data.isExpanded !== undefined) {
      setIsExpanded(!!data.isExpanded);
    }
  }, [data.isExpanded]);
  const [title, setTitle] = useState(typeof data.title === 'string' ? data.title : '');
  const [dueDateOption, setDueDateOption] = useState<string>(typeof data.dueDateOption === 'string' ? data.dueDateOption : '1day');
  const [customDueDate, setCustomDueDate] = useState<Date | undefined>(
    data.customDueDate && typeof data.customDueDate === 'string' ? new Date(data.customDueDate) : undefined
  );
  const [showCustomDate, setShowCustomDate] = useState(dueDateOption === 'custom');
  const [importance, setImportance] = useState<string>(
    typeof data.importance === 'string' ? data.importance : 'medium'
  );
  const [status, setStatus] = useState<string>(
    typeof data.status === 'string' ? data.status : 'not started'
  );
  // Removed nextAction state as it's no longer needed
  const [content, setContent] = useState<string>(
    typeof data.content === 'string' ? data.content : ''
  );
  const [assignee, setAssignee] = useState<string>(
    typeof data.assignee === 'string' ? data.assignee : ''
  );
  const [outcome, setOutcome] = useState<string>(
    typeof data.outcome === 'string' ? data.outcome : 'pending'
  );

  // Default outcomes to use if none are provided
  const defaultOutcomes: TaskOutcome[] = [
    { id: 'pending', label: 'Pending', color: 'gray' },
    { id: 'completed', label: 'Completed', color: 'green' },
    { id: 'approved', label: 'Approved', color: 'blue' },
    { id: 'rejected', label: 'Rejected', color: 'red' },
    { id: 'deferred', label: 'Deferred', color: 'yellow' },
    { id: 'cancelled', label: 'Cancelled', color: 'secondary' }
  ];

  // State for custom outcomes
  const [outcomes, setOutcomes] = useState<TaskOutcome[]>(
    Array.isArray(data.outcomes) ? data.outcomes : defaultOutcomes
  );

  // State for outcomes modal
  const [isOutcomesModalOpen, setIsOutcomesModalOpen] = useState(false);

  // Toggle expanded/collapsed state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    handleChange('isExpanded', !isExpanded);
  };

  // Update the node data when inputs change
  const handleChange = useCallback((field: string, value: any) => {
    if (data.onChange && typeof data.onChange === 'function') {
      data.onChange(field, value);
    }

    // Emit an event for connected nodes to listen to
    emitNodeUpdated({
      nodeId: typeof data.id === 'string' ? data.id : '',
      nodeType: 'taskNode',
      field,
      value
    });
  }, [data]);

  // Handle due date option change
  useEffect(() => {
    setShowCustomDate(dueDateOption === 'custom');
    handleChange('dueDateOption', dueDateOption);

    if (dueDateOption !== 'custom') {
      handleChange('customDueDate', undefined);
    }
  }, [dueDateOption]);

  // Handle custom due date change
  useEffect(() => {
    if (showCustomDate && customDueDate) {
      handleChange('customDueDate', customDueDate);
    }
  }, [customDueDate, showCustomDate, handleChange]);

  // Ensure outcomes are saved to node data when component mounts
  useEffect(() => {
    // If outcomes aren't already saved in the node data, save the default ones
    if (!Array.isArray(data.outcomes)) {
      handleChange('outcomes', outcomes);

      // Emit an event to notify connected decision nodes about the outcomes
      setTimeout(() => {
        emitNodeUpdated({
          nodeId: typeof data.id === 'string' ? data.id : '',
          nodeType: 'taskNode',
          field: 'outcomes',
          value: outcomes
        });
      }, 0);
    }
  }, [data.id, data.outcomes, outcomes, handleChange]);

  // Store the current node ID in a ref for event handlers
  const nodeIdRef = useRef(data.id);

  // Update the ref when the node ID changes
  useEffect(() => {
    nodeIdRef.current = data.id;
  }, [data.id]);

  // Listen for edge creation events to emit outcomes when connected to a decision node
  useEffect(() => {
    const handleEdgeCreated = (event: CustomEvent<EdgeEventData>) => {
      const { source } = event.detail;

      // If this node is the source of a new connection
      if (source === nodeIdRef.current) {
        // Emit outcomes to update the connected decision node
        setTimeout(() => {
          emitNodeUpdated({
            nodeId: typeof data.id === 'string' ? data.id : '',
            nodeType: 'taskNode',
            field: 'outcomes',
            value: outcomes
          });
        }, 50); // Small delay to ensure the connection is fully established
      }
    };

    // Add event listener
    window.addEventListener(EDGE_CREATED_EVENT, handleEdgeCreated as EventListener);

    // Clean up event listener
    return () => {
      window.removeEventListener(EDGE_CREATED_EVENT, handleEdgeCreated as EventListener);
    };
  }, [data.id, outcomes]);

  // Check if this node already has a connected notification node
  const [hasNotificationNode, setHasNotificationNode] = useState(false);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Check for existing notification nodes connected to this node
  useEffect(() => {
    if (!nodeId) return;

    const edges = getEdges();
    const connectedNotificationEdge = edges.find(edge =>
      edge.source === nodeId &&
      edge.target.startsWith('notification-')
    );

    setHasNotificationNode(!!connectedNotificationEdge);
    console.log('Node has notification:', !!connectedNotificationEdge, 'for node:', nodeId);

    // Listen for notification node deletion events
    const handleNotificationDeleted = (event: CustomEvent<{parentNodeId: string}>) => {
      const { parentNodeId } = event.detail;

      if (parentNodeId === nodeId) {
        console.log('Notification node deleted for parent:', nodeId);
        setHasNotificationNode(false);
      }
    };

    window.addEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);

    return () => {
      window.removeEventListener('notification-node-deleted', handleNotificationDeleted as EventListener);
    };
  }, [nodeId, getEdges]);

  return (
    <div
      className="relative"
      onMouseEnter={() => {
        // Clear any existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
          hoverTimeoutRef.current = null;
        }
        setIsHovered(true);
      }}
      onMouseLeave={() => {
        // Add a delay before hiding the button
        hoverTimeoutRef.current = setTimeout(() => {
          setIsHovered(false);
        }, 300); // 300ms delay
      }}
      style={{
        padding: '40px 0', // Add padding to extend hover area
        margin: '-40px 0', // Negative margin to compensate for padding
      }}
    >
    <Card
      className={`w-[300px] shadow-md border-2 ${selected ? 'border-blue-500 ring-2 ring-blue-500' : 'border-blue-200'} bg-white relative`}
    >
      {/* Colored header with node type */}
      <div className="bg-blue-500 text-white p-2 flex items-center justify-between rounded-t-md">
        <div className="flex items-center">
          <CheckSquare className="h-4 w-4 mr-2" />
          <span className="text-xs font-medium">Task</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-blue-600"
            onClick={() => {
              if (typeof data.onDelete === 'function' && data.id) {
                data.onDelete(data.id);
              }
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 text-white hover:bg-blue-600"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      <CardHeader className="p-3 pb-0 flex flex-row items-center">
        <CardTitle className="text-sm font-medium flex-1">
          <Input
            placeholder="Task title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              handleChange('title', e.target.value);
            }}
            className="nodrag h-7 text-sm"
          />
        </CardTitle>
      </CardHeader>

      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-[10px] opacity-0 pb-3'}`}>
        <CardContent className="p-3 space-y-2">
        <div className="space-y-1">
          <Label className="text-xs">Due Date</Label>
          <Select
            value={dueDateOption}
            onValueChange={(value) => {
              setDueDateOption(value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select due date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1day">1 day after</SelectItem>
              <SelectItem value="3days">3 days after</SelectItem>
              <SelectItem value="1week">1 week after</SelectItem>
              <SelectItem value="2weeks">2 weeks after</SelectItem>
              <SelectItem value="1month">1 month after</SelectItem>
              <SelectItem value="custom">Custom date</SelectItem>
            </SelectContent>
          </Select>

          {showCustomDate && (
            <div className="mt-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="nodrag w-full justify-start text-left font-normal h-7 text-xs"
                  >
                    <CalendarIcon className="mr-2 h-3 w-3" />
                    {customDueDate ? format(customDueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 nodrag" align="start">
                  <Calendar
                    mode="single"
                    selected={customDueDate}
                    onSelect={setCustomDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Task Content</Label>
          <Textarea
            placeholder="Task description and instructions"
            value={content}
            onChange={(e) => {
              setContent(e.target.value);
              handleChange('content', e.target.value);
            }}
            className="nodrag text-xs h-20 resize-none"
          />
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Assignee</Label>
          <Select
            value={assignee}
            onValueChange={(value) => {
              setAssignee(value);
              handleChange('assignee', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select assignee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="user1">User 1</SelectItem>
              <SelectItem value="user2">User 2</SelectItem>
              <SelectItem value="user3">User 3</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Importance</Label>
          <Select
            value={importance}
            onValueChange={(value) => {
              setImportance(value);
              handleChange('importance', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select importance" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1">
          <Label className="text-xs">Status</Label>
          <Select
            value={status}
            onValueChange={(value) => {
              setStatus(value);
              handleChange('status', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="not started">Not Started</SelectItem>
              <SelectItem value="in progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Next Action section removed as requested */}

        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <Label className="text-xs">Outcome</Label>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => setIsOutcomesModalOpen(true)}
            >
              <Settings className="h-3 w-3 mr-1" />
              Edit Outcomes
            </Button>
          </div>
          <Select
            value={outcome}
            onValueChange={(value) => {
              setOutcome(value);
              handleChange('outcome', value);
            }}
          >
            <SelectTrigger className="nodrag h-7 text-xs">
              <SelectValue placeholder="Select outcome" />
            </SelectTrigger>
            <SelectContent>
              {outcomes.map(outcomeOption => (
                <SelectItem key={outcomeOption.id} value={outcomeOption.id}>
                  {outcomeOption.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="mt-1">
            {outcomes.map(outcomeOption => {
              if (outcomeOption.id === outcome) {
                return (
                  <Badge
                    key={outcomeOption.id}
                    variant={outcomeOption.color === 'green' ? 'green' :
                            outcomeOption.color === 'blue' ? 'blue' :
                            outcomeOption.color === 'red' ? 'destructive' :
                            outcomeOption.color === 'yellow' ? 'yellow' :
                            outcomeOption.color === 'gray' ? 'outline' : 'secondary'}
                    className="text-xs"
                  >
                    {outcomeOption.label}
                  </Badge>
                );
              }
              return null;
            })}
          </div>
        </div>
        </CardContent>
      </div>

      {/* Task Outcomes Modal */}
      <TaskOutcomesModal
        isOpen={isOutcomesModalOpen}
        onClose={() => setIsOutcomesModalOpen(false)}
        outcomes={outcomes}
        onSave={(newOutcomes) => {
          setOutcomes(newOutcomes);
          handleChange('outcomes', newOutcomes);

          // If the current outcome is no longer in the list, reset to pending
          if (!newOutcomes.some(o => o.id === outcome)) {
            setOutcome('pending');
            handleChange('outcome', 'pending');
          }

          // Explicitly emit an event for the outcomes change to ensure connected nodes update
          setTimeout(() => {
            emitNodeUpdated({
              nodeId: typeof data.id === 'string' ? data.id : '',
              nodeType: 'taskNode',
              field: 'outcomes',
              value: newOutcomes
            });
          }, 0);
        }}
        nodeId={typeof data.id === 'string' ? data.id : ''}
      />

      {/* Handles for connecting nodes - one input on left, one output on right */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="primary"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-green-500"
      />

      {/* Additional connection points for notification nodes */}
      <Handle
        type="target"
        position={Position.Top}
        id="notification-top"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="notification-bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Initially hidden, will be shown dynamically */
      />

      {/* Add source handle for the floating edge (bottom only) */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-purple-500"
        style={{ visibility: 'hidden' }} /* Hidden but functional */
      />

      {/* Add notification button at bottom only - VISIBLE ON HOVER */}
      {nodeId && !hasNotificationNode && isHovered && (
        <>
          {/* BOTTOM BUTTON ONLY */}
          <div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 z-50"
            style={{
              position: 'absolute',
              bottom: '-30px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 9999,
              pointerEvents: 'all',
              padding: '10px', // Add padding to make the clickable area larger
              margin: '-10px', // Negative margin to compensate for padding
              cursor: 'pointer' // Show pointer cursor to indicate clickability
            }}
            onMouseEnter={() => {
              // Clear any existing timeout when hovering over the button
              if (hoverTimeoutRef.current) {
                clearTimeout(hoverTimeoutRef.current);
                hoverTimeoutRef.current = null;
              }
              setIsHovered(true);
            }}
          >
            <button
              className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-500 text-white hover:bg-purple-600 shadow-lg border-2 border-white animate-pulse"
              style={{
                boxShadow: '0 0 15px 5px rgba(168, 85, 247, 0.8)',
                transition: 'all 0.3s ease'
              }}
              onClick={() => {
                if (!nodeId) return;

                // Generate a unique ID for the new notification node
                const newNodeId = `notification-${Date.now()}`;

                // Get the parent node to position the notification node relative to it
                const allNodes = getNodes();
                const parent = allNodes.find((node: Node) => node.id === nodeId);
                if (!parent) return;

                // Position the notification node BELOW the parent node
                const notificationNodePosition = {
                  x: parent.position.x,
                  y: parent.position.y + 750 // Position 2.5x lower (300 * 2.5 = 750)
                };

                // Create the notification node
                const notificationNode = {
                  id: newNodeId,
                  type: 'notificationNode',
                  position: notificationNodePosition,
                  data: {
                    id: newNodeId, // Explicitly set the ID in the data object
                    title: 'Send Notification',
                    notificationType: 'task_update',
                    content: 'Notification content',
                    recipients: 'all_members',
                    isExpanded: true,
                    parentNodeId: nodeId, // Store the parent node ID
                    connectedEdges: [{ source: nodeId }], // Store edge info for deletion
                    onDelete: (nodeId: string) => {
                      console.log("onDelete called with nodeId:", nodeId);
                      if (typeof data.onDelete === 'function') {
                        data.onDelete(newNodeId); // Use the newNodeId directly
                      }
                    }
                  }
                };

                // Add the notification node to the flow
                setNodes((nodes) => [...nodes, notificationNode]);

                // Create a floating edge from the parent node to the notification node
                const newEdgeId = `edge-${nodeId}-${newNodeId}`;
                const newEdge = {
                  id: newEdgeId,
                  source: nodeId,
                  target: newNodeId,
                  sourceHandle: 'bottom', // Specify a source handle ID
                  targetHandle: 'target', // Specify a target handle ID
                  type: 'floating', // Use floating edge type
                  animated: false,
                  interactionWidth: 0, // Make edge non-interactive
                  updatable: false, // Prevent edge from being updated
                  deletable: false, // Prevent edge from being deleted
                  style: {
                    stroke: '#9333ea',
                    strokeWidth: 3,
                    strokeDasharray: '5,5' // Dashed line for visual distinction
                  }
                };

                // Add the edge to the flow
                setEdges((edges) => [...edges, newEdge]);

                // Update the hasNotificationNode state
                setHasNotificationNode(true);
              }}
              title="Add Notification Node Below"
            >
              <Bell className="h-5 w-5" />
            </button>
          </div>
        </>
      )}
    </Card>
    </div>
  );
}
