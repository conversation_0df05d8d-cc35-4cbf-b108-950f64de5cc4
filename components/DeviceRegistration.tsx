'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { generateDeviceId, getDeviceName, getUserAgent } from '@/utils/device-id';
import { registerDeviceAction } from '@/app/actions';
import { useRouter } from 'next/navigation';

export default function DeviceRegistration() {
  const [isRegistering, setIsRegistering] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // Check if we've already registered this device in this session
    if (isRegistered) {
      return;
    }

    const registerCurrentDevice = async () => {
      try {
        // Check if user is authenticated
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
          console.log('User not authenticated, skipping device registration');
          return;
        }

        // Generate device ID and name
        const deviceId = generateDeviceId();
        const deviceName = getDeviceName();
        const userAgent = getUserAgent();

        if (!deviceId) {
          console.error('Failed to generate device ID');
          return;
        }

        console.log('Registering device:', {
          deviceId,
          deviceName,
          userAgent: userAgent.substring(0, 50) + '...' // Truncate for readability
        });

        // Check if this device is already registered for this user
        const { data: existingDevice, error: deviceError } = await supabase
          .from('user_devices')
          .select('*')
          .eq('user_id', user.id)
          .eq('device_id', deviceId)
          .maybeSingle();

        if (deviceError) {
          console.error('Error checking if device is registered:', deviceError);
          return;
        }

        // If device is already registered, just update last_active
        if (existingDevice) {
          console.log('Device already registered, updating last_active');

          const { error: updateError } = await supabase
            .from('user_devices')
            .update({
              last_active: new Date().toISOString(),
              is_current: true
            })
            .eq('id', existingDevice.id);

          if (updateError) {
            console.error('Error updating device last_active:', updateError);
          } else {
            // Set all other devices as not current
            await supabase
              .from('user_devices')
              .update({ is_current: false })
              .eq('user_id', user.id)
              .neq('id', existingDevice.id);

            setIsRegistered(true);
          }
          return;
        }

        setIsRegistering(true);

        // Register the device
        const formData = new FormData();
        formData.append('deviceId', deviceId);
        formData.append('deviceName', deviceName);
        formData.append('userAgent', userAgent);
        formData.append('returnPath', window.location.pathname);

        const result = await registerDeviceAction(formData);
        console.log('Device registration result:', result);
        setIsRegistered(true);
      } catch (error) {
        console.error('Error registering device:', error);
      } finally {
        setIsRegistering(false);
      }
    };

    // Register device on component mount
    registerCurrentDevice();
  }, [isRegistered]);

  // This component doesn't render anything visible
  return null;
}
