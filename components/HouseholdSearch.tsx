

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { searchHouseholds } from './sidebars/search'; // Import search function only
import type { Household, Scenario } from './sidebars/search'; // Import types
import { debounce } from 'lodash'; // Import debounce from lodash
import { Input } from '@/components/ui/input';
import { Loader2, Search, X } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation'; // Import useRouter

interface HouseholdSearchProps {
  // onSelect is no longer needed based on the new requirements
  // Consider adding onSelectScenario if different behavior is needed
}

interface SearchResults {
  households: Household[];
  scenarios: Scenario[];
}

export const HouseholdSearch: React.FC<HouseholdSearchProps> = () => { // Remove onSelect from parameters
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResults>({ households: [], scenarios: [] });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter(); // Initialize router

  // Use the imported search function
  const performSearch = useCallback(async (term: string) => {
    if (term.length < 2) {
      setSearchResults({ households: [], scenarios: [] });
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const results = await searchHouseholds(term);
      setSearchResults(results);
    } catch (err) {
      console.error('Error performing search:', err);
      setError('Failed to perform search');
      setSearchResults({ households: [], scenarios: [] });
    } finally {
      setLoading(false);
    }
  }, []);

  // Use lodash debounce directly
  const debouncedPerformSearch = useCallback(debounce(performSearch, 300), [performSearch]);

  useEffect(() => {
    debouncedPerformSearch(searchTerm);
    // Cleanup function for lodash debounce
    return () => {
      debouncedPerformSearch.cancel();
    };
  }, [searchTerm, debouncedPerformSearch]);

  const clearSearch = () => {
    setSearchTerm('');
    setSearchResults({ households: [], scenarios: [] });
    setError(null);
  };

  // Function to handle selecting a household
  const handleSelectHousehold = (household: Household) => {
    router.push(`/protected/households/household/${household.id}/household_overview`);
    clearSearch();
  };

  // Function to handle selecting a scenario (opens planner in new tab)
  const handleSelectScenario = (scenario: Scenario) => {
    const plannerUrl = `/protected/planner?scenarioId=${scenario.id}`;
    window.open(plannerUrl, '_blank'); // Open in new tab
    clearSearch();
  };

  return (
    <div className="relative w-full">
      {/* Input stays the same */}
      <div className="relative">
        <Input
          type="text"
          placeholder="Search households or scenarios..." // Updated placeholder
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pr-10"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          ) : searchTerm ? (
            <X
              className="h-4 w-4 text-muted-foreground cursor-pointer hover:text-destructive"
              onClick={clearSearch}
            />
          ) : (
            <Search className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      </div>

      {/* Error display stays the same */}
      {error && (
        <div className="text-sm text-destructive mt-1 pl-1">
          {error}
        </div>
      )}

      {/* Results Display Logic */}
      {!loading && searchTerm.length >= 2 && (searchResults.households.length > 0 || searchResults.scenarios.length > 0) && (
        // Added min-w-[450px] for wider container and right-0 to align right edge
        <div className="absolute right-0 z-50 mt-1 w-auto min-w-[450px] bg-white border rounded-md shadow-lg max-h-80 overflow-y-auto"> 
          <div className="flex">
            {/* Households Column */}
            {searchResults.households.length > 0 && (
              <div className={`flex-1 ${searchResults.scenarios.length > 0 ? 'border-r' : ''}`}>
                <div className="px-3 py-1 text-xs font-semibold text-muted-foreground sticky top-0 bg-white border-b">Households</div>
                {searchResults.households.map((household) => (
                  <div
                    key={`hh-${household.id}`}
                    className="px-3 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
                    onClick={() => handleSelectHousehold(household)} // Use new handler
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-sm">{household.householdName}</span>
                      {household.address && (
                        <span className="text-xs text-muted-foreground truncate ml-2">
                          {household.address}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Scenarios Column */}
            {searchResults.scenarios.length > 0 && (
              <div className="flex-1">
                 <div className="px-3 py-1 text-xs font-semibold text-muted-foreground sticky top-0 bg-white border-b">Scenarios</div>
                {searchResults.scenarios.map((scenario) => (
                  <div
                    key={`sc-${scenario.id}`}
                    className="px-3 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
                    onClick={() => handleSelectScenario(scenario)}
                  >
                     <div> {/* Wrap content for better structure */}
                       <span className="font-medium text-sm block">{scenario.scenario_name}</span>
                       {/* Display household name if available */}
                       {scenario.householdName && (
                         <span className="text-xs text-muted-foreground block pl-2"> {/* Indent with pl-2 */}
                           {scenario.householdName}
                         </span>
                       )}
                     </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* No Results Message */}
      {!loading && searchTerm.length >= 2 && searchResults.households.length === 0 && searchResults.scenarios.length === 0 && !error && (
        <div className="absolute z-50 mt-1 w-full bg-white border rounded-md shadow-lg">
          <div className="px-3 py-2 text-sm text-muted-foreground">
            No households or scenarios found.
          </div>
        </div>
      )}
    </div>
  );
};
