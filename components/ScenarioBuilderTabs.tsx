import { useState, useEffect } from 'react';
import { ScenarioMetricsData, isToggleEnabled, getDefaultValue } from '@/hooks/useScenarioMetrics';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { FUND_TYPES, FundType } from '@/app/constants/fundTypes';
import React from 'react'; // Ensure React is imported

// Interface ScenarioData needs to be defined or imported here
// Assuming it's defined elsewhere or passed as a generic type
// For now, let's redefine it based on the original file for completeness
interface ScenarioData {
  householdId: string;
  name: string;
  personal: {
    name: string;
    starting_age: number | null;
    ending_age: number | null;
    include_partner: boolean;
    partner_name: string;
    partner_starting_age: number | null;
  };
  income: {
    annual_income: number | null;
    income_period: [number | null, number | null];
    partner_annual_income: number | null;
    partner_income_period: [number | null, number | null];
    include_superannuation: boolean;
  };
  savings: {
    savings_amount: number | null;
    cash_reserve: number | null;
    saving_percentage: number | null;
  };
  expenditure: {
    annual_expenses1: number | null;
    expense_period1: [number | null, number | null];
    annual_expenses2: number | null;
    expense_period2: [number | null, number | null];
    additional_expenses: Array<{ title: string; value: number; period: [number, number]; frequency: number }>;
  };
  investment: {
    annual_investment_return?: number;
    inv_std_dev?: number;
    initial_investment: number | null;
    annual_investment_contribution: number | null;
    fundType: FundType;
    fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
  };
  kiwiSaver: {
    initial_kiwisaver: number | null;
    kiwisaver_contribution: number | null;
    employer_contribution: number | null;
    annual_kiwisaver_return?: number | null;
    ks_std_dev?: number | null;
    partner_initial_kiwisaver: number | null;
    partner_kiwisaver_contribution: number | null;
    partner_employer_contribution: number | null;
    partner_annual_kiwisaver_return?: number | null;
    partner_ks_std_dev?: number | null;
    fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
    partner_fund_periods: Array<{
      fundType: FundType;
      period: [number | null, number | null];
      return: number | null;
      stdDev: number | null;
    }>;
  };
  property: {
    property_value: number | null;
    property_growth: number | null;
    debt: number | null;
    debt_ir: number | null;
    initial_debt_years: number | null;
    additional_debt_repayments: number | null;
    show_repayments: boolean;
    sell_main_property: boolean;
    main_property_sale_age: number | null;
    main_prop_sale_value: number | null;
    sale_allocate_to_investment: boolean;
    downsize: boolean;
    downsize_age: number | null;
    new_property_value: number | null;
    allocate_to_investment: boolean;
    pay_off_debt: boolean;
    additional_properties: Array<{
      property_value: number;
      property_growth: number;
      debt: number;
      debt_ir: number;
      initial_debt_years: number;
      additional_debt_repayments: number;
      sell_property: boolean;
      show_repayments: boolean;
    }>;
  };
  economic: {
    inflation_rate: number | null;
  };
  monteCarlo: {
    simulations: number | null;
    confidence_interval: number | null;
  };
  misc: {
    household_name: string | null;
  };
}


// Define tab sections
const tabSections = [
  { value: 'personal', label: 'Personal' },
  { value: 'income', label: 'Income' },
  { value: 'savings', label: 'Savings' },
  { value: 'expenditure', label: 'Expenditure' },
  { value: 'investment', label: 'Investment' },
  { value: 'kiwiSaver', label: 'KiwiSaver' },
  { value: 'property', label: 'Property' },
  { value: 'economic', label: 'Economic' },
  { value: 'monteCarlo', label: 'Monte Carlo' },
];

interface FormProps {
  data: ScenarioData;
  updateData: React.Dispatch<React.SetStateAction<ScenarioData>>;
}

interface ScenarioBuilderTabsProps {
  scenarioData: ScenarioData;
  setScenarioData: React.Dispatch<React.SetStateAction<ScenarioData>>;
  metrics?: ScenarioMetricsData;
}

export default function ScenarioBuilderTabs({ scenarioData, setScenarioData, metrics }: ScenarioBuilderTabsProps) {
  return (
    <Tabs defaultValue="personal" className="flex-grow flex flex-col">
      <TabsList className="grid w-full grid-cols-3 sm:grid-cols-5 md:grid-cols-9 border-b rounded-none px-2">
        {/* Dynamically create Tab Triggers */}
        {tabSections.map(section => (
          <TabsTrigger key={section.value} value={section.value} className="text-xs sm:text-sm">
            {section.label}
          </TabsTrigger>
        ))}
      </TabsList>
      <CardContent className="flex-grow overflow-y-auto p-4 md:p-6">
        {/* Map sections to Tab Content */}
        <TabsContent value="personal">
          <PersonalForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="income">
          <IncomeForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="savings">
          <SavingsForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="expenditure">
          <ExpenditureForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="investment">
          <InvestmentForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="kiwiSaver">
          <KiwiSaverForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="property">
          <PropertyForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="economic">
          <EconomicForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
        <TabsContent value="monteCarlo">
          <MonteCarloForm data={scenarioData} updateData={setScenarioData} />
        </TabsContent>
      </CardContent>
    </Tabs>
  );
}

// --- Form Component Implementations ---
// (Keep all existing Form components: PersonalForm, IncomeForm, SavingsForm, etc.)

function PersonalForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      personal: {
        ...prevData.personal,
        [name]: parsedValue
      }
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    updateData(prevData => ({
      ...prevData,
      personal: {
        ...prevData.personal,
        include_partner: checked,
        // Reset partner details if unchecked? Optional, depends on desired UX
        // partner_name: checked ? prevData.personal.partner_name : '',
        // partner_starting_age: checked ? prevData.personal.partner_starting_age : null,
      }
    }));
  };

  return (
    <div className="space-y-4 p-1"> {/* Added slight padding */}
      {/* Removed Scenario Name input from here as it's in the header now */}
      <div>
        <Label htmlFor="name">Primary Name</Label>
        <Input
          id="name"
          name="name"
          value={data.personal.name || ''}
          onChange={handleChange}
          placeholder="Primary Applicant's Name"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="starting_age">Starting Age <span className="text-red-500">*</span></Label>
          <Input
            id="starting_age"
            name="starting_age"
            type="number"
            min={18}
            max={100}
            value={data.personal.starting_age ?? ''}
            onChange={handleChange}
            placeholder="e.g., 35"
          />
        </div>
        <div>
          <Label htmlFor="ending_age">Ending Age (Projection End) <span className="text-red-500">*</span></Label>
          <Input
            id="ending_age"
            name="ending_age"
            type="number"
            min={data.personal.starting_age ?? 18} // Ensure end age >= start age
            max={120}
            value={data.personal.ending_age ?? ''}
            onChange={handleChange}
            placeholder="e.g., 95"
          />
        </div>
      </div>
      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="include_partner"
          checked={data.personal.include_partner}
          onCheckedChange={handleSwitchChange}
        />
        <Label htmlFor="include_partner">Include Partner</Label>
      </div>
      {data.personal.include_partner && (
        <div className="space-y-4 pl-6 border-l-2 border-border ml-2 pt-2"> {/* Indent partner fields */}
          <div>
            <Label htmlFor="partner_name">Partner's Name</Label>
            <Input
              id="partner_name"
              name="partner_name"
              value={data.personal.partner_name || ''}
              onChange={handleChange}
              placeholder="Partner's Name"
            />
          </div>
          <div>
            <Label htmlFor="partner_starting_age">Partner's Starting Age <span className="text-red-500">*</span></Label>
            <Input
              id="partner_starting_age"
              name="partner_starting_age"
              type="number"
              min={18}
              max={100}
              value={data.personal.partner_starting_age ?? ''}
              onChange={handleChange}
              placeholder="e.g., 34"
            />
          </div>
        </div>
      )}
    </div>
  );
}

function IncomeForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      income: {
        ...prevData.income,
        [name]: parsedValue
      }
    }));
  };

  const handlePeriodChange = (field: 'income_period' | 'partner_income_period', index: 0 | 1, value: string) => {
    const numValue = value === '' ? null : parseInt(value);
    updateData(prevData => {
      const currentPeriod = [...(prevData.income[field] || [null, null])] as [number | null, number | null];
      currentPeriod[index] = numValue;
      // Basic validation: ensure end >= start
      if (index === 0 && currentPeriod[1] !== null && numValue !== null && numValue > currentPeriod[1]) {
        currentPeriod[1] = numValue; // Adjust end if start exceeds it
      }
      if (index === 1 && currentPeriod[0] !== null && numValue !== null && numValue < currentPeriod[0]) {
        currentPeriod[0] = numValue; // Adjust start if end precedes it
      }
      return {
        ...prevData,
        income: {
          ...prevData.income,
          [field]: currentPeriod
        }
      };
    });
  };

  const handleSwitchChange = (checked: boolean) => {
    updateData(prevData => ({
      ...prevData,
      income: {
        ...prevData.income,
        include_superannuation: checked
      }
    }));
  };

  // Initialize income periods based on personal ages if not set
  useEffect(() => {
    updateData(prevData => {
      let needsUpdate = false;
      const updatedIncome = { ...prevData.income };

      const startAge = prevData.personal.starting_age;
      const endAge = prevData.personal.ending_age;
      const partnerStartAge = prevData.personal.partner_starting_age;

      if (!updatedIncome.income_period || updatedIncome.income_period[0] === null || updatedIncome.income_period[1] === null) {
        updatedIncome.income_period = [startAge, endAge];
        needsUpdate = true;
      }

      if (prevData.personal.include_partner && (!updatedIncome.partner_income_period || updatedIncome.partner_income_period[0] === null || updatedIncome.partner_income_period[1] === null)) {
        updatedIncome.partner_income_period = [partnerStartAge, endAge];
        needsUpdate = true;
      }

      return needsUpdate ? { ...prevData, income: updatedIncome } : prevData;
    });
  }, [data.personal.starting_age, data.personal.partner_starting_age, data.personal.include_partner, data.personal.ending_age, updateData]);


  const incomePeriod = data.income.income_period || [null, null];
  const partnerIncomePeriod = data.income.partner_income_period || [null, null];
  const minAge = 18; // Use a constant or derive from data if needed
  const maxAge = data.personal.ending_age ?? 120;

  return (
    <div className="space-y-6 p-1">
      {/* Primary Income Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">{data.personal.name || 'Primary Applicant'}'s Income</CardTitle>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div>
            <Label htmlFor="annual_income">Annual Income (Gross)</Label>
            <Input
              id="annual_income"
              name="annual_income"
              type="number"
              min={0}
              value={data.income.annual_income ?? ''}
              onChange={handleChange}
              placeholder="e.g., 80000"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="income_period_start">Income Start Age</Label>
              <Input
                type="number"
                id="income_period_start"
                name="income_period_start"
                placeholder="Start Age"
                min={minAge}
                max={maxAge}
                value={incomePeriod[0] ?? ''}
                onChange={(e) => handlePeriodChange('income_period', 0, e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="income_period_end">Income End Age</Label>
              <Input
                type="number"
                id="income_period_end"
                name="income_period_end"
                placeholder="End Age"
                min={incomePeriod[0] ?? minAge} // Min end age is start age
                max={maxAge}
                value={incomePeriod[1] ?? ''}
                onChange={(e) => handlePeriodChange('income_period', 1, e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Superannuation Switch */}
      <Card>
         <CardContent className="pt-6 flex items-center space-x-2">
            <Switch
              id="include_superannuation"
              checked={data.income.include_superannuation}
              onCheckedChange={handleSwitchChange}
            />
            <Label htmlFor="include_superannuation">Include NZ Superannuation</Label>
         </CardContent>
      </Card>

      {/* Partner Income Section */}
      {data.personal.include_partner && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base font-medium">{data.personal.partner_name || 'Partner'}'s Income</CardTitle>
          </CardHeader>
          <CardContent className="pt-4 space-y-4">
            <div>
              <Label htmlFor="partner_annual_income">Partner's Annual Income (Gross)</Label>
              <Input
                id="partner_annual_income"
                name="partner_annual_income"
                type="number"
                min={0}
                value={data.income.partner_annual_income ?? ''}
                onChange={handleChange}
                placeholder="e.g., 75000"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="partner_income_period_start">Partner Income Start Age</Label>
                <Input
                  type="number"
                  id="partner_income_period_start"
                  name="partner_income_period_start"
                  placeholder="Start Age"
                  min={minAge}
                  max={maxAge}
                  value={partnerIncomePeriod[0] ?? ''}
                  onChange={(e) => handlePeriodChange('partner_income_period', 0, e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="partner_income_period_end">Partner Income End Age</Label>
                <Input
                  type="number"
                  id="partner_income_period_end"
                  name="partner_income_period_end"
                  placeholder="End Age"
                  min={partnerIncomePeriod[0] ?? minAge} // Min end age is start age
                  max={maxAge}
                  value={partnerIncomePeriod[1] ?? ''}
                  onChange={(e) => handlePeriodChange('partner_income_period', 1, e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}


function SavingsForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    let parsedValue: string | number | null = value;

    if (type === 'number') {
      parsedValue = value === '' ? null : parseFloat(value);
      // Clamp percentage between 0 and 100
      if (name === 'saving_percentage') {
        parsedValue = Math.max(0, Math.min(100, parsedValue ?? 0));
      }
    }

    updateData(prevData => ({
      ...prevData,
      savings: {
        ...prevData.savings,
        [name]: parsedValue
      }
    }));
  };

  return (
    <div className="space-y-4 p-1">
      <div>
        <Label htmlFor="savings_amount">Current Savings Balance</Label>
        <Input
          id="savings_amount"
          name="savings_amount"
          type="number"
          min={0}
          value={data.savings.savings_amount ?? ''}
          onChange={handleChange}
          placeholder="e.g., 25000"
        />
         <p className="text-sm text-muted-foreground mt-1">
          Total current liquid savings (excluding KiwiSaver, investments, property equity).
        </p>
      </div>
      <div>
        <Label htmlFor="cash_reserve">Desired Cash Reserve</Label>
        <Input
          id="cash_reserve"
          name="cash_reserve"
          type="number"
          min={0}
          value={data.savings.cash_reserve ?? ''}
          onChange={handleChange}
          placeholder="e.g., 10000"
        />
         <p className="text-sm text-muted-foreground mt-1">
          Minimum cash amount to keep readily available (emergency fund).
        </p>
      </div>
    </div>
  );
}

function ExpenditureForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      expenditure: {
        ...prevData.expenditure,
        [name]: parsedValue
      }
    }));
  };

  const handlePeriodChange = (field: 'expense_period1' | 'expense_period2', index: 0 | 1, value: string) => {
    const numValue = value === '' ? null : parseInt(value);
    updateData(prevData => {
      const currentPeriod = [...(prevData.expenditure[field] || [null, null])] as [number | null, number | null];
      currentPeriod[index] = numValue;
      // Basic validation: ensure end >= start
      if (index === 0 && currentPeriod[1] !== null && numValue !== null && numValue > currentPeriod[1]) {
        currentPeriod[1] = numValue;
      }
      if (index === 1 && currentPeriod[0] !== null && numValue !== null && numValue < currentPeriod[0]) {
        currentPeriod[0] = numValue;
      }
      return {
        ...prevData,
        expenditure: {
          ...prevData.expenditure,
          [field]: currentPeriod
        }
      };
    });
  };

   // Initialize expense periods based on personal ages if not set
   useEffect(() => {
    updateData(prevData => {
      let needsUpdate = false;
      const updatedExpenditure = { ...prevData.expenditure };

      const startAge = prevData.personal.starting_age;
      const endAge = prevData.personal.ending_age;
      const retirementAgeGuess = 65; // A common retirement age, adjust if needed

      // Default Period 1: Start Age to Retirement Age (or End Age if sooner)
      if (!updatedExpenditure.expense_period1 || updatedExpenditure.expense_period1[0] === null || updatedExpenditure.expense_period1[1] === null) {
         const period1End = endAge !== null ? Math.min(retirementAgeGuess, endAge) : retirementAgeGuess;
         updatedExpenditure.expense_period1 = [startAge, period1End];
         needsUpdate = true;
      }

      // Default Period 2: Retirement Age + 1 to End Age
      if (!updatedExpenditure.expense_period2 || updatedExpenditure.expense_period2[0] === null || updatedExpenditure.expense_period2[1] === null) {
         const period1End = updatedExpenditure.expense_period1[1];
         if (period1End !== null && endAge !== null && period1End < endAge) {
            updatedExpenditure.expense_period2 = [period1End + 1, endAge];
            needsUpdate = true;
         } else if (period1End === null && endAge !== null) {
            // If period 1 end is unknown, default period 2 from retirement guess
            updatedExpenditure.expense_period2 = [retirementAgeGuess + 1, endAge];
            needsUpdate = true;
         }
      }


      return needsUpdate ? { ...prevData, expenditure: updatedExpenditure } : prevData;
    });
  }, [data.personal.starting_age, data.personal.ending_age, updateData]);


  const expensePeriod1 = data.expenditure.expense_period1 || [null, null];
  const expensePeriod2 = data.expenditure.expense_period2 || [null, null];
  const minAge = data.personal.starting_age ?? 18;
  const maxAge = data.personal.ending_age ?? 120;

  return (
    <div className="space-y-6 p-1">
      {/* Expense Period 1 */}
      <Card>
         <CardHeader>
          <CardTitle className="text-base font-medium">Expense Period 1 (e.g., Pre-Retirement)</CardTitle>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div>
            <Label htmlFor="annual_expenses1">Annual Expenses (Period 1) <span className="text-red-500">*</span></Label>
            <Input
              id="annual_expenses1"
              name="annual_expenses1"
              type="number"
              min={0}
              value={data.expenditure.annual_expenses1 ?? ''}
              onChange={handleChange}
              placeholder="e.g., 60000"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expense_period1_start">Period 1 Start Age <span className="text-red-500">*</span></Label>
              <Input
                type="number"
                id="expense_period1_start"
                name="expense_period1_start"
                placeholder="Start Age"
                min={minAge}
                max={maxAge}
                value={expensePeriod1[0] ?? ''}
                onChange={(e) => handlePeriodChange('expense_period1', 0, e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="expense_period1_end">Period 1 End Age <span className="text-red-500">*</span></Label>
              <Input
                type="number"
                id="expense_period1_end"
                name="expense_period1_end"
                placeholder="End Age"
                min={expensePeriod1[0] ?? minAge}
                max={maxAge}
                value={expensePeriod1[1] ?? ''}
                onChange={(e) => handlePeriodChange('expense_period1', 1, e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Expense Period 2 */}
      <Card>
         <CardHeader>
          <CardTitle className="text-base font-medium">Expense Period 2 (e.g., Post-Retirement)</CardTitle>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div>
            <Label htmlFor="annual_expenses2">Annual Expenses (Period 2) <span className="text-red-500">*</span></Label>
            <Input
              id="annual_expenses2"
              name="annual_expenses2"
              type="number"
              min={0}
              value={data.expenditure.annual_expenses2 ?? ''}
              onChange={handleChange}
              placeholder="e.g., 45000"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expense_period2_start">Period 2 Start Age <span className="text-red-500">*</span></Label>
              <Input
                type="number"
                id="expense_period2_start"
                name="expense_period2_start"
                placeholder="Start Age"
                min={expensePeriod1[1] ? expensePeriod1[1] + 1 : minAge} // Start after period 1 ends
                max={maxAge}
                value={expensePeriod2[0] ?? ''}
                onChange={(e) => handlePeriodChange('expense_period2', 0, e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="expense_period2_end">Period 2 End Age <span className="text-red-500">*</span></Label>
              <Input
                type="number"
                id="expense_period2_end"
                name="expense_period2_end"
                placeholder="End Age"
                min={expensePeriod2[0] ?? minAge}
                max={maxAge}
                value={expensePeriod2[1] ?? ''}
                onChange={(e) => handlePeriodChange('expense_period2', 1, e.target.value)}
              />
            </div>
          </div>
           <p className="text-sm text-muted-foreground pt-2">
            Define different annual expense levels for distinct periods of life. Expenses are assumed to inflate annually based on the Economic settings.
          </p>
        </CardContent>
      </Card>
      {/* Consider adding UI for 'additional_expenses' if needed */}
    </div>
  );
}


function InvestmentForm({ data, updateData }: FormProps) {
  const [showAddPeriod, setShowAddPeriod] = useState(false);
  const [editingPeriodIndex, setEditingPeriodIndex] = useState<number | null>(null);
  const [newPeriod, setNewPeriod] = useState<{
    fundType: FundType;
    start: number | null;
    end: number | null;
    return: number | null;
    stdDev: number | null;
  }>({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null }); // Default to Balanced

  const fundPeriods = data.investment?.fund_periods || [];
  const minAge = data.personal.starting_age ?? 18;
  const maxAge = data.personal.ending_age ?? 120;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      investment: {
        ...prevData.investment,
        [name]: parsedValue
      }
    }));
  };

  const handleMainFundTypeChange = (value: FundType) => {
     updateData(prevData => ({
      ...prevData,
      investment: {
        ...prevData.investment,
        fundType: value,
        // Clear periods if a main type is selected? Or keep them as overrides? Decide UX.
        // fund_periods: [], // Option 1: Clear periods
      }
    }));
  };

  const handlePeriodFundTypeChange = (value: FundType) => {
    const fundData = FUND_TYPES[value];
    setNewPeriod(prev => ({
      ...prev,
      fundType: value,
      // Ensure null is used if fundData or its properties are missing
      return: value !== 'Custom' ? (fundData?.return ?? null) : prev.return,
      stdDev: value !== 'Custom' ? (fundData?.stdDev ?? null) : prev.stdDev,
    }));
  };

  const handlePeriodValueChange = (field: 'start' | 'end' | 'return' | 'stdDev', value: string) => {
    const numValue = value === '' ? null : parseFloat(value);
    setNewPeriod(prev => ({ ...prev, [field]: numValue }));
  };

  const getNextAvailableStartAge = () => {
    if (!fundPeriods || fundPeriods.length === 0) {
      return minAge;
    }
    const lastPeriod = fundPeriods[fundPeriods.length - 1];
    return lastPeriod.period[1] !== null && lastPeriod.period[1] < maxAge
      ? lastPeriod.period[1] + 1
      : null; // Return null if last period ends at maxAge or has no end
  };

  const handleShowAddPeriod = () => {
    const nextStartAge = getNextAvailableStartAge();
    if (nextStartAge === null) return; // Don't show if no more periods can be added

    setNewPeriod({
      fundType: 'Balanced', // Default new period type
      start: nextStartAge,
      end: maxAge, // Default end to max age
      return: FUND_TYPES['Balanced'].return,
      stdDev: FUND_TYPES['Balanced'].stdDev,
    });
    setEditingPeriodIndex(null); // Ensure it's add mode
    setShowAddPeriod(true);
  };

  const handleAddOrUpdatePeriod = () => {
    // Validation
    if (newPeriod.start === null || newPeriod.end === null || newPeriod.start > newPeriod.end) {
      console.error("Invalid period start/end age"); return;
    }
    if (newPeriod.fundType === 'Custom' && (newPeriod.return === null || newPeriod.stdDev === null)) {
      console.error("Custom fund requires return and std dev"); return;
    }

    const periodToAddOrUpdate: ScenarioData['investment']['fund_periods'][0] = {
      fundType: newPeriod.fundType,
      period: [newPeriod.start, newPeriod.end],
      return: newPeriod.fundType === 'Custom' ? newPeriod.return : FUND_TYPES[newPeriod.fundType].return,
      stdDev: newPeriod.fundType === 'Custom' ? newPeriod.stdDev : FUND_TYPES[newPeriod.fundType].stdDev,
    };

    updateData(prevData => {
      const existingPeriods = prevData.investment?.fund_periods || [];
      let updatedPeriods;
      if (editingPeriodIndex !== null) {
        updatedPeriods = [...existingPeriods];
        updatedPeriods[editingPeriodIndex] = periodToAddOrUpdate;
      } else {
        updatedPeriods = [...existingPeriods, periodToAddOrUpdate];
      }
      // Sort periods by start age
      updatedPeriods.sort((a, b) => (a.period[0] ?? 0) - (b.period[0] ?? 0));
      // Optional: Add validation for overlapping periods here

      return {
        ...prevData,
        investment: {
          ...prevData.investment,
          fund_periods: updatedPeriods
        }
      };
    });

    handleCancelEdit(); // Reset form
  };

  const handleEditPeriod = (index: number) => {
    const period = fundPeriods[index];
    setNewPeriod({
      fundType: period.fundType,
      start: period.period[0],
      end: period.period[1],
      return: period.return,
      stdDev: period.stdDev,
    });
    setEditingPeriodIndex(index);
    setShowAddPeriod(true);
  };

  const handleRemovePeriod = (index: number) => {
    updateData(prevData => {
      const updatedPeriods = [...(prevData.investment?.fund_periods || [])];
      updatedPeriods.splice(index, 1);
      return {
        ...prevData,
        investment: {
          ...prevData.investment,
          fund_periods: updatedPeriods
        }
      };
    });
  };

  const handleCancelEdit = () => {
    setShowAddPeriod(false);
    setEditingPeriodIndex(null);
    setNewPeriod({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null }); // Reset
  };

  const canAddPeriod = getNextAvailableStartAge() !== null;

  return (
    <div className="space-y-6 p-1">
      {/* Initial Investment Details */}
      <Card>
         <CardHeader>
            <CardTitle className="text-base font-medium">Initial & Contributions</CardTitle>
         </CardHeader>
         <CardContent className="pt-4 space-y-4">
            <div>
              <Label htmlFor="initial_investment">Initial Investment Balance</Label>
              <Input
                id="initial_investment"
                name="initial_investment"
                type="number"
                min={0}
                value={data.investment.initial_investment ?? ''}
                onChange={handleChange}
                placeholder="e.g., 50000"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Current balance of investments (excluding KiwiSaver, property).
              </p>
            </div>
         </CardContent>
      </Card>

      {/* Investment Periods */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Investment Strategy & Periods</CardTitle>
           <p className="text-sm text-muted-foreground pt-1">
            Define investment periods with specific fund types.
          </p>
        </CardHeader>
        <CardContent className="pt-4 space-y-4">

          {/* Display Existing Periods */}
          {fundPeriods.map((period, index) => (
            <div key={index} className="flex items-center justify-between space-x-4 p-3 border rounded-lg bg-muted/40">
              <div className="flex-1">
                <div className="font-medium">{period.fundType}</div>
                <div className="text-sm text-muted-foreground">
                  Ages: {period.period[0] ?? '?'} - {period.period[1] ?? '?'}
                  {' | '}
                  Return: {period.return ?? FUND_TYPES[period.fundType]?.return ?? '?'}%
                  {' | '}
                  StdDev: {period.stdDev ?? FUND_TYPES[period.fundType]?.stdDev ?? '?'}%
                </div>
              </div>
              <div className="flex space-x-1">
                <Button variant="ghost" size="sm" onClick={() => handleEditPeriod(index)}>Edit</Button>
                <Button variant="ghost" size="sm" onClick={() => handleRemovePeriod(index)} className="text-destructive hover:text-destructive">Remove</Button>
              </div>
            </div>
          ))}

          {/* Add/Edit Period Form */}
          {showAddPeriod && (
            <div className="space-y-4 p-4 border rounded-lg shadow-sm">
              <h4 className="font-medium">{editingPeriodIndex !== null ? 'Edit' : 'Add'} Investment Period</h4>
              <div>
                <Label>Fund Type</Label>
                <Select value={newPeriod.fundType} onValueChange={handlePeriodFundTypeChange}>
                  <SelectTrigger><SelectValue placeholder="Select fund type" /></SelectTrigger>
                  <SelectContent>
                    {Object.keys(FUND_TYPES).map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Start Age</Label>
                  <Input
                    type="number"
                    min={minAge} max={maxAge}
                    value={newPeriod.start ?? ''}
                    onChange={(e) => handlePeriodValueChange('start', e.target.value)}
                    placeholder={`e.g., ${getNextAvailableStartAge() ?? minAge}`}
                    // Consider disabling if editing? Or allow changing start?
                  />
                </div>
                <div>
                  <Label>End Age</Label>
                  <Input
                    type="number"
                    min={newPeriod.start ?? minAge} max={maxAge}
                    value={newPeriod.end ?? ''}
                    onChange={(e) => handlePeriodValueChange('end', e.target.value)}
                    placeholder={`e.g., ${maxAge}`}
                  />
                </div>
              </div>
              {newPeriod.fundType === 'Custom' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Custom Return Rate (%)</Label>
                    <Input
                      type="number" step="0.1"
                      value={newPeriod.return ?? ''}
                      onChange={(e) => handlePeriodValueChange('return', e.target.value)}
                      placeholder="e.g., 7.5"
                    />
                  </div>
                  <div>
                    <Label>Custom Standard Deviation (%)</Label>
                    <Input
                      type="number" step="0.1"
                      value={newPeriod.stdDev ?? ''}
                      onChange={(e) => handlePeriodValueChange('stdDev', e.target.value)}
                       placeholder="e.g., 15.0"
                    />
                  </div>
                </div>
              )}
              <div className="flex justify-end space-x-2 pt-2">
                <Button variant="ghost" onClick={handleCancelEdit}>Cancel</Button>
                <Button onClick={handleAddOrUpdatePeriod} disabled={newPeriod.start === null || newPeriod.end === null || (newPeriod.fundType === 'Custom' && (newPeriod.return === null || newPeriod.stdDev === null))}>
                  {editingPeriodIndex !== null ? 'Update Period' : 'Add Period'}
                </Button>
              </div>
            </div>
          )}

          {/* Add Period Button */}
          {!showAddPeriod && (
            <Button
              variant="outline"
              onClick={handleShowAddPeriod}
              disabled={!canAddPeriod}
              className="mt-2"
            >
              {canAddPeriod ? '+ Add Investment Period' : 'All periods covered'}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


function KiwiSaverForm({ data, updateData }: FormProps) {
  // State for managing add/edit forms for both main and partner
  const [showAddPeriod, setShowAddPeriod] = useState(false);
  const [editingPeriodIndex, setEditingPeriodIndex] = useState<number | null>(null);
  const [newPeriod, setNewPeriod] = useState<{ fundType: FundType; start: number | null; end: number | null; return: number | null; stdDev: number | null; }>({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null });

  const [showAddPartnerPeriod, setShowAddPartnerPeriod] = useState(false);
  const [editingPartnerPeriodIndex, setEditingPartnerPeriodIndex] = useState<number | null>(null);
  const [newPartnerPeriod, setNewPartnerPeriod] = useState<{ fundType: FundType; start: number | null; end: number | null; return: number | null; stdDev: number | null; }>({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null });

  const fundPeriods = data.kiwiSaver?.fund_periods || [];
  const partnerFundPeriods = data.kiwiSaver?.partner_fund_periods || [];
  const minAge = 18; // Or derive from personal data
  const maxAge = data.personal.ending_age ?? 120;
  const primaryStartAge = data.personal.starting_age ?? minAge;
  const partnerStartAge = data.personal.partner_starting_age ?? minAge;

  // --- Generic Handlers ---
  const handleKiwiSaverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      kiwiSaver: { ...prevData.kiwiSaver, [name]: parsedValue }
    }));
  };

  const handleContributionChange = (field: 'kiwisaver_contribution' | 'employer_contribution' | 'partner_kiwisaver_contribution' | 'partner_employer_contribution', value: string) => {
     const numValue = value === '' ? null : parseInt(value);
     // Basic validation for contribution %
     const validatedValue = numValue !== null ? Math.max(0, Math.min(100, numValue)) : null;
      updateData(prevData => ({
        ...prevData,
        kiwiSaver: { ...prevData.kiwiSaver, [field]: validatedValue }
      }));
  };

  // --- Period Management Logic (Refactored for Reusability) ---

  const getNextAvailablePeriodStart = (periods: ScenarioData['kiwiSaver']['fund_periods'], personStartAge: number) => {
    if (!periods || periods.length === 0) return personStartAge;
    const lastPeriod = periods[periods.length - 1];
    return lastPeriod.period[1] !== null && lastPeriod.period[1] < maxAge ? lastPeriod.period[1] + 1 : null;
  };

  const setupNewPeriod = (personStartAge: number) => ({
    fundType: 'Balanced' as FundType,
    start: getNextAvailablePeriodStart(fundPeriods, personStartAge),
    end: maxAge,
    return: FUND_TYPES['Balanced'].return,
    stdDev: FUND_TYPES['Balanced'].stdDev,
  });

   const setupNewPartnerPeriod = (personStartAge: number) => ({
    fundType: 'Balanced' as FundType,
    start: getNextAvailablePeriodStart(partnerFundPeriods, personStartAge),
    end: maxAge,
    return: FUND_TYPES['Balanced'].return,
    stdDev: FUND_TYPES['Balanced'].stdDev,
  });

  const handleShowAdd = (isPartner: boolean) => {
    if (isPartner) {
      const nextStart = getNextAvailablePeriodStart(partnerFundPeriods, partnerStartAge);
      if (nextStart === null) return;
      setNewPartnerPeriod(setupNewPartnerPeriod(partnerStartAge));
      setEditingPartnerPeriodIndex(null);
      setShowAddPartnerPeriod(true);
    } else {
       const nextStart = getNextAvailablePeriodStart(fundPeriods, primaryStartAge);
       if (nextStart === null) return;
      setNewPeriod(setupNewPeriod(primaryStartAge));
      setEditingPeriodIndex(null);
      setShowAddPeriod(true);
    }
  };

  const handleCancel = (isPartner: boolean) => {
    if (isPartner) {
      setShowAddPartnerPeriod(false);
      setEditingPartnerPeriodIndex(null);
      setNewPartnerPeriod({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null });
    } else {
      setShowAddPeriod(false);
      setEditingPeriodIndex(null);
      setNewPeriod({ fundType: 'Balanced', start: null, end: null, return: null, stdDev: null });
    }
  };

  const handleEdit = (index: number, isPartner: boolean) => {
    const period = isPartner ? partnerFundPeriods[index] : fundPeriods[index];
    const setupFunc = isPartner ? setNewPartnerPeriod : setNewPeriod;
    setupFunc({
      fundType: period.fundType,
      start: period.period[0], end: period.period[1],
      return: period.return, stdDev: period.stdDev,
    });
    if (isPartner) setEditingPartnerPeriodIndex(index); else setEditingPeriodIndex(index);
    if (isPartner) setShowAddPartnerPeriod(true); else setShowAddPeriod(true);
  };

  const handleRemove = (index: number, isPartner: boolean) => {
    updateData(prevData => {
      const key = isPartner ? 'partner_fund_periods' : 'fund_periods';
      const updatedPeriods = [...(prevData.kiwiSaver?.[key] || [])];
      updatedPeriods.splice(index, 1);
      return { ...prevData, kiwiSaver: { ...prevData.kiwiSaver, [key]: updatedPeriods } };
    });
  };

  const handleAddOrUpdate = (isPartner: boolean) => {
    const periodState = isPartner ? newPartnerPeriod : newPeriod;
    const editingIndex = isPartner ? editingPartnerPeriodIndex : editingPeriodIndex;

    // Validation (same as InvestmentForm)
    if (periodState.start === null || periodState.end === null || periodState.start > periodState.end) return;
    if (periodState.fundType === 'Custom' && (periodState.return === null || periodState.stdDev === null)) return;

    const periodToAddOrUpdate: ScenarioData['kiwiSaver']['fund_periods'][0] = {
      fundType: periodState.fundType,
      period: [periodState.start, periodState.end],
      return: periodState.fundType === 'Custom' ? periodState.return : FUND_TYPES[periodState.fundType].return,
      stdDev: periodState.fundType === 'Custom' ? periodState.stdDev : FUND_TYPES[periodState.fundType].stdDev,
    };

    updateData(prevData => {
      const key = isPartner ? 'partner_fund_periods' : 'fund_periods';
      const existingPeriods = prevData.kiwiSaver?.[key] || [];
      let updatedPeriods;
      if (editingIndex !== null) {
        updatedPeriods = [...existingPeriods];
        updatedPeriods[editingIndex] = periodToAddOrUpdate;
      } else {
        updatedPeriods = [...existingPeriods, periodToAddOrUpdate];
      }
      updatedPeriods.sort((a, b) => (a.period[0] ?? 0) - (b.period[0] ?? 0));
      return { ...prevData, kiwiSaver: { ...prevData.kiwiSaver, [key]: updatedPeriods } };
    });

    handleCancel(isPartner); // Reset form
  };

  // --- Component Rendering ---

  const renderPeriodForm = (
    periodState: typeof newPeriod,
    setPeriodState: React.Dispatch<React.SetStateAction<typeof newPeriod>>,
    isPartner: boolean
  ) => {
     const handleFundTypeChange = (value: FundType) => {
        const fundData = FUND_TYPES[value];
        setPeriodState(prev => ({
          ...prev, fundType: value,
          // Ensure null is used if fundData or its properties are missing
          return: value !== 'Custom' ? (fundData?.return ?? null) : prev.return,
          stdDev: value !== 'Custom' ? (fundData?.stdDev ?? null) : prev.stdDev,
        }));
      };
      const handleValueChange = (field: 'start' | 'end' | 'return' | 'stdDev', value: string) => {
        const numValue = value === '' ? null : parseFloat(value);
        setPeriodState(prev => ({ ...prev, [field]: numValue }));
      };
      const editingIndex = isPartner ? editingPartnerPeriodIndex : editingPeriodIndex;
      const personMinAge = isPartner ? partnerStartAge : primaryStartAge;

    return (
       <div className="space-y-4 p-4 border rounded-lg shadow-sm mt-4">
         <h4 className="font-medium">{editingIndex !== null ? 'Edit' : 'Add'} {isPartner ? "Partner's" : ""} KiwiSaver Period</h4>
          <div>
            <Label>Fund Type</Label>
            <Select value={periodState.fundType} onValueChange={handleFundTypeChange}>
              <SelectTrigger><SelectValue placeholder="Select fund type" /></SelectTrigger>
              <SelectContent>
                {Object.keys(FUND_TYPES).map((type) => ( <SelectItem key={type} value={type}>{type}</SelectItem> ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div><Label>Start Age</Label><Input type="number" min={personMinAge} max={maxAge} value={periodState.start ?? ''} onChange={(e) => handleValueChange('start', e.target.value)} placeholder={`e.g., ${personMinAge}`} /></div>
            <div><Label>End Age</Label><Input type="number" min={periodState.start ?? personMinAge} max={maxAge} value={periodState.end ?? ''} onChange={(e) => handleValueChange('end', e.target.value)} placeholder={`e.g., ${maxAge}`} /></div>
          </div>
          {periodState.fundType === 'Custom' && (
            <div className="grid grid-cols-2 gap-4">
              <div><Label>Custom Return (%)</Label><Input type="number" step="0.1" value={periodState.return ?? ''} onChange={(e) => handleValueChange('return', e.target.value)} placeholder="e.g., 6.0" /></div>
              <div><Label>Custom Std Dev (%)</Label><Input type="number" step="0.1" value={periodState.stdDev ?? ''} onChange={(e) => handleValueChange('stdDev', e.target.value)} placeholder="e.g., 12.0" /></div>
            </div>
          )}
          <div className="flex justify-end space-x-2 pt-2">
            <Button variant="ghost" onClick={() => handleCancel(isPartner)}>Cancel</Button>
            <Button onClick={() => handleAddOrUpdate(isPartner)} disabled={periodState.start === null || periodState.end === null || (periodState.fundType === 'Custom' && (periodState.return === null || periodState.stdDev === null))}>
              {editingIndex !== null ? 'Update Period' : 'Add Period'}
            </Button>
          </div>
       </div>
    );
  };

   const renderPeriodsList = (periods: ScenarioData['kiwiSaver']['fund_periods'], isPartner: boolean) => (
    <div className="space-y-2 mt-4">
      {periods.map((period, index) => (
        <div key={index} className="flex items-center justify-between space-x-4 p-3 border rounded-lg bg-muted/40">
          <div className="flex-1">
            <div className="font-medium">{period.fundType}</div>
            <div className="text-sm text-muted-foreground">
              Ages: {period.period[0] ?? '?'} - {period.period[1] ?? '?'} | R: {period.return ?? FUND_TYPES[period.fundType]?.return ?? '?'}% | SD: {period.stdDev ?? FUND_TYPES[period.fundType]?.stdDev ?? '?'}%
            </div>
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" onClick={() => handleEdit(index, isPartner)}>Edit</Button>
            <Button variant="ghost" size="sm" onClick={() => handleRemove(index, isPartner)} className="text-destructive hover:text-destructive">Remove</Button>
          </div>
        </div>
      ))}
    </div>
  );

  const canAddPrimary = getNextAvailablePeriodStart(fundPeriods, primaryStartAge) !== null;
  const canAddPartner = getNextAvailablePeriodStart(partnerFundPeriods, partnerStartAge) !== null;

  return (
    <div className="space-y-6 p-1">
      {/* Primary KiwiSaver */}
      <Card>
        <CardHeader><CardTitle className="text-base font-medium">{data.personal.name || 'Primary Applicant'}'s KiwiSaver</CardTitle></CardHeader>
        <CardContent className="pt-4 space-y-4">
          <div>
            <Label htmlFor="initial_kiwisaver">Initial Balance</Label>
            <Input id="initial_kiwisaver" name="initial_kiwisaver" type="number" min={0} value={data.kiwiSaver.initial_kiwisaver ?? ''} onChange={handleKiwiSaverChange} placeholder="e.g., 30000" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="kiwisaver_contribution">Contribution (% of Income)</Label>
               <Select value={data.kiwiSaver.kiwisaver_contribution?.toString() ?? ''} onValueChange={(value) => handleContributionChange('kiwisaver_contribution', value)}>
                  <SelectTrigger><SelectValue placeholder="Select %" /></SelectTrigger>
                  <SelectContent>
                     {[0, 3, 4, 6, 8, 10].map(p => <SelectItem key={p} value={p.toString()}>{p}%</SelectItem>)}
                  </SelectContent>
               </Select>
            </div>
            <div>
              <Label htmlFor="employer_contribution">Employer Contribution (%)</Label>
               <Select value={data.kiwiSaver.employer_contribution?.toString() ?? ''} onValueChange={(value) => handleContributionChange('employer_contribution', value)}>
                  <SelectTrigger><SelectValue placeholder="Select %" /></SelectTrigger>
                  <SelectContent>
                     {[0, 3].map(p => <SelectItem key={p} value={p.toString()}>{p}%</SelectItem>)}
                     {/* Add more options if needed */}
                  </SelectContent>
               </Select>
            </div>
          </div>
          {/* Primary Periods */}
          <h4 className="text-sm font-medium pt-2">KiwiSaver Fund Periods</h4>
           <p className="text-xs text-muted-foreground">Define specific fund strategies for different age ranges. Overrides default if periods exist.</p>
          {renderPeriodsList(fundPeriods, false)}
          {showAddPeriod ? renderPeriodForm(newPeriod, setNewPeriod, false) : (
            <Button variant="outline" onClick={() => handleShowAdd(false)} disabled={!canAddPrimary} className="mt-2">
              {canAddPrimary ? '+ Add KiwiSaver Period' : 'All periods covered'}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Partner KiwiSaver */}
      {data.personal.include_partner && (
        <Card>
          <CardHeader><CardTitle className="text-base font-medium">{data.personal.partner_name || 'Partner'}'s KiwiSaver</CardTitle></CardHeader>
          <CardContent className="pt-4 space-y-4">
            <div>
              <Label htmlFor="partner_initial_kiwisaver">Partner's Initial Balance</Label>
              <Input id="partner_initial_kiwisaver" name="partner_initial_kiwisaver" type="number" min={0} value={data.kiwiSaver.partner_initial_kiwisaver ?? ''} onChange={handleKiwiSaverChange} placeholder="e.g., 20000" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="partner_kiwisaver_contribution">Partner Contribution (%)</Label>
                 <Select value={data.kiwiSaver.partner_kiwisaver_contribution?.toString() ?? ''} onValueChange={(value) => handleContributionChange('partner_kiwisaver_contribution', value)}>
                    <SelectTrigger><SelectValue placeholder="Select %" /></SelectTrigger>
                    <SelectContent>
                       {[0, 3, 4, 6, 8, 10].map(p => <SelectItem key={p} value={p.toString()}>{p}%</SelectItem>)}
                    </SelectContent>
                 </Select>
              </div>
              <div>
                <Label htmlFor="partner_employer_contribution">Partner Employer Contribution (%)</Label>
                 <Select value={data.kiwiSaver.partner_employer_contribution?.toString() ?? ''} onValueChange={(value) => handleContributionChange('partner_employer_contribution', value)}>
                    <SelectTrigger><SelectValue placeholder="Select %" /></SelectTrigger>
                    <SelectContent>
                       {[0, 3].map(p => <SelectItem key={p} value={p.toString()}>{p}%</SelectItem>)}
                    </SelectContent>
                 </Select>
              </div>
            </div>
             {/* Partner Periods */}
             <h4 className="text-sm font-medium pt-2">Partner's KiwiSaver Fund Periods</h4>
             <p className="text-xs text-muted-foreground">Define specific fund strategies for the partner.</p>
            {renderPeriodsList(partnerFundPeriods, true)}
            {showAddPartnerPeriod ? renderPeriodForm(newPartnerPeriod, setNewPartnerPeriod, true) : (
              <Button variant="outline" onClick={() => handleShowAdd(true)} disabled={!canAddPartner} className="mt-2">
                 {canAddPartner ? "+ Add Partner's Period" : 'All periods covered'}
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}


function PropertyForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
    updateData(prevData => ({
      ...prevData,
      property: { ...prevData.property, [name]: parsedValue }
    }));
  };

   const handleSwitchChange = (name: keyof ScenarioData['property'], checked: boolean) => {
    updateData(prevData => ({
      ...prevData,
      property: { ...prevData.property, [name]: checked }
    }));
  };

  // Basic validation or defaults can be added via useEffect if needed

  return (
    <div className="space-y-6 p-1">
      {/* Main Property Details */}
      <Card>
         <CardHeader><CardTitle className="text-base font-medium">Primary Residence</CardTitle></CardHeader>
         <CardContent className="pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <div>
                 <Label htmlFor="property_value">Current Property Value</Label>
                 <Input id="property_value" name="property_value" type="number" min={0} value={data.property.property_value ?? ''} onChange={handleChange} placeholder="e.g., 800000" />
               </div>
               <div>
                 <Label htmlFor="property_growth">Annual Property Growth (%)</Label>
                 <Input id="property_growth" name="property_growth" type="number" step="0.1" min={-10} max={20} value={data.property.property_growth ?? ''} onChange={handleChange} placeholder="e.g., 3.5" />
               </div>
            </div>
         </CardContent>
      </Card>

      {/* Debt Details */}
       <Card>
         <CardHeader><CardTitle className="text-base font-medium">Property Debt (Mortgage)</CardTitle></CardHeader>
         <CardContent className="pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <div>
                 <Label htmlFor="debt">Current Debt Balance</Label>
                 <Input id="debt" name="debt" type="number" min={0} value={data.property.debt ?? ''} onChange={handleChange} placeholder="e.g., 400000" />
               </div>
               <div>
                 <Label htmlFor="debt_ir">Debt Interest Rate (%)</Label>
                 <Input id="debt_ir" name="debt_ir" type="number" step="0.01" min={0} max={20} value={data.property.debt_ir ?? ''} onChange={handleChange} placeholder="e.g., 5.75" />
               </div>
               <div>
                 <Label htmlFor="initial_debt_years">Remaining Loan Term (Years)</Label>
                 <Input id="initial_debt_years" name="initial_debt_years" type="number" min={0} max={50} value={data.property.initial_debt_years ?? ''} onChange={handleChange} placeholder="e.g., 25" />
               </div>
               <div>
                 <Label htmlFor="additional_debt_repayments">Additional Monthly Repayments</Label>
                 <Input id="additional_debt_repayments" name="additional_debt_repayments" type="number" min={0} value={data.property.additional_debt_repayments ?? ''} onChange={handleChange} placeholder="e.g., 200" />
               </div>
            </div>
         </CardContent>
      </Card>
    </div>
  );
}


function EconomicForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    // Ensure value stays within reasonable bounds
    let parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;
     if (name === 'inflation_rate' && typeof parsedValue === 'number') {
        parsedValue = Math.max(0, Math.min(20, parsedValue)); // Clamp between 0% and 20%
     }

    updateData(prevData => ({
      ...prevData,
      economic: { ...prevData.economic, [name]: parsedValue }
    }));
  };

  return (
    <div className="space-y-4 p-1">
      <div>
        <Label htmlFor="inflation_rate">Annual Inflation Rate (%)</Label>
        <Input
          id="inflation_rate"
          name="inflation_rate"
          type="number"
          min={0}
          max={20} // Set practical max
          step={0.1}
          value={data.economic.inflation_rate ?? ''}
          onChange={handleChange}
          placeholder="e.g., 2.5"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Assumed average annual inflation rate used to adjust future income, expenses, and asset growth (unless overridden). Default is 2.0%.
        </p>
      </div>
      {/* Add other economic factors here if needed in the future */}
    </div>
  );
}


function MonteCarloForm({ data, updateData }: FormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    let parsedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;

    // Apply constraints
    if (name === 'simulations' && typeof parsedValue === 'number') {
      parsedValue = Math.max(100, Math.min(10000, Math.round(parsedValue))); // Min 100, Max 10000, integer
    } else if (name === 'confidence_interval' && typeof parsedValue === 'number') {
      parsedValue = Math.max(50, Math.min(100, parsedValue)); // Min 50%, Max 100%
    }

    updateData(prevData => ({
      ...prevData,
      monteCarlo: { ...prevData.monteCarlo, [name]: parsedValue }
    }));
  };

  return (
    <div className="space-y-4 p-1">
      <div>
        <Label htmlFor="simulations">Number of Simulations</Label>
        <Input
          id="simulations"
          name="simulations"
          type="number"
          min={100} // Sensible minimum
          max={1000} // Sensible maximum
          step={100}
          value={data.monteCarlo.simulations ?? ''}
          onChange={handleChange}
          placeholder="e.g., 100"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Number of random scenarios generated (100-10,000). More simulations increase accuracy but take longer. Default: 100.
        </p>
      </div>
      <div>
        <Label htmlFor="confidence_interval">Success Rate Confidence Interval (%)</Label>
        <Input
          id="confidence_interval"
          name="confidence_interval"
          type="number"
          min={50} // Sensible minimum
          max={100} // Sensible maximum
          step={1}
          value={data.monteCarlo.confidence_interval ?? ''}
          onChange={handleChange}
          placeholder="e.g., 80"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Probability range for success metrics (50-100%). A 80% interval shows outcomes expected 80% of the time. Default: 80%.
        </p>
      </div>
    </div>
  );
}
