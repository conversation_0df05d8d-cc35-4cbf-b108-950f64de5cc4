import React from 'react';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, ArrowUp, ArrowDown, Settings, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { InputData } from '@/app/protected/planner/types';
import { InvestmentModal } from './modals/InvestmentModal';
import { FUND_TAILWIND_COLORS } from '@/app/constants/fundColors';

interface InvestmentFundCardProps {
  id: number;
  fundNumber: number;
  priorityIndex: number;
  description: string;
  amount: number;
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
  onMoveUp?: (id: number) => void;
  onMoveDown?: (id: number) => void;
  onRemove?: (fundNumber: number) => void;
  isFirst: boolean;
  isLast: boolean;
  totalFunds: number;
}

export function InvestmentFundCard({
  id,
  fundNumber,
  priorityIndex,
  description,
  amount,
  inputData,
  setInputData,
  readOnly,
  onMoveUp,
  onMoveDown,
  onRemove,
  isFirst,
  isLast,
  totalFunds
}: InvestmentFundCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get color from the shared constants
  const color = FUND_TAILWIND_COLORS[fundNumber as keyof typeof FUND_TAILWIND_COLORS] || FUND_TAILWIND_COLORS[1];

  const handleInputChange = (name: string, value: string) => {
    setInputData((prevData) => {
      const newValue = name.includes('description') ? value : parseFloat(value);

      return {
        ...prevData,
        [name]: newValue,
      };
    });
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="mb-2"
    >
      <Card className={`overflow-hidden border-l-4 ${color.border}`}>
        <div className={`${color.bg} flex items-center justify-between p-3`}>
          <div className="flex items-center gap-3 flex-1">
            {!readOnly && (
              <div
                {...attributes}
                {...listeners}
                className="cursor-grab active:cursor-grabbing p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <GripVertical className="h-5 w-5" />
              </div>
            )}

            {/* Priority Badge */}
            <Badge className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700">
              Priority {priorityIndex + 1}
            </Badge>

            {/* Fund Number Badge */}
            <Badge className={`${color.badge} border-none`}>
              Fund {fundNumber}
            </Badge>

            {/* Fund Description */}
            <div className="flex-1">
              <Input
                id={`investment_description${fundNumber}`}
                name={`investment_description${fundNumber}`}
                type="text"
                value={description}
                onChange={(e) => handleInputChange(`investment_description${fundNumber}`, e.target.value)}
                placeholder={`Investment Fund ${fundNumber}`}
                readOnly={readOnly}
                className={`bg-transparent border-none focus:ring-0 p-0 h-auto ${color.text} font-medium`}
              />
            </div>

            {/* Fund Amount */}
            <div className="flex items-center gap-2 min-w-[180px]">
              <span className={`${color.text}`}>$</span>
              <Input
                id={`initial_investment${fundNumber}`}
                name={`initial_investment${fundNumber}`}
                type="number"
                value={amount}
                onChange={(e) => handleInputChange(`initial_investment${fundNumber}`, e.target.value)}
                readOnly={readOnly}
                className="bg-transparent border-none focus:ring-0 p-0 h-auto text-right font-semibold"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1 ml-2">
            {!readOnly && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMoveUp?.(id)}
                  disabled={isFirst}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  title="Move up in priority"
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMoveDown?.(id)}
                  disabled={isLast}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  title="Move down in priority"
                >
                  <ArrowDown className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  title="Configure fund settings"
                >
                  <InvestmentModal inputData={inputData} setInputData={setInputData} />
                </Button>
                {totalFunds > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemove?.(fundNumber)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                    title="Remove fund"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
