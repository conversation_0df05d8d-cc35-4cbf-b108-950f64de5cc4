import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface MediaPlayerProps {
  filePath: string;
  fileType: string;
  className?: string;
}

export default function MediaPlayer({ filePath, fileType, className = '' }: MediaPlayerProps) {
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const getMediaUrl = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching media URL for path:', filePath);

        // Get a signed URL for the media file
        const { data, error } = await supabase.storage
          .from('media')
          .createSignedUrl(filePath, 3600); // 1 hour expiry

        if (error) {
          console.error('Error creating signed URL:', error);
          throw error;
        }

        if (data?.signedUrl) {
          console.log('Successfully retrieved signed URL');
          setMediaUrl(data.signedUrl);
        } else {
          console.error('No signed URL returned from Supabase');
          setError('Failed to get media URL');
        }
      } catch (err) {
        console.error('Error fetching media URL:', err);

        // Try a direct public URL as fallback
        try {
          const publicUrl = supabase.storage.from('media').getPublicUrl(filePath);
          if (publicUrl?.data?.publicUrl) {
            console.log('Using public URL as fallback:', publicUrl.data.publicUrl);
            setMediaUrl(publicUrl.data.publicUrl);
            setError(null);
          } else {
            setError('Error loading media file');
          }
        } catch (fallbackErr) {
          console.error('Fallback also failed:', fallbackErr);
          setError('Error loading media file');
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (filePath) {
      getMediaUrl();
    }
  }, [filePath]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-20 bg-muted/20 rounded-md">
      <div className="animate-pulse flex space-x-2">
        <div className="h-2 w-2 bg-muted-foreground rounded-full"></div>
        <div className="h-2 w-2 bg-muted-foreground rounded-full"></div>
        <div className="h-2 w-2 bg-muted-foreground rounded-full"></div>
      </div>
      <span className="ml-2 text-sm text-muted-foreground">Loading media...</span>
    </div>;
  }

  if (error) {
    return <div className="text-red-500 p-4 border border-red-200 bg-red-50 rounded-md">
      <p className="font-medium">Error loading media</p>
      <p className="text-sm">{error}</p>
    </div>;
  }

  if (!mediaUrl) {
    return <div className="p-4 border border-amber-200 bg-amber-50 rounded-md text-amber-700">
      No media available. The file may have been deleted or is inaccessible.
    </div>;
  }

  // Determine if it's an audio or video file
  const isAudio = fileType.startsWith('audio/');

  return (
    <div className={`rounded-md overflow-hidden ${className}`}>
      {isAudio ? (
        <audio
          controls
          className="w-full"
          src={mediaUrl}
          preload="metadata"
        >
          Your browser does not support the audio element.
        </audio>
      ) : (
        <video
          controls
          className="w-full max-h-[400px]"
          src={mediaUrl}
          preload="metadata"
        >
          Your browser does not support the video element.
        </video>
      )}
    </div>
  );
}
