import React from "react";
import { Handle, Position } from "@xyflow/react";
import { cn } from "@/lib/utils";

interface LabeledHandleProps {
  id: string;
  title: string;
  description?: string;
  onDescriptionChange?: (value: string) => void;
  type: "source" | "target";
  position: Position;
  className?: string;
  color?: string;
  isConnectable?: boolean;
  style?: React.CSSProperties;
}

export function LabeledHandle({
  id,
  title,
  description,
  onDescriptionChange,
  type,
  position,
  className,
  color = "#6b7280", // Default gray color
  isConnectable = true,
  style,
}: LabeledHandleProps) {
  // Determine background color based on color prop
  const bgColor =
    color === "green" ? "bg-green-50" :
    color === "yellow" ? "bg-yellow-50" :
    color === "red" ? "bg-red-50" :
    color === "blue" ? "bg-blue-50" :
    color === "purple" ? "bg-purple-50" : "bg-gray-50";

  // Determine dot color based on color prop
  const dotColor =
    color === "green" ? "bg-green-500" :
    color === "yellow" ? "bg-yellow-500" :
    color === "red" ? "bg-red-500" :
    color === "blue" ? "bg-blue-500" :
    color === "purple" ? "bg-purple-500" : "bg-gray-500";

  // Determine handle color based on color prop
  const handleColor =
    color === "green" ? "#22c55e" :
    color === "yellow" ? "#eab308" :
    color === "red" ? "#ef4444" :
    color === "blue" ? "#3b82f6" :
    color === "purple" ? "#a855f7" : "#6b7280";

  return (
    <div className={cn("space-y-1 p-2 rounded-md relative w-full overflow-visible", bgColor, className)}>
      <div className="text-xs flex items-center font-medium">
        <div className={cn("w-3 h-3 rounded-full mr-2", dotColor)}></div>
        {title}
      </div>
      {description !== undefined && (
        <input
          type="text"
          value={description}
          placeholder={`Path for ${title}`}
          className="nodrag h-8 text-xs w-full px-3 py-1 rounded border border-gray-200"
          onChange={(e) => onDescriptionChange?.(e.target.value)}
          readOnly={!onDescriptionChange}
        />
      )}
      <Handle
        id={id}
        type={type}
        position={position}
        isConnectable={isConnectable}
        style={{
          ...style,
          // Position the handle exactly at the edge of the node
          // Use a transform to ensure the handle is centered vertically on its row
          right: position === Position.Right ? '-6px' : undefined,
          left: position === Position.Left ? '-6px' : undefined,
          transform: position === Position.Right ? 'translateX(50%)' : 'translateX(-50%)',
          top: '50%', // Center vertically
          marginTop: '-6px', // Half of the handle height
          width: '12px',
          height: '12px',
          background: handleColor,
          border: '2px solid white',
          boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.2)',
          zIndex: 50 // Higher z-index to ensure it appears above other elements
        }}
        className={`handle-${id}`}
      />
    </div>
  );
}
