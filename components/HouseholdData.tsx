import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Button } from "./ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { createClient } from '@supabase/supabase-js';
import { LoadingModal } from "./modals/LoadingModal";
import { useToast } from "@/hooks/use-toast";

interface HouseholdData {
  id: string;
  householdData: {
    householdName: string;
    name1: string;
    age1: string;
    income1: string;
    kiwisaverValue1: string;
    kiwisaverContribution1: string;
    employerContribution1: string;
    kiwisaverProfile1: string;
    name2: string;
    age2: string;
    income2: string;
    kiwisaverValue2: string;
    kiwisaverContribution2: string;
    employerContribution2: string;
    kiwisaverProfile2: string;
    annualExpenses: number;
    propertyValue: number;
    debtValue: number;
    investmentValue: number;
    riskProfile: string;
    savingsValue: number;
  };
}

export default function HouseholdData({ householdData, isViewOnly = false }: { householdData: HouseholdData; isViewOnly?: boolean }) {
  const [data, setData] = useState<HouseholdData>(householdData);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string | number) => {
    if (!isViewOnly) {
      setData(prevData => ({
        ...prevData,
        householdData: {
          ...prevData.householdData,
          [field]: value
        }
      }));
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!);

    // Prepare the data for saving
    const dataToSave = {
      householdName: data.householdData.householdName,
      members: {
        name1: data.householdData.name1,
        name2: data.householdData.name2,
        age1: data.householdData.age1,
        age2: data.householdData.age2,
        income1: data.householdData.income1,
        income2: data.householdData.income2,
        kiwisaverValue1: data.householdData.kiwisaverValue1,
        kiwisaverValue2: data.householdData.kiwisaverValue2,
        kiwisaverContribution1: data.householdData.kiwisaverContribution1,
        kiwisaverContribution2: data.householdData.kiwisaverContribution2,
        employerContribution1: data.householdData.employerContribution1,
        employerContribution2: data.householdData.employerContribution2,
        kiwisaverProfile1: data.householdData.kiwisaverProfile1,
        kiwisaverProfile2: data.householdData.kiwisaverProfile2,
        annualExpenses: data.householdData.annualExpenses,
        propertyValue: data.householdData.propertyValue,
        debtValue: data.householdData.debtValue,
        investmentValue: data.householdData.investmentValue,
        riskProfile: data.householdData.riskProfile,
        savingsValue: data.householdData.savingsValue,
      }
    };

    const { error } = await supabase
      .from('households')
      .update(dataToSave)
      .eq('id', data.id);

    setIsLoading(false);

    if (error) {
      console.error('Error saving household data:', error);
      toast({
        title: "Error",
        description: "Failed to save household data. Please try again.",
        variant: "destructive",
      });
    } else {
      console.log('Household data saved successfully');
      toast({
        title: "Success",
        description: "Household data saved successfully.",
      });
    }
  };

  const contributionOptions = ['0', '3', '4', '6', '8', '10'];

  return (
    <>
      <Card className="w-full h-full overflow-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            {data.householdData.householdName} Household
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            <div>
              <h2 className="text-lg font-semibold mb-4">Member Information</h2>
              <hr className="my-4" />
              <div className="grid grid-cols-2 gap-8">
                {[1, 2].map((memberNum) => (
                  <div key={memberNum} className="space-y-4">
                    <h3 className="text-md font-semibold">Member {memberNum}</h3>
                    <div className="space-y-2">
                      <Label htmlFor={`name${memberNum}`}>Name</Label>
                      <Input
                        id={`name${memberNum}`}
                        value={data.householdData[`name${memberNum}` as keyof typeof data.householdData]}
                        onChange={(e) => handleInputChange(`name${memberNum}`, e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`age${memberNum}`}>Age</Label>
                      <Input
                        id={`age${memberNum}`}
                        type="number"
                        value={data.householdData[`age${memberNum}` as keyof typeof data.householdData]}
                        onChange={(e) => handleInputChange(`age${memberNum}`, e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`income${memberNum}`}>Income</Label>
                      <Input
                        id={`income${memberNum}`}
                        type="number"
                        value={data.householdData[`income${memberNum}` as keyof typeof data.householdData]}
                        onChange={(e) => handleInputChange(`income${memberNum}`, e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`kiwisaverValue${memberNum}`}>KiwiSaver Value</Label>
                      <Input
                        id={`kiwisaverValue${memberNum}`}
                        type="number"
                        value={data.householdData[`kiwisaverValue${memberNum}` as keyof typeof data.householdData]}
                        onChange={(e) => handleInputChange(`kiwisaverValue${memberNum}`, e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`kiwisaverContribution${memberNum}`}>KiwiSaver Contribution (%)</Label>
                      <Select 
                        value={String(data.householdData[`kiwisaverContribution${memberNum}` as keyof typeof data.householdData])} 
                        onValueChange={(value) => handleInputChange(`kiwisaverContribution${memberNum}`, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select contribution %" />
                        </SelectTrigger>
                        <SelectContent>
                          {contributionOptions.map((option) => (
                            <SelectItem key={option} value={option}>{option}%</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`employerContribution${memberNum}`}>Employer Contribution (%)</Label>
                      <Select 
                        value={String(data.householdData[`employerContribution${memberNum}` as keyof typeof data.householdData])} 
                        onValueChange={(value) => handleInputChange(`employerContribution${memberNum}`, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select contribution %" />
                        </SelectTrigger>
                        <SelectContent>
                          {contributionOptions.map((option) => (
                            <SelectItem key={option} value={option}>{option}%</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`kiwisaverProfile${memberNum}`}>KiwiSaver Profile</Label>
                      <Select 
                        value={String(data.householdData[`kiwisaverProfile${memberNum}` as keyof typeof data.householdData])} 
                        onValueChange={(value) => handleInputChange(`kiwisaverProfile${memberNum}`, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select KiwiSaver profile" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="conservative">Conservative</SelectItem>
                          <SelectItem value="balanced">Balanced</SelectItem>
                          <SelectItem value="growth">Growth</SelectItem>
                          <SelectItem value="aggressive">Aggressive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h2 className="text-lg font-semibold mb-4">Household Information</h2>
              <hr className="my-4" />
              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="annualExpenses">Annual Expenses</Label>
                  <Input
                    id="annualExpenses"
                    type="number"
                    value={data.householdData.annualExpenses}
                    onChange={(e) => handleInputChange('annualExpenses', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="propertyValue">Property Value</Label>
                  <Input
                    id="propertyValue"
                    type="number"
                    value={data.householdData.propertyValue}
                    onChange={(e) => handleInputChange('propertyValue', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="debtValue">Debt Value</Label>
                  <Input
                    id="debtValue"
                    type="number"
                    value={data.householdData.debtValue}
                    onChange={(e) => handleInputChange('debtValue', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="investmentValue">Investment Value</Label>
                  <Input
                    id="investmentValue"
                    type="number"
                    value={data.householdData.investmentValue}
                    onChange={(e) => handleInputChange('investmentValue', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="riskProfile">Risk Profile</Label>
                  <Select value={data.householdData.riskProfile} onValueChange={(value) => handleInputChange('riskProfile', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select risk profile" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="savingsValue">Savings Value</Label>
                  <Input
                    id="savingsValue"
                    type="number"
                    value={data.householdData.savingsValue}
                    onChange={(e) => handleInputChange('savingsValue', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          {!isViewOnly && (
            <div className="mt-8 flex justify-end">
              <Button onClick={handleSave}>Save Changes</Button>
            </div>
          )}
        </CardContent>
      </Card>
      <LoadingModal isOpen={isLoading} message="Saving household data..." />
    </>
  );
}
