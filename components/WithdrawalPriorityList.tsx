import React, { useState, useEffect } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { InputData } from '@/app/protected/planner/types';
import { LabelWithTooltip } from '@/components/TabTooltips';
import { FUND_TAILWIND_COLORS } from '@/app/constants/fundColors';

interface WithdrawalPriorityListProps {
  inputData: NonNullable<InputData>;
  setInputData: (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => void;
  readOnly?: boolean;
}

interface SortableItemProps {
  id: number;
  fundNumber: number;
  description: string;
  readOnly?: boolean;
  onMoveUp?: (id: number) => void;
  onMoveDown?: (id: number) => void;
  isFirst: boolean;
  isLast: boolean;
}

const SortableItem = ({
  id,
  fundNumber,
  description,
  readOnly,
  onMoveUp,
  onMoveDown,
  isFirst,
  isLast
}: SortableItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="mb-2"
    >
      <Card className="p-3 bg-white dark:bg-gray-800 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {!readOnly && (
            <div
              {...attributes}
              {...listeners}
              className="cursor-grab active:cursor-grabbing p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <GripVertical className="h-5 w-5" />
            </div>
          )}
          {/* Use consistent fund colors from our shared constants */}
          <Badge
            variant="outline"
            className={`${FUND_TAILWIND_COLORS[fundNumber as keyof typeof FUND_TAILWIND_COLORS]?.badge || 'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800'}`}
          >
            {fundNumber}
          </Badge>
          <div className="font-medium">{description || `Investment Fund ${fundNumber}`}</div>
        </div>

        {!readOnly && (
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveUp?.(id)}
              disabled={isFirst}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <ArrowUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveDown?.(id)}
              disabled={isLast}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export function WithdrawalPriorityList({ inputData, setInputData, readOnly }: WithdrawalPriorityListProps) {
  // Get the active investment funds count
  const activeInvestmentFunds = Array.from({ length: 5 }, (_, i) => i + 1)
    .filter(i => inputData[`initial_investment${i}` as keyof InputData] !== undefined &&
                 Number(inputData[`initial_investment${i}` as keyof InputData]) > 0)
    .length || 1;

  // Initialize priorities if not set
  const [items, setItems] = useState<number[]>([]);

  useEffect(() => {
    // Initialize with default priorities if not set
    if (!inputData.withdrawal_priorities || inputData.withdrawal_priorities.length !== activeInvestmentFunds) {
      const defaultPriorities = Array.from({ length: activeInvestmentFunds }, (_, i) => i + 1);
      setItems(defaultPriorities);

      // Update the input data with default priorities
      if (!readOnly) {
        setInputData(prevData => ({
          ...prevData,
          withdrawal_priorities: defaultPriorities
        }));
      }
    } else {
      setItems(inputData.withdrawal_priorities);
    }
  }, [inputData.withdrawal_priorities, activeInvestmentFunds, setInputData, readOnly]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(Number(active.id));
        const newIndex = items.indexOf(Number(over.id));

        const newItems = arrayMove(items, oldIndex, newIndex);

        // Update the input data with new priorities
        setInputData(prevData => ({
          ...prevData,
          withdrawal_priorities: newItems
        }));

        return newItems;
      });
    }
  };

  const handleMoveUp = (id: number) => {
    const index = items.indexOf(id);
    if (index > 0) {
      const newItems = arrayMove(items, index, index - 1);
      setItems(newItems);

      // Update the input data with new priorities
      setInputData(prevData => ({
        ...prevData,
        withdrawal_priorities: newItems
      }));
    }
  };

  const handleMoveDown = (id: number) => {
    const index = items.indexOf(id);
    if (index < items.length - 1) {
      const newItems = arrayMove(items, index, index + 1);
      setItems(newItems);

      // Update the input data with new priorities
      setInputData(prevData => ({
        ...prevData,
        withdrawal_priorities: newItems
      }));
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <LabelWithTooltip
          label="Withdrawal Priority Order"
          tooltipText="Funds will be withdrawn in this order. The first fund will be completely exhausted before moving to the next one." htmlFor={''}        />
      </div>

      <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-md">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={items}
            strategy={verticalListSortingStrategy}
          >
            {items.map((id, index) => (
              <SortableItem
                key={id}
                id={id}
                fundNumber={id}
                description={inputData[`investment_description${id}` as keyof InputData] as string || `Investment Fund ${id}`}
                readOnly={readOnly}
                onMoveUp={handleMoveUp}
                onMoveDown={handleMoveDown}
                isFirst={index === 0}
                isLast={index === items.length - 1}
              />
            ))}
          </SortableContext>
        </DndContext>

        {items.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            No investment funds available
          </div>
        )}
      </div>
    </div>
  );
}
