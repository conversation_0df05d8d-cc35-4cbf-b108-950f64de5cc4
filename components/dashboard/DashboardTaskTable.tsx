'use client';

import React, { useState, useMemo } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { startOfToday, startOfWeek, endOfWeek, isSameDay, isWithinInterval, parseISO } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { format } from 'date-fns';

interface Comment {
  id: number;
  task_id: number;
  content: string;
  created_at: string;
  user_id: string;
}

interface Task {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  comments: Comment[];
  assigned_to?: string;
  assigned_name?: string;
}

interface DashboardTaskTableProps {
  data: Task[];
  onDataChange: () => Promise<void>;
  onEditTask?: (task: Task) => void;
  dateFilter: 'today' | 'week';
  onDateFilterChange: (filter: 'today' | 'week') => void;
}

export function DashboardTaskTable({
  data,
  onDataChange,
  onEditTask,
  dateFilter,
  onDateFilterChange
}: DashboardTaskTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const supabase = createClient();

  // Memoize filtered data
  const filteredData = useMemo(() => {
    let filteredTasks = [...data];

    // Date filtering
    const today = startOfToday();
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 }); // Monday
    const weekEnd = endOfWeek(new Date(), { weekStartsOn: 1 }); // Sunday

    filteredTasks = filteredTasks.filter(task => {
      const dueDate = parseISO(task.due_date);
      switch (dateFilter) {
        case "today":
          return isSameDay(dueDate, today);
        case "week":
          return isWithinInterval(dueDate, { start: weekStart, end: weekEnd });
        default:
          return true;
      }
    });

    return filteredTasks;
  }, [data, dateFilter]);

  const columns: ColumnDef<Task>[] = [
    {
      accessorKey: "title",
      header: "Task Name",
      cell: ({ row }) => (
        <button
          onClick={() => {
            if (onEditTask) {
              onEditTask(row.original);
            }
          }}
          className="text-left hover:underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded"
        >
          {row.getValue("title")}
        </button>
      ),
    },
    {
      accessorKey: "household_name",
      header: "Household",
      cell: ({ row }) => <div>{row.getValue("household_name")}</div>,
    },
    {
      accessorKey: "importance",
      header: "Importance",
      cell: ({ row }) => {
        const importance = (row.getValue("importance") as string || "").toLowerCase();
        const colorMap: { [key: string]: string } = {
          high: "bg-red-100 text-red-800",
          medium: "bg-blue-100 text-blue-800",
          low: "bg-yellow-100 text-yellow-800"
        };
        const displayText = importance.charAt(0).toUpperCase() + importance.slice(1);
        return (
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[importance] || "bg-gray-100 text-gray-800"}`}>
            {displayText}
          </div>
        );
      },
    },
    {
      accessorKey: "due_date",
      header: "Due Date",
      cell: ({ row }) => {
        const dueDate = parseISO(row.getValue("due_date"));
        const isOverdue = dueDate < new Date();
        return (
          <div className={isOverdue ? "text-red-600 font-medium" : ""}>
            {format(dueDate, 'EEE, MMM d')}
          </div>
        );
      },
    },
    {
      accessorKey: "assigned_name",
      header: "Assigned To",
      cell: ({ row }) => <div>{row.getValue("assigned_name") || "Unassigned"}</div>,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const task = row.original;

        const handleDelete = async () => {
          const { error } = await supabase
            .from('tasks')
            .delete()
            .eq('id', task.id);

          if (!error) {
            onDataChange();
          }
        };

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEditTask && (
                <DropdownMenuItem onClick={() => onEditTask(task)}>
                  Edit Task
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handleDelete}>
                Delete Task
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  return (
    <div className="space-y-2">
      <div className="rounded-md border overflow-auto max-h-[calc(100%-10px)]">
        <Table>
          <TableHeader className="sticky top-0 bg-white z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                const dueDate = new Date(row.getValue("due_date"));
                const isOverdue = dueDate < new Date();

                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={isOverdue ? "bg-red-50" : ""}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No tasks found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
