'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { createClient } from '@/utils/supabase/client';
import { DashboardTaskTable } from '@/components/dashboard/DashboardTaskTable';
import TaskModal from '@/components/modals/TaskModal';
import { startOfToday, endOfDay, startOfWeek, endOfWeek } from 'date-fns';

interface Comment {
  id: number;
  task_id: number;
  content: string;
  created_at: string;
  user_id: string;
}

interface Task {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  comments: Comment[];
  assigned_to?: string;
  assigned_name?: string;
}

export default function TasksDueSection() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [fullTaskData, setFullTaskData] = useState<any>(null);
  const [dateFilter, setDateFilter] = useState<'today' | 'week'>('today');

  const supabase = createClient();

  useEffect(() => {
    fetchTasks();
  }, [dateFilter]);

  const fetchTasks = async () => {
    setIsLoading(true);
    try {
      // Set date range based on date filter
      let startDate, endDate;

      if (dateFilter === 'today') {
        startDate = startOfToday();
        endDate = endOfDay(startOfToday());
      } else {
        // Week view - Monday to Sunday
        startDate = startOfWeek(new Date(), { weekStartsOn: 1 }); // Start on Monday
        endDate = endOfWeek(new Date(), { weekStartsOn: 1 }); // End on Sunday
      }

      // Set time to beginning and end of day
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);

      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          *,
          households (householdName),
          task_comments (*)
        `)
        .gte('due_date', startDate.toISOString())
        .lte('due_date', endDate.toISOString())
        .order('due_date', { ascending: true });

      if (tasksError) throw tasksError;

      const formattedTasks = tasksData?.map(task => ({
        id: task.id,
        title: task.title,
        due_date: task.due_date,
        household_name: task.households.householdName,
        household_id: task.household_id,
        importance: task.importance || 'medium',
        content: task.content || '',
        status: task.status || '',
        comments: task.task_comments || [],
        created_at: task.created_at,
        updated_at: task.updated_at,
        user_id: task.user_id,
      })) || [];

      setTasks(formattedTasks);
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTaskDetails = async (taskId: number) => {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('Error fetching task details:', error);
      return null;
    }

    return data;
  };

  return (
    <Card className="flex flex-col h-full overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle>Tasks Due</CardTitle>
          <div className="flex space-x-2">
            <Button
              variant={dateFilter === 'today' ? "default" : "outline"}
              size="sm"
              onClick={() => setDateFilter('today')}
            >
              Today
            </Button>
            <Button
              variant={dateFilter === 'week' ? "default" : "outline"}
              size="sm"
              onClick={() => setDateFilter('week')}
            >
              This Week
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow overflow-hidden p-0 pt-2 px-4 max-h-[calc(100%-60px)]" style={{ height: 'calc(100% - 60px)' }}>
        <div className="h-full">
          <DashboardTaskTable
            data={tasks}
            onDataChange={fetchTasks}
            dateFilter={dateFilter}
            onDateFilterChange={setDateFilter}
            onEditTask={(task) => {
              setSelectedTask(task);
              setFullTaskData(task);
              setIsTaskModalOpen(true);
            }}
          />
        </div>
      </CardContent>

      {selectedTask && fullTaskData && (
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={() => {
            setIsTaskModalOpen(false);
            setSelectedTask(null);
            setFullTaskData(null);
          }}
          task={{
            id: fullTaskData.id,
            title: fullTaskData.title,
            due_date: fullTaskData.due_date,
            household_id: fullTaskData.household_id,
            importance: fullTaskData.importance || '',
            content: fullTaskData.content || '',
            status: fullTaskData.status || '',
            comments: fullTaskData.comments || []
          }}
          onSave={async () => {
            await fetchTasks();
          }}
          householdName={selectedTask.household_name}
          readonly={false}
        />
      )}
    </Card>
  );
}
