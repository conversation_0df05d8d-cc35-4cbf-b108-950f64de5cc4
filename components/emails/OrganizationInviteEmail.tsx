import * as React from 'react';
import BaseEmailTemplate, { COLORS } from './BaseEmailTemplate';

interface OrganizationInviteEmailProps {
  organizationName: string;
  inviteUrl: string;
  siteUrl: string;
}

export const OrganizationInviteEmail: React.FC<Readonly<OrganizationInviteEmailProps>> = ({
  organizationName,
  inviteUrl,
  siteUrl,
}) => {
  return (
    <BaseEmailTemplate title="You've Been Invited!" siteUrl={siteUrl}>
      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Hello,</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>You've been invited to join <strong>{organizationName}</strong> on Wealthie, a financial planning platform.</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>To accept this invitation, please click the button below to create your account:</p>

      <div style={{
        textAlign: 'center',
        margin: '30px 0',
      }}>
        <a
          href={inviteUrl}
          style={{
            display: 'inline-block',
            backgroundColor: COLORS.primary,
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          Accept Invitation
        </a>
      </div>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>This invitation will expire in 48 hours.</p>
    </BaseEmailTemplate>
  );
};

export default OrganizationInviteEmail;
