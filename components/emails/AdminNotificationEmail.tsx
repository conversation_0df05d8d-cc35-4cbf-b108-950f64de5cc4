import * as React from 'react';
import BaseEmailTemplate, { COLORS } from './BaseEmailTemplate';

interface AdminNotificationEmailProps {
  userData: {
    email: string;
    firstName: string;
    lastName: string;
    organizationName: string;
    phone: string;
  };
  siteUrl: string;
}

export const AdminNotificationEmail: React.FC<Readonly<AdminNotificationEmailProps>> = ({
  userData,
  siteUrl,
}) => {
  return (
    <BaseEmailTemplate title="New User Sign-Up" siteUrl={siteUrl}>
      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>A new user has signed up for Wealthie:</p>

      <div style={{
        margin: '20px 0',
        border: `1px solid ${COLORS.border}`,
        borderRadius: '4px',
        padding: '15px',
        backgroundColor: '#f9f9f9',
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
        }}>
          <tbody>
            <tr>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
                fontWeight: 'bold',
                width: '120px',
              }}>Name:</td>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
              }}>{userData.firstName} {userData.lastName}</td>
            </tr>
            <tr>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
                fontWeight: 'bold',
              }}>Email:</td>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
              }}>{userData.email}</td>
            </tr>
            <tr>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
                fontWeight: 'bold',
              }}>Phone:</td>
              <td style={{
                padding: '8px',
                borderBottom: `1px solid ${COLORS.border}`,
              }}>{userData.phone}</td>
            </tr>
            <tr>
              <td style={{
                padding: '8px',
                fontWeight: 'bold',
              }}>Organization:</td>
              <td style={{
                padding: '8px',
              }}>{userData.organizationName}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Please review this user's account.</p>

      <div style={{
        textAlign: 'center',
        margin: '30px 0',
      }}>
        <a
          href={`${siteUrl}/protected/admin/users`}
          style={{
            display: 'inline-block',
            backgroundColor: COLORS.primary,
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          View User Management
        </a>
      </div>
    </BaseEmailTemplate>
  );
};

export default AdminNotificationEmail;
