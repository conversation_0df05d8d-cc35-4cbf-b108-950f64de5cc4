import * as React from 'react';

interface CalendarInviteEmailProps {
  title: string;
  date: string;
  startTime?: string;
  endTime?: string;
  isAllDay: boolean;
  description?: string;
  location?: string;
  organizerName: string;
  googleCalendarUrl: string;
  outlookCalendarUrl: string;
  eventId: number;
  attendees: Array<{
    name?: string;
    email: string;
    role: string;
    isCurrentRecipient?: boolean;
  }>;
  // Direct RSVP URLs with the recipient's email already included
  rsvpYesUrl: string;
  rsvpNoUrl: string;
  rsvpMaybeUrl: string;
  recipientEmail: string;
}

// SVG Logo component
const Logo = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 40 40"
    width="60"
    height="60"
    style={{ display: 'block', margin: '0 auto', borderRadius: '50%', overflow: 'hidden' }}
  >
    <style>
      {`
        .circle { fill: #f0f0f0; }
        .tree-left { fill: #1a5f38; }
        .tree-middle { fill: #2e8b57; }
        .tree-right { fill: #3cb371; }
      `}
    </style>

    {/* Background Circle */}
    <circle className="circle" cx="20" cy="20" r="20" />

    {/* Clipping Path for Trees */}
    <clipPath id="circle-clip">
      <circle cx="20" cy="20" r="20" />
    </clipPath>

    <g clipPath="url(#circle-clip)">
      {/* Left Tree */}
      <polygon className="tree-left" points="6,40 16,12 26,40" />
      <polygon className="tree-left" points="8,40 16,16 24,40" />
      <polygon className="tree-left" points="10,36 16,14 22,36" />

      {/* Middle Tree */}
      <polygon className="tree-middle" points="13,40 22,10 31,40" />
      <polygon className="tree-middle" points="15,40 22,14 29,40" />
      <polygon className="tree-middle" points="17,36 22,12 27,36" />

      {/* Right Tree */}
      <polygon className="tree-right" points="20,40 30,12 40,40" />
      <polygon className="tree-right" points="22,40 30,16 38,40" />
      <polygon className="tree-right" points="24,36 30,14 36,36" />
    </g>
  </svg>
);

export const CalendarInviteEmailNew: React.FC<Readonly<CalendarInviteEmailProps>> = ({
  title,
  date,
  startTime,
  endTime,
  isAllDay,
  description,
  location,
  organizerName,
  googleCalendarUrl,
  outlookCalendarUrl,
  eventId,
  attendees,
  rsvpYesUrl,
  rsvpNoUrl,
  rsvpMaybeUrl,
  recipientEmail,
}) => {
  // Format the date and time for display
  const formattedDate = new Date(date).toLocaleDateString('en-NZ', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const timeDisplay = isAllDay
    ? 'All day'
    : `${startTime} - ${endTime}`;

  // Create display-friendly attendee list
  const displayAttendees = attendees.map(attendee => {
    if (attendee.isCurrentRecipient) {
      // This is the current recipient
      return {
        ...attendee,
        displayName: attendee.name || 'You',
        displayEmail: '(you)'
      };
    }
    return {
      ...attendee,
      displayName: attendee.name || attendee.email,
      displayEmail: attendee.name ? `(${attendee.email})` : ''
    };
  });

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      padding: '0',
      backgroundColor: '#ffffff',
      color: '#333333',
    }}>
      {/* Email Header */}
      <div style={{
        backgroundColor: '#ffffff',
        padding: '30px 20px',
        textAlign: 'center',
        borderBottom: '1px solid #f0f0f0',
      }}>
        <Logo />
        <h1 style={{
          fontSize: '24px',
          fontWeight: '600',
          color: '#2e8b57',
          margin: '20px 0 0 0',
        }}>Calendar Invitation</h1>
      </div>

      {/* Event Details */}
      <div style={{
        padding: '30px 20px',
        backgroundColor: '#ffffff',
      }}>
        <h2 style={{
          fontSize: '20px',
          fontWeight: '600',
          color: '#333333',
          margin: '0 0 20px 0',
        }}>{title}</h2>

        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          marginBottom: '25px',
        }}>
          <tbody>
            <tr>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                width: '100px',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#666666',
                }}>When</p>
              </td>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  color: '#333333',
                }}>{formattedDate}</p>
              </td>
            </tr>
            <tr>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#666666',
                }}>Time</p>
              </td>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  color: '#333333',
                }}>{timeDisplay}</p>
              </td>
            </tr>
            <tr>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#666666',
                }}>Organizer</p>
              </td>
              <td style={{
                padding: '10px 0',
                borderBottom: '1px solid #f0f0f0',
                verticalAlign: 'top',
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '14px',
                  color: '#333333',
                }}>{organizerName}</p>
              </td>
            </tr>
            {location && (
              <tr>
                <td style={{
                  padding: '10px 0',
                  borderBottom: '1px solid #f0f0f0',
                  verticalAlign: 'top',
                }}>
                  <p style={{
                    margin: '0',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: '#666666',
                  }}>Location</p>
                </td>
                <td style={{
                  padding: '10px 0',
                  borderBottom: '1px solid #f0f0f0',
                  verticalAlign: 'top',
                }}>
                  <p style={{
                    margin: '0',
                    fontSize: '14px',
                    color: '#333333',
                  }}>{location}</p>
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {description && (
          <div style={{
            marginBottom: '25px',
            padding: '15px',
            backgroundColor: '#f9f9f9',
            borderRadius: '4px',
          }}>
            <p style={{
              margin: '0',
              fontSize: '14px',
              lineHeight: '1.5',
              color: '#333333',
              whiteSpace: 'pre-line',
            }}>{description}</p>
          </div>
        )}
      </div>

      {/* RSVP Section */}
      <div style={{
        padding: '0 20px 30px 20px',
        textAlign: 'center',
      }}>
        <p style={{
          fontSize: '16px',
          fontWeight: '600',
          color: '#333333',
          margin: '0 0 15px 0',
        }}>Will you attend?</p>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          margin: '0 auto',
          maxWidth: '400px',
        }}>
          <div style={{
            width: '33.33%',
            padding: '0 5px',
          }}>
            <a
              href={rsvpYesUrl}
              style={{
                display: 'block',
                backgroundColor: '#2e8b57',
                color: '#ffffff',
                padding: '10px',
                borderRadius: '4px',
                textDecoration: 'none',
                fontWeight: '600',
                fontSize: '14px',
                textAlign: 'center',
              }}
            >
              Yes
            </a>
          </div>
          <div style={{
            width: '33.33%',
            padding: '0 5px',
          }}>
            <a
              href={rsvpMaybeUrl}
              style={{
                display: 'block',
                backgroundColor: '#f0f0f0',
                color: '#333333',
                padding: '10px',
                borderRadius: '4px',
                textDecoration: 'none',
                fontWeight: '600',
                fontSize: '14px',
                textAlign: 'center',
              }}
            >
              Maybe
            </a>
          </div>
          <div style={{
            width: '33.33%',
            padding: '0 5px',
          }}>
            <a
              href={rsvpNoUrl}
              style={{
                display: 'block',
                backgroundColor: '#f0f0f0',
                color: '#333333',
                padding: '10px',
                borderRadius: '4px',
                textDecoration: 'none',
                fontWeight: '600',
                fontSize: '14px',
                textAlign: 'center',
              }}
            >
              No
            </a>
          </div>
        </div>
      </div>

      {/* Add to Calendar Section */}
      <div style={{
        padding: '20px',
        backgroundColor: '#f9f9f9',
        textAlign: 'center',
        borderTop: '1px solid #f0f0f0',
        borderBottom: '1px solid #f0f0f0',
      }}>
        <p style={{
          fontSize: '14px',
          color: '#666666',
          margin: '0 0 15px 0',
        }}>Add to your calendar</p>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          flexWrap: 'wrap',
          gap: '10px',
        }}>
          <a
            href={googleCalendarUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'inline-block',
              backgroundColor: '#ffffff',
              color: '#333333',
              padding: '10px 15px',
              borderRadius: '4px',
              textDecoration: 'none',
              fontWeight: '600',
              fontSize: '14px',
              border: '1px solid #e0e0e0',
            }}
          >
            Google Calendar
          </a>

          <a
            href={outlookCalendarUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'inline-block',
              backgroundColor: '#ffffff',
              color: '#333333',
              padding: '10px 15px',
              borderRadius: '4px',
              textDecoration: 'none',
              fontWeight: '600',
              fontSize: '14px',
              border: '1px solid #e0e0e0',
            }}
          >
            Outlook Calendar
          </a>
        </div>
      </div>

      {/* Attendees Section */}
      {attendees.length > 0 && (
        <div style={{
          padding: '30px 20px',
          borderBottom: '1px solid #f0f0f0',
        }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#333333',
            margin: '0 0 15px 0',
          }}>Attendees</h3>

          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
          }}>
            <tbody>
              {displayAttendees.map((attendee, index) => (
                <tr key={index}>
                  <td style={{
                    padding: '8px 0',
                    borderBottom: index < displayAttendees.length - 1 ? '1px solid #f0f0f0' : 'none',
                    fontSize: '14px',
                    color: '#333333',
                  }}>
                    {attendee.displayName} {attendee.displayEmail}
                  </td>
                  <td style={{
                    padding: '8px 0',
                    borderBottom: index < displayAttendees.length - 1 ? '1px solid #f0f0f0' : 'none',
                    fontSize: '14px',
                    color: '#666666',
                    textAlign: 'right',
                  }}>
                    {attendee.role === 'REQ-PARTICIPANT' ? 'Required' : 'Optional'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Footer */}
      <div style={{
        padding: '20px',
        textAlign: 'center',
        fontSize: '12px',
        color: '#999999',
      }}>
        <p style={{ margin: '0 0 10px 0' }}>
          This invitation was sent from Wealthie Calendar.
        </p>
        <p style={{ margin: '0' }}>
          Please find the calendar invitation attached.
        </p>
      </div>
    </div>
  );
};
