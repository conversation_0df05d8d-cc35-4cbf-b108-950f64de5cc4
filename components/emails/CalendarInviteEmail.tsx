import * as React from 'react';

interface CalendarInviteEmailProps {
  title: string;
  date: string;
  startTime?: string;
  endTime?: string;
  isAllDay: boolean;
  description?: string;
  organizerName: string;
  googleCalendarUrl: string;
  outlookCalendarUrl: string;
  attendees: Array<{
    name?: string;
    email: string;
    role: string;
  }>;
}

export const CalendarInviteEmail: React.FC<Readonly<CalendarInviteEmailProps>> = ({
  title,
  date,
  startTime,
  endTime,
  isAllDay,
  description,
  organizerName,
  googleCalendarUrl,
  outlookCalendarUrl,
  attendees,
}) => {
  // Format the date and time for display
  const formattedDate = new Date(date).toLocaleDateString('en-NZ', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const timeDisplay = isAllDay 
    ? 'All day' 
    : `${startTime} - ${endTime}`;

  return (
    <div style={{
      fontFamily: 'Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      padding: '20px',
      color: '#333',
    }}>
      {/* Header with Logo */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
      }}>
        <img 
          src="https://www.wealthie.co.nz/images/logo.svg" 
          alt="Wealthie Logo" 
          width="150"
          style={{
            marginBottom: '15px',
          }}
        />
        <h1 style={{
          fontSize: '24px',
          color: '#4f46e5',
          margin: '0',
        }}>Calendar Invitation</h1>
      </div>

      {/* Event Details */}
      <div style={{
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '25px',
        border: '1px solid #e5e7eb',
      }}>
        <h2 style={{
          fontSize: '20px',
          margin: '0 0 15px 0',
          color: '#111827',
        }}>{title}</h2>
        
        <div style={{
          marginBottom: '15px',
        }}>
          <p style={{
            margin: '0 0 5px 0',
            fontSize: '16px',
          }}>
            <strong>When:</strong> {formattedDate}
          </p>
          <p style={{
            margin: '0 0 5px 0',
            fontSize: '16px',
          }}>
            <strong>Time:</strong> {timeDisplay}
          </p>
          <p style={{
            margin: '0 0 5px 0',
            fontSize: '16px',
          }}>
            <strong>Organizer:</strong> {organizerName}
          </p>
        </div>

        {description && (
          <div style={{
            marginTop: '15px',
            padding: '10px',
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            border: '1px solid #e5e7eb',
          }}>
            <p style={{
              margin: '0',
              fontSize: '14px',
              whiteSpace: 'pre-line',
            }}>{description}</p>
          </div>
        )}
      </div>

      {/* Add to Calendar Buttons */}
      <div style={{
        textAlign: 'center',
        marginBottom: '25px',
      }}>
        <p style={{
          fontSize: '16px',
          marginBottom: '15px',
        }}>Add this event to your calendar:</p>
        
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '15px',
        }}>
          <a 
            href={googleCalendarUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'inline-block',
              backgroundColor: '#4285F4',
              color: '#ffffff',
              padding: '10px 20px',
              borderRadius: '4px',
              textDecoration: 'none',
              fontWeight: 'bold',
              fontSize: '14px',
            }}
          >
            Add to Google Calendar
          </a>
          
          <a 
            href={outlookCalendarUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'inline-block',
              backgroundColor: '#0078D4',
              color: '#ffffff',
              padding: '10px 20px',
              borderRadius: '4px',
              textDecoration: 'none',
              fontWeight: 'bold',
              fontSize: '14px',
            }}
          >
            Add to Outlook
          </a>
        </div>
      </div>

      {/* Attendees */}
      {attendees.length > 0 && (
        <div style={{
          marginBottom: '25px',
          padding: '15px',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb',
        }}>
          <h3 style={{
            fontSize: '16px',
            margin: '0 0 10px 0',
            color: '#111827',
          }}>Attendees:</h3>
          
          <ul style={{
            margin: '0',
            padding: '0 0 0 20px',
          }}>
            {attendees.map((attendee, index) => (
              <li key={index} style={{
                fontSize: '14px',
                marginBottom: '5px',
              }}>
                {attendee.name ? `${attendee.name} (${attendee.email})` : attendee.email}
                {attendee.role === 'REQ-PARTICIPANT' && ' - Required'}
                {attendee.role === 'OPT-PARTICIPANT' && ' - Optional'}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Footer */}
      <div style={{
        textAlign: 'center',
        borderTop: '1px solid #e5e7eb',
        paddingTop: '15px',
        fontSize: '12px',
        color: '#6b7280',
      }}>
        <p>This invitation was sent from Wealthie Calendar.</p>
        <p>Please find the calendar invitation attached.</p>
      </div>
    </div>
  );
};
