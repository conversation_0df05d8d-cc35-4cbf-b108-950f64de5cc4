import * as React from 'react';
import BaseEmailTemplate, { COLORS } from './BaseEmailTemplate';

interface VerificationEmailProps {
  token: string;
  tokenHash: string;
  redirectTo: string;
  siteUrl: string;
}

export const VerificationEmail: React.FC<Readonly<VerificationEmailProps>> = ({
  token,
  tokenHash,
  redirectTo,
  siteUrl,
}) => {
  const confirmUrl = `${siteUrl}/auth/confirm?token_hash=${tokenHash}&type=email&next=${redirectTo}`;

  return (
    <BaseEmailTemplate title="Confirm Your Email Address" siteUrl={siteUrl}>
      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Hello,</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Thank you for signing up for Wealthie. To complete your registration, please confirm your email address by clicking the button below:</p>

      <div style={{
        textAlign: 'center',
        margin: '30px 0',
      }}>
        <a
          href={confirmUrl}
          style={{
            display: 'inline-block',
            backgroundColor: COLORS.primary,
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          Confirm Email Address
        </a>
      </div>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>If you didn't create an account with us, you can safely ignore this email.</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>This link will expire in 24 hours.</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Alternatively, you can use this code: <strong>{token}</strong></p>
    </BaseEmailTemplate>
  );
};

export default VerificationEmail;
