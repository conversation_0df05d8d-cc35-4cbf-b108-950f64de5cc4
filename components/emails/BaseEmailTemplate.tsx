import * as React from 'react';

// Define the Wealthie brand colors
const COLORS = {
  primary: '#1a5f38', // Dark green from the Logo
  secondary: '#2e8b57', // Medium green from the Logo
  accent: '#3cb371', // Light green from the Logo
  background: '#ffffff',
  text: '#333333',
  textLight: '#999999',
  border: '#f0f0f0',
};

interface BaseEmailTemplateProps {
  title: string;
  children: React.ReactNode;
  siteUrl: string;
}

export const BaseEmailTemplate: React.FC<Readonly<BaseEmailTemplateProps>> = ({
  title,
  children,
  siteUrl,
}) => {
  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      padding: '0',
      backgroundColor: COLORS.background,
      color: COLORS.text,
    }}>
      <div style={{
        backgroundColor: COLORS.background,
        padding: '30px 20px',
        textAlign: 'center',
        borderBottom: `1px solid ${COLORS.border}`,
      }}>
        {/* Logo */}
        <div style={{
          marginBottom: '15px',
          display: 'inline-block',
        }}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="150" height="150">
            {/* Background Circle */}
            <circle fill="#f0f0f0" cx="20" cy="20" r="19.6" />
            
            {/* Clipping Path for Trees */}
            <clipPath id="circle-clip">
              <circle cx="20" cy="20" r="19.6" />
            </clipPath>
            
            <g clipPath="url(#circle-clip)">
              {/* Left Tree */}
              <polygon fill={COLORS.primary} points="6,40 16,12 26,40" />
              <polygon fill={COLORS.primary} points="8,40 16,16 24,40" />
              <polygon fill={COLORS.primary} points="10,36 16,14 22,36" />
              
              {/* Middle Tree */}
              <polygon fill={COLORS.secondary} points="13,40 22,10 31,40" />
              <polygon fill={COLORS.secondary} points="15,40 22,14 29,40" />
              <polygon fill={COLORS.secondary} points="17,36 22,12 27,36" />
              
              {/* Right Tree */}
              <polygon fill={COLORS.accent} points="20,40 30,12 40,40" />
              <polygon fill={COLORS.accent} points="22,40 30,16 38,40" />
              <polygon fill={COLORS.accent} points="24,36 30,14 36,36" />
            </g>
            
            {/* Circle Border */}
            <circle fill="none" stroke="#333" strokeWidth="0.8" cx="20" cy="20" r="19.6" />
          </svg>
        </div>
        <h1 style={{
          fontSize: '24px',
          fontWeight: '600',
          color: COLORS.primary,
          margin: '20px 0 0 0',
        }}>{title}</h1>
      </div>
      
      <div style={{
        padding: '30px 20px',
        backgroundColor: COLORS.background,
      }}>
        {children}
      </div>
      
      <div style={{
        padding: '20px',
        textAlign: 'center',
        fontSize: '12px',
        color: COLORS.textLight,
        borderTop: `1px solid ${COLORS.border}`,
      }}>
        <p style={{ margin: '0 0 10px 0' }}>
          &copy; {new Date().getFullYear()} Wealthie. All rights reserved.
        </p>
        <p style={{ margin: '0' }}>
          Wealthie Ltd, New Zealand
        </p>
      </div>
    </div>
  );
};

export default BaseEmailTemplate;

// Export the colors for use in other email templates
export { COLORS };
