import * as React from 'react';
import BaseEmailTemplate, { COLORS } from './BaseEmailTemplate';

interface PasswordResetEmailProps {
  token: string;
  tokenHash: string;
  redirectTo: string;
  siteUrl: string;
}

export const PasswordResetEmail: React.FC<Readonly<PasswordResetEmailProps>> = ({
  token,
  tokenHash,
  redirectTo,
  siteUrl,
}) => {
  const resetUrl = `${siteUrl}/auth/confirm?token_hash=${tokenHash}&type=recovery&next=${redirectTo}`;

  return (
    <BaseEmailTemplate title="Reset Your Password" siteUrl={siteUrl}>
      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Hello,</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>We received a request to reset your password for your Wealthie account. Click the button below to reset your password:</p>

      <div style={{
        textAlign: 'center',
        margin: '30px 0',
      }}>
        <a
          href={resetUrl}
          style={{
            display: 'inline-block',
            backgroundColor: COLORS.primary,
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 'bold',
            fontSize: '16px',
          }}
        >
          Reset Password
        </a>
      </div>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>If you didn't request a password reset, you can safely ignore this email.</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>This link will expire in 24 hours.</p>

      <p style={{
        fontSize: '16px',
        lineHeight: '1.6',
        color: COLORS.text,
        margin: '0 0 20px 0',
      }}>Alternatively, you can use this code: <strong>{token}</strong></p>
    </BaseEmailTemplate>
  );
};

export default PasswordResetEmail;
