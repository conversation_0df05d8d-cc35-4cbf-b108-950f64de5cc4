import { useState, useRef, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Loader2, Upload, X, Image } from 'lucide-react';
import { toast } from 'sonner';

interface LogoUploadProps {
  userId: string;
  currentLogoPath?: string | null;
  compact?: boolean;
  onLogoUpdate: (logoPath: string | null) => void;
}

export default function LogoUpload({ userId, currentLogoPath, compact = false, onLogoUpdate }: LogoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [logoPath, setLogoPath] = useState<string | null>(currentLogoPath || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const supabase = createClient();

  // Fetch the logo URL when the component mounts or when logo<PERSON><PERSON> changes
  const fetchLogoUrl = async () => {
    if (!logoPath) {
      setLogoUrl(null);
      return;
    }

    try {
      console.log(`Fetching logo URL for path: ${logoPath}`);

      // First try using the server-side API to get the URL (which uses admin privileges)
      try {
        const response = await fetch('/api/get-organization-logo', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ logoPath }),
        });

        const result = await response.json();

        if (response.ok && result.signedUrl) {
          console.log('Successfully fetched logo URL from server');
          setLogoUrl(result.signedUrl);
          return;
        } else {
          console.warn('Server-side URL generation failed, falling back to client-side:', result.error);
        }
      } catch (serverErr) {
        console.warn('Error calling server API for logo URL, falling back to client-side:', serverErr);
      }

      // Fallback to client-side URL generation (might fail due to RLS)
      console.log('Attempting client-side URL generation...');
      const { data, error } = await supabase.storage
        .from('media')
        .createSignedUrl(logoPath, 3600); // 1 hour expiry

      if (error) {
        console.error('Error creating signed URL on client:', error);
        throw error;
      }

      if (data?.signedUrl) {
        console.log('Successfully generated URL on client-side');
        setLogoUrl(data.signedUrl);
      } else {
        console.error('No signed URL returned from Supabase');
        setLogoUrl(null);
      }
    } catch (err) {
      console.error('Error fetching logo URL:', err);
      setLogoUrl(null);
    }
  };

  // Fetch the organization logo path if not provided
  const fetchOrganizationLogoPath = async () => {
    if (logoPath) {
      // If we already have a logo path, no need to fetch
      return;
    }

    try {
      console.log('Fetching organization logo path...');
      const response = await fetch('/api/get-organization-logo-path');

      if (!response.ok) {
        const error = await response.json();
        console.warn('Error fetching organization logo path:', error);
        return;
      }

      const result = await response.json();

      if (result.success && result.logoPath) {
        console.log(`Found organization logo path: ${result.logoPath}`);
        setLogoPath(result.logoPath);
      }
    } catch (err) {
      console.error('Error fetching organization logo path:', err);
    }
  };

  // Call fetchLogoUrl when the component mounts or when logoPath changes
  useEffect(() => {
    fetchLogoUrl();
  }, [logoPath]);

  // Fetch the organization logo path when the component mounts
  useEffect(() => {
    fetchOrganizationLogoPath();
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      validateAndUploadLogo(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      validateAndUploadLogo(file);
    }
  };

  const validateAndUploadLogo = (file: File) => {
    // Check if file is an image
    if (!file.type.match('image/(jpeg|jpg|png|svg\\+xml)')) {
      toast.error('Please select a valid image file (JPG, PNG, or SVG)');
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    uploadLogo(file);
  };

  const uploadLogo = async (file: File) => {
    setIsUploading(true);

    try {
      console.log('Starting logo upload process...');

      // First, ensure the media bucket exists
      console.log('Creating storage bucket if it does not exist...');
      const bucketResponse = await fetch('/api/create-storage-bucket', {
        method: 'POST',
      });

      if (!bucketResponse.ok) {
        const bucketError = await bucketResponse.json();
        console.error('Error creating storage bucket:', bucketError);
        throw new Error(bucketError.error || 'Failed to create storage bucket');
      }

      console.log('Storage bucket ready, preparing to upload logo...');

      // The server will generate a unique filename and path

      // Skip client-side upload attempts and go straight to server-side upload
      // which uses the admin client with service role key
      console.log('Using server-side upload with admin privileges...');

      // Create a FormData object for the server-side upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);

      // Use the server-side upload endpoint
      const response = await fetch('/api/upload-logo', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('Server-side upload failed:', result.error);
        throw new Error(result.error || 'Server-side upload failed');
      }

      console.log('Logo uploaded successfully:', result);

      // Update the file path with the one returned from the server
      const filePath = result.filePath;

      // Update the profile with the logo path using the API
      try {
        const response = await fetch('/api/update-logo', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ logoPath: filePath }),
        });

        const result = await response.json();

        if (!response.ok) {
          console.warn('Error updating profile with logo path:', result.error);
          // Continue with the process even if profile update fails
        }
      } catch (updateErr) {
        console.error('Error calling update-logo API:', updateErr);
        // Continue with the process even if profile update fails
      }

      // Update local state
      setLogoPath(filePath);

      // Fetch the new logo URL using signed URL
      const { data: urlData, error: urlError } = await supabase.storage
        .from('media')
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (urlError) {
        console.error('Error creating signed URL:', urlError);
      } else if (urlData?.signedUrl) {
        setLogoUrl(urlData.signedUrl);
      }

      // Notify parent component
      onLogoUpdate(filePath);

      toast.success('Logo uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading logo:', err);
      toast.error(`Failed to upload logo: ${err.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveLogo = async () => {
    if (!logoPath) return;

    setIsUploading(true);

    try {
      // Try to delete the file from storage
      const { error: deleteError } = await supabase.storage
        .from('media')
        .remove([logoPath]);

      if (deleteError) {
        console.warn('Client-side delete failed:', deleteError.message);

        // Try server-side delete as fallback
        try {
          const response = await fetch('/api/delete-logo', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ logoPath }),
          });

          const result = await response.json();

          if (!response.ok) {
            console.error('Server-side delete failed:', result.error);
            // Continue anyway to update the profile
          }
        } catch (serverDeleteErr) {
          console.error('Error calling delete-logo API:', serverDeleteErr);
          // Continue anyway to update the profile
        }
      }

      // Update the profile to remove the logo path using the API
      try {
        const response = await fetch('/api/update-logo', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ logoPath: null }),
        });

        const result = await response.json();

        if (!response.ok) {
          console.warn('Error updating profile to remove logo path:', result.error);
          // Continue with the process even if profile update fails
        }
      } catch (updateErr) {
        console.error('Error calling update-logo API:', updateErr);
        // Continue with the process even if profile update fails
      }

      // Update local state
      setLogoPath(null);
      setLogoUrl(null);

      // Notify parent component
      onLogoUpdate(null);

      toast.success('Logo removed successfully');
    } catch (err: any) {
      console.error('Error removing logo:', err);
      toast.error(`Failed to remove logo: ${err.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  if (compact) {
    return (
      <div>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/jpeg,image/png,image/jpg,image/svg+xml"
          onChange={handleFileSelect}
        />

        {!logoUrl && !isUploading ? (
          <div className="flex justify-start">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 h-8 text-xs px-2"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-3 w-3" />
              Upload Logo
            </Button>
          </div>
        ) : (
          <div className="flex justify-start items-center">
            {isUploading ? (
              <div className="flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span className="text-xs">Uploading...</span>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs px-2 py-0 h-6 text-primary hover:text-primary"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Change
                </Button>
                <span className="text-muted-foreground">|</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs px-2 py-0 h-6 text-destructive hover:text-destructive"
                  onClick={handleRemoveLogo}
                >
                  Remove
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  // Standard (non-compact) view
  return (
    <div className="space-y-4">
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/jpeg,image/png,image/jpg,image/svg+xml"
        onChange={handleFileSelect}
      />

      {!logoUrl && !isUploading ? (
        <div
          className="border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <Image className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
          <p className="text-sm font-medium mb-1">Upload Company Logo</p>
          <p className="text-xs text-muted-foreground mb-2">
            Supports JPG, PNG, and SVG (max 5MB)
          </p>
          <Button variant="outline" size="sm" type="button">
            Select File
          </Button>
        </div>
      ) : (
        <div className="border rounded-lg p-3">
          {isUploading ? (
            <div className="flex items-center justify-center py-2 gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <p className="text-sm">Uploading logo...</p>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Company Logo</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleRemoveLogo}
                  className="h-6 w-6"
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">Remove Logo</span>
                </Button>
              </div>
              <div className="flex justify-center">
                <img
                  src={logoUrl || ''}
                  alt="Company Logo"
                  className="max-h-24 max-w-full object-contain"
                />
              </div>
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-7 px-2"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Change Logo
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
