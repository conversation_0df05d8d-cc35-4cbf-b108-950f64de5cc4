import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { HelpCircle } from "lucide-react"

interface LabelWithTooltipProps {
  htmlFor: string;
  label: string;
  tooltipText: string;
}

export function LabelWithTooltip({ htmlFor, label, tooltipText }: LabelWithTooltipProps) {
  return (
    <div className="flex items-center gap-2">
      <Label htmlFor={htmlFor}>{label}</Label>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-3 w-3 text-muted-foreground pb-1" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}