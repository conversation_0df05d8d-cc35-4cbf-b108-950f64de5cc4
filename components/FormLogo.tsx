import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import Image from 'next/image';

interface FormLogoProps {
  orgId?: string; // Optional org_id to override the default behavior
}

export default function FormLogo({ orgId }: FormLogoProps = {}) {
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [orgName, setOrgName] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchLogo = async () => {
      try {
        // First get the current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        // If user is logged in, get their organization info
        if (user) {
          // Get the user's profile to find their organization ID
          const { data: userProfile, error: profileError } = await supabase
            .from('profiles')
            .select('org_id, org_name, logo_path')
            .eq('user_id', user.id)
            .single();

          if (!profileError && userProfile && userProfile.org_name) {
            console.log('Found user profile with org_name:', userProfile.org_name);
            // Set the organization name from the user's profile
            setOrgName(userProfile.org_name);

            // If the user has a logo path, use it
            if (userProfile.logo_path) {
              console.log('Found logo path:', userProfile.logo_path);
              // Get a signed URL for the logo
              const { data: urlData, error: urlError } = await supabase.storage
                .from('media')
                .createSignedUrl(userProfile.logo_path, 3600); // 1 hour expiry

              if (!urlError && urlData?.signedUrl) {
                setLogoUrl(urlData.signedUrl);
              }
            }
            return; // Exit early if we found the user's org info
          } else {
            console.log('User profile not found or missing org_name:', { profileError, userProfile });
          }
        }

        // Fallback for non-logged-in users or if user's org info not found
        // Try to find a profile with the specified org_id or default to Solid Steele Advice (org_id: 3)
        const targetOrgId = orgId || '3'; // Use provided orgId or default to Solid Steele Advice
        console.log('Looking for fallback org with ID:', targetOrgId);

        const { data: orgProfile, error: orgError } = await supabase
          .from('profiles')
          .select('org_name, logo_path')
          .eq('org_id', targetOrgId)
          .not('logo_path', 'is', null)
          .limit(1)
          .maybeSingle();

        if (!orgError && orgProfile && orgProfile.org_name) {
          console.log('Found fallback org profile with org_name:', orgProfile.org_name);
          setOrgName(orgProfile.org_name);

          if (orgProfile.logo_path) {
            console.log('Found fallback logo path:', orgProfile.logo_path);
            // Get a signed URL for the logo
            const { data: urlData, error: urlError } = await supabase.storage
              .from('media')
              .createSignedUrl(orgProfile.logo_path, 3600); // 1 hour expiry

            if (!urlError && urlData?.signedUrl) {
              setLogoUrl(urlData.signedUrl);
            }
          }
        } else {
          console.log('Fallback org profile not found or missing org_name:', { orgError, orgProfile });
        }
      } catch (error) {
        console.error('Error fetching logo:', error);
      }
    };

    fetchLogo();
  }, []);

  if (!logoUrl && !orgName) return null;

  return (
    <div className="flex items-center">
      {logoUrl ? (
        <div className="relative h-8 w-24">
          <Image
            src={logoUrl}
            alt={orgName || 'Company Logo'}
            fill
            style={{ objectFit: 'contain' }}
            priority
          />
        </div>
      ) : (
        <span className="text-sm font-medium text-gray-600">{orgName}</span>
      )}
    </div>
  );
}
