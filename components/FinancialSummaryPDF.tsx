'use client';

import React from 'react';
import { Document, Page, Text, View, StyleSheet, Font } from '@react-pdf/renderer';
import { Asset, Liability } from './tables/Assets_LiabilitiesTable';
import { Income, Expense } from './tables/Income_ExpenseTable';
import { formatCurrency } from '@/lib/utils';
import { format } from 'date-fns';

// Register a standard font that's more reliable for PDF generation
Font.register({
  family: 'Roboto',
  fonts: [
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-light-webfont.ttf', fontWeight: 300 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf', fontWeight: 400 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf', fontWeight: 500 },
    { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf', fontWeight: 700 },
  ],
});

// Register a logo image
const WEALTHIE_GREEN = '#1a5f38';

// Define styles
const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Roboto',
    fontSize: 9,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  header: {
    marginBottom: 25,
    borderBottom: '1px solid #e5e7eb',
    paddingBottom: 15,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    width: 100,
    textAlign: 'right',
  },
  logo: {
    height: 30,
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: 700,
    marginBottom: 5,
    color: WEALTHIE_GREEN,
  },
  subtitle: {
    fontSize: 12,
    fontWeight: 500,
    marginBottom: 8,
  },
  date: {
    fontSize: 9,
    marginBottom: 5,
    color: '#6b7280',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 600,
    marginBottom: 10,
    padding: 6,
    backgroundColor: '#f3f4f6',
    borderLeft: `4px solid ${WEALTHIE_GREEN}`,
    paddingLeft: 10,
    color: '#333333',
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 10,
    borderRadius: 4,
    overflow: 'hidden',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    minHeight: 22,
    alignItems: 'center',
  },
  tableRowEven: {
    backgroundColor: '#f9fafb',
  },
  tableHeader: {
    backgroundColor: '#f3f4f6',
    fontWeight: 600,
    minHeight: 24,
  },
  tableCell: {
    padding: 5,
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
    fontSize: 8,
  },
  tableCellLast: {
    borderRightWidth: 0,
  },
  tableCellAmount: {
    textAlign: 'right',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    marginBottom: 15,
    paddingRight: 5,
  },
  summaryLabel: {
    fontWeight: 600,
    marginRight: 10,
    fontSize: 9,
  },
  summaryValue: {
    fontWeight: 600,
    fontSize: 9,
    color: WEALTHIE_GREEN,
  },
  card: {
    border: '1px solid #e5e7eb',
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
    backgroundColor: '#ffffff',
  },
  cardTitle: {
    fontSize: 10,
    fontWeight: 600,
    marginBottom: 6,
    color: WEALTHIE_GREEN,
    borderBottom: '1px solid #f3f4f6',
    paddingBottom: 4,
  },
  cardRow: {
    flexDirection: 'row',
    marginBottom: 3,
    fontSize: 8,
  },
  cardLabel: {
    flex: 1,
    fontWeight: 600,
    color: '#4b5563',
  },
  cardValue: {
    flex: 2,
    color: '#111827',
  },
  badge: {
    padding: '2px 6px',
    borderRadius: 10,
    fontSize: 7,
    fontWeight: 600,
    marginRight: 5,
  },
  badgeGreen: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
  badgeBlue: {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
  },
  badgeYellow: {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
  badgeRed: {
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  badgeGray: {
    backgroundColor: '#f3f4f6',
    color: '#374151',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 7,
    color: '#6b7280',
    borderTop: '1px solid #e5e7eb',
    paddingTop: 10,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    fontSize: 7,
    color: '#6b7280',
  },
  overviewGrid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 15,
  },
  overviewCard: {
    width: '48%',
    margin: '1%',
    padding: 10,
    backgroundColor: '#f9fafb',
    borderRadius: 4,
    borderLeft: `3px solid ${WEALTHIE_GREEN}`,
  },
  overviewLabel: {
    fontSize: 8,
    color: '#6b7280',
    marginBottom: 4,
  },
  overviewValue: {
    fontSize: 12,
    fontWeight: 600,
    color: '#111827',
  },
  positiveValue: {
    color: '#047857',
  },
  negativeValue: {
    color: '#dc2626',
  },
  twoColumnGrid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  columnCard: {
    width: '48%',
  },
});

// Helper functions
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'achieved':
    case 'implemented':
      return styles.badgeGreen;
    case 'in progress':
      return styles.badgeBlue;
    case 'not started':
      return styles.badgeGray;
    case 'delayed':
    case 'pending client approval':
      return styles.badgeYellow;
    case 'cancelled':
    case 'declined':
      return styles.badgeRed;
    default:
      return styles.badgeGray;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority?.toLowerCase()) {
    case 'high':
      return styles.badgeRed;
    case 'medium':
      return styles.badgeYellow;
    case 'low':
      return styles.badgeBlue;
    default:
      return styles.badgeGray;
  }
};

const calculateAnnualAmount = (amount: number, frequency: string): number => {
  switch (frequency?.toLowerCase()) {
    case 'weekly':
      return amount * 52;
    case 'fortnightly':
      return amount * 26;
    case 'monthly':
      return amount * 12;
    case 'quarterly':
      return amount * 4;
    case 'annually':
    default:
      return amount;
  }
};

interface Insurance {
  id: string;
  type: string;
  provider: string;
  policy_number: string;
  premium: number;
  frequency: string;
  coverage_amount: number;
  renewal_date: string;
  details?: string;
  policy_owner?: string;
  person_insured?: string;
}

interface Goal {
  id: string;
  title: string;
  type: string;
  details?: string;
  start_date?: string;
  achieved_date?: string;
  status: string;
  member?: string;
  target_amount?: number;
  priority?: string;
}

interface Recommendation {
  id: string;
  title: string;
  type: string;
  details?: string;
  created_date?: string;
  implementation_date?: string;
  status: string;
  member?: string;
  priority?: string;
  financial_impact?: number;
  adviser_notes?: string;
}

interface FinancialSummaryPDFProps {
  householdName: string;
  assets: Asset[];
  liabilities: Liability[];
  incomes: Income[];
  expenses: Expense[];
  insurances: Insurance[];
  goals: Goal[];
  recommendations: Recommendation[];
  showAssets: boolean;
  showLiabilities: boolean;
  showIncomes: boolean;
  showExpenses: boolean;
  showInsurances: boolean;
  showGoals: boolean;
  showRecommendations: boolean;
  createdAt: string;
}

export const FinancialSummaryPDF: React.FC<FinancialSummaryPDFProps> = ({
  householdName,
  assets,
  liabilities,
  incomes,
  expenses,
  insurances,
  goals,
  recommendations,
  showAssets,
  showLiabilities,
  showIncomes,
  showExpenses,
  showInsurances,
  showGoals,
  showRecommendations,
  createdAt,
}) => {
  // Calculate totals
  const totalAssets = assets.reduce((sum, asset) => sum + asset.value, 0);
  const totalLiabilities = liabilities.reduce((sum, liability) => sum + liability.amount, 0);
  const netWorth = totalAssets - totalLiabilities;

  const totalAnnualIncome = incomes.reduce(
    (sum, income) => sum + calculateAnnualAmount(income.amount, income.frequency),
    0
  );

  const totalAnnualExpenses = expenses.reduce(
    (sum, expense) => sum + calculateAnnualAmount(expense.amount, expense.frequency),
    0
  );

  const totalAnnualInsurancePremiums = insurances.reduce(
    (sum, insurance) => sum + calculateAnnualAmount(insurance.premium, insurance.frequency),
    0
  );

  const totalInsuranceCoverage = insurances.reduce(
    (sum, insurance) => sum + (insurance.coverage_amount || 0),
    0
  );

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header} fixed>
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Text style={styles.title}>Financial Summary</Text>
              <Text style={styles.subtitle}>{householdName}</Text>
              <Text style={styles.date}>
                Generated on: {format(new Date(createdAt), 'dd MMMM yyyy')}
              </Text>
            </View>
            <View style={styles.headerRight}>
              {/* Wealthie logo would go here if we had an image */}
              <Text style={[styles.title, { fontSize: 14, textAlign: 'right' }]}>Wealthie</Text>
            </View>
          </View>
        </View>

        {/* Financial Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Financial Overview</Text>

          <View style={styles.overviewGrid}>
            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Total Assets</Text>
              <Text style={styles.overviewValue}>{formatCurrency(totalAssets)}</Text>
            </View>

            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Total Liabilities</Text>
              <Text style={styles.overviewValue}>{formatCurrency(totalLiabilities)}</Text>
            </View>

            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Net Worth</Text>
              <Text style={[
                styles.overviewValue,
                netWorth >= 0 ? styles.positiveValue : styles.negativeValue
              ]}>
                {formatCurrency(netWorth)}
              </Text>
            </View>

            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Annual Income</Text>
              <Text style={[styles.overviewValue, styles.positiveValue]}>
                {formatCurrency(totalAnnualIncome)}
              </Text>
            </View>

            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Annual Expenses</Text>
              <Text style={[styles.overviewValue, styles.negativeValue]}>
                {formatCurrency(totalAnnualExpenses)}
              </Text>
            </View>

            <View style={styles.overviewCard}>
              <Text style={styles.overviewLabel}>Annual Insurance Premiums</Text>
              <Text style={styles.overviewValue}>
                {formatCurrency(totalAnnualInsurancePremiums)}
              </Text>
            </View>
          </View>
        </View>

        {/* Assets Section */}
        {showAssets && assets.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Assets</Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.tableHeader]}>
                <Text style={styles.tableCell}>Name</Text>
                <Text style={styles.tableCell}>Type</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Value</Text>
                <Text style={[styles.tableCell, styles.tableCellLast]}>Details</Text>
              </View>
              {assets.map((asset, index) => (
                <View
                  key={asset.id}
                  style={[
                    styles.tableRow,
                    index % 2 === 1 ? styles.tableRowEven : {}
                  ]}
                >
                  <Text style={styles.tableCell}>{asset.name}</Text>
                  <Text style={styles.tableCell}>{asset.type}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>
                    {formatCurrency(asset.value)}
                  </Text>
                  <Text style={[styles.tableCell, styles.tableCellLast]}>
                    {asset.details || '-'}
                  </Text>
                </View>
              ))}
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Assets:</Text>
              <Text style={[styles.summaryValue, styles.positiveValue]}>{formatCurrency(totalAssets)}</Text>
            </View>
          </View>
        )}

        {/* Liabilities Section */}
        {showLiabilities && liabilities.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Liabilities</Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.tableHeader]}>
                <Text style={styles.tableCell}>Name</Text>
                <Text style={styles.tableCell}>Type</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Amount</Text>
                <Text style={styles.tableCell}>Lender</Text>
                <Text style={[styles.tableCell, styles.tableCellLast]}>Interest Rate</Text>
              </View>
              {liabilities.map((liability, index) => (
                <View
                  key={liability.id}
                  style={[
                    styles.tableRow,
                    index % 2 === 1 ? styles.tableRowEven : {}
                  ]}
                >
                  <Text style={styles.tableCell}>{liability.name}</Text>
                  <Text style={styles.tableCell}>{liability.type}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>
                    {formatCurrency(liability.amount)}
                  </Text>
                  <Text style={styles.tableCell}>{liability.lender || '-'}</Text>
                  <Text style={[styles.tableCell, styles.tableCellLast]}>
                    {liability.interest_rate ? `${liability.interest_rate}%` : '-'}
                  </Text>
                </View>
              ))}
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Liabilities:</Text>
              <Text style={[styles.summaryValue, styles.negativeValue]}>{formatCurrency(totalLiabilities)}</Text>
            </View>
          </View>
        )}

        {/* Income Section */}
        {showIncomes && incomes.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Income</Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.tableHeader]}>
                <Text style={styles.tableCell}>Source</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Amount</Text>
                <Text style={styles.tableCell}>Frequency</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Annual Amount</Text>
                <Text style={styles.tableCell}>Type</Text>
                <Text style={[styles.tableCell, styles.tableCellLast]}>Assigned To</Text>
              </View>
              {incomes.map((income, index) => (
                <View key={income.id} style={[
                  styles.tableRow,
                  index % 2 === 1 ? styles.tableRowEven : {}
                ]}>
                  <Text style={styles.tableCell}>{income.source}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>{formatCurrency(income.amount)}</Text>
                  <Text style={styles.tableCell}>{income.frequency}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>
                    {formatCurrency(calculateAnnualAmount(income.amount, income.frequency))}
                  </Text>
                  <Text style={styles.tableCell}>{income.income_type || '-'}</Text>
                  <Text style={[styles.tableCell, styles.tableCellLast]}>{income.member_name || 'Household'}</Text>
                </View>
              ))}
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Annual Income:</Text>
              <Text style={[styles.summaryValue, styles.positiveValue]}>{formatCurrency(totalAnnualIncome)}</Text>
            </View>
          </View>
        )}

        {/* Expenses Section */}
        {showExpenses && expenses.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Expenses</Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.tableHeader]}>
                <Text style={styles.tableCell}>Name</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Amount</Text>
                <Text style={styles.tableCell}>Frequency</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Annual Amount</Text>
                <Text style={styles.tableCell}>Category</Text>
                <Text style={[styles.tableCell, styles.tableCellLast]}>Details</Text>
              </View>
              {expenses.map((expense, index) => (
                <View key={expense.id} style={[
                  styles.tableRow,
                  index % 2 === 1 ? styles.tableRowEven : {}
                ]}>
                  <Text style={styles.tableCell}>{expense.name}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>{formatCurrency(expense.amount)}</Text>
                  <Text style={styles.tableCell}>{expense.frequency}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>
                    {formatCurrency(calculateAnnualAmount(expense.amount, expense.frequency))}
                  </Text>
                  <Text style={styles.tableCell}>{expense.category || '-'}</Text>
                  <Text style={[styles.tableCell, styles.tableCellLast]}>{expense.details || '-'}</Text>
                </View>
              ))}
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Annual Expenses:</Text>
              <Text style={[styles.summaryValue, styles.negativeValue]}>{formatCurrency(totalAnnualExpenses)}</Text>
            </View>
          </View>
        )}

        {/* Insurance Section */}
        {showInsurances && insurances.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Insurance</Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.tableHeader]}>
                <Text style={styles.tableCell}>Type</Text>
                <Text style={styles.tableCell}>Provider</Text>
                <Text style={styles.tableCell}>Policy Number</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount]}>Premium</Text>
                <Text style={styles.tableCell}>Frequency</Text>
                <Text style={[styles.tableCell, styles.tableCellAmount, styles.tableCellLast]}>Coverage</Text>
              </View>
              {insurances.map((insurance, index) => (
                <View key={insurance.id} style={[
                  styles.tableRow,
                  index % 2 === 1 ? styles.tableRowEven : {}
                ]}>
                  <Text style={styles.tableCell}>{insurance.type}</Text>
                  <Text style={styles.tableCell}>{insurance.provider}</Text>
                  <Text style={styles.tableCell}>{insurance.policy_number || '-'}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount]}>
                    {formatCurrency(insurance.premium)}
                  </Text>
                  <Text style={styles.tableCell}>{insurance.frequency}</Text>
                  <Text style={[styles.tableCell, styles.tableCellAmount, styles.tableCellLast]}>
                    {formatCurrency(insurance.coverage_amount || 0)}
                  </Text>
                </View>
              ))}
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Annual Premiums:</Text>
              <Text style={[styles.summaryValue, styles.negativeValue]}>{formatCurrency(totalAnnualInsurancePremiums)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Total Coverage:</Text>
              <Text style={[styles.summaryValue, styles.positiveValue]}>{formatCurrency(totalInsuranceCoverage)}</Text>
            </View>
          </View>
        )}

        {/* Goals Section */}
        {showGoals && goals.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Goals</Text>

            <View style={styles.twoColumnGrid}>
              {goals.map((goal, index) => (
                <View key={goal.id} style={[styles.card, styles.columnCard]}>
                  <Text style={styles.cardTitle}>{goal.title}</Text>
                  <View style={styles.cardRow}>
                    <Text style={styles.cardLabel}>Type:</Text>
                    <Text style={styles.cardValue}>{goal.type}</Text>
                  </View>
                  <View style={styles.cardRow}>
                    <Text style={styles.cardLabel}>Status:</Text>
                    <View style={[styles.badge, getStatusColor(goal.status)]}>
                      <Text>{goal.status}</Text>
                    </View>
                  </View>
                  {goal.priority && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Priority:</Text>
                      <View style={[styles.badge, getPriorityColor(goal.priority)]}>
                        <Text>{goal.priority}</Text>
                      </View>
                    </View>
                  )}
                  {goal.member && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>For:</Text>
                      <Text style={styles.cardValue}>{goal.member}</Text>
                    </View>
                  )}
                  {goal.target_amount && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Target Amount:</Text>
                      <Text style={[styles.cardValue, styles.positiveValue]}>
                        {formatCurrency(goal.target_amount)}
                      </Text>
                    </View>
                  )}
                  {goal.start_date && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Start Date:</Text>
                      <Text style={styles.cardValue}>
                        {format(new Date(goal.start_date), 'dd MMM yyyy')}
                      </Text>
                    </View>
                  )}
                  {goal.achieved_date && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Achieved Date:</Text>
                      <Text style={styles.cardValue}>
                        {format(new Date(goal.achieved_date), 'dd MMM yyyy')}
                      </Text>
                    </View>
                  )}
                  {goal.details && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Details:</Text>
                      <Text style={styles.cardValue}>{goal.details}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Recommendations Section */}
        {showRecommendations && recommendations.length > 0 && (
          <View style={styles.section} break>
            <Text style={styles.sectionTitle}>Recommendations</Text>

            <View style={styles.twoColumnGrid}>
              {recommendations.map((recommendation, index) => (
                <View key={recommendation.id} style={[styles.card, styles.columnCard]}>
                  <Text style={styles.cardTitle}>{recommendation.title}</Text>
                  <View style={styles.cardRow}>
                    <Text style={styles.cardLabel}>Type:</Text>
                    <Text style={styles.cardValue}>{recommendation.type || '-'}</Text>
                  </View>
                  <View style={styles.cardRow}>
                    <Text style={styles.cardLabel}>Status:</Text>
                    <View style={[styles.badge, getStatusColor(recommendation.status)]}>
                      <Text>{recommendation.status}</Text>
                    </View>
                  </View>
                  {recommendation.priority && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Priority:</Text>
                      <View style={[styles.badge, getPriorityColor(recommendation.priority)]}>
                        <Text>{recommendation.priority}</Text>
                      </View>
                    </View>
                  )}
                  {recommendation.member && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>For:</Text>
                      <Text style={styles.cardValue}>{recommendation.member}</Text>
                    </View>
                  )}
                  {recommendation.financial_impact !== undefined && recommendation.financial_impact !== null && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Financial Impact:</Text>
                      <Text style={[styles.cardValue, recommendation.financial_impact >= 0 ? styles.positiveValue : styles.negativeValue]}>
                        {formatCurrency(recommendation.financial_impact)}
                      </Text>
                    </View>
                  )}
                  {recommendation.implementation_date && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Implementation Date:</Text>
                      <Text style={styles.cardValue}>
                        {format(new Date(recommendation.implementation_date), 'dd MMM yyyy')}
                      </Text>
                    </View>
                  )}
                  {recommendation.details && (
                    <View style={styles.cardRow}>
                      <Text style={styles.cardLabel}>Details:</Text>
                      <Text style={styles.cardValue}>{recommendation.details}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}

        <Text style={styles.footer}>
          This document was generated by Wealthie. The information contained in this document is confidential and intended solely for the addressee.
          © {new Date().getFullYear()} Wealthie. All rights reserved.
        </Text>

        <Text
          style={styles.pageNumber}
          render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
          fixed
        />
      </Page>
    </Document>
  );
}


export default FinancialSummaryPDF;
