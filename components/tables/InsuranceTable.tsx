'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { formatCurrency } from '@/lib/utils';

interface Insurance {
  id: string;
  type: string;
  provider: string;
  policy_number: string;
  premium: number;
  frequency: string;
  coverage_amount: number;
  renewal_date: string;
  details?: string;
}

interface InsuranceTableProps {
  householdId: string;
}

export default function InsuranceTable({ householdId }: InsuranceTableProps) {
  const [insurances, setInsurances] = useState<Insurance[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const supabase = createClient();

      const { data: insuranceData, error: insuranceError } = await supabase
        .from('insurances')
        .select('*')
        .eq('household_id', householdId)
        .order('created_at', { ascending: false });

      if (insuranceError) {
        console.error('Error fetching insurances:', insuranceError);
      } else {
        setInsurances(insuranceData);
      }

      setLoading(false);
    };

    fetchData();
  }, [householdId]);

  const calculateAnnualPremium = (premium: number, frequency: string): number => {
    switch (frequency) {
      case 'weekly':
        return premium * 52;
      case 'fortnightly':
        return premium * 26;
      case 'monthly':
        return premium * 12;
      case 'quarterly':
        return premium * 4;
      case 'annually':
        return premium;
      default:
        return premium;
    }
  };

  const totalAnnualPremiums = insurances.reduce((total, insurance) => 
    total + calculateAnnualPremium(insurance.premium, insurance.frequency), 0);

  const totalCoverage = insurances.reduce((total, insurance) => 
    total + insurance.coverage_amount, 0);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Total Annual Premiums</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {formatCurrency(totalAnnualPremiums)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Total Coverage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(totalCoverage)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Insurance Policies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {insurances.map((insurance) => (
              <div key={insurance.id} className="flex justify-between items-start p-4 border rounded-lg">
                <div>
                  <div className="font-medium">{insurance.type}</div>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div>Provider: {insurance.provider}</div>
                    <div>Policy Number: {insurance.policy_number}</div>
                    <div>Premium: {formatCurrency(insurance.premium)} {insurance.frequency}</div>
                    <div>Coverage: {formatCurrency(insurance.coverage_amount)}</div>
                    <div>Renewal Date: {new Date(insurance.renewal_date).toLocaleDateString()}</div>
                    {insurance.details && <div>Details: {insurance.details}</div>}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">Annual Premium</div>
                  <div className="text-destructive">
                    {formatCurrency(calculateAnnualPremium(insurance.premium, insurance.frequency))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
