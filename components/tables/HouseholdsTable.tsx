'use client';

import React, { useState, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  PaginationState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import DeleteHouseholdModal from '../modals/DeleteHouseholdModal';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface Household {
  id: number;
  householdName: string;
  members: string;
}

interface HouseholdsTableProps {
  data: Household[];
  onDataChange: () => Promise<void>;
  onCreateHousehold: () => void;
  onEditClick: (householdId: number) => void;
  viewMode: 'user' | 'organization';
  onViewModeChange: (value: 'user' | 'organization') => void;
}

export function HouseholdsTable({ data, onDataChange, onCreateHousehold, onEditClick, viewMode, onViewModeChange }: HouseholdsTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedHousehold, setSelectedHousehold] = useState<Household | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [householdToDelete, setHouseholdToDelete] = useState<Household | null>(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const router = useRouter();

  // Check if sidebar is collapsed on component mount and when localStorage changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedCollapsed = localStorage.getItem('sidebarCollapsed');
      setSidebarCollapsed(savedCollapsed ? JSON.parse(savedCollapsed) : false);
    };

    // Initial check
    checkSidebarState();

    // Listen for storage events (when localStorage changes)
    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);

    // Create a custom event listener to detect sidebar changes
    const handleSidebarChange = () => {
      checkSidebarState();
    };

    window.addEventListener('sidebarChange', handleSidebarChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarChange', handleSidebarChange);
    };
  }, []);

  const handleEdit = (household: Household) => {
    setSelectedHousehold(household);
    setIsEditModalOpen(true);
  };

  const handleDelete = (household: Household) => {
    setHouseholdToDelete(household);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (householdToDelete) {
      const supabase = createClient();
      const { error } = await supabase
        .from('households')
        .delete()
        .eq('id', householdToDelete.id);

      if (error) {
        console.error('Error deleting household:', error);
      } else {
        onDataChange(); // Refresh the table
      }
      setIsDeleteModalOpen(false);
      setHouseholdToDelete(null);
    }
  };

  const columns: ColumnDef<Household>[] = [
    {
      accessorKey: "householdName",
      header: "Household Name",
      size: 250,
      cell: ({ row }) => {
        const household = row.original;
        return (
          <button
            onClick={() => router.push(`/protected/households/household/${household.id}/household_overview`)}
            className="text-blue-600 hover:text-blue-800 hover:underline text-left"
          >
            {household.householdName}
          </button>
        );
      },
    },
    {
      accessorKey: "members",
      header: "Members",
      size: 250,
      cell: ({ row }) => {
        const members = row.original.members;
        let memberNames = '';
        let hasMembers = false;

        if (typeof members === 'string') {
          try {
            const parsedMembers = JSON.parse(members);
            memberNames = `${parsedMembers.name1 || ''} ${parsedMembers.name2 ? '& ' + parsedMembers.name2 : ''}`.trim();
            hasMembers = !!(parsedMembers.name1 || parsedMembers.name2);
          } catch (e) {
            console.error('Error parsing members:', e);
            memberNames = '';
          }
        } else if (typeof members === 'object' && members !== null) {
          const membersObj = members as { name1?: string; name2?: string };
          memberNames = `${membersObj.name1 || ''} ${membersObj.name2 ? '& ' + membersObj.name2 : ''}`.trim();
          hasMembers = !!(membersObj.name1 || membersObj.name2);
        }

        return hasMembers ? memberNames : "No Members yet";
      },
    },
    {
      id: "actions",
      size: 80,
      cell: ({ row }) => {
        const household = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleDelete(household)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
  });

  const handleEditSubmit = () => {
    // This will refresh the table data
  };

  return (
    <div
      className="flex flex-col h-full"
      style={{
        maxWidth: `calc(100vw - ${sidebarCollapsed ? '64px' : '220px'})`,
        marginLeft: 'auto',
        marginRight: 'auto',
        transition: 'max-width 0.3s ease-in-out'
      }}
    >
      {/* Fixed header with filters and create button */}
      <div className="flex items-center justify-between py-4 sticky top-0 bg-white z-20">
        <div className="flex items-center space-x-4">
          <Input
            placeholder="Filter households..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="max-w-sm"
          />
          <div className="flex items-center space-x-2 min-w-[200px]">
            <Label htmlFor="view-mode-toggle">
              {viewMode === 'user' ? 'My Households' : 'Organization View'}
            </Label>
            <Switch
              id="view-mode-toggle"
              checked={viewMode === 'organization'}
              onCheckedChange={(checked) =>
                onViewModeChange(checked ? 'organization' : 'user')
              }
            />
          </div>
        </div>
        <Button onClick={onCreateHousehold}>Create Household</Button>
      </div>

      {/* Scrollable table area with fixed header */}
      <div className="flex-1 min-h-0 overflow-hidden border rounded-md">
        <div className="relative">
          {/* Fixed header */}
          <div className="sticky top-0 bg-white z-10 w-full">
            <Table className="w-full table-fixed">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        style={{ width: `${header.getSize()}px` }}
                        className={header.id === "actions" ? "text-right" : ""}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
            </Table>
          </div>

          {/* Scrollable body */}
          <div className="overflow-auto max-h-[calc(100vh-300px)]">
            <Table className="w-full table-fixed">
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => {
                    let hasMembers = false;
                    const members = row.original.members;

                    if (typeof members === 'string') {
                      try {
                        const parsedMembers = JSON.parse(members);
                        hasMembers = !!(parsedMembers.name1 || parsedMembers.name2);
                      } catch (e) {
                        console.error('Error parsing members:', e);
                      }
                    } else if (typeof members === 'object' && members !== null) {
                      const membersObj = members as { name1?: string; name2?: string };
                      hasMembers = !!(membersObj.name1 || membersObj.name2);
                    }

                    return (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        className={!hasMembers ? "bg-red-50" : ""}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            key={cell.id}
                            style={{ width: `${cell.column.getSize()}px` }}
                            className={cell.column.id === "actions" ? "text-right" : ""}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Fixed footer with pagination controls */}
      <div className="flex items-center justify-between space-x-2 py-4 sticky bottom-0 bg-white z-20">
        <div className="flex items-center space-x-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredRowModel().rows.length} total household{table.getFilteredRowModel().rows.length === 1 ? '' : 's'}
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm">Show</span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pagination.pageSize.toString()} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm">households</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <div className="flex items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount()}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>

      {householdToDelete && (
        <DeleteHouseholdModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setHouseholdToDelete(null);
          }}
          onConfirm={confirmDelete}
          householdName={householdToDelete.householdName}
        />
      )}
    </div>
  );
}
