import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from "@/components/ui/table"
  
  const formatValue = (key: string, value: any): string => {
    if (value === null || value === undefined) return '-';
    
    // Handle arrays (like periods)
    if (Array.isArray(value)) {
      return value.join(' to ');
    }

    // Handle numbers with currency
    if (
      [
        'annual_investment_contribution',
        'initial_investment',
        'savings_amount',
        'annual_expenses1',
        'annual_expenses2',
        'annual_income',
        'partner_annual_income'
      ].includes(key)
    ) {
      return value ? `$${value.toLocaleString()}` : '-';
    }

    // Handle percentages
    if (
      [
        'annual_investment_return',
        'inv_std_dev',
        'saving_percentage',
        'inflation_rate',
        'property_growth',
        'debt_ir'
      ].includes(key)
    ) {
      return value ? `${value}%` : '-';
    }

    // Handle boolean values
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }

    // Handle objects
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }

    return value.toString();
  }

  const getDisplayName = (key: string): string => {
    const nameMap: { [key: string]: string } = {
      annual_investment_contribution: 'Annual Investment Contribution',
      initial_investment: 'Initial Investment',
      annual_investment_return: 'Annual Investment Return',
      inv_std_dev: 'Investment Standard Deviation',
      inv_tax: 'Investment Tax Type',
      investment_return_period: 'Investment Return Period',
      contribution_period: 'Contribution Period',
      fund_periods: 'Fund Periods',
      annual_income: 'Annual Income',
      partner_annual_income: 'Partner Annual Income',
      starting_age: 'Starting Age',
      ending_age: 'Ending Age',
      savings_amount: 'Savings Amount',
      annual_expenses1: 'Annual Expenses (1)',
      annual_expenses2: 'Annual Expenses (2)',
      saving_percentage: 'Saving Percentage',
      inflation_rate: 'Inflation Rate',
      property_growth: 'Property Growth Rate',
      debt_ir: 'Debt Interest Rate'
    };
    return nameMap[key] || key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }
  
  export default function ScenarioDataTable({ data }: { data: any }) {
    const priorityFields = [
      'annual_investment_contribution',
      'contribution_period',
      'initial_investment',
      'annual_investment_return',
      'inv_std_dev',
      'inv_tax',
      'investment_return_period',
      'fund_periods'
    ];

    const entries = Object.entries(data)
      .filter(([key]) => key !== 'id' && key !== 'scenario_name')
      .sort(([keyA], [keyB]) => {
        const isPriorityA = priorityFields.includes(keyA);
        const isPriorityB = priorityFields.includes(keyB);
        if (isPriorityA && !isPriorityB) return -1;
        if (!isPriorityA && isPriorityB) return 1;
        return keyA.localeCompare(keyB);
      });
  
    return (
      <Table className="mt-4">
        <TableHeader>
          <TableRow>
            <TableHead>Field</TableHead>
            <TableHead>Value</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {entries.map(([key, value]) => (
            <TableRow key={key}>
              <TableCell>{getDisplayName(key)}</TableCell>
              <TableCell>{formatValue(key, value)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    )
  }