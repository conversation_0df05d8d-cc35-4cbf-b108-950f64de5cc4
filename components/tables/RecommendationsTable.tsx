'use client';

import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { formatCurrency } from '@/lib/utils';
import { Plus, Pencil, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from "@/components/ui/badge";
import { RecommendationsModal } from '@/components/modals/RecommendationsModal';

interface Recommendation {
  id: string;
  title: string;
  type: string;
  details?: string;
  created_date?: string;
  implementation_date?: string;
  status: string;
  member?: string;
  priority?: string;
  financial_impact?: number;
  adviser_notes?: string;
  household_id: string;
  user_id: string;
  org_id: string;
}

interface RecommendationsTableProps {
  householdId: string;
  householdMembers?: { name1: string; name2?: string };
}

export default function RecommendationsTable({ 
  householdId, 
  householdMembers
}: RecommendationsTableProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState<Recommendation | undefined>();

  useEffect(() => {
    fetchRecommendations();
  }, [householdId]);

  const fetchRecommendations = async () => {
    const supabase = createClient();

    const { data: recommendationsData, error: recommendationsError } = await supabase
      .from('recommendations')
      .select('*')
      .eq('household_id', householdId)
      .order('created_date', { ascending: false });

    if (recommendationsError) {
      console.error('Error fetching recommendations:', recommendationsError);
    } else {
      setRecommendations(recommendationsData || []);
    }

    setLoading(false);
  };

  const handleAddRecommendation = () => {
    setSelectedRecommendation(undefined);
    setIsModalOpen(true);
  };

  const handleEditRecommendation = (recommendation: Recommendation) => {
    setSelectedRecommendation(recommendation);
    setIsModalOpen(true);
  };

  const handleModalSuccess = () => {
    fetchRecommendations();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'implemented':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'not started':
        return 'bg-gray-100 text-gray-800';
      case 'pending client approval':
        return 'bg-yellow-100 text-yellow-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'investment':
        return '📈';
      case 'insurance':
        return '🛡️';
      case 'retirement':
        return '🏡';
      case 'estate planning':
        return '📝';
      case 'tax strategy':
        return '💰';
      case 'debt management':
        return '💳';
      case 'cash flow':
        return '💸';
      case 'portfolio adjustment':
        return '📊';
      case 'risk profile':
        return '⚠️';
      default:
        return '📋';
    }
  };

  if (loading) {
    return <div>Loading recommendations...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Adviser Recommendations</h3>
        <Button onClick={handleAddRecommendation} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Recommendation
        </Button>
      </div>

      {recommendations.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            No recommendations have been added yet. Click "Add Recommendation" to create your first recommendation.
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {recommendations.map((recommendation) => (
            <Card key={recommendation.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-4 border-b">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-2">
                      <span className="text-xl">{getTypeIcon(recommendation.type)}</span>
                      <h4 className="font-medium">{recommendation.title}</h4>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => handleEditRecommendation(recommendation)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex justify-between">
                    <Badge className={getStatusColor(recommendation.status)}>{recommendation.status}</Badge>
                    {recommendation.priority && (
                      <Badge className={getPriorityColor(recommendation.priority)}>Priority: {recommendation.priority}</Badge>
                    )}
                  </div>
                  
                  {recommendation.details && (
                    <p className="text-sm text-muted-foreground">{recommendation.details}</p>
                  )}
                  
                  <div className="text-sm space-y-1">
                    {recommendation.member && (
                      <div className="flex items-center">
                        <span className="font-medium mr-2">For:</span> {recommendation.member}
                      </div>
                    )}
                    
                    {recommendation.financial_impact !== undefined && (
                      <div className="flex items-center">
                        <span className="font-medium mr-2">Financial Impact:</span> {formatCurrency(recommendation.financial_impact)}
                      </div>
                    )}
                    
                    {recommendation.implementation_date && (
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1" />
                        <span className="font-medium mr-2">Implementation Date:</span> 
                        {format(new Date(recommendation.implementation_date), 'dd MMM yyyy')}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <RecommendationsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        householdId={householdId}
        recommendation={selectedRecommendation}
        householdMembers={householdMembers}
      />
    </div>
  );
}