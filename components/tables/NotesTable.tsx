
"use client";

import { useState } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  PaginationState,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { createClient } from "@/utils/supabase/client";
import { format } from "date-fns";
import { Pencil, Trash2, ChevronDown, ChevronUp } from "lucide-react";

interface Note {
  id: string;
  title: string;
  household_id: string;
  household_name: string;
  created_at: string;
  last_edited_at: string;
  user_id: string;
  created_by: string;
  note_type: string;
  summary: string | null;
  metadata?: {
    media_file_path?: string;
    media_file_type?: string;
    transcription_file_path?: string;
    transcription_status?: string;
    original_filename?: string;
  };
}

interface NotesTableProps {
  data: Note[];
  onDataChange: () => Promise<void>;
  onCreateNote: () => void;
  onCreateTranscription?: () => void;
}

export function NotesTable({ data, onDataChange, onCreateNote, onCreateTranscription }: NotesTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const router = useRouter();

  // Toggle row expansion
  const toggleRowExpansion = (rowId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [rowId]: !prev[rowId]
    }));
  };

  const handleDeleteNote = async () => {
    if (!noteToDelete) return;

    const supabase = createClient();

    // First, get the note to check if it's a transcription with media files
    const { data: noteData, error: fetchError } = await supabase
      .from("notes")
      .select("*")
      .eq("id", noteToDelete)
      .single();

    if (fetchError) {
      console.error("Error fetching note for deletion:", fetchError);
      return;
    }

    // Check if this is a transcription note with media files to delete
    const isTranscription = noteData?.note_type === 'transcription';
    const mediaFilePath = noteData?.metadata?.media_file_path;
    const transcriptionFilePath = noteData?.metadata?.transcription_file_path;

    // Delete the note from the database
    const { error } = await supabase
      .from("notes")
      .delete()
      .eq("id", noteToDelete);

    if (error) {
      console.error("Error deleting note:", error);
    } else {
      // If this was a transcription note, delete the associated media files
      if (isTranscription) {
        // Delete the media file (video or audio)
        if (mediaFilePath) {
          const { error: mediaDeleteError } = await supabase.storage
            .from('media')
            .remove([mediaFilePath]);

          if (mediaDeleteError) {
            console.error('Error deleting media file:', mediaDeleteError);
          }
        }

        // If there's a separate transcription file (extracted audio), delete that too
        if (transcriptionFilePath && transcriptionFilePath !== mediaFilePath) {
          const { error: transcriptionDeleteError } = await supabase.storage
            .from('media')
            .remove([transcriptionFilePath]);

          if (transcriptionDeleteError) {
            console.error('Error deleting transcription file:', transcriptionDeleteError);
          }
        }
      }

      onDataChange();
    }

    setNoteToDelete(null);
  };

  const columns: ColumnDef<Note>[] = [
    {
      id: "expander",
      header: "",
      size: 40,
      cell: ({ row }) => {
        const note = row.original;
        const isExpanded = expandedRows[row.id] || false;

        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              toggleRowExpansion(row.id);
            }}
            className="h-8 w-8"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        );
      },
    },
    {
      accessorKey: "title",
      header: "Title",
      size: 250,
      cell: ({ row }) => {
        const note = row.original;
        const notePath = note.note_type === 'transcription'
          ? `/protected/notes/transcription/${note.id}`
          : `/protected/notes/note/${note.id}`;

        return (
          <button
            onClick={() => router.push(notePath)}
            className="text-blue-600 hover:text-blue-800 hover:underline text-left"
          >
            {note.title}
          </button>
        );
      },
    },
    {
      accessorKey: "note_type",
      header: "Type",
      size: 100,
      cell: ({ row }) => {
        const type = row.original.note_type || "general";
        const colorMap: Record<string, string> = {
          general: "bg-gray-100 text-gray-800",
          important: "bg-red-100 text-red-800",
          info: "bg-blue-100 text-blue-800",
          task: "bg-green-100 text-green-800",
          meeting: "bg-purple-100 text-purple-800"
        };

        const bgColor = colorMap[type] || colorMap.general;

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${bgColor}`}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </span>
        );
      },
    },
    {
      accessorKey: "household_name",
      header: "Household",
      size: 180,
    },
    {
      accessorKey: "created_by",
      header: "Created by",
      size: 150,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.original.created_at), "MMM d, yyyy");
      },
    },
    {
      accessorKey: "last_edited_at",
      header: "Last Edited",
      size: 120,
      cell: ({ row }) => {
        return format(new Date(row.original.last_edited_at), "MMM d, yyyy");
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: ({ row }) => {
        const note = row.original;
        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                const notePath = note.note_type === 'transcription'
                  ? `/protected/notes/transcription/${note.id}`
                  : `/protected/notes/note/${note.id}`;
                router.push(notePath);
              }}
              className="h-8 w-8"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                setNoteToDelete(note.id);
                setDeleteDialogOpen(true);
              }}
              className="h-8 w-8 text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: setPagination,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      pagination,
      globalFilter,
    },
  });

  return (
    <div className="flex flex-col h-full">
      {/* Fixed header with filters and create button */}
      <div className="flex items-center justify-between py-4 sticky top-0 bg-white z-20">
        <Input
          placeholder="Filter notes..."
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex gap-2">
          {onCreateTranscription && (
            <Button onClick={onCreateTranscription} variant="outline">
              Create Transcription
            </Button>
          )}
          <Button onClick={onCreateNote}>Create Note</Button>
        </div>
      </div>

      <div className="rounded-md border flex-1 overflow-auto">
        <Table className="min-w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      style={{ width: header.getSize() }}
                      className="px-4 py-3"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <>
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className="h-10" // Add this to make rows thinner
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        style={{ width: cell.column.getSize() }}
                        className="px-4 py-2" // Reduce padding from py-3 to py-2
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  {expandedRows[row.id] && (
                    <TableRow key={`${row.id}-expanded`} className="bg-muted/50">
                      <TableCell colSpan={columns.length} className="px-6 py-4">
                        <div className="space-y-3">
                          {row.original.summary && (
                            <div>
                              <h3 className="text-sm font-medium">Summary</h3>
                              <div className="mt-2 text-sm text-gray-700">
                                {row.original.summary}
                              </div>
                            </div>
                          )}
                          {!row.original.summary && (
                            <div className="text-sm text-gray-500 italic">
                              No summary available for this note.
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No notes found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <div className="flex items-center justify-center text-sm font-medium">
          Page {table.getState().pagination.pageIndex + 1} of{' '}
          {table.getPageCount()}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the note.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteNote} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

