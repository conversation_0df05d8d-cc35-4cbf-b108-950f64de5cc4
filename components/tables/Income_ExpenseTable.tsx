'use client';

import { useEffect, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableFooter, // Import TableFooter
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Plus } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { formatCurrency } from '@/lib/utils';
import { Asset, Liability } from './Assets_LiabilitiesTable';

export interface Income {
  id: number;
  source: string;
  amount: number;
  frequency: string;
  income_type?: string;
  details?: string;
  household_id?: number;
  linked_asset_id?: string;
  member_id?: number;
  member_name?: string; // For display purposes only
}

export interface Expense {
  id: number;
  name: string;
  category?: string;
  amount: number;
  frequency: string;
  details?: string;
  household_id?: number;
  linked_liability_id?: string;
}

interface Income_ExpenseTableProps {
  householdId: string;
  onAddIncome: () => void;
  onAddExpense: () => void;
  onDataChange: () => Promise<void>;
  incomes: Income[];
  expenses: Expense[];
  onEditIncome?: (income: Income) => void;
  onEditExpense?: (expense: Expense) => void;
  assets?: Asset[];
  liabilities?: Liability[];
}

export default function Income_ExpenseTable({
  householdId,
  onAddIncome,
  onAddExpense,
  onDataChange,
  incomes,
  expenses,
  onEditIncome,
  onEditExpense,
  assets,
  liabilities
}: Income_ExpenseTableProps) {
  const [loading, setLoading] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [activeTab, setActiveTab] = useState('income');
  const [householdMembers, setHouseholdMembers] = useState<{[key: string]: string}>({});
  const [processedIncomes, setProcessedIncomes] = useState<Income[]>([]);

  // Fetch household member data
  useEffect(() => {
    const fetchHouseholdMembers = async () => {
      if (!householdId) return;

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('members')
        .eq('id', householdId)
        .single();

      if (error || !data?.members) {
        console.error('Error fetching household members:', error);
        return;
      }

      const members = data.members;
      const memberMap: {[key: string]: string} = {};

      // Map member IDs to names
      // 0 = Household
      memberMap['0'] = 'Household';

      // 1 = Main member (name1)
      if (members.name1) {
        memberMap['1'] = members.name1;
      }

      // 2 = Partner (name2)
      if (members.name2) {
        memberMap['2'] = members.name2;
      }

      setHouseholdMembers(memberMap);
    };

    fetchHouseholdMembers();
  }, [householdId]);

  // Process incomes to add member names
  useEffect(() => {
    if (incomes.length > 0 && Object.keys(householdMembers).length > 0) {
      const processed = incomes.map(income => {
        let memberId = '0'; // Default to household

        if (income.member_id !== undefined && income.member_id !== null) {
          memberId = income.member_id.toString();
        }

        return {
          ...income,
          member_name: householdMembers[memberId] || `Member ${memberId}`
        };
      });
      setProcessedIncomes(processed);
    } else {
      setProcessedIncomes(incomes);
    }
  }, [incomes, householdMembers]);

  const handleDelete = async (id: number, type: 'income' | 'expenses') => {
    setLoading(true);
    const supabase = createClient();

    // Check if this income/expense is linked to an asset/liability
    if (type === 'income') {
      const income = incomes.find(inc => inc.id === id);
      if (income?.linked_asset_id) {
        // Update the asset to remove the link
        await supabase
          .from('assets')
          .update({ linked_income_id: null })
          .eq('id', income.linked_asset_id);
      }
    } else if (type === 'expenses') {
      const expense = expenses.find(exp => exp.id === id);
      if (expense?.linked_liability_id) {
        // Update the liability to remove the link
        await supabase
          .from('liabilities')
          .update({ linked_expense_id: null })
          .eq('id', expense.linked_liability_id);
      }
    }

    // Now delete the income/expense
    const { error } = await supabase
      .from(type)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting ${type}:`, error);
    } else {
      await onDataChange();
    }
    setLoading(false);
  };

  const incomeColumns: ColumnDef<Income>[] = [
    {
      accessorKey: "source",
      header: "Source",
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => formatCurrency(row.original.amount),
    },
    {
      accessorKey: "frequency",
      header: "Frequency",
    },
    {
      accessorKey: "income_type",
      header: "Income Type",
      cell: ({ row }) => {
        const type = row.original.income_type || "";
        return type.charAt(0).toUpperCase() + type.slice(1); // Capitalize first letter
      },
    },
    {
      accessorKey: "member_name",
      header: "Assigned To",
      cell: ({ row }) => {
        // If we have a member_name, use it directly
        if (row.original.member_name) {
          return row.original.member_name;
        }

        // Otherwise, show based on member_id
        const memberId = row.original.member_id;
        if (memberId === 0) return 'Household';
        if (memberId === undefined || memberId === null) return 'Household';
        return `Member ${memberId}`; // Fallback if name not available
      },
    },
    {
      accessorKey: "details",
      header: "Details",
    },
    {
      accessorKey: "linked_asset_id",
      header: "Linked Asset",
      cell: ({ row }) => {
        const assetId = row.original.linked_asset_id;
        if (!assetId) return 'None';

        if (!assets || assets.length === 0) {
          return `Asset not loaded (ID: ${assetId})`;
        }

        const linkedAsset = assets.find(asset => asset.id === assetId);
        return linkedAsset ? `${linkedAsset.name} (${linkedAsset.type})` : `Unknown Asset (ID: ${assetId})`;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEditIncome && (
                <DropdownMenuItem onClick={() => onEditIncome(row.original)}>
                  Edit
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => handleDelete(row.original.id, 'income')}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const expenseColumns: ColumnDef<Expense>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => formatCurrency(row.original.amount),
    },
    {
      accessorKey: "frequency",
      header: "Frequency",
    },
    {
      accessorKey: "category",
      header: "Category",
    },
    {
      accessorKey: "details",
      header: "Details",
    },
    {
      accessorKey: "linked_liability_id",
      header: "Linked Liability",
      cell: ({ row }) => {
        const liabilityId = row.original.linked_liability_id;
        if (!liabilityId) return 'None';

        if (!liabilities || liabilities.length === 0) {
          return `Liability not loaded (ID: ${liabilityId})`;
        }

        const linkedLiability = liabilities.find(liability => liability.id === liabilityId);
        return linkedLiability ? `${linkedLiability.name} (${linkedLiability.type})` : `Unknown Liability (ID: ${liabilityId})`;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEditExpense && (
                <DropdownMenuItem onClick={() => onEditExpense(row.original)}>
                  Edit
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => handleDelete(row.original.id, 'expenses')}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const incomeTable = useReactTable({
    data: processedIncomes,
    columns: incomeColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      sorting: [
        {
          id: 'source',
          desc: false,
        },
      ],
    },
  });

  const expenseTable = useReactTable({
    data: expenses,
    columns: expenseColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  const getAnnualizedAmount = (amount: number, frequency: string): number => {
    const freqLower = frequency?.toLowerCase();
    switch (freqLower) {
      case 'weekly':
        return amount * 52;
      case 'fortnightly':
        return amount * 26;
      case 'monthly':
        return amount * 12;
      case 'quarterly':
        return amount * 4;
      case 'annually':
      default: // Treat unrecognized frequencies as annual
        return amount;
    }
  };

  const totalIncome = processedIncomes.reduce((sum, income) => sum + getAnnualizedAmount(income.amount, income.frequency), 0);
  const totalExpense = expenses.reduce((sum, expense) => sum + getAnnualizedAmount(expense.amount, expense.frequency), 0);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="income">Income</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
          </TabsList>
        </Tabs>
        <Button
          className="flex items-center gap-2"
          onClick={activeTab === 'income' ? onAddIncome : onAddExpense}
        >
          <Plus className="h-4 w-4" />
          Add {activeTab === 'income' ? 'Income' : 'Expense'}
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {activeTab === 'income' ? (
              <TableRow>
                {incomeTable.getHeaderGroups().map((headerGroup) => (
                  headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))
                ))}
              </TableRow>
            ) : (
              <TableRow>
                {expenseTable.getHeaderGroups().map((headerGroup) => (
                  headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))
                ))}
              </TableRow>
            )}
          </TableHeader>
          <TableBody>
            {activeTab === 'income' ? (
              incomeTable.getRowModel().rows?.length ? (
                incomeTable.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={incomeColumns.length} className="h-24 text-center">
                    No income sources found.
                  </TableCell>
                </TableRow>
              )
            ) : (
              expenseTable.getRowModel().rows?.length ? (
                expenseTable.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={expenseColumns.length} className="h-24 text-center">
                    No expenses found.
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              {activeTab === 'income' ? (
                <>
                  <TableCell colSpan={incomeColumns.length - 1} className="text-right font-medium">Total Income:</TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(totalIncome)}</TableCell>
                </>
              ) : (
                <>
                  <TableCell colSpan={expenseColumns.length - 1} className="text-right font-medium">Total Expenses:</TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(totalExpense)}</TableCell>
                </>
              )}
            </TableRow>
          </TableFooter>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 pt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => activeTab === 'income' ? incomeTable.previousPage() : expenseTable.previousPage()}
          disabled={activeTab === 'income'
            ? !incomeTable.getCanPreviousPage()
            : !expenseTable.getCanPreviousPage()
          }
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => activeTab === 'income' ? incomeTable.nextPage() : expenseTable.nextPage()}
          disabled={activeTab === 'income'
            ? !incomeTable.getCanNextPage()
            : !expenseTable.getCanNextPage()
          }
        >
          Next
        </Button>
      </div>
      {/* Removed the old total section */}
    </div>
  );
}
