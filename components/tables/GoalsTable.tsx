'use client';

import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { formatCurrency } from '@/lib/utils';
import { Plus, Pencil, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from "@/components/ui/badge";
import { GoalsModal } from '@/components/modals/GoalsModal';

interface Goal {
  id: string;
  title: string;
  type: string;
  details?: string;
  start_date?: string;
  achieved_date?: string;
  status: string;
  member?: string;
  target_amount?: number;
  priority?: string;
}

interface GoalsTableProps {
  householdId: string;
  householdMembers?: { name1: string; name2?: string };
}

export default function GoalsTable({ 
  householdId, 
  householdMembers
}: GoalsTableProps) {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<Goal | undefined>();

  useEffect(() => {
    fetchGoals();
  }, [householdId]);

  const fetchGoals = async () => {
    const supabase = createClient();

    const { data: goalsData, error: goalsError } = await supabase
      .from('goals')
      .select('*')
      .eq('household_id', householdId)
      .order('created_at', { ascending: false });

    if (goalsError) {
      console.error('Error fetching goals:', goalsError);
    } else {
      setGoals(goalsData || []);
    }

    setLoading(false);
  };

  const handleAddGoal = () => {
    setSelectedGoal(undefined);
    setIsModalOpen(true);
  };

  const handleEditGoal = (goal: Goal) => {
    setSelectedGoal(goal);
    setIsModalOpen(true);
  };

  const handleModalSuccess = () => {
    fetchGoals();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'achieved':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'not started':
        return 'bg-gray-100 text-gray-800';
      case 'delayed':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'financial':
        return '💰';
      case 'lifestyle':
        return '🏖️';
      case 'retirement':
        return '🏡';
      case 'education':
        return '🎓';
      case 'health':
        return '🏥';
      case 'travel':
        return '✈️';
      case 'family':
        return '👪';
      case 'career':
        return '💼';
      case 'personal':
        return '🎯';
      default:
        return '🎯';
    }
  };

  if (loading) {
    return <div>Loading goals...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Household Goals</h3>
        <Button onClick={handleAddGoal} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Goal
        </Button>
      </div>

      {goals.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            No goals have been added yet. Click "Add Goal" to create your first goal.
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {goals.map((goal) => (
            <Card key={goal.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-4 border-b">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-2">
                      <span className="text-xl">{getTypeIcon(goal.type)}</span>
                      <h4 className="font-medium">{goal.title}</h4>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => handleEditGoal(goal)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex justify-between">
                    <Badge className={getStatusColor(goal.status)}>{goal.status}</Badge>
                    {goal.priority && (
                      <Badge className={getPriorityColor(goal.priority)}>Priority: {goal.priority}</Badge>
                    )}
                  </div>
                  
                  {goal.details && (
                    <p className="text-sm text-muted-foreground">{goal.details}</p>
                  )}
                  
                  <div className="text-sm space-y-1">
                    {goal.member && (
                      <div className="flex items-center">
                        <span className="font-medium mr-2">For:</span> {goal.member}
                      </div>
                    )}
                    
                    {goal.target_amount && (
                      <div className="flex items-center">
                        <span className="font-medium mr-2">Target:</span> {formatCurrency(goal.target_amount)}
                      </div>
                    )}
                    
                    {goal.start_date && (
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1" />
                        <span className="font-medium mr-2">Start:</span> 
                        {format(new Date(goal.start_date), 'dd MMM yyyy')}
                      </div>
                    )}
                    
                    {goal.achieved_date && (
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1" />
                        <span className="font-medium mr-2">Achieved:</span> 
                        {format(new Date(goal.achieved_date), 'dd MMM yyyy')}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <GoalsModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        householdId={householdId}
        goal={selectedGoal}
        householdMembers={householdMembers}
      />
    </div>
  );
}
