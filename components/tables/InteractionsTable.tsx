'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from "next/navigation";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createClient } from '@/utils/supabase/client';
import { format, addDays, isWithinInterval, parseISO } from 'date-fns';
import { useToast } from "@/hooks/use-toast";
import { MessageSquare, Paperclip, Plus, MoreHorizontal, Pencil, Trash2, Search, Link, ChevronDown, ChevronUp } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// Removed AlertDialog imports
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import InteractionsModal, { Interaction, InteractionModalMode } from '@/components/modals/InteractionsModal';
import { Badge } from "@/components/ui/badge";
import React from 'react';

// Removed useDropzone, useRef, useCallback

interface FilterState {
  type: string;
  dateRange: DateRange | undefined;
  search: string;
}

// Helper function to get badge variant based on interaction type
const getInteractionBadgeVariant = (type: string) => {
  switch (type) {
    case 'email':
      return 'blue';
    case 'phone':
      return 'green';
    case 'meeting':
      return 'purple';
    case 'note':
      return 'yellow';
    default:
      return 'outline';
  }
};

export default function InteractionsTable({ householdId }: { householdId: number }) {
  const [interactions, setInteractions] = useState<Interaction[]>([]);
  const [filters, setFilters] = useState<FilterState>({
    type: '',
    dateRange: undefined,
    search: ''
  });
  // State for the new modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedInteraction, setSelectedInteraction] = useState<Interaction | null>(null);
  const [modalMode, setModalMode] = useState<InteractionModalMode>('new');
  const [expandedRows, setExpandedRows] = useState<{ [key: number]: boolean }>({});
  const [fullInteractionData, setFullInteractionData] = useState<any>(null);
  const [userProfiles, setUserProfiles] = useState<{[key: string]: {name: string}}>({});

  const supabase = createClient();
  const { toast } = useToast();
  const searchParams = useSearchParams();

  useEffect(() => {
    fetchInteractions();
  }, [householdId]); // Add householdId as dependency

  const fetchInteractions = async () => {
    const { data, error } = await supabase
      .from('interactions')
      .select(`
        *,
        attachments (*),
        comments (*),
        interaction_task_links (
          id,
          task_id,
          tasks:task_id (id, title, status)
        )
      `)
      .eq('household_id', householdId)
      .order('date', { ascending: false });

    if (error) {
      console.error('Error fetching interactions:', error);
      return;
    }

    setInteractions(data || []);
  };

  useEffect(() => {
    const viewInteractionId = searchParams.get('view');
    
    if (viewInteractionId) {
      
      // Find the interaction in our loaded data
      const interactionToView = interactions.find(
        interaction => interaction.id.toString() === viewInteractionId
      );
      
      if (interactionToView) {
        // Open the modal with the interaction
        openModal('details', interactionToView);
      } else {
        // Fetch the specific interaction if not in our current data
        fetchSpecificInteraction(parseInt(viewInteractionId));
      }
    }
  }, [searchParams, interactions]); // Re-run when searchParams or interactions change

  const fetchSpecificInteraction = async (interactionId: number) => {
    
    const { data, error } = await supabase
      .from('interactions')
      .select(`
        *,
        attachments (*),
        comments (*),
        interaction_task_links (
          id,
          task_id,
          tasks:task_id (id, title, status)
        )
      `)
      .eq('id', interactionId)
      .single();
      
    if (error) {
      console.error('Error fetching specific interaction:', error);
      return;
    }
    
    if (data) {
      // Open the modal with the fetched interaction
      openModal('details', data);
    }
  };

  // --- Modal Handling ---
  const openModal = (mode: InteractionModalMode, interaction: Interaction | null = null) => {
    setModalMode(mode);
    setSelectedInteraction(interaction);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedInteraction(null); // Clear selection on close
  };

  const handleInteractionSaved = () => {
    fetchInteractions(); // Refresh data after save/update
  };

  const handleInteractionDeleted = () => {
    fetchInteractions(); // Refresh data after delete
  };
  // --- End Modal Handling ---

  // Removed old handlers: handleFileSelect, removeFile, sanitizeFileName, handleSubmitInteraction, handleAddComment, handleOpenDetails, handleEditInteraction, handleDeleteInteraction, handleStartEdit, handleCancelEdit, handleDownloadAttachment, handleDeleteAttachment, onDrop, getRootProps, getInputProps

  // Add this function to fetch interaction details
  const fetchInteractionDetails = async (interactionId: number) => {
    const { data, error } = await supabase
      .from('interactions')
      .select('*, comments(*)')
      .eq('id', interactionId)
      .single();

    if (error) {
      console.error('Error fetching interaction details:', error);
      return null;
    }

    return data;
  };

  // Add this function to toggle row expansion
  const toggleRowExpanded = async (interactionId: number) => {
    setExpandedRows(prev => {
      const newExpandedRows = { ...prev };
      newExpandedRows[interactionId] = !prev[interactionId];
      
      // If expanding the row, fetch interaction details and user profiles for comments
      if (newExpandedRows[interactionId]) {
        // Fetch the interaction details including comments
        supabase
          .from('interactions')
          .select('*, comments(*)')
          .eq('id', interactionId)
          .single()
          .then(({ data: interactionData, error }) => {
            if (error) {
              console.error('Error fetching interaction details:', error);
              return;
            }
            
            // Update the fullInteractionData state with the fetched interaction
            setFullInteractionData(interactionData);
            
            // If there are comments, fetch the user profiles
            if (interactionData?.comments?.length) {
              // Get unique user IDs from comments
              const userIds = [...new Set(interactionData.comments.map((comment: any) => comment.user_id))].filter(Boolean);
              
              if (userIds.length > 0) {
                supabase
                  .from('profiles')
                  .select('user_id, name')
                  .in('user_id', userIds)
                  .then(({ data: profilesData, error: profilesError }) => {
                    if (profilesError) {
                      console.error('Error fetching user profiles:', profilesError);
                      return;
                    }

                    const profiles: {[key: string]: {name: string}} = {};
                    profilesData?.forEach(profile => {
                      profiles[profile.user_id] = { name: profile.name || 'Unknown User' };
                    });

                    setUserProfiles(profiles);
                  });
              }
            }
          });
      }
      
      return newExpandedRows;
    });
  };

  const filteredInteractions = interactions.filter(interaction => {
    // Type filter
    if (filters.type && filters.type !== 'all' && interaction.type !== filters.type) {
      return false;
    }

    // Date range filter
    if (filters.dateRange?.from && filters.dateRange?.to) {
      const interactionDate = parseISO(interaction.date);
      if (!isWithinInterval(interactionDate, {
        start: filters.dateRange.from,
        end: addDays(filters.dateRange.to, 1) // Include the end date
      })) {
        return false;
      }
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        interaction.title.toLowerCase().includes(searchLower) ||
        interaction.content.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
        {/* Type Filter */}
        <div>
          <Select
            value={filters.type}
            onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="phone">Phone</SelectItem>
              <SelectItem value="meeting">Meeting</SelectItem>
              <SelectItem value="note">Note</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date Range Filter */}
        <div>
          <DateRangePicker
            value={filters.dateRange}
            onChange={(range) => setFilters(prev => ({ ...prev, dateRange: range }))}
          />
        </div>

        {/* Search */}
        <div className="relative w-full md:w-auto md:flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search interactions..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-9"
          />
        </div>

        {/* New Interaction Button */}
        <Button onClick={() => openModal('new')}>
          <Plus className="h-4 w-4 mr-2" />
          New Interaction
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[30px]"></TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Content</TableHead>
              <TableHead>Attachments</TableHead>
              <TableHead>Comments</TableHead>
              <TableHead>Tasks</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredInteractions.map((interaction) => (
              <React.Fragment key={interaction.id}>
                <TableRow>
                  <TableCell>
                    <Button 
                      variant="ghost" 
                      className="h-8 w-8 p-0"
                      onClick={() => toggleRowExpanded(interaction.id)}
                    >
                      {expandedRows[interaction.id] ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell>{format(new Date(interaction.date), 'dd MMMM yyyy')}</TableCell>
                  <TableCell>
                    <Badge variant={getInteractionBadgeVariant(interaction.type)} className="capitalize">
                      {interaction.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-normal text-left"
                      onClick={() => openModal('details', interaction)}
                    >
                      {interaction.title}
                    </Button>
                  </TableCell>
                  <TableCell className="max-w-[300px] truncate">
                    <div 
                      className="max-w-full truncate prose prose-sm" 
                      dangerouslySetInnerHTML={{ __html: interaction.content }}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {interaction.attachments && interaction.attachments.length > 0 ? (
                        <>
                          <Paperclip className="h-4 w-4" />
                          <span className="text-sm">{interaction.attachments.length}</span>
                        </>
                      ) : (
                        <span className="text-sm text-muted-foreground">None</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">{interaction.comments?.length || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Link className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">{interaction.interaction_task_links?.length || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openModal('details', interaction)}>
                          <Search className="h-4 w-4 mr-2" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openModal('edit', interaction)}>
                          <Pencil className="h-4 w-4 mr-2" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => openModal('delete', interaction)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
                {expandedRows[interaction.id] && fullInteractionData && fullInteractionData.id === interaction.id && (
                  <TableRow>
                    <TableCell colSpan={9} className="bg-muted/50 p-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium">Content</h3>
                          <div 
                            className="mt-2 prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{ __html: fullInteractionData.content }}
                          />
                        </div>
                        
                        <div>
                          <h3 className="text-sm font-medium">Comments</h3>
                          <div className="mt-2">
                            {fullInteractionData.comments && fullInteractionData.comments.length > 0 ? (
                              <div className="space-y-2">
                                {fullInteractionData.comments.map((comment: any) => (
                                  <div key={comment.id} className="bg-white p-3 rounded border text-sm">
                                    <p>{comment.content}</p>
                                    <div className="flex justify-between items-center mt-2">
                                      <span className="text-xs text-gray-500">
                                        {new Date(comment.created_at).toLocaleString()}
                                      </span>
                                      <span className="text-xs font-medium text-gray-700">
                                        {userProfiles[comment.user_id]?.name || 'Unknown User'}
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <p className="text-sm text-gray-500">No comments yet</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Render the modal */}
      {isModalOpen && (
        <InteractionsModal
          isOpen={isModalOpen}
          onClose={closeModal}
          householdId={householdId}
          interactionToEdit={selectedInteraction}
          onInteractionSaved={handleInteractionSaved}
          onInteractionDeleted={handleInteractionDeleted}
          mode={modalMode}
        />
      )}
    </div>
  );
}
