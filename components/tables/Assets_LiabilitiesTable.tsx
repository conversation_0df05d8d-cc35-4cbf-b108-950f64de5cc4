'use client';

import { useEffect, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Plus } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { formatCurrency } from '@/lib/utils';
import AssetsModal from "@/components/modals/AssetsModal";
import LiabilitiesModal from "@/components/modals/LiabilitiesModal";

export interface Asset {
  id: string;
  name: string;
  type: string;
  value: number;
  details?: string;
  property_type?: string;
  rental_income?: number;
  provider?: string;
  linked_income_id?: string;
  fund_type?: string;
  member_id?: string;
}

export interface Liability {
  id: string;
  name: string;
  type: string;
  amount: number;
  interest_rate?: number;
  lender?: string;
  payment_amount?: number;
  payment_frequency?: string;
  loan_type?: string;
  linked_asset_id: string;
  linked_expense_id?: string;
  details?: string;
}

interface Assets_LiabilitiesTableProps {
  assets: Asset[];
  liabilities: Liability[];
  householdId: string;
  onDataChange: () => void;
  onAddAsset: () => void;
  onAddLiability: () => void;
  onEditAsset: (asset: Asset) => void;
  onEditLiability: (liability: Liability) => void;
}

export default function Assets_LiabilitiesTable({
  assets,
  liabilities,
  householdId,
  onDataChange,
  onAddAsset,
  onAddLiability,
  onEditAsset,
  onEditLiability
}: Assets_LiabilitiesTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [activeTab, setActiveTab] = useState('assets');
  const [householdMembers, setHouseholdMembers] = useState<{[key: string]: string}>({});

  // Fetch household member data
  useEffect(() => {
    const fetchHouseholdMembers = async () => {
      if (!householdId) return;

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('members')
        .eq('id', householdId)
        .single();

      if (error || !data?.members) {
        console.error('Error fetching household members:', error);
        return;
      }

      const members = data.members;
      const memberMap: {[key: string]: string} = {};

      // Map member IDs to names
      // 1 = Main member (name1)
      if (members.name1) {
        memberMap['1'] = members.name1;
      }

      // 2 = Partner (name2)
      if (members.name2) {
        memberMap['2'] = members.name2;
      }

      setHouseholdMembers(memberMap);
    };

    fetchHouseholdMembers();
  }, [householdId]);

  const handleDelete = async (id: string, type: 'assets' | 'liabilities') => {
    const supabase = createClient();

    // When deleting an asset or liability, the linked income/expense will be automatically deleted
    // due to the ON DELETE CASCADE constraint in the database
    const { error } = await supabase
      .from(type)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting ${type}:`, error);
    } else {
      onDataChange();
    }
  };

  const assetColumns: ColumnDef<Asset>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <button
          onClick={() => onEditAsset(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('name')}
        </button>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => (
        <button
          onClick={() => onEditAsset(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('type')}
        </button>
      ),
    },
    {
      accessorKey: "value",
      header: "Value",
      cell: ({ row }) => (
        <button
          onClick={() => onEditAsset(row.original)}
          className="text-right hover:underline block w-full"
        >
          {formatCurrency(row.original.value)}
        </button>
      ),
    },
    {
      accessorKey: "property_type",
      header: "Property Type",
      cell: ({ row }) => {
        const propertyType = row.original.property_type;
        if (!propertyType || row.original.type !== 'property') return '-';
        return propertyType === 'owner_occupied' ? 'Owner Occupied' : 'Investment';
      },
    },
    {
      accessorKey: "rental_income",
      header: "Rental Income",
      cell: ({ row }) => {
        if (row.original.type === 'property' &&
            row.original.property_type === 'investment' &&
            row.original.rental_income) {
          return formatCurrency(row.original.rental_income);
        }
        return '-';
      },
    },
    {
      accessorKey: "provider",
      header: "Provider",
      cell: ({ row }) => {
        const type = row.original.type;
        if (!['investment', 'savings', 'superannuation', 'kiwisaver'].includes(type)) return '-';
        return row.original.provider || '-';
      },
    },
    {
      accessorKey: "fund_type",
      header: "Fund Type",
      cell: ({ row }) => {
        if (row.original.type !== 'kiwisaver') return '-';
        return row.original.fund_type || '-';
      },
    },
    {
      accessorKey: "member_id",
      header: "Member",
      cell: ({ row }) => {
        if (row.original.type !== 'kiwisaver') return '-';
        const memberId = row.original.member_id;
        return memberId && householdMembers[memberId] ? householdMembers[memberId] : '-';
      },
    },
    {
      accessorKey: "details",
      header: "Details",
      cell: ({ row }) => row.original.details || '-',
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditAsset(row.original)}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDelete(row.original.id, 'assets')}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const liabilityColumns: ColumnDef<Liability>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <button
          onClick={() => onEditLiability(row.original)}
          className="text-left hover:underline"
        >
          {row.getValue('name')}
        </button>
      ),
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.type;
        // Format the type for display (e.g., convert car_loan to Car Loan)
        return type ? type.split('_').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ') : '-';
      },
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => (
        <button
          onClick={() => onEditLiability(row.original)}
          className="text-right hover:underline block w-full"
        >
          {formatCurrency(row.original.amount)}
        </button>
      ),
    },
    {
      accessorKey: "lender",
      header: "Lender",
      cell: ({ row }) => row.original.lender || '-',
    },
    {
      accessorKey: "interest_rate",
      header: "Interest Rate",
      cell: ({ row }) => row.original.interest_rate ? `${row.original.interest_rate}%` : '-',
    },
    {
      accessorKey: "payment_amount",
      header: "Payment",
      cell: ({ row }) => {
        if (row.original.payment_amount && row.original.payment_frequency) {
          return `${formatCurrency(row.original.payment_amount)} ${row.original.payment_frequency}`;
        }
        return '-';
      },
    },
    {
      accessorKey: "loan_type",
      header: "Loan Type",
      cell: ({ row }) => {
        const loanType = row.original.loan_type;
        if (!loanType || !['mortgage', 'car_loan', 'personal_loan', 'business_loan'].includes(row.original.type)) {
          return '-';
        }
        return loanType === 'principal_and_interest' ? 'Principal and Interest' : 'Interest Only';
      },
    },
    {
      accessorKey: "linked_asset_id",
      header: "Linked Asset",
      cell: ({ row }) => {
        const assetId = row.original.linked_asset_id;
        if (!assetId || assetId === 'none') return 'None';

        const linkedAsset = assets.find(asset => asset.id === assetId);
        return linkedAsset ? `${linkedAsset.name} (${linkedAsset.type})` : 'Unknown Asset';
      },
    },
    {
      accessorKey: "details",
      header: "Details",
      cell: ({ row }) => row.original.details || '-',
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditLiability(row.original)}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDelete(row.original.id, 'liabilities')}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const assetsTable = useReactTable({
    data: assets,
    columns: assetColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  const liabilitiesTable = useReactTable({
    data: liabilities,
    columns: liabilityColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  const totalAssets = assets.reduce((sum, asset) => sum + asset.value, 0);
  const totalLiabilities = liabilities.reduce((sum, liability) => sum + liability.amount, 0);

  if (!assets || !liabilities) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="assets">Assets</TabsTrigger>
            <TabsTrigger value="liabilities">Liabilities</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button
          className="flex items-center gap-2"
          onClick={activeTab === 'assets' ? onAddAsset : onAddLiability}
        >
          <Plus className="h-4 w-4" />
          Add {activeTab === 'assets' ? 'Asset' : 'Liability'}
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {activeTab === 'assets' ? (
              <TableRow>
                {assetsTable.getHeaderGroups().map((headerGroup) => (
                  headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))
                ))}
              </TableRow>
            ) : (
              <TableRow>
                {liabilitiesTable.getHeaderGroups().map((headerGroup) => (
                  headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))
                ))}
              </TableRow>
            )}
          </TableHeader>
          <TableBody>
            {activeTab === 'assets' ? (
              assetsTable.getRowModel().rows?.length ? (
                assetsTable.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={assetColumns.length} className="h-24 text-center">
                    No assets found.
                  </TableCell>
                </TableRow>
              )
            ) : (
              liabilitiesTable.getRowModel().rows?.length ? (
                liabilitiesTable.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={liabilityColumns.length} className="h-24 text-center">
                    No liabilities found.
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
          <TableFooter>
            <TableRow>
              {activeTab === 'assets' ? (
                <>
                  <TableCell colSpan={assetColumns.length - 1} className="text-right font-medium">Total Assets:</TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(totalAssets)}</TableCell>
                </>
              ) : (
                <>
                  <TableCell colSpan={liabilityColumns.length - 1} className="text-right font-medium">Total Liabilities:</TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(totalLiabilities)}</TableCell>
                </>
              )}
            </TableRow>
          </TableFooter>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 pt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => activeTab === 'assets' ? assetsTable.previousPage() : liabilitiesTable.previousPage()}
          disabled={activeTab === 'assets'
            ? !assetsTable.getCanPreviousPage()
            : !liabilitiesTable.getCanPreviousPage()
          }
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => activeTab === 'assets' ? assetsTable.nextPage() : liabilitiesTable.nextPage()}
          disabled={activeTab === 'assets'
            ? !assetsTable.getCanNextPage()
            : !liabilitiesTable.getCanNextPage()
          }
        >
          Next
        </Button>
      </div>


    </div>
  );
}
