import React, { useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useFinancialCalculations } from '@/app/utils/financialCalculations';

interface ModellingTableForPDFProps {
  inputData: any;
  allMetrics?: any[]; // Allow passing metrics directly
  maxRows?: number;
  maxColumns?: number;
  mainName?: string;
  partnerName?: string;
  selectedFields: {
    incomeExpenses: boolean;
    investmentsKiwiSaver: boolean;
    property: boolean;
    taxation: boolean;
    all: boolean;
  };
}

const ModellingTableForPDF: React.FC<ModellingTableForPDFProps> = ({
  inputData,
  allMetrics: externalMetrics,
  maxRows = 20,
  maxColumns = 8, // Maximum columns per table
  mainName = 'Main',
  partnerName = 'Partner',
  selectedFields
}) => {
  const [allMetrics, setAllMetrics] = useState<any[]>([]);
  const { calculateFinancialLife } = useFinancialCalculations(inputData);

  useEffect(() => {
    console.log('[ModellingTableForPDF] Component mounted or props changed');
    console.log('[ModellingTableForPDF] externalMetrics:', externalMetrics);
    console.log('[ModellingTableForPDF] inputData:', inputData);

    if (externalMetrics && externalMetrics.length > 0) {
      // Use externally provided metrics if available
      console.log('[ModellingTableForPDF] Using external metrics, count:', externalMetrics.length);
      setAllMetrics(externalMetrics);
    } else if (inputData) {
      // Otherwise calculate them
      console.log('[ModellingTableForPDF] Calculating metrics from inputData');
      const result = calculateFinancialLife(inputData);
      console.log('[ModellingTableForPDF] Calculated metrics, count:', result.allMetrics?.length || 0);
      setAllMetrics(result.allMetrics as any[]);
    } else {
      console.log('[ModellingTableForPDF] No data available - neither externalMetrics nor inputData');
    }
  }, [inputData, calculateFinancialLife, externalMetrics]);

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

  const percentFormatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  });

  const isCurrencyMetric = (key: string) => {
    // Get investment metrics data if available
    const investmentMetrics = inputData?.investment_metrics || null;

    // Get the investment fund descriptions from investment_metrics if available
    const fundDesc1 = investmentMetrics?.funds?.fund1?.investment_description ||
                      inputData?.investment_description1 || 'Investment Fund 1';
    const fundDesc2 = investmentMetrics?.funds?.fund2?.investment_description ||
                      inputData?.investment_description2 || 'Investment Fund 2';
    const fundDesc3 = investmentMetrics?.funds?.fund3?.investment_description ||
                      inputData?.investment_description3 || 'Investment Fund 3';
    const fundDesc4 = investmentMetrics?.funds?.fund4?.investment_description ||
                      inputData?.investment_description4 || 'Investment Fund 4';
    const fundDesc5 = investmentMetrics?.funds?.fund5?.investment_description ||
                      inputData?.investment_description5 || 'Investment Fund 5';

    // Create a mapping for currency metrics with dynamic names
    const dynamicCurrencyMetrics = [
      'Savings Fund', 'Gross Income', 'Net Income', 'Total Expenditure', 'Additional Expenditure',
      'Net Wealth', 'Net Cashflow', 'Total Withdrawals', 'Investments Fund',
      'Total KiwiSaver',
      // Dynamic names
      `${mainName} KiwiSaver`, `${partnerName} KiwiSaver`,
      `${mainName} Income`, `${partnerName} Income`,
      `${mainName} Income Tax`, `${partnerName} Income Tax`,
      `${mainName} Employee KiwiSaver`, `${mainName} Employer KiwiSaver`,
      `${partnerName} Employee KiwiSaver`, `${partnerName} Employer KiwiSaver`,
      // Original names (for backward compatibility)
      'Main KiwiSaver', 'Partner KiwiSaver',
      'Main Income', 'Partner Income',
      'Main Income Tax', 'Partner Income Tax',
      'Main Employee KiwiSaver', 'Main Employer KiwiSaver',
      'Partner Employee KiwiSaver', 'Partner Employer KiwiSaver',
      // Property related
      'Property Value', 'Property Value 2', 'Property Value 3', 'Property Value 4', 'Property Value 5',
      'Debt Value', 'Debt Value 2', 'Debt Value 3', 'Debt Value 4', 'Debt Value 5',
      'Monthly Debt Repayment', 'Monthly Debt Repayment 2', 'Monthly Debt Repayment 3',
      'Monthly Debt Repayment 4', 'Monthly Debt Repayment 5',
      'Annual Debt Repayments', 'Annual Debt Repayments 2', 'Annual Debt Repayments 3',
      'Annual Debt Repayments 4', 'Annual Debt Repayments 5',
      'Annual Interest Payments', 'Annual Interest Payments 2', 'Annual Interest Payments 3',
      'Annual Interest Payments 4', 'Annual Interest Payments 5',
      'Annual Principal Repayments', 'Annual Principal Repayments 2', 'Annual Principal Repayments 3',
      'Annual Principal Repayments 4', 'Annual Principal Repayments 5',
      'Property Sale Proceeds', 'Property Sale Proceeds 2', 'Property Sale Proceeds 3',
      'Property Sale Proceeds 4', 'Property Sale Proceeds 5',
      'Transaction Costs', 'Transaction Costs 2', 'Transaction Costs 3',
      'Transaction Costs 4', 'Transaction Costs 5',
      'Debt Paid', 'Debt Paid 2', 'Debt Paid 3', 'Debt Paid 4', 'Debt Paid 5',
      'Rental Income', 'Board Income',
      // Added metrics to ensure correct currency formatting
      'Annual Investment Return',
      'Annual KiwiSaver Return',
      'Minimum Investment Fund',
      'Maximum Investment Fund',
      'Annual Investment Contribution',
      // Individual investment funds with both default and custom names
      'Investment Fund 1', 'Investment Fund 2', 'Investment Fund 3', 'Investment Fund 4', 'Investment Fund 5',
      fundDesc1, fundDesc2, fundDesc3, fundDesc4, fundDesc5,
      // Investment fund contributions
      'Annual Investment Contribution 1', 'Annual Investment Contribution 2',
      'Annual Investment Contribution 3', 'Annual Investment Contribution 4', 'Annual Investment Contribution 5',
      // Investment fund returns
      'Annual Investment Return 1', 'Annual Investment Return 2',
      'Annual Investment Return 3', 'Annual Investment Return 4', 'Annual Investment Return 5'
    ];
    return dynamicCurrencyMetrics.includes(key);
  };

  const getSelectedColumns = () => {
    // Always include Age column first
    const selectedColumns = ['Age'];

    // Create a mapping between dynamic names and actual data keys
    const nameMapping: Record<string, string> = {};

    // Map dynamic column names to actual data keys
    if (mainName !== 'Main') {
      nameMapping[`${mainName} Income`] = 'Main Income';
      nameMapping[`${mainName} Income Tax`] = 'Main Income Tax';
      nameMapping[`${mainName} KiwiSaver`] = 'Main KiwiSaver';
      nameMapping[`${mainName} Employee KiwiSaver`] = 'Main Employee KiwiSaver';
      nameMapping[`${mainName} Employer KiwiSaver`] = 'Main Employer KiwiSaver';
    }

    if (partnerName !== 'Partner') {
      nameMapping[`${partnerName} Income`] = 'Partner Income';
      nameMapping[`${partnerName} Income Tax`] = 'Partner Income Tax';
      nameMapping[`${partnerName} KiwiSaver`] = 'Partner KiwiSaver';
      nameMapping[`${partnerName} Employee KiwiSaver`] = 'Partner Employee KiwiSaver';
      nameMapping[`${partnerName} Employer KiwiSaver`] = 'Partner Employer KiwiSaver';
    }

    // Get investment metrics data if available
    const investmentMetrics = inputData?.investment_metrics || null;

    // Get the investment fund descriptions from investment_metrics if available
    const fundDesc1 = investmentMetrics?.funds?.fund1?.investment_description ||
                      inputData?.investment_description1 || 'Investment Fund 1';
    const fundDesc2 = investmentMetrics?.funds?.fund2?.investment_description ||
                      inputData?.investment_description2 || 'Investment Fund 2';
    const fundDesc3 = investmentMetrics?.funds?.fund3?.investment_description ||
                      inputData?.investment_description3 || 'Investment Fund 3';
    const fundDesc4 = investmentMetrics?.funds?.fund4?.investment_description ||
                      inputData?.investment_description4 || 'Investment Fund 4';
    const fundDesc5 = investmentMetrics?.funds?.fund5?.investment_description ||
                      inputData?.investment_description5 || 'Investment Fund 5';

    // Map display names to actual data keys
    nameMapping[fundDesc1] = 'Investment Fund 1';
    nameMapping[fundDesc2] = 'Investment Fund 2';
    nameMapping[fundDesc3] = 'Investment Fund 3';
    nameMapping[fundDesc4] = 'Investment Fund 4';
    nameMapping[fundDesc5] = 'Investment Fund 5';

    // Map contribution names
    nameMapping['Annual Investment Contribution 1'] = 'Annual Investment Contribution 1';
    nameMapping['Annual Investment Contribution 2'] = 'Annual Investment Contribution 2';
    nameMapping['Annual Investment Contribution 3'] = 'Annual Investment Contribution 3';
    nameMapping['Annual Investment Contribution 4'] = 'Annual Investment Contribution 4';
    nameMapping['Annual Investment Contribution 5'] = 'Annual Investment Contribution 5';

    // Map return names
    nameMapping['Annual Investment Return 1'] = 'Annual Investment Return 1';
    nameMapping['Annual Investment Return 2'] = 'Annual Investment Return 2';
    nameMapping['Annual Investment Return 3'] = 'Annual Investment Return 3';
    nameMapping['Annual Investment Return 4'] = 'Annual Investment Return 4';
    nameMapping['Annual Investment Return 5'] = 'Annual Investment Return 5';

    // Define columns for each tab
    const tabColumns: Record<string, string[]> = {
      incomeExpenses: [
        'Savings Fund',
        'Gross Income',
        'Net Income',
        'Total Expenditure',
        'Additional Expenditure',
        'Net Cashflow',
        `${mainName} Income Tax`,
        `${partnerName} Income Tax`,
        `${mainName} Income`,
        `${partnerName} Income`
      ],
      investmentsKiwiSaver: [
        // Legacy combined investment fund
        'Investments Fund',

        // Individual investment funds - check investment_metrics first, then fall back to inputData
        ...(investmentMetrics?.funds?.fund1?.initial_investment > 0 || inputData?.initial_investment1 > 0 ?
            [fundDesc1] : []),
        ...(investmentMetrics?.funds?.fund2?.initial_investment > 0 || inputData?.initial_investment2 > 0 ?
            [fundDesc2] : []),
        ...(investmentMetrics?.funds?.fund3?.initial_investment > 0 || inputData?.initial_investment3 > 0 ?
            [fundDesc3] : []),
        ...(investmentMetrics?.funds?.fund4?.initial_investment > 0 || inputData?.initial_investment4 > 0 ?
            [fundDesc4] : []),
        ...(investmentMetrics?.funds?.fund5?.initial_investment > 0 || inputData?.initial_investment5 > 0 ?
            [fundDesc5] : []),

        // Investment fund contributions - check investment_metrics first, then fall back to inputData
        ...(investmentMetrics?.funds?.fund1?.annual_investment_contribution > 0 || inputData?.annual_investment_contribution1 > 0 ?
            ['Annual Investment Contribution 1'] : []),
        ...(investmentMetrics?.funds?.fund2?.annual_investment_contribution > 0 || inputData?.annual_investment_contribution2 > 0 ?
            ['Annual Investment Contribution 2'] : []),
        ...(investmentMetrics?.funds?.fund3?.annual_investment_contribution > 0 || inputData?.annual_investment_contribution3 > 0 ?
            ['Annual Investment Contribution 3'] : []),
        ...(investmentMetrics?.funds?.fund4?.annual_investment_contribution > 0 || inputData?.annual_investment_contribution4 > 0 ?
            ['Annual Investment Contribution 4'] : []),
        ...(investmentMetrics?.funds?.fund5?.annual_investment_contribution > 0 || inputData?.annual_investment_contribution5 > 0 ?
            ['Annual Investment Contribution 5'] : []),

        // KiwiSaver related
        'Total KiwiSaver',
        `${mainName} KiwiSaver`,
        `${partnerName} KiwiSaver`,
        `${mainName} Employee KiwiSaver`,
        `${mainName} Employer KiwiSaver`,
        `${partnerName} Employee KiwiSaver`,
        `${partnerName} Employer KiwiSaver`,

        // Other investment metrics
        'Annual Investment Return',
        'Annual KiwiSaver Return',
        'Minimum Investment Fund',
        'Maximum Investment Fund',
        'Annual Investment Contribution',
        'KiwiSaver Contributions',
        `${partnerName} KiwiSaver Contributions`,
        'Superannuation'
      ],
      taxation: [
        `${mainName} Income Tax`,
        `${partnerName} Income Tax`,
        'MTR Investment Tax',
        'PIE Investment tax',
        'KiwiSaver Tax',
        'Income Tax'
      ]
    };

    // Property columns are dynamic based on how many properties exist
    const propertyColumns: string[] = [];

    // Property 1 always exists
    const propertyTitle1 = inputData?.property_title || 'Property 1';
    propertyColumns.push(
      `${propertyTitle1} Value`,
      `${propertyTitle1} Debt`
    );
    nameMapping[`${propertyTitle1} Value`] = 'Property Value';
    nameMapping[`${propertyTitle1} Debt`] = 'Debt Value';

    // Only add Property 2 if it exists
    if (inputData?.property_value2) {
      const propertyTitle2 = inputData?.property_title2 || 'Property 2';
      propertyColumns.push(
        `${propertyTitle2} Value`,
        `${propertyTitle2} Debt`
      );
      nameMapping[`${propertyTitle2} Value`] = 'Property Value 2';
      nameMapping[`${propertyTitle2} Debt`] = 'Debt Value 2';
    }

    // Only add Property 3 if it exists
    if (inputData?.property_value3) {
      const propertyTitle3 = inputData?.property_title3 || 'Property 3';
      propertyColumns.push(
        `${propertyTitle3} Value`,
        `${propertyTitle3} Debt`
      );
      nameMapping[`${propertyTitle3} Value`] = 'Property Value 3';
      nameMapping[`${propertyTitle3} Debt`] = 'Debt Value 3';
    }

    // Collect all columns based on selected fields
    const allSelectedColumns: string[] = [];

    if (selectedFields.all) {
      // If "all" is selected, include all columns from all tabs
      allSelectedColumns.push(...tabColumns.incomeExpenses);
      allSelectedColumns.push(...tabColumns.investmentsKiwiSaver);
      allSelectedColumns.push(...propertyColumns);
      allSelectedColumns.push(...tabColumns.taxation);
    } else {
      // Otherwise, only include columns from selected tabs
      if (selectedFields.incomeExpenses) {
        allSelectedColumns.push(...tabColumns.incomeExpenses);
      }
      if (selectedFields.investmentsKiwiSaver) {
        allSelectedColumns.push(...tabColumns.investmentsKiwiSaver);
      }
      if (selectedFields.property) {
        allSelectedColumns.push(...propertyColumns);
      }
      if (selectedFields.taxation) {
        allSelectedColumns.push(...tabColumns.taxation);
      }
    }

    // Remove duplicates
    const uniqueColumns = [...new Set(allSelectedColumns)];

    // Add all unique columns after Age
    selectedColumns.push(...uniqueColumns);

    // Log the active investment funds for debugging
    console.log('[ModellingTableForPDF] Active investment funds:', {
      // Check both investment_metrics and inputData
      fund1: investmentMetrics?.funds?.fund1?.initial_investment > 0 || inputData?.initial_investment1 > 0,
      fund2: investmentMetrics?.funds?.fund2?.initial_investment > 0 || inputData?.initial_investment2 > 0,
      fund3: investmentMetrics?.funds?.fund3?.initial_investment > 0 || inputData?.initial_investment3 > 0,
      fund4: investmentMetrics?.funds?.fund4?.initial_investment > 0 || inputData?.initial_investment4 > 0,
      fund5: investmentMetrics?.funds?.fund5?.initial_investment > 0 || inputData?.initial_investment5 > 0,
      withdrawalPriorities: investmentMetrics?.withdrawal_priorities || inputData?.withdrawal_priorities,
      fundDescriptions: {
        fund1: fundDesc1,
        fund2: fundDesc2,
        fund3: fundDesc3,
        fund4: fundDesc4,
        fund5: fundDesc5
      }
    });

    return {
      columns: selectedColumns,
      nameMapping
    };
  };

  const { columns, nameMapping } = getSelectedColumns();

  // Split columns into chunks of maxColumns
  const columnChunks: string[][] = [];
  // Always include Age in each chunk
  for (let i = 1; i < columns.length; i += (maxColumns - 1)) {
    const chunk = ['Age', ...columns.slice(i, i + (maxColumns - 1))];
    columnChunks.push(chunk);
  }

  const getFilteredMetrics = (columnSet: string[]) => {
    return allMetrics.map(metric => {
      const filteredMetric: { [key: string]: any } = {};
      columnSet.forEach(key => {
        // If we have a mapping for this key, use the original data key
        const dataKey = nameMapping[key] || key;
        // Store the value under the display key (with dynamic name)
        filteredMetric[key] = metric[dataKey];
      });
      return filteredMetric;
    });
  };

  const renderCellValue = (key: string, value: any) => {
    if (value === undefined || value === null) {
      return '-';
    }

    if (typeof value === 'number' && key !== 'Age') {
      if (isCurrencyMetric(key)) {
        return formatter.format(value);
      }

      // Check if the key suggests a percentage
      if (key.toLowerCase().includes('rate') || key.toLowerCase().includes('return')) {
        return percentFormatter.format(value / 100);
      }

      return value.toLocaleString(undefined, {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    }
    return String(value);
  };

  console.log('[ModellingTableForPDF] Rendering with metrics count:', allMetrics.length);
  console.log('[ModellingTableForPDF] Selected columns:', getSelectedColumns());
  console.log('[ModellingTableForPDF] Column chunks:', columnChunks);

  if (!allMetrics.length) {
    console.log('[ModellingTableForPDF] No metrics available, showing empty state');
    return <div className="p-4 text-center text-gray-500">No data available</div>;
  }

  return (
    <div className="space-y-8">
      {columnChunks.map((columnSet, tableIndex) => {
        const tableMetrics = getFilteredMetrics(columnSet);

        return (
          <div key={tableIndex} className="overflow-x-auto mb-8">
            {/* Add a title for each table if there are multiple tables */}
            {columnChunks.length > 1 && (
              <h4 className="text-sm font-medium mb-2">
                Financial Metrics Table {tableIndex + 1} of {columnChunks.length}
              </h4>
            )}
            <Table className="w-full border-collapse">
              <TableHeader className="bg-gray-50">
                <TableRow>
                  {columnSet.map((key) => (
                    <TableHead
                      key={key}
                      className="px-2 py-2 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider border-b border-gray-200"
                      style={{
                        maxWidth: '80px',
                        overflow: 'hidden',
                        whiteSpace: 'normal',
                        wordBreak: 'break-word',
                        fontSize: '9px',
                        lineHeight: '1.2'
                      }}
                    >
                      {key}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {tableMetrics.slice(0, maxRows).map((metric, index) => (
                  <TableRow
                    key={index}
                    className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                  >
                    {columnSet.map(key => (
                      <TableCell
                        key={key}
                        className="px-2 py-1 whitespace-nowrap text-xs text-gray-600 border-b border-gray-100"
                        style={{
                          maxWidth: '80px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          fontSize: '9px'
                        }}
                      >
                        {renderCellValue(key, metric[key])}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        );
      })}
    </div>
  );
};

export default ModellingTableForPDF;
