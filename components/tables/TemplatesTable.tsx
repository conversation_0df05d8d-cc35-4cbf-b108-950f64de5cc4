'use client';

import React, { useState, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  PaginationState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Template {
  id: string;
  title: string;
  type: string;
  created_at: string;
  last_edited_at: string;
  created_by: string;
}

interface TemplatesTableProps {
  data: Template[];
  onDataChange: () => Promise<void>;
  onCreateTemplate: () => void;
  onEditTemplate?: (template: Template) => void;
}

export function TemplatesTable({ data, onDataChange, onCreateTemplate, onEditTemplate }: TemplatesTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Check if sidebar is collapsed on component mount and when localStorage changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedCollapsed = localStorage.getItem('adminSidebarCollapsed');
      setSidebarCollapsed(savedCollapsed ? JSON.parse(savedCollapsed) : false);
    };

    // Initial check
    checkSidebarState();

    // Listen for storage events (when localStorage changes)
    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);

    // Create a custom event listener to detect sidebar changes
    const handleSidebarChange = () => {
      checkSidebarState();
    };

    window.addEventListener('sidebarChange', handleSidebarChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarChange', handleSidebarChange);
    };
  }, []);

  const handleDelete = (template: Template) => {
    setTemplateToDelete(template.id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (templateToDelete) {
      const supabase = createClient();
      const { error } = await supabase
        .from('templates')
        .delete()
        .eq('id', templateToDelete);

      if (error) {
        console.error('Error deleting template:', error);
      } else {
        onDataChange(); // Refresh the table
      }
      setDeleteDialogOpen(false);
      setTemplateToDelete(null);
    }
  };

  const getTemplateTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'Discovery Document': "bg-blue-100 text-blue-800",
      'Terms of Engagement': "bg-green-100 text-green-800",
      'Risk Profiler': "bg-orange-100 text-orange-800",
      'Third Party Authority': "bg-purple-100 text-purple-800",
      'Workflow': "bg-indigo-100 text-indigo-800",
      'Email': "bg-red-100 text-red-800",
    };

    return colorMap[type] || "bg-gray-100 text-gray-800";
  };

  const columns: ColumnDef<Template>[] = [
    {
      accessorKey: "title",
      header: "Title",
      size: 250,
      cell: ({ row }) => {
        const template = row.original;
        return (
          <button
            onClick={() => {
              // For specialized editors, navigate to the appropriate page
              if (template.type === 'Discovery Document') {
                window.location.href = `/protected/admin/templates/discovery-document?templateId=${template.id}`;
              } else if (template.type === 'Risk Profiler') {
                window.location.href = `/protected/admin/templates/risk-profiler?templateId=${template.id}`;
              } else if (template.type === 'Terms of Engagement') {
                window.location.href = `/protected/admin/templates/toe?templateId=${template.id}`;
              } else {
                // For other template types, open the edit modal
                if (onEditTemplate) {
                  onEditTemplate(template);
                } else {
                  console.log('Edit template:', template.id);
                }
              }
            }}
            className="text-blue-600 hover:text-blue-800 hover:underline text-left"
          >
            {template.title}
          </button>
        );
      },
    },
    {
      accessorKey: "type",
      header: "Type",
      size: 180,
      cell: ({ row }) => {
        const type = row.original.type;
        const bgColor = getTemplateTypeColor(type);

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${bgColor}`}>
            {type}
          </span>
        );
      },
    },
    {
      accessorKey: "created_by",
      header: "Created by",
      size: 150,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      size: 120,
      cell: ({ row }) => {
        return new Date(row.original.created_at).toLocaleDateString();
      },
    },
    {
      accessorKey: "last_edited_at",
      header: "Last Edited",
      size: 120,
      cell: ({ row }) => {
        return new Date(row.original.last_edited_at).toLocaleDateString();
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: ({ row }) => {
        const template = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleDelete(template)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
  });

  return (
    <div
      className="flex flex-col h-full pb-4 pr-4 pl-4"
      style={{
        maxWidth: `calc(100vw - ${sidebarCollapsed ? '64px' : '220px'})`,
        marginLeft: 'auto',
        marginRight: 'auto',
        transition: 'max-width 0.3s ease-in-out'
      }}
    >
      {/* Fixed header with filters and create button */}
      <div className="flex items-center justify-between py-4 sticky top-0 bg-white z-20">
        <Input
          placeholder="Filter templates..."
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <Button onClick={onCreateTemplate}>Create Template</Button>
      </div>

      {/* Scrollable table area with fixed header */}
      <div className="flex-1 min-h-0 overflow-hidden border rounded-md">
        <div className="relative">
          {/* Fixed header */}
          <div className="sticky top-0 bg-white z-10 w-full">
            <Table className="w-full table-fixed">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        style={{ width: `${header.getSize()}px` }}
                        className={header.id === "actions" ? "text-right" : ""}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
            </Table>
          </div>

          {/* Scrollable body */}
          <div className="overflow-auto max-h-[calc(100vh-300px)]">
            <Table className="w-full table-fixed">
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="h-10"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          style={{ width: `${cell.column.getSize()}px` }}
                          className={cell.column.id === "actions" ? "text-right" : "px-4 py-2"}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No templates found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Fixed footer with pagination controls */}
      <div className="flex items-center justify-between space-x-2 py-4 sticky bottom-0 bg-white z-20">
        <div className="flex items-center space-x-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredRowModel().rows.length} total template{table.getFilteredRowModel().rows.length === 1 ? '' : 's'}
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm">Show</span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pagination.pageSize.toString()} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm">templates</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <div className="flex items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount()}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the template.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}