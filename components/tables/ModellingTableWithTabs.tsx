import React, { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFinancialCalculations } from '@/app/utils/financialCalculations';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { InfoIcon, Download } from 'lucide-react';

interface ModellingTableWithTabsProps {
  inputData: any;
  maxRows?: number;
  mainName?: string;
  partnerName?: string;
  initialActiveTab?: string;
}

const ModellingTableWithTabs: React.FC<ModellingTableWithTabsProps> = ({
  inputData,
  maxRows,
  mainName = 'Main',
  partnerName = 'Partner',
  initialActiveTab = 'all'
}) => {
  const [allMetrics, setAllMetrics] = useState([]);
  const { calculateFinancialLife } = useFinancialCalculations(inputData);
  const [activeTab, setActiveTab] = useState(initialActiveTab);

  useEffect(() => {
    if (inputData) {
      const result = calculateFinancialLife(inputData);
      setAllMetrics(result.allMetrics as typeof allMetrics);
    }
  }, [inputData, calculateFinancialLife]);

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

  const percentFormatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const isCurrencyMetric = (key: string) => {
    // Check if the key is an income portion metric
    if (key.toLowerCase().includes('income portion')) {
      return false;
    }

    // Check if it's a tax metric
    if (key.includes('Tax')) {
      return true;
    }

    // Get the investment fund descriptions from inputData for currency metrics
    const fundDesc1 = inputData?.investment_description1 || 'Investment Fund 1';
    const fundDesc2 = inputData?.investment_description2 || 'Investment Fund 2';
    const fundDesc3 = inputData?.investment_description3 || 'Investment Fund 3';
    const fundDesc4 = inputData?.investment_description4 || 'Investment Fund 4';
    const fundDesc5 = inputData?.investment_description5 || 'Investment Fund 5';

    // Create a mapping for currency metrics with dynamic names
    const dynamicCurrencyMetrics = [
      `${mainName} KiwiSaver Tax`,
      `${partnerName} KiwiSaver Tax`,
      'Savings Fund', 'Gross Income', 'Net Income', 'Total Expenditure', 'Additional Expenditure',
      'Net Wealth', 'Net Cashflow', 'Total Withdrawals', 'Investments Fund',
      'Total KiwiSaver',
      // Dynamic investment fund names
      fundDesc1, fundDesc2, fundDesc3, fundDesc4, fundDesc5,
      // Dynamic return names
      `${fundDesc1} Return`, `${fundDesc2} Return`, `${fundDesc3} Return`, `${fundDesc4} Return`, `${fundDesc5} Return`,
      // Dynamic names
      `${mainName} KiwiSaver`, `${partnerName} KiwiSaver`,
      `${mainName} Income`, `${partnerName} Income`,
      `${mainName} Income Tax`, `${partnerName} Income Tax`,
      `${mainName} Employee KiwiSaver`, `${mainName} Employer KiwiSaver`,
      `${partnerName} Employee KiwiSaver`, `${partnerName} Employer KiwiSaver`,
      // Original names (for backward compatibility)
      'Main KiwiSaver', 'Partner KiwiSaver',
      'Main Income', 'Partner Income',
      'Main Income Tax', 'Partner Income Tax',
      'Main Employee KiwiSaver', 'Main Employer KiwiSaver',
      'Partner Employee KiwiSaver', 'Partner Employer KiwiSaver',
      // Other currency metrics
      'Property Value', 'Property Value 2', 'Property Value 3', 'Property Value 4', 'Property Value 5',
      'Debt Value', 'Debt Value 2', 'Debt Value 3', 'Debt Value 4', 'Debt Value 5',
      'Monthly Debt Repayment', 'Monthly Debt Repayment 2', 'Monthly Debt Repayment 3',
      'Monthly Debt Repayment 4', 'Monthly Debt Repayment 5',
      'Annual Debt Repayments', 'Annual Debt Repayments 2', 'Annual Debt Repayments 3',
      'Annual Debt Repayments 4', 'Annual Debt Repayments 5',
      'Annual Interest Payments', 'Annual Interest Payments 2', 'Annual Interest Payments 3',
      'Annual Interest Payments 4', 'Annual Interest Payments 5',
      'Annual Principal Repayments', 'Annual Principal Repayments 2', 'Annual Principal Repayments 3',
      'Annual Principal Repayments 4', 'Annual Principal Repayments 5',
      'Property Sale Proceeds', 'Property Sale Proceeds 2', 'Property Sale Proceeds 3',
      'Property Sale Proceeds 4', 'Property Sale Proceeds 5',
      'Transaction Costs', 'Transaction Costs 2', 'Transaction Costs 3',
      'Transaction Costs 4', 'Transaction Costs 5',
      'Debt Paid', 'Debt Paid 2', 'Debt Paid 3', 'Debt Paid 4', 'Debt Paid 5',
      'Rental Income', 'Board Income',
      `${mainName} Income`, `${partnerName} Income`,
      `${mainName} Income Tax`, `${partnerName} Income Tax`,
      `${mainName} Employee KiwiSaver`, `${mainName} Employer KiwiSaver`,
      `${partnerName} Employee KiwiSaver`, `${partnerName} Employer KiwiSaver`,
      // Dynamic property names
      `${inputData?.property_title || 'Property 1'} Value`,
      `${inputData?.property_title2 || 'Property 2'} Value`,
      `${inputData?.property_title3 || 'Property 3'} Value`,
      `${inputData?.property_title4 || 'Property 4'} Value`,
      `${inputData?.property_title5 || 'Property 5'} Value`,
      `${inputData?.property_title || 'Property 1'} Debt`,
      `${inputData?.property_title2 || 'Property 2'} Debt`,
      `${inputData?.property_title3 || 'Property 3'} Debt`,
      `${inputData?.property_title4 || 'Property 4'} Debt`,
      `${inputData?.property_title5 || 'Property 5'} Debt`,
      `${inputData?.property_title || 'Property 1'} Monthly Repayment`,
      `${inputData?.property_title2 || 'Property 2'} Monthly Repayment`,
      `${inputData?.property_title3 || 'Property 3'} Monthly Repayment`,
      `${inputData?.property_title4 || 'Property 4'} Monthly Repayment`,
      `${inputData?.property_title5 || 'Property 5'} Monthly Repayment`,
      `${inputData?.property_title || 'Property 1'} Annual Repayments`,
      `${inputData?.property_title2 || 'Property 2'} Annual Repayments`,
      `${inputData?.property_title3 || 'Property 3'} Annual Repayments`,
      `${inputData?.property_title4 || 'Property 4'} Annual Repayments`,
      `${inputData?.property_title5 || 'Property 5'} Annual Repayments`,
      `${inputData?.property_title || 'Property 1'} Interest`,
      `${inputData?.property_title2 || 'Property 2'} Interest`,
      `${inputData?.property_title3 || 'Property 3'} Interest`,
      `${inputData?.property_title4 || 'Property 4'} Interest`,
      `${inputData?.property_title5 || 'Property 5'} Interest`,
      `${inputData?.property_title || 'Property 1'} Principal`,
      `${inputData?.property_title2 || 'Property 2'} Principal`,
      `${inputData?.property_title3 || 'Property 3'} Principal`,
      `${inputData?.property_title4 || 'Property 4'} Principal`,
      `${inputData?.property_title5 || 'Property 5'} Principal`,
      `${inputData?.property_title || 'Property 1'} Sale Proceeds`,
      `${inputData?.property_title2 || 'Property 2'} Sale Proceeds`,
      `${inputData?.property_title3 || 'Property 3'} Sale Proceeds`,
      `${inputData?.property_title4 || 'Property 4'} Sale Proceeds`,
      `${inputData?.property_title5 || 'Property 5'} Sale Proceeds`,
      `${inputData?.property_title || 'Property 1'} Transaction Costs`,
      `${inputData?.property_title2 || 'Property 2'} Transaction Costs`,
      `${inputData?.property_title3 || 'Property 3'} Transaction Costs`,
      `${inputData?.property_title4 || 'Property 4'} Transaction Costs`,
      `${inputData?.property_title5 || 'Property 5'} Transaction Costs`,
      `${inputData?.property_title || 'Property 1'} Debt Paid`,
      `${inputData?.property_title2 || 'Property 2'} Debt Paid`,
      `${inputData?.property_title3 || 'Property 3'} Debt Paid`,
      `${inputData?.property_title4 || 'Property 4'} Debt Paid`,
      `${inputData?.property_title5 || 'Property 5'} Debt Paid`,
      // Added metrics to ensure correct currency formatting
      'Annual Investment Return',
      'Annual Investment Return 1',
      'Annual Investment Return 2',
      'Annual Investment Return 3',
      'Annual Investment Return 4',
      'Annual Investment Return 5',
      'Annual KiwiSaver Return',
      `${mainName} KiwiSaver Return`,
      `${partnerName} KiwiSaver Return`,
      'Minimum Investment Fund',
      'Maximum Investment Fund',
      'Annual Investment Contribution',
      'Annual Investment Contribution 1',
      'Annual Investment Contribution 2',
      'Annual Investment Contribution 3',
      'Annual Investment Contribution 4',
      'Annual Investment Contribution 5',
      'Investment Fund 1',
      'Investment Fund 2',
      'Investment Fund 3',
      'Investment Fund 4',
      'Investment Fund 5',
      'Fund 1 Tax',
      'Fund 2 Tax',
      'Fund 3 Tax',
      'Fund 4 Tax',
      'Fund 5 Tax'
    ];
    return dynamicCurrencyMetrics.includes(key);
  };

  const getFilteredMetrics = () => {
    // Create a mapping between dynamic names and actual data keys
    const nameMapping: Record<string, string> = {};

    // Get the investment fund descriptions from inputData
    const fundDesc1 = inputData?.investment_description1 || 'Investment Fund 1';
    const fundDesc2 = inputData?.investment_description2 || 'Investment Fund 2';
    const fundDesc3 = inputData?.investment_description3 || 'Investment Fund 3';
    const fundDesc4 = inputData?.investment_description4 || 'Investment Fund 4';
    const fundDesc5 = inputData?.investment_description5 || 'Investment Fund 5';

    // Get active investment funds from withdrawal priorities
    const activeInvestmentFunds = inputData?.withdrawal_priorities || [1];
    const isActiveFund1 = activeInvestmentFunds.includes(1);
    const isActiveFund2 = activeInvestmentFunds.includes(2);
    const isActiveFund3 = activeInvestmentFunds.includes(3);
    const isActiveFund4 = activeInvestmentFunds.includes(4);
    const isActiveFund5 = activeInvestmentFunds.includes(5);

    // Map investment fund names to actual data keys
    nameMapping[fundDesc1] = 'Investment Fund 1';
    nameMapping[fundDesc2] = 'Investment Fund 2';
    nameMapping[fundDesc3] = 'Investment Fund 3';
    nameMapping[fundDesc4] = 'Investment Fund 4';
    nameMapping[fundDesc5] = 'Investment Fund 5';

    // Map fund tax names
    nameMapping[`${fundDesc1} Tax`] = 'Fund 1 Tax';
    nameMapping[`${fundDesc2} Tax`] = 'Fund 2 Tax';
    nameMapping[`${fundDesc3} Tax`] = 'Fund 3 Tax';
    nameMapping[`${fundDesc4} Tax`] = 'Fund 4 Tax';
    nameMapping[`${fundDesc5} Tax`] = 'Fund 5 Tax';

    // Map fund income portion names
    nameMapping[`${fundDesc1} Income Portion`] = 'Fund 1 Income Portion';
    nameMapping[`${fundDesc2} Income Portion`] = 'Fund 2 Income Portion';
    nameMapping[`${fundDesc3} Income Portion`] = 'Fund 3 Income Portion';
    nameMapping[`${fundDesc4} Income Portion`] = 'Fund 4 Income Portion';
    nameMapping[`${fundDesc5} Income Portion`] = 'Fund 5 Income Portion';

    // Map fund return names
    nameMapping[`${fundDesc1} Return`] = 'Annual Investment Return 1';
    nameMapping[`${fundDesc2} Return`] = 'Annual Investment Return 2';
    nameMapping[`${fundDesc3} Return`] = 'Annual Investment Return 3';
    nameMapping[`${fundDesc4} Return`] = 'Annual Investment Return 4';
    nameMapping[`${fundDesc5} Return`] = 'Annual Investment Return 5';

    // Map fund contribution names
    nameMapping[`${fundDesc1} Contribution`] = 'Annual Investment Contribution 1';
    nameMapping[`${fundDesc2} Contribution`] = 'Annual Investment Contribution 2';
    nameMapping[`${fundDesc3} Contribution`] = 'Annual Investment Contribution 3';
    nameMapping[`${fundDesc4} Contribution`] = 'Annual Investment Contribution 4';
    nameMapping[`${fundDesc5} Contribution`] = 'Annual Investment Contribution 5';

    // Get property titles from inputData
    const propertyTitle1 = inputData?.property_title || 'Property 1';
    const propertyTitle2 = inputData?.property_title2 || 'Property 2';
    const propertyTitle3 = inputData?.property_title3 || 'Property 3';
    const propertyTitle4 = inputData?.property_title4 || 'Property 4';
    const propertyTitle5 = inputData?.property_title5 || 'Property 5';

    // Map property names
    nameMapping[`${propertyTitle1} Value`] = 'Property Value';
    nameMapping[`${propertyTitle2} Value`] = 'Property Value 2';
    nameMapping[`${propertyTitle3} Value`] = 'Property Value 3';
    nameMapping[`${propertyTitle4} Value`] = 'Property Value 4';
    nameMapping[`${propertyTitle5} Value`] = 'Property Value 5';

    // Map property debt names
    nameMapping[`${propertyTitle1} Debt`] = 'Debt Value';
    nameMapping[`${propertyTitle2} Debt`] = 'Debt Value 2';
    nameMapping[`${propertyTitle3} Debt`] = 'Debt Value 3';
    nameMapping[`${propertyTitle4} Debt`] = 'Debt Value 4';
    nameMapping[`${propertyTitle5} Debt`] = 'Debt Value 5';

    // Map property repayment names
    nameMapping[`${propertyTitle1} Monthly Repayment`] = 'Monthly Debt Repayment';
    nameMapping[`${propertyTitle2} Monthly Repayment`] = 'Monthly Debt Repayment 2';
    nameMapping[`${propertyTitle3} Monthly Repayment`] = 'Monthly Debt Repayment 3';
    nameMapping[`${propertyTitle4} Monthly Repayment`] = 'Monthly Debt Repayment 4';
    nameMapping[`${propertyTitle5} Monthly Repayment`] = 'Monthly Debt Repayment 5';

    nameMapping[`${propertyTitle1} Annual Repayments`] = 'Annual Debt Repayments';
    nameMapping[`${propertyTitle2} Annual Repayments`] = 'Annual Debt Repayments 2';
    nameMapping[`${propertyTitle3} Annual Repayments`] = 'Annual Debt Repayments 3';
    nameMapping[`${propertyTitle4} Annual Repayments`] = 'Annual Debt Repayments 4';
    nameMapping[`${propertyTitle5} Annual Repayments`] = 'Annual Debt Repayments 5';

    // Map property interest and principal names
    nameMapping[`${propertyTitle1} Interest`] = 'Annual Interest Payments';
    nameMapping[`${propertyTitle2} Interest`] = 'Annual Interest Payments 2';
    nameMapping[`${propertyTitle3} Interest`] = 'Annual Interest Payments 3';
    nameMapping[`${propertyTitle4} Interest`] = 'Annual Interest Payments 4';
    nameMapping[`${propertyTitle5} Interest`] = 'Annual Interest Payments 5';

    nameMapping[`${propertyTitle1} Principal`] = 'Annual Principal Repayments';
    nameMapping[`${propertyTitle2} Principal`] = 'Annual Principal Repayments 2';
    nameMapping[`${propertyTitle3} Principal`] = 'Annual Principal Repayments 3';
    nameMapping[`${propertyTitle4} Principal`] = 'Annual Principal Repayments 4';
    nameMapping[`${propertyTitle5} Principal`] = 'Annual Principal Repayments 5';

    // Map property sale and transaction names
    nameMapping[`${propertyTitle1} Sale Proceeds`] = 'Property Sale Proceeds';
    nameMapping[`${propertyTitle2} Sale Proceeds`] = 'Property Sale Proceeds 2';
    nameMapping[`${propertyTitle3} Sale Proceeds`] = 'Property Sale Proceeds 3';
    nameMapping[`${propertyTitle4} Sale Proceeds`] = 'Property Sale Proceeds 4';
    nameMapping[`${propertyTitle5} Sale Proceeds`] = 'Property Sale Proceeds 5';

    nameMapping[`${propertyTitle1} Transaction Costs`] = 'Transaction Costs';
    nameMapping[`${propertyTitle2} Transaction Costs`] = 'Transaction Costs 2';
    nameMapping[`${propertyTitle3} Transaction Costs`] = 'Transaction Costs 3';
    nameMapping[`${propertyTitle4} Transaction Costs`] = 'Transaction Costs 4';
    nameMapping[`${propertyTitle5} Transaction Costs`] = 'Transaction Costs 5';

    nameMapping[`${propertyTitle1} Debt Paid`] = 'Debt Paid';
    nameMapping[`${propertyTitle2} Debt Paid`] = 'Debt Paid 2';
    nameMapping[`${propertyTitle3} Debt Paid`] = 'Debt Paid 3';
    nameMapping[`${propertyTitle4} Debt Paid`] = 'Debt Paid 4';
    nameMapping[`${propertyTitle5} Debt Paid`] = 'Debt Paid 5';

    // Map property purchase and sale names
    nameMapping[`${propertyTitle1} Purchase`] = 'Property Purchase';
    nameMapping[`${propertyTitle2} Purchase`] = 'Property Purchase 2';
    nameMapping[`${propertyTitle3} Purchase`] = 'Property Purchase 3';
    nameMapping[`${propertyTitle4} Purchase`] = 'Property Purchase 4';
    nameMapping[`${propertyTitle5} Purchase`] = 'Property Purchase 5';

    nameMapping[`${propertyTitle1} Sale`] = 'Property Sale';
    nameMapping[`${propertyTitle2} Sale`] = 'Property Sale 2';
    nameMapping[`${propertyTitle3} Sale`] = 'Property Sale 3';
    nameMapping[`${propertyTitle4} Sale`] = 'Property Sale 4';
    nameMapping[`${propertyTitle5} Sale`] = 'Property Sale 5';

    // Map rental income names
    nameMapping[`${propertyTitle1} Rental Income`] = 'Rental Income';
    nameMapping[`${propertyTitle2} Rental Income`] = 'Rental Income 2';
    nameMapping[`${propertyTitle3} Rental Income`] = 'Rental Income 3';
    nameMapping[`${propertyTitle4} Rental Income`] = 'Rental Income 4';
    nameMapping[`${propertyTitle5} Rental Income`] = 'Rental Income 5';

    // Map board income names
    nameMapping[`${propertyTitle1} Board Income`] = 'Board Income';
    nameMapping[`${propertyTitle2} Board Income`] = 'Board Income 2';
    nameMapping[`${propertyTitle3} Board Income`] = 'Board Income 3';
    nameMapping[`${propertyTitle4} Board Income`] = 'Board Income 4';
    nameMapping[`${propertyTitle5} Board Income`] = 'Board Income 5';

    // Map rental and board amount names
    nameMapping[`${propertyTitle1} Rental Amount`] = 'Rental Amount';
    nameMapping[`${propertyTitle2} Rental Amount`] = 'Rental Amount 2';
    nameMapping[`${propertyTitle3} Rental Amount`] = 'Rental Amount 3';
    nameMapping[`${propertyTitle4} Rental Amount`] = 'Rental Amount 4';
    nameMapping[`${propertyTitle5} Rental Amount`] = 'Rental Amount 5';

    nameMapping[`${propertyTitle1} Board Amount`] = 'Board Amount';
    nameMapping[`${propertyTitle2} Board Amount`] = 'Board Amount 2';
    nameMapping[`${propertyTitle3} Board Amount`] = 'Board Amount 3';
    nameMapping[`${propertyTitle4} Board Amount`] = 'Board Amount 4';
    nameMapping[`${propertyTitle5} Board Amount`] = 'Board Amount 5';

    // Map rental and board age names
    nameMapping[`${propertyTitle1} Rental Start Age`] = 'Rental Start Age';
    nameMapping[`${propertyTitle2} Rental Start Age`] = 'Rental Start Age 2';
    nameMapping[`${propertyTitle3} Rental Start Age`] = 'Rental Start Age 3';
    nameMapping[`${propertyTitle4} Rental Start Age`] = 'Rental Start Age 4';
    nameMapping[`${propertyTitle5} Rental Start Age`] = 'Rental Start Age 5';

    nameMapping[`${propertyTitle1} Rental End Age`] = 'Rental End Age';
    nameMapping[`${propertyTitle2} Rental End Age`] = 'Rental End Age 2';
    nameMapping[`${propertyTitle3} Rental End Age`] = 'Rental End Age 3';
    nameMapping[`${propertyTitle4} Rental End Age`] = 'Rental End Age 4';
    nameMapping[`${propertyTitle5} Rental End Age`] = 'Rental End Age 5';

    nameMapping[`${propertyTitle1} Board Start Age`] = 'Board Start Age';
    nameMapping[`${propertyTitle2} Board Start Age`] = 'Board Start Age 2';
    nameMapping[`${propertyTitle3} Board Start Age`] = 'Board Start Age 3';
    nameMapping[`${propertyTitle4} Board Start Age`] = 'Board Start Age 4';
    nameMapping[`${propertyTitle5} Board Start Age`] = 'Board Start Age 5';

    nameMapping[`${propertyTitle1} Board End Age`] = 'Board End Age';
    nameMapping[`${propertyTitle2} Board End Age`] = 'Board End Age 2';
    nameMapping[`${propertyTitle3} Board End Age`] = 'Board End Age 3';
    nameMapping[`${propertyTitle4} Board End Age`] = 'Board End Age 4';
    nameMapping[`${propertyTitle5} Board End Age`] = 'Board End Age 5';

    // Map purchase and sale age names
    nameMapping[`${propertyTitle1} Purchase Age`] = 'Purchase Age';
    nameMapping[`${propertyTitle2} Purchase Age`] = 'Purchase Age 2';
    nameMapping[`${propertyTitle3} Purchase Age`] = 'Purchase Age 3';
    nameMapping[`${propertyTitle4} Purchase Age`] = 'Purchase Age 4';
    nameMapping[`${propertyTitle5} Purchase Age`] = 'Purchase Age 5';

    nameMapping[`${propertyTitle1} Sale Age`] = 'Sale Age';
    nameMapping[`${propertyTitle2} Sale Age`] = 'Sale Age 2';
    nameMapping[`${propertyTitle3} Sale Age`] = 'Sale Age 3';
    nameMapping[`${propertyTitle4} Sale Age`] = 'Sale Age 4';
    nameMapping[`${propertyTitle5} Sale Age`] = 'Sale Age 5';

    // Map dynamic column names to actual data keys
    if (mainName !== 'Main') {
      nameMapping[`${mainName} Income`] = 'Main Income';
      nameMapping[`${mainName} Income Tax`] = 'Main Income Tax';
      nameMapping[`${mainName} KiwiSaver`] = 'Main KiwiSaver';
      nameMapping[`${mainName} Employee KiwiSaver`] = 'Main Employee KiwiSaver';
      nameMapping[`${mainName} Employer KiwiSaver`] = 'Main Employer KiwiSaver';
      nameMapping[`${mainName} KiwiSaver Tax`] = 'Main KiwiSaver Tax';
      nameMapping[`${mainName} KiwiSaver Return`] = 'Main KiwiSaver Return';
    }

    // Map renamed expenses
    nameMapping['Pre-retirement Expenses'] = 'Basic Expenses 1';
    nameMapping['Post-retirement Expenses'] = 'Basic Expenses 2';

    if (partnerName !== 'Partner') {
      nameMapping[`${partnerName} Income`] = 'Partner Income';
      nameMapping[`${partnerName} Income Tax`] = 'Partner Income Tax';
      nameMapping[`${partnerName} KiwiSaver`] = 'Partner KiwiSaver';
      nameMapping[`${partnerName} Employee KiwiSaver`] = 'Partner Employee KiwiSaver';
      nameMapping[`${partnerName} Employer KiwiSaver`] = 'Partner Employer KiwiSaver';
      nameMapping[`${partnerName} KiwiSaver Contributions`] = 'Partner KiwiSaver Contributions';
      nameMapping[`${partnerName} KiwiSaver Tax`] = 'Partner KiwiSaver Tax';
      nameMapping[`${partnerName} KiwiSaver Return`] = 'Partner KiwiSaver Return';
    }

    // Use property titles from above

    // Property Value mappings
    nameMapping[`${propertyTitle1} Value`] = 'Property Value';
    nameMapping[`${propertyTitle2} Value`] = 'Property Value 2';
    nameMapping[`${propertyTitle3} Value`] = 'Property Value 3';
    nameMapping[`${propertyTitle4} Value`] = 'Property Value 4';
    nameMapping[`${propertyTitle5} Value`] = 'Property Value 5';

    // Debt Value mappings
    nameMapping[`${propertyTitle1} Debt`] = 'Debt Value';
    nameMapping[`${propertyTitle2} Debt`] = 'Debt Value 2';
    nameMapping[`${propertyTitle3} Debt`] = 'Debt Value 3';
    nameMapping[`${propertyTitle4} Debt`] = 'Debt Value 4';
    nameMapping[`${propertyTitle5} Debt`] = 'Debt Value 5';

    // Monthly Debt Repayment mappings
    nameMapping[`${propertyTitle1} Monthly Repayment`] = 'Monthly Debt Repayment';
    nameMapping[`${propertyTitle2} Monthly Repayment`] = 'Monthly Debt Repayment 2';
    nameMapping[`${propertyTitle3} Monthly Repayment`] = 'Monthly Debt Repayment 3';
    nameMapping[`${propertyTitle4} Monthly Repayment`] = 'Monthly Debt Repayment 4';
    nameMapping[`${propertyTitle5} Monthly Repayment`] = 'Monthly Debt Repayment 5';

    // Annual Debt Repayments mappings
    nameMapping[`${propertyTitle1} Annual Repayments`] = 'Annual Debt Repayments';
    nameMapping[`${propertyTitle2} Annual Repayments`] = 'Annual Debt Repayments 2';
    nameMapping[`${propertyTitle3} Annual Repayments`] = 'Annual Debt Repayments 3';
    nameMapping[`${propertyTitle4} Annual Repayments`] = 'Annual Debt Repayments 4';
    nameMapping[`${propertyTitle5} Annual Repayments`] = 'Annual Debt Repayments 5';

    // Annual Interest Payments mappings
    nameMapping[`${propertyTitle1} Interest`] = 'Annual Interest Payments';
    nameMapping[`${propertyTitle2} Interest`] = 'Annual Interest Payments 2';
    nameMapping[`${propertyTitle3} Interest`] = 'Annual Interest Payments 3';
    nameMapping[`${propertyTitle4} Interest`] = 'Annual Interest Payments 4';
    nameMapping[`${propertyTitle5} Interest`] = 'Annual Interest Payments 5';

    // Annual Principal Repayments mappings
    nameMapping[`${propertyTitle1} Principal`] = 'Annual Principal Repayments';
    nameMapping[`${propertyTitle2} Principal`] = 'Annual Principal Repayments 2';
    nameMapping[`${propertyTitle3} Principal`] = 'Annual Principal Repayments 3';
    nameMapping[`${propertyTitle4} Principal`] = 'Annual Principal Repayments 4';
    nameMapping[`${propertyTitle5} Principal`] = 'Annual Principal Repayments 5';

    // Property Sale Proceeds mappings
    nameMapping[`${propertyTitle1} Sale Proceeds`] = 'Property Sale Proceeds';
    nameMapping[`${propertyTitle2} Sale Proceeds`] = 'Property Sale Proceeds 2';
    nameMapping[`${propertyTitle3} Sale Proceeds`] = 'Property Sale Proceeds 3';
    nameMapping[`${propertyTitle4} Sale Proceeds`] = 'Property Sale Proceeds 4';
    nameMapping[`${propertyTitle5} Sale Proceeds`] = 'Property Sale Proceeds 5';

    // Transaction Costs mappings
    nameMapping[`${propertyTitle1} Transaction Costs`] = 'Transaction Costs';
    nameMapping[`${propertyTitle2} Transaction Costs`] = 'Transaction Costs 2';
    nameMapping[`${propertyTitle3} Transaction Costs`] = 'Transaction Costs 3';
    nameMapping[`${propertyTitle4} Transaction Costs`] = 'Transaction Costs 4';
    nameMapping[`${propertyTitle5} Transaction Costs`] = 'Transaction Costs 5';

    // Debt Paid mappings
    nameMapping[`${propertyTitle1} Debt Paid`] = 'Debt Paid';
    nameMapping[`${propertyTitle2} Debt Paid`] = 'Debt Paid 2';
    nameMapping[`${propertyTitle3} Debt Paid`] = 'Debt Paid 3';
    nameMapping[`${propertyTitle4} Debt Paid`] = 'Debt Paid 4';
    nameMapping[`${propertyTitle5} Debt Paid`] = 'Debt Paid 5';

    const tabColumns: {
      incomeExpenses: string[];
      investmentsKiwiSaver: string[];
      property: () => string[];
      taxation: string[];
      all: string[];
    } = {
      incomeExpenses: [
        'Age',
        'Savings Fund',
        'Gross Income',
        'Net Income',
        'Total Expenditure',
        'Additional Expenditure',
        'Pre-retirement Expenses',
        'Post-retirement Expenses',
        'Net Cashflow',
        `${mainName} Income Tax`,
        `${partnerName} Income Tax`,
        `${mainName} Income`,
        `${partnerName} Income`
      ],
      investmentsKiwiSaver: [
        'Age',
        'Investments Fund',
        // Use dynamic fund titles - only include active funds
        ...(isActiveFund1 ? [fundDesc1] : []),
        ...(isActiveFund2 ? [fundDesc2] : []),
        ...(isActiveFund3 ? [fundDesc3] : []),
        ...(isActiveFund4 ? [fundDesc4] : []),
        ...(isActiveFund5 ? [fundDesc5] : []),
        'Total KiwiSaver',
        `${mainName} KiwiSaver`,
        `${partnerName} KiwiSaver`,
        `${mainName} Employee KiwiSaver`,
        `${mainName} Employer KiwiSaver`,
        `${partnerName} Employee KiwiSaver`,
        `${partnerName} Employer KiwiSaver`,
        'Annual Investment Return',
        // Use dynamic fund titles for return columns - only include active funds
        ...(isActiveFund1 ? [`${fundDesc1} Return`] : []),
        ...(isActiveFund2 ? [`${fundDesc2} Return`] : []),
        ...(isActiveFund3 ? [`${fundDesc3} Return`] : []),
        ...(isActiveFund4 ? [`${fundDesc4} Return`] : []),
        ...(isActiveFund5 ? [`${fundDesc5} Return`] : []),
        'Annual KiwiSaver Return',
        `${mainName} KiwiSaver Return`,
        `${partnerName} KiwiSaver Return`,
        'Minimum Investment Fund',
        'Maximum Investment Fund',
        'Annual Investment Contribution',
        // Use dynamic fund titles for contribution columns - only include active funds
        ...(isActiveFund1 ? [`${fundDesc1} Contribution`] : []),
        ...(isActiveFund2 ? [`${fundDesc2} Contribution`] : []),
        ...(isActiveFund3 ? [`${fundDesc3} Contribution`] : []),
        ...(isActiveFund4 ? [`${fundDesc4} Contribution`] : []),
        ...(isActiveFund5 ? [`${fundDesc5} Contribution`] : []),
        'KiwiSaver Contributions',
        `${partnerName} KiwiSaver Contributions`,
        'Superannuation'
      ],
      property: () => {
        // Start with Age column
        const columns = ['Age'];

        // Property 1 always exists
        const propertyTitle1 = inputData?.property_title || 'Property 1';
        columns.push(
          `${propertyTitle1} Value`,
          `${propertyTitle1} Debt`,
          `${propertyTitle1} Monthly Repayment`,
          `${propertyTitle1} Annual Repayments`,
          `${propertyTitle1} Interest`,
          `${propertyTitle1} Principal`,
          `${propertyTitle1} Sale Proceeds`,
          `${propertyTitle1} Transaction Costs`,
          `${propertyTitle1} Debt Paid`
        );

        // Only add Property 2 if it exists
        if (inputData?.property_value2) {
          const propertyTitle2 = inputData?.property_title2 || 'Property 2';
          columns.push(
            `${propertyTitle2} Value`,
            `${propertyTitle2} Debt`,
            `${propertyTitle2} Monthly Repayment`,
            `${propertyTitle2} Annual Repayments`,
            `${propertyTitle2} Interest`,
            `${propertyTitle2} Principal`,
            `${propertyTitle2} Sale Proceeds`,
            `${propertyTitle2} Transaction Costs`,
            `${propertyTitle2} Debt Paid`
          );
        }

        // Only add Property 3 if it exists
        if (inputData?.property_value3) {
          const propertyTitle3 = inputData?.property_title3 || 'Property 3';
          columns.push(
            `${propertyTitle3} Value`,
            `${propertyTitle3} Debt`,
            `${propertyTitle3} Monthly Repayment`,
            `${propertyTitle3} Annual Repayments`,
            `${propertyTitle3} Interest`,
            `${propertyTitle3} Principal`,
            `${propertyTitle3} Sale Proceeds`,
            `${propertyTitle3} Transaction Costs`,
            `${propertyTitle3} Debt Paid`
          );
        }

        // Only add Property 4 if it exists
        if (inputData?.property_value4) {
          const propertyTitle4 = inputData?.property_title4 || 'Property 4';
          columns.push(
            `${propertyTitle4} Value`,
            `${propertyTitle4} Debt`,
            `${propertyTitle4} Monthly Repayment`,
            `${propertyTitle4} Annual Repayments`,
            `${propertyTitle4} Interest`,
            `${propertyTitle4} Principal`,
            `${propertyTitle4} Sale Proceeds`,
            `${propertyTitle4} Transaction Costs`,
            `${propertyTitle4} Debt Paid`
          );
        }

        // Only add Property 5 if it exists
        if (inputData?.property_value5) {
          const propertyTitle5 = inputData?.property_title5 || 'Property 5';
          columns.push(
            `${propertyTitle5} Value`,
            `${propertyTitle5} Debt`,
            `${propertyTitle5} Monthly Repayment`,
            `${propertyTitle5} Annual Repayments`,
            `${propertyTitle5} Interest`,
            `${propertyTitle5} Principal`,
            `${propertyTitle5} Sale Proceeds`,
            `${propertyTitle5} Transaction Costs`,
            `${propertyTitle5} Debt Paid`
          );
        }

        // Add income columns at the end - only for active properties with specific titles
        // Add rental income for each active property
        if (inputData?.rental_income) {
          columns.push(`${propertyTitle1} Rental Income`);
        }

        if (inputData?.property_value2 && inputData?.rental_income2) {
          columns.push(`${propertyTitle2} Rental Income`);
        }

        if (inputData?.property_value3 && inputData?.rental_income3) {
          columns.push(`${propertyTitle3} Rental Income`);
        }

        if (inputData?.property_value4 && inputData?.rental_income4) {
          columns.push(`${propertyTitle4} Rental Income`);
        }

        if (inputData?.property_value5 && inputData?.rental_income5) {
          columns.push(`${propertyTitle5} Rental Income`);
        }

        // Add board income for each active property
        if (inputData?.board_income) {
          columns.push(`${propertyTitle1} Board Income`);
        }

        if (inputData?.property_value2 && inputData?.board_income2) {
          columns.push(`${propertyTitle2} Board Income`);
        }

        if (inputData?.property_value3 && inputData?.board_income3) {
          columns.push(`${propertyTitle3} Board Income`);
        }

        if (inputData?.property_value4 && inputData?.board_income4) {
          columns.push(`${propertyTitle4} Board Income`);
        }

        if (inputData?.property_value5 && inputData?.board_income5) {
          columns.push(`${propertyTitle5} Board Income`);
        }

        return columns;
      },
      taxation: [
        'Age',
        `${mainName} Income Tax`,
        `${partnerName} Income Tax`,
        'MTR Investment Tax',
        'PIE Investment tax',
        // Use dynamic fund titles for tax columns - only include active funds
        ...(isActiveFund1 ? [`${fundDesc1} Tax`] : []),
        ...(isActiveFund2 ? [`${fundDesc2} Tax`] : []),
        ...(isActiveFund3 ? [`${fundDesc3} Tax`] : []),
        ...(isActiveFund4 ? [`${fundDesc4} Tax`] : []),
        ...(isActiveFund5 ? [`${fundDesc5} Tax`] : []),
        'KiwiSaver Tax',
        `${mainName} KiwiSaver Tax`,
        `${partnerName} KiwiSaver Tax`,
        'Income Tax',
        // Use dynamic fund titles for income portion columns - only include active funds
        ...(isActiveFund1 ? [`${fundDesc1} Income Portion`] : []),
        ...(isActiveFund2 ? [`${fundDesc2} Income Portion`] : []),
        ...(isActiveFund3 ? [`${fundDesc3} Income Portion`] : []),
        ...(isActiveFund4 ? [`${fundDesc4} Income Portion`] : []),
        ...(isActiveFund5 ? [`${fundDesc5} Income Portion`] : []),
        'KiwiSaver Income Portion',
        'Partner KiwiSaver Income Portion'
      ],
      all: (() => {
        // Get all columns from the metrics
        let allColumns = Object.keys(allMetrics[0] || {});

        // Filter out inactive investment funds and properties
        allColumns = allColumns.filter(col => {
          // Keep all non-investment and non-property columns
          if (!col.includes('Investment Fund') &&
              !col.includes('Annual Investment Return') &&
              !col.includes('Annual Investment Contribution') &&
              !col.includes('Fund ') &&
              !col.includes('Property Value') &&
              !col.includes('Debt Value') &&
              !col.includes('Monthly Debt Repayment') &&
              !col.includes('Annual Debt Repayments') &&
              !col.includes('Annual Interest Payments') &&
              !col.includes('Annual Principal Repayments') &&
              !col.includes('Property Sale Proceeds') &&
              !col.includes('Transaction Costs') &&
              !col.includes('Debt Paid') &&
              !col.includes('Rental Income') &&
              !col.includes('Board Income') &&
              !col.includes('Rental Amount') &&
              !col.includes('Board Amount') &&
              !col.includes('Rental Start Age') &&
              !col.includes('Rental End Age') &&
              !col.includes('Board Start Age') &&
              !col.includes('Board End Age') &&
              !col.includes('Repayment') &&
              !col.includes('Interest Only') &&
              !col.includes('Lump Sum Payment') &&
              !col.includes('Deposit') &&
              !col.includes('Purchase Age') &&
              !col.includes('Sale Age') &&
              !col.includes('Property Purchase') &&
              !col.includes('Property Sale')) {
            return true;
          }

          // Filter investment fund columns
          if (col === 'Investment Fund 1' || col.includes('Annual Investment Return 1') ||
              col.includes('Annual Investment Contribution 1') || col.includes('Fund 1 ')) {
            return isActiveFund1;
          }
          if (col === 'Investment Fund 2' || col.includes('Annual Investment Return 2') ||
              col.includes('Annual Investment Contribution 2') || col.includes('Fund 2 ')) {
            return isActiveFund2;
          }
          if (col === 'Investment Fund 3' || col.includes('Annual Investment Return 3') ||
              col.includes('Annual Investment Contribution 3') || col.includes('Fund 3 ')) {
            return isActiveFund3;
          }
          if (col === 'Investment Fund 4' || col.includes('Annual Investment Return 4') ||
              col.includes('Annual Investment Contribution 4') || col.includes('Fund 4 ')) {
            return isActiveFund4;
          }
          if (col === 'Investment Fund 5' || col.includes('Annual Investment Return 5') ||
              col.includes('Annual Investment Contribution 5') || col.includes('Fund 5 ')) {
            return isActiveFund5;
          }

          // Filter property columns
          // Property 1 is always active
          if ((col === 'Property Value' || col === 'Debt Value' ||
              col === 'Monthly Debt Repayment' || col === 'Annual Debt Repayments' ||
              col === 'Annual Interest Payments' || col === 'Annual Principal Repayments' ||
              col === 'Property Sale Proceeds' || col === 'Transaction Costs' ||
              col === 'Debt Paid' || col === 'Rental Income' || col === 'Board Income' ||
              col === 'Rental Amount' || col === 'Board Amount' ||
              col === 'Rental Start Age' || col === 'Rental End Age' ||
              col === 'Board Start Age' || col === 'Board End Age' ||
              col === 'Property Purchase' || col === 'Property Sale' ||
              col.includes('Repayment') || col.includes('Interest Only') ||
              col.includes('Lump Sum Payment') || col.includes('Deposit') ||
              col.includes('Purchase Age') || col.includes('Sale Age')) &&
              !col.includes('2') && !col.includes('3') && !col.includes('4') && !col.includes('5')) {
            return true; // Property 1 is always active
          }

          // Property 2
          if (col.includes('2') || col.endsWith(' 2')) {
            return !!inputData?.property_value2;
          }

          // Property 3
          if (col.includes('3') || col.endsWith(' 3')) {
            return !!inputData?.property_value3;
          }

          // Property 4
          if (col.includes('4') || col.endsWith(' 4')) {
            return !!inputData?.property_value4;
          }

          // Property 5
          if (col.includes('5') || col.endsWith(' 5')) {
            return !!inputData?.property_value5;
          }

          return true;
        });

        // Create a mapping for columns that need to be renamed
        const columnRenameMap: Record<string, string> = {
          // Investment fund names - only include active funds
          ...(isActiveFund1 ? { 'Investment Fund 1': fundDesc1 } : {}),
          ...(isActiveFund2 ? { 'Investment Fund 2': fundDesc2 } : {}),
          ...(isActiveFund3 ? { 'Investment Fund 3': fundDesc3 } : {}),
          ...(isActiveFund4 ? { 'Investment Fund 4': fundDesc4 } : {}),
          ...(isActiveFund5 ? { 'Investment Fund 5': fundDesc5 } : {}),

          // Investment fund returns - only include active funds
          ...(isActiveFund1 ? { 'Annual Investment Return 1': `${fundDesc1} Return` } : {}),
          ...(isActiveFund2 ? { 'Annual Investment Return 2': `${fundDesc2} Return` } : {}),
          ...(isActiveFund3 ? { 'Annual Investment Return 3': `${fundDesc3} Return` } : {}),
          ...(isActiveFund4 ? { 'Annual Investment Return 4': `${fundDesc4} Return` } : {}),
          ...(isActiveFund5 ? { 'Annual Investment Return 5': `${fundDesc5} Return` } : {}),

          // Investment fund contributions - only include active funds
          ...(isActiveFund1 ? { 'Annual Investment Contribution 1': `${fundDesc1} Contribution` } : {}),
          ...(isActiveFund2 ? { 'Annual Investment Contribution 2': `${fundDesc2} Contribution` } : {}),
          ...(isActiveFund3 ? { 'Annual Investment Contribution 3': `${fundDesc3} Contribution` } : {}),
          ...(isActiveFund4 ? { 'Annual Investment Contribution 4': `${fundDesc4} Contribution` } : {}),
          ...(isActiveFund5 ? { 'Annual Investment Contribution 5': `${fundDesc5} Contribution` } : {}),

          // Investment fund taxes - only include active funds
          ...(isActiveFund1 ? { 'Fund 1 Tax': `${fundDesc1} Tax` } : {}),
          ...(isActiveFund2 ? { 'Fund 2 Tax': `${fundDesc2} Tax` } : {}),
          ...(isActiveFund3 ? { 'Fund 3 Tax': `${fundDesc3} Tax` } : {}),
          ...(isActiveFund4 ? { 'Fund 4 Tax': `${fundDesc4} Tax` } : {}),
          ...(isActiveFund5 ? { 'Fund 5 Tax': `${fundDesc5} Tax` } : {}),

          // Investment fund income portions - only include active funds
          ...(isActiveFund1 ? { 'Fund 1 Income Portion': `${fundDesc1} Income Portion` } : {}),
          ...(isActiveFund2 ? { 'Fund 2 Income Portion': `${fundDesc2} Income Portion` } : {}),
          ...(isActiveFund3 ? { 'Fund 3 Income Portion': `${fundDesc3} Income Portion` } : {}),
          ...(isActiveFund4 ? { 'Fund 4 Income Portion': `${fundDesc4} Income Portion` } : {}),
          ...(isActiveFund5 ? { 'Fund 5 Income Portion': `${fundDesc5} Income Portion` } : {}),

          // KiwiSaver names
          'Main KiwiSaver': `${mainName} KiwiSaver`,
          'Partner KiwiSaver': `${partnerName} KiwiSaver`,
          'Main KiwiSaver Tax': `${mainName} KiwiSaver Tax`,
          'Partner KiwiSaver Tax': `${partnerName} KiwiSaver Tax`,
          'Main KiwiSaver Return': `${mainName} KiwiSaver Return`,
          'Partner KiwiSaver Return': `${partnerName} KiwiSaver Return`,
          'Main Employee KiwiSaver': `${mainName} Employee KiwiSaver`,
          'Main Employer KiwiSaver': `${mainName} Employer KiwiSaver`,
          'Partner Employee KiwiSaver': `${partnerName} Employee KiwiSaver`,
          'Partner Employer KiwiSaver': `${partnerName} Employer KiwiSaver`,
          'Partner KiwiSaver Contributions': `${partnerName} KiwiSaver Contributions`,
          'Partner KiwiSaver Income Portion': `${partnerName} KiwiSaver Income Portion`,

          // Income and tax names
          'Main Income': `${mainName} Income`,
          'Partner Income': `${partnerName} Income`,
          'Main Income Tax': `${mainName} Income Tax`,
          'Partner Income Tax': `${partnerName} Income Tax`,

          // Rename basic expenses to pre/post retirement expenses
          'Basic Expenses 1': 'Pre-retirement Expenses',
          'Basic Expenses 2': 'Post-retirement Expenses',

          // Property names - only include active properties
          'Property Value': `${propertyTitle1} Value`,
          ...(inputData?.property_value2 ? { 'Property Value 2': `${propertyTitle2} Value` } : {}),
          ...(inputData?.property_value3 ? { 'Property Value 3': `${propertyTitle3} Value` } : {}),
          ...(inputData?.property_value4 ? { 'Property Value 4': `${propertyTitle4} Value` } : {}),
          ...(inputData?.property_value5 ? { 'Property Value 5': `${propertyTitle5} Value` } : {}),

          'Debt Value': `${propertyTitle1} Debt`,
          ...(inputData?.property_value2 ? { 'Debt Value 2': `${propertyTitle2} Debt` } : {}),
          ...(inputData?.property_value3 ? { 'Debt Value 3': `${propertyTitle3} Debt` } : {}),
          ...(inputData?.property_value4 ? { 'Debt Value 4': `${propertyTitle4} Debt` } : {}),
          ...(inputData?.property_value5 ? { 'Debt Value 5': `${propertyTitle5} Debt` } : {}),

          'Monthly Debt Repayment': `${propertyTitle1} Monthly Repayment`,
          ...(inputData?.property_value2 ? { 'Monthly Debt Repayment 2': `${propertyTitle2} Monthly Repayment` } : {}),
          ...(inputData?.property_value3 ? { 'Monthly Debt Repayment 3': `${propertyTitle3} Monthly Repayment` } : {}),
          ...(inputData?.property_value4 ? { 'Monthly Debt Repayment 4': `${propertyTitle4} Monthly Repayment` } : {}),
          ...(inputData?.property_value5 ? { 'Monthly Debt Repayment 5': `${propertyTitle5} Monthly Repayment` } : {}),

          'Annual Debt Repayments': `${propertyTitle1} Annual Repayments`,
          ...(inputData?.property_value2 ? { 'Annual Debt Repayments 2': `${propertyTitle2} Annual Repayments` } : {}),
          ...(inputData?.property_value3 ? { 'Annual Debt Repayments 3': `${propertyTitle3} Annual Repayments` } : {}),
          ...(inputData?.property_value4 ? { 'Annual Debt Repayments 4': `${propertyTitle4} Annual Repayments` } : {}),
          ...(inputData?.property_value5 ? { 'Annual Debt Repayments 5': `${propertyTitle5} Annual Repayments` } : {}),

          'Annual Interest Payments': `${propertyTitle1} Interest`,
          ...(inputData?.property_value2 ? { 'Annual Interest Payments 2': `${propertyTitle2} Interest` } : {}),
          ...(inputData?.property_value3 ? { 'Annual Interest Payments 3': `${propertyTitle3} Interest` } : {}),
          ...(inputData?.property_value4 ? { 'Annual Interest Payments 4': `${propertyTitle4} Interest` } : {}),
          ...(inputData?.property_value5 ? { 'Annual Interest Payments 5': `${propertyTitle5} Interest` } : {}),

          'Annual Principal Repayments': `${propertyTitle1} Principal`,
          ...(inputData?.property_value2 ? { 'Annual Principal Repayments 2': `${propertyTitle2} Principal` } : {}),
          ...(inputData?.property_value3 ? { 'Annual Principal Repayments 3': `${propertyTitle3} Principal` } : {}),
          ...(inputData?.property_value4 ? { 'Annual Principal Repayments 4': `${propertyTitle4} Principal` } : {}),
          ...(inputData?.property_value5 ? { 'Annual Principal Repayments 5': `${propertyTitle5} Principal` } : {}),

          'Property Sale Proceeds': `${propertyTitle1} Sale Proceeds`,
          ...(inputData?.property_value2 ? { 'Property Sale Proceeds 2': `${propertyTitle2} Sale Proceeds` } : {}),
          ...(inputData?.property_value3 ? { 'Property Sale Proceeds 3': `${propertyTitle3} Sale Proceeds` } : {}),
          ...(inputData?.property_value4 ? { 'Property Sale Proceeds 4': `${propertyTitle4} Sale Proceeds` } : {}),
          ...(inputData?.property_value5 ? { 'Property Sale Proceeds 5': `${propertyTitle5} Sale Proceeds` } : {}),

          'Transaction Costs': `${propertyTitle1} Transaction Costs`,
          ...(inputData?.property_value2 ? { 'Transaction Costs 2': `${propertyTitle2} Transaction Costs` } : {}),
          ...(inputData?.property_value3 ? { 'Transaction Costs 3': `${propertyTitle3} Transaction Costs` } : {}),
          ...(inputData?.property_value4 ? { 'Transaction Costs 4': `${propertyTitle4} Transaction Costs` } : {}),
          ...(inputData?.property_value5 ? { 'Transaction Costs 5': `${propertyTitle5} Transaction Costs` } : {}),

          'Debt Paid': `${propertyTitle1} Debt Paid`,
          ...(inputData?.property_value2 ? { 'Debt Paid 2': `${propertyTitle2} Debt Paid` } : {}),
          ...(inputData?.property_value3 ? { 'Debt Paid 3': `${propertyTitle3} Debt Paid` } : {}),
          ...(inputData?.property_value4 ? { 'Debt Paid 4': `${propertyTitle4} Debt Paid` } : {}),
          ...(inputData?.property_value5 ? { 'Debt Paid 5': `${propertyTitle5} Debt Paid` } : {}),

          // Property purchase and sale
          'Property Purchase': `${propertyTitle1} Purchase`,
          ...(inputData?.property_value2 ? { 'Property Purchase 2': `${propertyTitle2} Purchase` } : {}),
          ...(inputData?.property_value3 ? { 'Property Purchase 3': `${propertyTitle3} Purchase` } : {}),
          ...(inputData?.property_value4 ? { 'Property Purchase 4': `${propertyTitle4} Purchase` } : {}),
          ...(inputData?.property_value5 ? { 'Property Purchase 5': `${propertyTitle5} Purchase` } : {}),

          'Property Sale': `${propertyTitle1} Sale`,
          ...(inputData?.property_value2 ? { 'Property Sale 2': `${propertyTitle2} Sale` } : {}),
          ...(inputData?.property_value3 ? { 'Property Sale 3': `${propertyTitle3} Sale` } : {}),
          ...(inputData?.property_value4 ? { 'Property Sale 4': `${propertyTitle4} Sale` } : {}),
          ...(inputData?.property_value5 ? { 'Property Sale 5': `${propertyTitle5} Sale` } : {}),

          // Rental income
          'Rental Income': `${propertyTitle1} Rental Income`,
          ...(inputData?.property_value2 && inputData?.rental_income2 ? { 'Rental Income 2': `${propertyTitle2} Rental Income` } : {}),
          ...(inputData?.property_value3 && inputData?.rental_income3 ? { 'Rental Income 3': `${propertyTitle3} Rental Income` } : {}),
          ...(inputData?.property_value4 && inputData?.rental_income4 ? { 'Rental Income 4': `${propertyTitle4} Rental Income` } : {}),
          ...(inputData?.property_value5 && inputData?.rental_income5 ? { 'Rental Income 5': `${propertyTitle5} Rental Income` } : {}),

          // Board income
          'Board Income': `${propertyTitle1} Board Income`,
          ...(inputData?.property_value2 && inputData?.board_income2 ? { 'Board Income 2': `${propertyTitle2} Board Income` } : {}),
          ...(inputData?.property_value3 && inputData?.board_income3 ? { 'Board Income 3': `${propertyTitle3} Board Income` } : {}),
          ...(inputData?.property_value4 && inputData?.board_income4 ? { 'Board Income 4': `${propertyTitle4} Board Income` } : {}),
          ...(inputData?.property_value5 && inputData?.board_income5 ? { 'Board Income 5': `${propertyTitle5} Board Income` } : {}),

          // Rental amount
          'Rental Amount': `${propertyTitle1} Rental Amount`,
          ...(inputData?.property_value2 && inputData?.rental_income2 ? { 'Rental Amount 2': `${propertyTitle2} Rental Amount` } : {}),
          ...(inputData?.property_value3 && inputData?.rental_income3 ? { 'Rental Amount 3': `${propertyTitle3} Rental Amount` } : {}),
          ...(inputData?.property_value4 && inputData?.rental_income4 ? { 'Rental Amount 4': `${propertyTitle4} Rental Amount` } : {}),
          ...(inputData?.property_value5 && inputData?.rental_income5 ? { 'Rental Amount 5': `${propertyTitle5} Rental Amount` } : {}),

          // Board amount
          'Board Amount': `${propertyTitle1} Board Amount`,
          ...(inputData?.property_value2 && inputData?.board_income2 ? { 'Board Amount 2': `${propertyTitle2} Board Amount` } : {}),
          ...(inputData?.property_value3 && inputData?.board_income3 ? { 'Board Amount 3': `${propertyTitle3} Board Amount` } : {}),
          ...(inputData?.property_value4 && inputData?.board_income4 ? { 'Board Amount 4': `${propertyTitle4} Board Amount` } : {}),
          ...(inputData?.property_value5 && inputData?.board_income5 ? { 'Board Amount 5': `${propertyTitle5} Board Amount` } : {}),

          // Rental start age
          'Rental Start Age': `${propertyTitle1} Rental Start Age`,
          ...(inputData?.property_value2 && inputData?.rental_income2 ? { 'Rental Start Age 2': `${propertyTitle2} Rental Start Age` } : {}),
          ...(inputData?.property_value3 && inputData?.rental_income3 ? { 'Rental Start Age 3': `${propertyTitle3} Rental Start Age` } : {}),
          ...(inputData?.property_value4 && inputData?.rental_income4 ? { 'Rental Start Age 4': `${propertyTitle4} Rental Start Age` } : {}),
          ...(inputData?.property_value5 && inputData?.rental_income5 ? { 'Rental Start Age 5': `${propertyTitle5} Rental Start Age` } : {}),

          // Rental end age
          'Rental End Age': `${propertyTitle1} Rental End Age`,
          ...(inputData?.property_value2 && inputData?.rental_income2 ? { 'Rental End Age 2': `${propertyTitle2} Rental End Age` } : {}),
          ...(inputData?.property_value3 && inputData?.rental_income3 ? { 'Rental End Age 3': `${propertyTitle3} Rental End Age` } : {}),
          ...(inputData?.property_value4 && inputData?.rental_income4 ? { 'Rental End Age 4': `${propertyTitle4} Rental End Age` } : {}),
          ...(inputData?.property_value5 && inputData?.rental_income5 ? { 'Rental End Age 5': `${propertyTitle5} Rental End Age` } : {}),

          // Board start age
          'Board Start Age': `${propertyTitle1} Board Start Age`,
          ...(inputData?.property_value2 && inputData?.board_income2 ? { 'Board Start Age 2': `${propertyTitle2} Board Start Age` } : {}),
          ...(inputData?.property_value3 && inputData?.board_income3 ? { 'Board Start Age 3': `${propertyTitle3} Board Start Age` } : {}),
          ...(inputData?.property_value4 && inputData?.board_income4 ? { 'Board Start Age 4': `${propertyTitle4} Board Start Age` } : {}),
          ...(inputData?.property_value5 && inputData?.board_income5 ? { 'Board Start Age 5': `${propertyTitle5} Board Start Age` } : {}),

          // Board end age
          'Board End Age': `${propertyTitle1} Board End Age`,
          ...(inputData?.property_value2 && inputData?.board_income2 ? { 'Board End Age 2': `${propertyTitle2} Board End Age` } : {}),
          ...(inputData?.property_value3 && inputData?.board_income3 ? { 'Board End Age 3': `${propertyTitle3} Board End Age` } : {}),
          ...(inputData?.property_value4 && inputData?.board_income4 ? { 'Board End Age 4': `${propertyTitle4} Board End Age` } : {}),
          ...(inputData?.property_value5 && inputData?.board_income5 ? { 'Board End Age 5': `${propertyTitle5} Board End Age` } : {}),

          // Purchase age
          'Purchase Age': `${propertyTitle1} Purchase Age`,
          ...(inputData?.property_value2 ? { 'Purchase Age 2': `${propertyTitle2} Purchase Age` } : {}),
          ...(inputData?.property_value3 ? { 'Purchase Age 3': `${propertyTitle3} Purchase Age` } : {}),
          ...(inputData?.property_value4 ? { 'Purchase Age 4': `${propertyTitle4} Purchase Age` } : {}),
          ...(inputData?.property_value5 ? { 'Purchase Age 5': `${propertyTitle5} Purchase Age` } : {}),

          // Sale age
          'Sale Age': `${propertyTitle1} Sale Age`,
          ...(inputData?.property_value2 ? { 'Sale Age 2': `${propertyTitle2} Sale Age` } : {}),
          ...(inputData?.property_value3 ? { 'Sale Age 3': `${propertyTitle3} Sale Age` } : {}),
          ...(inputData?.property_value4 ? { 'Sale Age 4': `${propertyTitle4} Sale Age` } : {}),
          ...(inputData?.property_value5 ? { 'Sale Age 5': `${propertyTitle5} Sale Age` } : {})
        };

        // Map the columns to their dynamic names if needed
        return allColumns.map(col => columnRenameMap[col] || col);
      })()
    };

    // Get the columns for the active tab
    const activeTabColumns = activeTab === 'property'
      ? tabColumns.property()
      : tabColumns[activeTab as keyof typeof tabColumns] as string[];

    return allMetrics.map(metric => {
      const filteredMetric: { [key: string]: any } = {};
      activeTabColumns.forEach((key: string | number) => {
        // If we have a mapping for this key, use the original data key
        const dataKey = nameMapping[key] || key;
        // Store the value under the display key (with dynamic name)
        filteredMetric[key] = metric[dataKey];
      });
      return filteredMetric;
    });
  };

  const filteredMetrics = getFilteredMetrics();

  const renderCellValue = (key: string, value: any) => {
    // Handle undefined or null values
    if (value === undefined || value === null) {
      if (key.includes('Tax') || key.includes('Return')) {
        return formatter.format(0);
      }
      if (key.toLowerCase().includes('income portion')) {
        return percentFormatter.format(0);
      }
      return '0';
    }

    // Handle array values (like KiwiSaver tax arrays)
    if (Array.isArray(value)) {
      // For KiwiSaver tax arrays, make sure we're getting the last value
      if (key.includes('KiwiSaver Tax')) {
        // If the array is empty, return 0
        if (value.length === 0) {
          return formatter.format(0);
        }
        // Get the last value from the array
        value = value[value.length - 1];
      } else {
        value = value[value.length - 1];
      }
    }

    if (typeof value === 'number' && key !== 'Age') {
      if (isCurrencyMetric(key)) {
        return formatter.format(value);
      }

      // Check if the key suggests a percentage
      if (key.toLowerCase().includes('rate') ||
          key.toLowerCase().includes('income portion')) {
        return percentFormatter.format(value / 100);
      }

      // Get the investment fund descriptions for dynamic tax and return handling
      const fundDesc1 = inputData?.investment_description1 || 'Investment Fund 1';
      const fundDesc2 = inputData?.investment_description2 || 'Investment Fund 2';
      const fundDesc3 = inputData?.investment_description3 || 'Investment Fund 3';
      const fundDesc4 = inputData?.investment_description4 || 'Investment Fund 4';
      const fundDesc5 = inputData?.investment_description5 || 'Investment Fund 5';

      // Special handling for return values that should be currency but not percentages
      if ((key.toLowerCase().includes('return') &&
          (key.includes('Annual Investment Return 1') ||
           key.includes('Annual Investment Return 2') ||
           key.includes('Annual Investment Return 3') ||
           key.includes('Annual Investment Return 4') ||
           key.includes('Annual Investment Return 5') ||
           key === `${fundDesc1} Return` ||
           key === `${fundDesc2} Return` ||
           key === `${fundDesc3} Return` ||
           key === `${fundDesc4} Return` ||
           key === `${fundDesc5} Return` ||
           key.includes(`${mainName} KiwiSaver Return`) ||
           key.includes(`${partnerName} KiwiSaver Return`))) ||
          // Handle contribution columns
          key === `${fundDesc1} Contribution` ||
          key === `${fundDesc2} Contribution` ||
          key === `${fundDesc3} Contribution` ||
          key === `${fundDesc4} Contribution` ||
          key === `${fundDesc5} Contribution` ||
          // Handle both static and dynamic fund tax names
          key.includes('Fund 1 Tax') || key.includes(`${fundDesc1} Tax`) ||
          key.includes('Fund 2 Tax') || key.includes(`${fundDesc2} Tax`) ||
          key.includes('Fund 3 Tax') || key.includes(`${fundDesc3} Tax`) ||
          key.includes('Fund 4 Tax') || key.includes(`${fundDesc4} Tax`) ||
          key.includes('Fund 5 Tax') || key.includes(`${fundDesc5} Tax`) ||
          key.includes('KiwiSaver Tax')) {
        return formatter.format(value);
      }

      // For other return values, show as percentages
      if (key.toLowerCase().includes('return')) {
        return percentFormatter.format(value / 100);
      }

      return value.toLocaleString(undefined, {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    }
    return String(value);
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(filteredMetrics);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Financial Data');
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(data, 'financial_data.xlsx');
  };

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 bg-gray-100">
          {[
            { value: "incomeExpenses", label: "Income/Expenses" },
            { value: "investmentsKiwiSaver", label: "Investments/KiwiSaver" },
            { value: "property", label: "Property" },
            { value: "taxation", label: "Taxation" },
            { value: "all", label: "All" }
          ].map(tab => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="data-[state=active]:bg-blue-100 data-[state=active]:text-blue-800 hover:bg-gray-200 transition-colors"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        <TabsContent value={activeTab} className="p-2">
          <div className="border rounded-md">
            <Table>
              <TableHeader className="bg-gray-50">
                <TableRow>
                  {Object.keys(filteredMetrics[0] || {}).map((key) => (
                    <TableHead
                      key={key}
                      className="px-3 py-3 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap border-b border-gray-200"
                    >
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="flex items-center">
                            {key}
                            <InfoIcon className="ml-1 w-3 h-3 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Additional information about {key}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMetrics.slice(0, maxRows).map((metric, index) => (
                  <TableRow
                    key={index}
                    className="hover:bg-gray-50 transition-colors even:bg-gray-100/50"
                  >
                    {Object.entries(metric).map(([key, value]) => (
                      <TableCell
                        key={key}
                        className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 border-b border-gray-100"
                      >
                        {renderCellValue(key, value)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ModellingTableWithTabs;
