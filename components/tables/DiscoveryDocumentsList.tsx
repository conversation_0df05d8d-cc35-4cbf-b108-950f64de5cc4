import React, { useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreVertical } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { pdf } from '@react-pdf/renderer';
import { DiscoveryResponsesPDF } from '../DiscoveryResponsesPDF';
import { discoveryQuestions, DiscoveryQuestion } from '@/lib/discovery-questions';
import { DiscoveryToken, FormResponse } from '@/types/discovery';

// Function to transform discoveryQuestions into the format expected by DiscoveryResponsesPDF
const transformQuestionsForPDF = () => {
  const transformedQuestions: Array<{
    id: number;
    question: string;
    options: string[];
  }> = [];
  
  // Flatten the sections and transform each question
  discoveryQuestions.forEach((section, sectionIndex) => {
    section.questions.forEach((question, questionIndex) => {
      transformedQuestions.push({
        id: parseInt(`${sectionIndex}${questionIndex}`), // Create a numeric ID
        question: question.question,
        options: question.options || [],
      });
    });
  });
  
  return transformedQuestions;
};

interface DiscoveryDocumentsListProps {
  data: DiscoveryToken[];
  onDataChange: () => Promise<void>;
}

export function DiscoveryDocumentsList({ data, onDataChange }: DiscoveryDocumentsListProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [tokenToDelete, setTokenToDelete] = useState<DiscoveryToken | null>(null);
  const [showResponseDialog, setShowResponseDialog] = useState(false);
  const [selectedToken, setSelectedToken] = useState<DiscoveryToken | null>(null);

  const copyToClipboard = async (token: string) => {
    try {
      const baseUrl = window.location.origin;
      const discoveryLink = `${baseUrl}/discovery-form/${token}`;
      await navigator.clipboard.writeText(discoveryLink);
      toast({
        title: "Success",
        description: "Link copied to clipboard!",
      });
    } catch (err) {
      console.error('Failed to copy:', err);
      toast({
        title: "Error",
        description: "Failed to copy link. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!tokenToDelete) return;

    const supabase = createClient();
    const { error } = await supabase
      .from('discovery_tokens')
      .delete()
      .eq('token', tokenToDelete.token);

    if (error) {
      console.error('Error deleting token:', error);
      toast({
        title: "Error",
        description: "Failed to delete discovery token. Please try again.",
        variant: "destructive",
      });
    } else {
      toast({
        title: "Success",
        description: "Discovery token deleted successfully.",
      });
      onDataChange();
    }
    setShowDeleteDialog(false);
    setTokenToDelete(null);
  };

  const viewResponses = (token: DiscoveryToken) => {
    const baseUrl = window.location.origin;
    window.open(`${baseUrl}/discovery-form/${token.token}?view=true`, '_blank');
  };

  const downloadPDF = async (token: DiscoveryToken) => {
    try {
      // Fetch responses
      const supabase = createClient();
      const { data, error } = await supabase
        .from('discovery_tokens')
        .select('response, created_at')
        .eq('token', token.token)
        .single();

      if (error) {
        console.error('Error fetching responses:', error);
        toast({
          title: "Error",
          description: "Failed to fetch responses",
          variant: "destructive",
        });
        return;
      }

      if (!data?.response?.answers) {
        toast({
          title: "Error",
          description: "No responses found for this discovery form",
          variant: "destructive",
        });
        return;
      }

      // Generate PDF
      const pdfDoc = await pdf(
        <DiscoveryResponsesPDF
          responses={data.response.answers}
          questions={transformQuestionsForPDF()}
          createdAt={data.response.submitted_at || data.created_at}
        />
      ).toBlob();

      // Create download link
      const url = URL.createObjectURL(pdfDoc);
      const link = document.createElement('a');
      link.href = url;
      link.download = `discovery_responses_${token.token}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "PDF downloaded successfully",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: "Failed to generate PDF",
        variant: "destructive",
      });
    }
  };

  const columns: ColumnDef<DiscoveryToken>[] = [
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"));
        return date.toLocaleDateString();
      },
    },
    {
      accessorKey: "household_name",
      header: "Household",
      cell: ({ row }) => {
        return row.original.household_name || "N/A";
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${status === 'completed' ? 'bg-green-100 text-green-800' : 
              status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
              'bg-red-100 text-red-800'}`
          }>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
      },
    },
    {
      id: "link",
      header: "Link",
      cell: ({ row }) => {
        const token = row.original.token;
        return (
          <div className="flex items-center space-x-2">
            <span>copy link </span>
            <Button
              variant="link"
              className="p-0 h-auto font-semibold text-blue-600 hover:text-blue-800"
              onClick={() => copyToClipboard(token)}
            >
              here
            </Button>
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const token = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => viewResponses(token)}>
                View Responses
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadPDF(token)}>
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                setTokenToDelete(token);
                setShowDeleteDialog(true);
              }}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No discovery documents found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Discovery Token</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this discovery token? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showResponseDialog} onOpenChange={setShowResponseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Discovery Responses</DialogTitle>
          </DialogHeader>
          {selectedToken && (
            <div className="space-y-4">
              {/* TODO: Fetch and display responses */}
              <p>Responses for token: {selectedToken.token}</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
