import React, { useState, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  PaginationState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreVertical } from 'lucide-react';
import ConfirmDeleteModal from '@/components/modals/DeleteScenarioModal';
import PresentationModal from '@/components/modals/PresentationModal';
import { createClient } from '@/utils/supabase/client';
import { useRouter, usePathname } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface AllScenario {
  id: number;
  scenario_name: string;
  household_name: string | null;
  created_at: string;
  last_edited_at: string | null;
  household_id: number;
  user_id?: string;
  org_id?: string;
}

interface AllScenariosTableProps {
  data: AllScenario[];
  onDataChange: () => void;
  onCreateScenario: () => void;
  onPresentScenarios?: () => void;
  preselectedHousehold?: { id: number; householdName: string };
  viewMode: 'user' | 'organization';
  onViewModeChange: (value: 'user' | 'organization') => void;
}

const AllScenariosTable: React.FC<AllScenariosTableProps> = ({
  data,
  onDataChange,
  onCreateScenario,
  onPresentScenarios,
  preselectedHousehold,
  viewMode,
  onViewModeChange
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState<AllScenario | null>(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [scenariosWithHouseholds, setScenariosWithHouseholds] = useState<AllScenario[]>(data);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  // We're now using the viewMode prop directly instead of internal state

  const handleDeleteClick = (scenario: AllScenario) => {
    setSelectedScenario(scenario);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedScenario) return;

    const supabase = createClient();

    const { error } = await supabase
      .from('scenarios_data1')
      .delete()
      .eq('id', selectedScenario.id);

    if (error) {
      console.error('Error deleting scenario:', error);
    } else {
      onDataChange();
    }

    setIsDeleteModalOpen(false);
    setSelectedScenario(null);
  };

  // Check if we're in the household scenarios page
  const isInHouseholdPage = pathname?.includes('/scenarios');

  // Adjust column sizes based on context
  const getColumnSize = (defaultSize: number) => {
    return isInHouseholdPage ? Math.floor(defaultSize * 0.6) : defaultSize;
  };

  const columns: ColumnDef<AllScenario>[] = [
    {
      accessorKey: "scenario_name",
      header: "Scenario Name",
      size: getColumnSize(200),
      cell: ({ row }) => {
        const scenario = row.original;
        return (
          <span
            className={`cursor-pointer text-blue-600 hover:text-blue-800 ${isInHouseholdPage ? 'text-sm' : ''}`}
            onClick={() => window.open(`/protected/planner?scenarioId=${scenario.id}&household_id=${scenario.household_id}`, '_blank', 'noopener,noreferrer')}
          >
            {scenario.scenario_name}
          </span>
        );
      },
    },
    {
      accessorKey: "household_name",
      header: "Household Name",
      size: getColumnSize(200),
      cell: ({ row }) => {
        const scenario = row.original;
        if (!scenario.household_name && scenario.household_id) {
          return <span className={`text-gray-500 italic ${isInHouseholdPage ? 'text-sm' : ''}`}>Household {scenario.household_id}</span>;
        }
        return scenario.household_name ?
          <span className={isInHouseholdPage ? 'text-sm' : ''}>{scenario.household_name}</span> :
          <span className={`text-gray-500 italic ${isInHouseholdPage ? 'text-sm' : ''}`}>Unknown Household</span>;
      },
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      size: getColumnSize(180),
      cell: ({ row }) => (
        <span className={isInHouseholdPage ? 'text-sm' : ''}>
          {row.original.created_at ?
            new Date(row.original.created_at).toLocaleString() :
            'N/A'}
        </span>
      ),
    },
    {
      accessorKey: "last_edited_at",
      header: "Last Edited At",
      size: getColumnSize(180),
      cell: ({ row }) => (
        <span className={isInHouseholdPage ? 'text-sm' : ''}>
          {row.original.last_edited_at ?
            new Date(row.original.last_edited_at).toLocaleString() :
            'N/A'}
        </span>
      ),
    },
    {
      id: "actions",
      size: isInHouseholdPage ? 60 : 80,
      cell: ({ row }) => {
        const scenario = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => window.open(`/protected/planner?scenarioId=${scenario.id}&household_id=${scenario.household_id}`, '_blank', 'noopener,noreferrer')}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDeleteClick(scenario)}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: scenariosWithHouseholds,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    filterFns: {
      globalSearch: (row, columnId, filterValue) => {
        const searchValue = filterValue.toLowerCase();
        const value = row.getValue<string>(columnId);
        return value?.toLowerCase().includes(searchValue) || false;
      },
    },
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
  });


  return (
    <div className={`flex flex-col h-full ${isInHouseholdPage ? 'household-scenarios-table w-full' : ''}`} style={isInHouseholdPage ? { maxWidth: 'calc(100vw)' } : {}}>
      {/* Fixed header with filters and create button */}
      <div className="flex items-center justify-between py-2 flex-shrink-0 bg-white z-20">
        <div className="flex items-center space-x-4">
          <Input
            placeholder="Filter scenarios..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className={isInHouseholdPage ? "max-w-[150px] h-8 text-sm" : "max-w-sm"}
          />
          <div className="flex items-center space-x-2">
            <Label htmlFor="view-mode-toggle" className={isInHouseholdPage ? "text-sm" : ""}>
              {viewMode === 'user' ? 'My Scenarios' : 'Organization View'}
            </Label>
            <Switch
              id="view-mode-toggle"
              checked={viewMode === 'organization'}
              onCheckedChange={(checked) =>
                onViewModeChange(checked ? 'organization' : 'user')
              }
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={onCreateScenario} size={isInHouseholdPage ? "sm" : "default"} className={isInHouseholdPage ? "text-sm h-8 px-2" : ""}>
            Create Scenario
          </Button>
          {onPresentScenarios && (
            <Button onClick={onPresentScenarios} size={isInHouseholdPage ? "sm" : "default"} className={isInHouseholdPage ? "text-sm h-8 px-2" : ""} variant="outline">
              Present
            </Button>
          )}
        </div>
      </div>

      {/* Scrollable table area with fixed header */}
      <div className="flex-1 min-h-0 overflow-hidden border rounded-md">
        <div className="h-full flex flex-col">
          {/* Fixed header */}
          <div className="flex-shrink-0 bg-white z-10 w-full border-b">
            <Table className={`w-full table-fixed ${isInHouseholdPage ? 'household-table' : ''}`}>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className={isInHouseholdPage ? 'h-12' : ''}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        style={{ width: `${header.getSize()}px` }}
                        className={isInHouseholdPage ? 'text-sm px-2 py-1 h-8' : ''}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
            </Table>
          </div>

          {/* Scrollable body */}
          <div className="flex-1 overflow-auto">
            <Table className={`w-full table-fixed ${isInHouseholdPage ? 'household-table' : ''}`}>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className={isInHouseholdPage ? 'h-12' : ''}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          style={{ width: `${cell.column.getSize()}px` }}
                          className={isInHouseholdPage ? 'text-sm py-1 px-2' : ''}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className={`text-center ${isInHouseholdPage ? 'h-16 text-sm' : 'h-24'}`}>
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Fixed footer with pagination controls */}
      <div className={`flex items-center justify-between space-x-2 py-2 flex-shrink-0 bg-white z-20 border-t ${isInHouseholdPage ? 'text-sm' : ''}`}>
        <div className="flex items-center space-x-4">
          <div className={`flex-1 ${isInHouseholdPage ? 'text-sm' : 'text-sm'} text-muted-foreground`}>
            {table.getFilteredRowModel().rows.length} total scenario{table.getFilteredRowModel().rows.length === 1 ? '' : 's'}
          </div>

          <div className="flex items-center space-x-2">
            <span className={isInHouseholdPage ? "text-sm" : "text-sm"}>Show</span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className={isInHouseholdPage ? "h-6 w-[60px] text-sm" : "h-8 w-[70px]"}>
                <SelectValue placeholder={pagination.pageSize.toString()} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className={isInHouseholdPage ? "text-sm" : "text-sm"}>scenarios</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className={isInHouseholdPage ? "h-6 px-2 text-sm" : ""}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className={isInHouseholdPage ? "h-6 px-2 text-sm" : ""}
          >
            Next
          </Button>
        </div>
      </div>

      {selectedScenario && (
        <ConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={handleConfirmDelete}
          scenarioName={selectedScenario.scenario_name}
        />
      )}
    </div>
  );
};

export default AllScenariosTable;
