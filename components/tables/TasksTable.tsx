'use client';

import React, { useState, useMemo, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { startOfToday, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameDay, isWithinInterval, parseISO } from "date-fns";
import { useSearchParams } from "next/navigation";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, ChevronDown, ChevronUp } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TaskModal from '@/components/modals/TaskModal';

interface Comment {
  id: number;
  task_id: number;
  content: string;
  created_at: string;
  user_id: string;
}

interface Task {
  id: number;
  household_id: number;
  household_name: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  due_date: string;
  updated_at: string;
  user_id: string;
  status: string;
  comments: Comment[];
  assigned_to?: string;
  assigned_name?: string;
  metadata?: {
    workflow_id?: number;
    is_workflow_task?: boolean;
  };
}

interface TasksTableProps {
  data: Task[];
  onDataChange: () => Promise<void>;
  onCreateTask?: () => void;
  onEditTask?: (task: Task) => void;
  showHouseholdSearch?: boolean;
  fixedHouseholdId?: string;
  fixedHouseholdName?: string;
}

export function TasksTable({
  data,
  onDataChange,
  onCreateTask,
  onEditTask,
  showHouseholdSearch = true,
  fixedHouseholdId,
  fixedHouseholdName
}: TasksTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [dateFilter, setDateFilter] = useState<string>("all");
  const [householdSearch, setHouseholdSearch] = useState(fixedHouseholdName || "all");
  const [importanceFilter, setImportanceFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("in progress");
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [fullTaskData, setFullTaskData] = useState<any>(null);
  const [userProfiles, setUserProfiles] = useState<{[key: string]: {name: string}}>({});
  const supabase = createClient();

  const fetchTaskDetails = async (taskId: number) => {
    const { data, error } = await supabase
      .from('tasks')
      .select('*, task_comments(*)')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('Error fetching task details:', error);
      return null;
    }

    return data;
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !selectedTask?.id) return;

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Extract tagged users using regex
      const taggedUsernames = newComment.match(/@(\w+)/g) || [];
      const taggedUsers: { id: string, name: string }[] = [];

      if (taggedUsernames.length > 0) {
        // Remove @ symbol and find matching users
        const usernames = taggedUsernames.map(tag => tag.substring(1));

        // Fetch users that match the tagged usernames
        const { data: matchedUsers, error } = await supabase
          .from('profiles')
          .select('user_id, name')
          .in('name', usernames);

        if (!error && matchedUsers) {
          matchedUsers.forEach(matchedUser => {
            taggedUsers.push({ id: matchedUser.user_id, name: matchedUser.name });
          });
        }
      }

      // Insert the comment
      const { data: commentData, error } = await supabase
        .from('task_comments')
        .insert({
          task_id: selectedTask.id,
          content: newComment,
          user_id: user.id,
          tagged_users: taggedUsers.length > 0 ? taggedUsers.map(user => user.id) : null
        })
        .select('id')
        .single();

      if (error) throw error;

      // Send notifications to tagged users
      for (const taggedUser of taggedUsers) {
        // Skip if the tagged user is the current user
        if (taggedUser.id === user.id) continue;

        await supabase.from('notifications').insert({
          user_id: taggedUser.id,
          content: `You were mentioned in a comment on task: ${selectedTask.title}`,
          type: 'task_mention',
          link: `/protected/tasks?view=${selectedTask.id}`,
          created_at: new Date().toISOString()
        });
      }

      setNewComment('');
      onDataChange();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  // Get unique households from data
  const uniqueHouseholds = useMemo(() =>
    Array.from(new Set(data.map(task => task.household_name))).sort(),
    [data]
  );

  // Memoize filtered data
  const filteredData = useMemo(() => {
    let filteredTasks = [...data];

    // Date filtering
    if (dateFilter !== "all") {
      const today = startOfToday();
      const weekStart = startOfWeek(today, { weekStartsOn: 6 }); // Saturday
      const weekEnd = endOfWeek(today, { weekStartsOn: 6 }); // Friday
      const monthStart = startOfMonth(today);
      const monthEnd = endOfMonth(today);

      filteredTasks = filteredTasks.filter(task => {
        const dueDate = parseISO(task.due_date);
        switch (dateFilter) {
          case "today":
            return isSameDay(dueDate, today);
          case "week":
            return isWithinInterval(dueDate, { start: weekStart, end: weekEnd });
          case "month":
            return isWithinInterval(dueDate, { start: monthStart, end: monthEnd });
          default:
            return true;
        }
      });
    }

    // Household filtering
    if (fixedHouseholdId) {
      const numericHouseholdId = parseInt(fixedHouseholdId, 10);
      filteredTasks = filteredTasks.filter(task =>
        task.household_id === numericHouseholdId
      );
    } else if (householdSearch && householdSearch !== "all") {
      filteredTasks = filteredTasks.filter(task =>
        task.household_name.toLowerCase().includes(householdSearch.toLowerCase())
      );
    }

    // Importance filtering
    if (importanceFilter !== "all") {
      filteredTasks = filteredTasks.filter(task =>
        task.importance.toLowerCase() === importanceFilter.toLowerCase()
      );
    }

    // Status filtering
    if (statusFilter === "in progress") {
      // Show all active tasks (not complete or cancelled)
      filteredTasks = filteredTasks.filter(task =>
        !["complete", "cancelled"].includes(task.status)
      );
    } else if (statusFilter !== "all") {
      // Show specific status
      filteredTasks = filteredTasks.filter(task =>
        task.status === statusFilter
      );
    }

    return filteredTasks;
  }, [data, dateFilter, householdSearch, importanceFilter, statusFilter, fixedHouseholdId]);

  const statusOptions = [
    { value: "in progress", label: "In Progress" },
    { value: "not started", label: "Not Started" },
    { value: "with client", label: "With Client" },
    { value: "with third party", label: "With Third Party" },
    { value: "complete", label: "Complete" },
    { value: "cancelled", label: "Cancelled" }
  ];

  const toggleRowExpanded = async (rowId: string) => {
    const newExpandedRows = { ...expandedRows };
    newExpandedRows[rowId] = !expandedRows[rowId];
    setExpandedRows(newExpandedRows);

    // If expanding the row, fetch task details and user profiles for comments
    if (newExpandedRows[rowId]) {
      // Get the task directly from the data array using the row index
      const task = table.getRow(rowId).original;

      if (!task || !task.id) {
        console.error('Invalid task:', task);
        return;
      }

      // Fetch the task details including comments
      const { data: taskData, error } = await supabase
        .from('tasks')
        .select('*, task_comments(*)')
        .eq('id', task.id)
        .single();

      if (error) {
        console.error('Error fetching task details:', error);
        return;
      }

      // Update the fullTaskData state with the fetched task
      setFullTaskData(taskData);

      // If there are comments, fetch the user profiles
      if (taskData?.task_comments?.length) {
        // Get unique user IDs from comments
        const userIds = [...new Set(taskData.task_comments.map((comment: any) => comment.user_id))].filter(Boolean);

        if (userIds.length > 0) {
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('user_id, name')
            .in('user_id', userIds);

          if (profilesError) {
            console.error('Error fetching user profiles:', profilesError);
            return;
          }

          const profiles: {[key: string]: {name: string}} = {};
          profilesData?.forEach(profile => {
            profiles[profile.user_id] = { name: profile.name || 'Unknown User' };
          });

          setUserProfiles(profiles);
        }
      }
    }
  };

  const columns: ColumnDef<Task>[] = [
    {
      id: "expand",
      header: "",
      cell: ({ row }) => {
        const isExpanded = expandedRows[row.id] || false;
        return (
          <div>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => toggleRowExpanded(row.id)}
            >
              <span className="sr-only">{isExpanded ? "Collapse row" : "Expand row"}</span>
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>

          </div>
        );
      },
    },
    {
      accessorKey: "household_name",
      header: "Household",
      cell: ({ row }) => <div>{row.getValue("household_name")}</div>,
    },
    {
      id: "workflow",
      header: "Workflow",
      cell: ({ row }) => {
        const task = row.original;
        if (task.metadata?.workflow_id) {
          return (
            <div className="flex items-center">
              <a
                href={`/protected/workflows/${task.metadata.workflow_id}`}
                className="text-blue-600 hover:text-blue-800 hover:underline text-sm"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                View Workflow
              </a>
            </div>
          );
        }
        return null;
      },
    },
    {
      accessorKey: "title",
      header: "Task Title",
      cell: ({ row }) => (
        <button
          onClick={async () => {
            const task = row.original;
            setSelectedTask(task);
            const fullTask = await fetchTaskDetails(task.id);
            setFullTaskData(fullTask);
            setIsViewModalOpen(true);
          }}
          className="text-left hover:underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded"
        >
          {row.getValue("title")}
        </button>
      ),
    },
    {
      accessorKey: "importance",
      header: "Importance",
      cell: ({ row }) => {
        const importance = (row.getValue("importance") as string || "").toLowerCase();
        const colorMap: { [key: string]: string } = {
          high: "bg-red-100 text-red-800",
          medium: "bg-blue-100 text-blue-800",
          low: "bg-yellow-100 text-yellow-800"
        };
        const displayText = importance.charAt(0).toUpperCase() + importance.slice(1);
        return (
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[importance] || "bg-gray-100 text-gray-800"}`}>
            {displayText}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const colorMap: { [key: string]: string } = {
          "not started": "bg-gray-100 text-gray-800",
          "in progress": "bg-blue-100 text-blue-800",
          "with client": "bg-purple-100 text-purple-800",
          "with third party": "bg-orange-100 text-orange-800",
          "complete": "bg-green-100 text-green-800",
          "cancelled": "bg-red-100 text-red-800"
        };
        const displayText = status.split(' ').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
        return (
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorMap[status] || "bg-gray-100 text-gray-800"}`}>
            {displayText}
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created Date",
      cell: ({ row }) => <div>{new Date(row.getValue("created_at")).toLocaleDateString()}</div>,
    },
    {
      accessorKey: "due_date",
      header: "Due Date",
      cell: ({ row }) => <div>{new Date(row.getValue("due_date")).toLocaleDateString()}</div>,
    },
    {
      accessorKey: "assigned_name",
      header: "Assigned To",
      cell: ({ row }) => {
        const assignedName = row.getValue("assigned_name") as string | undefined;
        return (
          <div className="text-gray-500 italic">
            {assignedName || "Unassigned"}
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const task = row.original;

        const handleDelete = async () => {
          const { error } = await supabase
            .from('tasks')
            .delete()
            .eq('id', task.id);

          if (!error) {
            onDataChange();
          }
        };

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEditTask && (
                <DropdownMenuItem onClick={() => onEditTask(task)}>
                  Edit Task
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handleDelete}>
                Delete Task
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
  });

  const [newComment, setNewComment] = useState('');
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);

  const CommentsSection = () => (
    <div className="space-y-4">
      <div className="max-h-[300px] overflow-y-auto space-y-3">
        {selectedTask?.comments?.map((comment) => (
          <div key={comment.id} className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-600">{comment.content}</p>
            <p className="text-xs text-gray-400 mt-1">
              {new Date(comment.created_at).toLocaleString()}
            </p>
          </div>
        ))}
        {(!selectedTask?.comments || selectedTask.comments.length === 0) && (
          <p className="text-sm text-gray-700 text-center">No comments yet</p>
        )}
      </div>
      <div className="flex gap-2">
        <Input
          placeholder="Add a comment..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleAddComment();
            }
          }}
        />
        <Button onClick={handleAddComment}>Add</Button>
      </div>
    </div>
  );

  const fetchUserProfiles = async (taskId: number) => {
    const { data: taskData } = await supabase
      .from('tasks')
      .select('*, task_comments(*)')
      .eq('id', taskId)
      .single();

    if (!taskData?.task_comments?.length) return;

    // Get unique user IDs from comments
    const userIds = [...new Set(taskData.task_comments.map((comment: any) => comment.user_id))].filter(Boolean);

    if (userIds.length === 0) return;

    const { data, error } = await supabase
      .from('profiles')
      .select('user_id, name')
      .in('user_id', userIds);

    if (error) {
      console.error('Error fetching user profiles:', error);
      return;
    }

    const profiles: {[key: string]: {name: string}} = {};
    data?.forEach(profile => {
      profiles[profile.user_id] = { name: profile.name || 'Unknown User' };
    });

    setUserProfiles(profiles);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex flex-1 items-center space-x-4">
          {/* Date Filter */}
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Dates</SelectItem>
              <SelectItem value="today">Due Today</SelectItem>
              <SelectItem value="week">Due This Week</SelectItem>
              <SelectItem value="month">Due This Month</SelectItem>
            </SelectContent>
          </Select>

          {/* Household Search - Only show if not fixed */}
          {!fixedHouseholdId && showHouseholdSearch && (
            <Select
              value={householdSearch || "all"}
              onValueChange={setHouseholdSearch}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Filter by household" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Households</SelectItem>
                {uniqueHouseholds.map((household) => (
                  <SelectItem key={household} value={household}>
                    {household}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          {/* Importance Filter */}
          <Select value={importanceFilter} onValueChange={setImportanceFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by importance" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Importance</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          {/* Status Filter */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {statusOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {onCreateTask && (
          <Button onClick={onCreateTask}>
            Create Task
          </Button>
        )}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                const dueDate = new Date(row.getValue("due_date"));
                const isOverdue = dueDate < new Date();

                const isExpanded = expandedRows[row.id] || false;
                const task = row.original;

                return (
                  <React.Fragment key={row.id}>
                    <TableRow
                      data-state={row.getIsSelected() && "selected"}
                      className={isOverdue ? "bg-red-50" : ""}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                    {isExpanded && (
                      <TableRow className="bg-gray-50">
                        <TableCell colSpan={columns.length} className="p-4">
                          <div className="space-y-3">
                            <div>
                              <h3 className="text-sm font-medium">Task Details</h3>
                              <div className="mt-2 text-sm text-gray-700 whitespace-pre-wrap">
                                <div dangerouslySetInnerHTML={{ __html: fullTaskData?.content || "No description provided." }} />
                              </div>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Comments</h3>
                              <div className="mt-2">
                                {fullTaskData?.task_comments && fullTaskData.task_comments.length > 0 ? (
                                  <div className="space-y-2">
                                    {fullTaskData.task_comments.map((comment: any) => (
                                      <div key={comment.id} className="bg-white p-2 rounded border text-sm">
                                        <div dangerouslySetInnerHTML={{ __html: comment.content }} />
                                        <div className="flex justify-between items-center mt-1">
                                          <p className="text-xs text-gray-500">
                                            {new Date(comment.created_at).toLocaleString()}
                                          </p>
                                          <p className="text-xs font-medium text-gray-500">
                                            {userProfiles[comment.user_id]?.name || 'Unknown User'}
                                          </p>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <p className="text-sm text-gray-500">No comments yet</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No tasks found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>
      {selectedTask && fullTaskData && (
        <TaskModal
          isOpen={isViewModalOpen}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedTask(null);
            setFullTaskData(null);
          }}
          task={{
            id: fullTaskData.id,
            title: fullTaskData.title,
            due_date: fullTaskData.due_date,
            household_id: fullTaskData.household_id,
            importance: fullTaskData.importance || '',
            content: fullTaskData.content || '',
            status: fullTaskData.status || '',
            comments: fullTaskData.task_comments || []
          }}
          onSave={async () => {
            await onDataChange();
          }}
          householdName={selectedTask.household_name}
          readonly={true}
        />
      )}
    </div>
  );
}
