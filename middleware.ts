import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/utils/supabase/middleware";
import { protectRoute, getRouteConfig } from "@/utils/route-protection";

export async function middleware(request: NextRequest) {
  // First, handle session update
  const response = await updateSession(request);

  // Get the pathname from the URL
  const pathname = request.nextUrl.pathname;

  // Apply route protection based on pathname
  const routeConfig = getRouteConfig(pathname);
  const protectionResult = await protectRoute(request, routeConfig);

  // If protection returned a redirect, use it
  if (protectionResult) {
    return protectionResult;
  }

  // Define allowed routes for your sliced app
  const allowedRoutes = [
    '/protected/scenarios',
    '/protected/admin/profile',
    '/protected/admin/organisation',
    '/protected/households',
    '/protected/planner',
    '/protected/presentation'
  ];

  // Define allowed household detail routes
  const allowedHouseholdRoutes = [
    '/household_overview',
    '/income_expenses',
    '/assets_liabilities',
    '/scenarios'
  ];

  // Check if it's a household route and if it's allowed
  if (pathname.includes('/protected/households/household/')) {
    const householdId = pathname.split('/households/household/')[1].split('/')[0];
    const routeSuffix = pathname.split(`/households/household/${householdId}`)[1];

    // Allow the base household route
    if (!routeSuffix || routeSuffix === '') {
      return response;
    }

    // Check if the route suffix is in the allowed list
    const isAllowedHouseholdRoute = allowedHouseholdRoutes.some(route =>
      routeSuffix === route || routeSuffix.startsWith(`/${route}`)
    );

    if (!isAllowedHouseholdRoute) {
      return NextResponse.redirect(new URL(`/protected/households/household/${householdId}/household_overview`, request.url));
    }

    return response;
  }

  // Check if the current path is a protected route but not in allowed routes
  if (pathname.startsWith('/protected/') &&
      !allowedRoutes.some(route => pathname.startsWith(route)) &&
      pathname !== '/protected') {

    // Redirect to scenarios page
    return NextResponse.redirect(new URL('/protected/scenarios', request.url));
  }

  return response;
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
