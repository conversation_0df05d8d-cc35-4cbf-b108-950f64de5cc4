import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

// Define fund types
export type FundType = 'investment' | 'kiwisaver';

// Define fund interface
export interface Fund {
  id: string;
  name: string;
  type: FundType;
  return: number;
  stdDev: number;
  color?: string;
  isEditing?: boolean;
  incomePortion?: number;
}

// Default funds to use if no saved funds are found
export const defaultFunds: Fund[] = [
  // Investment funds
  {
    id: 'inv-1',
    name: 'Conservative',
    type: 'investment',
    return: 3.5,
    stdDev: 4.0,
    color: '#E2F2FF',
    incomePortion: 90
  },
  {
    id: 'inv-2',
    name: 'Moderate',
    type: 'investment',
    return: 4.5,
    stdDev: 6.0,
    color: '#C2E0FF',
    incomePortion: 80
  },
  {
    id: 'inv-3',
    name: 'Balanced',
    type: 'investment',
    return: 5.5,
    stdDev: 8.0,
    color: '#99CCF3',
    incomePortion: 60
  },
  {
    id: 'inv-4',
    name: 'Growth',
    type: 'investment',
    return: 6.5,
    stdDev: 10.0,
    color: '#66B2FF',
    incomePortion: 40
  },
  {
    id: 'inv-5',
    name: 'High Growth',
    type: 'investment',
    return: 7.5,
    stdDev: 12.0,
    color: '#3399FF',
    incomePortion: 20
  },
  // KiwiSaver funds
  {
    id: 'ks-1',
    name: 'Conservative',
    type: 'kiwisaver',
    return: 3.5,
    stdDev: 4.0,
    color: '#E5F5E0',
    incomePortion: 90
  },
  {
    id: 'ks-2',
    name: 'Moderate',
    type: 'kiwisaver',
    return: 4.5,
    stdDev: 6.0,
    color: '#C7E9C0',
    incomePortion: 80
  },
  {
    id: 'ks-3',
    name: 'Balanced',
    type: 'kiwisaver',
    return: 5.5,
    stdDev: 8.0,
    color: '#A1D99B',
    incomePortion: 60
  },
  {
    id: 'ks-4',
    name: 'Growth',
    type: 'kiwisaver',
    return: 6.5,
    stdDev: 10.0,
    color: '#74C476',
    incomePortion: 40
  },
  {
    id: 'ks-5',
    name: 'High Growth',
    type: 'kiwisaver',
    return: 7.5,
    stdDev: 12.0,
    color: '#41AB5D',
    incomePortion: 20
  }
];

// Custom hook to fetch funds
export function useFunds(userId?: string) {
  const [funds, setFunds] = useState<Fund[]>(defaultFunds);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchFunds = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Check if the table exists
        const { error: tableCheckError } = await supabase
          .from('scenario_metrics')
          .select('count')
          .limit(1);

        if (tableCheckError) {
          setIsLoading(false);
          return;
        }

        // Fetch the funds
        const { data, error } = await supabase
          .from('scenario_metrics')
          .select('funds')
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching funds:', error);
          setError(new Error(error.message));
          return;
        }

        if (data && data.funds && Array.isArray(data.funds) && data.funds.length > 0) {
          setFunds(data.funds);
        }
      } catch (err) {
        console.error('Exception fetching funds:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchFunds();
  }, [userId, supabase]);

  // Helper function to get funds by type
  const getFundsByType = (type: FundType): Fund[] => {
    return funds.filter(fund => fund.type === type);
  };

  // Helper function to get a fund by ID
  const getFundById = (id: string): Fund | undefined => {
    return funds.find(fund => fund.id === id);
  };

  // Helper function to get a fund by name and type
  const getFundByNameAndType = (name: string, type: FundType): Fund | undefined => {
    return funds.find(fund => fund.name === name && fund.type === type);
  };

  return {
    funds,
    isLoading,
    error,
    getFundsByType,
    getFundById,
    getFundByNameAndType
  };
}
