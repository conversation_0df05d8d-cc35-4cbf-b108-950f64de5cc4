/**
 * Enhanced Secure Authentication Hook
 *
 * Provides secure authentication state management with improved caching,
 * session monitoring, and seamless user experience.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  validateSessionClient,
  refreshSession,
  shouldRefreshSession,
  clearSessionCache,
  type SessionInfo
} from '@/utils/session-manager';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

export interface AuthState {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  sessionInfo: SessionInfo | null;
  error: string | null;
  isExpiringSoon: boolean;
  securityLevel: 'secure' | 'warning' | 'critical';
}

export interface UseSecureAuthOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  onAuthChange?: (user: any | null) => void;
}

/**
 * Helper function to create complete AuthState object
 */
function createAuthState(
  user: any | null,
  isLoading: boolean,
  isAuthenticated: boolean,
  sessionInfo: SessionInfo | null,
  error: string | null
): AuthState {
  return {
    user,
    isLoading,
    isAuthenticated,
    sessionInfo,
    error,
    isExpiringSoon: sessionInfo?.isExpiringSoon || false,
    securityLevel: sessionInfo?.securityLevel || 'critical'
  };
}

/**
 * Enhanced secure authentication hook with improved session management
 */
export function useSecureAuth(options: UseSecureAuthOptions = {}) {
  const { redirectTo = '/sign-in', requireAuth = false, onAuthChange } = options;
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>(
    createAuthState(null, true, false, null, null)
  );
  const [lastValidation, setLastValidation] = useState<number>(0);

  const validateAuth = useCallback(async (force: boolean = false) => {
    try {
      // Use cached validation for better performance
      const now = Date.now();
      const cacheTime = 30 * 1000; // 30 seconds

      if (!force && (now - lastValidation) < cacheTime && authState.user) {
        // Use cached result if recent and user exists
        return;
      }

      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Use enhanced session validation with caching
      const sessionInfo = await validateSessionClient(!force);
      setLastValidation(now);

      // Auto-refresh session if expiring soon
      if (sessionInfo.isValid && shouldRefreshSession(sessionInfo)) {
        try {
          const refreshedSession = await refreshSession();
          const newAuthState = createAuthState(
            refreshedSession.user,
            false,
            refreshedSession.isValid,
            refreshedSession,
            refreshedSession.error || null
          );
          setAuthState(newAuthState);

          if (onAuthChange) {
            onAuthChange(refreshedSession.user);
          }
          return;
        } catch (refreshError) {
          console.error('Auto-refresh failed:', refreshError);
          // Continue with original session if refresh fails
        }
      }

      if (sessionInfo.isValid && sessionInfo.user) {
        const newAuthState = createAuthState(
          sessionInfo.user,
          false,
          true,
          sessionInfo,
          null
        );
        setAuthState(newAuthState);

        if (onAuthChange) {
          onAuthChange(sessionInfo.user);
        }
      } else {
        const newAuthState = createAuthState(
          null,
          false,
          false,
          sessionInfo,
          sessionInfo.error || 'Authentication failed'
        );
        setAuthState(newAuthState);

        if (onAuthChange) {
          onAuthChange(null);
        }

        // Only redirect if authentication is required AND we're not already on the redirect page
        if (requireAuth && window.location.pathname !== redirectTo) {
          router.push(redirectTo);
        }
      }
    } catch (error) {
      const newAuthState = createAuthState(
        null,
        false,
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error'
      );
      setAuthState(newAuthState);

      if (onAuthChange) {
        onAuthChange(null);
      }

      if (requireAuth && window.location.pathname !== redirectTo) {
        router.push(redirectTo);
      }
    }
  }, [requireAuth, redirectTo, router, onAuthChange, lastValidation, authState.user]);

  // Initial authentication check - only run once on mount
  useEffect(() => {
    validateAuth();
  }, []); // Empty dependency array to run only once

  // Listen for auth state changes
  useEffect(() => {
    const supabase = createClient();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Prevent validation loops by handling events more carefully
        if (event === 'SIGNED_OUT') {
          // Clear session cache on sign out
          clearSessionCache();

          const newAuthState = createAuthState(null, false, false, null, null);
          setAuthState(newAuthState);

          if (onAuthChange) {
            onAuthChange(null);
          }
        } else if (event === 'SIGNED_IN' && session?.user) {
          // Create a minimal session info for immediate state update
          const quickSessionInfo: SessionInfo = {
            isValid: true,
            isExpired: false,
            isExpiringSoon: false,
            expiresAt: session.expires_at || null,
            timeUntilExpiry: session.expires_at ? (session.expires_at * 1000) - Date.now() : null,
            user: session.user,
            lastValidated: Date.now(),
            securityLevel: 'secure'
          };

          const newAuthState = createAuthState(
            session.user,
            false,
            true,
            quickSessionInfo,
            null
          );
          setAuthState(newAuthState);

          if (onAuthChange) {
            onAuthChange(session.user);
          }
        } else if (event === 'TOKEN_REFRESHED') {
          // Force re-validation on token refresh
          await validateAuth(true);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [validateAuth, onAuthChange]);

  // Refresh authentication state
  const refresh = useCallback(async () => {
    await validateAuth(true); // Force refresh
  }, [validateAuth]);

  // Enhanced sign out function with cache clearing
  const signOut = useCallback(async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();

      // Clear session cache
      clearSessionCache();

      const newAuthState = createAuthState(null, false, false, null, null);
      setAuthState(newAuthState);

      if (onAuthChange) {
        onAuthChange(null);
      }

      router.push('/sign-in');
    } catch (error) {
      // Handle sign out error silently
    }
  }, [router, onAuthChange]);

  // Session refresh function
  const refreshSessionManually = useCallback(async () => {
    try {
      const refreshedSession = await refreshSession();
      const newAuthState = createAuthState(
        refreshedSession.user,
        false,
        refreshedSession.isValid,
        refreshedSession,
        refreshedSession.error || null
      );
      setAuthState(newAuthState);

      if (onAuthChange) {
        onAuthChange(refreshedSession.user);
      }

      return refreshedSession;
    } catch (error) {
      console.error('Error refreshing session:', error);
      throw error;
    }
  }, [onAuthChange]);

  return {
    ...authState,
    refresh,
    signOut,
    validateAuth,
    refreshSession: refreshSessionManually,
  };
}

/**
 * Hook for components that require authentication
 */
export function useRequireAuth(redirectTo: string = '/sign-in') {
  return useSecureAuth({
    requireAuth: true,
    redirectTo,
  });
}

/**
 * Hook for optional authentication (doesn't redirect)
 */
export function useOptionalAuth() {
  return useSecureAuth({
    requireAuth: false,
  });
}

/**
 * Hook that provides authentication state without automatic validation
 * Use this for performance-sensitive components that don't need real-time validation
 */
export function useAuthState() {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    // Get initial user
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user);
      setIsLoading(false);
    });

    // Listen for changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_, session) => {
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
  };
}
