'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  validateSessionClient,
  refreshSession,
  shouldRefreshSession,
  getSessionSecurityStatus,
  clearAllAuthData,
  clearAllAuthDataAndRefresh,
  type SessionInfo,
  type SessionSecurityStatus
} from '@/utils/session-manager';
import { createClient } from '@/utils/supabase/client';

export interface SessionMonitorState {
  sessionInfo: SessionInfo | null;
  securityStatus: SessionSecurityStatus | null;
  isMonitoring: boolean;
  lastCheck: Date | null;
  autoRefreshEnabled: boolean;
}

export interface UseSessionMonitorOptions {
  autoRefresh?: boolean;
  checkInterval?: number; // in milliseconds
  onSessionExpired?: () => void;
  onSessionWarning?: (timeLeft: number) => void;
  onSecurityLevelChange?: (level: 'secure' | 'warning' | 'critical') => void;
}

/**
 * Enhanced session monitoring hook for real-time session status tracking
 */
export function useSessionMonitor(options: UseSessionMonitorOptions = {}) {
  const {
    autoRefresh = true,
    checkInterval = 60000, // 1 minute
    onSessionExpired,
    onSessionWarning,
    onSecurityLevelChange
  } = options;

  const [state, setState] = useState<SessionMonitorState>({
    sessionInfo: null,
    securityStatus: null,
    isMonitoring: false,
    lastCheck: null,
    autoRefreshEnabled: autoRefresh
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSecurityLevelRef = useRef<'secure' | 'warning' | 'critical' | null>(null);
  const callbacksRef = useRef({ onSessionExpired, onSessionWarning, onSecurityLevelChange });

  // Update callbacks ref when they change
  useEffect(() => {
    callbacksRef.current = { onSessionExpired, onSessionWarning, onSecurityLevelChange };
  }, [onSessionExpired, onSessionWarning, onSecurityLevelChange]);

  // Check session status
  const checkSession = useCallback(async (force: boolean = false) => {
    try {
      // Use shorter timeout for forced checks to avoid hanging UI
      const timeoutMs = force ? 10000 : 15000;
      const sessionInfo = await validateSessionClient(!force, timeoutMs);
      const securityStatus = getSessionSecurityStatus(sessionInfo);
      const now = new Date();

      setState(prev => ({
        ...prev,
        sessionInfo,
        securityStatus,
        lastCheck: now
      }));

      // Handle security level changes using stable callbacks
      if (lastSecurityLevelRef.current !== securityStatus.level) {
        lastSecurityLevelRef.current = securityStatus.level;
        if (callbacksRef.current.onSecurityLevelChange) {
          callbacksRef.current.onSecurityLevelChange(securityStatus.level);
        }
      }

      // Handle session expiration using stable callbacks
      if (sessionInfo.isExpired && callbacksRef.current.onSessionExpired) {
        callbacksRef.current.onSessionExpired();
      }

      // Handle session warning using stable callbacks
      if (sessionInfo.isExpiringSoon && sessionInfo.timeUntilExpiry && callbacksRef.current.onSessionWarning) {
        callbacksRef.current.onSessionWarning(sessionInfo.timeUntilExpiry);
      }

      // Auto-refresh if needed and enabled (but prevent infinite loops)
      if (autoRefresh && shouldRefreshSession(sessionInfo) && sessionInfo.isValid && !force) {
        try {
          // Use shorter timeout for auto-refresh to avoid hanging
          await refreshSession(10000);
          // Re-check after refresh with force=true to prevent infinite recursion
          const refreshedInfo = await validateSessionClient(true, 8000);
          const refreshedStatus = getSessionSecurityStatus(refreshedInfo);

          setState(prev => ({
            ...prev,
            sessionInfo: refreshedInfo,
            securityStatus: refreshedStatus,
            lastCheck: new Date()
          }));
        } catch (refreshError) {
          console.error('Auto-refresh failed:', refreshError);
          // If auto-refresh fails, clear auth data and refresh page
          if (refreshError instanceof Error && refreshError.message.includes('timed out')) {
            // Clear all auth data and refresh page on timeout
            clearAllAuthDataAndRefresh().catch(clearError => {
              console.error('Error clearing auth data and refreshing after timeout:', clearError);
            });

            // Note: setState below may not execute due to page refresh, but keeping for safety
            setState(prev => ({
              ...prev,
              sessionInfo: {
                ...sessionInfo,
                securityLevel: 'critical',
                error: 'Session refresh timed out - refreshing page'
              },
              securityStatus: {
                level: 'critical',
                message: 'Session Timeout',
                icon: '🚨',
                details: ['Session refresh timed out', 'Refreshing page to reset state'],
                requiresAction: true
              }
            }));
          }
        }
      }

      return { sessionInfo, securityStatus };
    } catch (error) {
      console.error('Session check failed:', error);

      // Handle timeout errors specifically
      if (error instanceof Error && error.message.includes('timed out')) {
        // Clear all auth data and refresh page on timeout
        clearAllAuthDataAndRefresh().catch(clearError => {
          console.error('Error clearing auth data and refreshing after timeout:', clearError);
        });

        // Note: setState below may not execute due to page refresh, but keeping for safety
        setState(prev => ({
          ...prev,
          sessionInfo: {
            isValid: false,
            isExpired: false,
            isExpiringSoon: false,
            timeUntilExpiry: null,
            expiresAt: null,
            user: null,
            securityLevel: 'critical',
            error: error.message,
            lastValidated: Date.now()
          },
          securityStatus: {
            level: 'critical',
            message: 'Authentication Timeout',
            icon: '🚨',
            details: [error.message, 'Refreshing page to reset state'],
            requiresAction: true
          }
        }));
      }

      return null;
    }
  }, [autoRefresh]); // Only depend on autoRefresh, not the callback functions

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    setState(prev => ({ ...prev, isMonitoring: true }));

    // Initial check
    checkSession(true);

    // Set up interval
    intervalRef.current = setInterval(() => {
      checkSession();
    }, checkInterval);
  }, [checkSession, checkInterval]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setState(prev => ({ ...prev, isMonitoring: false }));
  }, []);

  // Manual refresh
  const refreshSessionManually = useCallback(async () => {
    try {
      await refreshSession();
      await checkSession(true);
    } catch (error) {
      console.error('Manual session refresh failed:', error);
      throw error;
    }
  }, [checkSession]);

  // Toggle auto-refresh
  const toggleAutoRefresh = useCallback(() => {
    setState(prev => ({
      ...prev,
      autoRefreshEnabled: !prev.autoRefreshEnabled
    }));
  }, []);

  // Listen for auth state changes
  useEffect(() => {
    const supabase = createClient();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT') {
          setState(prev => ({
            ...prev,
            sessionInfo: null,
            securityStatus: null,
            isMonitoring: false
          }));
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
        } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          // Re-check session on auth events - use setTimeout to avoid immediate re-render
          setTimeout(() => {
            checkSession(true);
          }, 100);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []); // No dependencies to prevent infinite loops

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Auto-start monitoring if enabled
  useEffect(() => {
    if (autoRefresh) {
      // Use setTimeout to avoid immediate execution during render
      const timer = setTimeout(() => {
        startMonitoring();
      }, 100);

      return () => {
        clearTimeout(timer);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [autoRefresh]); // Only depend on autoRefresh

  return {
    ...state,
    checkSession,
    startMonitoring,
    stopMonitoring,
    refreshSession: refreshSessionManually,
    toggleAutoRefresh,
  };
}

/**
 * Hook for components that need basic session status without monitoring
 */
export function useSessionStatus() {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [securityStatus, setSecurityStatus] = useState<SessionSecurityStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      const info = await validateSessionClient();
      const status = getSessionSecurityStatus(info);

      setSessionInfo(info);
      setSecurityStatus(status);
    } catch (error) {
      console.error('Failed to check session status:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    sessionInfo,
    securityStatus,
    isLoading,
    refresh: checkStatus
  };
}

/**
 * Hook for session timeout warnings
 */
export function useSessionTimeoutWarning(warningThreshold: number = 5 * 60 * 1000) {
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);

  // Create stable callback functions to prevent infinite loops
  const onSessionWarning = useCallback((timeUntilExpiry: number) => {
    if (timeUntilExpiry <= warningThreshold) {
      setShowWarning(true);
      setTimeLeft(timeUntilExpiry);
    } else {
      setShowWarning(false);
      setTimeLeft(null);
    }
  }, [warningThreshold]);

  const onSessionExpired = useCallback(() => {
    setShowWarning(false);
    setTimeLeft(null);
  }, []);

  const { sessionInfo } = useSessionMonitor({
    autoRefresh: false,
    onSessionWarning,
    onSessionExpired
  });

  const dismissWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  return {
    showWarning,
    timeLeft,
    dismissWarning,
    sessionInfo
  };
}
