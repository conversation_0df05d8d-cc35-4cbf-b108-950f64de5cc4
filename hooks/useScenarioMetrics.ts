import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';

// Define types for scenario metrics
export interface DefaultValue {
  value: number | string | boolean;
}

export interface MetricDefaults {
  [key: string]: DefaultValue;
}

export interface ScenarioMetricsData {
  toggle_states: {
    essential?: {
      starting_age?: boolean;
      ending_age?: boolean;
      annual_income?: boolean;
      annual_expenses?: boolean;
    };
    personal?: {
      include_partner?: boolean;
    };
    income?: {
      include_superannuation?: boolean;
      income_inflation_rate_toggle?: boolean;
      partner_income_inflation_rate_toggle?: boolean;
      additional_income_inflation_rate_toggle?: boolean;
      enable_inflation?: boolean;
    };
    expense?: {
      second_expense?: boolean;
      expense1_inflation_rate_toggle?: boolean;
      expense2_inflation_rate_toggle?: boolean;
      additional_expense_inflation_rate_toggle?: boolean;
      enable_inflation?: boolean;
    };
    savings?: {
      cash_reserve?: boolean;
      saving_percentage?: boolean;
    };
    investment?: {
      utilise_excess_cashflow?: boolean;
      allocate_to_investment?: boolean;
    };
    kiwisaver?: {
      kiwisaver_contribution?: boolean;
      employer_contribution?: boolean;
      consolidate_kiwisaver?: boolean;
    };
    property?: {
      rental_income?: boolean;
      board_income?: boolean;
      interest_only_period?: boolean;
      property_inflation_rate_toggle?: boolean;
    };
    misc?: {
      show_savings?: boolean;
      show_investment?: boolean;
      show_kiwisaver?: boolean;
      show_monte_carlo?: boolean;
      show_property_value?: boolean;
      show_debt_value?: boolean;
      show_annotations?: boolean;
      show_realistic_netwealth?: boolean;
      show_cashflow?: boolean;
      num_simulations_toggle?: boolean;
      confidence_interval_toggle?: boolean;
      inflation_rate_toggle?: boolean;
    };
    [key: string]: any;
  };
  default_values: {
    personal?: {
      include_partner?: DefaultValue;
    };
    income?: {
      include_superannuation?: DefaultValue;
      income_inflation_rate?: DefaultValue;
      partner_income_inflation_rate?: DefaultValue;
      additional_income_inflation_rate?: DefaultValue;
      enable_inflation?: DefaultValue;
    };
    expense?: {
      second_expense?: DefaultValue;
      expense1_inflation_rate?: DefaultValue;
      expense2_inflation_rate?: DefaultValue;
      additional_expense_inflation_rate?: DefaultValue;
      enable_inflation?: DefaultValue;
    };
    savings?: {
      cash_reserve?: DefaultValue;
      saving_percentage?: DefaultValue;
    };
    investment?: {
      utilise_excess_cashflow?: DefaultValue;
      allocate_to_investment?: DefaultValue;
    };
    kiwisaver?: {
      kiwisaver_contribution?: DefaultValue;
      employer_contribution?: DefaultValue;
      consolidate_kiwisaver?: DefaultValue;
    };
    property?: {
      rental_income?: DefaultValue;
      board_income?: DefaultValue;
      interest_only_period?: DefaultValue;
      property_inflation_rate?: DefaultValue;
    };
    misc?: {
      show_savings?: DefaultValue;
      show_investment?: DefaultValue;
      show_kiwisaver?: DefaultValue;
      show_monte_carlo?: DefaultValue;
      show_property_value?: DefaultValue;
      show_debt_value?: DefaultValue;
      show_annotations?: DefaultValue;
      show_realistic_netwealth?: DefaultValue;
      show_cashflow?: DefaultValue;
      num_simulations?: DefaultValue;
      confidence_interval?: DefaultValue;
      inflation_rate?: DefaultValue;
    };
    [key: string]: any;
  };
  funds: Array<{
    id: string;
    name: string;
    type: 'investment' | 'kiwisaver';
    return: number;
    stdDev: number;
    color?: string;
  }>;
}

// Default values to use if no saved metrics are found
export const defaultScenarioMetrics: ScenarioMetricsData = {
  toggle_states: {
    essential: {
      starting_age: true,
      ending_age: true,
      annual_income: true,
      annual_expenses: true,
    },
    personal: {
      include_partner: true,
    },
    income: {
      include_superannuation: true,
      income_inflation_rate_toggle: true,
      partner_income_inflation_rate_toggle: true,
      additional_income_inflation_rate_toggle: true,
      enable_inflation: true,
    },
    expense: {
      second_expense: true,
      expense1_inflation_rate_toggle: true,
      expense2_inflation_rate_toggle: true,
      additional_expense_inflation_rate_toggle: true,
      enable_inflation: true,
    },
    savings: {
      cash_reserve: true,
      saving_percentage: true,
    },
    investment: {
      utilise_excess_cashflow: true,
      allocate_to_investment: true,
    },
    kiwisaver: {
      kiwisaver_contribution: true,
      employer_contribution: true,
      consolidate_kiwisaver: true,
    },
    property: {
      rental_income: true,
      board_income: true,
      interest_only_period: true,
      property_inflation_rate_toggle: true,
    },
    misc: {
      show_savings: true,
      show_investment: true,
      show_kiwisaver: true,
      show_monte_carlo: true,
      show_property_value: true,
      show_debt_value: true,
      show_annotations: true,
      show_realistic_netwealth: true,
      show_cashflow: true,
      num_simulations_toggle: true,
      confidence_interval_toggle: true,
      inflation_rate_toggle: true,
    },
  },
  default_values: {
    personal: {
      include_partner: { value: true },
    },
    income: {
      include_superannuation: { value: true },
      income_inflation_rate: { value: 2.0 },
      partner_income_inflation_rate: { value: 2.0 },
      additional_income_inflation_rate: { value: 2.0 },
      enable_inflation: { value: true },
    },
    expense: {
      second_expense: { value: true },
      expense1_inflation_rate: { value: 2.0 },
      expense2_inflation_rate: { value: 2.0 },
      additional_expense_inflation_rate: { value: 2.0 },
      enable_inflation: { value: true },
    },
    savings: {
      cash_reserve: { value: 10000 },
      saving_percentage: { value: 10 },
    },
    investment: {
      utilise_excess_cashflow: { value: true },
      allocate_to_investment: { value: true },
    },
    kiwisaver: {
      kiwisaver_contribution: { value: 3 },
      employer_contribution: { value: 3 },
      consolidate_kiwisaver: { value: false },
    },
    property: {
      rental_income: { value: 0 },
      board_income: { value: 0 },
      interest_only_period: { value: false },
      property_inflation_rate: { value: 2.0 },
    },
    misc: {
      show_savings: { value: true },
      show_investment: { value: true },
      show_kiwisaver: { value: true },
      show_monte_carlo: { value: true },
      show_property_value: { value: true },
      show_debt_value: { value: true },
      show_annotations: { value: false },
      show_realistic_netwealth: { value: true },
      show_cashflow: { value: true },
      num_simulations: { value: 1000 },
      confidence_interval: { value: 95 },
      inflation_rate: { value: 2.0 },
    },
  },
  funds: [
    // Investment funds
    {
      id: 'inv-1',
      name: 'Conservative',
      type: 'investment',
      return: 3.5,
      stdDev: 4.0,
      color: '#E2F2FF',
    },
    {
      id: 'inv-2',
      name: 'Moderate',
      type: 'investment',
      return: 4.5,
      stdDev: 6.0,
      color: '#C2E0FF',
    },
    {
      id: 'inv-3',
      name: 'Balanced',
      type: 'investment',
      return: 5.5,
      stdDev: 8.0,
      color: '#99CCF3',
    },
    {
      id: 'inv-4',
      name: 'Growth',
      type: 'investment',
      return: 6.5,
      stdDev: 10.0,
      color: '#66B2FF',
    },
    {
      id: 'inv-5',
      name: 'High Growth',
      type: 'investment',
      return: 7.5,
      stdDev: 12.0,
      color: '#3399FF',
    },
    // KiwiSaver funds
    {
      id: 'ks-1',
      name: 'Conservative',
      type: 'kiwisaver',
      return: 3.5,
      stdDev: 4.0,
      color: '#E5F5E0',
    },
    {
      id: 'ks-2',
      name: 'Moderate',
      type: 'kiwisaver',
      return: 4.5,
      stdDev: 6.0,
      color: '#C7E9C0',
    },
    {
      id: 'ks-3',
      name: 'Balanced',
      type: 'kiwisaver',
      return: 5.5,
      stdDev: 8.0,
      color: '#A1D99B',
    },
    {
      id: 'ks-4',
      name: 'Growth',
      type: 'kiwisaver',
      return: 6.5,
      stdDev: 10.0,
      color: '#74C476',
    },
    {
      id: 'ks-5',
      name: 'High Growth',
      type: 'kiwisaver',
      return: 7.5,
      stdDev: 12.0,
      color: '#41AB5D',
    }
  ]
};

// Helper function to check if a toggle is enabled
export const isToggleEnabled = (
  metrics: ScenarioMetricsData,
  category: string,
  setting: string
): boolean => {
  const toggleKey = setting.endsWith('_toggle') ? setting : `${setting}_toggle`;
  return metrics.toggle_states[category]?.[toggleKey] ?? true;
};

// Helper function to get a default value
export const getDefaultValue = <T extends number | string | boolean>(
  metrics: ScenarioMetricsData,
  category: string,
  setting: string,
  fallback: T
): T => {
  return (metrics.default_values[category]?.[setting]?.value as T) ?? fallback;
};

// Custom hook to fetch and use scenario metrics
export function useScenarioMetrics(userId?: string) {
  const [metrics, setMetrics] = useState<ScenarioMetricsData>(defaultScenarioMetrics);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchMetrics = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Check if the table exists
        const { error: tableCheckError } = await supabase
          .from('scenario_metrics')
          .select('count')
          .limit(1);

        if (tableCheckError) {
          console.log('Table may not exist yet, using default settings');
          setIsLoading(false);
          return;
        }

        // Fetch the metrics
        const { data, error } = await supabase
          .from('scenario_metrics')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching scenario metrics:', error);
          setError(new Error(error.message));
          return;
        }

        if (data) {
          // Merge with defaults to ensure all properties exist
          const mergedMetrics = {
            toggle_states: {
              ...defaultScenarioMetrics.toggle_states,
              ...data.toggle_states
            },
            default_values: {
              ...defaultScenarioMetrics.default_values,
              ...data.default_values
            },
            funds: data.funds && Array.isArray(data.funds) && data.funds.length > 0
              ? data.funds
              : defaultScenarioMetrics.funds
          };
          
          setMetrics(mergedMetrics);
        }
      } catch (err) {
        console.error('Exception fetching scenario metrics:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [userId, supabase]);

  return { metrics, isLoading, error };
}
