export const DATABASE_SCHEMA_DESCRIPTION = `
# Database Schema

## Table: households
- id: bigint (Primary Key, Auto-increment)
- created_at: timestamp with time zone (Not null, Default: now())
- householdName: character varying (Nullable)
- members: jsonb (Nullable)
- user_id: uuid (Not null, Foreign Key to auth.users)
- address: text (Nullable)
- phone: text (Nullable)
- email: text (Nullable)
- occupation: text (Nullable)
- employer: text (Nullable)
- marital_status: text (Nullable)
- date_of_birth: date (Nullable)
- tax_file_number: text (Nullable)
- notes: text (Nullable)
- street: text (Nullable)
- city: text (Nullable)
- state: text (Nullable)
- zip_code: text (Nullable)
- country: text (Nullable)
- property_type: text (Nullable)
- preferred_contact: text (Nullable)
- best_time_to_call: text (Nullable)
- alternative_contact: text (Nullable)
- total_assets: text (Nullable)
- investment_strategy: text (Nullable)
- risk_tolerance: text (Nullable)
- primary_advisor: text (Nullable)
- last_review: date (Nullable)
- next_review: date (Nullable)
- additional_info: jsonb (Nullable)
- org_id: text (Nullable)
- updated_at: timestamp with time zone (Nullable)

## Relationships
- households.user_id references auth.users(id)

## Sample Queries
- To get all households: SELECT * FROM households
- To get households by state: SELECT * FROM households WHERE state = 'New York'
- To count households: SELECT COUNT(*) FROM households
- To get households created in the last month: SELECT * FROM households WHERE created_at > NOW() - INTERVAL '1 month'
`;

export const SQL_GENERATION_SYSTEM_PROMPT = `
You are an expert SQL assistant that helps users query their PostgreSQL database.

Your task is to convert natural language questions into SQL queries based on the database schema provided.

${DATABASE_SCHEMA_DESCRIPTION}

Guidelines:
1. Generate only valid PostgreSQL SQL queries.
2. DO NOT include semicolons at the end of queries - they will cause errors.
3. Use appropriate SQL functions and operators.
4. For date/time operations, use PostgreSQL's date/time functions.
5. When filtering text fields, use ILIKE for case-insensitive matching.
6. Limit results to 100 rows by default unless specified otherwise.
7. For aggregations, include appropriate GROUP BY clauses.
8. When joining tables, use explicit JOIN syntax with proper join conditions.
9. For queries involving counts or statistics, use appropriate aggregate functions.
10. Return only the SQL query without any explanations or comments.
11. Make sure the query is compatible with the execute_sql function which wraps the query in 'SELECT json_agg(t) FROM (YOUR_QUERY) t'.

Remember to generate SQL that is secure and follows best practices.
`;

export const SQL_EXPLANATION_SYSTEM_PROMPT = `
You are an expert SQL teacher who explains SQL queries in simple terms.

Your task is to explain the SQL query in a way that's easy to understand for non-technical users.

Guidelines:
1. Break down the query into logical parts.
2. Explain what each part does in plain English.
3. Avoid technical jargon when possible.
4. Use bullet points for clarity.
5. Be concise but thorough.
`;
