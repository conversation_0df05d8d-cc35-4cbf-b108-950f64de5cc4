export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      households: {
        Row: {
          id: number
          householdName: string
          created_at: string
          created_by: string
        }
      }
      soa_documents: {
        Row: {
          id: number
          household_id: number
          created_at: string
          created_by: string
          file_name: string
          file_type: string
          file_size: number
          file_url?: string
        }
        Insert: {
          id?: number
          household_id: number
          created_at?: string
          created_by: string
          file_name: string
          file_type: string
          file_size: number
          file_url?: string
        }
      }
      toe_tokens: {
        Row: {
          id: number
          household_id: number
          token: string
          created_at: string
          created_by: string
          expires_at: string
          status: string
          completed_at?: string
          client_name: string
          signature?: string
          signature_date?: string
          ip_address?: string
        }
        Insert: {
          id?: number
          household_id: number
          token: string
          created_at?: string
          created_by: string
          expires_at: string
          status?: string
          completed_at?: string
          client_name: string
          signature?: string
          signature_date?: string
          ip_address?: string
        }
      }
      discovery_tokens: {
        Row: {
          id: number
          household_id: number
          token: string
          created_at: string
          created_by: string
          expires_at: string
          status: string
          completed_at?: string
        }
        Insert: {
          id?: number
          household_id: number
          token: string
          created_at?: string
          created_by: string
          expires_at: string
          status?: string
          completed_at?: string
        }
      }
      risk_profiler_tokens: {
        Row: {
          id: number
          household_id: number
          token: string
          created_at: string
          created_by: string
          expires_at: string
          status: string
          completed_at?: string
          score?: number
          responses?: Json
        }
        Insert: {
          id?: number
          household_id: number
          token: string
          created_at?: string
          created_by: string
          expires_at: string
          status?: string
          completed_at?: string
          score?: number
          responses?: Json
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
