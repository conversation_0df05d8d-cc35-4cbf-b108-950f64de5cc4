import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

// Renaming 'cn' to 'cx' as it seems to be the intended use in barList.tsx
export function cx(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Adding a standard focus ring utility class string
export const focusRing = "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900";

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
  }).format(amount);
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
