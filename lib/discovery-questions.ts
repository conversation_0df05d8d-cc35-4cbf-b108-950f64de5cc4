export interface DiscoverySection {
  id: string;
  title: string;
  questions: DiscoveryQuestion[];
}

export interface DiscoveryQuestion {
  mapping: string;
  id: string;
  question: string;
  type: 'text' | 'number' | 'select' | 'date' | 'radio' | 'textarea' | 'currency' | 'dynamic-list';
  options?: string[];
  subType?: string;
  defaultValue?: any;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export const discoveryQuestions: DiscoverySection[] = [
  {
    id: 'personal',
    title: 'Personal Information',
    questions: [
      {
        id: 'client_name',
        question: 'Main Client First Name',
        type: 'text',
        validation: { required: true },
        defaultValue: 'John',
        mapping: ""
      },
      {
        id: 'client_last_name',
        question: 'Main Client Last Name',
        type: 'text',
        validation: { required: true },
        defaultValue: 'Smith',
        mapping: ""
      },
      {
        id: 'partner_name',
        question: 'Partner First Name',
        type: 'text',
        defaultValue: 'Jane',
        mapping: ""
      },
      {
        id: 'partner_last_name',
        question: 'Partner Last Name',
        type: 'text',
        defaultValue: 'Smith',
        mapping: ""
      },
      {
        id: 'date_of_birth',
        question: 'Main Client Date of Birth',
        type: 'date',
        validation: { required: true },
        defaultValue: '1980-01-15',
        mapping: ""
      },
      {
        id: 'partner_date_of_birth',
        question: 'Partner Date of Birth',
        type: 'date',
        defaultValue: '1982-03-20',
        mapping: ""
      },
      {
        id: 'address',
        question: 'Residential Address',
        type: 'textarea',
        validation: { required: true },
        defaultValue: '123 Main Street\nWellington 6011',
        mapping: ""
      },
      {
        id: 'postal_address',
        question: 'Postal Address (if different)',
        type: 'textarea',
        defaultValue: 'PO Box 456\nWellington 6140',
        mapping: ""
      },
      {
        id: 'phone',
        question: 'Main Client Contact Phone',
        type: 'text',
        validation: { required: true },
        defaultValue: '************',
        mapping: ""
      },
      {
        id: 'partner_phone',
        question: 'Partner Contact Phone',
        type: 'text',
        defaultValue: '************',
        mapping: ""
      },
      {
        id: 'email',
        question: 'Main Client Email Address',
        type: 'text',
        validation: { required: true, pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' },
        defaultValue: '<EMAIL>',
        mapping: ""
      },
      {
        id: 'partner_email',
        question: 'Partner Email Address',
        type: 'text',
        validation: { pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' },
        defaultValue: '<EMAIL>',
        mapping: ""
      },
      {
        id: 'tax_residency',
        question: 'Main Client Tax Residency',
        type: 'text',
        validation: { required: true },
        defaultValue: 'New Zealand',
        mapping: ""
      },
      {
        id: 'partner_tax_residency',
        question: 'Partner Tax Residency',
        type: 'text',
        defaultValue: 'New Zealand',
        mapping: ""
      },
      {
        id: 'tax_number',
        question: 'Main Client IRD Number',
        type: 'text',
        validation: { required: true },
        defaultValue: '***********',
        mapping: ""
      },
      {
        id: 'partner_tax_number',
        question: 'Partner IRD Number',
        type: 'text',
        defaultValue: '***********',
        mapping: ""
      },
      {
        id: 'citizenship',
        question: 'Main Client Citizenship/Residency Status',
        type: 'text',
        validation: { required: true },
        defaultValue: 'New Zealand Citizen',
        mapping: ""
      },
      {
        id: 'partner_citizenship',
        question: 'Partner Citizenship/Residency Status',
        type: 'text',
        defaultValue: 'New Zealand Citizen',
        mapping: ""
      }
    ]
  },
  {
    id: 'relationships',
    title: 'Family and Professional Relationships',
    questions: [
      {
        id: 'marital_status',
        question: 'Marital Status',
        type: 'select',
        options: ['Single', 'Married', 'De Facto', 'Separated', 'Divorced', 'Widowed'],
        validation: { required: true },
        defaultValue: 'Married',
        mapping: ""
      },
      {
        id: 'children',
        question: 'Children',
        type: 'dynamic-list',
        subType: 'child',
        mapping: ""
      },
      {
        id: 'dependents',
        question: 'Other Dependents',
        type: 'dynamic-list',
        subType: 'dependent',
        mapping: ""
      },
      {
        id: 'professional_advisors',
        question: 'Professional Advisors',
        type: 'dynamic-list',
        subType: 'advisor',
        mapping: ""
      }
    ]
  },
  {
    id: 'employment',
    title: 'Employment Details',
    questions: [
      {
        id: 'employment_status',
        question: '{MainName} Employment Status',
        type: 'select',
        options: ['Full-time', 'Part-time', 'Self-employed', 'Contractor', 'Unemployed', 'Retired'],
        validation: { required: true },
        defaultValue: 'Full-time',
        mapping: ""
      },
      {
        id: 'occupation',
        question: '{MainName} Occupation',
        type: 'text',
        validation: { required: true },
        defaultValue: 'Software Engineer',
        mapping: ""
      },
      {
        id: 'employer',
        question: '{MainName} Employer Name',
        type: 'text',
        defaultValue: 'Tech Solutions Ltd',
        mapping: ""
      },
      {
        id: 'years_with_employer',
        question: 'Years with Current Employer',
        type: 'number',
        defaultValue: 5,
        mapping: ""
      },
      {
        id: 'partner_employment_status',
        question: '{PartnerName} Employment Status',
        type: 'select',
        options: ['Full-time', 'Part-time', 'Self-employed', 'Contractor', 'Unemployed', 'Retired'],
        defaultValue: 'Part-time',
        mapping: ""
      },
      {
        id: 'partner_occupation',
        question: '{PartnerName} Occupation',
        type: 'text',
        defaultValue: 'Marketing Specialist',
        mapping: ""
      },
      {
        id: 'partner_employer',
        question: '{PartnerName} Employer Name',
        type: 'text',
        defaultValue: 'Creative Marketing NZ',
        mapping: ""
      }
    ]
  },
  {
    id: 'goals',
    title: 'Goals and Objectives',
    questions: [
      {
        id: 'financial_priorities',
        question: 'What are your top financial priorities?',
        type: 'select',
        options: [
          'Retirement Planning',
          'Debt Reduction',
          'Wealth Building',
          'Education Funding',
          'Estate Planning',
          'Tax Planning',
          'Insurance Coverage',
          'Property Purchase',
          'Travel',
          'Business Goals',
          'Other'
        ],
        validation: { required: true },
        defaultValue: 'Retirement Planning',
        mapping: ""
      },
      {
        id: 'short_term_goals',
        question: 'What are your short-term financial goals (0-2 years)?',
        type: 'dynamic-list',
        subType: 'goal',
        mapping: ""
      },
      {
        id: 'medium_term_goals',
        question: 'What are your medium-term financial goals (2-5 years)?',
        type: 'dynamic-list',
        subType: 'goal',
        mapping: ""
      },
      {
        id: 'long_term_goals',
        question: 'What are your long-term financial goals (5+ years)?',
        type: 'dynamic-list',
        subType: 'goal',
        mapping: ""
      },
      {
        id: 'retirement_age',
        question: 'At what age does {MainName} plan to retire?',
        type: 'number',
        validation: { min: 0, max: 100 },
        defaultValue: 65,
        mapping: ""
      },
      {
        id: 'partner_retirement_age',
        question: 'At what age does {PartnerName} plan to retire?',
        type: 'number',
        validation: { min: 0, max: 100 },
        defaultValue: 65,
        mapping: ""
      },
      {
        id: 'retirement_income',
        question: 'What annual income do you need in retirement?',
        type: 'currency',
        validation: { required: true },
        defaultValue: 80000,
        mapping: ""
      },
      {
        id: 'legacy_goals',
        question: 'Do you have any legacy or estate planning goals?',
        type: 'textarea',
        defaultValue: 'Provide for children and grandchildren. Support local community charities.',
        mapping: ""
      }
    ]
  },
  {
    id: 'income',
    title: 'Income',
    questions: [
      {
        id: 'employment_income',
        question: '{MainName} Employment Income',
        type: 'currency',
        validation: { required: true },
        defaultValue: 95000,
        mapping: ""
      },
      {
        id: 'employment_income_frequency',
        question: '{MainName} Income Frequency',
        type: 'select',
        options: ['Annually', 'Monthly', 'Fortnightly', 'Weekly'],
        validation: { required: true },
        defaultValue: 'Annually',
        mapping: ""
      },
      {
        id: 'partner_employment_income',
        question: '{PartnerName} Employment Income',
        type: 'currency',
        defaultValue: 65000,
        mapping: ""
      },
      {
        id: 'partner_employment_income_frequency',
        question: '{PartnerName} Income Frequency',
        type: 'select',
        options: ['Annually', 'Monthly', 'Fortnightly', 'Weekly'],
        defaultValue: 'Annually',
        mapping: ""
      },
      {
        id: 'business_income',
        question: 'Business Income (if self-employed)',
        type: 'currency',
        defaultValue: 0,
        mapping: ""
      },
      {
        id: 'rental_income',
        question: 'Rental Income',
        type: 'currency',
        defaultValue: 25000,
        mapping: ""
      },
      {
        id: 'investment_income',
        question: 'Investment Income',
        type: 'currency',
        defaultValue: 8000,
        mapping: ""
      },
      {
        id: 'government_benefits',
        question: 'Government Benefits',
        type: 'currency',
        defaultValue: 0,
        mapping: ""
      },
      {
        id: 'other_income',
        question: 'Other Income Sources',
        type: 'dynamic-list',
        subType: 'income',
        defaultValue: [],
        mapping: ""
      }
    ]
  },
  {
    id: 'expenses',
    title: 'Expenses',
    questions: [
      {
        id: 'housing_expenses',
        question: 'Housing Expenses (Mortgage/Rent)',
        type: 'currency',
        validation: { required: true },
        defaultValue: 24000,
        mapping: ""
      },
      {
        id: 'utilities',
        question: 'Utilities (Power, Water, Internet, etc.)',
        type: 'currency',
        validation: { required: true },
        defaultValue: 6000,
        mapping: ""
      },
      {
        id: 'groceries',
        question: 'Groceries and Food',
        type: 'currency',
        validation: { required: true },
        defaultValue: 12000,
        mapping: ""
      },
      {
        id: 'transport',
        question: 'Transport Expenses',
        type: 'currency',
        validation: { required: true },
        defaultValue: 8000,
        mapping: ""
      },
      {
        id: 'insurance_premiums',
        question: 'Insurance Premiums',
        type: 'currency',
        validation: { required: true },
        defaultValue: 5000,
        mapping: ""
      },
      {
        id: 'debt_repayments',
        question: 'Debt Repayments',
        type: 'currency',
        defaultValue: 6000,
        mapping: ""
      },
      {
        id: 'living_expenses',
        question: 'Other Living Expenses',
        type: 'dynamic-list',
        subType: 'expense',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'discretionary_expenses',
        question: 'Discretionary Expenses',
        type: 'dynamic-list',
        subType: 'expense',
        defaultValue: [],
        mapping: ""
      }
    ]
  },
  {
    id: 'property',
    title: 'Property',
    questions: [
      {
        id: 'primary_residence',
        question: 'Primary Residence',
        type: 'dynamic-list',
        subType: 'property',
        mapping: ""
      },
      {
        id: 'investment_properties',
        question: 'Investment Properties',
        type: 'dynamic-list',
        subType: 'property',
        mapping: ""
      },
      {
        id: 'property_intentions',
        question: 'Future Property Purchase Intentions',
        type: 'textarea',
        defaultValue: 'Planning to purchase an investment property within the next 3 years.',
        mapping: ""
      },
      {
        id: 'property_notes',
        question: 'Additional Notes on Property',
        type: 'textarea',
        defaultValue: 'Looking for properties in growing suburbs with good rental yield.',
        mapping: ""
      }
    ]
  },
  {
    id: 'assets',
    title: 'Assets & Investments',
    questions: [
      {
        id: 'investment_assets',
        question: 'Investment Assets',
        type: 'dynamic-list',
        subType: 'investment',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'savings_accounts',
        question: 'Savings & Cash Accounts',
        type: 'dynamic-list',
        subType: 'savings',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'superannuation',
        question: 'Superannuation',
        type: 'dynamic-list',
        subType: 'superannuation',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'other_assets',
        question: 'Other Assets',
        type: 'dynamic-list',
        subType: 'other_asset',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'asset_notes',
        question: 'Additional Notes on Assets',
        type: 'textarea',
        defaultValue: 'Focusing on building liquid assets for medium-term goals.',
        mapping: ""
      }
    ]
  },
  {
    id: 'liabilities',
    title: 'Liabilities',
    questions: [
      {
        id: 'mortgages',
        question: 'Mortgages',
        type: 'dynamic-list',
        subType: 'mortgage',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'personal_loans',
        question: 'Personal Loans',
        type: 'dynamic-list',
        subType: 'loan',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'credit_cards',
        question: 'Credit Cards',
        type: 'dynamic-list',
        subType: 'credit',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'other_debts',
        question: 'Other Debts',
        type: 'dynamic-list',
        subType: 'debt',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'debt_strategy',
        question: 'Debt Reduction Strategy',
        type: 'textarea',
        defaultValue: 'Focusing on high-interest debt first while maintaining minimum payments on all other debts.',
        mapping: ""
      }
    ]
  },
  {
    id: 'kiwisaver',
    title: 'KiwiSaver',
    questions: [
      {
        id: 'kiwisaver_member',
        question: 'Is {MainName} a KiwiSaver member?',
        type: 'radio',
        options: ['Yes', 'No'],
        validation: { required: true },
        defaultValue: 'Yes',
        mapping: ""
      },
      {
        id: 'kiwisaver_provider',
        question: 'KiwiSaver Provider',
        type: 'text',
        defaultValue: 'ANZ KiwiSaver',
        mapping: ""
      },
      {
        id: 'kiwisaver_fund_type',
        question: 'KiwiSaver Fund Type',
        type: 'select',
        options: ['Conservative', 'Balanced', 'Growth', 'Aggressive', 'Other'],
        defaultValue: 'Balanced',
        mapping: ""
      },
      {
        id: 'kiwisaver_balance',
        question: 'Current KiwiSaver Balance',
        type: 'currency',
        defaultValue: 85000,
        mapping: ""
      },
      {
        id: 'kiwisaver_contribution_rate',
        question: 'Your Contribution Rate',
        type: 'select',
        options: ['3%', '4%', '6%', '8%', '10%', 'Not contributing'],
        defaultValue: '3%',
        mapping: ""
      },
      {
        id: 'employer_contribution_rate',
        question: 'Employer Contribution Rate',
        type: 'select',
        options: ['3%', '4%', '6%', '8%', '10%', 'Not contributing'],
        defaultValue: '3%',
        mapping: ""
      },
      {
        id: 'partner_kiwisaver_member',
        question: 'Is {PartnerName} a KiwiSaver member?',
        type: 'radio',
        options: ['Yes', 'No'],
        defaultValue: 'Yes',
        mapping: ""
      },
      {
        id: 'partner_kiwisaver_provider',
        question: '{PartnerName} KiwiSaver Provider',
        type: 'text',
        defaultValue: 'Kiwi Wealth',
        mapping: ""
      },
      {
        id: 'partner_kiwisaver_fund_type',
        question: '{PartnerName} KiwiSaver Fund Type',
        type: 'select',
        options: ['Conservative', 'Balanced', 'Growth', 'Aggressive', 'Other'],
        defaultValue: 'Growth',
        mapping: ""
      },
      {
        id: 'partner_kiwisaver_balance',
        question: '{PartnerName} Current KiwiSaver Balance',
        type: 'currency',
        defaultValue: 65000,
        mapping: ""
      },
      {
        id: 'partner_kiwisaver_contribution_rate',
        question: '{PartnerName} Contribution Rate',
        type: 'select',
        options: ['3%', '4%', '6%', '8%', '10%', 'Not contributing'],
        defaultValue: '3%',
        mapping: ""
      },
      {
        id: 'kiwisaver_first_home',
        question: 'Do you plan to use KiwiSaver for first home purchase?',
        type: 'radio',
        options: ['Yes', 'No', 'Already used', 'Not applicable'],
        defaultValue: 'No',
        mapping: ""
      }
    ]
  },
  {
    id: 'investments',
    title: 'Investments',
    questions: [
      {
        id: 'investment_experience',
        question: 'Investment Experience Level',
        type: 'select',
        options: ['None', 'Limited', 'Moderate', 'Experienced'],
        validation: { required: true },
        defaultValue: 'Moderate',
        mapping: ""
      },
      {
        id: 'risk_profile',
        question: 'Risk Profile',
        type: 'select',
        options: ['Conservative', 'Moderately Conservative', 'Balanced', 'Moderately Aggressive', 'Aggressive'],
        validation: { required: true },
        defaultValue: 'Balanced',
        mapping: ""
      },
      {
        id: 'shares',
        question: 'Share Investments',
        type: 'dynamic-list',
        subType: 'share',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'managed_funds',
        question: 'Managed Funds',
        type: 'dynamic-list',
        subType: 'managed_fund',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'term_deposits',
        question: 'Term Deposits',
        type: 'dynamic-list',
        subType: 'term_deposit',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'other_investments',
        question: 'Other Investments',
        type: 'dynamic-list',
        subType: 'investment',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'investment_goals',
        question: 'Investment Goals',
        type: 'textarea',
        defaultValue: 'Building wealth for retirement and creating passive income streams.',
        mapping: ""
      }
    ]
  },
  {
    id: 'insurance',
    title: 'Insurance',
    questions: [
      {
        id: 'life_insurance',
        question: 'Life Insurance',
        type: 'dynamic-list',
        subType: 'life_insurance',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'health_insurance',
        question: 'Health Insurance',
        type: 'dynamic-list',
        subType: 'health_insurance',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'income_protection',
        question: 'Income Protection',
        type: 'dynamic-list',
        subType: 'income_protection',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'trauma_insurance',
        question: 'Trauma Insurance',
        type: 'dynamic-list',
        subType: 'trauma_insurance',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'tpd_insurance',
        question: 'Total & Permanent Disability Insurance',
        type: 'dynamic-list',
        subType: 'tpd_insurance',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'general_insurance',
        question: 'General Insurance (Home, Contents, Vehicle)',
        type: 'dynamic-list',
        subType: 'general_insurance',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'insurance_notes',
        question: 'Additional Notes on Insurance',
        type: 'textarea',
        defaultValue: 'Reviewing coverage annually to ensure adequate protection for family.',
        mapping: ""
      }
    ]
  },
  {
    id: 'estate',
    title: 'Estate Planning',
    questions: [
      {
        id: 'will_status',
        question: 'Do you have a current Will?',
        type: 'radio',
        options: ['Yes', 'No', 'Not Sure'],
        validation: { required: true },
        defaultValue: 'Yes',
        mapping: ""
      },
      {
        id: 'will_date',
        question: 'Date Will was last updated',
        type: 'date',
        defaultValue: '2020-05-10',
        mapping: ""
      },
      {
        id: 'power_of_attorney',
        question: 'Do you have a Power of Attorney?',
        type: 'radio',
        options: ['Yes', 'No', 'Not Sure'],
        validation: { required: true },
        defaultValue: 'Yes',
        mapping: ""
      },
      {
        id: 'enduring_power_of_attorney',
        question: 'Do you have an Enduring Power of Attorney?',
        type: 'radio',
        options: ['Yes', 'No', 'Not Sure'],
        defaultValue: 'Yes',
        mapping: ""
      },
      {
        id: 'executor',
        question: 'Who is the executor of your Will?',
        type: 'text',
        defaultValue: 'Partner and Brother (James Smith)',
        mapping: ""
      },
      {
        id: 'beneficiaries',
        question: 'Beneficiaries',
        type: 'dynamic-list',
        subType: 'beneficiary',
        defaultValue: [],
        mapping: ""
      },
      {
        id: 'estate_concerns',
        question: 'Do you have any specific estate planning concerns?',
        type: 'textarea',
        defaultValue: 'Ensuring assets are distributed according to wishes and minimizing tax implications.',
        mapping: ""
      }
    ]
  },
  {
    id: 'risk_assessment',
    title: 'Risk Assessment',
    questions: [
      {
        id: 'risk_notes',
        question: 'Additional Notes on Risk Profile',
        type: 'textarea',
        defaultValue: 'Comfortable with moderate volatility for long-term growth potential.',
        mapping: ""
      }
    ]
  },
  {
    id: 'additional_info',
    title: 'Additional Information',
    questions: [
      {
        id: 'health_concerns',
        question: 'Any health concerns that may impact financial planning?',
        type: 'textarea',
        defaultValue: 'No significant health concerns at this time.',
        mapping: ""
      },
      {
        id: 'special_circumstances',
        question: 'Any special circumstances to consider?',
        type: 'textarea',
        defaultValue: 'Planning to support aging parents in the next 5-10 years.',
        mapping: ""
      },
      {
        id: 'other_notes',
        question: 'Any other information you would like to share?',
        type: 'textarea',
        defaultValue: 'Interested in ethical investment options and sustainable financial planning.',
        mapping: ""
      }
    ]
  }
];
