// Custom events for workflow nodes

// Event name constants
export const NODE_UPDATED_EVENT = 'workflow:node-updated';
export const EDGE_CREATED_EVENT = 'workflow:edge-created';
export const EDGE_REMOVED_EVENT = 'workflow:edge-removed';

// Event data interfaces
export interface NodeUpdatedEventData {
  nodeId: string;
  nodeType: string;
  field: string;
  value: any;
}

export interface EdgeEventData {
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

// Event emitter functions
export function emitNodeUpdated(data: NodeUpdatedEventData) {
  const event = new CustomEvent<NodeUpdatedEventData>(NODE_UPDATED_EVENT, { 
    detail: data,
    bubbles: true 
  });
  window.dispatchEvent(event);
}

export function emitEdgeCreated(data: EdgeEventData) {
  const event = new CustomEvent<EdgeEventData>(EDGE_CREATED_EVENT, { 
    detail: data,
    bubbles: true 
  });
  window.dispatchEvent(event);
}

export function emitEdgeRemoved(data: EdgeEventData) {
  const event = new CustomEvent<EdgeEventData>(EDGE_REMOVED_EVENT, { 
    detail: data,
    bubbles: true 
  });
  window.dispatchEvent(event);
}
