import { Node, Position } from '@xyflow/react';

// Helper function to get the center of a node
function getNodeCenter(node: Node) {
  return {
    x: node.position.x + (node.width || 0) / 2,
    y: node.position.y + (node.height || 0) / 2,
  };
}

// Helper function to get the position of a handle
function getHandlePosition(node: Node, handleId: string | null) {
  // Default to center if no handle is specified
  if (!handleId) {
    return getNodeCenter(node);
  }

  // Get the handle position based on its ID
  if (handleId === 'notification-top' || handleId === 'top' || handleId === 'target') {
    return {
      x: node.position.x + (node.width || 0) / 2,
      y: node.position.y,
    };
  } else if (handleId === 'notification-bottom' || handleId === 'bottom') {
    return {
      x: node.position.x + (node.width || 0) / 2,
      y: node.position.y + (node.height || 0),
    };
  } else if (handleId === 'notification-handle-top') {
    return {
      x: node.position.x + (node.width || 0) / 2,
      y: node.position.y,
    };
  } else if (handleId === 'source-top') {
    return {
      x: node.position.x + (node.width || 0) / 2,
      y: node.position.y,
    };
  } else if (handleId === 'source-bottom') {
    return {
      x: node.position.x + (node.width || 0) / 2,
      y: node.position.y + (node.height || 0),
    };
  }

  // Default to center for other handles
  return getNodeCenter(node);
}

// Function to get edge parameters for floating edges
export function getEdgeParams(source: Node, target: Node, sourceHandle?: string | null, targetHandle?: string | null) {
  const sourcePos = getHandlePosition(source, sourceHandle || null);
  const targetPos = getHandlePosition(target, targetHandle || null);

  return {
    sx: sourcePos.x,
    sy: sourcePos.y,
    tx: targetPos.x,
    ty: targetPos.y,
  };
}
