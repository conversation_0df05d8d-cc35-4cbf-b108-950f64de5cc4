export interface CardPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface CardLayout {
  id: string;
  position: CardPosition;
}

export const GRID_SIZE = 20;
export const MIN_WIDTH = 600;
export const MIN_HEIGHT = 500;
export const CONTAINER_PADDING = 20;
export const CONTAINER_WIDTH = 1240;
export const CARD_GAP = 20;

export function snapToGrid(value: number): number {
  return Math.round(value / GRID_SIZE) * GRID_SIZE;
}

export function isOverlapping(rect1: CardPosition, rect2: CardPosition): boolean {
  return !(
    rect1.x + rect1.width + CARD_GAP <= rect2.x ||
    rect1.x >= rect2.x + rect2.width + CARD_GAP ||
    rect1.y + rect1.height + CARD_GAP <= rect2.y ||
    rect1.y >= rect2.y + rect2.height + CARD_GAP
  );
}

export function findOverlappingCards(activeCard: CardLayout, allCards: CardLayout[]): CardLayout[] {
  return allCards.filter(card => 
    card.id !== activeCard.id && 
    isOverlapping(activeCard.position, card.position)
  );
}

export function adjustOverlappingCards(
  activeCard: CardLayout,
  allCards: CardLayout[],
  containerWidth: number = CONTAINER_WIDTH
): CardLayout[] {
  const updatedCards = [...allCards];
  const overlappingCards = findOverlappingCards(activeCard, allCards);
  
  if (overlappingCards.length === 0) {
    return updatedCards;
  }

  // Group cards by row
  const cardsByRow = new Map<number, CardLayout[]>();
  overlappingCards.forEach(card => {
    const row = snapToGrid(card.position.y);
    if (!cardsByRow.has(row)) {
      cardsByRow.set(row, []);
    }
    cardsByRow.get(row)!.push(card);
  });

  // Handle each row of overlapping cards
  cardsByRow.forEach((cardsInRow, rowY) => {
    // If active card is in this row
    if (snapToGrid(activeCard.position.y) === rowY) {
      // Adjust cards horizontally
      const availableWidth = containerWidth - CONTAINER_PADDING * 2 - activeCard.position.width;
      const widthPerCard = Math.max(MIN_WIDTH, availableWidth / cardsInRow.length);
      
      let currentX = activeCard.position.x + activeCard.position.width + CARD_GAP;
      cardsInRow.forEach(card => {
        const cardIndex = updatedCards.findIndex(c => c.id === card.id);
        if (cardIndex !== -1) {
          updatedCards[cardIndex] = {
            ...card,
            position: {
              ...card.position,
              x: currentX,
              width: widthPerCard
            }
          };
          currentX += widthPerCard + CARD_GAP;
        }
      });
    } else {
      // Move cards to next row
      const nextY = rowY + MIN_HEIGHT + CARD_GAP;
      let currentX = CONTAINER_PADDING;
      
      cardsInRow.forEach(card => {
        const cardIndex = updatedCards.findIndex(c => c.id === card.id);
        if (cardIndex !== -1) {
          updatedCards[cardIndex] = {
            ...card,
            position: {
              ...card.position,
              x: currentX,
              y: nextY
            }
          };
          currentX += card.position.width + CARD_GAP;
        }
      });
    }
  });

  return updatedCards;
}

export function getAvailablePosition(
  cardSize: { width: number; height: number },
  existingCards: CardLayout[],
  containerWidth: number = CONTAINER_WIDTH
): { x: number; y: number } {
  let y = CONTAINER_PADDING;
  let found = false;

  while (!found) {
    let x = CONTAINER_PADDING;
    
    // Check if this position works
    const testPosition: CardPosition = { x, y, ...cardSize };
    const wouldOverlap = existingCards.some(card => 
      isOverlapping(testPosition, card.position)
    );

    if (!wouldOverlap) {
      found = true;
      return { x, y };
    }

    // Try next row
    y += MIN_HEIGHT + CARD_GAP;
  }

  return { x: CONTAINER_PADDING, y };
}
