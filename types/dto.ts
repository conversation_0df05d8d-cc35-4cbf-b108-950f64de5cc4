/**
 * Data Transfer Objects (DTOs)
 *
 * Defines safe data structures that limit exposure of sensitive information
 * and provide type safety for API responses and database queries.
 */

/**
 * Base DTO interface with common fields
 */
export interface BaseDTO {
  id: string;
  created_at: string;
  updated_at?: string;
}

/**
 * Household DTOs
 */
export interface HouseholdSummaryDTO extends BaseDTO {
  householdName: string;
  memberCount: number;
  city?: string;
  state?: string;
  primary_advisor?: string;
  last_review?: string;
  next_review?: string;
}

export interface HouseholdDetailDTO extends HouseholdSummaryDTO {
  address?: string;
  street?: string;
  zip_code?: string;
  country?: string;
  marital_status?: string;
  preferred_contact?: string;
  best_time_to_call?: string;
  alternative_contact?: string;
  members?: HouseholdMembersDTO;
  // Exclude sensitive fields like internal_notes, admin_flags
}

export interface HouseholdMembersDTO {
  // Primary member
  name1?: string;
  last_name1?: string;
  age1?: string;
  income1?: string;
  occupation1?: string;
  employer1?: string;
  email1?: string;
  phone1?: string;
  date_of_birth1?: string;
  citizenship1?: string;
  tax_residency1?: string;
  employment_status1?: string;
  
  // Secondary member
  name2?: string;
  last_name2?: string;
  age2?: string;
  income2?: string;
  occupation2?: string;
  employer2?: string;
  email2?: string;
  phone2?: string;
  date_of_birth2?: string;
  citizenship2?: string;
  tax_residency2?: string;
  employment_status2?: string;
  
  // Financial data
  annualExpenses?: number;
  propertyValue?: number;
  debtValue?: number;
  investmentValue?: number;
  savingsValue?: number;
  riskProfile?: string;
  
  // KiwiSaver data
  kiwisaverValue1?: string;
  kiwisaverContribution1?: string;
  employerContribution1?: string;
  kiwisaverProfile1?: string;
  kiwisaverValue2?: string;
  kiwisaverContribution2?: string;
  employerContribution2?: string;
  kiwisaverProfile2?: string;
  
  // Exclude sensitive fields like tax_file_number1, tax_file_number2
}

/**
 * Asset DTOs
 */
export interface AssetSummaryDTO extends BaseDTO {
  name: string;
  value: number;
  asset_type: string;
  household_id: string;
}

export interface AssetDetailDTO extends AssetSummaryDTO {
  description?: string;
  purchase_date?: string;
  current_value?: number;
  // Exclude sensitive fields like account_numbers, passwords
}

/**
 * Liability DTOs
 */
export interface LiabilitySummaryDTO extends BaseDTO {
  name: string;
  value: number;
  liability_type: string;
  household_id: string;
}

export interface LiabilityDetailDTO extends LiabilitySummaryDTO {
  description?: string;
  interest_rate?: number;
  monthly_payment?: number;
  maturity_date?: string;
  // Exclude sensitive fields like account_numbers, loan_details
}

/**
 * Income DTOs
 */
export interface IncomeSummaryDTO extends BaseDTO {
  amount: number;
  frequency: string;
  source: string;
  household_id: string;
}

export interface IncomeDetailDTO extends IncomeSummaryDTO {
  description?: string;
  start_date?: string;
  end_date?: string;
  is_guaranteed?: boolean;
}

/**
 * Expense DTOs
 */
export interface ExpenseSummaryDTO extends BaseDTO {
  amount: number;
  frequency: string;
  category: string;
  household_id: string;
}

export interface ExpenseDetailDTO extends ExpenseSummaryDTO {
  description?: string;
  is_essential?: boolean;
  notes?: string;
}

/**
 * Profile DTOs
 */
export interface ProfileSummaryDTO extends BaseDTO {
  name?: string;
  org_role?: string;
  org_id?: string;
  // Exclude sensitive fields like mfa_secret, recovery_codes, password_hash
}

export interface ProfileDetailDTO extends ProfileSummaryDTO {
  email?: string;
  phone?: string;
  last_login?: string;
  mfa_enabled?: boolean;
  // Still exclude actual secrets and sensitive data
}

/**
 * Relationship DTOs
 */
export interface RelationshipSummaryDTO extends BaseDTO {
  name: string;
  relationship_type: 'family' | 'professional';
  household_id: string;
}

export interface FamilyMemberDTO extends RelationshipSummaryDTO {
  family_relationship?: string;
  phone?: string;
  email?: string;
  notes?: string;
}

export interface ProfessionalContactDTO extends RelationshipSummaryDTO {
  profession?: string;
  phone?: string;
  email?: string;
  notes?: string;
}

/**
 * Scenario DTOs
 */
export interface ScenarioSummaryDTO extends BaseDTO {
  name: string;
  scenario_type: string;
  household_id: string;
  status?: string;
}

export interface ScenarioDetailDTO extends ScenarioSummaryDTO {
  description?: string;
  parameters?: any; // Validated JSON
  results?: any; // Validated JSON
  // Exclude sensitive calculation details
}

/**
 * Note DTOs
 */
export interface NoteSummaryDTO extends BaseDTO {
  title: string;
  household_id: string;
  created_by: string;
}

export interface NoteDetailDTO extends NoteSummaryDTO {
  content: string;
  note_type?: string;
  is_private?: boolean;
  // Exclude admin-only notes or sensitive content
}

/**
 * Report DTOs
 */
export interface ReportSummaryDTO extends BaseDTO {
  name: string;
  report_type: string;
  created_by: string;
}

export interface ReportDetailDTO extends ReportSummaryDTO {
  description?: string;
  parameters?: any; // Validated JSON
  last_run?: string;
  // Exclude sensitive report data or admin configurations
}

/**
 * API Response DTOs
 */
export interface APIResponseDTO<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ValidationErrorDTO {
  field: string;
  message: string;
  code: string;
}

export interface APIErrorResponseDTO {
  success: false;
  error: string;
  details?: ValidationErrorDTO[];
  timestamp: string;
  path: string;
}

/**
 * Search and Filter DTOs
 */
export interface SearchParamsDTO {
  query?: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface SearchResultDTO<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * Utility functions for DTO conversion
 */

/**
 * Convert household database record to summary DTO
 */
export function toHouseholdSummaryDTO(household: any): HouseholdSummaryDTO {
  return {
    id: household.id,
    householdName: household.householdName || '',
    memberCount: household.members ? 
      (household.members.name1 ? 1 : 0) + (household.members.name2 ? 1 : 0) : 0,
    city: household.city,
    state: household.state,
    primary_advisor: household.primary_advisor,
    last_review: household.last_review,
    next_review: household.next_review,
    created_at: household.created_at,
    updated_at: household.updated_at
  };
}

/**
 * Convert household database record to detail DTO
 */
export function toHouseholdDetailDTO(household: any): HouseholdDetailDTO {
  const summary = toHouseholdSummaryDTO(household);
  
  return {
    ...summary,
    address: household.address,
    street: household.street,
    zip_code: household.zip_code,
    country: household.country,
    marital_status: household.marital_status,
    preferred_contact: household.preferred_contact,
    best_time_to_call: household.best_time_to_call,
    alternative_contact: household.alternative_contact,
    members: household.members ? {
      name1: household.members.name1,
      last_name1: household.members.last_name1,
      age1: household.members.age1,
      income1: household.members.income1,
      occupation1: household.members.occupation1,
      employer1: household.members.employer1,
      email1: household.members.email1,
      phone1: household.members.phone1,
      date_of_birth1: household.members.date_of_birth1,
      citizenship1: household.members.citizenship1,
      tax_residency1: household.members.tax_residency1,
      employment_status1: household.members.employment_status1,
      
      name2: household.members.name2,
      last_name2: household.members.last_name2,
      age2: household.members.age2,
      income2: household.members.income2,
      occupation2: household.members.occupation2,
      employer2: household.members.employer2,
      email2: household.members.email2,
      phone2: household.members.phone2,
      date_of_birth2: household.members.date_of_birth2,
      citizenship2: household.members.citizenship2,
      tax_residency2: household.members.tax_residency2,
      employment_status2: household.members.employment_status2,
      
      annualExpenses: household.members.annualExpenses,
      propertyValue: household.members.propertyValue,
      debtValue: household.members.debtValue,
      investmentValue: household.members.investmentValue,
      savingsValue: household.members.savingsValue,
      riskProfile: household.members.riskProfile,
      
      kiwisaverValue1: household.members.kiwisaverValue1,
      kiwisaverContribution1: household.members.kiwisaverContribution1,
      employerContribution1: household.members.employerContribution1,
      kiwisaverProfile1: household.members.kiwisaverProfile1,
      kiwisaverValue2: household.members.kiwisaverValue2,
      kiwisaverContribution2: household.members.kiwisaverContribution2,
      employerContribution2: household.members.employerContribution2,
      kiwisaverProfile2: household.members.kiwisaverProfile2
      
      // Explicitly exclude sensitive fields like tax_file_number1, tax_file_number2
    } : undefined
  };
}
