export interface HouseholdData {
  address: string;
  updated_at: any;
  id: number;
  created_at: string | number | Date;
  householdName: string;
  phone: string;
  email: string;
  occupation: string;
  employer: string;
  marital_status: string;
  date_of_birth: string;
  tax_file_number: string;
  notes: string;
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  property_type: string;
  preferred_contact: string;
  best_time_to_call: string;
  alternative_contact: string;
  total_assets: string;
  investment_strategy: string;
  risk_tolerance: string;
  primary_advisor: string;
  last_review: string;
  next_review: string;
  additional_info: any;
  user_id: string;
  members: {
    occupation1: string;
    employer1: string;
    date_of_birth1: string;
    tax_file_number1: string;
    citizenship1: string;
    tax_residency1: string;
    employment_status1: string;
    occupation2: string;
    employer2: string;
    date_of_birth2: string;
    tax_file_number2: string;
    citizenship2: string;
    tax_residency2: string;
    employment_status2: string;
    name1: string;
    last_name1?: string;
    email1: string;
    phone1: string;
    income1: string;
    riskProfile: string;
    kiwisaverValue1: string;
    kiwisaverProfile1: string;
    kiwisaverContribution1: string;
    employerContribution1: string;
    age1: string;
    propertyValue: string;
    investmentValue: string;
    savingsValue: string;
    debtValue: string;
    annualExpenses: string;
    name2?: string;
    last_name2?: string;
    email2?: string;
    phone2?: string;
    income2?: string;
    riskProfile2?: string;
    kiwisaverValue2?: string;
    kiwisaverProfile2?: string;
    kiwisaverContribution2?: string;
    employerContribution2?: string;
    age2?: string;
  };
}
