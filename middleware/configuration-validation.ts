/**
 * Configuration Validation Middleware
 * 
 * Validates that all required configuration is properly set and secure
 * before allowing API requests to proceed.
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateRuntimeConfigurationSecurity } from '@/utils/configuration-security';
import { validateEnvironmentVariables } from '@/utils/env-validation';

/**
 * Configuration validation results cache
 */
let configValidationCache: {
  isValid: boolean;
  lastChecked: number;
  errors: string[];
} | null = null;

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Validate configuration with caching
 */
function validateConfigurationWithCache(): {
  isValid: boolean;
  errors: string[];
} {
  const now = Date.now();
  
  // Return cached result if still valid
  if (configValidationCache && 
      (now - configValidationCache.lastChecked) < CACHE_DURATION) {
    return {
      isValid: configValidationCache.isValid,
      errors: configValidationCache.errors
    };
  }
  
  const errors: string[] = [];
  let isValid = true;
  
  try {
    // Validate environment variables
    validateEnvironmentVariables();
  } catch (error: any) {
    errors.push(`Environment validation failed: ${error.message}`);
    isValid = false;
  }
  
  try {
    // Validate runtime configuration security
    const securityValidation = validateRuntimeConfigurationSecurity();
    if (!securityValidation.isSecure) {
      errors.push(...securityValidation.issues);
      isValid = false;
    }
  } catch (error: any) {
    errors.push(`Security validation failed: ${error.message}`);
    isValid = false;
  }
  
  // Cache the result
  configValidationCache = {
    isValid,
    lastChecked: now,
    errors
  };
  
  return { isValid, errors };
}

/**
 * Configuration validation middleware for API routes
 */
export function withConfigurationValidation(
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (req: NextRequest, context?: any): Promise<NextResponse> => {
    // Skip validation for health check endpoints
    if (req.nextUrl.pathname.includes('/health') || 
        req.nextUrl.pathname.includes('/status')) {
      return handler(req, context);
    }
    
    const validation = validateConfigurationWithCache();
    
    if (!validation.isValid) {
      console.error('Configuration validation failed:', validation.errors);
      
      return NextResponse.json({
        success: false,
        error: 'Configuration validation failed',
        details: process.env.NODE_ENV === 'development' ? validation.errors : undefined,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
    
    // Add configuration security headers
    const response = await handler(req, context);
    
    // Add security headers to prevent configuration exposure
    response.headers.set('X-Configuration-Secure', 'true');
    response.headers.set('X-Environment-Validated', 'true');
    
    return response;
  };
}

/**
 * Validate specific configuration requirements for different API endpoints
 */
export function validateEndpointConfiguration(
  endpoint: string,
  requiredConfig: string[]
): { isValid: boolean; missingConfig: string[] } {
  const missingConfig: string[] = [];
  
  for (const configKey of requiredConfig) {
    if (!process.env[configKey]) {
      missingConfig.push(configKey);
    }
  }
  
  return {
    isValid: missingConfig.length === 0,
    missingConfig
  };
}

/**
 * Configuration requirements for different API endpoints
 */
export const ENDPOINT_CONFIG_REQUIREMENTS = {
  '/api/upload-logo': [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ],
  '/api/discovery-documents': [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'GEMINI_API_KEY'
  ],
  '/api/create-tasks-from-actions': [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'GEMINI_API_KEY'
  ],
  '/api/reports': [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ]
};

/**
 * Middleware to validate endpoint-specific configuration
 */
export function withEndpointConfigValidation(
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>,
  requiredConfig: string[]
) {
  return async (req: NextRequest, context?: any): Promise<NextResponse> => {
    const validation = validateEndpointConfiguration(
      req.nextUrl.pathname,
      requiredConfig
    );
    
    if (!validation.isValid) {
      console.error(
        `Endpoint ${req.nextUrl.pathname} missing required configuration:`,
        validation.missingConfig
      );
      
      return NextResponse.json({
        success: false,
        error: 'Endpoint configuration incomplete',
        details: process.env.NODE_ENV === 'development' 
          ? `Missing: ${validation.missingConfig.join(', ')}`
          : undefined,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
    
    return handler(req, context);
  };
}

/**
 * Log configuration security status
 */
export function logConfigurationSecurityStatus(): void {
  const validation = validateConfigurationWithCache();
  
  if (validation.isValid) {
    console.log('✅ Configuration security validation passed');
  } else {
    console.error('❌ Configuration security validation failed:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
  }
}

/**
 * Get configuration security report
 */
export function getConfigurationSecurityReport(): {
  status: 'secure' | 'warning' | 'critical';
  summary: string;
  details: {
    environmentVariables: boolean;
    runtimeSecurity: boolean;
    hardcodedValues: boolean;
  };
  recommendations: string[];
} {
  const validation = validateConfigurationWithCache();
  const recommendations: string[] = [];
  
  let status: 'secure' | 'warning' | 'critical' = 'secure';
  
  if (!validation.isValid) {
    status = 'critical';
    recommendations.push('Fix configuration validation errors immediately');
    recommendations.push('Review environment variable setup');
    recommendations.push('Ensure all required configuration is properly set');
  }
  
  // Check for development URLs in production
  if (process.env.NODE_ENV === 'production') {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    if (supabaseUrl.includes('localhost') || supabaseUrl.includes('127.0.0.1')) {
      status = 'critical';
      recommendations.push('Update Supabase URL for production environment');
    }
  }
  
  return {
    status,
    summary: validation.isValid 
      ? 'Configuration is secure and properly validated'
      : `Configuration has ${validation.errors.length} security issues`,
    details: {
      environmentVariables: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      runtimeSecurity: validation.isValid,
      hardcodedValues: false // Assuming no hardcoded values after fixes
    },
    recommendations
  };
}

/**
 * Initialize configuration security monitoring
 */
export function initializeConfigurationSecurity(): void {
  console.log('🔧 Initializing configuration security...');
  
  // Validate configuration on startup
  const validation = validateConfigurationWithCache();
  
  if (validation.isValid) {
    console.log('✅ Configuration security initialized successfully');
  } else {
    console.error('❌ Configuration security initialization failed');
    validation.errors.forEach(error => console.error(`  - ${error}`));
  }
  
  // Log security report
  const report = getConfigurationSecurityReport();
  console.log(`🛡️ Configuration Security Status: ${report.status.toUpperCase()}`);
  console.log(`📊 Summary: ${report.summary}`);
  
  if (report.recommendations.length > 0) {
    console.log('💡 Recommendations:');
    report.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
}
