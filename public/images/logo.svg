<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <style>
    .circle { fill: #f0f0f0; }
    .tree-left { fill: #1a5f38; }
    .tree-middle { fill: #2e8b57; }
    .tree-right { fill: #3cb371; }
  </style>
  
  <!-- Background Circle -->
  <circle class="circle" cx="100" cy="100" r="98" />
  
  <!-- Clipping Path for Trees -->
  <clipPath id="circle-clip">
    <circle cx="100" cy="100" r="98" />
  </clipPath>
  
  <g clip-path="url(#circle-clip)">
    <!-- Left Tree -->
    <polygon class="tree-left" points="30,200 80,60 130,200" />
    <polygon class="tree-left" points="40,200 80,80 120,200" />
    <polygon class="tree-left" points="50,180 80,70 110,180" />
    
    <!-- Middle Tree -->
    <polygon class="tree-middle" points="65,200 110,50 155,200" />
    <polygon class="tree-middle" points="75,200 110,70 145,200" />
    <polygon class="tree-middle" points="85,180 110,60 135,180" />
    
    <!-- Right Tree -->
    <polygon class="tree-right" points="100,200 150,60 200,200" />
    <polygon class="tree-right" points="110,200 150,80 190,200" />
    <polygon class="tree-right" points="120,180 150,70 180,180" />
  </g>
  
  <!-- Circle Border -->
  <circle fill="none" stroke="#333" stroke-width="4" cx="100" cy="100" r="98" />
</svg>